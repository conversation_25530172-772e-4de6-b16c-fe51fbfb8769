/**
 * Message Builder
 * 消息构建器 - 提供便捷的消息构建和发送工具
 */

/**
 * 消息构建器类
 * 链式调用构建复杂消息
 */
class MessageBuilder {
    constructor() {
        this.segments = [];
    }

    /**
     * 添加文本
     * @param {string} text 文本内容
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    text(text) {
        this.segments.push({
            type: 'text',
            data: { text: String(text) }
        });
        return this;
    }

    /**
     * 添加换行
     * @param {number} count 换行次数
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    newLine(count = 1) {
        this.segments.push({
            type: 'text',
            data: { text: '\n'.repeat(count) }
        });
        return this;
    }

    /**
     * 添加@用户
     * @param {string|number} userId 用户ID
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    at(userId) {
        this.segments.push({
            type: 'at',
            data: { qq: String(userId) }
        });
        return this;
    }

    /**
     * 添加@全体成员
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    atAll() {
        this.segments.push({
            type: 'at',
            data: { qq: 'all' }
        });
        return this;
    }

    /**
     * 添加图片
     * @param {string} image 图片文件名或URL
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    image(image) {
        this.segments.push({
            type: 'image',
            data: { file: image }
        });
        return this;
    }

    /**
     * 添加语音
     * @param {string} record 语音文件名或URL
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    record(record) {
        this.segments.push({
            type: 'record',
            data: { file: record }
        });
        return this;
    }

    /**
     * 添加视频
     * @param {string} video 视频文件名或URL
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    video(video) {
        this.segments.push({
            type: 'video',
            data: { file: video }
        });
        return this;
    }

    /**
     * 添加表情
     * @param {number} id 表情ID
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    face(id) {
        this.segments.push({
            type: 'face',
            data: { id: Number(id) }
        });
        return this;
    }

    /**
     * 添加链接分享
     * @param {string} url 链接地址
     * @param {string} title 标题
     * @param {string} content 内容描述
     * @param {string} image 图片URL
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    share(url, title = '', content = '', image = '') {
        this.segments.push({
            type: 'share',
            data: { url, title, content, image }
        });
        return this;
    }

    /**
     * 添加位置分享
     * @param {number} lat 纬度
     * @param {number} lon 经度
     * @param {string} title 标题
     * @param {string} content 内容
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    location(lat, lon, title = '', content = '') {
        this.segments.push({
            type: 'location',
            data: { lat, lon, title, content }
        });
        return this;
    }

    /**
     * 添加音乐分享
     * @param {string} type 音乐类型 (qq/163/xm)
     * @param {string} id 音乐ID
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    music(type, id) {
        this.segments.push({
            type: 'music',
            data: { type, id }
        });
        return this;
    }

    /**
     * 添加自定义音乐分享
     * @param {string} url 音乐URL
     * @param {string} audio 音频URL
     * @param {string} title 标题
     * @param {string} content 内容
     * @param {string} image 图片URL
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    customMusic(url, audio, title, content = '', image = '') {
        this.segments.push({
            type: 'music',
            data: { type: 'custom', url, audio, title, content, image }
        });
        return this;
    }

    /**
     * 添加回复
     * @param {number} messageId 要回复的消息ID
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    reply(messageId) {
        this.segments.push({
            type: 'reply',
            data: { id: messageId }
        });
        return this;
    }

    /**
     * 添加戳一戳
     * @param {string|number} userId 用户ID
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    poke(userId) {
        this.segments.push({
            type: 'poke',
            data: { qq: String(userId) }
        });
        return this;
    }

    /**
     * 添加礼物
     * @param {string|number} userId 用户ID
     * @param {number} id 礼物ID
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    gift(userId, id) {
        this.segments.push({
            type: 'gift',
            data: { qq: String(userId), id: Number(id) }
        });
        return this;
    }

    /**
     * 添加转发消息
     * @param {Array} messages 消息列表
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    forward(messages) {
        this.segments.push({
            type: 'forward',
            data: { id: messages }
        });
        return this;
    }

    /**
     * 添加XML消息
     * @param {string} data XML数据
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    xml(data) {
        this.segments.push({
            type: 'xml',
            data: { data }
        });
        return this;
    }

    /**
     * 添加JSON消息
     * @param {string} data JSON数据
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    json(data) {
        this.segments.push({
            type: 'json',
            data: { data }
        });
        return this;
    }

    /**
     * 添加自定义消息段
     * @param {string} type 消息段类型
     * @param {Object} data 消息段数据
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    custom(type, data) {
        this.segments.push({ type, data });
        return this;
    }

    /**
     * 清空消息
     * @returns {MessageBuilder} 返回自身支持链式调用
     */
    clear() {
        this.segments = [];
        return this;
    }

    /**
     * 获取消息段数组
     * @returns {Array} 消息段数组
     */
    build() {
        return [...this.segments];
    }

    /**
     * 获取纯文本内容
     * @returns {string} 纯文本内容
     */
    toText() {
        return this.segments
            .filter(seg => seg.type === 'text')
            .map(seg => seg.data.text)
            .join('');
    }

    /**
     * 获取消息长度
     * @returns {number} 消息段数量
     */
    length() {
        return this.segments.length;
    }

    /**
     * 检查是否为空消息
     * @returns {boolean} 是否为空
     */
    isEmpty() {
        return this.segments.length === 0;
    }

    /**
     * 克隆消息构建器
     * @returns {MessageBuilder} 新的消息构建器实例
     */
    clone() {
        const builder = new MessageBuilder();
        builder.segments = [...this.segments];
        return builder;
    }

    /**
     * 转换为JSON字符串
     * @returns {string} JSON字符串
     */
    toJSON() {
        return JSON.stringify(this.segments);
    }

    /**
     * 从JSON字符串创建消息构建器
     * @param {string} json JSON字符串
     * @returns {MessageBuilder} 消息构建器实例
     */
    static fromJSON(json) {
        const builder = new MessageBuilder();
        try {
            builder.segments = JSON.parse(json);
        } catch (error) {
            console.error('Failed to parse JSON:', error);
        }
        return builder;
    }

    /**
     * 创建快速文本消息
     * @param {string} text 文本内容
     * @returns {MessageBuilder} 消息构建器实例
     */
    static text(text) {
        return new MessageBuilder().text(text);
    }

    /**
     * 创建快速图片消息
     * @param {string} image 图片文件
     * @param {string} caption 图片说明
     * @returns {MessageBuilder} 消息构建器实例
     */
    static image(image, caption = '') {
        const builder = new MessageBuilder().image(image);
        if (caption) {
            builder.text(caption);
        }
        return builder;
    }

    /**
     * 创建快速@消息
     * @param {string|number} userId 用户ID
     * @param {string} text 文本内容
     * @returns {MessageBuilder} 消息构建器实例
     */
    static at(userId, text = '') {
        const builder = new MessageBuilder().at(userId);
        if (text) {
            builder.text(' ').text(text);
        }
        return builder;
    }
}

module.exports = MessageBuilder;
