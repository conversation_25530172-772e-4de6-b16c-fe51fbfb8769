/* 现代恐怖RPG风格 - 记忆深渊界面 */

:root {
    /* 深渊配色方案 */
    --abyss-black: #0a0a0a;
    --void-dark: #1a1a1a;
    --shadow-gray: #2a2a2a;
    --neural-blue: #00d4ff;
    --plasma-purple: #8b5cf6;
    --danger-red: #ef4444;
    --warning-amber: #f59e0b;
    --success-green: #10b981;
    --text-primary: #e5e7eb;
    --text-secondary: #9ca3af;
    --text-muted: #6b7280;

    /* 新增颜色变量 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    --border-color: #374151;
    --hover-bg: #1f2937;

    /* 神经网络效果 */
    --neural-glow: 0 0 20px var(--neural-blue);
    --plasma-glow: 0 0 15px var(--plasma-purple);
    --danger-glow: 0 0 10px var(--danger-red);

    /* 字体 */
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Raj<PERSON><PERSON>', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 规则怪谈风格滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(10, 10, 15, 0.8);
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.8);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
        rgba(80, 80, 100, 0.6) 0%,
        rgba(60, 60, 80, 0.8) 50%,
        rgba(40, 40, 60, 0.9) 100%);
    border-radius: 4px;
    border: 1px solid rgba(100, 100, 120, 0.3);
    box-shadow:
        0 0 5px rgba(80, 80, 100, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
        rgba(100, 100, 120, 0.8) 0%,
        rgba(80, 80, 100, 0.9) 50%,
        rgba(60, 60, 80, 1) 100%);
    box-shadow:
        0 0 10px rgba(100, 100, 120, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

::-webkit-scrollbar-corner {
    background: rgba(10, 10, 15, 0.8);
}

body {
    font-family: var(--font-secondary);
    background: var(--abyss-black);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
}

/* 神经网络背景 */
.neural-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.3;
}

#neural-canvas {
    width: 100%;
    height: 100%;
}

/* 主容器 */
.nexus-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, 
        rgba(10, 10, 10, 0.95) 0%, 
        rgba(26, 26, 26, 0.9) 50%, 
        rgba(42, 42, 42, 0.85) 100%);
    backdrop-filter: blur(10px);
}

/* 顶部状态栏 */
.nexus-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: linear-gradient(90deg, var(--void-dark), var(--shadow-gray));
    border-bottom: 2px solid var(--neural-blue);
    box-shadow: 0 2px 20px rgba(0, 212, 255, 0.3);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    margin: 0 2rem;
}

/* 系统监控面板 - 规则怪谈风格 */
.system-monitor {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(10, 10, 15, 0.95), rgba(20, 15, 25, 0.9));
    border: 1px solid rgba(100, 100, 120, 0.3);
    border-radius: 6px;
    backdrop-filter: blur(15px);
    margin-bottom: 1rem;
    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 2px 10px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(100, 100, 120, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 90px;
}

/* 镜面碎裂效果 */
.system-monitor::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 31%, rgba(255, 255, 255, 0.02) 32%, transparent 33%),
        linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.02) 31%, rgba(255, 255, 255, 0.02) 32%, transparent 33%),
        linear-gradient(90deg, transparent 70%, rgba(255, 255, 255, 0.01) 71%, rgba(255, 255, 255, 0.01) 72%, transparent 73%);
    background-size: 20px 20px, 25px 25px, 15px 15px;
    pointer-events: none;
    z-index: 1;
}

/* 响应式布局 */
@media (max-width: 1400px) {
    .system-monitor {
        grid-template-columns: repeat(3, 1fr);
        min-height: 140px;
    }
}

@media (max-width: 768px) {
    .system-monitor {
        grid-template-columns: repeat(2, 1fr);
        min-height: 200px;
    }
}

@media (max-width: 480px) {
    .system-monitor {
        grid-template-columns: 1fr;
        min-height: 400px;
    }
}

.monitor-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    padding: 0.6rem 0.4rem;
    background: rgba(15, 15, 20, 0.8);
    border: 1px solid rgba(80, 80, 100, 0.4);
    border-radius: 4px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-height: 70px;
    text-align: center;
    position: relative;
    z-index: 2;
    overflow: hidden;
}

/* 监控项目悬停效果 - 规则怪谈风格 */
.monitor-item:hover {
    background: rgba(25, 20, 30, 0.9);
    border-color: rgba(120, 120, 140, 0.6);
    box-shadow:
        0 0 15px rgba(100, 100, 120, 0.3),
        inset 0 0 10px rgba(0, 0, 0, 0.5);
    transform: translateY(-1px);
}

.monitor-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.6s ease;
}

.monitor-item:hover::before {
    left: 100%;
}

.monitor-label {
    font-size: 0.6rem;
    color: rgba(180, 180, 200, 0.8);
    font-family: var(--font-primary);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: 0.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    text-shadow: 0 0 3px rgba(180, 180, 200, 0.3);
}

.monitor-value {
    font-size: 0.8rem;
    color: var(--neural-blue);
    font-weight: 700;
    font-family: var(--font-primary);
    text-shadow: 0 0 8px var(--neural-blue);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 1.3;
}

.monitor-bar {
    width: 60px;
    height: 4px;
    background: var(--shadow-gray);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.monitor-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--neural-blue), var(--plasma-purple));
    border-radius: 2px;
    transition: width 0.5s ease;
    box-shadow: 0 0 8px var(--neural-blue);
    position: relative;
}

.monitor-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: monitor-shine 2s infinite;
}

@keyframes monitor-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 监控条颜色状态 */
.monitor-fill.monitor-normal {
    background: linear-gradient(90deg, var(--neural-blue), #00ff88);
    box-shadow: 0 0 8px var(--neural-blue);
}

.monitor-fill.monitor-warning {
    background: linear-gradient(90deg, #ffaa00, #ff6600);
    box-shadow: 0 0 8px #ffaa00;
}

.monitor-fill.monitor-danger {
    background: linear-gradient(90deg, #ff3366, #cc0033);
    box-shadow: 0 0 8px #ff3366;
}

/* 通知系统 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    min-width: 300px;
    max-width: 500px;
    padding: 1rem;
    background: linear-gradient(135deg, var(--void-dark), var(--shadow-gray));
    border: 1px solid var(--neural-blue);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.notification-show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-icon {
    font-size: 1.2rem;
}

.notification-message {
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: 0.9rem;
}

.notification-success {
    border-color: #00ff88;
    box-shadow: 0 4px 20px rgba(0, 255, 136, 0.3);
}

.notification-error {
    border-color: #ff3366;
    box-shadow: 0 4px 20px rgba(255, 51, 102, 0.3);
}

.notification-info {
    border-color: var(--neural-blue);
    box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
}

.nexus-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-core {
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, var(--neural-blue), var(--plasma-purple));
    border-radius: 50%;
    position: relative;
    animation: pulse-core 2s ease-in-out infinite;
}

.logo-core::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--abyss-black);
    border-radius: 50%;
}

.logo-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--neural-blue), var(--plasma-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--neural-glow);
}

.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-green);
    box-shadow: 0 0 10px var(--success-green);
    animation: blink 2s ease-in-out infinite;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.neural-activity {
    display: flex;
    gap: 4px;
    align-items: end;
}

.activity-bar {
    width: 4px;
    background: var(--neural-blue);
    border-radius: 2px;
    animation: neural-activity 1.5s ease-in-out infinite;
}

.activity-bar:nth-child(1) { height: 20px; animation-delay: 0s; }
.activity-bar:nth-child(2) { height: 30px; animation-delay: 0.2s; }
.activity-bar:nth-child(3) { height: 25px; animation-delay: 0.4s; }
.activity-bar:nth-child(4) { height: 35px; animation-delay: 0.6s; }

.sync-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--neural-blue), var(--plasma-purple));
    border: none;
    border-radius: 8px;
    color: var(--abyss-black);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--neural-glow);
}

.sync-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 25px rgba(0, 212, 255, 0.5);
}

.sync-icon {
    font-size: 1.2rem;
    animation: rotate 2s linear infinite;
}

/* 初始化按钮 */
.init-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 15px rgba(255, 107, 157, 0.3);
    margin-left: 0.5rem;
}

.init-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 25px rgba(255, 107, 157, 0.5);
}

.init-icon {
    font-size: 1.2rem;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 主内容区域 */
.nexus-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 左侧导航 */
.memory-nav {
    width: 300px;
    background: linear-gradient(180deg, var(--void-dark), var(--abyss-black));
    border-right: 1px solid var(--shadow-gray);
    padding: 1.5rem;
    overflow-y: auto;
}

.nav-header {
    margin-bottom: 2rem;
}

.nav-header h3 {
    font-family: var(--font-primary);
    font-size: 1.2rem;
    color: var(--neural-blue);
    margin-bottom: 0.5rem;
}

.nav-stats {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.category-group {
    margin-bottom: 2rem;
}

.category-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--shadow-gray);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: var(--void-dark);
    border: 1px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-item:hover::before {
    left: 100%;
}

.nav-item:hover {
    border-color: var(--neural-blue);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
    transform: translateX(5px);
}

.nav-item.active {
    background: linear-gradient(135deg, var(--neural-blue), var(--plasma-purple));
    color: var(--abyss-black);
    box-shadow: var(--neural-glow);
}

.nav-icon {
    font-size: 1.2rem;
    min-width: 20px;
}

.nav-text {
    font-weight: 500;
}

.nav-count {
    margin-left: auto;
    font-size: 0.8rem;
    background: var(--shadow-gray);
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
}

.nav-item.active .nav-count {
    background: rgba(10, 10, 10, 0.3);
}

/* 数据面板 */
.data-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 欢迎界面 */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: radial-gradient(circle at center, 
        rgba(0, 212, 255, 0.05) 0%, 
        transparent 70%);
}

.welcome-content {
    text-align: center;
    max-width: 500px;
}

.nexus-eye {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
}

.eye-core {
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, var(--neural-blue), var(--plasma-purple));
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-eye 3s ease-in-out infinite;
}

.eye-ring {
    width: 120px;
    height: 120px;
    border: 2px solid var(--neural-blue);
    border-radius: 50%;
    position: absolute;
    animation: rotate-ring 10s linear infinite;
}

.eye-ring::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    background: var(--neural-blue);
    border-radius: 50%;
    box-shadow: var(--neural-glow);
}

.welcome-content h2 {
    font-family: var(--font-primary);
    font-size: 2rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--neural-blue), var(--plasma-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-content p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.system-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 2rem;
}

.info-item {
    padding: 1rem;
    background: var(--void-dark);
    border: 1px solid var(--shadow-gray);
    border-radius: 8px;
    text-align: left;
}

.info-label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.info-value {
    font-family: var(--font-primary);
    font-size: 1.1rem;
    color: var(--neural-blue);
    font-weight: 600;
}

/* 动画效果 */
@keyframes pulse-core {
    0%, 100% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.5); }
    50% { box-shadow: 0 0 40px rgba(0, 212, 255, 0.8), 0 0 60px rgba(139, 92, 246, 0.4); }
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

@keyframes neural-activity {
    0%, 100% { height: 20px; opacity: 0.6; }
    50% { height: 40px; opacity: 1; }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse-eye {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes rotate-ring {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 表格界面 */
.table-interface {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 1.5rem;
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--void-dark);
    border: 1px solid var(--shadow-gray);
    border-radius: 12px;
}

.controls-left h3 {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    color: var(--neural-blue);
    margin-bottom: 0.5rem;
}

.table-stats {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.separator {
    margin: 0 0.5rem;
    color: var(--shadow-gray);
}

.controls-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--abyss-black);
    border: 1px solid var(--shadow-gray);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: var(--neural-blue);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

#search-input {
    background: transparent;
    border: none;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    outline: none;
    width: 250px;
}

#search-input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    background: var(--neural-blue);
    border: none;
    padding: 0.75rem 1rem;
    color: var(--abyss-black);
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: var(--plasma-purple);
}

.add-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--success-green), #059669);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(16, 185, 129, 0.4);
}

/* 批量操作按钮组 */
.batch-operations {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 12px;
}

.batch-btn {
    background: var(--warning-amber);
    border: 1px solid #d97706;
    color: var(--abyss-black);
    padding: 0.6rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-family: var(--font-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.batch-btn:hover:not(:disabled) {
    background: #f59e0b;
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.4);
    transform: translateY(-1px);
}

.batch-btn:disabled {
    background: var(--shadow-gray);
    color: var(--text-muted);
    cursor: not-allowed;
    border-color: var(--shadow-gray);
    opacity: 0.6;
}

.danger-btn {
    background: var(--danger-red);
    border: 1px solid #dc2626;
    color: white;
    padding: 0.6rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-family: var(--font-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.danger-btn:hover {
    background: #dc2626;
    box-shadow: var(--danger-glow);
    transform: translateY(-1px);
}

.danger-btn:active {
    transform: translateY(0);
}

/* 复选框样式优化 */
.record-checkbox, #select-all {
    width: 16px;
    height: 16px;
    accent-color: var(--neural-blue);
    cursor: pointer;
}

.record-checkbox:hover, #select-all:hover {
    transform: scale(1.1);
}

/* 分类导航样式 */
.nav-category {
    margin-bottom: 1rem;
}

.category-header {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--void-dark);
    border: 1px solid var(--shadow-gray);
    border-left: 4px solid var(--neural-blue);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.category-header:hover {
    background: var(--shadow-gray);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.category-header.collapsed {
    border-radius: 8px;
    margin-bottom: 0;
}

.category-icon {
    font-size: 1.2rem;
    margin-right: 0.75rem;
}

.category-name {
    flex: 1;
    font-weight: 600;
    color: var(--text-primary);
    font-family: var(--font-secondary);
}

.category-count {
    background: var(--neural-blue);
    color: var(--abyss-black);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.category-items {
    padding-left: 1rem;
    transition: all 0.3s ease;
    overflow: hidden;
}

.category-items.collapsed {
    max-height: 0;
    padding: 0;
    margin: 0;
}

.category-items .nav-item {
    display: flex;
    align-items: center;
    padding: 0.6rem 1rem;
    margin: 0.25rem 0;
    background: rgba(42, 42, 42, 0.5);
    border: 1px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-items .nav-item:hover {
    background: var(--shadow-gray);
    border-color: var(--neural-blue);
    transform: translateX(4px);
}

.category-items .nav-item.active {
    background: linear-gradient(135deg, var(--neural-blue), var(--plasma-purple));
    color: var(--abyss-black);
    border-color: var(--neural-blue);
    box-shadow: var(--neural-glow);
}

.category-items .nav-item.active .nav-text {
    color: var(--abyss-black);
    font-weight: 600;
}

.category-items .nav-item.active .nav-count {
    background: rgba(10, 10, 10, 0.8);
    color: white;
}

.category-items .nav-icon {
    font-size: 1rem;
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.category-items .nav-text {
    flex: 1;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.category-items .nav-count {
    background: var(--shadow-gray);
    color: var(--text-secondary);
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 500;
    min-width: 20px;
    text-align: center;
}

/* 筛选控制栏样式 */
.filter-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--void-dark);
    border: 1px solid var(--shadow-gray);
    border-radius: 8px;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    white-space: nowrap;
}

.filter-input-group {
    display: flex;
    align-items: center;
    position: relative;
}

.filter-group input {
    background: var(--abyss-black);
    border: 1px solid var(--shadow-gray);
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    padding-right: 2rem; /* 为清除按钮留出空间 */
    color: var(--text-primary);
    font-size: 0.85rem;
    width: 150px;
    transition: all 0.3s ease;
}

.filter-group input:focus {
    outline: none;
    border-color: var(--neural-blue);
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.filter-group input[type="datetime-local"] {
    width: 180px;
}

.filter-clear-btn {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--danger-red);
    border: none;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.filter-clear-btn:hover {
    opacity: 1;
    background: #dc2626;
    transform: translateY(-50%) scale(1.1);
}

.filter-clear-btn:active {
    transform: translateY(-50%) scale(0.95);
}

.filter-actions {
    margin-left: auto;
    display: flex;
    gap: 0.5rem;
}

.apply-filter-btn {
    background: var(--neural-blue);
    border: 1px solid var(--neural-blue);
    color: var(--abyss-black);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.apply-filter-btn:hover {
    background: #00b8e6;
    box-shadow: var(--neural-glow);
    transform: translateY(-1px);
}

.apply-filter-btn:active {
    transform: translateY(0);
}

.filter-btn {
    background: var(--shadow-gray);
    border: 1px solid var(--text-muted);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.filter-btn:hover {
    background: var(--text-muted);
    color: var(--abyss-black);
}

/* 排序图标样式 */
.sort-icon {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.sort-icon.active {
    opacity: 1;
    color: var(--neural-blue);
}

th:hover .sort-icon {
    opacity: 1;
}

/* 表格容器 */
.table-container {
    flex: 1;
    overflow: auto;
    background: var(--void-dark);
    border: 1px solid var(--shadow-gray);
    border-radius: 12px;
    margin-bottom: 1rem;
    width: 100%;
    max-width: 100%;
}

.memory-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    background: var(--void-dark);
    position: relative;
    table-layout: fixed; /* 固定表格布局，确保列宽均匀 */
}

/* 表格镜面碎裂效果 */
.memory-table::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(255, 255, 255, 0.01) 49%, rgba(255, 255, 255, 0.01) 51%, transparent 52%),
        linear-gradient(-45deg, transparent 48%, rgba(255, 255, 255, 0.01) 49%, rgba(255, 255, 255, 0.01) 51%, transparent 52%);
    background-size: 30px 30px, 35px 35px;
    pointer-events: none;
    z-index: 1;
}

.memory-table th {
    background: linear-gradient(135deg,
        rgba(20, 20, 30, 0.95) 0%,
        rgba(30, 25, 35, 0.9) 50%,
        rgba(25, 20, 30, 0.95) 100%);
    color: rgba(180, 200, 255, 0.9);
    font-family: var(--font-primary);
    font-weight: 700;
    padding: 0.6rem 0.4rem;
    text-align: center; /* 居中对齐 */
    border-bottom: 2px solid rgba(100, 120, 200, 0.6);
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow:
        0 2px 10px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    text-shadow: 0 0 5px rgba(180, 200, 255, 0.5);
    font-size: 0.7rem;
    letter-spacing: 0.3px;
    text-transform: uppercase;
    width: auto; /* 自动宽度，配合table-layout: fixed实现均匀分布 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.memory-table td {
    padding: 0.6rem 0.4rem;
    border-bottom: 1px solid rgba(60, 60, 80, 0.5);
    color: var(--text-primary);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    vertical-align: middle; /* 垂直居中 */
    text-align: center; /* 水平居中 */
    word-wrap: break-word;
    position: relative;
    z-index: 2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto; /* 自动宽度，均匀分布 */
    min-width: 0; /* 允许收缩 */
    font-size: 0.8rem;
}

/* 表格行悬停效果 - 规则怪谈风格 */
.memory-table tr:hover td {
    background: linear-gradient(90deg,
        rgba(25, 20, 35, 0.8) 0%,
        rgba(30, 25, 40, 0.9) 50%,
        rgba(25, 20, 35, 0.8) 100%);
    color: rgba(200, 220, 255, 0.95);
    transform: translateY(-1px);
    box-shadow:
        0 3px 15px rgba(100, 120, 200, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(100, 120, 200, 0.4);
    text-shadow: 0 0 3px rgba(200, 220, 255, 0.3);
}

.memory-table tr:nth-child(even) td {
    background: rgba(15, 15, 25, 0.6);
}

.memory-table tr:nth-child(even):hover td {
    background: linear-gradient(90deg,
        rgba(25, 20, 35, 0.8) 0%,
        rgba(30, 25, 40, 0.9) 50%,
        rgba(25, 20, 35, 0.8) 100%);
}

/* 表格数据单元格特殊效果 */
.memory-table td:hover {
    cursor: pointer;
    background: rgba(35, 30, 45, 0.9) !important;
    box-shadow:
        inset 0 0 10px rgba(100, 120, 200, 0.3),
        0 0 5px rgba(100, 120, 200, 0.2);
}

/* 确保表格内容横向排列 */
.memory-table td > * {
    display: inline-block;
    max-width: 100%;
    vertical-align: middle;
}

/* 特殊数据类型的样式 */
.memory-table td span[title] {
    max-width: 100%;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 响应式优化 - 确保充分利用空间 */
@media (min-width: 1200px) {
    .memory-table td {
        font-size: 0.85rem;
        padding: 0.7rem 0.5rem;
    }

    .memory-table th {
        font-size: 0.75rem;
        padding: 0.7rem 0.5rem;
    }

    .action-btn {
        width: 55px;
        height: 30px;
        padding: 0.4rem 0.7rem;
        font-size: 0.7rem;
    }

    .table-actions {
        gap: 0.5rem;
        min-width: 130px !important;
    }
}

@media (max-width: 768px) {
    .action-btn {
        width: 40px;
        height: 24px;
        padding: 0.2rem 0.4rem;
        font-size: 0.6rem;
    }

    .table-actions {
        gap: 0.2rem;
        padding: 0.3rem;
        min-width: 90px !important;
    }

    .memory-table td {
        font-size: 0.75rem;
        padding: 0.5rem 0.3rem;
    }

    .memory-table th {
        font-size: 0.65rem;
        padding: 0.5rem 0.3rem;
    }
}

.table-actions {
    display: flex;
    gap: 0.4rem;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
    width: 100%;
    padding: 0.4rem 0.5rem;
    box-sizing: border-box;
    min-width: 120px !important;
}

/* 确保操作列有足够宽度 */
.memory-table td.table-actions {
    min-width: 120px !important;
    width: 15% !important;
    max-width: 140px !important;
}

.action-btn {
    padding: 0.3rem 0.6rem;
    border: none;
    border-radius: 4px;
    font-size: 0.65rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    width: 50px;
    height: 28px;
    text-align: center;
    white-space: nowrap;
    line-height: 1.2;
    display: inline-block;
    flex: none;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.edit-btn {
    background: linear-gradient(135deg, var(--neural-blue), var(--info-color));
    color: var(--abyss-black);
    box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
}

.edit-btn:hover {
    background: linear-gradient(135deg, var(--plasma-purple), var(--neural-blue));
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
}

.delete-btn {
    background: linear-gradient(135deg, var(--danger-red), #dc2626);
    color: white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.delete-btn:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

/* 特殊数据显示样式 */
.affinity-positive {
    color: var(--success-color);
    font-weight: 600;
}

.affinity-negative {
    color: var(--error-color);
    font-weight: 600;
}

.affinity-neutral {
    color: var(--text-muted);
}

.confidence-high {
    color: var(--success-color);
    font-weight: 600;
}

.confidence-medium {
    color: var(--warning-color);
}

.confidence-low {
    color: var(--error-color);
}

.timestamp-cell {
    color: var(--neural-blue);
    font-family: var(--font-primary);
    font-size: 0.85rem;
}

.json-data {
    color: var(--plasma-purple);
    cursor: pointer;
    text-decoration: underline;
}

.json-data:hover {
    color: var(--neural-blue);
}

.long-text {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
}

.long-text:hover {
    white-space: normal;
    word-wrap: break-word;
}

/* 分页控制 */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--void-dark);
    border: 1px solid var(--shadow-gray);
    border-radius: 12px;
}

.page-btn {
    padding: 0.75rem 1.5rem;
    background: var(--shadow-gray);
    border: none;
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
    background: var(--neural-blue);
    color: var(--abyss-black);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.5rem;
}

.page-number {
    padding: 0.5rem 1rem;
    background: var(--shadow-gray);
    border: none;
    border-radius: 6px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-number:hover {
    background: var(--neural-blue);
    color: var(--abyss-black);
}

.page-number.active {
    background: linear-gradient(135deg, var(--neural-blue), var(--plasma-purple));
    color: var(--abyss-black);
    box-shadow: var(--neural-glow);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .memory-nav {
        width: 250px;
    }

    .nexus-header {
        padding: 1rem;
    }

    .header-left, .header-right {
        gap: 1rem;
    }

    .table-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .controls-right {
        justify-content: space-between;
    }
}

@media (max-width: 768px) {
    .memory-nav {
        position: absolute;
        left: -300px;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .memory-nav.mobile-open {
        left: 0;
    }

    .nexus-header {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .system-info {
        grid-template-columns: 1fr;
    }

    #search-input {
        width: 200px;
    }

    .table-interface {
        padding: 1rem;
    }

    .memory-table {
        font-size: 0.8rem;
    }

    .memory-table th,
    .memory-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: linear-gradient(135deg, var(--void-dark), var(--abyss-black));
    border: 2px solid var(--neural-blue);
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 212, 255, 0.3);
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--neural-blue), var(--plasma-purple));
    color: var(--abyss-black);
}

.modal-header h3 {
    font-family: var(--font-primary);
    font-size: 1.2rem;
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--abyss-black);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(10, 10, 10, 0.2);
    transform: rotate(90deg);
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--neural-blue);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 1rem;
    background: var(--abyss-black);
    border: 1px solid var(--shadow-gray);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--neural-blue);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
    font-family: 'Courier New', monospace;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--void-dark);
    border-top: 1px solid var(--shadow-gray);
}

.btn-primary {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--neural-blue), var(--plasma-purple));
    border: none;
    border-radius: 8px;
    color: var(--abyss-black);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 212, 255, 0.4);
}

.btn-secondary {
    padding: 0.75rem 1.5rem;
    background: var(--shadow-gray);
    border: none;
    border-radius: 8px;
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--void-dark);
    transform: translateY(-2px);
}

.btn-danger {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--danger-red), #dc2626);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(239, 68, 68, 0.4);
}

/* 通知系统 */
.notification-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 400px;
}

.notification {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    color: white;
    font-weight: 500;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, var(--success-green), #059669);
}

.notification.error {
    background: linear-gradient(135deg, var(--danger-red), #dc2626);
}

.notification.warning {
    background: linear-gradient(135deg, var(--warning-amber), #d97706);
}

.notification.info {
    background: linear-gradient(135deg, var(--neural-blue), var(--plasma-purple));
}

.notification::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    animation: notification-progress 5s linear forwards;
}

@keyframes notification-progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
}

.spinner-ring {
    width: 60px;
    height: 60px;
    border: 4px solid var(--shadow-gray);
    border-top: 4px solid var(--neural-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-text {
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: 1.1rem;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 筛选状态显示 */
.filter-status {
    background: linear-gradient(135deg, var(--void-dark) 0%, #1a1a2e 100%);
    border: 1px solid var(--neural-blue);
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
    animation: slideDown 0.3s ease-out;
}

.filter-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, var(--neural-blue) 0%, #4f46e5 100%);
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-status-title {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-status-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.filter-status-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.filter-status-content {
    padding: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    animation: fadeInScale 0.3s ease-out;
}

.filter-tag-label {
    opacity: 0.9;
    font-weight: 400;
}

.filter-tag-value {
    font-weight: 600;
}

.filter-tag-remove {
    background: none;
    border: none;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.1rem 0.3rem;
    border-radius: 50%;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.filter-tag-remove:hover {
    background: rgba(255, 255, 255, 0.2);
    opacity: 1;
    transform: scale(1.1);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 数据统计显示 */
.data-stats {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 1rem;
    background: linear-gradient(135deg, var(--void-dark) 0%, #1a1a2e 100%);
    border: 1px solid var(--shadow-gray);
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.stats-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.stats-label {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
}

.stats-value {
    color: var(--neural-blue);
    font-size: 1.1rem;
    font-weight: 700;
    min-width: 2rem;
    text-align: center;
}

.stats-unit {
    color: var(--text-secondary);
    font-size: 0.8rem;
    opacity: 0.8;
}

#filter-stats .stats-value {
    color: var(--success-green);
}

#search-stats .stats-value {
    color: var(--warning-orange);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .data-stats {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .stats-item {
        flex: 1;
        min-width: 120px;
    }
}
