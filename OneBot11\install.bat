@echo off
chcp 65001 >nul
title OneBot11适配器安装程序

echo ========================================
echo        OneBot11适配器安装程序
echo ========================================
echo.

:: 检查Node.js是否安装
echo [1/4] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo [✓] Node.js已安装:
node --version
echo.

:: 检查npm是否可用
echo [2/4] 检查npm环境...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] npm不可用，请检查Node.js安装
    pause
    exit /b 1
)

echo [✓] npm已安装:
npm --version
echo.

:: 安装依赖
echo [3/4] 安装项目依赖...
echo 这可能需要几分钟时间，请耐心等待...
echo.

npm install
if %errorlevel% neq 0 (
    echo [错误] 依赖安装失败，请检查网络连接或尝试使用国内镜像
    echo.
    echo 使用淘宝镜像的命令:
    echo npm install --registry https://registry.npmmirror.com
    echo.
    pause
    exit /b 1
)

echo [✓] 依赖安装完成
echo.

:: 创建必要的目录
echo [4/4] 创建必要的目录结构...

if not exist "logs" (
    mkdir logs
    echo [✓] 创建logs目录
)

if not exist "data" (
    mkdir data
    echo [✓] 创建data目录
)

if not exist "temp" (
    mkdir temp
    echo [✓] 创建temp目录
)

echo.
echo ========================================
echo           安装完成！
echo ========================================
echo.
echo 接下来的步骤:
echo 1. 编辑 config.json 配置文件
echo 2. 配置OneBot11服务器连接信息
echo 3. 配置VCP WebSocket连接信息
echo 4. 运行 start_bot.bat 启动适配器
echo.
echo 配置文件位置: %cd%\config.json
echo 启动脚本位置: %cd%\start_bot.bat
echo 文档位置: %cd%\README.md
echo.
echo 如需帮助，请查看README.md文档
echo.

pause
