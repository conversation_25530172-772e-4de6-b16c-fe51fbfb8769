// Plugin/ImageServer/image-server.js
const express = require('express');
const path = require('path');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}

let serverImageKeyForAuth; // Stores Image_Key from config
let pluginDebugMode = false; // To store the debug mode state for this plugin

/**
 * Registers the image server routes and middleware with the Express app.
 * @param {object} app - The Express application instance.
 * @param {object} pluginConfig - Configuration for this plugin, expecting { Image_Key: '...' }.
 * @param {string} projectBasePath - The absolute path to the project's root directory.
 */
function registerRoutes(app, pluginConfig, projectBasePath) {
    pluginDebugMode = pluginConfig && pluginConfig.DebugMode === true; // Set module-level debug mode

    if (pluginDebugMode) logger.info(`[图床服务插件] 正在注册图床服务路由。调试模式已开启。`);
    else logger.debug(`[图床服务插件] 正在注册图床服务路由。调试模式已关闭。`);

    if (!app || typeof app.use !== 'function') {
        logger.warning('[图床服务插件] 需要 Express 应用实例才能注册路由。');
        return;
    }
    if (!pluginConfig || !pluginConfig.Image_Key) {
        logger.warning('[图床服务插件] 图床服务插件缺少 Image_Key 配置。');
        // Fallback or strict error? For now, let it proceed but log heavily.
        // It won't be secure if Image_Key is missing.
        // return; // Or throw new Error('Image_Key configuration is missing for ImageServer plugin.');
    }
    serverImageKeyForAuth = pluginConfig.Image_Key || null;

    const imageAuthMiddleware = (req, res, next) => {
        if (!serverImageKeyForAuth) {
            logger.warning("[图床权限验证] 插件未配置 Image_Key。拒绝访问。");
            return res.status(500).type('text/plain').send('服务器配置错误：插件未设置图片访问密钥。');
        }
        const pathSegmentWithKey = req.params.pathSegmentWithKey;
        if (pluginDebugMode) logger.debug('图床权限验证', `请求路径参数: '${pathSegmentWithKey}'`);

        if (pathSegmentWithKey && pathSegmentWithKey.startsWith('pw=')) {
            const requestImageKey = pathSegmentWithKey.substring(3);
            
            const match = requestImageKey === serverImageKeyForAuth;
            if (pluginDebugMode) logger.debug(`[图床权限验证] 密钥比较结果: ${match}`);

            if (match) {
                if (pluginDebugMode) logger.debug('[图床权限验证] 验证成功。');
                next();
            } else {
                if (pluginDebugMode) logger.debug('[图床权限验证] 验证失败：密钥无效。');
                return res.status(401).type('text/plain').send('未授权：图片访问密钥无效。');
            }
        } else {
            if (pluginDebugMode) logger.warning('[图床权限验证] 验证失败：路径格式无效（不以 pw= 开头或缺少路径参数）。');
            return res.status(400).type('text/plain').send('请求错误：图片访问路径格式无效。');
        }
    };
    
    // Determine the correct path to the global 'image' directory from the project root
    const globalImageDir = path.join(projectBasePath, 'image');

    // 注册两个路由以支持不同的路径格式
    app.use('/:pathSegmentWithKey/images', imageAuthMiddleware, express.static(globalImageDir));
    app.use('/:pathSegmentWithKey/image', imageAuthMiddleware, express.static(globalImageDir));
    
    const imageKeyForLog = serverImageKeyForAuth || "";
    const maskedImageKey = imageKeyForLog.length > 6
        ? imageKeyForLog.substring(0,3) + "***" + imageKeyForLog.slice(-3)
        : (imageKeyForLog.length > 1 ? imageKeyForLog[0] + "***" + imageKeyForLog.slice(-1) : (imageKeyForLog.length === 1 ? "*" : "未配置"));
    
    if (serverImageKeyForAuth) {
        logger.debug(`[图床服务插件] 受保护的图片服务已注册。访问路径格式: /pw=${maskedImageKey}/images/... 或 /pw=${maskedImageKey}/image/... 服务目录: ${globalImageDir}`);
    } else {
        logger.debug(`[图床服务插件] 受保护的图片服务已注册，但 Image_Key 未配置。访问将被拒绝。`);
    }
}

module.exports = { registerRoutes };