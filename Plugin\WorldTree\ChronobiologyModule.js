/**
 * 时间生物学模块
 * 
 * 基于科学研究的时间相关算法：
 * 1. Circadian Clock Research (生物钟研究)
 * 2. Chronotype Theory (时型理论)
 * 3. Social Jetlag (社会时差)
 * 4. Light Exposure Effects (光照影响)
 * 5. Meal Timing Effects (进食时间影响)
 * 6. Sleep-Wake Homeostasis (睡眠-觉醒稳态)
 */

const moment = require('moment');
const { Matrix } = require('ml-matrix');

class ChronobiologyModule {
    constructor(config = {}) {
        this.config = {
            // 个体时型参数 (Chronotype)
            chronotype: config.chronotype || 'intermediate', // 'morning', 'evening', 'intermediate'
            sleepDuration: config.sleepDuration || 8, // 理想睡眠时长
            bedtime: config.bedtime || '23:00', // 理想就寝时间
            wakeTime: config.wakeTime || '07:00', // 理想起床时间
            
            // 环境因素
            lightExposure: config.lightExposure || 0.7, // 光照强度 (0-1)
            seasonalFactor: config.seasonalFactor || 0.5, // 季节因素
            socialSchedule: config.socialSchedule || 'standard', // 社会时间表
            
            // 生理参数
            cortisolPeak: config.cortisolPeak || 8, // 皮质醇峰值时间
            melatoninOnset: config.melatoninOnset || 21, // 褪黑素分泌开始时间
            bodyTempPeak: config.bodyTempPeak || 18, // 体温峰值时间
            
            ...config
        };
        
        // 时型特征定义
        this.chronotypeProfiles = {
            morning: {
                cortisolPeak: 7,
                melatoninOnset: 20,
                bodyTempPeak: 16,
                optimalPerformance: [7, 8, 9, 10, 11],
                energyPattern: [0.9, 1.0, 0.9, 0.7, 0.5, 0.3, 0.2, 0.1]
            },
            evening: {
                cortisolPeak: 9,
                melatoninOnset: 23,
                bodyTempPeak: 20,
                optimalPerformance: [14, 15, 16, 17, 18, 19, 20, 21],
                energyPattern: [0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
            },
            intermediate: {
                cortisolPeak: 8,
                melatoninOnset: 21,
                bodyTempPeak: 18,
                optimalPerformance: [9, 10, 11, 14, 15, 16, 17],
                energyPattern: [0.6, 0.8, 1.0, 0.9, 0.8, 0.7, 0.5, 0.3]
            }
        };
    }

    /**
     * 计算当前时间的生物钟状态
     */
    calculateCircadianState(currentTime = moment()) {
        const hour = currentTime.hour();
        const minute = currentTime.minute();
        const profile = this.chronotypeProfiles[this.config.chronotype];
        
        // 计算生物钟相位
        const circadianPhase = this.calculateCircadianPhase(hour, minute);
        
        // 计算各种生理节律
        const cortisolLevel = this.calculateCortisolLevel(hour, profile);
        const melatoninLevel = this.calculateMelatoninLevel(hour, profile);
        const bodyTemperature = this.calculateBodyTemperature(hour, profile);
        const alertnessLevel = this.calculateAlertness(hour, profile);
        
        // 计算认知表现窗口
        const cognitivePerformance = this.calculateCognitivePerformance(hour, profile);
        
        // 计算社会时差影响
        const socialJetlag = this.calculateSocialJetlag(currentTime);
        
        return {
            circadianPhase,
            cortisolLevel,
            melatoninLevel,
            bodyTemperature,
            alertnessLevel,
            cognitivePerformance,
            socialJetlag,
            chronotype: this.config.chronotype,
            optimalWindow: this.isOptimalPerformanceWindow(hour, profile),
            timeOfDay: this.getDetailedTimeOfDay(hour),
            biologicalTime: this.calculateBiologicalTime(hour, profile)
        };
    }

    /**
     * 计算生物钟相位 (0-1, 0.5为峰值)
     */
    calculateCircadianPhase(hour, minute) {
        const totalMinutes = hour * 60 + minute;
        const profile = this.chronotypeProfiles[this.config.chronotype];
        
        // 根据时型调整相位偏移
        const phaseShift = {
            morning: -1, // 提前1小时
            evening: 1,  // 延后1小时
            intermediate: 0
        }[this.config.chronotype];
        
        const adjustedHour = (hour + phaseShift + 24) % 24;
        return Math.sin((adjustedHour - 6) / 24 * 2 * Math.PI) * 0.5 + 0.5;
    }

    /**
     * 计算皮质醇水平
     * 基于皮质醇觉醒反应 (Cortisol Awakening Response)
     */
    calculateCortisolLevel(hour, profile) {
        const peakHour = profile.cortisolPeak;
        const timeDiff = Math.abs(hour - peakHour);
        
        // 皮质醇在觉醒后快速上升，然后逐渐下降
        if (timeDiff <= 2) {
            return 0.8 + (2 - timeDiff) * 0.1; // 峰值期
        } else if (timeDiff <= 6) {
            return 0.8 - (timeDiff - 2) * 0.1; // 下降期
        } else {
            return Math.max(0.2, 0.4 - (timeDiff - 6) * 0.02); // 基础水平
        }
    }

    /**
     * 计算褪黑素水平
     * 基于褪黑素分泌节律研究
     */
    calculateMelatoninLevel(hour, profile) {
        const onsetHour = profile.melatoninOnset;
        const offsetHour = (profile.cortisolPeak - 1 + 24) % 24;
        
        // 褪黑素在夜间分泌
        if (hour >= onsetHour || hour <= offsetHour) {
            const nightHours = hour >= onsetHour ? hour - onsetHour : hour + 24 - onsetHour;
            return Math.min(1.0, 0.1 + nightHours * 0.15); // 逐渐上升
        } else {
            return Math.max(0.05, 0.8 - (hour - offsetHour) * 0.1); // 白天快速下降
        }
    }

    /**
     * 计算体温节律
     * 基于核心体温日节律研究
     */
    calculateBodyTemperature(hour, profile) {
        const peakHour = profile.bodyTempPeak;
        const lowHour = (peakHour + 12) % 24; // 体温最低点在峰值后12小时
        
        // 体温呈正弦波变化，振幅约1°C
        const phase = (hour - lowHour + 24) % 24 / 24 * 2 * Math.PI;
        return Math.sin(phase) * 0.5 + 0.5; // 0-1标准化
    }

    /**
     * 计算警觉性水平
     * 综合多个生理指标
     */
    calculateAlertness(hour, profile) {
        const cortisol = this.calculateCortisolLevel(hour, profile);
        const melatonin = this.calculateMelatoninLevel(hour, profile);
        const bodyTemp = this.calculateBodyTemperature(hour, profile);
        const lightEffect = this.calculateLightEffect(hour);
        
        // 警觉性与皮质醇、体温正相关，与褪黑素负相关
        const alertness = (cortisol * 0.4 + bodyTemp * 0.3 + (1 - melatonin) * 0.2 + lightEffect * 0.1);
        return Math.max(0, Math.min(1, alertness));
    }

    /**
     * 计算认知表现
     * 基于认知能力的时间变化研究
     */
    calculateCognitivePerformance(hour, profile) {
        const alertness = this.calculateAlertness(hour, profile);
        const isOptimalWindow = profile.optimalPerformance.includes(hour);
        const energyLevel = this.getEnergyLevel(hour, profile);
        
        // 认知表现综合评估
        let performance = alertness * 0.5 + energyLevel * 0.3;
        if (isOptimalWindow) {
            performance += 0.2; // 最佳时间窗口加成
        }
        
        return Math.max(0, Math.min(1, performance));
    }

    /**
     * 计算社会时差
     * 基于社会时差理论 (Social Jetlag)
     */
    calculateSocialJetlag(currentTime) {
        const currentHour = currentTime.hour();
        const idealBedtime = moment(this.config.bedtime, 'HH:mm').hour();
        const idealWakeTime = moment(this.config.wakeTime, 'HH:mm').hour();
        
        // 计算实际作息与理想作息的偏差
        const sleepMidpoint = (idealBedtime + idealWakeTime + 24) % 24 / 2;
        const socialMidpoint = 12; // 假设社会时间中点为中午12点
        
        const jetlag = Math.abs(sleepMidpoint - socialMidpoint) / 12; // 标准化到0-1
        return Math.min(1, jetlag);
    }

    /**
     * 计算光照效应
     * 基于光照对生物钟的影响研究
     */
    calculateLightEffect(hour) {
        // 白天光照强，夜间光照弱
        if (hour >= 6 && hour <= 18) {
            return this.config.lightExposure * Math.sin((hour - 6) / 12 * Math.PI);
        } else {
            return this.config.lightExposure * 0.1; // 夜间微弱光照
        }
    }

    /**
     * 获取能量水平
     */
    getEnergyLevel(hour, profile) {
        const hourIndex = Math.floor(hour / 3); // 将24小时分为8个时段
        return profile.energyPattern[hourIndex] || 0.5;
    }

    /**
     * 判断是否为最佳表现窗口
     */
    isOptimalPerformanceWindow(hour, profile) {
        return profile.optimalPerformance.includes(hour);
    }

    /**
     * 获取详细的时间段信息
     */
    getDetailedTimeOfDay(hour) {
        const timeSegments = {
            'deep_night': [0, 1, 2, 3, 4, 5],
            'early_morning': [6, 7],
            'morning': [8, 9, 10, 11],
            'midday': [12, 13],
            'afternoon': [14, 15, 16, 17],
            'evening': [18, 19, 20],
            'night': [21, 22, 23]
        };
        
        for (const [segment, hours] of Object.entries(timeSegments)) {
            if (hours.includes(hour)) {
                return segment;
            }
        }
        return 'unknown';
    }

    /**
     * 计算生物学时间
     * 将时钟时间转换为个体的生物学时间
     */
    calculateBiologicalTime(hour, profile) {
        const chronotypeShift = {
            morning: -1,
            evening: 1,
            intermediate: 0
        }[this.config.chronotype];
        
        return (hour + chronotypeShift + 24) % 24;
    }

    /**
     * 预测未来时间段的状态
     */
    predictFutureState(hoursAhead = 1) {
        const futureTime = moment().add(hoursAhead, 'hours');
        return this.calculateCircadianState(futureTime);
    }

    /**
     * 获取今日最佳活动时间建议
     */
    getDailyOptimalTimes() {
        const profile = this.chronotypeProfiles[this.config.chronotype];
        const recommendations = {};
        
        // 分析24小时的状态
        for (let hour = 0; hour < 24; hour++) {
            const state = this.calculateCircadianState(moment().hour(hour));
            
            if (!recommendations.bestFocus || state.cognitivePerformance > recommendations.bestFocus.performance) {
                recommendations.bestFocus = { hour, performance: state.cognitivePerformance };
            }
            
            if (!recommendations.bestEnergy || state.alertnessLevel > recommendations.bestEnergy.alertness) {
                recommendations.bestEnergy = { hour, alertness: state.alertnessLevel };
            }
            
            if (!recommendations.bestRest || state.melatoninLevel > recommendations.bestRest.melatonin) {
                recommendations.bestRest = { hour, melatonin: state.melatoninLevel };
            }
        }
        
        return recommendations;
    }

    /**
     * 计算时间相关的心理影响因子
     */
    getTimeBasedPsychologyFactors(currentTime = moment()) {
        const state = this.calculateCircadianState(currentTime);
        
        return {
            // 基础生理因子
            energyMultiplier: state.alertnessLevel,
            focusMultiplier: state.cognitivePerformance,
            stressResistance: state.cortisolLevel,
            recoveryRate: 1 - state.melatoninLevel,
            
            // 时间段特征
            timeOfDay: state.timeOfDay,
            isOptimalWindow: state.optimalWindow,
            socialJetlagPenalty: state.socialJetlag,
            
            // 生物钟同步度
            circadianAlignment: 1 - Math.abs(state.circadianPhase - 0.5) * 2,
            chronotypeMatch: this.calculateChronotypeMatch(currentTime),
            
            // 元数据
            biologicalTime: state.biologicalTime,
            chronotype: this.config.chronotype
        };
    }

    /**
     * 计算当前时间与个体时型的匹配度
     */
    calculateChronotypeMatch(currentTime) {
        const hour = currentTime.hour();
        const profile = this.chronotypeProfiles[this.config.chronotype];
        
        if (profile.optimalPerformance.includes(hour)) {
            return 1.0; // 完美匹配
        }
        
        // 计算与最近最佳时间的距离
        const distances = profile.optimalPerformance.map(optHour => {
            return Math.min(Math.abs(hour - optHour), 24 - Math.abs(hour - optHour));
        });
        
        const minDistance = Math.min(...distances);
        return Math.max(0, 1 - minDistance / 12); // 距离越远匹配度越低
    }
}

module.exports = ChronobiologyModule;
