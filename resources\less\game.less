@primary-color: #4caf50;
@secondary-color: #2196f3;

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
}

.chat-container {
  max-width: 600px;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chat-box {
  height: 300px;
  overflow-y: auto;
  padding: 20px;
}

input[type="text"] {
  width: calc(100% - 80px);
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  width: 80px;
  padding: 10px;
  background-color: @primary-color;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: darken(@primary-color, 10%);
}

.message {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
}

.user-message {
  background-color: @primary-color;
  color: #fff;
  text-align: right;
}

.bot-message {
  background-color: @secondary-color;
  color: #fff;
}