/**
 * 沙盒世界安装脚本
 * 自动安装依赖和初始化系统
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class SandboxWorldInstaller {
    constructor() {
        this.projectRoot = __dirname;
        this.logPrefix = '[SandboxWorld安装器]';
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `${this.logPrefix} [${timestamp}]`;
        
        switch (type) {
            case 'error':
                console.error(`❌ ${prefix} ${message}`);
                break;
            case 'warning':
                console.warn(`⚠️ ${prefix} ${message}`);
                break;
            case 'success':
                console.log(`✅ ${prefix} ${message}`);
                break;
            default:
                console.log(`ℹ️ ${prefix} ${message}`);
        }
    }

    async checkNodeVersion() {
        this.log('检查Node.js版本...');
        
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
        
        if (majorVersion < 14) {
            throw new Error(`需要Node.js 14.0.0或更高版本，当前版本: ${nodeVersion}`);
        }
        
        this.log(`Node.js版本检查通过: ${nodeVersion}`, 'success');
    }

    async checkDirectories() {
        this.log('检查目录结构...');
        
        const requiredDirs = [
            'data',
            'data/agents',
            'data/events',
            'public'
        ];

        for (const dir of requiredDirs) {
            const dirPath = path.join(this.projectRoot, dir);
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
                this.log(`创建目录: ${dir}`, 'success');
            }
        }
    }

    async installDependencies() {
        this.log('安装依赖包...');
        
        return new Promise((resolve, reject) => {
            const npm = spawn('npm', ['install'], {
                cwd: this.projectRoot,
                stdio: 'inherit'
            });

            npm.on('close', (code) => {
                if (code === 0) {
                    this.log('依赖包安装完成', 'success');
                    resolve();
                } else {
                    reject(new Error(`npm install 失败，退出码: ${code}`));
                }
            });

            npm.on('error', (error) => {
                reject(new Error(`npm install 错误: ${error.message}`));
            });
        });
    }

    async createInitialData() {
        this.log('创建初始数据文件...');
        
        // 创建初始世界数据
        const worldDataPath = path.join(this.projectRoot, 'data', 'world.json');
        if (!fs.existsSync(worldDataPath)) {
            const initialWorldData = {
                isRunning: false,
                isPaused: false,
                worldTime: new Date().toISOString(),
                totalRunTime: 0,
                generation: 0,
                population: 0,
                events: [],
                statistics: {
                    totalInteractions: 0,
                    totalEvents: 0,
                    averageHappiness: 0,
                    socialCohesion: 0
                },
                createdAt: new Date().toISOString()
            };
            
            fs.writeFileSync(worldDataPath, JSON.stringify(initialWorldData, null, 2));
            this.log('创建初始世界数据', 'success');
        }

        // 创建初始关系数据
        const relationshipsPath = path.join(this.projectRoot, 'data', 'relationships.json');
        if (!fs.existsSync(relationshipsPath)) {
            fs.writeFileSync(relationshipsPath, JSON.stringify({}, null, 2));
            this.log('创建初始关系数据', 'success');
        }

        // 创建初始统计数据
        const statisticsPath = path.join(this.projectRoot, 'data', 'statistics.json');
        if (!fs.existsSync(statisticsPath)) {
            const initialStats = {
                totalRunTime: 0,
                totalAgents: 0,
                totalEvents: 0,
                totalConversations: 0,
                lastUpdated: new Date().toISOString()
            };
            
            fs.writeFileSync(statisticsPath, JSON.stringify(initialStats, null, 2));
            this.log('创建初始统计数据', 'success');
        }

        // 创建事件历史文件
        const eventHistoryPath = path.join(this.projectRoot, 'data', 'events', 'history.json');
        if (!fs.existsSync(eventHistoryPath)) {
            fs.writeFileSync(eventHistoryPath, JSON.stringify([], null, 2));
            this.log('创建事件历史文件', 'success');
        }
    }

    async validateInstallation() {
        this.log('验证安装...');
        
        // 检查关键文件
        const requiredFiles = [
            'SandboxWorldCore.js',
            'WorldEnvironment.js',
            'AgentEcosystem.js',
            'SocialNetwork.js',
            'AutonomousDialogue.js',
            'EventSystem.js',
            'WebInterface.js',
            'config.json',
            'package.json'
        ];

        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) {
                throw new Error(`缺少关键文件: ${file}`);
            }
        }

        // 检查依赖
        const nodeModulesPath = path.join(this.projectRoot, 'node_modules');
        if (!fs.existsSync(nodeModulesPath)) {
            throw new Error('依赖包未正确安装');
        }

        this.log('安装验证通过', 'success');
    }

    async install() {
        try {
            this.log('开始安装沙盒世界系统...');
            
            await this.checkNodeVersion();
            await this.checkDirectories();
            await this.installDependencies();
            await this.createInitialData();
            await this.validateInstallation();
            
            this.log('🎉 沙盒世界系统安装完成！', 'success');
            this.log('');
            this.log('📋 下一步操作:');
            this.log('1. 运行: node SandboxWorld.js 启动系统');
            this.log('2. 或通过VCP插件系统调用');
            this.log('3. 访问Web界面: http://localhost:8080');
            this.log('');
            
            return true;
            
        } catch (error) {
            this.log(`安装失败: ${error.message}`, 'error');
            throw error;
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const installer = new SandboxWorldInstaller();
    installer.install().catch(error => {
        console.error('安装过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = SandboxWorldInstaller;
