# 🌍 Sentra 沙盒世界 - 使用说明

## 🚀 快速启动

### 方法一：使用批处理文件（推荐）

1. **安装依赖**
   - 双击 `安装依赖.bat`
   - 等待依赖安装完成

2. **启动系统**
   - 双击 `启动沙盒世界.bat`
   - 系统会自动启动并显示Web界面地址

3. **访问管理界面**
   - 打开浏览器访问 `http://localhost:8080`
   - 开始管理您的沙盒世界！

### 方法二：命令行启动

```bash
# 1. 安装依赖
npm install

# 2. 运行安装脚本
node install.js

# 3. 启动系统
node start.js

# 或者运行测试
node test.js
```

### 方法三：通过VCP插件系统

```javascript
// 初始化
const result = await pluginManager.executePlugin('SandboxWorld', JSON.stringify({
    action: 'init'
}));

// 启动
await pluginManager.executePlugin('SandboxWorld', JSON.stringify({
    action: 'start'
}));
```

## 🎮 Web管理界面功能

### 主要控制面板

1. **世界控制**
   - 🚀 启动世界：开始沙盒世界模拟
   - ⏸️ 暂停：暂停所有活动但保持状态
   - ▶️ 恢复：从暂停状态恢复
   - 🛑 停止：完全停止并保存数据

2. **时间控制**
   - 调整时间流速（0.1x - 10x）
   - 实时显示世界时间

3. **Agent管理**
   - 查看所有Agent状态
   - 添加随机Agent
   - 监控Agent心情和需求

4. **事件监控**
   - 查看活跃事件
   - 手动触发事件
   - 事件历史记录

5. **对话监控**
   - 实时对话列表
   - 对话参与者信息
   - 对话类型和状态

## 🔧 配置说明

### 环境配置文件 (config.env)

```env
# 基础配置
WORLD_NAME=我的沙盒世界
MAX_AGENTS=50
TIME_SPEED=1

# Web界面
WEB_INTERFACE_PORT=8080

# 更新频率 (毫秒)
ENVIRONMENT_UPDATE_FREQUENCY=60000
AGENT_UPDATE_FREQUENCY=30000
DIALOGUE_UPDATE_FREQUENCY=45000
```

### 系统配置文件 (config.json)

主要配置项：
- `world`: 世界基础设置
- `agents`: Agent行为参数
- `dialogue`: 对话系统设置
- `events`: 事件系统配置
- `integration`: 与其他系统的集成

## 📊 API接口使用

### RESTful API

```javascript
// 获取世界状态
GET /api/world/status

// 启动世界
POST /api/world/start

// 获取所有Agent
GET /api/agents

// 添加Agent
POST /api/agents
{
  "name": "新Agent",
  "age": 25,
  "templateId": "现有Agent模板ID"
}

// 触发事件
POST /api/events/trigger
{
  "eventType": "festival_announcement",
  "context": {}
}
```

### VCP插件命令

```javascript
// 所有可用命令
const commands = [
  'init',           // 初始化系统
  'start',          // 启动世界
  'stop',           // 停止世界
  'pause',          // 暂停世界
  'resume',         // 恢复世界
  'addAgent',       // 添加Agent
  'removeAgent',    // 移除Agent
  'triggerEvent',   // 触发事件
  'setTimeSpeed',   // 设置时间流速
  'getStatus'       // 获取状态
];
```

## 🎯 使用场景示例

### 1. 创建小型社区

```javascript
// 添加几个不同性格的Agent
await addAgent({ name: '外向的小明', personality: { extroversion: 80 } });
await addAgent({ name: '内向的小红', personality: { extroversion: 20 } });
await addAgent({ name: '友善的小李', personality: { agreeableness: 90 } });

// 触发社交事件
await triggerEvent('weekly_market');
```

### 2. 观察关系发展

1. 启动世界并添加多个Agent
2. 观察他们的自主对话
3. 查看关系网络的变化
4. 触发特殊事件影响关系

### 3. 研究群体行为

1. 添加大量Agent（接近上限）
2. 观察社交群体的形成
3. 分析影响力分布
4. 研究事件对群体的影响

## 🔍 故障排除

### 常见问题

1. **系统无法启动**
   ```
   解决方案：
   - 检查Node.js版本（需要14.0.0+）
   - 运行 npm install 安装依赖
   - 检查端口8080是否被占用
   ```

2. **Web界面无法访问**
   ```
   解决方案：
   - 确认系统已启动
   - 检查防火墙设置
   - 尝试访问 http://127.0.0.1:8080
   ```

3. **Agent无法加载**
   ```
   解决方案：
   - 检查Agent文件格式
   - 确认Agent目录存在
   - 查看控制台错误信息
   ```

4. **依赖安装失败**
   ```
   解决方案：
   - 使用国内镜像：npm config set registry https://registry.npmmirror.com
   - 清除缓存：npm cache clean --force
   - 删除node_modules重新安装
   ```

### 日志查看

- **控制台日志**: 启动时的实时输出
- **Web界面日志**: 管理界面右下角的日志面板
- **文件日志**: 如果启用，保存在logs目录

### 性能优化

1. **减少更新频率**
   - 修改config.env中的UPDATE_FREQUENCY参数
   - 适当增加间隔时间

2. **限制Agent数量**
   - 根据硬件性能调整MAX_AGENTS
   - 建议从少量Agent开始

3. **关闭不需要的功能**
   - 在config.json中禁用某些系统
   - 减少事件触发频率

## 📈 监控和统计

### 实时监控指标

- **Agent数量**: 当前世界中的Agent总数
- **活跃事件**: 正在进行的事件数量
- **对话数量**: 当前活跃的对话数量
- **运行时间**: 世界总运行时间
- **平均心情**: 所有Agent的平均心情值

### 详细统计信息

通过API获取详细统计：
```javascript
const stats = await fetch('/api/statistics').then(r => r.json());
console.log(stats);
```

包含：
- Agent分布统计
- 关系网络分析
- 事件历史统计
- 对话模式分析

## 🎨 自定义扩展

### 添加自定义事件

1. 编辑 `EventSystem.js`
2. 在 `eventTemplates` 中添加新事件
3. 定义事件效果和触发条件

### 创建新地点

1. 编辑 `WorldEnvironment.js`
2. 在 `initializeLocations` 中添加新地点
3. 设置地点属性和活动

### 自定义Agent行为

1. 修改 `AgentEcosystem.js` 中的行为逻辑
2. 调整性格影响参数
3. 添加新的需求类型

## 🤝 技术支持

如果遇到问题：

1. 查看本文档的故障排除部分
2. 检查控制台错误信息
3. 运行测试脚本：`node test.js`
4. 查看GitHub Issues或提交新问题

## 🎉 开始探索

现在您已经了解了基本使用方法，开始创建您的沙盒世界吧！

每个世界都是独特的，Agent们会根据他们的性格、经历和环境产生不同的行为和关系。观察他们的互动，发现意想不到的故事！
