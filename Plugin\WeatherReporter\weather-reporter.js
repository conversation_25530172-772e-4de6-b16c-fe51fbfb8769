// Plugin/WeatherReporter/weather-reporter.js
const fs = require('fs').promises;
const path = require('path');
const dotenv = require('dotenv');
const stdin = require('process').stdin;

// 引入token工具
const { tokenUtils } = require('../../utils/tokenUtils');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    }
}

// const fetch = require('node-fetch'); // Use require for node-fetch - Removed

/**
 * 加载插件配置，优先使用插件自己的config.env
 */
function loadPluginConfig() {
    const pluginConfigPath = path.join(__dirname, 'config.env');

    let pluginConfig = {};

    // 加载插件配置
    try {
        const configContent = require('fs').readFileSync(pluginConfigPath, 'utf8');
        pluginConfig = parseEnvConfig(configContent);
        logger.info('天气报告', `从插件config.env加载了 ${Object.keys(pluginConfig).length} 个配置项`);
    } catch (error) {
        logger.info('天气报告', `插件config.env不存在或读取失败，使用主服务器配置作为备用`);
    }

    // 加载主服务器配置作为备用
    dotenv.config({ path: path.resolve(__dirname, '../../config.env') });

    // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
    const mergedConfig = {
        VarCity: pluginConfig.VarCity || process.env.VarCity || '',
        WeatherKey: pluginConfig.WeatherKey || process.env.WeatherKey || '',
        WeatherUrl: pluginConfig.WeatherUrl || process.env.WeatherUrl || '',
        DebugMode: pluginConfig.DebugMode || process.env.DebugMode || 'false'
    };

    // 将配置设置到process.env中，保持兼容性
    Object.keys(mergedConfig).forEach(key => {
        if (mergedConfig[key]) {
            process.env[key] = mergedConfig[key];
        }
    });

    return mergedConfig;
}

/**
 * 解析.env格式的配置文件
 */
function parseEnvConfig(content) {
    const config = {};
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                config[key] = value;
            }
        }
    });
    return config;
}

// 加载配置：优先使用插件配置，主服务器配置作为备用
const config = loadPluginConfig();

const CACHE_FILE_PATH = path.join(__dirname, 'weather_cache.txt');
const CITY_CACHE_FILE_PATH = path.join(__dirname, 'city_cache.txt');
const WEATHER_DATA_CACHE_PATH = path.join(__dirname, 'weather_data_cache.json');

// --- Start Cache Management Functions ---

// 检查缓存是否过期（每天刷新）
function isCacheExpired(timestamp) {
    if (!timestamp) return true;
    
    const now = new Date();
    const cacheDate = new Date(timestamp);
    
    // 检查是否是同一天
    return now.toDateString() !== cacheDate.toDateString();
}

// 读取天气数据缓存
async function readWeatherDataCache() {
    try {
        const data = await fs.readFile(WEATHER_DATA_CACHE_PATH, 'utf-8');
        const cache = JSON.parse(data);
        logger.debug('天气报告', `成功读取天气数据缓存文件`);
        return cache;
    } catch (error) {
        if (error.code !== 'ENOENT') {
            logger.error('天气报告', `读取天气数据缓存文件时出错:`, error.message);
        }
        return {}; // Return empty object if file doesn't exist or error occurs
    }
}

// 写入天气数据缓存
async function writeWeatherDataCache(cache) {
    try {
        await fs.writeFile(WEATHER_DATA_CACHE_PATH, JSON.stringify(cache, null, 2), 'utf-8');
        logger.debug('天气报告', `成功写入天气数据缓存文件`);
    } catch (error) {
        logger.error('天气报告', `写入天气数据缓存文件时出错:`, error.message);
    }
}

// 生成缓存键
function generateCacheKey(city, queryType) {
    return `${city}_${queryType}`;
}

// 获取缓存的天气数据
async function getCachedWeatherData(city, queryType) {
    try {
        const cache = await readWeatherDataCache();
        const cacheKey = generateCacheKey(city, queryType);
        const cachedData = cache[cacheKey];
        
        if (cachedData && !isCacheExpired(cachedData.timestamp)) {
            logger.debug('天气报告', `使用缓存的天气数据: ${cacheKey}`);
            return cachedData.data;
        }
        
        if (cachedData && isCacheExpired(cachedData.timestamp)) {
            logger.debug('天气报告', `天气数据缓存已过期: ${cacheKey}`);
        }
        
        return null;
    } catch (error) {
        logger.error('天气报告', `获取缓存天气数据时出错:`, error.message);
        return null;
    }
}

// 缓存天气数据
async function cacheWeatherData(city, queryType, data) {
    try {
        const cache = await readWeatherDataCache();
        const cacheKey = generateCacheKey(city, queryType);
        
        cache[cacheKey] = {
            timestamp: new Date().toISOString(),
            data: data
        };
        
        await writeWeatherDataCache(cache);
        logger.debug('天气报告', `成功缓存天气数据: ${cacheKey}`);
    } catch (error) {
        logger.error('天气报告', `缓存天气数据时出错:`, error.message);
    }
}

// --- End Cache Management Functions ---

// --- Start QWeather API Functions ---

// Function to read city cache
async function readCityCache() {
    try {
        const data = await fs.readFile(CITY_CACHE_FILE_PATH, 'utf-8');
        const cache = new Map();
        data.split('\n').forEach(line => {
            const [cityName, cityId] = line.split(':');
            if (cityName && cityId) {
                cache.set(cityName.trim(), cityId.trim());
            }
        });
        logger.debug('天气报告', `成功读取城市缓存文件 ${CITY_CACHE_FILE_PATH}`);
        return cache;
    } catch (error) {
        if (error.code !== 'ENOENT') {
            logger.error('天气报告', `读取城市缓存文件时出错 ${CITY_CACHE_FILE_PATH}:`, error.message);
        }
        return new Map(); // Return empty map if file doesn't exist or error occurs
    }
}

// Function to write city cache
async function writeCityCache(cityName, cityId) {
    try {
        // Append to the file, creating it if it doesn't exist
        await fs.appendFile(CITY_CACHE_FILE_PATH, `${cityName}:${cityId}\n`, 'utf-8');
        logger.debug('天气报告', `成功写入城市缓存 ${cityName}:${cityId} 到 ${CITY_CACHE_FILE_PATH}`);
    } catch (error) {
        logger.error('天气报告', `写入城市缓存文件时出错 ${CITY_CACHE_FILE_PATH}:`, error.message);
    }
}

// Function to get City ID from city name
async function getCityId(cityName, weatherKey, weatherUrl) {
    const { default: fetch } = await import('node-fetch'); // Dynamic import
    if (!cityName || !weatherKey || !weatherUrl) {
        logger.error('天气报告', '获取城市ID缺少必要参数：城市名称、天气密钥或天气URL');
        return { success: false, data: null, error: new Error('Missing parameters for getCityId.') };
    }

    // Check cache first
    const cityCache = await readCityCache();
    if (cityCache.has(cityName)) {
        const cachedCityId = cityCache.get(cityName);
        logger.debug('天气报告', `使用缓存的城市ID ${cityName}: ${cachedCityId}`);
        return { success: true, data: cachedCityId, error: null };
    }

    const lookupUrl = `https://${weatherUrl}/geo/v2/city/lookup?location=${encodeURIComponent(cityName)}&key=${weatherKey}`;

    try {
        logger.debug('天气报告', `正在获取城市ID：${cityName}`);
        const response = await fetch(lookupUrl, { timeout: 10000 }); // 10s timeout

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`QWeather City Lookup API failed: ${response.status} ${errorText.substring(0, 200)}`);
        }

        const data = await response.json();
        if (data.code === '200' && data.location && data.location.length > 0) {
            const cityId = data.location[0].id;
            logger.debug('天气报告', `成功找到城市ID：${cityId}`);
            // Write to cache
            await writeCityCache(cityName, cityId);
            return { success: true, data: cityId, error: null };
        } else {
             const errorMsg = data.code === '200' ? 'No location found' : `API returned code ${data.code}`;
             throw new Error(`获取城市ID失败：${cityName}。${errorMsg}`);
        }

    } catch (error) {
        logger.error('天气报告', `获取城市ID时出错：${error.message}`);
        return { success: false, data: null, error: error };
    }
}

// Function to get Current Weather from City ID
async function getCurrentWeather(cityId, weatherKey, weatherUrl) {
    const { default: fetch } = await import('node-fetch'); // Dynamic import
    if (!cityId || !weatherKey || !weatherUrl) {
        logger.error('天气报告', '获取当前天气缺少必要参数：城市ID、天气密钥或天气URL');
        return { success: false, data: null, error: new Error('Missing parameters for getCurrentWeather.') };
    }

    const weatherUrlEndpoint = `https://${weatherUrl}/v7/weather/now?location=${cityId}&key=${weatherKey}`;

    try {
        logger.debug('天气报告', `正在获取当前天气，城市ID：${cityId}`);
        const response = await fetch(weatherUrlEndpoint, { timeout: 10000 }); // 10s timeout

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`QWeather Current Weather API failed: ${response.status} ${errorText.substring(0, 200)}`);
        }

        const data = await response.json();
         if (data.code === '200' && data.now) {
            logger.debug('天气报告', `成功获取当前天气，城市ID：${cityId}`);
            return { success: true, data: data.now, error: null };
        } else {
             throw new Error(`获取当前天气失败，城市ID：${cityId}。API返回码：${data.code}`);
        }

    } catch (error) {
        logger.error('天气报告', `获取当前天气时出错：${error.message}`);
        return { success: false, data: null, error: error };
    }
}

// Function to get 7-day Forecast from City ID
async function get7DayForecast(cityId, weatherKey, weatherUrl) {
    const { default: fetch } = await import('node-fetch'); // Dynamic import
     if (!cityId || !weatherKey || !weatherUrl) {
        logger.error('天气报告', '获取7天预报缺少必要参数：城市ID、天气密钥或天气URL');
        return { success: false, data: null, error: new Error('Missing parameters for get7DayForecast.') };
    }

    const forecastUrlEndpoint = `https://${weatherUrl}/v7/weather/7d?location=${cityId}&key=${weatherKey}`;

    try {
        logger.debug('天气报告', `正在获取7天预报，城市ID：${cityId}`);
        const response = await fetch(forecastUrlEndpoint, { timeout: 10000 }); // 10s timeout

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`QWeather 7-day Forecast API failed: ${response.status} ${errorText.substring(0, 200)}`);
        }

        const data = await response.json();
         if (data.code === '200' && data.daily) {
            logger.debug('天气报告', `成功获取7天预报，城市ID：${cityId}`);
            return { success: true, data: data.daily, error: null };
        } else {
             throw new Error(`获取7天预报失败，城市ID：${cityId}。API返回码：${data.code}`);
        }

    } catch (error) {
        logger.error('天气报告', `获取7天预报时出错：${error.message}`);
        return { success: false, data: null, error: error };
    }
}

// Function to get 24-hour Forecast from City ID
async function get24HourForecast(cityId, weatherKey, weatherUrl) {
    const { default: fetch } = await import('node-fetch'); // Dynamic import
     if (!cityId || !weatherKey || !weatherUrl) {
        logger.error('天气报告', '获取24小时预报缺少必要参数：城市ID、天气密钥或天气URL');
        return { success: false, data: null, error: new Error('Missing parameters for get24HourForecast.') };
    }

    const hourlyUrlEndpoint = `https://${weatherUrl}/v7/weather/24h?location=${cityId}&key=${weatherKey}`;

    try {
        logger.debug('天气报告', `正在获取24小时预报，城市ID：${cityId}`);
        const response = await fetch(hourlyUrlEndpoint, { timeout: 10000 }); // 10s timeout

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`QWeather 24-hour Forecast API failed: ${response.status} ${errorText.substring(0, 200)}`);
        }

        const data = await response.json();
         if (data.code === '200' && data.hourly) {
            logger.debug('天气报告', `成功获取24小时预报，城市ID：${cityId}`);
            return { success: true, data: data.hourly, error: null };
        } else {
             throw new Error(`获取24小时预报失败，城市ID：${cityId}。API返回码：${data.code}`);
        }

    } catch (error) {
        logger.error('天气报告', `获取24小时预报时出错：${error.message}`);
        return { success: false, data: null, error: error };
    }
}

// Function to get Weather Warning from City ID
async function getWeatherWarning(cityId, weatherKey, weatherUrl) {
    const { default: fetch } = await import('node-fetch'); // Dynamic import
     if (!cityId || !weatherKey || !weatherUrl) {
        logger.error('天气报告', '获取天气预警缺少必要参数：城市ID、天气密钥或天气URL');
        return { success: false, data: null, error: new Error('Missing parameters for getWeatherWarning.') };
    }

    const warningUrlEndpoint = `https://${weatherUrl}/v7/warning/now?location=${cityId}&key=${weatherKey}`;

    try {
        logger.debug('天气报告', `正在获取天气预警，城市ID：${cityId}`);
        const response = await fetch(warningUrlEndpoint, { timeout: 10000 }); // 10s timeout

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`QWeather Warning API failed: ${response.status} ${errorText.substring(0, 200)}`);
        }

        const data = await response.json();
         if (data.code === '200') {
            logger.debug('天气报告', `成功获取天气预警，城市ID：${cityId}`);
            return { success: true, data: data.warning || [], error: null };
        } else {
             throw new Error(`获取天气预警失败，城市ID：${cityId}。API返回码：${data.code}`);
        }

    } catch (error) {
        logger.error('天气报告', `获取天气预警时出错：${error.message}`);
        return { success: false, data: null, error: error };
    }
}

// Helper to format weather data into a readable string
function formatWeatherInfo(currentWeather, hourlyForecast, weatherWarning, forecast, queryType = 'all') {
    if (!currentWeather && !hourlyForecast && (!weatherWarning || weatherWarning.length === 0) && (!forecast || forecast.length === 0)) {
        return "[天气信息获取失败]";
    }

    let result = "";

    // Current Weather section
    if ((queryType === 'current' || queryType === 'all') && currentWeather) {
        result += "【当前天气】\n";
        result += `天气: ${currentWeather.text}\n`;
        result += `温度: ${currentWeather.temp}℃\n`;
        result += `体感温度: ${currentWeather.feelsLike}℃\n`;
        result += `风向: ${currentWeather.windDir}\n`;
        result += `风力: ${currentWeather.windScale}级\n`;
        result += `风速: ${currentWeather.windSpeed}公里/小时\n`;
        result += `湿度: ${currentWeather.humidity}%\n`;
        result += `降水量: ${currentWeather.precip}毫米\n`;
        result += `能见度: ${currentWeather.vis}公里\n`;
        result += `云量: ${currentWeather.cloud}%\n`;
        result += `大气压强: ${currentWeather.pressure}百帕\n`;
        result += `数据观测时间: ${new Date(currentWeather.obsTime).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n`;
        result += "\n";
    }

    // Add Weather Warning section
    if ((queryType === 'warning' || queryType === 'all') && weatherWarning) {
    result += "【天气预警】\n";
        if (weatherWarning.length > 0) {
        weatherWarning.forEach(warning => {
            result += `\n标题: ${warning.title}\n`;
            result += `发布时间: ${new Date(warning.pubTime).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n`;
            result += `级别: ${warning.severityColor || '未知'}\n`;
            result += `类型: ${warning.typeName}\n`;
            result += `内容: ${warning.text}\n`;
        });
    } else {
        result += "当前无天气预警信息。\n";
        }
        result += "\n";
    }

    // Add 24-hour Forecast section
    if ((queryType === 'hourly' || queryType === 'all') && hourlyForecast) {
        result += "【未来24小时天气预报】\n";
        if (hourlyForecast.length > 0) {
        // Only show the first 8 hours, the 10th hour, and the 12th hour
        for (let i = 0; i < hourlyForecast.length; i++) {
            if (i < 8 || i === 9 || i === 11|| i === 16|| i === 20) {
                const hour = hourlyForecast[i];
                const time = new Date(hour.fxTime).toLocaleString('zh-CN', { hour: '2-digit', minute: '2-digit', timeZone: 'Asia/Shanghai' });
                result += `\n时间: ${time}\n`;
                result += `天气: ${hour.text}\n`;
                result += `温度: ${hour.temp}℃\n`;
                result += `风向: ${hour.windDir}\n`;
                result += `风力: ${hour.windScale}级\n`;
                result += `湿度: ${hour.humidity}%\n`;
                result += `降水概率: ${hour.pop}%\n`;
                result += `降水量: ${hour.precip}毫米\n`;
            }
        }
    } else {
        result += "未来24小时天气预报获取失败。\n";
        }
        result += "\n";
    }

    // Keep 7-day Forecast section
    if ((queryType === 'forecast' || queryType === 'all') && forecast) {
        if (forecast.length > 0) {
            result += "【未来7日天气预报】\n";
        forecast.forEach(day => {
            result += `\n日期: ${day.fxDate}\n`;
            result += `白天: ${day.textDay} (图标: ${day.iconDay}), 最高温: ${day.tempMax}℃, 风向: ${day.windDirDay}, 风力: ${day.windScaleDay}级\n`;
            result += `夜间: ${day.textNight} (图标: ${day.iconNight}), 最低温: ${day.tempMin}℃, 风向: ${day.windDirNight}, 风力: ${day.windScaleNight}级\n`;
            result += `湿度: ${day.humidity}%\n`;
            result += `降水: ${day.precip}毫米\n`;
            result += `紫外线指数: ${day.uvIndex}\n`;
        });
    } else {
             result += "未来7日天气预报获取失败。\n";
        }
    }

    return result.trim();
}

// --- End QWeather API Functions ---

// 获取静态插件的缓存天气数据（用于系统提示词占位符）
async function getStaticCachedWeather() {
    try {
        const cachedData = await fs.readFile(CACHE_FILE_PATH, 'utf-8');
        // Basic validation: check if it's not an error message itself
        if (cachedData && !cachedData.startsWith("[Error") && !cachedData.startsWith("[天气API请求失败")) {
            return cachedData.trim();
        }
    } catch (error) {
        if (error.code !== 'ENOENT') {
            logger.error('天气报告', `读取静态缓存文件时出错 ${CACHE_FILE_PATH}:`, error.message);
        }
    }
    return null;
}

async function fetchAndCacheWeather(cityName = null, queryType = 'all') {
    let lastError = null;

    const varCity = cityName || process.env.VarCity;
    const weatherKey = process.env.WeatherKey;
    const weatherUrl = process.env.WeatherUrl;

    if (!varCity || !weatherKey || !weatherUrl) {
        lastError = new Error('天气插件错误：获取天气所需的配置不完整 (VarCity, WeatherKey, WeatherUrl)。');
        logger.error('天气报告', `${lastError.message}`);
        return { success: false, data: null, error: lastError };
    }

    // 首先检查缓存
    const cachedData = await getCachedWeatherData(varCity, queryType);
    if (cachedData) {
        logger.info('天气报告', `使用缓存的天气数据: ${varCity} - ${queryType}`);
        return { success: true, data: cachedData, error: null, fromCache: true };
    }

    let cityId = null;
    let currentWeather = null;
    let hourlyForecast = null;
    let weatherWarning = null;
    let forecast = null;

    // 1. Get City ID
    const cityResult = await getCityId(varCity, weatherKey, weatherUrl);
    if (cityResult.success) {
        cityId = cityResult.data;
    } else {
        lastError = cityResult.error;
        logger.error('天气报告', `获取失败 city ID: ${lastError.message}`);
        return { success: false, data: null, error: lastError };
    }

    // 2. Get Current Weather (if requested)
    if ((queryType === 'current' || queryType === 'all') && cityId) {
        const currentResult = await getCurrentWeather(cityId, weatherKey, weatherUrl);
        if (currentResult.success) {
            currentWeather = currentResult.data;
        } else {
            lastError = currentResult.error;
            logger.error('天气报告', `获取失败 current weather: ${lastError.message}`);
        }
    }

    // 3. Get 24-hour Forecast (if requested)
    if ((queryType === 'hourly' || queryType === 'all') && cityId) {
        const hourlyResult = await get24HourForecast(cityId, weatherKey, weatherUrl);
        if (hourlyResult.success) {
            hourlyForecast = hourlyResult.data;
        } else {
            lastError = hourlyResult.error;
            logger.error('天气报告', `获取失败 24-hour forecast: ${lastError.message}`);
        }
    }

    // 4. Get Weather Warning (if requested)
    if ((queryType === 'warning' || queryType === 'all') && cityId) {
        const warningResult = await getWeatherWarning(cityId, weatherKey, weatherUrl);
        if (warningResult.success) {
            weatherWarning = warningResult.data;
        } else {
            lastError = warningResult.error;
            logger.error('天气报告', `获取失败 weather warning: ${lastError.message}`);
        }
    }

    // 5. Get 7-day Forecast (if requested)
    if ((queryType === 'forecast' || queryType === 'all') && cityId) {
        const forecastResult = await get7DayForecast(cityId, weatherKey, weatherUrl);
        if (forecastResult.success) {
            forecast = forecastResult.data;
        } else {
            lastError = forecastResult.error;
            logger.error('天气报告', `获取失败 7-day forecast: ${lastError.message}`);
        }
    }

    // 6. Format and Cache the results
    if (currentWeather || hourlyForecast || weatherWarning || (forecast && forecast.length > 0)) {
        const formattedWeather = formatWeatherInfo(currentWeather, hourlyForecast, weatherWarning, forecast, queryType);
        
        // 缓存天气数据
        await cacheWeatherData(varCity, queryType, formattedWeather);
        
        // 如果是默认城市的全部信息查询，也更新静态插件缓存（用于系统提示词占位符）
        if (queryType === 'all' && varCity === process.env.VarCity) {
        try {
            await fs.writeFile(CACHE_FILE_PATH, formattedWeather, 'utf-8');
                logger.info('天气报告', '成功更新静态插件缓存文件');
        } catch (writeError) {
                logger.error('天气报告', `写入静态缓存文件时出错: ${writeError.message}`);
            }
        }
        
        return { success: true, data: formattedWeather, error: null, fromCache: false };
    } else {
        lastError = lastError || new Error("未能获取天气信息。");
        logger.error('天气报告', `${lastError.message}`);
        return { success: false, data: null, error: lastError };
    }
}

// 新增：处理stdio输入的主函数
async function handleStdioInput() {
    let inputData = '';
    stdin.setEncoding('utf8');

    stdin.on('data', function(chunk) {
        inputData += chunk;
    });

    stdin.on('end', async function() {
        let output = {};
        try {
            if (!inputData.trim()) {
                throw new Error("未从 stdin 接收到输入数据。");
            }

            const data = JSON.parse(inputData);
            const queryType = data.query_type || 'current';
            const city = data.city;

            logger.info('天气报告', `开始处理天气查询: 类型=${queryType}, 城市=${city || '默认'}`);

            // 验证查询类型
            const validTypes = ['current', 'forecast', 'hourly', 'warning', 'all'];
            if (!validTypes.includes(queryType)) {
                throw new Error(`无效的查询类型: ${queryType}。支持的类型: ${validTypes.join(', ')}`);
            }

            const result = await fetchAndCacheWeather(city, queryType);

            if (result.success && result.data) {
                logger.info('天气报告', `天气查询成功，原始内容长度: ${result.data.length} 字符${result.fromCache ? ' (来自缓存)' : ''}`);
                
                // 使用token工具处理内容截断
                const tokenResult = tokenUtils.processPluginContent(result.data, 'WEATHERREPORTER');
                
                logger.info('天气报告', `Token处理结果: 原始${tokenResult.originalTokens}个token, 最终${tokenResult.finalTokens}个token, 是否截断: ${tokenResult.truncated}`);
                
                if (tokenResult.truncated) {
                    logger.warning('天气报告', `天气信息已截断，原始token数量超过限制 (${tokenResult.originalTokens} > ${tokenResult.config.maxTokens})`);
                }
                
                output = {
                    status: "success",
                    result: tokenResult.content,
                    token_info: {
                        original_tokens: tokenResult.originalTokens,
                        final_tokens: tokenResult.finalTokens,
                        truncated: tokenResult.truncated,
                        max_tokens: tokenResult.config.maxTokens,
                        truncate_enabled: tokenResult.config.enabled,
                        query_type: queryType,
                        city: city || process.env.VarCity,
                        from_cache: result.fromCache || false
                    }
                };
            } else {
                throw new Error(result.error ? result.error.message : '天气查询失败');
            }

        } catch (e) {
            let errorMessage;
            if (e instanceof SyntaxError) {
                errorMessage = "无效的 JSON 输入。";
            } else if (e instanceof Error) {
                errorMessage = e.message;
            } else {
                errorMessage = "发生未知错误。";
            }
            
            logger.error('天气报告', `处理失败: ${errorMessage}`);
            output = { status: "error", error: `WeatherReporter 错误: ${errorMessage}` };
        }

        process.stdout.write(JSON.stringify(output, null, 2));
    });
}

// 原有的静态模式main函数（用于系统提示词占位符）
async function staticMain() {
    // 使用默认配置获取全部天气信息
    const apiResult = await fetchAndCacheWeather();

    if (apiResult.success && apiResult.data) {
        process.stdout.write(apiResult.data);
        process.exit(0);
    } else {
        // API failed, try to use static cache
        const cachedData = await getStaticCachedWeather();
        if (cachedData) {
            logger.warning('天气报告', "API获取失败，使用静态缓存数据");
            process.stdout.write(cachedData);
            process.exit(0); // Exit 0 because we are providing data, albeit stale.
        } else {
            // API failed AND no cache available
            const errorMessage = `[天气API请求失败且无可用缓存: ${apiResult.error ? apiResult.error.message.substring(0,100) : '未知错误'}]`;
            logger.error('天气报告', `${errorMessage}`);
            process.stdout.write(errorMessage); // Output error to stdout so Plugin.js can use it as placeholder
            process.exit(1); // Exit 1 to indicate to Plugin.js that the update truly failed to produce a usable value.
        }
    }
}

if (require.main === module) {
    // 检查是否有stdin输入
    if (!process.stdin.isTTY) {
        // 有stdin输入，作为同步插件处理JSON输入
        handleStdioInput();
    } else {
        // 无stdin输入，作为静态插件处理
        staticMain();
    }
}
