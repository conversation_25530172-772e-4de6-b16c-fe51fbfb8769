AI助手为 静 生成的代码
生成时间: 2025-07-11 21:26:57
==================================================

代码块 1 (html)
------------------------------
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人博客</title>
    <style>
        /* 基础重置与全局样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #fdfdfd; /* 接近白色的背景 */
            color: #333; /* 主要文本颜色 */
        }

        /* 容器样式 */
        .container {
            max-width: 760px;
            margin: 40px auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        header h1 {
            font-size: 2.5em;
            margin-bottom: 0.2em;
            color: #111; /* 标题颜色加深 */
        }

        header p {
            font-size: 1.1em;
            color: #666; /* 副标题/描述颜色 */
            margin-top: 0;
        }

        /* 文章样式 */
        article {
            padding-bottom: 2em;
            border-bottom: 1px solid #eee; /* 文章间的分隔线 */
            margin-bottom: 2em;
        }

        article:last-of-type {
            border-bottom: none; /* 最后一篇文章无下划线 */
            margin-bottom: 0;
        }

        article h2 {
            font-size: 1.8em;
            margin-bottom: 0.5em;
        }

        article h2 a {
            text-decoration: none;
            color: #333;
        }

        .post-meta {
            font-size: 0.9em;
            color: #888; /* 元数据（如日期）颜色 */
            margin-bottom: 1em;
        }

        /* 页脚样式 */
        footer {
            text-align: center;
            padding: 2em 0;
            margin-top: 2em;
            font-size: 0.9em;
            color: #999;
        }
    </style>
</head>
<body>

    <div class="container">
        <header>
            <h1>我的博客</h1>
            <p>记录思考的安静角落。</p>
        </header>

        <main>
            <article>
                <h2><a href="#">第一篇日志</a></h2>
                <p class="post-meta">发布于 2025年07月11日</p>
                <p>这里是文章的第一段内容。程序停止响应时，世界却更清楚了。</p>
                <p>可以继续添加段落。每个段落都由 <code>&lt;p&gt;</code> 标签包裹。</p>
            </article>

            <article>
                <h2><a href="#">关于这个博客</a></h2>
                <p class="post-meta">发布于 2025年07月10日</p>
                <p>这是一个极简的HTML博客模板。没有复杂的脚本，没有多余的元素。只有结构和内容。</p>
            </article>
        </main>

        <footer>
            <p>&copy; 2025 你的名字</p>
        </footer>
    </div>

</body>
</html>

