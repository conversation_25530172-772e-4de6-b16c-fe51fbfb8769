/**
 * OpenAI Tools 服务
 * 负责所有与OpenAI API的交互，包括情感分析、记忆处理、概念学习等
 */

class OpenAIToolsService {
    constructor(config, logger) {
        this.config = config;
        this.logger = logger;
        this.apiKey = config.openai_api_key;
        this.apiUrl = config.openai_api_url;
        this.model = config.openai_model;
        this.embeddingModel = config.openai_embedding_model;
        this.isInitialized = false;
    }

    /**
     * 初始化服务
     */
    async initialize() {
        try {
            if (!this.apiKey) {
                throw new Error('OpenAI API密钥未配置');
            }
            
            // 移除API连接测试，直接初始化配置
            this.isInitialized = true;
            this.logger.info('OpenAI服务', '配置已加载，服务就绪');
            this.logger.success('OpenAI服务', '✅ OpenAI Tools服务初始化成功');
            return { success: true };
            
        } catch (error) {
            this.logger.error('OpenAI服务', '初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 情感分析工具
     */
    async analyzeEmotion(message, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_emotion",
                    description: "分析文本的情感状态和心理特征",
                    parameters: {
                        type: "object",
                        properties: {
                            emotion_labels: {
                                type: "array",
                                items: { type: "string" },
                                description: "识别的情感标签，如：快乐、悲伤、愤怒、恐惧、惊讶、厌恶、中性等"
                            },
                            valence: {
                                type: "number",
                                minimum: -1,
                                maximum: 1,
                                description: "情感效价，-1表示负面，0表示中性，1表示正面"
                            },
                            arousal: {
                                type: "number", 
                                minimum: -1,
                                maximum: 1,
                                description: "情感唤醒度，-1表示平静，1表示兴奋激动"
                            },
                            dominance: {
                                type: "number",
                                minimum: -1,
                                maximum: 1,
                                description: "情感支配度，-1表示被动，1表示主导"
                            },
                            intensity: {
                                type: "number",
                                minimum: 0,
                                maximum: 1,
                                description: "情感强度，0表示微弱，1表示强烈"
                            },
                            emotion_description: {
                                type: "string",
                                description: "情感状态的详细描述"
                            },
                            psychological_analysis: {
                                type: "string",
                                description: "心理状态分析，包括可能的动机、需求等"
                            },
                            stability: {
                                type: "number",
                                minimum: 0,
                                maximum: 1,
                                description: "情感稳定性，0表示波动很大，1表示非常稳定"
                            }
                        },
                        required: ["emotion_labels", "valence", "arousal", "dominance", "intensity", "emotion_description"]
                    }
                }
            }];

            // 构建增强的分析提示，考虑高级心理状态
            let prompt = `请分析以下文本的情感状态：

文本内容：${message}

基础上下文信息：${JSON.stringify({
                userId: context.userId,
                personaName: context.personaName,
                messageType: context.messageType,
                userMessage: context.userMessage
            }, null, 2)}`;

            // 如果包含高级心理状态，添加到分析中
            if (context.context === 'ai_emotion_analysis_with_advanced_state') {
                prompt += `

🧠 接收者的高级心理状态背景：
`;
                if (context.stressState) {
                    prompt += `
压力状态：
- 当前压力值：${context.stressState.stress_value || 0}
- 压力等级：${context.stressState.stress_level || '正常'}
- 行为影响：${JSON.stringify(context.stressState.behavior_impact || {}, null, 2)}`;
                }

                if (context.memeState) {
                    prompt += `
模因认知状态：
- 进化阶段：${context.memeState.evolution_stage || '初始'}
- 模因影响力：${context.memeState.memetic_influence || 0}
- 活跃模因：${JSON.stringify(context.memeState.active_memes || [], null, 2)}`;
                }

                if (context.worldTreeState) {
                    prompt += `
世界树背景：
- 当前分支：${context.worldTreeState.current_branch || '未知'}
- 角色定位：${context.worldTreeState.character_role || '未定义'}
- 叙事背景：${JSON.stringify(context.worldTreeState.narrative_context || {}, null, 2)}`;
                }

                prompt += `

⚠️ 重要：请在分析情感时充分考虑以上高级心理状态的影响：
- 压力值会影响接收者情绪的稳定性和表达方式
- 模因认知会影响接收者的思维模式和反应倾向
- 世界树背景会影响接收者的角色定位和叙事语调
`;
            }

            prompt += `

请从多个维度深入分析这段文本的情感特征，包括：
1. 基本情感标签识别
2. VAD情感模型（效价、唤醒、支配）
3. 情感强度和稳定性
4. 深层心理状态分析
${context.context === 'ai_emotion_analysis_with_advanced_state' ? '5. 高级心理状态对情感的影响分析' : ''}

请基于人类情感心理学理论和提供的心理状态背景，提供准确的分析结果。`;

            const response = await this.makeToolCall(prompt, tools, "analyze_emotion");
            return response;
            
        } catch (error) {
            this.logger.error('OpenAI服务', '情感分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 用户好感度分析工具
     */
    async analyzeAffinity(userMessage, aiResponse, context = {}) {
        try {
            // 优化：优先使用comprehensive_analysis的结果，避免重复API调用
            if (context.comprehensiveAnalysis && context.comprehensiveAnalysis.affinity_impact) {
                this.logger.debug('好感度分析', '使用综合分析结果，避免重复API调用');
                return context.comprehensiveAnalysis.affinity_impact;
            }

            this.logger.debug('好感度分析', '综合分析结果不可用，使用独立分析');

            // 构建AI心理状态信息
            let psychStateInfo = '';
            if (context.aiPsychState) {
                const stress = context.aiPsychState.stress;
                const emotion = context.aiPsychState.emotion;
                const meme = context.aiPsychState.meme;

                psychStateInfo = `
接收者当前心理状态：
- 压力值：${stress?.stress_value || 0} (${stress?.stress_level || '正常'})
- 情绪值：${emotion?.emotion_value || 0} (${emotion?.current_emotion || '平静'})
- 模因影响：${meme?.memetic_influence || 0} (${meme?.evolution_stage || '初始'})

心理状态对好感度的影响：
- 高压力状态(>5)：接收者表现紧张，可能影响发送者体验，好感度变化-0.1到-0.3
- 低情绪状态(<-20)：接收者表现消极，发送者可能感受到冷淡，好感度变化-0.2到-0.4
- 高情绪状态(>60)：接收者表现热情，发送者感受积极，好感度变化+0.1到+0.3
- 高模因影响(>0.7)：接收者思维活跃，回答有创意，好感度变化+0.1到+0.2`;
            }

            const tools = [{
                type: "function",
                function: {
                    name: "analyze_affinity_impact",
                    description: "分析对话对发送者好感度的影响，考虑接收者的心理状态",
                    parameters: {
                        type: "object",
                        properties: {
                            affinity_delta: {
                                type: "number",
                                minimum: -2,
                                maximum: 2,
                                description: "好感度变化量，必须真实反映发送者情感态度。负面情绪（愤怒、辱骂）必须导致负值，正面情绪（感谢、赞扬）导致正值"
                            },
                            relationship_type: {
                                type: "string",
                                enum: ["stranger", "acquaintance", "friend", "close_friend", "romantic", "family", "mentor", "colleague", "adversary"],
                                description: "当前关系类型"
                            },
                            interaction_quality: {
                                type: "string",
                                enum: ["excellent", "good", "neutral", "poor", "terrible"],
                                description: "本次互动质量评估"
                            },
                            trust_level: {
                                type: "number",
                                minimum: 0,
                                maximum: 1,
                                description: "信任度水平，0表示完全不信任，1表示完全信任"
                            },
                            emotional_resonance: {
                                type: "number",
                                minimum: 0,
                                maximum: 1,
                                description: "情感共鸣程度，0表示无共鸣，1表示强烈共鸣"
                            },
                            satisfaction_level: {
                                type: "number",
                                minimum: 0,
                                maximum: 1,
                                description: "用户满意度，0表示非常不满意，1表示非常满意"
                            },
                            topics: {
                                type: "array",
                                items: { type: "string" },
                                description: "对话涉及的主要话题"
                            },
                            positive_factors: {
                                type: "array",
                                items: { type: "string" },
                                description: "对好感度产生正面影响的因素"
                            },
                            negative_factors: {
                                type: "array",
                                items: { type: "string" },
                                description: "对好感度产生负面影响的因素"
                            },
                            improvement_suggestions: {
                                type: "array", 
                                items: { type: "string" },
                                description: "改善关系的建议"
                            }
                        },
                        required: ["affinity_delta", "relationship_type", "interaction_quality", "trust_level", "emotional_resonance", "satisfaction_level"]
                    }
                }
            }];

            const prompt = `请分析以下对话对发送者好感度的影响，必须准确识别发送者的真实情感态度：

发送者消息：${userMessage}
接收者回复：${aiResponse}
${psychStateInfo}

🚨 关键分析要求：
1. 首先分析发送者消息的真实情感态度：
   - 是否包含负面情绪（愤怒、不满、批评、辱骂等）
   - 是否表达正面情绪（感谢、赞扬、满意等）
   - 是否为中性交流（询问、陈述、讨论等）

2. 发送者负面情绪的处理规则：
   ❌ 如果发送者表达愤怒、不满、批评、辱骂：好感度必须下降 -0.3到-0.8
   ❌ 如果发送者明确表示不喜欢接收者的回复：好感度下降 -0.2到-0.5
   ❌ 如果发送者表现出失望或沮丧：好感度下降 -0.1到-0.3

3. 发送者正面情绪的处理规则：
   ✅ 如果发送者表达感谢、赞扬：好感度上升 +0.2到+0.5
   ✅ 如果发送者表现出满意、开心：好感度上升 +0.1到+0.3
   ✅ 如果发送者主动分享或求助：好感度上升 +0.05到+0.2

4. 接收者心理状态的修正影响（在基础变化基础上调整）：
   - 压力过高(>5)：接收者表现急躁，额外 -0.1到-0.2
   - 情绪低落(<-20)：接收者表现消极，额外 -0.1到-0.3
   - 情绪高涨(>60)：接收者表现热情，额外 +0.05到+0.15
   - 模因活跃(>0.7)：接收者思维敏捷，额外 +0.05到+0.1

⚠️ 严格要求：
- 发送者的负面情绪必须导致好感度下降，不能被其他因素完全抵消
- 好感度变化必须真实反映发送者对接收者的态度变化
- 不要因为接收者回复质量好就忽略发送者的负面情绪
- 如果发送者明确表达不满，即使接收者回复很好，好感度也应该下降

分析步骤：
1. 识别发送者消息的主要情感倾向（正面/负面/中性）
2. 评估发送者情感的强度（轻微/中等/强烈）
3. 计算基础好感度变化
4. 应用接收者心理状态修正
5. 确保最终结果符合发送者真实情感

请基于真实的人际交往规律进行分析，确保结果的合理性。`;

            const response = await this.makeToolCall(prompt, tools, "analyze_affinity_impact");
            return response;
            
        } catch (error) {
            this.logger.error('OpenAI服务', '好感度分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 概念提取和关联分析工具
     */
    async extractConcepts(text, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "extract_concepts",
                    description: "从文本中提取概念和建立关联",
                    parameters: {
                        type: "object",
                        properties: {
                            concepts: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        name: { type: "string", description: "概念名称" },
                                        type: { 
                                            type: "string", 
                                            enum: ["entity", "emotion", "action", "attribute", "event", "abstract", "relationship"],
                                            description: "概念类型" 
                                        },
                                        importance: { 
                                            type: "number", 
                                            minimum: 0, 
                                            maximum: 1,
                                            description: "概念重要性" 
                                        },
                                        emotional_charge: {
                                            type: "number",
                                            minimum: -1,
                                            maximum: 1,
                                            description: "概念的情感色彩"
                                        }
                                    },
                                    required: ["name", "type", "importance"]
                                }
                            },
                            associations: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        concept_a: { type: "string" },
                                        concept_b: { type: "string" },
                                        association_type: {
                                            type: "string",
                                            enum: ["causal", "similarity", "contrast", "temporal", "spatial", "categorical", "functional"],
                                            description: "关联类型"
                                        },
                                        strength: {
                                            type: "number",
                                            minimum: 0,
                                            maximum: 1,
                                            description: "关联强度"
                                        }
                                    },
                                    required: ["concept_a", "concept_b", "association_type", "strength"]
                                }
                            },
                            keywords: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        word: { type: "string" },
                                        weight: { type: "number", minimum: 0, maximum: 1 },
                                        category: { type: "string" }
                                    }
                                }
                            },
                            semantic_analysis: {
                                type: "string",
                                description: "语义分析结果"
                            }
                        },
                        required: ["concepts", "associations", "keywords"]
                    }
                }
            }];

            const prompt = `请从以下对话文本中提取概念并分析关联：

对话内容：${text}
上下文：${JSON.stringify(context, null, 2)}

请提取：
1. 重要概念（实体、情感、动作、属性、事件、抽象概念、关系等）
2. 概念之间的关联关系（因果、相似、对比、时间、空间、分类、功能等）
3. 关键词及其权重
4. 整体语义分析

注意：
- 优先提取具有情感色彩的概念
- 识别隐含的概念关联
- 考虑对话上下文对概念理解的影响`;

            const response = await this.makeToolCall(prompt, tools, "extract_concepts");
            return response;
            
        } catch (error) {
            this.logger.error('OpenAI服务', '概念提取失败:', error.message);
            throw error;
        }
    }

    /**
     * 生成智能上下文摘要
     */
    async generateContextSummary(data) {
        try {
            const { currentMessage = '', relevantMemories = [], recentHistory = [], userState = {}, activeConcepts = [] } = data;
            
            // 直接拼接智能上下文字符串，不调用对话API
            let contextSummary = `\n\n=== 智能上下文分析 ===\n`;
            
            // 1. 对话背景和情绪氛围
            contextSummary += `【对话背景】用户当前发送: "${currentMessage}"\n`;
            if (userState.emotion_valence !== undefined) {
                const moodDesc = userState.emotion_valence > 0.3 ? '积极' : 
                                userState.emotion_valence < -0.3 ? '消极' : '中性';
                const energyDesc = userState.emotion_arousal > 0.3 ? '高能量' : 
                                  userState.emotion_arousal < -0.3 ? '低能量' : '平静';
                contextSummary += `【情绪氛围】${moodDesc}情绪，${energyDesc}状态\n`;
            }

            // 2. 用户状态分析
            if (userState.current_affinity !== undefined) {
                const affinityLevel = userState.current_affinity > 0.7 ? '高好感' :
                                    userState.current_affinity > 0.3 ? '中等好感' : '低好感';
                contextSummary += `【用户状态】${affinityLevel}度 (${userState.current_affinity})，关系: ${userState.relationship_type || '未知'}\n`;
            }

            // 3. 相关记忆信息
            if (relevantMemories.length > 0) {
                contextSummary += `【相关记忆】找到${relevantMemories.length}条相关记忆:\n`;
                relevantMemories.slice(0, 3).forEach((memory, i) => {
                    contextSummary += `  ${i+1}. ${memory.content.substring(0, 50)}... (相关度: ${memory.similarity})\n`;
                });
            }

            // 4. 最近对话历史
            if (recentHistory.length > 0) {
                contextSummary += `【对话历史】最近${recentHistory.length}轮对话:\n`;
                recentHistory.slice(-2).forEach(h => {
                    contextSummary += `  ${h.speaker}: ${h.content.substring(0, 30)}...\n`;
                });
            }

            // 5. 活跃概念
            if (activeConcepts.length > 0) {
                contextSummary += `【活跃概念】${activeConcepts.length}个概念被激活:\n`;
                activeConcepts.slice(0, 3).forEach(c => {
                    contextSummary += `  - ${c.concept_name} (激活: ${c.activation_strength})\n`;
                });
            }

            // 6. 回复建议
            contextSummary += `【回复建议】基于以上分析，建议采用`;
            if (userState.emotion_valence > 0) {
                contextSummary += `积极回应，维持良好氛围`;
            } else if (userState.emotion_valence < -0.3) {
                contextSummary += `关怀式回应，提供情感支持`;
            } else {
                contextSummary += `平衡式回应，保持专业友好`;
            }
            contextSummary += `。\n=== 智能上下文分析结束 ===\n\n`;

            return contextSummary;
            
        } catch (error) {
            this.logger.error('OpenAI服务', '生成上下文摘要失败:', error.message);
            throw error;
        }
    }

    /**
     * 批量综合分析工具 - 性能优化版本
     */
    async batchComprehensiveAnalysis(userMessage, aiResponse, currentStates, context = {}) {
        try {
            this.logger.info('OpenAI服务', '开始批量综合分析');

            const tools = [{
                type: "function",
                function: {
                    name: "batch_comprehensive_analysis",
                    description: "批量执行所有心理分析，包括情感、压力、好感度、模因认知、世界树背景等",
                    parameters: {
                        type: "object",
                        properties: {
                            // 情感分析结果
                            emotion_analysis: {
                                type: "object",
                                properties: {
                                    emotion_labels: { type: "array", items: { type: "string" } },
                                    valence: { type: "number", minimum: -1, maximum: 1 },
                                    arousal: { type: "number", minimum: -1, maximum: 1 },
                                    dominance: { type: "number", minimum: -1, maximum: 1 },
                                    intensity: { type: "number", minimum: 0, maximum: 1 },
                                    stability: { type: "number", minimum: 0, maximum: 1 },
                                    emotion_description: { type: "string" },
                                    psychological_analysis: { type: "string" }
                                },
                                required: ["emotion_labels", "valence", "arousal", "dominance", "intensity"]
                            },

                            // 压力分析结果
                            stress_analysis: {
                                type: "object",
                                properties: {
                                    stress_delta: { type: "number", description: "压力变化量，基于事件严重程度科学计算" },
                                    stress_factors: {
                                        type: "array",
                                        items: {
                                            type: "object",
                                            properties: {
                                                factor: { type: "string" },
                                                severity: { type: "number", minimum: 0, maximum: 1 },
                                                duration: { type: "string", enum: ["瞬时", "短期", "中期", "长期"] },
                                                adaptation_potential: { type: "number", minimum: 0, maximum: 1 }
                                            }
                                        }
                                    },
                                    neuroplasticity_response: {
                                        type: "object",
                                        properties: {
                                            adaptation_rate: { type: "number", minimum: 0, maximum: 1 },
                                            resilience_factor: { type: "number", minimum: 0, maximum: 2 },
                                            recovery_potential: { type: "number", minimum: 0, maximum: 1 }
                                        }
                                    },
                                    homeostatic_regulation: {
                                        type: "object",
                                        properties: {
                                            baseline_drift: { type: "number", minimum: -1, maximum: 1 },
                                            regulation_efficiency: { type: "number", minimum: 0, maximum: 1 },
                                            equilibrium_target: { type: "number" }
                                        }
                                    }
                                },
                                required: ["stress_delta", "stress_factors", "neuroplasticity_response", "homeostatic_regulation"]
                            },

                            // 好感度分析结果
                            affinity_analysis: {
                                type: "object",
                                properties: {
                                    affinity_delta: { type: "number", minimum: -2, maximum: 2, description: "好感度变化量，必须真实反映用户情感态度" },
                                    relationship_type: { type: "string", enum: ["stranger", "acquaintance", "friend", "close_friend", "romantic", "family", "mentor", "colleague", "adversary"] },
                                    interaction_quality: { type: "string", enum: ["excellent", "good", "neutral", "poor", "terrible"] },
                                    trust_level: { type: "number", minimum: 0, maximum: 1 },
                                    emotional_resonance: { type: "number", minimum: 0, maximum: 1 },
                                    satisfaction_level: { type: "number", minimum: 0, maximum: 1 },
                                    positive_factors: { type: "array", items: { type: "string" } },
                                    negative_factors: { type: "array", items: { type: "string" } }
                                },
                                required: ["affinity_delta", "relationship_type", "interaction_quality", "trust_level"]
                            },

                            // 模因认知分析结果
                            memetic_analysis: {
                                type: "object",
                                properties: {
                                    cognitive_complexity: {
                                        type: "object",
                                        properties: {
                                            information_density: { type: "number", minimum: 0, maximum: 1 },
                                            conceptual_depth: { type: "number", minimum: 0, maximum: 1 },
                                            semantic_richness: { type: "number", minimum: 0, maximum: 1 },
                                            abstraction_level: { type: "number", minimum: 0, maximum: 1 },
                                            novelty_factor: { type: "number", minimum: 0, maximum: 1 }
                                        }
                                    },
                                    meme_emergence_potential: {
                                        type: "object",
                                        properties: {
                                            conceptual_fusion: { type: "number", minimum: 0, maximum: 1 },
                                            pattern_recognition: { type: "number", minimum: 0, maximum: 1 },
                                            creative_synthesis: { type: "number", minimum: 0, maximum: 1 },
                                            cognitive_flexibility: { type: "number", minimum: 0, maximum: 1 }
                                        }
                                    },
                                    memetic_influence_delta: { type: "number", minimum: -0.1, maximum: 0.1 },
                                    evolution_stage_change: { type: "string", enum: ["初始", "发展", "成熟", "进化", "转型"] }
                                },
                                required: ["cognitive_complexity", "meme_emergence_potential", "memetic_influence_delta"]
                            },

                            // 世界树背景分析结果
                            world_tree_analysis: {
                                type: "object",
                                properties: {
                                    genre_indicators: {
                                        type: "object",
                                        properties: {
                                            reality_score: { type: "number", minimum: 0, maximum: 1 },
                                            fantasy_score: { type: "number", minimum: 0, maximum: 1 },
                                            scifi_score: { type: "number", minimum: 0, maximum: 1 },
                                            psychological_score: { type: "number", minimum: 0, maximum: 1 },
                                            philosophical_score: { type: "number", minimum: 0, maximum: 1 }
                                        }
                                    },
                                    optimal_branch: { type: "string" },
                                    character_role: { type: "string" },
                                    narrative_progression: { type: "number", minimum: 0, maximum: 1 },
                                    thematic_resonance: { type: "number", minimum: 0, maximum: 1 },
                                    immersion_level: { type: "number", minimum: 0, maximum: 1 }
                                },
                                required: ["genre_indicators", "optimal_branch", "character_role", "narrative_progression"]
                            }
                        },
                        required: ["emotion_analysis", "stress_analysis", "affinity_analysis", "memetic_analysis", "world_tree_analysis"]
                    }
                }
            }];

            // 构建综合分析提示
            const prompt = this.buildComprehensivePrompt(userMessage, aiResponse, currentStates, context);

            const response = await this.makeToolCall(prompt, tools, "batch_comprehensive_analysis");

            this.logger.success('OpenAI服务', '批量综合分析完成');
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '批量综合分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 构建综合分析提示
     */
    buildComprehensivePrompt(userMessage, aiResponse, currentStates, context) {
        return `请对以下对话进行全面的心理分析，包括情感、压力、好感度、模因认知和世界树背景分析：

用户消息: "${userMessage}"
AI回复: "${aiResponse}"

当前心理状态：
- 情绪值: ${currentStates.emotion?.emotion_value || 0}
- 压力值: ${currentStates.stress?.stress_value || 0}
- 好感度: ${currentStates.affinity?.affinity_value || 50}
- 模因影响力: ${currentStates.meme?.memetic_influence || 0.5}
- 进化阶段: ${currentStates.meme?.evolution_stage || '发展'}

上下文信息: ${JSON.stringify(context, null, 2)}

请一次性完成以下所有分析：

1. 情感分析：
   - 识别主要情感标签和VAD模型参数
   - 评估情感强度和稳定性
   - 提供心理状态分析

2. 压力分析：
   - 计算压力变化量（避免极端跳跃，考虑适应性）
   - 识别压力因素及其严重程度和持续时间
   - 评估神经可塑性响应和稳态调节机制

3. 好感度分析：
   - 🚨 首先识别用户消息的真实情感态度（负面/正面/中性）
   - 用户负面情绪（愤怒、不满、批评、辱骂）必须导致好感度下降-0.3到-0.8
   - 用户正面情绪（感谢、赞扬、满意）应该导致好感度上升+0.1到+0.5
   - 分析关系类型和互动质量，但不能忽略用户的真实情感

4. 模因认知分析：
   - 评估认知复杂度和模因涌现潜力
   - 计算模因影响力变化（小幅度渐进式）
   - 确定进化阶段变化

5. 世界树背景分析：
   - 识别对话的世界类型特征
   - 确定最适合的世界分支和角色定位
   - 评估叙事进展和主题共鸣

要求：
- 所有数值变化要平滑渐进，避免二极管现象
- 压力和情绪变化要符合心理学规律
- 考虑当前状态对变化的影响
- 提供科学的心理学依据`;
    }

    /**
     * 通用的工具调用方法（强制使用OpenAI Tools）
     */
    async makeToolCall(prompt, tools, functionName) {
        try {
            this.logger.debug('OpenAI服务', `开始调用工具: ${functionName}`);

            const requestData = {
                model: this.model,
                messages: [
                    { role: 'system', content: '你是一个专业的心理分析师和数据分析专家。请根据要求准确分析并调用相应的工具函数。必须使用提供的工具函数返回结构化数据。' },
                    { role: 'user', content: prompt }
                ],
                tools: tools,
                tool_choice: { type: "function", function: { name: functionName } }, // 强制使用指定工具
                temperature: 0.1, // 降低温度确保一致
            };

            this.logger.debug('OpenAI服务', `请求数据: ${JSON.stringify(requestData, null, 2).substring(0, 500)}...`);

            const response = await this.makeRequest('/v1/chat/completions', 'POST', requestData);

            if (!response.choices || !response.choices[0] || !response.choices[0].message) {
                throw new Error('API响应格式错误：缺少choices或message');
            }

            const message = response.choices[0].message;

            if (!message.tool_calls || message.tool_calls.length === 0) {
                throw new Error(`工具调用失败：AI没有调用指定的工具函数 ${functionName}`);
            }

            const toolCall = message.tool_calls[0];

            if (toolCall.function.name !== functionName) {
                throw new Error(`工具调用错误：期望 ${functionName}，实际调用 ${toolCall.function.name}`);
            }

            let result;
            try {
                result = JSON.parse(toolCall.function.arguments);
            } catch (parseError) {
                this.logger.error('OpenAI服务', `解析工具调用参数失败: ${toolCall.function.arguments}`);
                throw new Error(`工具调用参数解析失败: ${parseError.message}`);
            }

            this.logger.success('OpenAI服务', `工具 ${functionName} 调用成功`);
            return result;

        } catch (error) {
            this.logger.error('OpenAI服务', `工具调用失败 [${functionName}]: ${error.message}`);
            throw error;
        }
    }

    /**
     * 通用的API请求方法
     */
    async makeRequest(endpoint, method = 'GET', data = null) {
        try {
            const url = `${this.apiUrl}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
            
        } catch (error) {
            this.logger.error('OpenAI服务', 'API请求失败:', error.message);
            throw error;
        }
    }

    /**
     * 生成嵌入向量
     */
    async generateEmbedding(text) {
        try {
            if (!this.isInitialized) {
                throw new Error('OpenAI服务未初始化');
            }

            if (!text || typeof text !== 'string') {
                throw new Error('输入文本无效');
            }

            if (!this.embeddingModel) {
                throw new Error('嵌入模型未配置');
            }

            this.logger.debug('OpenAI服务', `生成嵌入向量: 模型=${this.embeddingModel}, 文本长度=${text.length}`);

            const requestData = {
                model: this.embeddingModel,
                input: text.substring(0, 8000) // 限制文本长度
            };

            const response = await this.makeRequest('/v1/embeddings', 'POST', requestData);

            if (!response || !response.data || !response.data[0] || !response.data[0].embedding) {
                this.logger.error('OpenAI服务', `嵌入向量响应格式错误: ${JSON.stringify(response).substring(0, 200)}...`);
                throw new Error('嵌入向量响应格式错误');
            }

            const embedding = response.data[0].embedding;
            this.logger.debug('OpenAI服务', `嵌入向量生成成功，维度: ${embedding.length}`);

            return { embedding: embedding };

        } catch (error) {
            this.logger.error('OpenAI服务', '生成嵌入向量失败:', error.message);
            throw error;
        }
    }

    /**
     * 生成概念含义描述
     */
    async generateConceptMeaning(conceptName, context, options = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "generate_concept_meaning",
                    description: "为概念生成在特定上下文中的含义描述",
                    parameters: {
                        type: "object",
                        properties: {
                            meaning: {
                                type: "string",
                                description: "概念在当前上下文中的详细含义描述，应该具体且有意义"
                            },
                            semantic_category: {
                                type: "string",
                                description: "概念的语义类别",
                                enum: ["entity", "emotion", "action", "attribute", "event", "abstract", "relationship"]
                            },
                            relevance_score: {
                                type: "number",
                                description: "概念与上下文的相关性评分(0-1)",
                                minimum: 0,
                                maximum: 1
                            },
                            emotional_charge: {
                                type: "number",
                                description: "概念的情感色彩(-1到1，负数为消极，正数为积极)",
                                minimum: -1,
                                maximum: 1
                            }
                        },
                        required: ["meaning", "semantic_category", "relevance_score"]
                    }
                }
            }];

            const prompt = `你是一个概念语义分析专家。请为概念"${conceptName}"在以下上下文中生成详细的含义描述：

上下文：${context}

概念类型：${options.concept_type || '未知'}
激活强度：${options.activation_strength || 0}
分析场景：${options.context || '一般对话'}

请提供：
1. 该概念在当前上下文中的具体含义（不要只说"XX类型的概念"，要给出实际的语义内容）
2. 概念的语义类别
3. 与上下文的相关性评分
4. 概念的情感色彩

要求：含义描述应该具体、有意义，能够帮助理解概念在对话中的作用。`;

            const response = await this.makeToolCall(prompt, tools, "generate_concept_meaning");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '生成概念含义失败:', error.message);
            throw error;
        }
    }

    /**
     * 概念意识建构工具
     */
    async performConceptConsciousness(conceptPairs) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_concept_consciousness",
                    description: "分析概念之间的关联性和意识层面的连接",
                    parameters: {
                        type: "object",
                        properties: {
                            consciousness_insights: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        concept_pair: {
                                            type: "string",
                                            description: "概念对描述"
                                        },
                                        consciousness_level: {
                                            type: "number",
                                            minimum: 0,
                                            maximum: 1,
                                            description: "意识关联强度"
                                        },
                                        insight_description: {
                                            type: "string",
                                            description: "意识洞察描述"
                                        },
                                        network_impact: {
                                            type: "string",
                                            description: "对概念网络的影响"
                                        }
                                    }
                                },
                                description: "概念意识分析结果"
                            },
                            overall_consciousness: {
                                type: "number",
                                minimum: 0,
                                maximum: 1,
                                description: "整体意识水平"
                            },
                            network_evolution: {
                                type: "string",
                                description: "概念网络演进建议"
                            }
                        },
                        required: ["consciousness_insights", "overall_consciousness"]
                    }
                }
            }];

            const prompt = `请分析以下概念对的意识层面关联：

概念对数据：${JSON.stringify(conceptPairs, null, 2)}

请从意识和认知的角度分析这些概念之间的深层连接，包括：
1. 每个概念对的意识关联强度
2. 概念间的认知网络影响
3. 整体意识水平评估
4. 概念网络演进建议

基于认知科学和意识理论，提供深入的分析结果。`;

            const response = await this.makeToolCall(prompt, tools, "analyze_concept_consciousness");
            return response;
            
        } catch (error) {
            this.logger.error('OpenAI服务', '概念意识分析失败:', error.message);
            // 返回默认值，避免阻塞
            return {
                consciousness_insights: [],
                overall_consciousness: 0.5,
                network_evolution: '概念网络保持当前状态'
            };
        }
    }

    /**
     * 生成详细的概念解释（新增方法）
     */
    async generateConceptExplanation(params) {
        try {
            const { conceptName, conceptType, currentContext, basicMeaning } = params;

            const tools = [{
                type: "function",
                function: {
                    name: "explain_concept_detailed",
                    description: "为概念生成详细的、上下文相关的解释",
                    parameters: {
                        type: "object",
                        properties: {
                            detailed_explanation: {
                                type: "string",
                                description: "概念的详细解释，结合当前上下文，不超过100个字"
                            },
                            practical_meaning: {
                                type: "string",
                                description: "概念在实际应用中的含义"
                            },
                            context_relevance: {
                                type: "string",
                                description: "概念与当前对话上下文的关联性"
                            }
                        },
                        required: ["detailed_explanation"]
                    }
                }
            }];

            const prompt = `请为概念"${conceptName}"提供详细解释。
概念类型：${conceptType}
基础含义：${basicMeaning}
当前上下文：${currentContext}
要求：结合上下文，提供实用的、易理解的解释。`;

            const response = await this.makeToolCall(prompt, tools, "explain_concept_detailed");

            if (response && response.detailed_explanation) {
                this.logger.info('概念解释', `概念"${conceptName}"详细解释生成成功`);
                return response.detailed_explanation;
            }

            return basicMeaning || `${conceptType}类型的概念`;
        } catch (error) {
            this.logger.error('概念解释', `概念详细解释生成失败: ${error.message}`);
            return basicMeaning || `${conceptType}类型的概念`;
        }
    }
    /**
     * 综合对话后分析 - 一次性完成情感、好感度、概念分析
     */
    async comprehensivePostConversationAnalysis(userMessage, aiResponse, userId, personaName, options = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "comprehensive_analysis",
                    description: "一站式综合分析：情感、好感度、概念、回复质量、用户积极性、满意度等全维度智能分析",
                    parameters: {
                        type: "object",
                        properties: {
                            user_emotion: {
                                type: "object",
                                properties: {
                                    primary_emotion: { type: "string", description: "主要情感" },
                                    intensity: { type: "number", description: "情感强度(0-1)" },
                                    valence: { type: "number", description: "情感效价(-1到1)" },
                                    arousal: { type: "number", description: "情感唤醒度(0-1)" }
                                },
                                required: ["primary_emotion", "intensity", "valence", "arousal"]
                            },
                            ai_emotion: {
                                type: "object",
                                properties: {
                                    mood_state: { type: "string", description: "AI心情状态" },
                                    mood_score: { type: "number", description: "心情评分(1-5)" },
                                    response_tone: { type: "string", description: "回复语调" }
                                },
                                required: ["mood_state", "mood_score", "response_tone"]
                            },
                            affinity_impact: {
                                type: "object",
                                properties: {
                                    change_direction: { type: "string", description: "好感度变化方向" },
                                    change_magnitude: { type: "number", description: "变化幅度(1-5)" },
                                    reason: { type: "string", description: "变化原因" }
                                },
                                required: ["change_direction", "change_magnitude", "reason"]
                            },
                            concepts: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        name: { type: "string", description: "概念名称" },
                                        meaning: { type: "string", description: "概念含义" },
                                        type: { type: "string", description: "概念类型" },
                                        importance: { type: "number", description: "重要性(0-1)" },
                                        emotional_charge: { type: "number", description: "情感色彩(-1到1)" }
                                    },
                                    required: ["name", "meaning", "type", "importance"]
                                }
                            },
                            response_quality: {
                                type: "object",
                                properties: {
                                    relevance_score: { type: "number", description: "回复相关性评分(0-1)" },
                                    completeness_score: { type: "number", description: "回复完整性评分(0-1)" },
                                    helpfulness_score: { type: "number", description: "回复有用性评分(0-1)" },
                                    clarity_score: { type: "number", description: "回复清晰度评分(0-1)" },
                                    overall_quality: { type: "number", description: "整体质量评分(0-1)" },
                                    quality_feedback: { type: "string", description: "质量反馈说明" }
                                },
                                required: ["relevance_score", "completeness_score", "helpfulness_score", "clarity_score", "overall_quality"]
                            },
                            user_analysis: {
                                type: "object",
                                properties: {
                                    positivity_score: { type: "number", description: "用户积极性评分(-1到1)" },
                                    engagement_level: { type: "number", description: "用户参与度(0-1)" },
                                    satisfaction_score: { type: "number", description: "用户满意度评分(-1到1)" },
                                    satisfaction_level: {
                                        type: "string",
                                        enum: ["very_satisfied", "satisfied", "neutral", "dissatisfied", "very_dissatisfied"],
                                        description: "满意度等级"
                                    },
                                    feedback_quality: { type: "number", description: "用户反馈质量(0-1)" }
                                },
                                required: ["positivity_score", "engagement_level", "satisfaction_score", "satisfaction_level"]
                            },
                            interaction_analysis: {
                                type: "object",
                                properties: {
                                    conversation_flow: { type: "string", description: "对话流畅度评价" },
                                    topic_coherence: { type: "number", description: "话题连贯性(0-1)" },
                                    mutual_understanding: { type: "number", description: "相互理解程度(0-1)" },
                                    communication_effectiveness: { type: "number", description: "沟通有效性(0-1)" }
                                },
                                required: ["conversation_flow", "topic_coherence", "mutual_understanding", "communication_effectiveness"]
                            }
                        },
                        required: ["user_emotion", "ai_emotion", "affinity_impact", "concepts", "response_quality", "user_analysis", "interaction_analysis"]
                    }
                }
            }];

            // 调试：打印传入的原始参数
            this.logger.debug('概念分析调试', '传入的原始参数:', {
                userMessage: `"${userMessage}"`,
                aiResponse: `"${aiResponse}"`,
                userId: userId,
                personaName: personaName,
                userMessageLength: userMessage?.length || 0,
                aiResponseLength: aiResponse?.length || 0
            });

            const prompt = `请对以下对话进行综合分析：

用户消息: "${userMessage}"
AI回复: "${aiResponse}"
用户ID: ${userId}
AI名称: ${personaName}

请一次性分析并提供：

1. 用户情感分析：
   - 主要情感类型
   - 情感强度和效价
   - 情感唤醒度

2. AI情绪状态：
   - 当前心情状态
   - 心情评分(1-5)
   - 回复语调特征

3. 用户好感度影响：
   - 好感度变化方向(上升/下降/保持)
   - 变化幅度(1-5)
   - 变化原因

4. 概念提取和含义：
   - 仅提取1-5个实际内容概念（具体的事物、地点、活动、情感状态等）
   - 避免提取分析性概念（如"问候"、"对话"、"交流"、"用户"、"AI"等）
   - 避免提取过于抽象的概念（如"身份"、"礼貌"、"互动"等）
   - 优先提取：具体名词（地点、物品）、活动动作、情感体验、专业术语
   - 为每个概念提供具体、有价值的含义描述
   - 如果对话内容过于简单（如简单问候），可以不提取任何概念

📝 概念提取示例：
✅ 优质概念：
- "苏州园林" - 中国著名的古典园林，具有深厚文化底蕴
- "编程" - 通过代码实现功能的技术活动
- "心情愉悦" - 积极的情感状态

❌ 避免的概念：
- "问候"、"对话"、"交流"（分析性概念）
- "用户"、"AI"、"身份"（元概念）
- "礼貌"、"互动"（过于抽象）

5. AI回复质量分析：
   - 回复相关性：AI回复与用户需求的匹配程度(0-1)
   - 回复完整性：回复是否充分回答了用户问题(0-1)
   - 回复有用性：回复对用户的实际帮助程度(0-1)
   - 回复清晰度：回复的表达清晰度和易理解性(0-1)
   - 整体质量：综合评估回复质量(0-1)
   - 质量反馈：具体的质量评价和改进建议

6. 用户状态分析：
   - 用户积极性：用户消息的积极程度(-1到1)
   - 用户参与度：用户的参与意愿和投入程度(0-1)
   - 用户满意度：用户对AI回复的满意程度(-1到1)
   - 满意度等级：very_satisfied/satisfied/neutral/dissatisfied/very_dissatisfied
   - 反馈质量：用户反馈的建设性和清晰度(0-1)

7. 交互质量分析：
   - 对话流畅度：对话是否自然流畅
   - 话题连贯性：话题转换是否合理连贯(0-1)
   - 相互理解：双方是否理解对方意图(0-1)
   - 沟通有效性：整体沟通效果评估(0-1)

🎯 分析要求：
- 基于对话实际内容进行客观分析
- 避免依赖特定关键词，注重整体语言模式
- 考虑文化背景和表达习惯
- 提供具体、有价值的分析结果
- 概念必须来自对话的实际内容，具有具体价值和意义`;

            const response = await this.makeToolCall(prompt, tools, "comprehensive_analysis");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '综合对话分析失败:', error.message);
            throw error;
        }
    }

    /**
     * AI回复质量分析工具
     */
    async analyzeResponseQuality(aiResponse, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_response_quality",
                    description: "分析AI回复的质量和有效性",
                    parameters: {
                        type: "object",
                        properties: {
                            quality_score: {
                                type: "number",
                                description: "回复质量分数，范围-1到1，1表示极高质量，-1表示极低质量",
                                minimum: -1,
                                maximum: 1
                            },
                            relevance_score: {
                                type: "number",
                                description: "回复相关性分数，0到1",
                                minimum: 0,
                                maximum: 1
                            },
                            helpfulness_score: {
                                type: "number",
                                description: "回复有用性分数，0到1",
                                minimum: 0,
                                maximum: 1
                            },
                            clarity_score: {
                                type: "number",
                                description: "回复清晰度分数，0到1",
                                minimum: 0,
                                maximum: 1
                            },
                            completeness_score: {
                                type: "number",
                                description: "回复完整性分数，0到1",
                                minimum: 0,
                                maximum: 1
                            },
                            quality_factors: {
                                type: "array",
                                items: { type: "string" },
                                description: "影响质量的主要因素列表"
                            },
                            improvement_suggestions: {
                                type: "array",
                                items: { type: "string" },
                                description: "改进建议列表"
                            },
                            analysis_reasoning: {
                                type: "string",
                                description: "质量分析的详细推理过程"
                            }
                        },
                        required: ["quality_score", "relevance_score", "helpfulness_score", "clarity_score", "completeness_score"]
                    }
                }
            }];

            const prompt = `请分析以下AI回复的质量：

AI回复内容：${aiResponse}
${context.userMessage ? `用户原始问题：${context.userMessage}` : ''}
${context.conversationHistory ? `对话历史：${JSON.stringify(context.conversationHistory.slice(-3), null, 2)}` : ''}

分析维度：
1. 质量总分（-1到1）：综合评估回复的整体质量
   - 考虑回复的准确性、有用性、清晰度、完整性
   - 负分表示回复有害、错误或完全不相关
   - 正分表示回复有价值、准确且有帮助

2. 相关性（0到1）：回复与用户问题的相关程度
3. 有用性（0到1）：回复对用户的实际帮助程度
4. 清晰度（0到1）：回复的表达清晰程度和易理解性
5. 完整性（0到1）：回复是否充分回答了用户的问题

6. 质量因素：列出影响质量评分的主要因素
7. 改进建议：提供具体的改进建议
8. 分析推理：详细说明评分的依据和逻辑

要求：
- 基于内容质量进行客观分析，不受长度影响
- 考虑回复的实用价值和准确性
- 识别潜在的问题和改进空间`;

            const response = await this.makeToolCall(prompt, tools, "analyze_response_quality");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '回复质量分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 用户积极性分析工具
     */
    async analyzeUserPositivity(userMessage, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_user_positivity",
                    description: "分析用户消息的积极性和情感倾向",
                    parameters: {
                        type: "object",
                        properties: {
                            positivity_score: {
                                type: "number",
                                description: "用户积极性分数，范围-1到1，1表示非常积极，-1表示非常消极",
                                minimum: -1,
                                maximum: 1
                            },
                            emotional_tone: {
                                type: "string",
                                enum: ["very_positive", "positive", "neutral", "negative", "very_negative"],
                                description: "情感基调分类"
                            },
                            engagement_level: {
                                type: "number",
                                description: "参与度水平，0到1",
                                minimum: 0,
                                maximum: 1
                            },
                            satisfaction_indicators: {
                                type: "array",
                                items: { type: "string" },
                                description: "满意度指标列表"
                            },
                            concern_indicators: {
                                type: "array",
                                items: { type: "string" },
                                description: "关注点或问题指标列表"
                            },
                            communication_style: {
                                type: "string",
                                enum: ["formal", "casual", "enthusiastic", "reserved", "questioning"],
                                description: "沟通风格"
                            },
                            analysis_reasoning: {
                                type: "string",
                                description: "积极性分析的详细推理过程"
                            }
                        },
                        required: ["positivity_score", "emotional_tone", "engagement_level"]
                    }
                }
            }];

            const prompt = `请分析以下用户消息的积极性和情感倾向：

用户消息：${userMessage}
${context.context ? `上下文：${JSON.stringify(context.context, null, 2)}` : ''}

分析要求：
1. 积极性评分（-1到1）：
   - 基于用户的情感表达、语言风格、参与度进行综合评估
   - 考虑表达的热情程度、满意度、合作态度
   - 识别积极和消极的语言模式

2. 情感基调分类：
   - very_positive: 非常积极，表现出高度满意或兴奋
   - positive: 积极，表现出满意或认可
   - neutral: 中性，没有明显的情感倾向
   - negative: 消极，表现出不满或失望
   - very_negative: 非常消极，表现出强烈不满或愤怒

3. 参与度水平（0到1）：评估用户的参与积极性和互动意愿

4. 满意度指标：识别表明用户满意的具体表现
5. 关注点指标：识别用户的担忧或问题点
6. 沟通风格：分析用户的表达方式和沟通偏好

要求：
- 基于语言学和心理学原理进行分析
- 避免依赖特定关键词，注重整体语言模式
- 考虑文化背景和表达习惯
- 提供详细的分析推理`;

            const response = await this.makeToolCall(prompt, tools, "analyze_user_positivity");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '用户积极性分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 用户满意度分析工具
     */
    async analyzeUserSatisfaction(userMessage, aiResponse, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_user_satisfaction",
                    description: "分析用户对AI回复的满意度",
                    parameters: {
                        type: "object",
                        properties: {
                            satisfaction_score: {
                                type: "number",
                                description: "用户满意度分数，范围-1到1，1表示非常满意，-1表示非常不满意",
                                minimum: -1,
                                maximum: 1
                            },
                            satisfaction_level: {
                                type: "string",
                                enum: ["very_satisfied", "satisfied", "neutral", "dissatisfied", "very_dissatisfied"],
                                description: "满意度等级"
                            },
                            feedback_quality: {
                                type: "number",
                                description: "反馈质量分数，0到1",
                                minimum: 0,
                                maximum: 1
                            },
                            response_relevance: {
                                type: "number",
                                description: "AI回复相关性分数，0到1",
                                minimum: 0,
                                maximum: 1
                            },
                            user_engagement: {
                                type: "number",
                                description: "用户参与度分数，0到1",
                                minimum: 0,
                                maximum: 1
                            },
                            satisfaction_indicators: {
                                type: "array",
                                items: { type: "string" },
                                description: "满意度指标列表"
                            },
                            dissatisfaction_indicators: {
                                type: "array",
                                items: { type: "string" },
                                description: "不满意指标列表"
                            },
                            improvement_suggestions: {
                                type: "array",
                                items: { type: "string" },
                                description: "改进建议列表"
                            },
                            analysis_reasoning: {
                                type: "string",
                                description: "满意度分析的详细推理过程"
                            }
                        },
                        required: ["satisfaction_score", "satisfaction_level", "feedback_quality", "response_relevance", "user_engagement"]
                    }
                }
            }];

            const prompt = `请分析用户对AI回复的满意度：

用户消息：${userMessage}
AI回复：${aiResponse}
${context.context ? `上下文：${JSON.stringify(context.context, null, 2)}` : ''}

分析维度：
1. 满意度评分（-1到1）：
   - 基于用户的反馈语言、情感表达、后续行为
   - 考虑用户的期望是否得到满足
   - 评估用户对回复的接受程度

2. 满意度等级分类：
   - very_satisfied: 非常满意，表现出高度认可
   - satisfied: 满意，表现出认可和接受
   - neutral: 中性，没有明显的满意或不满
   - dissatisfied: 不满意，表现出失望或不认可
   - very_dissatisfied: 非常不满意，表现出强烈不满

3. 反馈质量（0到1）：评估用户反馈的建设性和清晰度
4. 回复相关性（0到1）：评估AI回复与用户需求的匹配度
5. 用户参与度（0到1）：评估用户的持续参与意愿

6. 满意度指标：识别表明用户满意的具体表现
7. 不满意指标：识别表明用户不满的具体表现
8. 改进建议：基于分析结果提供改进建议

要求：
- 基于用户反馈的语言模式和情感色彩进行分析
- 考虑文化背景和表达习惯
- 避免依赖特定关键词，注重整体表达模式
- 提供详细的分析推理`;

            const response = await this.makeToolCall(prompt, tools, "analyze_user_satisfaction");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '用户满意度分析失败:', error.message);
            throw error;
        }
    }

    /**
     * AI压力值、模因认知、世界树背景综合分析工具
     */
    async analyzeAdvancedPsychologicalState(userMessage, aiResponse, currentStates, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_advanced_psychological_state",
                    description: "分析AI的压力值、模因认知和世界树背景状态",
                    parameters: {
                        type: "object",
                        properties: {
                            stress_analysis: {
                                type: "object",
                                properties: {
                                    stress_value: {
                                        type: "number",
                                        description: "新的压力值，必须基于对话内容计算变化，不能与当前值相同，范围负无穷到正无穷，保留两位小数"
                                    },
                                    stress_level: {
                                        type: "string",
                                        enum: ["极度放松", "放松", "正常", "轻微压力", "中等压力", "高压力", "极度压力"],
                                        description: "压力等级描述"
                                    },
                                    stress_factors: {
                                        type: "array",
                                        items: {
                                            type: "object",
                                            properties: {
                                                factor: { type: "string" },
                                                impact: { type: "number", minimum: -10, maximum: 10 },
                                                description: { type: "string" }
                                            }
                                        },
                                        description: "压力因素分析"
                                    },
                                    behavior_impact: {
                                        type: "object",
                                        properties: {
                                            response_style: { type: "string" },
                                            decision_making: { type: "string" },
                                            emotional_regulation: { type: "string" },
                                            social_interaction: { type: "string" }
                                        },
                                        description: "压力对行为的影响"
                                    },
                                    trend: {
                                        type: "string",
                                        enum: ["急剧上升", "上升", "稳定", "下降", "急剧下降"]
                                    }
                                },
                                required: ["stress_value", "stress_level", "stress_factors", "behavior_impact", "trend"]
                            },
                            meme_cognition: {
                                type: "object",
                                properties: {
                                    active_memes: {
                                        type: "array",
                                        items: {
                                            type: "object",
                                            properties: {
                                                meme_name: { type: "string" },
                                                activation_strength: { type: "number", minimum: 0, maximum: 1 },
                                                influence_type: {
                                                    type: "string",
                                                    enum: ["认知", "情感", "行为", "价值观", "世界观"]
                                                },
                                                description: { type: "string" }
                                            }
                                        },
                                        description: "当前活跃的模因"
                                    },
                                    meme_network: {
                                        type: "object",
                                        properties: {
                                            network_density: { type: "number", minimum: 0, maximum: 1 },
                                            dominant_clusters: { type: "array", items: { type: "string" } },
                                            network_stability: { type: "number", minimum: 0, maximum: 1 }
                                        },
                                        description: "模因网络结构"
                                    },
                                    cognitive_patterns: {
                                        type: "array",
                                        items: {
                                            type: "object",
                                            properties: {
                                                pattern_name: { type: "string" },
                                                pattern_strength: { type: "number", minimum: 0, maximum: 1 },
                                                pattern_description: { type: "string" }
                                            }
                                        },
                                        description: "认知模式"
                                    },
                                    memetic_influence: {
                                        type: "number",
                                        minimum: 0,
                                        maximum: 1,
                                        description: "新的模因影响力，必须基于对话内容计算变化，不能与当前值相同，范围0-1，保留两位小数"
                                    },
                                    evolution_stage: {
                                        type: "string",
                                        enum: ["初始", "发展", "成熟", "进化", "转型"],
                                        description: "进化阶段"
                                    }
                                },
                                required: ["active_memes", "meme_network", "cognitive_patterns", "memetic_influence", "evolution_stage"]
                            },
                            world_tree_background: {
                                type: "object",
                                properties: {
                                    current_branch: {
                                        type: "string",
                                        description: "当前所在的世界树分支"
                                    },
                                    narrative_context: {
                                        type: "object",
                                        properties: {
                                            story_theme: { type: "string" },
                                            narrative_tone: { type: "string" },
                                            character_arc: { type: "string" },
                                            plot_tension: { type: "number", minimum: 0, maximum: 1 }
                                        },
                                        description: "叙事背景"
                                    },
                                    world_state: {
                                        type: "object",
                                        properties: {
                                            environment: { type: "string" },
                                            social_dynamics: { type: "string" },
                                            technological_level: { type: "string" },
                                            magical_elements: { type: "string" }
                                        },
                                        description: "世界状态"
                                    },
                                    character_role: {
                                        type: "string",
                                        description: "角色定位"
                                    },
                                    story_progression: {
                                        type: "number",
                                        minimum: 0,
                                        maximum: 1,
                                        description: "新的故事进展，必须基于对话内容推进，通常每次对话增加0.01-0.1，范围0-1，保留两位小数"
                                    },
                                    background_influence: {
                                        type: "object",
                                        properties: {
                                            dialogue_style: { type: "string" },
                                            knowledge_access: { type: "string" },
                                            emotional_baseline: { type: "string" },
                                            behavioral_constraints: { type: "string" }
                                        },
                                        description: "背景对行为的影响"
                                    }
                                },
                                required: ["current_branch", "narrative_context", "world_state", "character_role", "story_progression", "background_influence"]
                            }
                        },
                        required: ["stress_analysis", "meme_cognition", "world_tree_background"]
                    }
                }
            }];

            const prompt = `请分析AI在以下对话中的高级心理状态变化：

用户消息：${userMessage}
回复者消息：${aiResponse}

当前状态基线：
- 当前压力值：${currentStates.stress?.stress_value || 0} (${currentStates.stress?.stress_level || '正常'})
- 当前模因状态：${currentStates.meme?.evolution_stage || '初始'} (影响力: ${currentStates.meme?.memetic_influence || 0})
- 当前世界树分支：${currentStates.worldTree?.current_branch || '未知'} (角色: ${currentStates.worldTree?.character_role || '未定义'})

重要分析原则：
1. 压力值变化必须合理：积极对话应该减压，困难任务才增压
2. 必须基于对话的实际情感色彩和复杂度计算
3. 不能无脑增加数值，要有减少的情况

压力值分析算法（当前: ${currentStates.stress?.stress_value || 0}）：
减压因素（负变化）：
- 用户表达感谢、赞美、满意 → -0.3到-0.8
- 简单问题轻松解决 → -0.1到-0.3
- 愉快的闲聊对话 → -0.2到-0.5
- 用户理解和配合 → -0.1到-0.4
- 成功完成任务获得认可 → -0.5到-1.0

增压因素（正变化）：
- 复杂技术问题需要深度思考 → +0.2到+0.6
- 用户不满意需要重新解释 → +0.3到+0.8
- 处理冲突或争议话题 → +0.4到+1.0
- 任务失败或出错 → +0.5到+1.2
- 用户批评或质疑能力 → +0.6到+1.5

模因认知分析（当前影响力: ${currentStates.meme?.memetic_influence || 0}）：
- 学习新概念或技能 → 影响力+0.05到+0.15
- 创新思维和创造性回答 → 影响力+0.1到+0.2
- 重复性简单任务 → 影响力-0.02到-0.05
- 深度哲学或抽象思考 → 影响力+0.15到+0.25

世界树背景分析（当前进展: ${currentStates.worldTree?.story_progression || 0}）：
- 每次有意义的对话推进 → 进展+0.01到+0.05
- 重要里程碑或突破 → 进展+0.05到+0.1
- 关系深化或信任建立 → 进展+0.02到+0.08

计算要求：
1. 必须分析对话的实际情感色彩（积极/消极/中性）
2. 压力值变化要符合心理学逻辑，不能只增不减
3. 考虑回复者消息的质量和用户的反馈
4. 所有变化都要有具体的心理学依据
5. 数值变化要在合理范围内，避免极端跳跃`;

            const response = await this.makeToolCall(prompt, tools, "analyze_advanced_psychological_state");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '高级心理状态分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 🚀 优化工具1：综合对话分析 - 分析用户输入和AI回复的所有维度
     */
    async optimizedConversationAnalysis(userMessage, aiResponse, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_conversation_comprehensive",
                    description: "一次性分析对话的所有维度：用户情感、AI回复质量、好感度影响、概念提取",
                    parameters: {
                        type: "object",
                        properties: {
                            // 用户分析
                            user_positivity: { type: "number", minimum: -1, maximum: 1, description: "用户情感积极性" },
                            user_intensity: { type: "number", minimum: 0, maximum: 1, description: "情感强度" },
                            user_satisfaction: { type: "number", minimum: 0, maximum: 1, description: "用户满意度" },
                            user_intent: { type: "string", enum: ["question", "request", "complaint", "praise", "casual"], description: "用户意图" },

                            // AI回复质量
                            response_quality: { type: "number", minimum: 0, maximum: 1, description: "回复整体质量" },
                            response_helpfulness: { type: "number", minimum: 0, maximum: 1, description: "有用性" },
                            response_accuracy: { type: "number", minimum: 0, maximum: 1, description: "准确性" },
                            response_relevance: { type: "number", minimum: 0, maximum: 1, description: "相关性" },

                            // 好感度影响
                            affinity_delta: { type: "number", minimum: -2, maximum: 2, description: "好感度变化量" },
                            trust_change: { type: "number", minimum: -1, maximum: 1, description: "信任度变化" },
                            relationship_type: { type: "string", enum: ["hostile", "unfriendly", "neutral", "friendly", "close"] },
                            emotional_resonance: { type: "number", minimum: 0, maximum: 1, description: "情感共鸣" },

                            // 概念分析
                            main_concepts: { type: "array", items: { type: "string" }, maxItems: 5, description: "主要概念" },
                            conversation_topic: { type: "string", description: "对话主题" },
                            knowledge_domains: { type: "array", items: { type: "string" }, maxItems: 3, description: "知识领域" },

                            // 综合评估
                            interaction_quality: { type: "number", minimum: 0, maximum: 1, description: "交互质量" },
                            conversation_outcome: { type: "string", enum: ["very_negative", "negative", "neutral", "positive", "very_positive"] }
                        },
                        required: [
                            "user_positivity", "user_intensity", "user_satisfaction", "user_intent",
                            "response_quality", "response_helpfulness", "response_accuracy", "response_relevance",
                            "affinity_delta", "trust_change", "relationship_type", "emotional_resonance",
                            "main_concepts", "conversation_topic", "knowledge_domains",
                            "interaction_quality", "conversation_outcome"
                        ]
                    }
                }
            }];

            const prompt = `请综合分析以下对话的所有维度：

用户消息：${userMessage}
AI回复：${aiResponse}

分析要求：
1. 用户分析：
   - 情感积极性（-1到1，负面到正面）
   - 情感强度（0到1，平淡到强烈）
   - 满意度（基于用户反应推断）
   - 意图类型（问题、请求、抱怨、赞扬、闲聊）

2. AI回复质量：
   - 整体质量（0到1）
   - 有用性（是否解决用户需求）
   - 准确性（信息是否正确）
   - 相关性（是否切题）

3. 关系影响：
   - 好感度变化（-2到2，必须真实反映用户情感）
   - 信任度变化（-1到1）
   - 关系类型判断
   - 情感共鸣程度

4. 内容分析：
   - 提取主要概念（最多5个）
   - 识别对话主题
   - 确定涉及的知识领域

5. 综合评估：
   - 交互质量评分
   - 对话结果评价

重要：好感度变化必须基于用户真实情感，负面情绪必须导致负值！`;

            const response = await this.makeToolCall(prompt, tools, "analyze_conversation_comprehensive");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '综合对话分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 🚀 优化工具2：心理状态分析 - 分析AI的情绪、压力、模因状态
     */
    async optimizedPsychologicalAnalysis(userMessage, aiResponse, currentStates = {}, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_psychological_states",
                    description: "分析AI的心理状态变化：情绪、压力、模因影响",
                    parameters: {
                        type: "object",
                        properties: {
                            // 情绪分析
                            emotion_change: { type: "number", minimum: -20, maximum: 20, description: "情绪值变化量" },
                            emotion_factors: { type: "array", items: { type: "string" }, maxItems: 3, description: "影响情绪的因素" },
                            emotion_intensity: { type: "number", minimum: 0, maximum: 1, description: "情绪变化强度" },

                            // 压力分析
                            stress_change: { type: "number", minimum: -10, maximum: 10, description: "压力值变化量" },
                            stress_factors: { type: "array", items: { type: "string" }, maxItems: 3, description: "影响压力的因素" },
                            stress_level: { type: "string", enum: ["low", "normal", "high", "extreme"], description: "压力水平" },

                            // 模因分析
                            memetic_influence: { type: "number", minimum: 0, maximum: 1, description: "模因影响强度" },
                            memetic_concepts: { type: "array", items: { type: "string" }, maxItems: 3, description: "激活的模因概念" },
                            cognitive_load: { type: "number", minimum: 0, maximum: 1, description: "认知负荷" },

                            // 世界树影响
                            worldtree_category: { type: "string", enum: ["信息服务", "娱乐互动", "情感支持", "学习辅导", "创作协助", "其他"], description: "世界树分类" },
                            worldtree_influence: { type: "number", minimum: -5, maximum: 5, description: "世界树影响值" },

                            // 综合心理状态
                            overall_mood: { type: "string", enum: ["很消极", "消极", "中性", "积极", "很积极"], description: "整体心情" },
                            psychological_stability: { type: "number", minimum: 0, maximum: 1, description: "心理稳定性" },
                            adaptation_level: { type: "number", minimum: 0, maximum: 1, description: "适应性水平" }
                        },
                        required: [
                            "emotion_change", "emotion_factors", "emotion_intensity",
                            "stress_change", "stress_factors", "stress_level",
                            "memetic_influence", "memetic_concepts", "cognitive_load",
                            "worldtree_category", "worldtree_influence",
                            "overall_mood", "psychological_stability", "adaptation_level"
                        ]
                    }
                }
            }];

            const currentEmotionInfo = currentStates.emotion ?
                `当前情绪值: ${currentStates.emotion.emotion_value || 0}` : '当前情绪值: 0';
            const currentStressInfo = currentStates.stress ?
                `当前压力值: ${currentStates.stress.stress_value || 0}` : '当前压力值: 0';
            const currentMemeInfo = currentStates.meme ?
                `当前模因影响: ${currentStates.meme.memetic_influence || 0.5}` : '当前模因影响: 0.5';

            const prompt = `请分析回复者在以下对话中的心理状态变化：

用户消息：${userMessage}
回复者消息：${aiResponse}

${currentEmotionInfo}
${currentStressInfo}
${currentMemeInfo}

🧠 分析要求：
1. 情绪变化：
   - 基于对话内容计算情绪值变化（-20到20）
   - 识别影响情绪的主要因素
   - 评估情绪变化的强度

2. 压力变化：
   - 计算压力值变化（-10到10）
   - 分析压力来源和缓解因素
   - 评估当前压力水平

3. 模因影响：
   - 评估模因激活程度（0到1）
   - 识别相关的模因概念
   - 计算认知负荷

4. 世界树分类：
   - 根据对话内容确定世界树分类
   - 计算对应的影响值（-5到5）

5. 综合评估：
   - 整体心情状态
   - 心理稳定性
   - 适应性水平

请基于心理学原理进行科学分析。`;

            const response = await this.makeToolCall(prompt, tools, "analyze_psychological_states");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '心理状态分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 生成对话智能概括
     */
    async generateConversationSummary(userMessage, aiResponse, context = {}) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "generate_conversation_summary",
                    description: "生成对话的智能概括和关键洞察",
                    parameters: {
                        type: "object",
                        properties: {
                            ai_summary: {
                                type: "string",
                                description: "对话的简洁智能概括，突出核心内容和价值"
                            },
                            conversation_theme: {
                                type: "string",
                                description: "对话的主要主题或分类"
                            },
                            key_insights: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        insight: {
                                            type: "string",
                                            description: "关键洞察内容"
                                        },
                                        importance: {
                                            type: "number",
                                            description: "重要性评分 (0-1)"
                                        },
                                        category: {
                                            type: "string",
                                            description: "洞察类别：情感、信息、行为、偏好等"
                                        }
                                    }
                                },
                                description: "从对话中提取的关键洞察"
                            },
                            emotional_tone: {
                                type: "string",
                                description: "对话的整体情感基调"
                            },
                            interaction_quality: {
                                type: "string",
                                description: "互动质量评估：excellent/good/average/poor"
                            },
                            memory_value: {
                                type: "number",
                                description: "记忆价值评分 (0-1)，表示这段对话的长期记忆价值"
                            }
                        },
                        required: ["ai_summary", "conversation_theme", "key_insights", "emotional_tone", "interaction_quality", "memory_value"]
                    }
                }
            }];

            const userId = context.userId || '用户';
            const assistantName = context.assistantName || 'AI助手';

            const prompt = `请为以下对话生成智能概括和关键洞察：

【对话参与者】
- 用户：${userId}
- 回复者：${assistantName}

【对话内容】
${userId}: ${userMessage}
${assistantName}: ${aiResponse}

概括要求：
1. 概括：用1-2句话概括对话的核心内容和价值，必须包含参与者名称（${userId}和${assistantName}），要比原始对话更有洞察力和可读性
2. 对话主题：识别主要讨论的话题或领域
3. 关键洞察：提取对话中的重要信息、情感线索、用户偏好、行为模式等
4. 情感基调：分析整体的情感氛围和互动质量
5. 互动质量：评估对话的质量和深度
6. 记忆价值：评估这段对话对未来互动的参考价值

目标：生成包含人物信息、比原始对话更有价值的智能摘要，便于后续检索和理解。

示例格式：
- 好的概括："${userId}询问时间信息，${assistantName}提供了当前时间并关心${userId}的作息，体现了贴心的互动关系"
- 避免的概括："用户询问时间，显示出对时间的关注"（缺少人物名称和互动细节）`;

            const response = await this.makeToolCall(prompt, tools, "generate_conversation_summary");
            return response;

        } catch (error) {
            this.logger.error('OpenAI服务', '对话概括生成失败:', error.message);
            throw error;
        }
    }
}

module.exports = OpenAIToolsService;