/**
 * Command Processing Plugin
 * Handles commands starting with /
 */

const BasePlugin = require('./base_plugin');

class CommandPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('Command', adapter, logger, config);

        this.priority = 10; // Highest priority
        this.supportedTypes = ['message'];

        // Command handler mapping
        this.commands = new Map();

        // Register built-in commands
        this.registerBuiltinCommands();
    }

    async initialize() {
        await super.initialize();
        this.logger.info('Plugin', `Command plugin loaded ${this.commands.size} commands`);
    }

    /**
     * Register built-in commands
     */
    registerBuiltinCommands() {
        // Help command
        this.registerCommand('help', {
            description: 'Show help information',
            usage: '/help [command_name]',
            handler: this.handleHelp.bind(this)
        });

        // Status command
        this.registerCommand('status', {
            description: 'Show bot status',
            usage: '/status',
            handler: this.handleStatus.bind(this)
        });

        // Time command
        this.registerCommand('time', {
            description: 'Show current time',
            usage: '/time',
            handler: this.handleTime.bind(this)
        });

        // Ping command
        this.registerCommand('ping', {
            description: 'Test bot response',
            usage: '/ping',
            handler: this.handlePing.bind(this)
        });

        // Info command
        this.registerCommand('info', {
            description: 'Show group or user information',
            usage: '/info',
            handler: this.handleInfo.bind(this)
        });

        // Echo command
        this.registerCommand('echo', {
            description: 'Echo message',
            usage: '/echo <message_content>',
            handler: this.handleEcho.bind(this)
        });

        // Plugin management command
        this.registerCommand('plugins', {
            description: 'Show plugin list',
            usage: '/plugins',
            handler: this.handlePlugins.bind(this),
            requireAdmin: true
        });

        // Statistics command
        this.registerCommand('stats', {
            description: 'Show statistics',
            usage: '/stats',
            handler: this.handleStats.bind(this)
        });
    }

    /**
     * Register command
     */
    registerCommand(name, config) {
        this.commands.set(name.toLowerCase(), {
            name: name,
            description: config.description || 'No description',
            usage: config.usage || `/${name}`,
            handler: config.handler,
            requireAdmin: config.requireAdmin || false,
            enabled: config.enabled !== false
        });
    }

    /**
     * Check if this message should be handled
     */
    async shouldHandle(context) {
        if (!context.message || !context.message.trim().startsWith('/')) {
            return false;
        }

        return await super.shouldHandle(context);
    }

    /**
     * Handle command
     */
    async handle(context) {
        const message = context.message.trim();
        const [commandName, ...args] = message.substring(1).split(' ');

        const command = this.commands.get(commandName.toLowerCase());

        if (!command) {
            await this.reply(context, `❌ Unknown command: ${commandName}\nType /help to see available commands`);
            return { handled: false, reason: 'unknown_command' };
        }

        if (!command.enabled) {
            await this.reply(context, `❌ Command ${commandName} is disabled`);
            return { handled: false, reason: 'command_disabled' };
        }

        // Check admin permission
        if (command.requireAdmin && !await this.checkAdminPermission(context)) {
            await this.reply(context, `❌ Command ${commandName} requires admin permission`);
            return { handled: false, reason: 'permission_denied' };
        }

        try {
            const result = await command.handler(context, args);
            this.logger.info('Plugin', `Command executed: ${commandName} by ${context.userId}`);

            return {
                handled: true,
                command: commandName,
                args: args,
                result: result
            };

        } catch (error) {
            this.logger.error('Plugin', `Command execution failed: ${commandName} - ${error.message}`);
            await this.reply(context, `❌ Command execution failed: ${error.message}`);

            return {
                handled: false,
                reason: 'execution_error',
                error: error.message
            };
        }
    }

    /**
     * Help command handler
     */
    async handleHelp(context, args) {
        if (args.length > 0) {
            // Show specific command help
            const commandName = args[0].toLowerCase();
            const command = this.commands.get(commandName);

            if (!command) {
                await this.reply(context, `❌ Command ${commandName} does not exist`);
                return;
            }

            const helpText = `📖 Command Help: ${command.name}\n\n` +
                           `📝 Description: ${command.description}\n` +
                           `💡 Usage: ${command.usage}\n` +
                           `🔒 Requires Admin: ${command.requireAdmin ? 'Yes' : 'No'}`;

            await this.reply(context, helpText);
        } else {
            // Show all commands list
            const commandList = Array.from(this.commands.values())
                .filter(cmd => cmd.enabled)
                .map(cmd => `/${cmd.name} - ${cmd.description}`)
                .join('\n');

            const helpText = `🤖 OneBot11 Bot Command Help\n\n` +
                           `📋 Available Commands:\n${commandList}\n\n` +
                           `💡 Use /help <command_name> for detailed help`;

            await this.reply(context, helpText);
        }
    }

    /**
     * Status command handler
     */
    async handleStatus(context, args) {
        const adapterStats = this.adapter.getStats ? this.adapter.getStats() : {};
        const startTime = this.adapter.startTime || Date.now();
        const uptime = Math.floor((Date.now() - startTime) / 1000);

        const statusText = `📊 Bot Status Report\n\n` +
                          `✅ Status: Running\n` +
                          `⏰ Uptime: ${this.formatUptime(uptime)}\n` +
                          `📨 Messages Processed: ${adapterStats.messagesProcessed || 0}\n` +
                          `👥 Group Messages: ${adapterStats.groupMessages || 0}\n` +
                          `💬 Private Messages: ${adapterStats.privateMessages || 0}\n` +
                          `🔔 Notice Events: ${adapterStats.notices || 0}\n` +
                          `📊 Active Connections: ${adapterStats.connections || 0}`;

        await this.reply(context, statusText);
    }

    /**
     * Time command handler
     */
    async handleTime(context, args) {
        const now = new Date();
        const timeText = `🕐 Current Time Information\n\n` +
                        `📅 Date: ${now.toLocaleDateString()}\n` +
                        `⏰ Time: ${now.toLocaleTimeString()}\n` +
                        `🌍 Timezone: ${Intl.DateTimeFormat().resolvedOptions().timeZone}\n` +
                        `📊 Timestamp: ${now.getTime()}`;

        await this.reply(context, timeText);
    }

    /**
     * Ping command handler
     */
    async handlePing(context, args) {
        const startTime = Date.now();
        await this.reply(context, `🏓 Pong! Latency: ${Date.now() - startTime}ms`);
    }

    /**
     * Info command handler
     */
    async handleInfo(context, args) {
        let infoText = `📋 Message Information\n\n`;

        if (context.type === 'group') {
            infoText += `🏠 Group Chat Info:\n` +
                       `🆔 Group ID: ${context.groupId}\n` +
                       `👤 Sender: ${context.sender?.nickname || context.sender?.card || 'Unknown'}\n` +
                       `🆔 User ID: ${context.userId}\n` +
                       `👑 Role: ${context.sender?.role || 'Member'}`;
        } else {
            infoText += `👤 Private Chat Info:\n` +
                       `🆔 User ID: ${context.userId}\n` +
                       `👤 Nickname: ${context.sender?.nickname || 'Unknown'}`;
        }

        infoText += `\n⏰ Message Time: ${new Date().toLocaleString()}`;

        await this.reply(context, infoText);
    }

    /**
     * Echo command handler
     */
    async handleEcho(context, args) {
        if (args.length === 0) {
            await this.reply(context, `❌ Please provide content to echo\nUsage: /echo <message_content>`);
            return;
        }

        const message = args.join(' ');
        await this.reply(context, `🔄 ${message}`);
    }

    /**
     * Plugins command handler
     */
    async handlePlugins(context, args) {
        const plugins = this.adapter.getPluginList ? this.adapter.getPluginList() : [];

        const pluginList = plugins.map(plugin => {
            const status = plugin.enabled ? '✅' : '❌';
            return `${status} ${plugin.name} (Priority: ${plugin.priority})`;
        }).join('\n');

        const pluginText = `🔌 Plugin List\n\n${pluginList}\n\n` +
                          `📊 Total: ${plugins.length} plugins`;

        await this.reply(context, pluginText);
    }

    /**
     * Statistics command handler
     */
    async handleStats(context, args) {
        const stats = this.stats;

        const statsText = `📊 Command Plugin Statistics\n\n` +
                         `🎯 Commands Triggered: ${stats.triggered}\n` +
                         `❌ Error Count: ${stats.errors}\n` +
                         `📝 Registered Commands: ${this.commands.size}\n` +
                         `⏰ Last Triggered: ${stats.lastTriggered ? stats.lastTriggered.toLocaleString() : 'None'}`;

        await this.reply(context, statsText);
    }

    /**
     * Format uptime
     */
    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    /**
     * Get command list
     */
    getCommands() {
        return Array.from(this.commands.values());
    }
}

module.exports = CommandPlugin;
