from enum import IntFlag

import comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 as __wrapper_module__
from comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 import (
    UIA_SplitButtonControlTypeId, TextEditChangeType_None,
    AnnotationType_UnsyncedChange, HeadingLevel4,
    UIA_IsRangeValuePatternAvailablePropertyId,
    UIA_IsMultipleViewPatternAvailablePropertyId,
    UIA_IsContentElementPropertyId, UIA_FontSizeAttributeId,
    WindowVisualState_Normal, AnnotationType_Header, GUID,
    TextUnit_Word, StyleId_BulletedList, UIA_VisualEffectsPropertyId,
    UIA_TransformPattern2Id, StyleId_Custom, AnnotationType_Unknown,
    UIA_StrikethroughColorAttributeId,
    PropertyConditionFlags_MatchSubstring, UIA_NotificationEventId,
    ProviderOptions_RefuseNonClientSupport,
    UIA_ScrollVerticalScrollPercentPropertyId,
    IUIAutomationObjectModelPattern, TreeScope_Ancestors,
    UIA_BeforeParagraphSpacingAttributeId, TreeScope_Parent,
    UIA_OutlineStylesAttributeId, UIA_TogglePatternId,
    StyleId_Heading6, UIA_ValuePatternId, UIA_ControlTypePropertyId,
    AnnotationType_FormulaError, IUIAutomationStylesPattern,
    UIA_DropTargetDropTargetEffectPropertyId,
    UIA_DragDropEffectPropertyId, UIA_TableRowOrColumnMajorPropertyId,
    UIA_SynchronizedInputPatternId, UIA_ForegroundColorAttributeId,
    UIA_IsActiveAttributeId, IUIAutomationElement5, HeadingLevel_None,
    AnnotationType_Highlighted, StyleId_Quote,
    IUIAutomationDragPattern, UIA_AnnotationObjectsPropertyId,
    UIA_LocalizedLandmarkTypePropertyId, UIA_GridItemPatternId,
    TreeTraversalOptions_Default, UIA_RangeValueIsReadOnlyPropertyId,
    UIA_CalendarControlTypeId, UIA_IsDialogPropertyId,
    UIA_NamePropertyId, DockPosition_Left,
    UIA_IsKeyboardFocusablePropertyId, UIA_AutomationIdPropertyId,
    UIA_IsCustomNavigationPatternAvailablePropertyId,
    UIA_Transform2ZoomLevelPropertyId,
    NotificationProcessing_CurrentThenMostRecent, DockPosition_Bottom,
    IUIAutomationTransformPattern2,
    UIA_IsTableItemPatternAvailablePropertyId,
    UIA_MenuBarControlTypeId, UIA_SelectionItemPatternId,
    UIA_TransformCanResizePropertyId,
    WindowInteractionState_BlockedByModalWindow,
    UIA_AnnotationTypesPropertyId,
    StructureChangeType_ChildrenReordered,
    AnnotationType_CircularReferenceError,
    UIA_DockDockPositionPropertyId, UIA_SayAsInterpretAsMetadataId,
    UIA_IsTextChildPatternAvailablePropertyId,
    UIA_SpreadsheetPatternId, IUIAutomationElement7,
    UIA_IsControlElementPropertyId, SupportedTextSelection_None,
    UIA_IsStylesPatternAvailablePropertyId,
    UIA_AnnotationAuthorPropertyId, TextUnit_Format,
    StructureChangeType_ChildrenInvalidated, IUIAutomationTextPattern,
    UIA_IsDragPatternAvailablePropertyId,
    UIA_IsRequiredForFormPropertyId, IUIAutomationRangeValuePattern,
    NavigateDirection_NextSibling, UIA_IsSuperscriptAttributeId,
    HeadingLevel2, UIA_HelpTextPropertyId, ExtendedProperty,
    IUIAutomationTransformPattern, UIA_SizePropertyId,
    UIA_MenuOpenedEventId, ZoomUnit_SmallIncrement,
    DockPosition_Right, UIA_CenterPointPropertyId,
    UIA_LegacyIAccessibleNamePropertyId,
    UIA_LegacyIAccessibleStatePropertyId, Library,
    UIA_Drag_DragCancelEventId, IUIAutomationAndCondition,
    UIA_MenuControlTypeId, UIA_PaneControlTypeId,
    UIA_ListItemControlTypeId, ScrollAmount_LargeIncrement,
    UIA_SelectionItem_ElementAddedToSelectionEventId, IUnknown,
    SupportedTextSelection_Multiple, TreeScope_Children,
    UIA_AriaRolePropertyId, RowOrColumnMajor_RowMajor,
    UIA_LegacyIAccessibleKeyboardShortcutPropertyId,
    UIA_WindowIsModalPropertyId, UIA_NavigationLandmarkTypeId,
    UIA_FrameworkIdPropertyId, UIA_TabControlTypeId,
    UIA_CaretPositionAttributeId,
    UIA_ScrollVerticallyScrollablePropertyId,
    UIA_TableItemColumnHeaderItemsPropertyId,
    UIA_IsSelectionPattern2AvailablePropertyId, UIA_SummaryChangeId,
    UIA_Drag_DragCompleteEventId, UIA_IsReadOnlyAttributeId,
    SynchronizedInputType_RightMouseDown,
    ConnectionRecoveryBehaviorOptions_Disabled,
    UIA_IsTextEditPatternAvailablePropertyId, UIA_RangeValuePatternId,
    UIA_TransformPatternId, IUIAutomationInvokePattern,
    ProviderOptions_NonClientAreaProvider,
    UIA_ExpandCollapseExpandCollapseStatePropertyId, StyleId_Heading9,
    HeadingLevel1, UIA_TransformCanMovePropertyId,
    UIA_AnnotationTypesAttributeId,
    UIA_AnnotationAnnotationTypeIdPropertyId, UIA_SelectionPattern2Id,
    tagRECT, ScrollAmount_LargeDecrement, NavigateDirection_Parent,
    DockPosition_Fill, typelib_path, UIA_MenuClosedEventId,
    UIA_LegacyIAccessiblePatternId, IUIAutomationTableItemPattern,
    AnnotationType_Author, UIA_DropTarget_DragEnterEventId,
    IUIAutomationSelectionPattern2, UIA_RuntimeIdPropertyId,
    UIA_SizeOfSetPropertyId, UIA_GridItemRowSpanPropertyId,
    UIA_CheckBoxControlTypeId, UIA_TableItemRowHeaderItemsPropertyId,
    UIA_GridRowCountPropertyId, UIA_ExpandCollapsePatternId,
    ExpandCollapseState_PartiallyExpanded,
    UIA_MarginLeadingAttributeId, IUIAutomationTogglePattern,
    StructureChangeType_ChildrenBulkRemoved,
    ProviderOptions_UseClientCoordinates,
    UIA_WindowIsTopmostPropertyId,
    UIA_AfterParagraphSpacingAttributeId, ScrollAmount_SmallDecrement,
    ToggleState_Indeterminate, NotificationProcessing_ImportantAll,
    UIA_AsyncContentLoadedEventId, AutomationElementMode_Full,
    RowOrColumnMajor_Indeterminate, IUIAutomationTextRangeArray,
    UIA_EditControlTypeId, IUIAutomationScrollItemPattern,
    UIA_AriaPropertiesPropertyId, UIA_IsPeripheralPropertyId,
    UIA_AccessKeyPropertyId, UIA_MenuModeEndEventId,
    UIA_ObjectModelPatternId, UIA_TableControlTypeId,
    SynchronizedInputType_KeyDown, TreeScope_Descendants,
    IUIAutomationNotificationEventHandler, UIA_FillColorPropertyId,
    UIA_ListControlTypeId, UIA_StrikethroughStyleAttributeId,
    UIA_DocumentControlTypeId, UIA_StyleIdAttributeId,
    ZoomUnit_SmallDecrement, UIA_RangeValueValuePropertyId,
    ExpandCollapseState_LeafNode, IUIAutomationTreeWalker,
    UIA_DragDropEffectsPropertyId, UIA_CaretBidiModeAttributeId,
    UIA_HasKeyboardFocusPropertyId, ScrollAmount_NoAmount,
    IUIAutomationSynchronizedInputPattern,
    UIA_ToggleToggleStatePropertyId,
    IUIAutomationStructureChangedEventHandler,
    UIA_ValueValuePropertyId, IUIAutomationElement4,
    UIA_HeaderItemControlTypeId, UIA_AutomationPropertyChangedEventId,
    CoalesceEventsOptions_Enabled, UIA_MenuModeStartEventId,
    UIA_TextChildPatternId, UIA_GridItemColumnSpanPropertyId,
    StyleId_Title, ScrollAmount_SmallIncrement,
    SynchronizedInputType_RightMouseUp, IUIAutomation,
    UIA_IsSelectionPatternAvailablePropertyId, IUIAutomationCondition,
    _check_version, UIA_SeparatorControlTypeId,
    AnnotationType_EditingLockedChange,
    IUIAutomationPropertyChangedEventHandler, Polite, IUIAutomation3,
    UIA_MarginTopAttributeId, ZoomUnit_NoAmount,
    UIA_MultipleViewCurrentViewPropertyId, IUIAutomationElement,
    IUIAutomationAnnotationPattern,
    UIA_SelectionItemIsSelectedPropertyId, VARIANT,
    IUIAutomationProxyFactoryEntry,
    UIA_Selection2CurrentSelectedItemPropertyId,
    UIA_LandmarkTypePropertyId, UIA_LabeledByPropertyId,
    UIA_IsVirtualizedItemPatternAvailablePropertyId, IUIAutomation5,
    IUIAutomationElement9, WindowInteractionState_NotResponding,
    UIA_AutomationFocusChangedEventId, IUIAutomationDockPattern,
    UIA_IsTransformPatternAvailablePropertyId, UIA_TreeControlTypeId,
    UIA_SearchLandmarkTypeId, WindowInteractionState_Running,
    AnnotationType_Comment, SupportedTextSelection_Single,
    UIA_SystemAlertEventId, UIA_HostedFragmentRootsInvalidatedEventId,
    RowOrColumnMajor_ColumnMajor,
    UIA_IsExpandCollapsePatternAvailablePropertyId,
    UIA_IsWindowPatternAvailablePropertyId, UIA_ButtonControlTypeId,
    UIA_CustomNavigationPatternId, UIA_GridItemColumnPropertyId,
    UIA_CapStyleAttributeId, IUIAutomationItemContainerPattern,
    IUIAutomation2, Assertive, UIA_RadioButtonControlTypeId,
    UIA_ItemTypePropertyId, StructureChangeType_ChildAdded,
    UIA_ToolBarControlTypeId, UIA_FullDescriptionPropertyId,
    UIA_FlowsToPropertyId, NotificationKind_ItemRemoved,
    UIA_IsEnabledPropertyId, CoalesceEventsOptions_Disabled,
    UIA_AnnotationAnnotationTypeNamePropertyId,
    NavigateDirection_PreviousSibling, UIA_FontNameAttributeId,
    UIA_Selection2FirstSelectedItemPropertyId,
    UIA_AnnotationObjectsAttributeId,
    SynchronizedInputType_LeftMouseDown, BSTR, HeadingLevel8,
    UIA_MultipleViewPatternId, UIA_IsInvokePatternAvailablePropertyId,
    ProviderOptions_UseComThreading,
    ProviderOptions_ClientSideProvider, UIA_CultureAttributeId,
    IUIAutomationSelectionPattern, UIA_TextEdit_TextChangedEventId,
    UIA_RangeValueMaximumPropertyId, TextUnit_Line, HeadingLevel6,
    UIA_ProcessIdPropertyId, UIA_SelectionItem_ElementSelectedEventId,
    IUIAutomationChangesEventHandler, UIA_TablePatternId, dispid,
    UIA_WindowCanMaximizePropertyId, UIA_DragIsGrabbedPropertyId,
    UIA_GridPatternId, UIA_Window_WindowOpenedEventId,
    UIA_Drag_DragStartEventId, StyleId_Subtitle,
    UIA_GridItemContainingGridPropertyId,
    UIA_SelectionSelectionPropertyId, IUIAutomationBoolCondition,
    UIA_CustomControlTypeId, UIA_MainLandmarkTypeId,
    UIA_ItemStatusPropertyId, UIA_TextControlTypeId, IUIAutomation4,
    WSTRING, tagPOINT, UIA_Selection2ItemCountPropertyId,
    UIA_IsSpreadsheetItemPatternAvailablePropertyId,
    UIA_SelectionActiveEndAttributeId, UIA_StructureChangedEventId,
    NavigateDirection_FirstChild, IUIAutomationSelectionItemPattern,
    AnnotationType_FormatChange,
    StructureChangeType_ChildrenBulkAdded, UIA_TextPatternId,
    UIA_MultipleViewSupportedViewsPropertyId, UIA_LevelPropertyId,
    NotificationKind_ActionCompleted,
    StructureChangeType_ChildRemoved,
    IUIAutomationProxyFactoryMapping, UIA_DropTargetPatternId,
    UIA_OutlineColorPropertyId, UIA_LinkAttributeId,
    TextEditChangeType_AutoComplete,
    UIA_IsSynchronizedInputPatternAvailablePropertyId,
    UIA_VirtualizedItemPatternId,
    IUIAutomationCustomNavigationPattern,
    UIA_ProviderDescriptionPropertyId, StyleId_Heading1,
    UIA_AnnotationDateTimePropertyId, UIA_OrientationPropertyId,
    UIA_ControllerForPropertyId,
    UIA_IsSelectionItemPatternAvailablePropertyId,
    ProviderOptions_ProviderOwnsSetFocus, CUIAutomation8,
    UIA_RangeValueLargeChangePropertyId,
    UIA_LegacyIAccessibleHelpPropertyId,
    UIA_Transform2ZoomMinimumPropertyId, IUIAutomation6,
    AnnotationType_DataValidationError,
    AnnotationType_InsertionChange, StyleId_Heading3,
    IUIAutomationGridItemPattern, StyleId_Heading5,
    UIA_IsItemContainerPatternAvailablePropertyId,
    IUIAutomationTextEditTextChangedEventHandler,
    IUIAutomationEventHandler,
    UIA_IsAnnotationPatternAvailablePropertyId,
    UIA_MarginBottomAttributeId, UIA_LineSpacingAttributeId,
    UIA_UnderlineStyleAttributeId, PropertyConditionFlags_None,
    UIA_IsTransformPattern2AvailablePropertyId,
    NotificationProcessing_ImportantMostRecent,
    UIA_ScrollHorizontalViewSizePropertyId,
    UIA_OptimizeForVisualContentPropertyId,
    NotificationProcessing_MostRecent, AnnotationType_ExternalChange,
    UIA_TreeItemControlTypeId, TextUnit_Page,
    UIA_UnderlineColorAttributeId,
    IUIAutomationSpreadsheetItemPattern, UIA_StatusBarControlTypeId,
    UIA_AnnotationTargetPropertyId,
    UIA_SelectionItemSelectionContainerPropertyId,
    UIA_RangeValueSmallChangePropertyId, IUIAutomationTextRange3,
    ConnectionRecoveryBehaviorOptions_Enabled, StyleId_Normal,
    UIA_WindowControlTypeId, UIA_LayoutInvalidatedEventId,
    UIA_ScrollBarControlTypeId, UIA_DropTarget_DroppedEventId,
    UIA_ActiveTextPositionChangedEventId, AnnotationType_Endnote,
    AnnotationType_MoveChange,
    UIA_LegacyIAccessibleSelectionPropertyId, UIA_ThumbControlTypeId,
    AnnotationType_Footer, SynchronizedInputType_LeftMouseUp,
    UIA_IsGridPatternAvailablePropertyId,
    UIA_StylesFillColorPropertyId, IUIAutomationPropertyCondition,
    UIA_IndentationLeadingAttributeId, UIA_InputReachedTargetEventId,
    TreeScope_Subtree, IUIAutomationDropTargetPattern,
    UIA_StylesPatternId, ZoomUnit_LargeDecrement, Off,
    UIA_ChangesEventId, UIA_SpreadsheetItemFormulaPropertyId,
    UIA_AppBarControlTypeId, UIA_IsPasswordPropertyId,
    UIA_IsScrollItemPatternAvailablePropertyId, TextUnit_Character,
    TreeScope_Element, TreeTraversalOptions_PostOrder,
    UIA_StylesFillPatternStylePropertyId, UIA_TitleBarControlTypeId,
    UIA_LiveSettingPropertyId, UIA_IsTextPattern2AvailablePropertyId,
    IUIAutomationExpandCollapsePattern, UIA_ClickablePointPropertyId,
    IDispatch, UIA_IsLegacyIAccessiblePatternAvailablePropertyId,
    UIA_DescribedByPropertyId, IUIAutomationElementArray,
    UIA_DataItemControlTypeId, UIA_LegacyIAccessibleValuePropertyId,
    UIA_WindowWindowVisualStatePropertyId,
    PropertyConditionFlags_IgnoreCase,
    UIA_SpreadsheetItemAnnotationTypesPropertyId,
    UIA_SelectionItem_ElementRemovedFromSelectionEventId,
    UIA_IndentationTrailingAttributeId, UIA_OverlineColorAttributeId,
    ExpandCollapseState_Collapsed, IUIAutomationElement3,
    StyleId_Heading4, UIA_InvokePatternId,
    AnnotationType_AdvancedProofingIssue,
    UIA_AnimationStyleAttributeId, AnnotationType_TrackChanges,
    ProviderOptions_OverrideProvider, UIA_RangeValueMinimumPropertyId,
    UIA_HeaderControlTypeId, UIA_ScrollVerticalViewSizePropertyId,
    OrientationType_None, UIA_IsTextPatternAvailablePropertyId,
    AnnotationType_Mathematics,
    UIA_WindowWindowInteractionStatePropertyId, _lcid,
    UIA_MarginTrailingAttributeId, CoClass, UIA_TabItemControlTypeId,
    UIA_Transform2CanZoomPropertyId, HeadingLevel9,
    StyleId_NumberedList, ZoomUnit_LargeIncrement,
    AnnotationType_GrammarError, UIA_TextPattern2Id,
    IUIAutomationTextRange2, UIA_Window_WindowClosedEventId,
    NavigateDirection_LastChild, UIA_TextFlowDirectionsAttributeId,
    UIA_ScrollPatternId, UIA_InputDiscardedEventId, UIA_DragPatternId,
    UIA_IsObjectModelPatternAvailablePropertyId,
    UIA_StylesExtendedPropertiesPropertyId,
    ProviderOptions_ServerSideProvider, IUIAutomationTablePattern,
    AnnotationType_DeletionChange, UIA_WindowCanMinimizePropertyId,
    UIA_DropTarget_DragLeaveEventId, IUIAutomationTextChildPattern,
    IUIAutomationElement6, UIA_Selection2LastSelectedItemPropertyId,
    HeadingLevel7, UIA_SpreadsheetItemAnnotationObjectsPropertyId,
    IUIAutomationSpreadsheetPattern, IUIAutomationTextRange,
    NotificationProcessing_All, UiaChangeInfo,
    UIA_LegacyIAccessibleDefaultActionPropertyId,
    UIA_IsItalicAttributeId, DockPosition_Top,
    UIA_SelectionCanSelectMultiplePropertyId, UIA_TabsAttributeId,
    ExpandCollapseState_Expanded, UIA_SpinnerControlTypeId,
    UIA_TableRowHeadersPropertyId,
    UIA_Text_TextSelectionChangedEventId, AnnotationType_Sensitive,
    UIA_LocalizedControlTypePropertyId, UIA_IsOffscreenPropertyId,
    UIA_SelectionPatternId, AutomationElementMode_None,
    UIA_GroupControlTypeId, CUIAutomation, NotificationKind_Other,
    UIA_IsSpreadsheetPatternAvailablePropertyId,
    UIA_HeadingLevelPropertyId, UIA_IsTablePatternAvailablePropertyId,
    UIA_DragGrabbedItemsPropertyId, UIA_WindowPatternId,
    IUIAutomationTextEditPattern, UIA_IsSubscriptAttributeId,
    UIA_SpreadsheetItemPatternId, UIA_StylesStyleIdPropertyId,
    IRawElementProviderSimple, UIA_ValueIsReadOnlyPropertyId,
    IUIAutomationLegacyIAccessiblePattern,
    SynchronizedInputType_KeyUp, StyleId_Heading2,
    UIA_IsDockPatternAvailablePropertyId,
    TextPatternRangeEndpoint_End,
    IUIAutomationFocusChangedEventHandler,
    IUIAutomationEventHandlerGroup, UIA_GridItemRowPropertyId,
    UIA_LegacyIAccessibleDescriptionPropertyId,
    UIA_Invoke_InvokedEventId, IUIAutomationVirtualizedItemPattern,
    AnnotationType_SpellingError, ToggleState_On,
    UIA_Selection_InvalidatedEventId, UIA_ScrollItemPatternId,
    HRESULT, IUIAutomationCacheRequest,
    UIA_HorizontalTextAlignmentAttributeId,
    UIA_AcceleratorKeyPropertyId, AnnotationType_ConflictingChange,
    TreeTraversalOptions_LastToFirstOrder,
    UIA_LegacyIAccessibleRolePropertyId,
    UIA_SayAsInterpretAsAttributeId, UIA_TextEditPatternId,
    UIA_TextEdit_ConversionTargetChangedEventId,
    TextEditChangeType_Composition,
    WindowInteractionState_ReadyForUserInteraction, HeadingLevel5,
    UIA_IsDataValidForFormPropertyId,
    UIA_ScrollHorizontallyScrollablePropertyId,
    UIA_StyleNameAttributeId, UIA_FontWeightAttributeId,
    UIA_CulturePropertyId, TextEditChangeType_AutoCorrect,
    UIA_FillTypePropertyId, UIA_LiveRegionChangedEventId,
    UIA_NativeWindowHandlePropertyId, WindowInteractionState_Closing,
    UIA_OverlineStyleAttributeId, StyleId_Emphasis,
    IUIAutomationScrollPattern, UIA_TransformCanRotatePropertyId,
    UIA_ProgressBarControlTypeId, TextUnit_Document,
    IUIAutomationValuePattern, UIA_IsScrollPatternAvailablePropertyId,
    TextEditChangeType_CompositionFinalized,
    UIA_IndentationFirstLineAttributeId, UIA_CustomLandmarkTypeId,
    UIA_RotationPropertyId, UIA_SliderControlTypeId,
    AnnotationType_Footnote, UIA_Text_TextChangedEventId,
    UIA_OutlineThicknessPropertyId,
    UIA_IsValuePatternAvailablePropertyId, IUIAutomationWindowPattern,
    StyleId_Heading8, IUIAutomationOrCondition,
    UIA_SemanticZoomControlTypeId, NotificationKind_ItemAdded,
    TextUnit_Paragraph, UIA_ScrollHorizontalScrollPercentPropertyId,
    DockPosition_None, ToggleState_Off, UIA_ToolTipOpenedEventId,
    IUIAutomationActiveTextPositionChangedEventHandler,
    UIA_IsTogglePatternAvailablePropertyId,
    UIA_DropTargetDropTargetEffectsPropertyId,
    IUIAutomationGridPattern,
    UIA_SelectionIsSelectionRequiredPropertyId,
    UIA_ClassNamePropertyId, UIA_StylesStyleNamePropertyId,
    UIA_DataGridControlTypeId, UIA_Transform2ZoomMaximumPropertyId,
    COMMETHOD, UIA_InputReachedOtherElementEventId, StyleId_Heading7,
    UIA_FlowsFromPropertyId, UIA_AnnotationPatternId,
    UIA_GridColumnCountPropertyId, IUIAutomationProxyFactory,
    UIA_ItemContainerPatternId, IUIAutomationTextPattern2,
    UIA_FormLandmarkTypeId, UIA_ToolTipControlTypeId,
    UIA_IsGridItemPatternAvailablePropertyId,
    UIA_StylesFillPatternColorPropertyId, OrientationType_Vertical,
    IAccessible, _midlSAFEARRAY, UIA_MenuItemControlTypeId,
    HeadingLevel3, WindowVisualState_Minimized,
    WindowVisualState_Maximized, UIA_StylesShapePropertyId,
    UIA_ToolTipClosedEventId, NotificationKind_ActionAborted,
    IUIAutomationElement2, UIA_HyperlinkControlTypeId,
    UIA_DockPatternId, TreeScope_None, UIA_BulletStyleAttributeId,
    ProviderOptions_HasNativeIAccessible, UIA_ImageControlTypeId,
    UIA_TableItemPatternId, IUIAutomationMultipleViewPattern,
    TextPatternRangeEndpoint_Start,
    UIA_LegacyIAccessibleChildIdPropertyId,
    UIA_TableColumnHeadersPropertyId, OrientationType_Horizontal,
    UIA_PositionInSetPropertyId,
    UIA_IsDropTargetPatternAvailablePropertyId,
    UIA_BackgroundColorAttributeId, UIA_ComboBoxControlTypeId,
    UIA_IsHiddenAttributeId, UIA_BoundingRectanglePropertyId,
    IUIAutomationNotCondition, IUIAutomationElement8
)


class CoalesceEventsOptions(IntFlag):
    CoalesceEventsOptions_Disabled = 0
    CoalesceEventsOptions_Enabled = 1


class TextPatternRangeEndpoint(IntFlag):
    TextPatternRangeEndpoint_Start = 0
    TextPatternRangeEndpoint_End = 1


class TextUnit(IntFlag):
    TextUnit_Character = 0
    TextUnit_Format = 1
    TextUnit_Word = 2
    TextUnit_Line = 3
    TextUnit_Paragraph = 4
    TextUnit_Page = 5
    TextUnit_Document = 6


class TreeScope(IntFlag):
    TreeScope_None = 0
    TreeScope_Element = 1
    TreeScope_Children = 2
    TreeScope_Descendants = 4
    TreeScope_Parent = 8
    TreeScope_Ancestors = 16
    TreeScope_Subtree = 7


class OrientationType(IntFlag):
    OrientationType_None = 0
    OrientationType_Horizontal = 1
    OrientationType_Vertical = 2


class LiveSetting(IntFlag):
    Off = 0
    Polite = 1
    Assertive = 2


class SynchronizedInputType(IntFlag):
    SynchronizedInputType_KeyUp = 1
    SynchronizedInputType_KeyDown = 2
    SynchronizedInputType_LeftMouseUp = 4
    SynchronizedInputType_LeftMouseDown = 8
    SynchronizedInputType_RightMouseUp = 16
    SynchronizedInputType_RightMouseDown = 32


class RowOrColumnMajor(IntFlag):
    RowOrColumnMajor_RowMajor = 0
    RowOrColumnMajor_ColumnMajor = 1
    RowOrColumnMajor_Indeterminate = 2


class TextEditChangeType(IntFlag):
    TextEditChangeType_None = 0
    TextEditChangeType_AutoCorrect = 1
    TextEditChangeType_Composition = 2
    TextEditChangeType_CompositionFinalized = 3
    TextEditChangeType_AutoComplete = 4


class SupportedTextSelection(IntFlag):
    SupportedTextSelection_None = 0
    SupportedTextSelection_Single = 1
    SupportedTextSelection_Multiple = 2


class ToggleState(IntFlag):
    ToggleState_Off = 0
    ToggleState_On = 1
    ToggleState_Indeterminate = 2


class PropertyConditionFlags(IntFlag):
    PropertyConditionFlags_None = 0
    PropertyConditionFlags_IgnoreCase = 1
    PropertyConditionFlags_MatchSubstring = 2


class WindowVisualState(IntFlag):
    WindowVisualState_Normal = 0
    WindowVisualState_Maximized = 1
    WindowVisualState_Minimized = 2


class WindowInteractionState(IntFlag):
    WindowInteractionState_Running = 0
    WindowInteractionState_Closing = 1
    WindowInteractionState_ReadyForUserInteraction = 2
    WindowInteractionState_BlockedByModalWindow = 3
    WindowInteractionState_NotResponding = 4


class NavigateDirection(IntFlag):
    NavigateDirection_Parent = 0
    NavigateDirection_NextSibling = 1
    NavigateDirection_PreviousSibling = 2
    NavigateDirection_FirstChild = 3
    NavigateDirection_LastChild = 4


class AutomationElementMode(IntFlag):
    AutomationElementMode_None = 0
    AutomationElementMode_Full = 1


class ZoomUnit(IntFlag):
    ZoomUnit_NoAmount = 0
    ZoomUnit_LargeDecrement = 1
    ZoomUnit_SmallDecrement = 2
    ZoomUnit_LargeIncrement = 3
    ZoomUnit_SmallIncrement = 4


class ConnectionRecoveryBehaviorOptions(IntFlag):
    ConnectionRecoveryBehaviorOptions_Disabled = 0
    ConnectionRecoveryBehaviorOptions_Enabled = 1


class DockPosition(IntFlag):
    DockPosition_Top = 0
    DockPosition_Left = 1
    DockPosition_Bottom = 2
    DockPosition_Right = 3
    DockPosition_Fill = 4
    DockPosition_None = 5


class ExpandCollapseState(IntFlag):
    ExpandCollapseState_Collapsed = 0
    ExpandCollapseState_Expanded = 1
    ExpandCollapseState_PartiallyExpanded = 2
    ExpandCollapseState_LeafNode = 3


class ScrollAmount(IntFlag):
    ScrollAmount_LargeDecrement = 0
    ScrollAmount_SmallDecrement = 1
    ScrollAmount_NoAmount = 2
    ScrollAmount_LargeIncrement = 3
    ScrollAmount_SmallIncrement = 4


class TreeTraversalOptions(IntFlag):
    TreeTraversalOptions_Default = 0
    TreeTraversalOptions_PostOrder = 1
    TreeTraversalOptions_LastToFirstOrder = 2


class StructureChangeType(IntFlag):
    StructureChangeType_ChildAdded = 0
    StructureChangeType_ChildRemoved = 1
    StructureChangeType_ChildrenInvalidated = 2
    StructureChangeType_ChildrenBulkAdded = 3
    StructureChangeType_ChildrenBulkRemoved = 4
    StructureChangeType_ChildrenReordered = 5


class NotificationKind(IntFlag):
    NotificationKind_ItemAdded = 0
    NotificationKind_ItemRemoved = 1
    NotificationKind_ActionCompleted = 2
    NotificationKind_ActionAborted = 3
    NotificationKind_Other = 4


class NotificationProcessing(IntFlag):
    NotificationProcessing_ImportantAll = 0
    NotificationProcessing_ImportantMostRecent = 1
    NotificationProcessing_All = 2
    NotificationProcessing_MostRecent = 3
    NotificationProcessing_CurrentThenMostRecent = 4


class ProviderOptions(IntFlag):
    ProviderOptions_ClientSideProvider = 1
    ProviderOptions_ServerSideProvider = 2
    ProviderOptions_NonClientAreaProvider = 4
    ProviderOptions_OverrideProvider = 8
    ProviderOptions_ProviderOwnsSetFocus = 16
    ProviderOptions_UseComThreading = 32
    ProviderOptions_RefuseNonClientSupport = 64
    ProviderOptions_HasNativeIAccessible = 128
    ProviderOptions_UseClientCoordinates = 256


__all__ = [
    'UIA_SplitButtonControlTypeId', 'TextEditChangeType_None',
    'AnnotationType_UnsyncedChange', 'HeadingLevel4',
    'UIA_IsRangeValuePatternAvailablePropertyId',
    'UIA_IsMultipleViewPatternAvailablePropertyId', 'ZoomUnit',
    'UIA_IsContentElementPropertyId', 'UIA_FontSizeAttributeId',
    'WindowVisualState_Normal', 'AnnotationType_Header',
    'TextUnit_Word', 'StyleId_BulletedList',
    'UIA_VisualEffectsPropertyId', 'TextPatternRangeEndpoint',
    'UIA_TransformPattern2Id', 'StyleId_Custom',
    'AnnotationType_Unknown', 'UIA_StrikethroughColorAttributeId',
    'PropertyConditionFlags_MatchSubstring',
    'UIA_NotificationEventId',
    'ProviderOptions_RefuseNonClientSupport',
    'UIA_ScrollVerticalScrollPercentPropertyId',
    'IUIAutomationObjectModelPattern', 'TreeScope_Ancestors',
    'UIA_BeforeParagraphSpacingAttributeId', 'TreeScope_Parent',
    'UIA_OutlineStylesAttributeId', 'UIA_TogglePatternId',
    'StyleId_Heading6', 'UIA_ValuePatternId',
    'UIA_ControlTypePropertyId', 'AnnotationType_FormulaError',
    'IUIAutomationStylesPattern',
    'UIA_DropTargetDropTargetEffectPropertyId',
    'UIA_DragDropEffectPropertyId',
    'UIA_TableRowOrColumnMajorPropertyId',
    'UIA_SynchronizedInputPatternId',
    'UIA_ForegroundColorAttributeId', 'UIA_IsActiveAttributeId',
    'IUIAutomationElement5', 'HeadingLevel_None',
    'AnnotationType_Highlighted', 'StyleId_Quote',
    'IUIAutomationDragPattern', 'UIA_AnnotationObjectsPropertyId',
    'UIA_LocalizedLandmarkTypePropertyId', 'UIA_GridItemPatternId',
    'TreeTraversalOptions_Default',
    'UIA_RangeValueIsReadOnlyPropertyId', 'UIA_CalendarControlTypeId',
    'UIA_IsDialogPropertyId', 'UIA_NamePropertyId',
    'DockPosition_Left', 'UIA_IsKeyboardFocusablePropertyId',
    'UIA_AutomationIdPropertyId',
    'UIA_IsCustomNavigationPatternAvailablePropertyId',
    'UIA_Transform2ZoomLevelPropertyId',
    'NotificationProcessing_CurrentThenMostRecent',
    'DockPosition_Bottom', 'IUIAutomationTransformPattern2',
    'UIA_IsTableItemPatternAvailablePropertyId',
    'UIA_MenuBarControlTypeId', 'UIA_SelectionItemPatternId',
    'UIA_TransformCanResizePropertyId',
    'WindowInteractionState_BlockedByModalWindow',
    'UIA_AnnotationTypesPropertyId',
    'StructureChangeType_ChildrenReordered',
    'AnnotationType_CircularReferenceError',
    'UIA_DockDockPositionPropertyId',
    'UIA_SayAsInterpretAsMetadataId',
    'UIA_IsTextChildPatternAvailablePropertyId',
    'UIA_SpreadsheetPatternId', 'IUIAutomationElement7',
    'UIA_IsControlElementPropertyId', 'SupportedTextSelection_None',
    'UIA_IsStylesPatternAvailablePropertyId',
    'UIA_AnnotationAuthorPropertyId', 'TextUnit_Format',
    'StructureChangeType_ChildrenInvalidated',
    'IUIAutomationTextPattern',
    'UIA_IsDragPatternAvailablePropertyId',
    'UIA_IsRequiredForFormPropertyId',
    'IUIAutomationRangeValuePattern', 'NavigateDirection_NextSibling',
    'UIA_IsSuperscriptAttributeId', 'HeadingLevel2',
    'UIA_HelpTextPropertyId', 'ExtendedProperty',
    'IUIAutomationTransformPattern', 'UIA_SizePropertyId',
    'UIA_MenuOpenedEventId', 'ZoomUnit_SmallIncrement',
    'DockPosition_Right', 'UIA_CenterPointPropertyId',
    'UIA_LegacyIAccessibleNamePropertyId',
    'UIA_LegacyIAccessibleStatePropertyId', 'Library',
    'UIA_Drag_DragCancelEventId', 'CoalesceEventsOptions',
    'IUIAutomationAndCondition', 'UIA_MenuControlTypeId',
    'UIA_PaneControlTypeId', 'UIA_ListItemControlTypeId',
    'ScrollAmount_LargeIncrement',
    'UIA_SelectionItem_ElementAddedToSelectionEventId',
    'SupportedTextSelection_Multiple', 'TreeScope_Children',
    'UIA_AriaRolePropertyId', 'RowOrColumnMajor_RowMajor',
    'UIA_LegacyIAccessibleKeyboardShortcutPropertyId',
    'UIA_WindowIsModalPropertyId', 'UIA_NavigationLandmarkTypeId',
    'UIA_FrameworkIdPropertyId', 'UIA_TabControlTypeId',
    'UIA_CaretPositionAttributeId',
    'UIA_ScrollVerticallyScrollablePropertyId',
    'UIA_TableItemColumnHeaderItemsPropertyId',
    'UIA_IsSelectionPattern2AvailablePropertyId',
    'UIA_SummaryChangeId', 'UIA_Drag_DragCompleteEventId',
    'UIA_IsReadOnlyAttributeId',
    'SynchronizedInputType_RightMouseDown',
    'ConnectionRecoveryBehaviorOptions_Disabled',
    'UIA_IsTextEditPatternAvailablePropertyId',
    'UIA_RangeValuePatternId', 'UIA_TransformPatternId',
    'IUIAutomationInvokePattern',
    'ProviderOptions_NonClientAreaProvider',
    'UIA_ExpandCollapseExpandCollapseStatePropertyId',
    'StyleId_Heading9', 'HeadingLevel1',
    'UIA_TransformCanMovePropertyId',
    'UIA_AnnotationTypesAttributeId',
    'UIA_AnnotationAnnotationTypeIdPropertyId',
    'UIA_SelectionPattern2Id', 'ScrollAmount_LargeDecrement',
    'NavigateDirection_Parent', 'DockPosition_Fill', 'typelib_path',
    'UIA_MenuClosedEventId', 'UIA_LegacyIAccessiblePatternId',
    'IUIAutomationTableItemPattern', 'AnnotationType_Author',
    'UIA_DropTarget_DragEnterEventId', 'ProviderOptions',
    'RowOrColumnMajor', 'IUIAutomationSelectionPattern2',
    'UIA_RuntimeIdPropertyId', 'UIA_SizeOfSetPropertyId',
    'UIA_GridItemRowSpanPropertyId', 'UIA_CheckBoxControlTypeId',
    'UIA_TableItemRowHeaderItemsPropertyId',
    'UIA_GridRowCountPropertyId', 'UIA_ExpandCollapsePatternId',
    'ExpandCollapseState_PartiallyExpanded',
    'UIA_MarginLeadingAttributeId', 'IUIAutomationTogglePattern',
    'StructureChangeType_ChildrenBulkRemoved',
    'ProviderOptions_UseClientCoordinates',
    'UIA_WindowIsTopmostPropertyId',
    'UIA_AfterParagraphSpacingAttributeId',
    'ScrollAmount_SmallDecrement', 'ToggleState_Indeterminate',
    'NotificationProcessing_ImportantAll',
    'UIA_AsyncContentLoadedEventId', 'AutomationElementMode_Full',
    'RowOrColumnMajor_Indeterminate', 'IUIAutomationTextRangeArray',
    'UIA_EditControlTypeId', 'OrientationType',
    'IUIAutomationScrollItemPattern', 'UIA_AriaPropertiesPropertyId',
    'UIA_IsPeripheralPropertyId', 'UIA_AccessKeyPropertyId',
    'UIA_MenuModeEndEventId', 'UIA_ObjectModelPatternId',
    'DockPosition', 'UIA_TableControlTypeId', 'StructureChangeType',
    'SynchronizedInputType_KeyDown', 'TreeScope_Descendants',
    'IUIAutomationNotificationEventHandler',
    'UIA_FillColorPropertyId', 'LiveSetting', 'UIA_ListControlTypeId',
    'UIA_StrikethroughStyleAttributeId', 'UIA_DocumentControlTypeId',
    'UIA_StyleIdAttributeId', 'ZoomUnit_SmallDecrement',
    'UIA_RangeValueValuePropertyId', 'ExpandCollapseState_LeafNode',
    'IUIAutomationTreeWalker', 'UIA_DragDropEffectsPropertyId',
    'UIA_CaretBidiModeAttributeId', 'UIA_HasKeyboardFocusPropertyId',
    'ScrollAmount_NoAmount', 'NotificationProcessing',
    'IUIAutomationSynchronizedInputPattern',
    'UIA_ToggleToggleStatePropertyId',
    'IUIAutomationStructureChangedEventHandler',
    'UIA_ValueValuePropertyId', 'IUIAutomationElement4',
    'UIA_HeaderItemControlTypeId',
    'UIA_AutomationPropertyChangedEventId',
    'CoalesceEventsOptions_Enabled', 'UIA_MenuModeStartEventId',
    'UIA_TextChildPatternId', 'UIA_GridItemColumnSpanPropertyId',
    'StyleId_Title', 'ScrollAmount_SmallIncrement',
    'SynchronizedInputType_RightMouseUp', 'IUIAutomation',
    'UIA_IsSelectionPatternAvailablePropertyId', 'TreeScope',
    'IUIAutomationCondition', 'UIA_SeparatorControlTypeId',
    'AnnotationType_EditingLockedChange',
    'IUIAutomationPropertyChangedEventHandler', 'Polite',
    'IUIAutomation3', 'UIA_MarginTopAttributeId', 'ZoomUnit_NoAmount',
    'UIA_MultipleViewCurrentViewPropertyId', 'IUIAutomationElement',
    'IUIAutomationAnnotationPattern',
    'UIA_SelectionItemIsSelectedPropertyId',
    'IUIAutomationProxyFactoryEntry',
    'UIA_Selection2CurrentSelectedItemPropertyId',
    'UIA_LandmarkTypePropertyId', 'UIA_LabeledByPropertyId',
    'UIA_IsVirtualizedItemPatternAvailablePropertyId',
    'IUIAutomation5', 'SynchronizedInputType',
    'IUIAutomationElement9', 'WindowInteractionState_NotResponding',
    'UIA_AutomationFocusChangedEventId', 'IUIAutomationDockPattern',
    'UIA_IsTransformPatternAvailablePropertyId',
    'UIA_TreeControlTypeId', 'UIA_SearchLandmarkTypeId',
    'WindowInteractionState_Running', 'AnnotationType_Comment',
    'SupportedTextSelection_Single', 'UIA_SystemAlertEventId',
    'UIA_HostedFragmentRootsInvalidatedEventId',
    'RowOrColumnMajor_ColumnMajor',
    'ConnectionRecoveryBehaviorOptions',
    'UIA_IsExpandCollapsePatternAvailablePropertyId',
    'UIA_IsWindowPatternAvailablePropertyId',
    'UIA_ButtonControlTypeId', 'UIA_CustomNavigationPatternId',
    'UIA_GridItemColumnPropertyId', 'ToggleState',
    'UIA_CapStyleAttributeId', 'IUIAutomationItemContainerPattern',
    'IUIAutomation2', 'Assertive', 'UIA_RadioButtonControlTypeId',
    'UIA_ItemTypePropertyId', 'StructureChangeType_ChildAdded',
    'UIA_ToolBarControlTypeId', 'UIA_FullDescriptionPropertyId',
    'UIA_FlowsToPropertyId', 'NotificationKind_ItemRemoved',
    'UIA_IsEnabledPropertyId', 'CoalesceEventsOptions_Disabled',
    'UIA_AnnotationAnnotationTypeNamePropertyId',
    'NavigateDirection_PreviousSibling', 'UIA_FontNameAttributeId',
    'UIA_Selection2FirstSelectedItemPropertyId',
    'UIA_AnnotationObjectsAttributeId',
    'SynchronizedInputType_LeftMouseDown', 'HeadingLevel8',
    'NavigateDirection', 'UIA_MultipleViewPatternId',
    'UIA_IsInvokePatternAvailablePropertyId',
    'ProviderOptions_UseComThreading',
    'ProviderOptions_ClientSideProvider', 'UIA_CultureAttributeId',
    'IUIAutomationSelectionPattern',
    'UIA_TextEdit_TextChangedEventId',
    'UIA_RangeValueMaximumPropertyId', 'TextUnit_Line',
    'HeadingLevel6', 'UIA_ProcessIdPropertyId',
    'UIA_SelectionItem_ElementSelectedEventId',
    'IUIAutomationChangesEventHandler', 'UIA_TablePatternId',
    'UIA_WindowCanMaximizePropertyId', 'UIA_DragIsGrabbedPropertyId',
    'UIA_GridPatternId', 'UIA_Window_WindowOpenedEventId',
    'UIA_Drag_DragStartEventId', 'StyleId_Subtitle',
    'UIA_GridItemContainingGridPropertyId',
    'UIA_SelectionSelectionPropertyId', 'IUIAutomationBoolCondition',
    'UIA_CustomControlTypeId', 'UIA_MainLandmarkTypeId',
    'UIA_ItemStatusPropertyId', 'UIA_TextControlTypeId',
    'IUIAutomation4', 'UIA_Selection2ItemCountPropertyId',
    'UIA_IsSpreadsheetItemPatternAvailablePropertyId',
    'UIA_SelectionActiveEndAttributeId',
    'UIA_StructureChangedEventId', 'NavigateDirection_FirstChild',
    'IUIAutomationSelectionItemPattern',
    'AnnotationType_FormatChange',
    'StructureChangeType_ChildrenBulkAdded', 'UIA_TextPatternId',
    'UIA_MultipleViewSupportedViewsPropertyId', 'UIA_LevelPropertyId',
    'NotificationKind_ActionCompleted',
    'StructureChangeType_ChildRemoved',
    'IUIAutomationProxyFactoryMapping', 'UIA_DropTargetPatternId',
    'UIA_OutlineColorPropertyId', 'UIA_LinkAttributeId',
    'TextEditChangeType_AutoComplete',
    'UIA_IsSynchronizedInputPatternAvailablePropertyId',
    'UIA_VirtualizedItemPatternId',
    'IUIAutomationCustomNavigationPattern',
    'UIA_ProviderDescriptionPropertyId', 'StyleId_Heading1',
    'UIA_AnnotationDateTimePropertyId', 'UIA_OrientationPropertyId',
    'UIA_ControllerForPropertyId',
    'UIA_IsSelectionItemPatternAvailablePropertyId',
    'ProviderOptions_ProviderOwnsSetFocus', 'CUIAutomation8',
    'UIA_RangeValueLargeChangePropertyId',
    'UIA_LegacyIAccessibleHelpPropertyId',
    'UIA_Transform2ZoomMinimumPropertyId', 'IUIAutomation6',
    'AnnotationType_DataValidationError',
    'AnnotationType_InsertionChange', 'PropertyConditionFlags',
    'StyleId_Heading3', 'IUIAutomationGridItemPattern',
    'StyleId_Heading5',
    'UIA_IsItemContainerPatternAvailablePropertyId',
    'IUIAutomationTextEditTextChangedEventHandler',
    'IUIAutomationEventHandler',
    'UIA_IsAnnotationPatternAvailablePropertyId',
    'UIA_MarginBottomAttributeId', 'UIA_LineSpacingAttributeId',
    'UIA_UnderlineStyleAttributeId', 'PropertyConditionFlags_None',
    'UIA_IsTransformPattern2AvailablePropertyId',
    'NotificationProcessing_ImportantMostRecent',
    'UIA_ScrollHorizontalViewSizePropertyId',
    'UIA_OptimizeForVisualContentPropertyId',
    'NotificationProcessing_MostRecent',
    'AnnotationType_ExternalChange', 'UIA_TreeItemControlTypeId',
    'TextUnit_Page', 'UIA_UnderlineColorAttributeId',
    'IUIAutomationSpreadsheetItemPattern',
    'UIA_StatusBarControlTypeId', 'UIA_AnnotationTargetPropertyId',
    'UIA_SelectionItemSelectionContainerPropertyId',
    'UIA_RangeValueSmallChangePropertyId', 'IUIAutomationTextRange3',
    'ConnectionRecoveryBehaviorOptions_Enabled', 'StyleId_Normal',
    'UIA_WindowControlTypeId', 'UIA_LayoutInvalidatedEventId',
    'UIA_ScrollBarControlTypeId', 'UIA_DropTarget_DroppedEventId',
    'UIA_ActiveTextPositionChangedEventId', 'AnnotationType_Endnote',
    'AnnotationType_MoveChange',
    'UIA_LegacyIAccessibleSelectionPropertyId',
    'UIA_ThumbControlTypeId', 'AnnotationType_Footer',
    'SynchronizedInputType_LeftMouseUp',
    'UIA_IsGridPatternAvailablePropertyId',
    'UIA_StylesFillColorPropertyId', 'IUIAutomationPropertyCondition',
    'UIA_IndentationLeadingAttributeId',
    'UIA_InputReachedTargetEventId', 'TreeScope_Subtree',
    'IUIAutomationDropTargetPattern', 'UIA_StylesPatternId',
    'ZoomUnit_LargeDecrement', 'Off', 'UIA_ChangesEventId',
    'UIA_SpreadsheetItemFormulaPropertyId', 'UIA_AppBarControlTypeId',
    'UIA_IsPasswordPropertyId',
    'UIA_IsScrollItemPatternAvailablePropertyId',
    'TextUnit_Character', 'TreeScope_Element',
    'TreeTraversalOptions_PostOrder',
    'UIA_StylesFillPatternStylePropertyId',
    'UIA_TitleBarControlTypeId', 'SupportedTextSelection',
    'UIA_LiveSettingPropertyId', 'NotificationKind',
    'UIA_IsTextPattern2AvailablePropertyId',
    'IUIAutomationExpandCollapsePattern',
    'UIA_ClickablePointPropertyId',
    'UIA_IsLegacyIAccessiblePatternAvailablePropertyId',
    'UIA_DescribedByPropertyId', 'IUIAutomationElementArray',
    'UIA_DataItemControlTypeId',
    'UIA_LegacyIAccessibleValuePropertyId',
    'UIA_WindowWindowVisualStatePropertyId',
    'PropertyConditionFlags_IgnoreCase',
    'UIA_SpreadsheetItemAnnotationTypesPropertyId',
    'UIA_SelectionItem_ElementRemovedFromSelectionEventId',
    'UIA_IndentationTrailingAttributeId',
    'UIA_OverlineColorAttributeId', 'ExpandCollapseState_Collapsed',
    'IUIAutomationElement3', 'StyleId_Heading4',
    'UIA_InvokePatternId', 'AnnotationType_AdvancedProofingIssue',
    'UIA_AnimationStyleAttributeId', 'AnnotationType_TrackChanges',
    'ProviderOptions_OverrideProvider',
    'UIA_RangeValueMinimumPropertyId', 'UIA_HeaderControlTypeId',
    'UIA_ScrollVerticalViewSizePropertyId', 'OrientationType_None',
    'UIA_IsTextPatternAvailablePropertyId',
    'AnnotationType_Mathematics',
    'UIA_WindowWindowInteractionStatePropertyId',
    'UIA_MarginTrailingAttributeId', 'UIA_TabItemControlTypeId',
    'UIA_Transform2CanZoomPropertyId', 'HeadingLevel9',
    'StyleId_NumberedList', 'ZoomUnit_LargeIncrement',
    'AnnotationType_GrammarError', 'TextEditChangeType',
    'UIA_TextPattern2Id', 'IUIAutomationTextRange2',
    'UIA_Window_WindowClosedEventId', 'NavigateDirection_LastChild',
    'UIA_TextFlowDirectionsAttributeId', 'UIA_ScrollPatternId',
    'UIA_InputDiscardedEventId', 'UIA_DragPatternId',
    'UIA_IsObjectModelPatternAvailablePropertyId',
    'UIA_StylesExtendedPropertiesPropertyId', 'AutomationElementMode',
    'ProviderOptions_ServerSideProvider', 'IUIAutomationTablePattern',
    'AnnotationType_DeletionChange',
    'UIA_WindowCanMinimizePropertyId',
    'UIA_DropTarget_DragLeaveEventId', 'TreeTraversalOptions',
    'IUIAutomationTextChildPattern', 'IUIAutomationElement6',
    'UIA_Selection2LastSelectedItemPropertyId', 'HeadingLevel7',
    'UIA_SpreadsheetItemAnnotationObjectsPropertyId',
    'IUIAutomationSpreadsheetPattern', 'IUIAutomationTextRange',
    'NotificationProcessing_All', 'UiaChangeInfo',
    'UIA_LegacyIAccessibleDefaultActionPropertyId',
    'UIA_IsItalicAttributeId', 'DockPosition_Top',
    'UIA_SelectionCanSelectMultiplePropertyId', 'UIA_TabsAttributeId',
    'ExpandCollapseState_Expanded', 'UIA_SpinnerControlTypeId',
    'UIA_TableRowHeadersPropertyId',
    'UIA_Text_TextSelectionChangedEventId',
    'AnnotationType_Sensitive', 'WindowVisualState',
    'UIA_LocalizedControlTypePropertyId', 'UIA_IsOffscreenPropertyId',
    'UIA_SelectionPatternId', 'AutomationElementMode_None',
    'UIA_GroupControlTypeId', 'CUIAutomation',
    'NotificationKind_Other',
    'UIA_IsSpreadsheetPatternAvailablePropertyId',
    'UIA_HeadingLevelPropertyId',
    'UIA_IsTablePatternAvailablePropertyId',
    'UIA_DragGrabbedItemsPropertyId', 'UIA_WindowPatternId',
    'IUIAutomationTextEditPattern', 'UIA_IsSubscriptAttributeId',
    'UIA_SpreadsheetItemPatternId', 'UIA_StylesStyleIdPropertyId',
    'IRawElementProviderSimple', 'UIA_ValueIsReadOnlyPropertyId',
    'IUIAutomationLegacyIAccessiblePattern',
    'SynchronizedInputType_KeyUp', 'StyleId_Heading2',
    'UIA_IsDockPatternAvailablePropertyId',
    'TextPatternRangeEndpoint_End',
    'IUIAutomationFocusChangedEventHandler',
    'IUIAutomationEventHandlerGroup', 'UIA_GridItemRowPropertyId',
    'UIA_LegacyIAccessibleDescriptionPropertyId',
    'UIA_Invoke_InvokedEventId',
    'IUIAutomationVirtualizedItemPattern',
    'AnnotationType_SpellingError', 'ToggleState_On',
    'UIA_Selection_InvalidatedEventId', 'UIA_ScrollItemPatternId',
    'IUIAutomationCacheRequest',
    'UIA_HorizontalTextAlignmentAttributeId',
    'UIA_AcceleratorKeyPropertyId',
    'AnnotationType_ConflictingChange',
    'TreeTraversalOptions_LastToFirstOrder',
    'UIA_LegacyIAccessibleRolePropertyId',
    'UIA_SayAsInterpretAsAttributeId', 'UIA_TextEditPatternId',
    'UIA_TextEdit_ConversionTargetChangedEventId',
    'TextEditChangeType_Composition',
    'WindowInteractionState_ReadyForUserInteraction', 'HeadingLevel5',
    'UIA_IsDataValidForFormPropertyId',
    'UIA_ScrollHorizontallyScrollablePropertyId',
    'UIA_StyleNameAttributeId', 'UIA_FontWeightAttributeId',
    'UIA_CulturePropertyId', 'TextEditChangeType_AutoCorrect',
    'UIA_FillTypePropertyId', 'UIA_LiveRegionChangedEventId',
    'UIA_NativeWindowHandlePropertyId',
    'WindowInteractionState_Closing', 'UIA_OverlineStyleAttributeId',
    'StyleId_Emphasis', 'IUIAutomationScrollPattern',
    'UIA_TransformCanRotatePropertyId',
    'UIA_ProgressBarControlTypeId', 'TextUnit_Document',
    'IUIAutomationValuePattern',
    'UIA_IsScrollPatternAvailablePropertyId',
    'TextEditChangeType_CompositionFinalized',
    'UIA_IndentationFirstLineAttributeId', 'UIA_CustomLandmarkTypeId',
    'UIA_RotationPropertyId', 'UIA_SliderControlTypeId',
    'AnnotationType_Footnote', 'UIA_Text_TextChangedEventId',
    'UIA_OutlineThicknessPropertyId',
    'UIA_IsValuePatternAvailablePropertyId',
    'IUIAutomationWindowPattern', 'StyleId_Heading8',
    'IUIAutomationOrCondition', 'UIA_SemanticZoomControlTypeId',
    'ScrollAmount', 'NotificationKind_ItemAdded',
    'TextUnit_Paragraph',
    'UIA_ScrollHorizontalScrollPercentPropertyId',
    'DockPosition_None', 'ToggleState_Off',
    'UIA_ToolTipOpenedEventId',
    'IUIAutomationActiveTextPositionChangedEventHandler',
    'UIA_IsTogglePatternAvailablePropertyId',
    'UIA_DropTargetDropTargetEffectsPropertyId',
    'IUIAutomationGridPattern',
    'UIA_SelectionIsSelectionRequiredPropertyId',
    'UIA_ClassNamePropertyId', 'UIA_StylesStyleNamePropertyId',
    'UIA_DataGridControlTypeId',
    'UIA_Transform2ZoomMaximumPropertyId',
    'UIA_InputReachedOtherElementEventId', 'StyleId_Heading7',
    'UIA_FlowsFromPropertyId', 'UIA_AnnotationPatternId',
    'UIA_GridColumnCountPropertyId', 'IUIAutomationProxyFactory',
    'UIA_ItemContainerPatternId', 'TextUnit',
    'IUIAutomationTextPattern2', 'UIA_FormLandmarkTypeId',
    'UIA_ToolTipControlTypeId',
    'UIA_IsGridItemPatternAvailablePropertyId',
    'UIA_StylesFillPatternColorPropertyId',
    'OrientationType_Vertical', 'IAccessible',
    'UIA_MenuItemControlTypeId', 'HeadingLevel3',
    'ExpandCollapseState', 'WindowVisualState_Minimized',
    'WindowVisualState_Maximized', 'UIA_StylesShapePropertyId',
    'UIA_ToolTipClosedEventId', 'NotificationKind_ActionAborted',
    'IUIAutomationElement2', 'UIA_HyperlinkControlTypeId',
    'UIA_DockPatternId', 'TreeScope_None',
    'UIA_BulletStyleAttributeId',
    'ProviderOptions_HasNativeIAccessible', 'UIA_ImageControlTypeId',
    'UIA_TableItemPatternId', 'IUIAutomationMultipleViewPattern',
    'TextPatternRangeEndpoint_Start',
    'UIA_LegacyIAccessibleChildIdPropertyId',
    'UIA_TableColumnHeadersPropertyId', 'OrientationType_Horizontal',
    'UIA_PositionInSetPropertyId',
    'UIA_IsDropTargetPatternAvailablePropertyId',
    'UIA_BackgroundColorAttributeId', 'UIA_ComboBoxControlTypeId',
    'UIA_IsHiddenAttributeId', 'UIA_BoundingRectanglePropertyId',
    'IUIAutomationNotCondition', 'WindowInteractionState',
    'IUIAutomationElement8'
]

