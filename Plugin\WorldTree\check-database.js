/**
 * 检查数据库中的心理活动记录
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, '../AdvancedMemorySystem/data/emotion_memory.db');

console.log('🔍 检查数据库中的心理活动记录...');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ 数据库连接失败:', err.message);
        return;
    }
    console.log('✅ 数据库连接成功');
});

// 检查memory_fragments表结构
db.all("PRAGMA table_info(memory_fragments)", (err, rows) => {
    if (err) {
        console.error('❌ 获取表结构失败:', err.message);
        return;
    }
    
    console.log('\n📋 memory_fragments表结构:');
    rows.forEach(row => {
        console.log(`  ${row.name}: ${row.type}`);
    });
});

// 检查心理活动记录数量
db.get("SELECT COUNT(*) as count FROM memory_fragments WHERE memory_type = 'psychology_activity'", (err, row) => {
    if (err) {
        console.error('❌ 查询心理活动记录失败:', err.message);
        return;
    }
    
    console.log(`\n📊 心理活动记录数量: ${row.count}`);
});

// 获取最近的心理活动记录
db.all(`
    SELECT 
        user_id,
        persona_name,
        content,
        ai_summary,
        creation_time,
        emotional_context
    FROM memory_fragments 
    WHERE memory_type = 'psychology_activity'
    ORDER BY creation_time DESC 
    LIMIT 5
`, (err, rows) => {
    if (err) {
        console.error('❌ 获取心理活动记录失败:', err.message);
        return;
    }
    
    console.log(`\n📝 最近的心理活动记录 (${rows.length}条):`);
    rows.forEach((row, index) => {
        console.log(`\n${index + 1}. Agent: ${row.persona_name}`);
        console.log(`   用户: ${row.user_id}`);
        console.log(`   内容: ${row.content}`);
        console.log(`   摘要: ${row.ai_summary}`);
        console.log(`   时间: ${row.creation_time}`);
        console.log(`   情感: ${row.emotional_context}`);
    });
    
    // 关闭数据库连接
    db.close((err) => {
        if (err) {
            console.error('❌ 数据库关闭失败:', err.message);
        } else {
            console.log('\n✅ 数据库连接已关闭');
        }
    });
});
