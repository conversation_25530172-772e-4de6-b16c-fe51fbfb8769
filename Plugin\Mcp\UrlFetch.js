// Plugin/Mcp/UrlFetch.js - URL获取MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}


class UrlFetchMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'UrlFetch';
        this.description = '获取网页内容和信息';
        this.vcpName = 'UrlFetch';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: '要获取的网页URL'
                },
                method: {
                    type: 'string',
                    description: 'HTTP请求方法',
                    enum: ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS'],
                    default: 'GET'
                },
                headers: {
                    type: 'object',
                    description: '请求头',
                    additionalProperties: {
                        type: 'string'
                    },
                    default: {}
                },
                body: {
                    type: 'string',
                    description: '请求体数据',
                    default: ''
                },
                timeout: {
                    type: 'number',
                    description: '请求超时时间（毫秒）',
                    minimum: 1000,
                    maximum: 30000,
                    default: 10000
                },
                extract_type: {
                    type: 'string',
                    description: '内容提取类型',
                    enum: ['text', 'html', 'json', 'raw'],
                    default: 'text'
                }
            },
            required: ['url']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);
        
        this.log('info', `开始获取网页内容`, {
            url: args.url,
            method: args.method || 'GET'
        });
        
        try {
        // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);
            
            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }
            
            // 构建标准响应格式
            const response = {
                type: 'url_fetch',
                status: 'success',
                message: '网页内容获取完成',
                data: {
                    url: args.url,
                    method: args.method || 'GET',
                    status_code: parsedResult?.status_code || 200,
                    content_type: parsedResult?.content_type,
                    content: parsedResult?.content,
                    content_length: parsedResult?.content?.length || 0,
                    headers: parsedResult?.headers || {},
                    timing: {
                        start_time: parsedResult?.timing?.start_time,
                        end_time: parsedResult?.timing?.end_time,
                        duration: parsedResult?.timing?.duration
                    }
                }
            };
        
            this.log('success', `网页内容获取完成`, {
                url: args.url,
                status_code: response.data.status_code,
                content_length: response.data.content_length
            });
        
            return response;
            
        } catch (error) {
            const errorResponse = {
            type: 'url_fetch',
                status: 'error',
                message: error.message,
                data: {
                    url: args.url,
                    error: error.message
                }
            };
            
            this.log('error', `网页内容获取失败`, errorResponse);
            throw errorResponse;
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查必要的环境变量（如果有的话）
            const requiredEnvVars = [];
            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    throw new Error(`未配置${envVar}环境变量`);
                }
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = UrlFetchMcp; 