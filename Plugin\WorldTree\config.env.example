# 世界树VCP插件配置文件示例
# 复制此文件为 config.env 并根据需要修改配置

# 基础配置
WORLDTREE_ENABLED=true
WORLDTREE_USE_LOCAL_ALGORITHM=true

# API配置（可选，如果不使用本地算法）
WORLDTREE_API_URL=
WORLDTREE_API_KEY=
WORLDTREE_MODEL=gpt-4o-mini

# 心理状态更新配置
WORLDTREE_PSYCHOLOGY_UPDATE_INTERVAL=300000

# 超时配置
WORLDTREE_TIMEOUT=120000

# 数据库配置（自动使用AdvancedMemorySystem的数据库）
# 无需手动配置数据库路径

# 算法参数配置
WORLDTREE_STRESS_WEIGHT=0.3
WORLDTREE_EMOTION_WEIGHT=0.25
WORLDTREE_ENERGY_WEIGHT=0.2
WORLDTREE_MOOD_WEIGHT=0.15
WORLDTREE_FOCUS_WEIGHT=0.1

# 时间因素配置
WORLDTREE_MORNING_ENERGY_FACTOR=1.2
WORLDTREE_AFTERNOON_ENERGY_FACTOR=0.9
WORLDTREE_EVENING_ENERGY_FACTOR=0.7
WORLDTREE_NIGHT_ENERGY_FACTOR=0.5

# 日志配置
WORLDTREE_DEBUG_MODE=false
WORLDTREE_LOG_LEVEL=info
