/**
 * 对话记录管理模块（优化版 v2）
 * 记录用户问题和AI反馈，按用户分文件夹存储，单独文件记录
 * 支持动态上下文检索和北京时间格式
 */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const crypto = require('crypto');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? './logger.cjs' 
        : './logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

class ConversationLogger {
    constructor(options = {}) {
        this.dataDir = options.dataDir || path.join(__dirname, '../Database/conversations');
        this.segmenter = null;
        
        // 确保目录存在
        this.ensureDirectories();
        
        // 初始化分词器
        this.initSegmenter();
    }

    /**
     * 获取北京时间
     */
    getBeijingTime(date = null) {
        const now = date || new Date();
        // 北京时间 UTC+8
        const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
        return {
            iso: beijingTime.toISOString(),
            formatted: beijingTime.toISOString().replace('T', ' ').replace('Z', ' (北京时间)'),
            timestamp: now.getTime(), // 保持UTC时间戳便于比较
            readable: beijingTime.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Shanghai'
            })
        };
    }

    /**
     * 确保所有必要的目录存在
     */
    ensureDirectories() {
        if (!fsSync.existsSync(this.dataDir)) {
            fsSync.mkdirSync(this.dataDir, { recursive: true });
            logger.debug(`对话记录目录已创建: ${this.dataDir}`);
        }
    }

    /**
     * 为用户创建专属目录
     */
    ensureUserDirectory(userId) {
        // 生成安全的目录名（移除Windows不允许的字符）
        const safeUserId = this.sanitizeFileName(userId);
        const userDir = path.join(this.dataDir, safeUserId);
        const dirs = [
            userDir,
            path.join(userDir, 'conversations'),
            path.join(userDir, 'keywords'),
            path.join(userDir, 'statistics')
        ];

        dirs.forEach(dir => {
            if (!fsSync.existsSync(dir)) {
                fsSync.mkdirSync(dir, { recursive: true });
            }
        });

        return {
            userDir,
            conversationsDir: dirs[1],
            keywordsDir: dirs[2],
            statisticsDir: dirs[3]
        };
    }

    /**
     * 清理文件名，移除不安全的字符
     */
    sanitizeFileName(fileName) {
        // 移除Windows文件系统不允许的字符: < > : " / \ | ? *
        // 同时移除方括号和其他可能有问题的字符
        return fileName.replace(/[<>:"/\\|?*\[\]]/g, '_');
    }

    /**
     * 初始化分词器
     */
    initSegmenter() {
        try {
            // 尝试加载 nodejieba 分词器
            const nodejieba = require('nodejieba');
            this.segmenter = {
                type: 'nodejieba',
                instance: nodejieba,
                segment: (text) => this.preprocessAndSegment(text, nodejieba.cut),
                extract: (text, topN = 10) => nodejieba.extract(text, topN)
            };
            logger.debug('nodejieba 分词器初始化成功');
        } catch (error) {
            try {
                // 备用方案：使用 segment 分词器
                const Segment = require('segment');
                const segment = new Segment();
                segment.useDefault(); // 使用默认的识别模块和字典
                
                this.segmenter = {
                    type: 'segment',
                    instance: segment,
                    segment: (text) => this.preprocessAndSegment(text, (t) => segment.doSegment(t).map(item => item.w)),
                    extract: (text, topN = 10) => this.extractKeywordsFromSegment(text, topN)
                };
                logger.debug('segment 分词器初始化成功');
            } catch (segmentError) {
                // 最后备用方案：简单分词
                this.segmenter = {
                    type: 'simple',
                    instance: null,
                    segment: (text) => this.preprocessAndSegment(text, this.simpleSegment.bind(this)),
                    extract: (text, topN = 10) => this.simpleExtractKeywords(text, topN)
                };
                logger.debug('使用简单分词器（建议安装 nodejieba 或 segment）');
            }
        }
    }

    /**
     * 预处理文本，保护URL、邮箱等特殊内容
     */
    preprocessAndSegment(text, segmentFunction) {
        if (!text || typeof text !== 'string') {
            return [];
        }

        // 定义需要保护的模式
        const patterns = [
            // HTTP/HTTPS链接
            /https?:\/\/[^\s\u4e00-\u9fa5]+/g,
            // WebSocket链接
            /wss?:\/\/[^\s\u4e00-\u9fa5]+/g,
            // 邮箱地址
            /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
            // IP地址
            /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g,
            // 文件路径（Windows和Unix）
            /[a-zA-Z]:[\\\/][^\s\u4e00-\u9fa5]*|\/[^\s\u4e00-\u9fa5]+/g,
            // 版本号
            /v?\d+\.\d+(?:\.\d+)?(?:-[a-zA-Z0-9]+)?/g,
            // 代码块
            /```[\s\S]*?```|`[^`]+`/g,
            // UUID
            /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi,
            // 长哈希值
            /[a-fA-F0-9]{32,}/g
        ];

        // 提取所有需要保护的内容
        const protectedItems = [];
        let workingText = text;

        patterns.forEach((pattern, patternIndex) => {
            const matches = Array.from(workingText.matchAll(pattern));
            matches.forEach((match, matchIndex) => {
                const placeholder = `__PROTECTED_${patternIndex}_${matchIndex}__`;
                protectedItems.push({
                    placeholder,
                    original: match[0],
                    start: match.index,
                    end: match.index + match[0].length
                });
            });
        });

        // 按位置排序，从后往前替换避免位置偏移
        protectedItems.sort((a, b) => b.start - a.start);
        
        protectedItems.forEach(item => {
            workingText = workingText.substring(0, item.start) + 
                         ` ${item.placeholder} ` + 
                         workingText.substring(item.end);
        });

        // 对处理后的文本进行分词
        let segments = segmentFunction(workingText);

        // 还原保护的内容
        segments = segments.map(segment => {
            const trimmed = segment.trim();
            const protectedItem = protectedItems.find(item => item.placeholder === trimmed);
            return protectedItem ? protectedItem.original : segment;
        });

        // 过滤空白和无意义的片段
        segments = segments.filter(segment => {
            return segment && 
                   segment.trim().length > 0 && 
                   !/^[_\-\s\u3000]+$/.test(segment) && // 过滤纯符号
                   segment.length < 500; // 过滤异常长的片段
        });

        return segments;
    }

    /**
     * 简单分词实现
     */
    simpleSegment(text) {
        // 简单的中文分词：按标点和空格分割，过滤短词
        return text
            .replace(/[，。！？；：""''（）【】《》\s]+/g, ' ')
            .split(/\s+/)
            .filter(word => word.length >= 2)
            .filter(Boolean);
    }

    /**
     * 简单关键词提取
     */
    simpleExtractKeywords(text, topN = 10) {
        const words = this.preprocessAndSegment(text, this.simpleSegment.bind(this));
        const wordCount = {};
        
        // 过滤停用词
        const stopWords = new Set(['这个', '那个', '什么', '怎么', '为什么', '因为', '所以', '但是', '然后', '现在', '可以', '应该', '需要', '我们', '他们', '她们', '我的', '你的', '他的']);
        
        words.forEach(word => {
            if (!stopWords.has(word) && word.length >= 2) {
                wordCount[word] = (wordCount[word] || 0) + 1;
            }
        });
        
        return Object.entries(wordCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, topN)
            .map(([word, weight]) => ({ word, weight }));
    }

    /**
     * 从segment分词结果提取关键词
     */
    extractKeywordsFromSegment(text, topN = 10) {
        const segment = this.segmenter.instance;
        const words = segment.doSegment(text);
        
        // 只保留名词、动词、形容词
        const meaningfulWords = words.filter(item => 
            ['n', 'v', 'a', 'nr', 'ns', 'nt', 'nz'].includes(item.p)
        );
        
        const wordCount = {};
        meaningfulWords.forEach(item => {
            if (item.w.length >= 2) {
                wordCount[item.w] = (wordCount[item.w] || 0) + 1;
            }
        });
        
        return Object.entries(wordCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, topN)
            .map(([word, weight]) => ({ word, weight }));
    }

    /**
     * 获取相关上下文
     * @param {string} userId - 用户ID
     * @param {string} userMessage - 用户消息
     * @param {Object} options - 配置选项
     * @param {number} [options.maxContextSize=15] - 最大上下文数量
     * @param {number} [options.maxResults=15] - 每种类型（关键词/最近）的最大结果数
     * @returns {Promise<Array>} 相关上下文数组
     */
    async getRelevantContext(userId, userMessage, options = {}) {
        const { 
            maxContextSize = 15,  // 新增参数：最大上下文数量
            maxResults = 15       // 保持原有参数：每种类型的最大结果数
        } = options;

        try {
            const userDirs = this.ensureUserDirectory(userId);
            const conversationsDir = userDirs.conversationsDir;
            
            if (!fsSync.existsSync(conversationsDir)) {
                return [];
            }

            // 对用户当前问题进行分词
            const currentKeywords = this.segmenter.extract(userMessage, 20);
            const currentWords = new Set(currentKeywords.map(k => k.word.toLowerCase()));

            // 读取所有对话记录
            const files = await fs.readdir(conversationsDir);
            const conversations = [];
            
            for (const file of files) {
                if (!file.endsWith('.json')) continue;
                
                try {
                    const filePath = path.join(conversationsDir, file);
                    const content = await fs.readFile(filePath, 'utf8');
                    const record = JSON.parse(content);
                    conversations.push(record);
                } catch (error) {
                    console.warn(`读取对话文件失败: ${file}`, error.message);
                }
            }

            // 计算相关性分数
            const scoredConversations = conversations.map(conv => {
                let relevanceScore = 0;
                
                // 基于关键词相似度计算分数
                const allKeywords = [
                    ...(conv.userMessage.keywords || []),
                    ...(conv.aiResponse.keywords || []),
                    ...(conv.analysis.combinedKeywords || [])
                ];
                
                allKeywords.forEach(keyword => {
                    if (currentWords.has(keyword.word.toLowerCase())) {
                        relevanceScore += keyword.weight || 1;
                    }
                });

                // 时间衰减：越新的对话权重越高
                const ageInDays = (Date.now() - conv.timestampMs) / (1000 * 60 * 60 * 24);
                const timeDecay = Math.exp(-ageInDays / 30); // 30天半衰期
                
                return {
                    ...conv,
                    relevanceScore: relevanceScore * timeDecay,
                    rawRelevance: relevanceScore,
                    timeDecay: timeDecay
                };
            });

            // 按相关性排序，关键词优先
            scoredConversations.sort((a, b) => {
                if (Math.abs(a.relevanceScore - b.relevanceScore) < 0.1) {
                    // 相关性接近时，按时间排序
                    return b.timestampMs - a.timestampMs;
                }
                return b.relevanceScore - a.relevanceScore;
            });

            // 获取最相关的记录
            const relevantByKeywords = scoredConversations
                .filter(conv => conv.rawRelevance > 0)
                .slice(0, maxResults);

            // 获取最近的记录
            const recentConversations = conversations
                .sort((a, b) => b.timestampMs - a.timestampMs)
                .slice(0, maxResults);

            // 去重合并，关键词优先
            const combinedMap = new Map();
            
            // 先添加关键词相关的
            relevantByKeywords.forEach(conv => {
                combinedMap.set(conv.id, { ...conv, source: 'keyword' });
            });

            // 再添加时间相关的（不覆盖已有的）
            recentConversations.forEach(conv => {
                if (!combinedMap.has(conv.id)) {
                    combinedMap.set(conv.id, { ...conv, source: 'recent' });
                }
            });

            // 转换为数组并限制数量为maxContextSize
            const finalResults = Array.from(combinedMap.values())
                .slice(0, maxContextSize)  // 使用maxContextSize参数限制最终结果数量
                .map(conv => ({
                    id: conv.id,
                    timestamp: conv.timestamp,
                    userMessage: conv.userMessage.content,
                    aiResponse: conv.aiResponse.content,
                    relevanceScore: conv.relevanceScore || 0,
                    source: conv.source,
                    topics: conv.analysis.topics || []
                }));

            logger.debug(`为用户 ${userId} 找到 ${finalResults.length} 条相关上下文（上限：${maxContextSize}条）`);
            
            return finalResults;

        } catch (error) {
            console.error('获取相关上下文失败:', error.message);
            return [];
        }
    }

    /**
     * 格式化上下文为system消息
     * @param {Array} context - 上下文数组
     * @param {string} userMessage - 用户消息
     * @param {Object} options - 配置选项
     * @param {number} [options.maxContextSize] - 最大上下文数量限制
     * @returns {string} 格式化后的system消息
     */
    formatContextForSystem(context, userMessage, options = {}) {
        if (!context || context.length === 0) {
            return '';
        }

        const { maxContextSize } = options;
        const contextText = context.map((item, index) => {
            const timeStr = new Date(item.timestamp).toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            const topics = item.topics.map(t => t.topic).join(', ');
            const topicsStr = topics ? ` [话题: ${topics}]` : '';
            
            return `${index + 1}. 【${timeStr}】${topicsStr}
用户: ${item.userMessage.substring(0, 100)}${item.userMessage.length > 100 ? '...' : ''}
助手: ${item.aiResponse.substring(0, 100)}${item.aiResponse.length > 100 ? '...' : ''}`;
        }).join('\n\n');

        const limitInfo = maxContextSize ? ` (已限制最多${maxContextSize}条)` : '';
        return `\n\n--- 相关对话历史 (${context.length}条${limitInfo}) ---\n${contextText}\n--- 历史结束 ---\n\n基于以上历史对话，请更好地理解用户需求并提供相关回答。`;
    }

    /**
     * 记录对话
     */
    async logConversation(data) {
        const {
            userId = 'anonymous',
            userMessage,
            aiResponse,
            model,
            type = 'chat',
            imageUrl = null,
            metadata = {}
        } = data;

        try {
            const conversationId = this.generateConversationId();
            const timeInfo = this.getBeijingTime();
            
            // 创建用户目录结构
            const userDirs = this.ensureUserDirectory(userId);
            
            // 分词处理
            const userSegments = userMessage ? this.segmenter.segment(userMessage) : [];
            const aiSegments = aiResponse ? this.segmenter.segment(aiResponse) : [];
            
            // 提取关键词
            const userKeywords = userMessage ? this.segmenter.extract(userMessage, 10) : [];
            const aiKeywords = aiResponse ? this.segmenter.extract(aiResponse, 10) : [];
            const combinedKeywords = this.combineKeywords(userKeywords, aiKeywords);

            // 构建记录数据
            const conversationRecord = {
                id: conversationId,
                userId: userId,
                timestamp: timeInfo.iso,
                timestampMs: timeInfo.timestamp,
                beijingTime: timeInfo.formatted,
                readableTime: timeInfo.readable,
                model: model,
                type: type,
                userMessage: {
                    content: userMessage,
                    segments: userSegments,
                    keywords: userKeywords,
                    length: userMessage ? userMessage.length : 0,
                    characterCount: userMessage ? this.countCharacters(userMessage) : { chinese: 0, english: 0, number: 0, other: 0 }
                },
                aiResponse: {
                    content: aiResponse,
                    segments: aiSegments,
                    keywords: aiKeywords,
                    length: aiResponse ? aiResponse.length : 0,
                    characterCount: aiResponse ? this.countCharacters(aiResponse) : { chinese: 0, english: 0, number: 0, other: 0 }
                },
                analysis: {
                    combinedKeywords: combinedKeywords,
                    totalWords: userSegments.length + aiSegments.length,
                    conversationLength: (userMessage?.length || 0) + (aiResponse?.length || 0),
                    topics: this.extractTopics(combinedKeywords),
                    sentiment: this.analyzeSentiment(userMessage, aiResponse)
                },
                imageUrl: imageUrl,
                metadata: {
                    ...metadata,
                    segmenterType: this.segmenter.type,
                    ip: metadata.ip || 'unknown',
                    userAgent: metadata.userAgent || 'unknown'
                }
            };

            // 保存单个对话记录文件
            await this.saveConversationRecord(userDirs, conversationRecord);
            
            // 更新用户关键词索引
            await this.updateUserKeywordIndex(userDirs, combinedKeywords, conversationId, timeInfo);
            
            // 更新用户统计
            await this.updateUserStats(userDirs, conversationRecord);

            logger.debug(`对话记录已保存: ${conversationId} (用户: ${userId}) - ${timeInfo.readable}`);
            
            return {
                success: true,
                conversationId: conversationId,
                keywords: combinedKeywords,
                analysis: conversationRecord.analysis
            };

        } catch (error) {
            console.error('❌ 对话记录保存失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 生成对话ID
     */
    generateConversationId() {
        const timestamp = Date.now();
        const random = crypto.randomBytes(4).toString('hex');
        return `conv_${timestamp}_${random}`;
    }

    /**
     * 统计字符类型
     */
    countCharacters(text) {
        const result = { chinese: 0, english: 0, number: 0, other: 0 };
        
        for (const char of text) {
            if (/[\u4e00-\u9fa5]/.test(char)) {
                result.chinese++;
            } else if (/[a-zA-Z]/.test(char)) {
                result.english++;
            } else if (/[0-9]/.test(char)) {
                result.number++;
            } else {
                result.other++;
            }
        }
        
        return result;
    }

    /**
     * 合并关键词
     */
    combineKeywords(userKeywords, aiKeywords) {
        const combined = {};
        
        userKeywords.forEach(item => {
            combined[item.word] = (combined[item.word] || 0) + item.weight * 1.2; // 用户关键词权重稍高
        });
        
        aiKeywords.forEach(item => {
            combined[item.word] = (combined[item.word] || 0) + item.weight;
        });
        
        return Object.entries(combined)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 15)
            .map(([word, weight]) => ({ word, weight: Math.round(weight * 100) / 100 }));
    }

    /**
     * 提取话题
     */
    extractTopics(keywords) {
        // 基于关键词推断话题
        const topicKeywords = {
            '技术': ['代码', '编程', '算法', '数据', '系统', '开发', '软件', '技术', 'AI', '人工智能', '机器学习', 'API', 'HTTP', 'URL', 'WebSocket'],
            '学习': ['学习', '教育', '知识', '课程', '考试', '研究', '理解', '掌握', '练习'],
            '生活': ['生活', '健康', '食物', '运动', '旅行', '家庭', '朋友', '工作', '娱乐'],
            '商业': ['商业', '市场', '营销', '管理', '财务', '投资', '创业', '公司', '产品'],
            '科学': ['科学', '物理', '化学', '生物', '数学', '实验', '研究', '发现', '理论']
        };
        
        const topicScores = {};
        
        keywords.forEach(({ word, weight }) => {
            Object.entries(topicKeywords).forEach(([topic, words]) => {
                if (words.some(tw => word.includes(tw) || tw.includes(word))) {
                    topicScores[topic] = (topicScores[topic] || 0) + weight;
                }
            });
        });
        
        return Object.entries(topicScores)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([topic, score]) => ({ topic, score }));
    }

    /**
     * 情感分析（简单实现）
     */
    analyzeSentiment(userMessage, aiResponse) {
        const positiveWords = ['好', '很好', '棒', '优秀', '满意', '喜欢', '开心', '高兴', '感谢', '谢谢'];
        const negativeWords = ['不好', '差', '糟糕', '失望', '生气', '难过', '问题', '错误', '失败'];
        
        let userSentiment = 0;
        let aiSentiment = 0;
        
        if (userMessage) {
            positiveWords.forEach(word => {
                if (userMessage.includes(word)) userSentiment += 1;
            });
            negativeWords.forEach(word => {
                if (userMessage.includes(word)) userSentiment -= 1;
            });
        }
        
        if (aiResponse) {
            positiveWords.forEach(word => {
                if (aiResponse.includes(word)) aiSentiment += 1;
            });
            negativeWords.forEach(word => {
                if (aiResponse.includes(word)) aiSentiment -= 1;
            });
        }
        
        return {
            user: userSentiment > 0 ? 'positive' : userSentiment < 0 ? 'negative' : 'neutral',
            ai: aiSentiment > 0 ? 'positive' : aiSentiment < 0 ? 'negative' : 'neutral',
            userScore: userSentiment,
            aiScore: aiSentiment
        };
    }

    /**
     * 保存单个对话记录文件
     */
    async saveConversationRecord(userDirs, record) {
        const fileName = `${record.id}.json`;
        const filePath = path.join(userDirs.conversationsDir, fileName);
        
        await fs.writeFile(filePath, JSON.stringify(record, null, 2), 'utf8');
    }

    /**
     * 更新用户关键词索引
     */
    async updateUserKeywordIndex(userDirs, keywords, conversationId, timeInfo) {
        const keywordIndexFile = path.join(userDirs.keywordsDir, 'keyword_index.json');
        
        let keywordIndex = {};
        try {
            if (fsSync.existsSync(keywordIndexFile)) {
                const content = await fs.readFile(keywordIndexFile, 'utf8');
                keywordIndex = JSON.parse(content);
            }
        } catch (error) {
            console.warn('读取用户关键词索引失败，创建新索引:', error.message);
        }
        
        keywords.forEach(({ word, weight }) => {
            if (!keywordIndex[word]) {
                keywordIndex[word] = {
                    totalWeight: 0,
                    conversations: [],
                    firstSeen: timeInfo.formatted,
                    lastSeen: timeInfo.formatted,
                    firstSeenTimestamp: timeInfo.timestamp,
                    lastSeenTimestamp: timeInfo.timestamp
                };
            }
            
            keywordIndex[word].totalWeight += weight;
            keywordIndex[word].conversations.push({
                id: conversationId,
                weight: weight,
                timestamp: timeInfo.formatted,
                timestampMs: timeInfo.timestamp
            });
            keywordIndex[word].lastSeen = timeInfo.formatted;
            keywordIndex[word].lastSeenTimestamp = timeInfo.timestamp;
            
            // 只保留最近30次对话
            if (keywordIndex[word].conversations.length > 30) {
                keywordIndex[word].conversations = keywordIndex[word].conversations.slice(-30);
            }
        });
        
        await fs.writeFile(keywordIndexFile, JSON.stringify(keywordIndex, null, 2), 'utf8');
    }

    /**
     * 更新用户统计
     */
    async updateUserStats(userDirs, record) {
        const userStatsFile = path.join(userDirs.statisticsDir, 'user_stats.json');
        
        let userStats = {
            userId: record.userId,
            totalConversations: 0,
            totalWords: 0,
            totalCharacters: 0,
            firstConversation: record.timestamp,
            lastConversation: record.timestamp,
            firstConversationReadable: record.readableTime,
            lastConversationReadable: record.readableTime,
            favoriteTopics: {},
            mostUsedModels: {},
            conversationTypes: {},
            averageResponseLength: 0,
            conversationsByDate: {},
            lastUpdated: record.timestamp,
            lastUpdatedReadable: record.readableTime
        };
        
        try {
            if (fsSync.existsSync(userStatsFile)) {
                const content = await fs.readFile(userStatsFile, 'utf8');
                userStats = { ...userStats, ...JSON.parse(content) };
            }
        } catch (error) {
            console.warn('读取用户统计失败，使用默认值:', error.message);
        }
        
        // 更新统计数据
        userStats.totalConversations++;
        userStats.totalWords += record.analysis.totalWords;
        userStats.totalCharacters += record.analysis.conversationLength;
        userStats.lastConversation = record.timestamp;
        userStats.lastConversationReadable = record.readableTime;
        userStats.lastUpdated = record.timestamp;
        userStats.lastUpdatedReadable = record.readableTime;
        
        // 更新话题统计
        record.analysis.topics.forEach(({ topic, score }) => {
            userStats.favoriteTopics[topic] = (userStats.favoriteTopics[topic] || 0) + score;
        });
        
        // 更新模型使用统计
        userStats.mostUsedModels[record.model] = (userStats.mostUsedModels[record.model] || 0) + 1;
        
        // 更新对话类型统计
        userStats.conversationTypes[record.type] = (userStats.conversationTypes[record.type] || 0) + 1;
        
        // 更新平均回复长度
        userStats.averageResponseLength = Math.round(userStats.totalCharacters / userStats.totalConversations);
        
        // 更新日期统计
        const dateKey = record.timestamp.split('T')[0];
        userStats.conversationsByDate[dateKey] = (userStats.conversationsByDate[dateKey] || 0) + 1;
        
        await fs.writeFile(userStatsFile, JSON.stringify(userStats, null, 2), 'utf8');
    }

    /**
     * 获取用户统计
     */
    async getUserStats(userId) {
        const userDirs = this.ensureUserDirectory(userId);
        const userStatsFile = path.join(userDirs.statisticsDir, 'user_stats.json');
        
        try {
            if (fsSync.existsSync(userStatsFile)) {
                const content = await fs.readFile(userStatsFile, 'utf8');
                return JSON.parse(content);
            }
        } catch (error) {
            console.error('获取用户统计失败:', error.message);
        }
        
        return null;
    }

    /**
     * 搜索用户对话
     */
    async searchUserConversations(userId, query, options = {}) {
        const {
            dateFrom = null,
            dateTo = null,
            type = null,
            limit = 50
        } = options;

        try {
            const userDirs = this.ensureUserDirectory(userId);
            const conversationsDir = userDirs.conversationsDir;
            
            if (!fsSync.existsSync(conversationsDir)) {
                return [];
            }

            const files = await fs.readdir(conversationsDir);
            const results = [];
            
            for (const file of files) {
                if (!file.endsWith('.json')) continue;
                
                try {
                    const filePath = path.join(conversationsDir, file);
                    const content = await fs.readFile(filePath, 'utf8');
                    const record = JSON.parse(content);
                    
                    // 过滤条件检查
                    if (type && record.type !== type) continue;
                    if (dateFrom && record.timestamp < dateFrom) continue;
                    if (dateTo && record.timestamp > dateTo) continue;
                    
                    // 内容搜索
                    const contentToSearch = (record.userMessage.content + ' ' + record.aiResponse.content).toLowerCase();
                    if (contentToSearch.includes(query.toLowerCase())) {
                        results.push(record);
                    }
                    
                    if (results.length >= limit) break;
                } catch (error) {
                    console.warn(`读取对话文件失败: ${file}`, error.message);
                }
            }
            
            // 按时间排序（最新的在前）
            return results.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            
        } catch (error) {
            console.error('搜索用户对话失败:', error.message);
            return [];
        }
    }

    /**
     * 获取用户对话列表
     */
    async getUserConversations(userId, options = {}) {
        const {
            limit = 20,
            offset = 0,
            type = null
        } = options;

        try {
            const userDirs = this.ensureUserDirectory(userId);
            const conversationsDir = userDirs.conversationsDir;
            
            if (!fsSync.existsSync(conversationsDir)) {
                return [];
            }

            const files = await fs.readdir(conversationsDir);
            const conversations = [];
            
            for (const file of files) {
                if (!file.endsWith('.json')) continue;
                
                try {
                    const filePath = path.join(conversationsDir, file);
                    const content = await fs.readFile(filePath, 'utf8');
                    const record = JSON.parse(content);
                    
                    if (!type || record.type === type) {
                        conversations.push(record);
                    }
                } catch (error) {
                    console.warn(`读取对话文件失败: ${file}`, error.message);
                }
            }
            
            // 按时间排序并分页
            const sorted = conversations.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            return sorted.slice(offset, offset + limit);
            
        } catch (error) {
            console.error('获取用户对话列表失败:', error.message);
            return [];
        }
    }
}

module.exports = ConversationLogger; 