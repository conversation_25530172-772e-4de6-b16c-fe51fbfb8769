{"name": "onebot11-adapter", "version": "1.0.0", "description": "OneBot11适配器 - 连接到VCPToolBox系统的OneBot11机器人适配器", "main": "bot.js", "scripts": {"start": "node bot.js", "dev": "node --inspect bot.js", "test": "echo \"Error: no test specified\" && exit 1", "install-deps": "npm install", "clean": "rm -rf node_modules package-lock.json", "reinstall": "npm run clean && npm install"}, "keywords": ["onebot11", "chatbot", "qq", "adapter", "vcptoolbox", "websocket"], "author": "VCPToolBox Team", "license": "MIT", "dependencies": {"ws": "^8.14.2", "axios": "^1.6.0", "express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "multer": "^1.4.5-lts.1", "fs-extra": "^11.1.1", "path": "^0.12.7", "crypto": "^1.0.1", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "chalk": "^4.1.2", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.1", "eslint": "^8.52.0", "prettier": "^3.0.3"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/vcptoolbox/onebot11-adapter.git"}, "bugs": {"url": "https://github.com/vcptoolbox/onebot11-adapter/issues"}, "homepage": "https://github.com/vcptoolbox/onebot11-adapter#readme"}