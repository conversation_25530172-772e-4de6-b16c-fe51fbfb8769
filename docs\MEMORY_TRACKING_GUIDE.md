# Memory Tracking 对话记录功能指南（优化版）

## 概述

Memory Tracking 是一个智能对话记录系统，支持按用户分文件夹存储、智能分词、关键词提取和话题分析。每次对话都会记录到单独的文件中，便于管理和查看。

## 优化特性

### 📁 按用户分文件夹存储
- 每个用户有独立的存储空间
- 对话、关键词、统计数据分别管理
- 单文件存储，不会无限叠加

### 🔧 智能分词优化
- 保护URL、邮箱、文件路径等特殊内容完整性
- 支持HTTP/HTTPS链接：`https://api.openai.com/v1/chat/completions`
- 支持WebSocket链接：`wss://example.com/websocket`
- 支持邮箱地址：`<EMAIL>`
- 支持文件路径：`C:\Users\<USER>\Documents\project.json`
- 支持版本号：`v1.2.3`
- 支持UUID：`123e4567-e89b-12d3-a456-************`
- 支持哈希值：`a1b2c3d4e5f6789012345678901234567890abcdef`

### 📊 全面的数据分析
- 智能关键词提取
- 话题识别和分类
- 情感分析
- 用户行为统计

## 存储结构

```
Database/conversations/
├── user_123/                      # 用户ID目录
│   ├── conversations/             # 对话记录
│   │   ├── conv_1234567890_abcd.json
│   │   ├── conv_1234567891_efgh.json
│   │   └── ...
│   ├── keywords/                  # 关键词索引
│   │   └── keyword_index.json
│   └── statistics/                # 用户统计
│       └── user_stats.json
├── user_456/
│   ├── conversations/
│   ├── keywords/
│   └── statistics/
└── ...
```

## 启用方法

### 在聊天API中启用

```javascript
const response = await fetch('/v1/chat/completions', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        model: 'gemini-2.5-flash-preview-05-20',
        memory_tracking: true,         // 启用对话记录
        userId: 'user_12345',          // 指定用户ID
        messages: [
            {
                role: 'user', 
                content: '你好，请帮我分析这个API接口：https://api.openai.com/v1/chat/completions'
            }
        ]
    })
});
```

### 在MCP模式中启用

```javascript
const response = await fetch('/v1/chat/completions', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        model: 'gemini-2.5-flash-preview-05-20',
        type: "mcp",
        memory_tracking: true,         // 启用对话记录
        userId: 'user_12345',          // 指定用户ID
        messages: [
            {
                role: 'user', 
                content: '我的邮箱是****************，文件路径是C:\\Users\\<USER>\\project.json'
            }
        ]
    })
});
```

## 数据格式

### 单个对话记录文件

```json
{
  "id": "conv_1750734580363_6a255a00",
  "userId": "user_123",
  "timestamp": "2025-06-24T03:09:40.363Z",
  "timestampMs": 1750734580363,
  "model": "gpt-4",
  "type": "chat",
  "userMessage": {
    "content": "你好，我想了解 https://api.openai.com/v1/chat/completions",
    "segments": ["你好", "我", "想", "了解", "https://api.openai.com/v1/chat/completions"],
    "keywords": [
      {"word": "api", "weight": 25.83},
      {"word": "openai", "weight": 25.83}
    ],
    "length": 55,
    "characterCount": {"chinese": 12, "english": 40, "number": 1, "other": 2}
  },
  "aiResponse": {
    "content": "这是OpenAI的主要聊天接口...",
    "segments": ["这是", "OpenAI", "的", "主要", "聊天", "接口"],
    "keywords": [
      {"word": "OpenAI", "weight": 15.2},
      {"word": "聊天", "weight": 12.8}
    ],
    "length": 45,
    "characterCount": {"chinese": 35, "english": 8, "number": 0, "other": 2}
  },
  "analysis": {
    "combinedKeywords": [
      {"word": "api", "weight": 51.65},
      {"word": "openai", "weight": 25.83}
    ],
    "totalWords": 12,
    "conversationLength": 100,
    "topics": [{"topic": "技术", "score": 85.2}],
    "sentiment": {
      "user": "neutral",
      "ai": "positive",
      "userScore": 0,
      "aiScore": 1
    }
  },
  "imageUrl": null,
  "metadata": {
    "ip": "*************",
    "userAgent": "Mozilla/5.0...",
    "segmenterType": "nodejieba"
  }
}
```

### 用户统计文件

```json
{
  "userId": "user_123",
  "totalConversations": 15,
  "totalWords": 1250,
  "totalCharacters": 3500,
  "firstConversation": "2025-06-20T10:30:00.000Z",
  "lastConversation": "2025-06-24T03:09:40.363Z",
  "favoriteTopics": {
    "技术": 85.5,
    "学习": 42.3,
    "生活": 15.7
  },
  "mostUsedModels": {
    "gpt-4": 8,
    "gemini-2.5-flash-preview-05-20": 5,
    "gpt-3.5-turbo": 2
  },
  "conversationTypes": {
    "chat": 10,
    "mcp_direct": 3,
    "mcp_tools": 2
  },
  "averageResponseLength": 233,
  "conversationsByDate": {
    "2025-06-20": 3,
    "2025-06-21": 5,
    "2025-06-22": 2,
    "2025-06-23": 3,
    "2025-06-24": 2
  },
  "lastUpdated": "2025-06-24T03:09:40.400Z"
}
```

### 关键词索引文件

```json
{
  "api": {
    "totalWeight": 125.5,
    "conversations": [
      {
        "id": "conv_1750734580363_6a255a00",
        "weight": 51.65,
        "timestamp": "2025-06-24T03:09:40.363Z"
      }
    ],
    "firstSeen": "2025-06-20T10:30:00.000Z",
    "lastSeen": "2025-06-24T03:09:40.363Z"
  }
}
```

## API 方法

### 获取用户统计

```javascript
const ConversationLogger = require('./utils/conversationLogger');
const logger = new ConversationLogger();

// 获取用户统计信息
const stats = await logger.getUserStats('user_123');
console.log('用户统计:', stats);
```

### 搜索用户对话

```javascript
// 搜索特定内容
const results = await logger.searchUserConversations('user_123', 'API', {
    type: 'chat',           // 可选：按类型过滤
    dateFrom: '2025-06-20', // 可选：开始日期
    dateTo: '2025-06-24',   // 可选：结束日期
    limit: 20               // 可选：结果数量限制
});

console.log('搜索结果:', results);
```

### 获取用户对话列表

```javascript
// 获取最近的对话
const conversations = await logger.getUserConversations('user_123', {
    limit: 10,    // 每页数量
    offset: 0,    // 偏移量
    type: 'chat'  // 可选：过滤类型
});

console.log('对话列表:', conversations);
```

## 支持的分词器

1. **nodejieba** (推荐)
   ```bash
   npm install nodejieba --save
   ```

2. **segment** (备用)
   ```bash
   npm install segment --save
   ```

3. **简单分词器** (内置备用方案)

## 话题分类

系统自动识别以下话题：
- **技术**: 编程、API、系统、开发相关
- **学习**: 教育、知识、课程相关
- **生活**: 日常生活、健康、娱乐相关
- **商业**: 市场、管理、投资相关
- **科学**: 物理、化学、数学、研究相关

## 配置选项

```javascript
const logger = new ConversationLogger({
    dataDir: './custom/path/conversations'  // 自定义存储目录
});
```

## 故障排除

### 1. 分词器安装问题
```bash
# 如果nodejieba安装失败，可以使用segment
npm install segment --save

# 或者使用内置的简单分词器（不需要额外安装）
```

### 2. 权限问题
确保应用有写入Database目录的权限：
```bash
chmod 755 Database/
```

### 3. 特殊字符处理
系统会自动处理URL、邮箱等特殊内容，无需特别配置。

### 4. 性能优化
- 关键词索引只保留最近30次对话记录
- 文件按用户分离，避免单文件过大
- 使用单独文件存储，便于并发访问

## 最佳实践

1. **用户ID命名**: 使用有意义的用户标识，如 `user_12345` 或 `guest_timestamp`
2. **定期清理**: 可根据需要定期清理旧的对话记录
3. **备份策略**: 重要数据建议定期备份用户统计和关键词索引
4. **监控存储**: 定期检查存储空间使用情况

## 与图片渲染功能结合

Memory Tracking 可以与现有的图片渲染功能完美结合：

```javascript
{
    model: 'gemini-2.5-flash-preview-05-20',
    memory_tracking: true,      // 启用对话记录
    userId: 'user_12345',       // 用户ID
    render_as_image: true,      // 启用图片渲染
    messages: [...]
}
```

这样既能生成图片，又能记录对话数据进行分析。 