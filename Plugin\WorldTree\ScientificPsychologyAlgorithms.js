/**
 * 基于科学研究的心理状态算法模块
 * 
 * 参考文献和理论基础：
 * 1. Circadian Rhythm Research (生理节律研究)
 * 2. Yerkes-Dodson Law (耶克斯-多德森定律)
 * 3. Cognitive Load Theory (认知负荷理论)
 * 4. Attention Restoration Theory (注意力恢复理论)
 * 5. Homeostatic Sleep Drive (睡眠稳态驱动)
 * 6. Ultradian Rhythms (超日节律)
 * 7. Maslow's Hierarchy of Needs (马斯洛需求层次理论)
 * 8. Flow Theory (心流理论)
 */

const moment = require('moment');
const { Matrix } = require('ml-matrix');
const natural = require('natural');

class ScientificPsychologyAlgorithms {
    constructor(config = {}) {
        this.config = {
            // 生理节律参数
            circadianPeriod: 24, // 24小时生理节律
            ultradianPeriod: 1.5, // 90分钟超日节律
            sleepDriveDecay: 0.95, // 睡眠压力衰减系数
            
            // 认知负荷参数
            maxCognitiveCapacity: 100,
            attentionSpan: 25, // 注意力持续时间（分钟）
            restoreRate: 0.1, // 注意力恢复速率
            
            // 生理需求参数
            hungerCycle: 4, // 饥饿周期（小时）
            metabolicRate: 1.0, // 基础代谢率
            stressDecay: 0.98, // 压力衰减系数
            
            ...config
        };
        
        // 初始化状态
        this.lastUpdateTime = Date.now();
        this.cumulativeCognitiveLoad = 0;
        this.sleepDebt = 0;
        this.stressAccumulation = 0;
    }

    /**
     * 计算综合心理状态
     * 基于多个科学模型的综合评估
     */
    calculatePsychologicalState(contextFactors = {}) {
        const now = moment();
        const timeFactors = this.calculateTimeFactors(now);
        const physiologicalState = this.calculatePhysiologicalState(now, contextFactors);
        const cognitiveState = this.calculateCognitiveState(contextFactors);
        const emotionalState = this.calculateEmotionalState(contextFactors);
        
        return {
            // 核心指标
            focus: this.calculateFocus(cognitiveState, timeFactors, contextFactors),
            energy: this.calculateEnergy(physiologicalState, timeFactors),
            fatigue: this.calculateFatigue(physiologicalState, cognitiveState),
            alertness: this.calculateAlertness(timeFactors, physiologicalState),
            hunger: this.calculateHunger(timeFactors, physiologicalState),
            stress: this.calculateStress(contextFactors, cognitiveState),
            mood: this.calculateMood(emotionalState, physiologicalState),
            
            // 时间相关
            timePeriod: this.getTimePeriod(now),
            circadianPhase: timeFactors.circadianPhase,
            ultradianPhase: timeFactors.ultradianPhase,
            
            // 详细状态
            physiological: physiologicalState,
            cognitive: cognitiveState,
            emotional: emotionalState,
            temporal: timeFactors,
            
            // 元数据
            timestamp: now.format('YYYY/MM/DD HH:mm:ss'),
            algorithm: 'scientific_v2.0'
        };
    }

    /**
     * 计算时间因素
     * 基于生理节律研究 (Circadian & Ultradian Rhythms)
     */
    calculateTimeFactors(now) {
        const hour = now.hour();
        const minute = now.minute();
        const totalMinutes = hour * 60 + minute;
        
        // 生理节律相位 (0-1, 0.5为峰值)
        const circadianPhase = Math.sin((totalMinutes / (24 * 60)) * 2 * Math.PI + Math.PI / 2) * 0.5 + 0.5;
        
        // 超日节律相位 (90分钟周期)
        const ultradianPhase = Math.sin((totalMinutes / 90) * 2 * Math.PI) * 0.5 + 0.5;
        
        // 基于研究的时间段特征
        const timeCharacteristics = this.getTimeCharacteristics(hour);
        
        return {
            hour,
            minute,
            totalMinutes,
            circadianPhase,
            ultradianPhase,
            ...timeCharacteristics
        };
    }

    /**
     * 获取时间段特征
     * 基于生理心理学研究的时间段特点
     */
    getTimeCharacteristics(hour) {
        if (hour >= 6 && hour < 10) {
            // 早晨：皮质醇峰值，警觉性高
            return {
                period: 'morning',
                cortisolLevel: 0.9,
                alertnessBonus: 0.3,
                energyMultiplier: 1.2,
                cognitiveCapacity: 0.8
            };
        } else if (hour >= 10 && hour < 14) {
            // 上午：认知能力峰值
            return {
                period: 'late_morning',
                cortisolLevel: 0.7,
                alertnessBonus: 0.2,
                energyMultiplier: 1.1,
                cognitiveCapacity: 1.0
            };
        } else if (hour >= 14 && hour < 18) {
            // 下午：体温峰值，运动能力强
            return {
                period: 'afternoon',
                cortisolLevel: 0.5,
                alertnessBonus: 0.1,
                energyMultiplier: 1.0,
                cognitiveCapacity: 0.9
            };
        } else if (hour >= 18 && hour < 22) {
            // 傍晚：社交活动峰值
            return {
                period: 'evening',
                cortisolLevel: 0.4,
                alertnessBonus: 0.0,
                energyMultiplier: 0.8,
                cognitiveCapacity: 0.7
            };
        } else {
            // 夜晚：褪黑素分泌，准备睡眠
            return {
                period: 'night',
                cortisolLevel: 0.2,
                alertnessBonus: -0.2,
                energyMultiplier: 0.5,
                cognitiveCapacity: 0.6
            };
        }
    }

    /**
     * 计算生理状态
     * 基于稳态调节理论 (Homeostatic Regulation)
     */
    calculatePhysiologicalState(now, contextFactors) {
        const timeSinceLastMeal = contextFactors.timeSinceLastMeal || 3; // 小时
        const timeSinceLastSleep = contextFactors.timeSinceLastSleep || 8; // 小时
        const physicalActivity = contextFactors.physicalActivity || 0.3; // 0-1
        
        // 睡眠压力累积 (Process S)
        const sleepPressure = Math.min(1.0, timeSinceLastSleep / 16); // 16小时后达到最大
        
        // 代谢状态
        const metabolicState = this.calculateMetabolicState(timeSinceLastMeal, physicalActivity);
        
        // 体温节律
        const bodyTemperature = this.calculateBodyTemperature(now);
        
        return {
            sleepPressure,
            metabolicState,
            bodyTemperature,
            timeSinceLastMeal,
            timeSinceLastSleep,
            physicalActivity
        };
    }

    /**
     * 计算认知状态
     * 基于认知负荷理论 (Cognitive Load Theory)
     */
    calculateCognitiveState(contextFactors) {
        const recentInteractions = contextFactors.recentInteractions || 0;
        const taskComplexity = contextFactors.taskComplexity || 0.5;
        const multitasking = contextFactors.multitasking || 0;
        
        // 认知负荷累积
        const intrinsicLoad = taskComplexity * 0.4;
        const extraneousLoad = multitasking * 0.3;
        const germaneLoad = Math.max(0, 0.3 - (intrinsicLoad + extraneousLoad));
        
        const totalCognitiveLoad = intrinsicLoad + extraneousLoad + germaneLoad;
        
        // 注意力资源
        const attentionCapacity = Math.max(0, 1 - totalCognitiveLoad);
        
        // 心流状态评估 (Flow Theory)
        const flowState = this.calculateFlowState(taskComplexity, attentionCapacity);
        
        return {
            intrinsicLoad,
            extraneousLoad,
            germaneLoad,
            totalCognitiveLoad,
            attentionCapacity,
            flowState,
            recentInteractions
        };
    }

    /**
     * 计算情绪状态
     * 基于情绪的维度模型 (Dimensional Model of Emotion)
     */
    calculateEmotionalState(contextFactors) {
        const socialInteraction = contextFactors.socialInteraction || 0.5;
        const achievement = contextFactors.achievement || 0.5;
        const novelty = contextFactors.novelty || 0.3;
        
        // 情绪维度：效价 (Valence) 和唤醒 (Arousal)
        const valence = (achievement * 0.4 + socialInteraction * 0.3 + novelty * 0.3) * 2 - 1; // -1 to 1
        const arousal = (novelty * 0.5 + socialInteraction * 0.3 + achievement * 0.2) * 2 - 1; // -1 to 1
        
        // 支配感 (Dominance)
        const dominance = achievement * 0.6 + (1 - contextFactors.uncertainty || 0.3) * 0.4;
        
        return {
            valence,
            arousal,
            dominance,
            socialInteraction,
            achievement,
            novelty
        };
    }

    /**
     * 计算专注度
     * 基于注意力网络理论 (Attention Network Theory)
     */
    calculateFocus(cognitiveState, timeFactors, contextFactors) {
        const baseAttention = timeFactors.cognitiveCapacity;
        const cognitiveLoad = cognitiveState.totalCognitiveLoad;
        const flowBonus = cognitiveState.flowState * 0.3;
        const circadianBonus = timeFactors.circadianPhase * 0.2;
        const ultradianBonus = timeFactors.ultradianPhase * 0.1;
        
        // Yerkes-Dodson Law: 适度的唤醒提高表现
        const arousalOptimal = Math.abs(contextFactors.arousal || 0.5 - 0.6); // 最优唤醒在0.6
        const arousalEffect = Math.max(0, 1 - arousalOptimal * 2);
        
        const focus = Math.max(0, Math.min(100, 
            (baseAttention - cognitiveLoad + flowBonus + circadianBonus + ultradianBonus + arousalEffect * 0.2) * 100
        ));
        
        return focus;
    }

    /**
     * 计算精力水平
     * 基于能量分配理论 (Energy Allocation Theory)
     */
    calculateEnergy(physiologicalState, timeFactors) {
        const baseEnergy = timeFactors.energyMultiplier;
        const sleepDebt = physiologicalState.sleepPressure;
        const metabolicEnergy = physiologicalState.metabolicState.energyLevel;
        const circadianEnergy = timeFactors.circadianPhase;
        
        // 能量消耗模型
        const energy = Math.max(0, Math.min(100,
            (baseEnergy * metabolicEnergy * circadianEnergy * (1 - sleepDebt * 0.7)) * 100
        ));
        
        return energy;
    }

    /**
     * 计算疲劳度
     * 基于疲劳累积模型 (Fatigue Accumulation Model)
     */
    calculateFatigue(physiologicalState, cognitiveState) {
        const physicalFatigue = physiologicalState.sleepPressure * 0.6;
        const mentalFatigue = cognitiveState.totalCognitiveLoad * 0.4;
        const metabolicFatigue = (1 - physiologicalState.metabolicState.energyLevel) * 0.3;
        
        const totalFatigue = Math.max(0, Math.min(100,
            (physicalFatigue + mentalFatigue + metabolicFatigue) * 100
        ));
        
        return totalFatigue;
    }

    /**
     * 计算警觉性
     * 基于警觉性网络模型 (Alertness Network Model)
     */
    calculateAlertness(timeFactors, physiologicalState) {
        const circadianAlertness = timeFactors.circadianPhase;
        const cortisolEffect = timeFactors.cortisolLevel;
        const sleepEffect = 1 - physiologicalState.sleepPressure;
        const temperatureEffect = physiologicalState.bodyTemperature;
        
        const alertness = Math.max(0, Math.min(100,
            (circadianAlertness * 0.3 + cortisolEffect * 0.3 + sleepEffect * 0.3 + temperatureEffect * 0.1) * 100
        ));
        
        return alertness;
    }

    /**
     * 计算饥饿感
     * 基于代谢调节理论 (Metabolic Regulation Theory)
     */
    calculateHunger(timeFactors, physiologicalState) {
        const timeSinceLastMeal = physiologicalState.timeSinceLastMeal;
        const metabolicRate = this.config.metabolicRate;
        const circadianHunger = Math.sin((timeFactors.totalMinutes / (24 * 60)) * 2 * Math.PI * 3) * 0.3 + 0.7; // 3餐节律
        
        // 血糖水平模拟
        const bloodGlucose = Math.max(0.3, Math.exp(-timeSinceLastMeal / 4) * metabolicRate);
        const hungerDrive = (1 - bloodGlucose) * circadianHunger;
        
        const hunger = Math.max(0, Math.min(100, hungerDrive * 100));
        
        return hunger;
    }

    // 辅助方法
    calculateMetabolicState(timeSinceLastMeal, physicalActivity) {
        const glucoseLevel = Math.max(0.3, Math.exp(-timeSinceLastMeal / 4));
        const energyLevel = glucoseLevel * (1 - physicalActivity * 0.3);
        
        return { glucoseLevel, energyLevel };
    }

    calculateBodyTemperature(now) {
        const hour = now.hour();
        // 体温日节律：下午最高，凌晨最低
        return Math.sin((hour - 6) / 24 * 2 * Math.PI) * 0.5 + 0.5;
    }

    calculateFlowState(taskComplexity, attentionCapacity) {
        // 心流状态：任务难度与能力匹配时达到最佳
        const skillChallengeBalance = 1 - Math.abs(taskComplexity - attentionCapacity);
        return Math.max(0, skillChallengeBalance);
    }

    calculateStress(contextFactors, cognitiveState) {
        const uncertainty = contextFactors.uncertainty || 0.3;
        const timePress = contextFactors.timePress || 0.3;
        const cognitiveOverload = Math.max(0, cognitiveState.totalCognitiveLoad - 0.7);
        
        const stress = Math.max(0, Math.min(100,
            (uncertainty * 0.4 + timePress * 0.3 + cognitiveOverload * 0.3) * 100
        ));
        
        return stress;
    }

    calculateMood(emotionalState, physiologicalState) {
        const valenceEffect = emotionalState.valence * 0.5 + 0.5; // 0-1
        const energyEffect = physiologicalState.metabolicState.energyLevel;
        const sleepEffect = 1 - physiologicalState.sleepPressure;
        
        const mood = Math.max(0, Math.min(100,
            (valenceEffect * 0.5 + energyEffect * 0.3 + sleepEffect * 0.2) * 100
        ));
        
        return mood;
    }

    getTimePeriod(now) {
        const hour = now.hour();
        if (hour >= 6 && hour < 10) return 'morning';
        if (hour >= 10 && hour < 14) return 'late_morning';
        if (hour >= 14 && hour < 18) return 'afternoon';
        if (hour >= 18 && hour < 22) return 'evening';
        return 'night';
    }
}

module.exports = ScientificPsychologyAlgorithms;
