const fs = require('fs').promises;
const path = require('path');

// 引入VCP日志系统，但强制输出到stderr以避免污染stdout的JSON
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    }
}

// --- Configuration ---
const DEBUG_MODE = (process.env.DebugMode || "false").toLowerCase() === "true";
const projectBasePath = process.env.PROJECT_BASE_PATH;

// --- Paths ---
// Source directory for images (relative to project root)
const sourceImageBaseDir = projectBasePath ? path.join(projectBasePath, 'image') : null;
// Output directory for generated .txt lists (within this plugin's directory)
const outputDirForLists = path.join(__dirname, 'generated_lists');

// --- Debug Logging (to stderr) ---
function debugLog(message, ...args) {
    if (DEBUG_MODE) {
        logger.debug('表情包列表生成', message, ...args);
    }
}

// --- Main Logic ---
async function generateEmojiLists() {
    if (!sourceImageBaseDir) {
        logger.error('表情包列表生成', 'PROJECT_BASE_PATH环境变量未设置。无法定位图片目录');
        process.stdout.write(JSON.stringify({ status: "error", message: "PROJECT_BASE_PATH not set." }));
        return;
    }
    debugLog(`Source image directory: ${sourceImageBaseDir}`);
    debugLog(`Output directory for lists: ${outputDirForLists}`);

    try {
        // Ensure the output directory exists
        await fs.mkdir(outputDirForLists, { recursive: true });
        debugLog(`Ensured output directory exists: ${outputDirForLists}`);

        const entries = await fs.readdir(sourceImageBaseDir, { withFileTypes: true });
        const emojiDirs = entries.filter(entry => entry.isDirectory() && entry.name.endsWith('表情包'));

        if (emojiDirs.length === 0) {
            logger.warning('表情包列表生成', `在 \${sourceImageBaseDir} 中未找到以'表情包'结尾的目录`);
            process.stdout.write(JSON.stringify({ status: "success", message: "No emoji pack directories found.", generated_files: 0 }));
            return;
        }

        let generatedCount = 0;
        for (const dirEntry of emojiDirs) {
            const emojiPackName = dirEntry.name; // e.g., "通用表情包"
            const emojiPackPath = path.join(sourceImageBaseDir, emojiPackName);
            const outputFilePath = path.join(outputDirForLists, `${emojiPackName}.txt`);

            try {
                const files = await fs.readdir(emojiPackPath);
                const imageFiles = files.filter(file => /\.(jpg|jpeg|png|gif)$/i.test(file));
                const listContent = imageFiles.join('|');

                await fs.writeFile(outputFilePath, listContent);
                debugLog(`Successfully generated list for ${emojiPackName} at ${outputFilePath} with ${imageFiles.length} images.`);
                generatedCount++;
            } catch (error) {
                logger.error('表情包列表生成', `处理表情包 \${emojiPackName} 时出错`, error.message);
                // Continue to next pack if one fails
            }
        }
        const successMsg = `Generated ${generatedCount} emoji list files in ${outputDirForLists}.`;
        logger.plugin('表情包列表生成', successMsg);
        process.stdout.write(JSON.stringify({ status: "success", message: successMsg, generated_files: generatedCount }));

    } catch (error) {
        if (error.code === 'ENOENT' && error.path === sourceImageBaseDir) {
             logger.error('表情包列表生成', `在 \${sourceImageBaseDir} 找不到源图片目录`);
             process.stdout.write(JSON.stringify({ status: "error", message: `Source image directory not found: ${sourceImageBaseDir}` }));
        } else {
            logger.error('表情包列表生成', '生成表情包列表时出错', error.message);
            process.stdout.write(JSON.stringify({ status: "error", message: error.message || "An unknown error occurred during list generation." }));
        }
    }
}

// --- Execution ---
(async () => {
    try {
        await generateEmojiLists();
    } catch (e) {
        // Catch any unhandled errors from generateEmojiLists itself, though it should handle its own.
        logger.error('表情包列表生成', '执行期间发生致命错误', e);
        process.stdout.write(JSON.stringify({ status: "fatal", message: e.message || "A fatal unknown error occurred." }));
        process.exitCode = 1;
    }
})();