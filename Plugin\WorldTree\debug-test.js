/**
 * 世界树VCP插件调试测试脚本
 * 用于快速验证修复后的插件功能
 */

const WorldTreeVCP = require('./WorldTreeVCP.js');
const path = require('path');

// 模拟日志记录器
const mockLogger = {
    info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
    error: (tag, ...args) => console.error(`[ERROR] [${tag}]`, ...args),
    warning: (tag, ...args) => console.warn(`[WARNING] [${tag}]`, ...args),
    debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args),
    success: (tag, ...args) => console.log(`[SUCCESS] [${tag}]`, ...args),
    system: (...args) => console.log(`[SYSTEM]`, ...args)
};

async function debugTest() {
    console.log('🔧 开始调试测试世界树VCP插件...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 创建插件实例
        console.log('1. 创建插件实例...');
        worldTreeVCP = new WorldTreeVCP();
        console.log('✅ 插件实例创建成功');
        
        // 2. 检查数据库路径
        console.log('\n2. 检查数据库路径...');
        console.log('数据库路径:', worldTreeVCP.dbPath);
        
        const fs = require('fs');
        const dbExists = fs.existsSync(worldTreeVCP.dbPath);
        console.log('数据库文件存在:', dbExists);
        
        if (!dbExists) {
            console.log('⚠️ 数据库文件不存在，将会自动创建');
        }
        
        // 3. 初始化插件
        console.log('\n3. 初始化插件...');
        const initResult = await worldTreeVCP.initialize(mockLogger);
        
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        
        console.log('✅ 插件初始化成功');
        
        // 4. 测试数据库连接
        console.log('\n4. 测试数据库连接...');
        const testQuery = await worldTreeVCP.dbGet('SELECT 1 as test');
        console.log('数据库连接测试结果:', testQuery);
        console.log('✅ 数据库连接正常');
        
        // 5. 检查表结构
        console.log('\n5. 检查表结构...');
        const tables = await worldTreeVCP.dbAll(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE 'world_tree_%' OR name LIKE 'psychology_%' OR name LIKE 'time_events'
        `);
        console.log('世界树相关表:', tables.map(t => t.name));
        console.log('✅ 表结构检查完成');
        
        // 6. 测试基础功能
        console.log('\n6. 测试基础功能...');
        
        // 测试时间功能
        const currentTime = worldTreeVCP.getCurrentLocalTime();
        const timePeriod = worldTreeVCP.getTimePeriod();
        console.log('当前时间:', currentTime);
        console.log('时间段:', timePeriod);
        
        // 测试插件状态
        const status = worldTreeVCP.getStatus();
        console.log('插件状态:', {
            initialized: status.isInitialized,
            configuredAgents: status.statistics.configuredAgents
        });
        
        console.log('✅ 基础功能测试通过');
        
        // 7. 测试Agent列表
        console.log('\n7. 测试Agent列表...');
        try {
            const agents = await worldTreeVCP.getAgentList();
            console.log('找到的Agent:', agents.map(a => a.name));
            console.log('✅ Agent列表获取成功');
        } catch (error) {
            console.log('⚠️ Agent列表获取失败:', error.message);
        }
        
        // 8. 测试简单配置创建
        console.log('\n8. 测试简单配置创建...');
        const testConfig = {
            worldBackground: '测试世界背景',
            timeArchitecture: {
                morning: '测试早晨设定'
            },
            characterSchedules: {
                '09:00-12:00': '测试日程'
            },
            narrativeRules: {
                '测试规则': '测试描述'
            }
        };
        
        const configResult = await worldTreeVCP.createOrUpdateWorldTreeConfig('TestAgent', testConfig);
        if (configResult) {
            console.log('✅ 配置创建成功');
            
            // 测试配置获取
            const retrievedConfig = await worldTreeVCP.getWorldTreeConfig('TestAgent');
            if (retrievedConfig) {
                console.log('✅ 配置获取成功');
            } else {
                console.log('❌ 配置获取失败');
            }
        } else {
            console.log('❌ 配置创建失败');
        }
        
        console.log('\n🎉 调试测试完成！插件基本功能正常。');
        
    } catch (error) {
        console.error('\n❌ 调试测试失败:', error.message);
        console.error('错误堆栈:', error.stack);
        
        // 提供调试建议
        console.log('\n🔍 调试建议:');
        if (error.message.includes('SQLITE_ERROR')) {
            console.log('- 检查SQL语法是否正确');
            console.log('- 确认数据库文件权限');
        }
        if (error.message.includes('ENOENT')) {
            console.log('- 检查文件路径是否正确');
            console.log('- 确认相关目录是否存在');
        }
        if (error.message.includes('数据库')) {
            console.log('- 检查AdvancedMemorySystem是否正常运行');
            console.log('- 确认数据库文件是否可访问');
        }
        
    } finally {
        // 清理
        if (worldTreeVCP) {
            try {
                await worldTreeVCP.cleanup();
                console.log('\n🧹 插件清理完成');
            } catch (cleanupError) {
                console.error('清理失败:', cleanupError.message);
            }
        }
    }
}

// 运行调试测试
if (require.main === module) {
    debugTest().then(() => {
        console.log('\n调试测试结束，退出程序。');
        process.exit(0);
    }).catch(error => {
        console.error('\n调试测试过程中发生未捕获的错误:', error);
        process.exit(1);
    });
}

module.exports = { debugTest };
