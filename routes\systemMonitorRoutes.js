const express = require('express');
const os = require('os');
const { exec } = require('child_process');
const router = express.Router();

// 格式化字节数
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化运行时间
function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    let result = '';
    if (days > 0) result += `${days}天 `;
    if (hours > 0) result += `${hours}小时 `;
    if (minutes > 0) result += `${minutes}分钟 `;
    result += `${secs}秒`;
    
    return result;
}

// 获取CPU使用率（需要两次采样）
let lastCpuUsage = null;

function getCpuUsage() {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
        for (let type in cpu.times) {
            totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
    });
    
    const currentUsage = {
        idle: totalIdle,
        total: totalTick,
        timestamp: Date.now()
    };
    
    if (lastCpuUsage) {
        const idleDiff = currentUsage.idle - lastCpuUsage.idle;
        const totalDiff = currentUsage.total - lastCpuUsage.total;
        const cpuPercent = 100 - Math.floor(100 * idleDiff / totalDiff);
        lastCpuUsage = currentUsage;
        return Math.max(0, Math.min(100, cpuPercent));
    } else {
        lastCpuUsage = currentUsage;
        return 0; // 第一次调用返回0
    }
}

// 获取网络接口信息
function getNetworkInterfaces() {
    const interfaces = os.networkInterfaces();
    const result = [];

    for (const [name, addrs] of Object.entries(interfaces)) {
        const ipv4 = addrs.find(addr => addr.family === 'IPv4' && !addr.internal);
        if (ipv4) {
            result.push({
                name: name,
                address: ipv4.address,
                netmask: ipv4.netmask,
                mac: ipv4.mac
            });
        }
    }

    return result;
}

// 获取GPU信息（Windows）
function getGpuInfo() {
    return new Promise((resolve) => {
        if (process.platform !== 'win32') {
            resolve({
                name: 'N/A (非Windows系统)',
                usage: 0,
                memory: { used: 0, total: 0 },
                temperature: 0
            });
            return;
        }

        let gpuInfo = {
            name: '未知显卡',
            usage: 0,
            memory: { used: 0, total: 0 },
            temperature: 0
        };

        // 获取所有GPU信息，优先选择独立显卡
        exec('wmic path win32_VideoController get name,AdapterRAM /format:csv', { timeout: 5000 }, (error, stdout) => {
            if (!error && stdout) {
                const lines = stdout.split('\n').filter(line => line.trim() && !line.includes('AdapterRAM'));
                let selectedGpu = null;
                let hasNvidiaGpu = false;

                // 解析所有GPU，优先选择独立显卡
                for (const line of lines) {
                    const parts = line.split(',');
                    if (parts.length >= 3) {
                        const gpuName = parts[2].trim();
                        const adapterRAM = parseInt(parts[1]) || 0;

                        if (gpuName && gpuName !== '') {
                            // 优先级：NVIDIA > AMD > 其他独立显卡 > 核显
                            if (gpuName.toLowerCase().includes('nvidia') ||
                                gpuName.toLowerCase().includes('geforce') ||
                                gpuName.toLowerCase().includes('rtx') ||
                                gpuName.toLowerCase().includes('gtx')) {
                                selectedGpu = gpuName;
                                hasNvidiaGpu = true;
                                break; // NVIDIA优先级最高
                            } else if (!selectedGpu &&
                                      (gpuName.toLowerCase().includes('amd') ||
                                       gpuName.toLowerCase().includes('radeon') ||
                                       adapterRAM > 1000000000)) {
                                selectedGpu = gpuName; // AMD或有独立显存的显卡
                            } else if (!selectedGpu &&
                                      !gpuName.toLowerCase().includes('intel') &&
                                      !gpuName.toLowerCase().includes('uhd') &&
                                      !gpuName.toLowerCase().includes('hd graphics')) {
                                selectedGpu = gpuName; // 其他非核显
                            }
                        }
                    }
                }

                // 如果没有找到独立显卡，才使用核显
                if (!selectedGpu) {
                    for (const line of lines) {
                        const parts = line.split(',');
                        if (parts.length >= 3) {
                            const gpuName = parts[2].trim();
                            if (gpuName && gpuName !== '') {
                                selectedGpu = gpuName;
                                break;
                            }
                        }
                    }
                }

                if (selectedGpu) {
                    gpuInfo.name = selectedGpu;
                }

                // 如果是NVIDIA显卡，尝试获取详细信息
                if (hasNvidiaGpu) {
                    exec('nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits',
                         { timeout: 3000 }, (error, stdout) => {
                        if (!error && stdout) {
                            const lines = stdout.trim().split('\n');
                            if (lines.length > 0) {
                                const data = lines[0].split(',').map(item => item.trim());
                                if (data.length >= 4) {
                                    gpuInfo.usage = parseFloat(data[0]) || 0;
                                    gpuInfo.memory.used = parseFloat(data[1]) || 0;
                                    gpuInfo.memory.total = parseFloat(data[2]) || 0;
                                    gpuInfo.temperature = parseFloat(data[3]) || 0;
                                }
                            }
                        }
                        resolve(gpuInfo);
                    });
                } else {
                    resolve(gpuInfo);
                }
            } else {
                resolve(gpuInfo);
            }
        });

        // 超时处理
        setTimeout(() => resolve(gpuInfo), 6000);
    });
}

// 实时系统监控数据
router.get('/realtime', async (req, res) => {
    try {
        const memoryUsage = process.memoryUsage();
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        const cpus = os.cpus();
        const loadAvg = os.loadavg();

        // 获取CPU使用率
        const cpuUsage = getCpuUsage();

        // 获取GPU信息
        const gpuInfo = await getGpuInfo();

        res.json({
            success: true,
            timestamp: new Date().toISOString(),
            data: {
                // CPU信息
                cpu: {
                    usage: cpuUsage,
                    cores: cpus.length,
                    model: cpus[0]?.model || 'Unknown',
                    speed: cpus[0]?.speed || 0,
                    loadAverage: {
                        '1min': loadAvg[0].toFixed(2),
                        '5min': loadAvg[1].toFixed(2),
                        '15min': loadAvg[2].toFixed(2)
                    }
                },

                // GPU信息
                gpu: {
                    name: gpuInfo.name,
                    usage: gpuInfo.usage,
                    memory: {
                        used: gpuInfo.memory.used,
                        total: gpuInfo.memory.total,
                        usagePercent: gpuInfo.memory.total > 0 ?
                            ((gpuInfo.memory.used / gpuInfo.memory.total) * 100).toFixed(1) : 0,
                        usedFormatted: formatBytes(gpuInfo.memory.used * 1024 * 1024),
                        totalFormatted: formatBytes(gpuInfo.memory.total * 1024 * 1024)
                    },
                    temperature: gpuInfo.temperature
                },

                // 内存信息
                memory: {
                    system: {
                        total: totalMemory,
                        used: usedMemory,
                        free: freeMemory,
                        usagePercent: ((usedMemory / totalMemory) * 100).toFixed(1),
                        totalFormatted: formatBytes(totalMemory),
                        usedFormatted: formatBytes(usedMemory),
                        freeFormatted: formatBytes(freeMemory)
                    },
                    process: {
                        rss: memoryUsage.rss,
                        heapTotal: memoryUsage.heapTotal,
                        heapUsed: memoryUsage.heapUsed,
                        external: memoryUsage.external,
                        heapUsagePercent: ((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(1),
                        rssFormatted: formatBytes(memoryUsage.rss),
                        heapTotalFormatted: formatBytes(memoryUsage.heapTotal),
                        heapUsedFormatted: formatBytes(memoryUsage.heapUsed),
                        externalFormatted: formatBytes(memoryUsage.external)
                    }
                },

                // 系统信息
                system: {
                    platform: os.platform(),
                    arch: os.arch(),
                    hostname: os.hostname(),
                    uptime: os.uptime(),
                    uptimeFormatted: formatUptime(os.uptime()),
                    processUptime: process.uptime(),
                    processUptimeFormatted: formatUptime(process.uptime())
                },

                // 网络接口
                network: getNetworkInterfaces()
            }
        });

    } catch (error) {
        console.error('获取实时监控数据失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取系统详细信息
router.get('/details', (req, res) => {
    try {
        const cpus = os.cpus();
        const networkInterfaces = os.networkInterfaces();
        
        res.json({
            success: true,
            data: {
                // 详细CPU信息
                cpu: {
                    count: cpus.length,
                    model: cpus[0]?.model || 'Unknown',
                    speed: cpus[0]?.speed || 0,
                    cores: cpus.map((cpu, index) => ({
                        core: index,
                        model: cpu.model,
                        speed: cpu.speed,
                        times: cpu.times
                    }))
                },
                
                // 系统信息
                system: {
                    platform: os.platform(),
                    arch: os.arch(),
                    release: os.release(),
                    version: os.version(),
                    hostname: os.hostname(),
                    homedir: os.homedir(),
                    tmpdir: os.tmpdir(),
                    endianness: os.endianness(),
                    nodeVersion: process.version,
                    pid: process.pid
                },
                
                // 网络接口详情
                network: networkInterfaces,
                
                // 用户信息
                user: os.userInfo()
            }
        });
        
    } catch (error) {
        console.error('获取系统详细信息失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取进程信息
router.get('/process', (req, res) => {
    try {
        const memoryUsage = process.memoryUsage();
        const resourceUsage = process.resourceUsage();
        
        res.json({
            success: true,
            data: {
                pid: process.pid,
                ppid: process.ppid,
                platform: process.platform,
                arch: process.arch,
                version: process.version,
                versions: process.versions,
                uptime: process.uptime(),
                uptimeFormatted: formatUptime(process.uptime()),
                memory: {
                    ...memoryUsage,
                    rssFormatted: formatBytes(memoryUsage.rss),
                    heapTotalFormatted: formatBytes(memoryUsage.heapTotal),
                    heapUsedFormatted: formatBytes(memoryUsage.heapUsed),
                    externalFormatted: formatBytes(memoryUsage.external)
                },
                resourceUsage: resourceUsage,
                env: {
                    NODE_ENV: process.env.NODE_ENV,
                    PORT: process.env.PORT,
                    DEBUG_MODE: process.env.DEBUG_MODE
                }
            }
        });
        
    } catch (error) {
        console.error('获取进程信息失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
