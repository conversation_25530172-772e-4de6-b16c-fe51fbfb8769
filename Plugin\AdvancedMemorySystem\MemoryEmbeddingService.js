/**
 * 记忆嵌入服务
 * 基于OpenAI embedding进行记忆检索和相似度计算
 */

const path = require('path');
const fs = require('fs');

class MemoryEmbeddingService {
    constructor(config, openaiService, logger) {
        this.config = config;
        this.openaiService = openaiService;
        this.logger = logger;
        this.isInitialized = false;
        
        // 缓存
        this.embeddingCache = new Map();
        this.similarityCache = new Map();
        this.cacheMaxSize = 1000;
    }

    /**
     * 初始化服务
     */
    async initialize() {
        try {
            if (!this.openaiService || !this.openaiService.isInitialized) {
                throw new Error('OpenAI服务未初始化');
            }
            
            this.isInitialized = true;
            this.logger.success('记忆嵌入服务', '✅ 记忆嵌入服务初始化成功');
            return { success: true };
            
        } catch (error) {
            this.logger.error('记忆嵌入服务', '初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 生成文本的嵌入向量
     */
    async generateEmbedding(text) {
        try {
            // 检查缓存
            const cacheKey = this.hashText(text);
            if (this.embeddingCache.has(cacheKey)) {
                return this.embeddingCache.get(cacheKey);
            }

            // 预处理文本
            const processedText = this.preprocessText(text);

            // 调用OpenAI生成嵌入向量
            const response = await this.openaiService.generateEmbedding(processedText);

            // 提取嵌入向量数组
            let embeddingVector;
            if (response && response.embedding && Array.isArray(response.embedding)) {
                embeddingVector = response.embedding;
            } else if (Array.isArray(response)) {
                embeddingVector = response;
            } else {
                this.logger.error('记忆嵌入服务', `OpenAI返回格式错误: ${JSON.stringify(response).substring(0, 100)}...`);
                return null;
            }

            // 验证嵌入向量
            if (!embeddingVector || embeddingVector.length === 0) {
                this.logger.error('记忆嵌入服务', '嵌入向量为空或无效');
                return null;
            }

            this.logger.debug('记忆嵌入服务', `嵌入向量生成成功，维度: ${embeddingVector.length}`);

            // 缓存结果
            this.cacheEmbedding(cacheKey, embeddingVector);

            return embeddingVector;

        } catch (error) {
            this.logger.error('记忆嵌入服务', '生成嵌入向量失败:', error.message);
            return null; // 返回null而不是抛出异常
        }
    }

    /**
     * 计算两个向量的余弦相似度
     */
    calculateCosineSimilarity(vectorA, vectorB) {
        try {
            if (!vectorA || !vectorB || vectorA.length !== vectorB.length) {
                return 0;
            }

            let dotProduct = 0;
            let normA = 0;
            let normB = 0;

            for (let i = 0; i < vectorA.length; i++) {
                dotProduct += vectorA[i] * vectorB[i];
                normA += vectorA[i] * vectorA[i];
                normB += vectorB[i] * vectorB[i];
            }

            normA = Math.sqrt(normA);
            normB = Math.sqrt(normB);

            if (normA === 0 || normB === 0) {
                return 0;
            }

            return dotProduct / (normA * normB);

        } catch (error) {
            this.logger.error('记忆嵌入服务', '计算余弦相似度失败:', error.message);
            return 0;
        }
    }

    /**
     * 安全解析JSON字符串
     */
    parseJSON(jsonString, defaultValue = null) {
        try {
            if (!jsonString || typeof jsonString !== 'string') {
                return defaultValue;
            }
            return JSON.parse(jsonString);
        } catch (error) {
            this.logger.debug('记忆嵌入服务', `JSON解析失败: ${error.message}`);
            return defaultValue;
        }
    }

    /**
     * 查找相关记忆
     */
    async findRelevantMemories(queryText, options = {}) {
        try {
            const {
                userId,
                personaName,
                limit = 10,
                threshold = 0.2, // 降低阈值，更容易找到相关记忆
                memoryTypes = ['short_term', 'long_term', 'reflection', 'conversation'],
                timeWindow = null // 时间窗口（小时）
            } = options;

            // 检查数据库是否已设置
            if (!this.db || !this.dbAll) {
                this.logger.warning('记忆嵌入服务', '数据库未初始化，返回空结果');
                return [];
            }

            // 生成查询文本的嵌入向量
            const queryEmbedding = await this.generateEmbedding(queryText);

            if (!queryEmbedding || !Array.isArray(queryEmbedding)) {
                this.logger.error('记忆嵌入服务', '查询嵌入向量生成失败');
                return [];
            }

            this.logger.debug('记忆嵌入服务', `查询嵌入向量生成成功，维度: ${queryEmbedding.length}`);

            // 构建查询条件 - 优化查询逻辑
            let whereClause = '';
            const params = [];

            // 优化查询逻辑：优先按userId查询，persona作为可选条件
            if (userId) {
                whereClause = `WHERE user_id = ?`;
                params.push(userId);

                // 记录查询条件用于调试
                this.logger.debug('记忆嵌入服务', `查询条件: userId=${userId}, personaName=${personaName || 'null'}`);

                // 如果同时提供了personaName，尝试精确匹配，如果没找到则忽略persona条件
                if (personaName) {
                    // 先尝试精确匹配
                    const exactMatches = await this.dbAll(`
                        SELECT COUNT(*) as count FROM memory_fragments
                        WHERE user_id = ? AND persona_name = ?
                    `, [userId, personaName]);

                    if (exactMatches && exactMatches[0] && exactMatches[0].count > 0) {
                        whereClause += ` AND persona_name = ?`;
                        params.push(personaName);
                        this.logger.debug('记忆嵌入服务', `使用精确persona匹配: ${personaName}`);
                    } else {
                        this.logger.debug('记忆嵌入服务', `persona ${personaName} 无匹配记录，忽略persona条件`);
                    }
                }
            } else if (personaName) {
                // 如果只提供了personaName，按persona查询
                whereClause = `WHERE persona_name = ?`;
                params.push(personaName);
            } else {
                // 如果都没提供，查询所有记忆（用于全局搜索）
                whereClause = `WHERE 1=1`;
            }

            if (memoryTypes && memoryTypes.length > 0) {
                whereClause += ` AND memory_type IN (${memoryTypes.map(() => '?').join(',')})`;
                params.push(...memoryTypes);
            }

            if (timeWindow) {
                whereClause += ` AND creation_time > datetime('now', '-${timeWindow} hours')`;
            }

            // 从数据库获取记忆片段（包含AI概括字段）
            const memories = await this.dbAll(`
                SELECT id, content, ai_summary, key_insights, conversation_theme,
                       embedding_vector, importance_score, memory_type, creation_time,
                       access_count, related_concepts
                FROM memory_fragments
                ${whereClause}
                ORDER BY importance_score DESC, access_count DESC
            `, params);

            // 确保memories是数组
            if (!Array.isArray(memories)) {
                this.logger.warning('记忆嵌入服务', '数据库查询返回非数组结果，返回空数组');
                return [];
            }

            this.logger.info('记忆嵌入服务', `数据库查询到 ${memories.length} 条记忆片段`);

            // 计算相似度并排序
            const relevantMemories = [];
            let validEmbeddingCount = 0;
            let similaritySum = 0;

            for (const memory of memories) {
                try {
                    // 解析嵌入向量
                    let memoryEmbedding;
                    try {
                        memoryEmbedding = JSON.parse(memory.embedding_vector);
                    } catch (parseError) {
                        this.logger.debug('记忆嵌入服务', `记忆 ${memory.id} 嵌入向量解析失败: ${parseError.message}`);
                        continue;
                    }

                    // 验证嵌入向量格式
                    if (!Array.isArray(memoryEmbedding) || memoryEmbedding.length === 0) {
                        this.logger.debug('记忆嵌入服务', `记忆 ${memory.id} 嵌入向量格式无效，类型: ${typeof memoryEmbedding}, 长度: ${memoryEmbedding?.length || 0}`);
                        continue;
                    }

                    // 检查向量维度是否匹配
                    if (memoryEmbedding.length !== queryEmbedding.length) {
                        this.logger.debug('记忆嵌入服务', `记忆 ${memory.id} 向量维度不匹配: ${memoryEmbedding.length} vs ${queryEmbedding.length}`);
                        continue;
                    }

                    validEmbeddingCount++;

                    // 计算余弦相似度
                    const similarity = this.calculateCosineSimilarity(queryEmbedding, memoryEmbedding);
                    similaritySum += similarity;

                    this.logger.debug('记忆嵌入服务', `记忆 ${memory.id} 相似度: ${similarity.toFixed(3)}, 阈值: ${threshold}`);

                    if (similarity >= threshold) {
                        relevantMemories.push({
                            ...memory,
                            similarity,
                            relevance_score: this.calculateRelevanceScore(similarity, memory),
                            related_concepts: this.parseJSON(memory.related_concepts, [])
                        });
                    }
                } catch (e) {
                    this.logger.warning('记忆嵌入服务', `处理记忆 ${memory.id} 时出错: ${e.message}`);
                }
            }

            const avgSimilarity = validEmbeddingCount > 0 ? similaritySum / validEmbeddingCount : 0;
            this.logger.info('记忆嵌入服务', `处理了 ${memories.length} 条记忆，有效嵌入 ${validEmbeddingCount} 条，平均相似度: ${avgSimilarity.toFixed(3)}`);

            if (validEmbeddingCount === 0) {
                this.logger.warning('记忆嵌入服务', '没有找到有效的嵌入向量');
                return [];
            }

            // 按相关度排序并限制数量
            relevantMemories.sort((a, b) => b.relevance_score - a.relevance_score);
            
            const result = relevantMemories.slice(0, limit);
            
            // 更新访问计数
            if (result.length > 0) {
                await this.updateAccessCounts(result.map(m => m.id));
            }
            
            this.logger.info('记忆嵌入服务', `找到 ${result.length} 条相关记忆，平均相似度: ${this.calculateAverageSimilarity(result)}`);
            
            return result;
            
        } catch (error) {
            this.logger.error('记忆嵌入服务', '查找相关记忆失败:', error.message);
            return [];
        }
    }

    /**
     * 计算综合相关度分数
     */
    calculateRelevanceScore(similarity, memory) {
        try {
            // 基础相似度权重 (70%)
            let score = similarity * 0.7;
            
            // 重要性评分权重 (20%)
            score += (memory.importance_score || 0) * 0.2;
            
            // 时间衰减权重 (10%) - 越新的记忆权重越高
            const daysSinceCreation = this.getDaysSince(memory.creation_time);
            const timeDecay = Math.exp(-daysSinceCreation / 30); // 30天衰减
            score += timeDecay * 0.1;
            
            return Math.min(score, 1.0);
            
        } catch (error) {
            return similarity;
        }
    }

    /**
     * 智能记忆聚类
     */
    async clusterMemories(memories, numClusters = 5) {
        try {
            if (memories.length <= numClusters) {
                return memories.map((memory, index) => ({ ...memory, cluster: index }));
            }

            // 提取嵌入向量
            const embeddings = memories.map(m => {
                try {
                    return JSON.parse(m.embedding_vector);
                } catch {
                    return null;
                }
            }).filter(e => e !== null);

            if (embeddings.length === 0) {
                return memories;
            }

            // 简单的K-means聚类实现
            const clusters = await this.performKMeansClustering(embeddings, numClusters);
            
            // 为记忆分配聚类标签
            const clusteredMemories = memories.map((memory, index) => ({
                ...memory,
                cluster: clusters[index] || 0
            }));

            this.logger.info('记忆嵌入服务', `记忆聚类完成: ${memories.length} 条记忆分为 ${numClusters} 个聚类`);
            
            return clusteredMemories;
            
        } catch (error) {
            this.logger.error('记忆嵌入服务', '记忆聚类失败:', error.message);
            return memories;
        }
    }

    /**
     * 执行K-means聚类
     */
    async performKMeansClustering(embeddings, k) {
        try {
            const maxIterations = 10;
            const dimension = embeddings[0].length;
            
            // 随机初始化聚类中心
            let centroids = [];
            for (let i = 0; i < k; i++) {
                centroids.push(embeddings[Math.floor(Math.random() * embeddings.length)].slice());
            }
            
            let clusters = new Array(embeddings.length);
            
            for (let iteration = 0; iteration < maxIterations; iteration++) {
                let changed = false;
                
                // 分配点到最近的聚类中心
                for (let i = 0; i < embeddings.length; i++) {
                    let minDistance = Infinity;
                    let closestCluster = 0;
                    
                    for (let j = 0; j < k; j++) {
                        const distance = this.calculateEuclideanDistance(embeddings[i], centroids[j]);
                        if (distance < minDistance) {
                            minDistance = distance;
                            closestCluster = j;
                        }
                    }
                    
                    if (clusters[i] !== closestCluster) {
                        clusters[i] = closestCluster;
                        changed = true;
                    }
                }
                
                if (!changed) break;
                
                // 更新聚类中心
                for (let j = 0; j < k; j++) {
                    const clusterPoints = embeddings.filter((_, i) => clusters[i] === j);
                    if (clusterPoints.length > 0) {
                        for (let d = 0; d < dimension; d++) {
                            centroids[j][d] = clusterPoints.reduce((sum, point) => sum + point[d], 0) / clusterPoints.length;
                        }
                    }
                }
            }
            
            return clusters;
            
        } catch (error) {
            this.logger.error('记忆嵌入服务', 'K-means聚类失败:', error.message);
            return new Array(embeddings.length).fill(0);
        }
    }

    /**
     * 计算欧几里得距离
     */
    calculateEuclideanDistance(vectorA, vectorB) {
        let sum = 0;
        for (let i = 0; i < vectorA.length; i++) {
            sum += Math.pow(vectorA[i] - vectorB[i], 2);
        }
        return Math.sqrt(sum);
    }

    /**
     * 基于语义的记忆检索增强
     */
    async enhancedSemanticSearch(queryText, options = {}) {
        try {
            const baseResults = await this.findRelevantMemories(queryText, options);
            
            // 使用OpenAI进行语义增强
            const enhancedQuery = await this.openaiService.makeRequest('/v1/chat/completions', 'POST', {
                model: this.config.openai_model,
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的语义分析专家。请扩展和重新表述用户的查询，以捕获更多相关的语义信息。'
                    },
                    {
                        role: 'user',
                        content: `原始查询: ${queryText}\n\n请生成3-5个语义相关的查询变体，每个一行：`
                    }
                ],
                temperature: 0.3,
                max_tokens: 200
            });

            const variations = enhancedQuery.choices[0].message.content
                .split('\n')
                .filter(line => line.trim())
                .slice(0, 5);

            // 对每个变体进行搜索
            const allResults = [...baseResults];
            
            for (const variation of variations) {
                const variantResults = await this.findRelevantMemories(variation, {
                    ...options,
                    limit: 5,
                    threshold: options.threshold * 0.8 // 稍微降低阈值
                });
                
                // 合并结果，避免重复
                for (const result of variantResults) {
                    if (!allResults.find(r => r.id === result.id)) {
                        allResults.push({ ...result, similarity: result.similarity * 0.9 }); // 稍微降低变体的权重
                    }
                }
            }

            // 重新排序和限制
            allResults.sort((a, b) => b.relevance_score - a.relevance_score);
            
            return allResults.slice(0, options.limit || 10);
            
        } catch (error) {
            this.logger.error('记忆嵌入服务', '增强语义搜索失败:', error.message);
            return await this.findRelevantMemories(queryText, options);
        }
    }

    /**
     * 预处理文本
     */
    preprocessText(text) {
        try {
            // 基本清理
            let processed = text.trim();
            
            // 移除过多的空白字符
            processed = processed.replace(/\s+/g, ' ');
            
            // 限制长度（embedding模型的限制）
            if (processed.length > 8000) {
                processed = processed.substring(0, 8000) + '...';
            }
            
            return processed;
            
        } catch (error) {
            this.logger.warning('记忆嵌入服务', '文本预处理失败:', error.message);
            return text;
        }
    }

    /**
     * 文本哈希
     */
    hashText(text) {
        let hash = 0;
        for (let i = 0; i < text.length; i++) {
            const char = text.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString();
    }

    /**
     * 缓存嵌入向量
     */
    cacheEmbedding(key, embedding) {
        if (this.embeddingCache.size >= this.cacheMaxSize) {
            // 删除最旧的缓存项
            const firstKey = this.embeddingCache.keys().next().value;
            this.embeddingCache.delete(firstKey);
        }
        this.embeddingCache.set(key, embedding);
    }

    /**
     * 更新访问计数
     */
    async updateAccessCounts(memoryIds) {
        try {
            if (memoryIds.length === 0) return;
            
            const placeholders = memoryIds.map(() => '?').join(',');
            await this.db.run(`
                UPDATE memory_fragments 
                SET access_count = access_count + 1, last_accessed = CURRENT_TIMESTAMP
                WHERE id IN (${placeholders})
            `, memoryIds);
            
        } catch (error) {
            this.logger.warning('记忆嵌入服务', '更新访问计数失败:', error.message);
        }
    }

    /**
     * 计算平均相似度
     */
    calculateAverageSimilarity(memories) {
        if (memories.length === 0) return 0;
        const total = memories.reduce((sum, m) => sum + (m.similarity || 0), 0);
        return (total / memories.length).toFixed(3);
    }

    /**
     * 计算日期差
     */
    getDaysSince(dateString) {
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        } catch {
            return 0;
        }
    }

    /**
     * 设置数据库引用
     */
    setDatabase(db) {
        this.db = db;
        // 确保数据库方法存在
        if (db && typeof db.run === 'function') {
            this.dbRun = require('util').promisify(db.run.bind(db));
            this.dbGet = require('util').promisify(db.get.bind(db));
            this.dbAll = require('util').promisify(db.all.bind(db));
            this.logger.info('记忆嵌入服务', '数据库方法设置完成');
        } else {
            this.logger.warning('记忆嵌入服务', '数据库对象无效，无法设置数据库方法');
        }
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            cacheSize: this.embeddingCache.size,
            maxCacheSize: this.cacheMaxSize
        };
    }

    /**
     * 存储对话的嵌入向量（完整对话记录）
     */
    async storeConversationEmbedding(userId, userMessage, aiResponse, extraData = {}) {
        try {
            this.logger.info('记忆嵌入服务', '开始生成和存储对话嵌入向量');

            // 检查数据库是否已设置
            if (!this.db || !this.dbRun) {
                this.logger.warning('记忆嵌入服务', '数据库未初始化，无法存储');
                return { success: false, error: '数据库未初始化' };
            }

            // 合并用户消息和AI回复作为完整对话内容 - 使用实际的用户名和助手名
            const personaName = extraData.personaName || 'Assistant';
            const conversationText = `${userId}: ${userMessage}\n${personaName}: ${aiResponse}`;

            // 生成AI智能概括
            let aiSummary = null;
            let keyInsights = null;
            let conversationTheme = null;

            try {
                if (this.openaiService && this.openaiService.generateConversationSummary) {
                    this.logger.debug('记忆嵌入服务', '开始生成AI智能概括');
                    const summaryResult = await this.openaiService.generateConversationSummary(
                        userMessage,
                        aiResponse,
                        {
                            userId: userId,
                            assistantName: personaName
                        }
                    );

                    if (summaryResult) {
                        aiSummary = summaryResult.ai_summary;
                        keyInsights = JSON.stringify(summaryResult.key_insights || []);
                        conversationTheme = summaryResult.conversation_theme;

                        this.logger.info('记忆嵌入服务', 'AI智能概括生成成功', {
                            theme: conversationTheme,
                            insightsCount: summaryResult.key_insights?.length || 0
                        });
                    }
                }
            } catch (summaryError) {
                this.logger.warning('记忆嵌入服务', `AI概括生成失败，使用原始内容: ${summaryError.message}`);
            }

            // 生成嵌入向量（基于概括内容或原始内容）
            const embeddingText = aiSummary || conversationText;
            const embeddingVector = await this.generateEmbedding(embeddingText);

            if (!embeddingVector || !Array.isArray(embeddingVector)) {
                this.logger.error('记忆嵌入服务', '嵌入向量生成失败或格式错误');
                return { success: false, error: '嵌入向量生成失败' };
            }

            // 计算重要性分数
            const importanceScore = this.calculateConversationImportance(userMessage, aiResponse, extraData);

            // 使用北京时间
            const beijingTime = new Date(new Date().getTime() + 8 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);

            // 存储到记忆片段表（包含新的AI概括字段）
            const result = await this.dbRun(`
                INSERT INTO memory_fragments (
                    user_id, persona_name, content, ai_summary, key_insights, conversation_theme,
                    embedding_vector, memory_type, importance_score, creation_time, access_count,
                    emotional_context, related_concepts
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                userId,
                extraData.personaName || 'Assistant',
                conversationText,
                aiSummary,
                keyInsights,
                conversationTheme,
                JSON.stringify(embeddingVector),
                'conversation',
                importanceScore,
                beijingTime,
                0,
                JSON.stringify({
                    emotionData: extraData.emotionData || {},
                    timestamp: beijingTime,
                    conversationId: extraData.conversationId || null,
                    metadata: extraData.metadata || {}
                }),
                JSON.stringify(extraData.relatedConcepts || [])
            ]);

            const memoryId = result && result.lastID ? result.lastID : Date.now();

            this.logger.success('记忆嵌入服务', '对话嵌入向量存储成功', {
                memoryId: memoryId,
                importanceScore: importanceScore.toFixed(3),
                conversationLength: conversationText.length,
                embeddingDimension: embeddingVector.length,
                beijingTime
            });

            return {
                success: true,
                memoryId: memoryId,
                embeddingVector,
                importanceScore,
                conversationId: extraData.conversationId,
                beijingTime
            };

        } catch (error) {
            this.logger.error('记忆嵌入服务', `存储对话嵌入向量失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 计算对话重要性分数
     */
    calculateConversationImportance(userMessage, aiResponse, extraData = {}) {
        try {
            let score = 0.5; // 基础分数

            // 基于长度
            const totalLength = userMessage.length + aiResponse.length;
            if (totalLength > 100) score += 0.1;
            if (totalLength > 300) score += 0.1;

            // 基于情感强度
            if (extraData.emotionData) {
                const emotionIntensity = extraData.emotionData.intensity || 0;
                score += emotionIntensity * 0.2;
            }

            // 基于特殊关键词
            const importantKeywords = ['重要', '问题', '帮助', '学习', '记住'];
            const hasImportantKeyword = importantKeywords.some(keyword => 
                userMessage.includes(keyword) || aiResponse.includes(keyword)
            );
            if (hasImportantKeyword) {
                score += 0.15;
            }

            return Math.min(score, 1.0);

        } catch (error) {
            return 0.5; // 默认分数
        }
    }

    /**
     * 获取最近的记忆片段（按时间排序）- 使用数据库存储
     */
    async getRecentMemories(userId, personaName = null, limit = 10) {
        try {
            // 首先尝试从新的 conversation_history 表获取数据
            let conversations = [];

            if (personaName) {
                // 获取最近的对话组，每组包含user和assistant消息
                const conversationGroups = await this.dbAll(`
                    SELECT conversation_id,
                           MAX(CASE WHEN speaker = 'user' THEN timestamp END) as user_timestamp,
                           MAX(CASE WHEN speaker = 'assistant' THEN timestamp END) as assistant_timestamp,
                           MAX(timestamp) as latest_timestamp
                    FROM conversation_history
                    WHERE user_id = ? AND persona_name = ? AND speaker IN ('user', 'assistant')
                    GROUP BY conversation_id
                    HAVING user_timestamp IS NOT NULL AND assistant_timestamp IS NOT NULL
                    ORDER BY latest_timestamp DESC
                    LIMIT ?
                `, [userId, personaName, Math.ceil(limit / 2)]); // 每组对话包含2条消息

                if (conversationGroups.length > 0) {
                    const conversationIds = conversationGroups.map(conv => conv.conversation_id);
                    const placeholders = conversationIds.map(() => '?').join(',');

                    // 获取这些对话组的所有消息
                    const messages = await this.dbAll(`
                        SELECT id, speaker, content, timestamp, importance_score, conversation_id, emotion_state
                        FROM conversation_history
                        WHERE user_id = ? AND persona_name = ?
                          AND conversation_id IN (${placeholders})
                          AND speaker IN ('user', 'assistant')
                        ORDER BY timestamp ASC
                    `, [userId, personaName, ...conversationIds]);

                    // 按对话组重新组织数据
                    const groupedMessages = {};
                    messages.forEach(msg => {
                        if (!groupedMessages[msg.conversation_id]) {
                            groupedMessages[msg.conversation_id] = { user: null, assistant: null };
                        }
                        if (msg.speaker === 'user') {
                            groupedMessages[msg.conversation_id].user = msg;
                        } else if (msg.speaker === 'assistant') {
                            groupedMessages[msg.conversation_id].assistant = msg;
                        }
                    });

                    // 转换为MCP工具期望的格式
                    conversations = conversationGroups.map(group => {
                        const msgs = groupedMessages[group.conversation_id];
                        return {
                            id: group.conversation_id,
                            content: `用户: ${msgs.user?.content || ''}\nAI: ${msgs.assistant?.content || ''}`,
                            original_content: {
                                user_message: msgs.user?.content || '',
                                ai_response: msgs.assistant?.content || ''
                            },
                            summary_info: {},
                            importance_score: Math.max(msgs.user?.importance_score || 0, msgs.assistant?.importance_score || 0),
                            creation_time: group.latest_timestamp,
                            memory_type: 'conversation',
                            access_count: 1,
                            user_id: userId,
                            persona_name: personaName,
                            emotional_context: JSON.parse(msgs.user?.emotion_state || '{}')
                        };
                    });
                }
            }

            // 如果新表没有数据，回退到旧表
            if (conversations.length === 0) {
                this.logger.debug('记忆嵌入服务', '新表无数据，尝试从旧表获取');

                let query, params;
                if (personaName) {
                    query = `
                        SELECT * FROM recent_conversations
                        WHERE persona_name = ? AND user_id = ?
                        ORDER BY creation_time DESC
                        LIMIT ?
                    `;
                    params = [personaName, userId, limit];
                } else {
                    query = `
                        SELECT * FROM recent_conversations
                        WHERE user_id = ?
                        ORDER BY creation_time DESC
                        LIMIT ?
                    `;
                    params = [userId, limit];
                }

                const oldMemories = await this.dbAll(query, params);
                conversations = oldMemories.map(memory => ({
                    id: memory.id,
                    content: memory.content,
                    original_content: JSON.parse(memory.original_content || '{}'),
                    summary_info: JSON.parse(memory.summary_info || '{}'),
                    importance_score: memory.importance_score || 0.5,
                    creation_time: memory.creation_time,
                    memory_type: memory.memory_type || 'conversation',
                    access_count: memory.access_count || 1,
                    user_id: memory.user_id,
                    persona_name: memory.persona_name,
                    emotional_context: JSON.parse(memory.emotional_context || '{}')
                }));
            }

            this.logger.info('记忆嵌入服务', `从数据库获取到用户 ${userId} ${personaName ? `(${personaName})` : ''} 的 ${conversations.length} 条最近记忆`);
            return conversations;

        } catch (error) {
            this.logger.error('记忆嵌入服务', `获取最近记忆失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 使用OpenAI Tools概括内容
     */
    async summarizeContentWithTools(userMessage, aiResponse, timeoutMs = 20000) {
        try {
            const startTime = Date.now();

            // 创建超时Promise
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('概括超时')), timeoutMs);
            });

            // 创建概括Promise
            const summarizePromise = this.openaiService.makeToolCall(
                `请概括以下对话内容：

用户问题：${userMessage}
AI回复：${aiResponse}

要求：
1. 用户问题概括：最多20词，提取核心问题
2. AI回复概括：最多50词，提取关键信息
3. 如果内容过长，用...省略号表示`,
                [{
                    type: "function",
                    function: {
                        name: "summarize_conversation",
                        description: "概括对话内容",
                        parameters: {
                            type: "object",
                            properties: {
                                user_summary: {
                                    type: "string",
                                    description: "用户问题概括，最多20词"
                                },
                                ai_summary: {
                                    type: "string",
                                    description: "AI回复概括，最多50词"
                                }
                            },
                            required: ["user_summary", "ai_summary"]
                        }
                    }
                }],
                "summarize_conversation"
            );

            // 竞争执行
            const result = await Promise.race([summarizePromise, timeoutPromise]);

            const processingTime = Date.now() - startTime;
            this.logger.info('内容概括', `OpenAI概括完成，耗时: ${processingTime}ms`);

            return {
                userSummary: result.user_summary,
                aiSummary: result.ai_summary,
                processingTime
            };

        } catch (error) {
            this.logger.warning('内容概括', `概括失败，使用字符截断: ${error.message}`);

            // 超时或失败时使用字符截断
            const userSummary = userMessage.length > 60 ? userMessage.substring(0, 60) + '...' : userMessage;
            const aiSummary = aiResponse.length > 150 ? aiResponse.substring(0, 150) + '...' : aiResponse;

            return {
                userSummary,
                aiSummary,
                processingTime: 0,
                fallback: true
            };
        }
    }

    /**
     * 保存AI反馈结果到数据库（替代文件存储）
     */
    async saveRecentMemoryToDatabase(userId, personaName, userMessage, aiResponse, emotionContext = null) {
        try {
            // 生成北京时间
            const beijingTime = this.getBeijingTimeString();

            // 生成对话摘要内容
            const content = `${userId}说${userMessage}，${personaName}回复${aiResponse}。`;

            // 构建原始内容（移除用户ID格式化）
            const originalContent = {
                user_message: userMessage,
                ai_response: aiResponse
            };

            // 构建摘要信息（如果有的话）
            const summaryInfo = {
                user_summary: `用户消息: ${userMessage.substring(0, 50)}${userMessage.length > 50 ? '...' : ''}`,
                ai_summary: `AI回复: ${aiResponse.substring(0, 50)}${aiResponse.length > 50 ? '...' : ''}`,
                processing_time: Date.now(),
                fallback_used: false
            };

            // 获取北京时间并格式化为: YYYY年MM月DD日 HH:mm 北京时间
            const now = new Date();
            const beijingDate = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
            const year = beijingDate.getFullYear();
            const month = String(beijingDate.getMonth() + 1).padStart(2, '0');
            const day = String(beijingDate.getDate()).padStart(2, '0');
            const hour = String(beijingDate.getHours()).padStart(2, '0');
            const minute = String(beijingDate.getMinutes()).padStart(2, '0');
            const second = String(beijingDate.getSeconds()).padStart(2, '0');
            const formattedBeijingTime = `${year}年${month}月${day}日 ${hour}:${minute}:${second} 北京时间`;

            // 注意：不再使用旧的概况生成，新的智能概括在 storeConversationEmbedding 中处理
            // 构建简单的内容用于最近记忆存储
            const finalContent = `${userId}: ${userMessage}\n${personaName}: ${aiResponse}`;
            const finalSummaryInfo = {
                note: '最近记忆不再使用AI概况，只保存基本对话记录'
            };

            // 插入到数据库
            await this.dbRun(`
                INSERT INTO recent_conversations (
                    persona_name, user_id, content, original_content, summary_info,
                    importance_score, creation_time, memory_type, access_count, emotional_context
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                personaName || 'Assistant',
                userId,
                finalContent,
                JSON.stringify(originalContent),
                JSON.stringify(finalSummaryInfo),
                0.6,
                formattedBeijingTime,
                'conversation',
                1,
                JSON.stringify(emotionContext || {})
            ]);

            // 清理旧记录，保留每个助手-用户组合的最近50条记录
            await this.dbRun(`
                DELETE FROM recent_conversations
                WHERE persona_name = ? AND user_id = ? AND id NOT IN (
                    SELECT id FROM recent_conversations
                    WHERE persona_name = ? AND user_id = ?
                    ORDER BY creation_time DESC
                    LIMIT 50
                )
            `, [personaName || 'Assistant', userId, personaName || 'Assistant', userId]);

            this.logger.info('记忆嵌入服务', `已保存用户 ${userId} (${personaName}) 的新记忆到数据库`);
            return true;

        } catch (error) {
            this.logger.error('记忆嵌入服务', `保存记忆到数据库失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 获取北京时间字符串
     */
    getBeijingTimeString() {
        const now = new Date();
        const beijingDate = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
        const year = beijingDate.getFullYear();
        const month = String(beijingDate.getMonth() + 1).padStart(2, '0');
        const day = String(beijingDate.getDate()).padStart(2, '0');
        const hour = String(beijingDate.getHours()).padStart(2, '0');
        const minute = String(beijingDate.getMinutes()).padStart(2, '0');
        return `${year}年${month}月${day}日 ${hour}:${minute} 北京时间`;
    }

    /**
     * 兼容性方法：保持原有的方法名
     */
    async saveRecentMemoryToFile(userId, personaName, userMessage, aiResponse, emotionContext = null) {
        return await this.saveRecentMemoryToDatabase(userId, personaName, userMessage, aiResponse, emotionContext);
    }
}

module.exports = MemoryEmbeddingService;