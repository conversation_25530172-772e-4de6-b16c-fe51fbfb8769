/**
 * Image Sender Plugin
 * 发送图片示例插件
 * 演示如何发送各种类型的图片：网络图片、本地图片、Base64图片
 */

const BasePlugin = require('./base_plugin');
const fs = require('fs');
const path = require('path');

class ImageSenderPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('ImageSender', adapter, logger, config);

        this.priority = 60; // 中等优先级
        this.permission = 'all'; // 所有用户可用
        this.supportedTypes = ['message'];

        // 插件元信息
        this.meta = {
            version: '1.0.0',
            author: 'VCPToolBox',
            description: '图片发送功能插件',
            usage: '发送图片相关关键词来测试图片发送功能',
            example: '发送网络图片'
        };

        // 图片命令配置
        this.imageCommands = {
            // 网络图片示例
            '发送网络图片': {
                type: 'url',
                url: 'https://picsum.photos/400/300', // 随机图片API
                description: '发送一张随机网络图片'
            },
            '发送猫咪图片': {
                type: 'url',
                url: 'https://api.thecatapi.com/v1/images/search',
                description: '发送一张随机猫咪图片',
                needsApiCall: true
            },
            // 本地图片示例
            '发送本地图片': {
                type: 'local',
                path: './assets/sample.jpg', // 需要在OneBot11目录下创建assets文件夹和示例图片
                description: '发送本地示例图片'
            },
            // Base64图片示例
            '发送表情包': {
                type: 'base64',
                description: '发送一个简单的表情包',
                generateBase64: true
            }
        };

        this.logger.info('Plugin', 'Image sender plugin initialized');
    }

    /**
     * 检查是否应该处理此消息
     */
    async shouldHandle(context) {
        // 只处理文本消息
        if (!context.message || typeof context.message !== 'string') {
            return false;
        }

        const message = context.message.trim();
        
        // 检查是否是图片发送命令
        return Object.keys(this.imageCommands).some(cmd => 
            message.includes(cmd) || message === cmd
        );
    }

    /**
     * 处理图片发送命令
     */
    async handle(context) {
        const message = context.message.trim();

        // 查找匹配的命令
        for (const [command, config] of Object.entries(this.imageCommands)) {
            if (message.includes(command) || message === command) {
                this.logger.info('Plugin', `Processing image command: ${command}`);

                try {
                    await this.sendImageByType(context, config);
                    return {
                        handled: true,
                        command: command,
                        type: config.type
                    };
                } catch (error) {
                    this.logger.error('Plugin', `Failed to send image: ${error.message}`);
                    await this.reply(context, `发送图片失败: ${error.message}`);
                    return {
                        handled: true,
                        error: error.message
                    };
                }
            }
        }

        return { handled: false };
    }

    /**
     * 根据类型发送图片
     */
    async sendImageByType(context, config) {
        let imageMessage;

        switch (config.type) {
            case 'url':
                imageMessage = await this.createUrlImage(config);
                break;
            case 'local':
                imageMessage = await this.createLocalImage(config);
                break;
            case 'base64':
                imageMessage = await this.createBase64Image(config);
                break;
            default:
                throw new Error(`Unsupported image type: ${config.type}`);
        }

        // 发送图片消息
        await this.reply(context, imageMessage);
        this.logger.info('Plugin', `Image sent successfully: ${config.type}`);
    }

    /**
     * 创建网络图片消息
     */
    async createUrlImage(config) {
        if (config.needsApiCall) {
            // 对于需要API调用的情况（如猫咪API）
            const response = await fetch(config.url);
            const data = await response.json();
            const imageUrl = Array.isArray(data) ? data[0].url : data.url;
            
            return [
                { type: 'text', data: { text: config.description + '\n' } },
                { type: 'image', data: { file: imageUrl } }
            ];
        } else {
            // 直接使用URL
            return [
                { type: 'text', data: { text: config.description + '\n' } },
                { type: 'image', data: { file: config.url } }
            ];
        }
    }

    /**
     * 创建本地图片消息
     */
    async createLocalImage(config) {
        const imagePath = path.resolve(config.path);
        
        // 检查文件是否存在
        if (!fs.existsSync(imagePath)) {
            throw new Error(`Local image file not found: ${imagePath}`);
        }

        return [
            { type: 'text', data: { text: config.description + '\n' } },
            { type: 'image', data: { file: `file://${imagePath}` } }
        ];
    }

    /**
     * 创建Base64图片消息
     */
    async createBase64Image(config) {
        let base64Data;

        if (config.generateBase64) {
            // 生成一个简单的表情包（1x1像素的红色图片作为示例）
            base64Data = this.generateSimpleEmoji();
        } else if (config.base64) {
            base64Data = config.base64;
        } else {
            throw new Error('No base64 data provided');
        }

        return [
            { type: 'text', data: { text: config.description + '\n' } },
            { type: 'image', data: { file: `base64://${base64Data}` } }
        ];
    }

    /**
     * 生成简单的表情包（示例）
     */
    generateSimpleEmoji() {
        // 这是一个1x1像素红色PNG图片的base64编码
        // 在实际应用中，你可以使用更复杂的图片生成逻辑
        return 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    }

    /**
     * 添加新的图片命令
     */
    addImageCommand(command, config) {
        this.imageCommands[command] = config;
        this.logger.info('Plugin', `Added image command: ${command}`);
    }

    /**
     * 移除图片命令
     */
    removeImageCommand(command) {
        if (this.imageCommands[command]) {
            delete this.imageCommands[command];
            this.logger.info('Plugin', `Removed image command: ${command}`);
            return true;
        }
        return false;
    }

    /**
     * 获取所有图片命令
     */
    getImageCommands() {
        return Object.keys(this.imageCommands);
    }

    /**
     * 获取插件统计信息
     */
    getStats() {
        return {
            ...super.getStats(),
            commandCount: Object.keys(this.imageCommands).length,
            availableCommands: this.getImageCommands()
        };
    }
}

module.exports = ImageSenderPlugin;
