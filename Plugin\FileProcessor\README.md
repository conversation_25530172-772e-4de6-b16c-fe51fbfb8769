# FileProcessor 图文件处理插件

## 📋 功能概述

FileProcessor 是一个强大的图文件处理插件，支持从markdown格式的链接中提取文件，并使用AI模型进行智能分析。

## 🔧 主要功能

### 1. Markdown链接提取
- 自动识别markdown格式的图片链接：`![描述](链接地址)`
- 支持网络链接和本地绝对路径
- 必须包含有效的文件后缀名
- 支持处理1-4个链接

### 2. 图片处理
- **支持格式**: .jpg, .jpeg, .png, .gif, .bmp, .webp, .svg, .tiff, .ico
- **处理方式**: 本地文件直接读取或网络下载，转换为base64格式
- **MIME检测**: 自动识别图片类型，生成正确的data URL格式
- **AI分析**: 使用视觉模型（如gpt-4o, gpt-4-vision-preview）进行图片内容分析
- **输出**: base64数据 + AI分析结果

### 3. 文档处理
- **支持格式**: .txt, .md, .pdf, .doc, .docx, .rtf, .csv, .json, .xml, .html
- **处理方式**: 提取文本内容（PDF和Word需要额外库支持）
- **AI分析**: 使用文本模型进行内容分析
- **输出**: 文本内容 + AI分析结果

## ⚙️ 配置说明

### 环境变量配置

```env
# API配置
FILEPROCESSOR_API_URL=https://api.openai.com/v1/chat/completions
FILEPROCESSOR_API_KEY=your_api_key_here

# 模型配置（用逗号分隔）
FILEPROCESSOR_IMAGE_MODELS=gpt-4o,gpt-4-vision-preview,claude-3-sonnet
FILEPROCESSOR_FILE_MODELS=gpt-4o,gpt-4-turbo,claude-3-sonnet

# 文件下载配置
FILEPROCESSOR_MAX_FILE_SIZE=52428800  # 50MB (字节)
FILEPROCESSOR_DOWNLOAD_TIMEOUT=30  # 30秒

# API调用超时配置
FILEPROCESSOR_API_TIMEOUT=120  # 120秒
```

### 模型选择逻辑
- **图片文件**: 使用 `FILEPROCESSOR_IMAGE_MODELS` 中的模型
- **文档文件**: 使用 `FILEPROCESSOR_FILE_MODELS` 中的模型
- **自动选择**: 如果未指定模型，将从对应列表中随机选择
- **手动指定**: 可以通过参数指定特定模型

## 📝 使用示例

### VCP插件调用
```javascript
const fileProcessor = new FileProcessor();

const result = await fileProcessor.execute({
    content: "请分析这些文件：![一只可爱的猫](http://localhost:6005/images/cat.jpg) 和 ![技术文档](http://example.com/doc.pdf)",
    prompt: "请详细分析文件内容并总结要点",
    model: "gpt-4o"  // 可选，不指定则自动选择
});
```

### MCP插件调用
```javascript
const result = await mcpManager.executeMcpPlugin('FileProcessor', {
    content: "![一只可爱的动漫风格猫猫，毛色柔和，眼神温柔，坐在阳光下，背景简单干净，氛围安静温馨](http://localhost:6005/pw=vcptools123/images/novelaigen/453fd497-e7ac-4392-98c1-26818ecee77a.webp)",
    prompt: "请分析这张图片的艺术风格和情感表达"
});
```

### OpenAI Tools格式
```json
{
    "tool_calls": [
        {
            "function": {
                "name": "FileProcessor",
                "arguments": {
                    "content": "![示例图片](http://example.com/image.jpg)",
                    "prompt": "请分析图片内容"
                }
            }
        }
    ]
}
```

## 📊 返回格式

### 成功响应
```json
{
    "type": "file_processing",
    "status": "success",
    "message": "文件处理完成",
    "data": {
        "processed_count": 2,
        "results": [
            {
                "url": "http://example.com/image.jpg",
                "alt": "示例图片",
                "type": "image",
                "status": "success",
                "model": "gpt-4o",
                "base64_data": "iVBORw0KGgoAAAANSUhEUgAA...",
                "analysis": "这是一张展示...",
                "tokens_used": {"prompt_tokens": 100, "completion_tokens": 50}
            }
        ],
        "original_content": "![示例图片](http://example.com/image.jpg)",
        "prompt_used": "请分析这个文件的内容",
        "processing_info": {
            "timestamp": "2024-01-15T10:30:15.000Z",
            "plugin_version": "1.0.0"
        }
    }
}
```

### 错误响应
```json
{
    "type": "file_processing",
    "status": "error",
    "message": "未找到有效的markdown格式链接",
    "data": {
        "content": "无效内容",
        "error": "未找到有效的markdown格式链接"
    }
}
```

## 🔧 依赖安装

### 必需依赖
```bash
npm install axios form-data
```

### 可选依赖（用于文档处理）
```bash
# PDF处理
npm install pdf-parse

# Word文档处理
npm install textract
```

## ⚠️ 注意事项

1. **链接格式**: 必须是标准的markdown图片语法 `![alt](url)`
2. **文件后缀**: 链接必须包含有效的文件后缀名
3. **文件大小**: 默认限制50MB，可通过环境变量调整
4. **模型限制**: 确保配置的模型支持相应的功能（图片模型需要视觉能力）
5. **API配额**: 注意API调用的费用和限制
6. **临时文件**: 插件会自动清理下载的临时文件

## 🚀 扩展功能

- 支持更多文件格式
- 批量处理优化
- 缓存机制
- 文件预处理
- 自定义分析模板
