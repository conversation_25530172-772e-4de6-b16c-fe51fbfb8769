# 多AI协商插件 (MultiAIConsensus)

## 概述

多AI协商插件是一个高级的AI工具，可以同时向多个不同的AI模型发送相同的查询，收集它们的响应，并生成综合性的总结。这个插件支持异步并发请求，可以帮助用户获得更全面、更平衡的AI回答。

## 功能特性

### 🚀 核心功能
- **多模型并行查询**：同时向多个AI模型发送请求
- **智能总结生成**：自动分析和汇总多个AI的回答
- **异步并发处理**：高效的并行请求处理
- **错误容错机制**：即使部分模型失败也能正常工作
- **灵活的模型选择**：支持自定义模型列表

### 🛠️ 技术特性
- **VCP和MCP双重支持**：同时支持VCP和MCP协议
- **配置继承**：可以继承主配置文件的API设置
- **性能监控**：记录每个模型的响应时间
- **Token使用追踪**：监控API调用的Token消耗

## 安装配置

### 1. 基础配置

复制配置文件：
```bash
cp Plugin/MultiAIConsensus/config.env.example Plugin/MultiAIConsensus/config.env
```

### 2. 配置参数

编辑 `config.env` 文件：

```env
# API配置 - 继承主配置或自定义
MULTI_AI_API_URL=https://yuanplus.cloud
MULTI_AI_API_KEY=sk-your-api-key-here

# 并发配置
MULTI_AI_MAX_CONCURRENT=5
MULTI_AI_TIMEOUT=30000

# 可用模型列表 - 用逗号分隔
MULTI_AI_AVAILABLE_MODELS=gpt-4o,gpt-4o-mini,claude-3-5-sonnet-20241022,gemini-2.5-flash-preview-05-20,deepseek-chat

# 默认使用的模型 - 用逗号分隔
MULTI_AI_DEFAULT_MODELS=gpt-4o-mini,claude-3-5-sonnet-20241022

# 是否启用AI总结功能
MULTI_AI_ENABLE_SUMMARY=true

# 用于生成总结的模型
MULTI_AI_SUMMARY_MODEL=gpt-4o-mini
```

### 3. 主配置继承

如果不创建单独的配置文件，插件会自动继承主配置文件 `config.env` 中的以下设置：
- `API_URL` 或 `YUANPLUS_API_KEY`
- `API_Key` 或 `YUANPLUS_API_KEY`

## 使用方法

### VCP调用方式

#### 基础用法
```
MultiAIConsensus:「query」什么是人工智能的未来发展趋势？「/query」
```

#### 指定模型
```
MultiAIConsensus:「query」分析当前经济形势「/query」「models」["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-2.5-flash-preview-05-20"]「/models」
```

#### 详细模式
```
MultiAIConsensus:「query」如何学习编程？「/query」「includeDetails」true「/includeDetails」「maxModels」3「/maxModels」
```

#### 自定义API
```
MultiAIConsensus:「query」解释量子计算原理「/query」「customApiUrl」https://your-api.com「/customApiUrl」「customApiKey」sk-your-key「/customApiKey」
```

### MCP调用方式

#### 多AI协商
```json
{
  "tool": "multi_ai_consensus",
  "query": "什么是人工智能的未来发展趋势？",
  "models": ["gpt-4o-mini", "claude-3-5-sonnet-20241022"],
  "max_models": 3,
  "include_details": true
}
```

#### 获取可用模型
```json
{
  "tool": "get_available_models"
}
```

#### 配置模型列表
```json
{
  "tool": "configure_models",
  "available_models": ["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-2.5-flash-preview-05-20"],
  "default_models": ["gpt-4o", "claude-3-5-sonnet-20241022"]
}
```

## 参数说明

### 主要参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `query` | string | 是 | - | 要询问的问题或任务 |
| `models` | array | 否 | 配置中的默认模型 | 要使用的AI模型列表 |
| `maxModels` | number | 否 | 3 | 最大使用的模型数量 (1-5) |
| `includeDetails` | boolean | 否 | false | 是否包含每个模型的详细响应 |
| `customApiUrl` | string | 否 | - | 自定义API URL |
| `customApiKey` | string | 否 | - | 自定义API密钥 |

### 配置参数

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `MULTI_AI_API_URL` | API服务器地址 | 继承主配置 |
| `MULTI_AI_API_KEY` | API密钥 | 继承主配置 |
| `MULTI_AI_MAX_CONCURRENT` | 最大并发请求数 | 5 |
| `MULTI_AI_TIMEOUT` | 请求超时时间(ms) | 30000 |
| `MULTI_AI_AVAILABLE_MODELS` | 可用模型列表 | 内置默认列表 |
| `MULTI_AI_DEFAULT_MODELS` | 默认模型列表 | gpt-4o-mini,claude-3-5-sonnet-20241022 |
| `MULTI_AI_ENABLE_SUMMARY` | 启用AI总结 | true |
| `MULTI_AI_SUMMARY_MODEL` | 总结生成模型 | gpt-4o-mini |

## 支持的模型

### 默认支持的模型
- `gpt-4o`
- `gpt-4o-mini`
- `claude-3-5-sonnet-20241022`
- `gemini-2.5-flash-preview-05-20`
- `deepseek-chat`
- `grok-3-beta`

### 添加自定义模型

在配置文件中修改 `MULTI_AI_AVAILABLE_MODELS`：
```env
MULTI_AI_AVAILABLE_MODELS=gpt-4o,your-custom-model,another-model
```

## 响应格式

### 基础响应
```json
{
  "success": true,
  "query": "什么是人工智能？",
  "totalModels": 2,
  "successfulResponses": 2,
  "failedResponses": 0,
  "summary": "综合总结内容...",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 详细响应 (includeDetails=true)
```json
{
  "success": true,
  "query": "什么是人工智能？",
  "totalModels": 2,
  "successfulResponses": 2,
  "failedResponses": 0,
  "summary": "综合总结内容...",
  "responses": [
    {
      "success": true,
      "model": "gpt-4o-mini",
      "content": "AI回答内容...",
      "responseTime": 1500,
      "tokenUsage": {
        "prompt_tokens": 20,
        "completion_tokens": 150,
        "total_tokens": 170
      },
      "timestamp": "2024-01-15T10:30:00.000Z"
    }
  ],
  "errors": [],
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 管理面板配置

### 在Sentra管理面板中配置

1. **导航到插件管理**
   - 进入管理面板 → 插件管理 → MultiAIConsensus

2. **配置可用模型**
   ```
   可用模型列表: gpt-4o,gpt-4o-mini,claude-3-5-sonnet-20241022
   默认模型: gpt-4o-mini,claude-3-5-sonnet-20241022
   ```

3. **API设置**
   ```
   API URL: https://yuanplus.cloud
   API Key: sk-your-api-key-here
   ```

4. **高级设置**
   ```
   最大并发数: 5
   请求超时: 30000ms
   启用总结: true
   总结模型: gpt-4o-mini
   ```

## 使用案例

### 案例1：技术问题对比分析
```
输入：MultiAIConsensus:「query」比较React和Vue.js的优缺点「/query」

输出：收集多个AI模型的分析，提供全面的技术对比
```

### 案例2：创意写作灵感
```
输入：MultiAIConsensus:「query」为科幻小说提供三个创新的故事概念「/query」「models」["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-2.5-flash-preview-05-20"]「/models」

输出：汇集多个AI的创意想法，提供丰富的写作灵感
```

### 案例3：学术研究辅助
```
输入：MultiAIConsensus:「query」分析气候变化对全球经济的影响「/query」「includeDetails」true「/includeDetails」

输出：详细的多角度分析，包含每个模型的具体观点
```

## 错误处理

### 常见错误类型
1. **API密钥无效**：检查配置文件中的API密钥
2. **模型不可用**：验证模型名称是否正确
3. **网络超时**：调整 `MULTI_AI_TIMEOUT` 设置
4. **并发限制**：降低 `MULTI_AI_MAX_CONCURRENT` 值

### 容错机制
- 部分模型失败时，仍返回成功模型的结果
- 自动重试机制（可配置）
- 详细的错误日志记录

## 性能优化

### 建议配置
- **低延迟场景**：减少模型数量，使用快速模型如 `gpt-4o-mini`
- **高质量场景**：使用多个高级模型如 `gpt-4o`, `claude-3-5-sonnet-20241022`
- **成本优化**：主要使用 mini 版本模型

### 监控指标
- 响应时间
- 成功率
- Token消耗
- 并发负载

## 开发者信息

- **版本**：1.0.0
- **作者**：VCPToolBox
- **许可证**：MIT
- **GitHub**：[VCPToolBox Repository](https://github.com/your-repo/VCPToolBox)

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持VCP和MCP双协议
- 实现多AI并发查询
- 添加智能总结功能
- 完整的配置管理系统

## 技术支持

如有问题或建议，请：
1. 查看插件日志文件
2. 检查配置文件设置
3. 提交Issue到GitHub仓库
4. 联系技术支持团队

---

**注意**：使用此插件会产生API调用费用，请根据实际需求合理配置模型数量和使用频率。 

## 概述

多AI协商插件是一个高级的AI工具，可以同时向多个不同的AI模型发送相同的查询，收集它们的响应，并生成综合性的总结。这个插件支持异步并发请求，可以帮助用户获得更全面、更平衡的AI回答。

## 功能特性

### 🚀 核心功能
- **多模型并行查询**：同时向多个AI模型发送请求
- **智能总结生成**：自动分析和汇总多个AI的回答
- **异步并发处理**：高效的并行请求处理
- **错误容错机制**：即使部分模型失败也能正常工作
- **灵活的模型选择**：支持自定义模型列表

### 🛠️ 技术特性
- **VCP和MCP双重支持**：同时支持VCP和MCP协议
- **配置继承**：可以继承主配置文件的API设置
- **性能监控**：记录每个模型的响应时间
- **Token使用追踪**：监控API调用的Token消耗

## 安装配置

### 1. 基础配置

复制配置文件：
```bash
cp Plugin/MultiAIConsensus/config.env.example Plugin/MultiAIConsensus/config.env
```

### 2. 配置参数

编辑 `config.env` 文件：

```env
# API配置 - 继承主配置或自定义
MULTI_AI_API_URL=https://yuanplus.cloud
MULTI_AI_API_KEY=sk-your-api-key-here

# 并发配置
MULTI_AI_MAX_CONCURRENT=5
MULTI_AI_TIMEOUT=30000

# 可用模型列表 - 用逗号分隔
MULTI_AI_AVAILABLE_MODELS=gpt-4o,gpt-4o-mini,claude-3-5-sonnet-20241022,gemini-2.5-flash-preview-05-20,deepseek-chat

# 默认使用的模型 - 用逗号分隔
MULTI_AI_DEFAULT_MODELS=gpt-4o-mini,claude-3-5-sonnet-20241022

# 是否启用AI总结功能
MULTI_AI_ENABLE_SUMMARY=true

# 用于生成总结的模型
MULTI_AI_SUMMARY_MODEL=gpt-4o-mini
```

### 3. 主配置继承

如果不创建单独的配置文件，插件会自动继承主配置文件 `config.env` 中的以下设置：
- `API_URL` 或 `YUANPLUS_API_KEY`
- `API_Key` 或 `YUANPLUS_API_KEY`

## 使用方法

### VCP调用方式

#### 基础用法
```
MultiAIConsensus:「query」什么是人工智能的未来发展趋势？「/query」
```

#### 指定模型
```
MultiAIConsensus:「query」分析当前经济形势「/query」「models」["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-2.5-flash-preview-05-20"]「/models」
```

#### 详细模式
```
MultiAIConsensus:「query」如何学习编程？「/query」「includeDetails」true「/includeDetails」「maxModels」3「/maxModels」
```

#### 自定义API
```
MultiAIConsensus:「query」解释量子计算原理「/query」「customApiUrl」https://your-api.com「/customApiUrl」「customApiKey」sk-your-key「/customApiKey」
```

### MCP调用方式

#### 多AI协商
```json
{
  "tool": "multi_ai_consensus",
  "query": "什么是人工智能的未来发展趋势？",
  "models": ["gpt-4o-mini", "claude-3-5-sonnet-20241022"],
  "max_models": 3,
  "include_details": true
}
```

#### 获取可用模型
```json
{
  "tool": "get_available_models"
}
```

#### 配置模型列表
```json
{
  "tool": "configure_models",
  "available_models": ["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-2.5-flash-preview-05-20"],
  "default_models": ["gpt-4o", "claude-3-5-sonnet-20241022"]
}
```

## 参数说明

### 主要参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `query` | string | 是 | - | 要询问的问题或任务 |
| `models` | array | 否 | 配置中的默认模型 | 要使用的AI模型列表 |
| `maxModels` | number | 否 | 3 | 最大使用的模型数量 (1-5) |
| `includeDetails` | boolean | 否 | false | 是否包含每个模型的详细响应 |
| `customApiUrl` | string | 否 | - | 自定义API URL |
| `customApiKey` | string | 否 | - | 自定义API密钥 |

### 配置参数

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `MULTI_AI_API_URL` | API服务器地址 | 继承主配置 |
| `MULTI_AI_API_KEY` | API密钥 | 继承主配置 |
| `MULTI_AI_MAX_CONCURRENT` | 最大并发请求数 | 5 |
| `MULTI_AI_TIMEOUT` | 请求超时时间(ms) | 30000 |
| `MULTI_AI_AVAILABLE_MODELS` | 可用模型列表 | 内置默认列表 |
| `MULTI_AI_DEFAULT_MODELS` | 默认模型列表 | gpt-4o-mini,claude-3-5-sonnet-20241022 |
| `MULTI_AI_ENABLE_SUMMARY` | 启用AI总结 | true |
| `MULTI_AI_SUMMARY_MODEL` | 总结生成模型 | gpt-4o-mini |

## 支持的模型

### 默认支持的模型
- `gpt-4o`
- `gpt-4o-mini`
- `claude-3-5-sonnet-20241022`
- `gemini-2.5-flash-preview-05-20`
- `deepseek-chat`
- `grok-3-beta`

### 添加自定义模型

在配置文件中修改 `MULTI_AI_AVAILABLE_MODELS`：
```env
MULTI_AI_AVAILABLE_MODELS=gpt-4o,your-custom-model,another-model
```

## 响应格式

### 基础响应
```json
{
  "success": true,
  "query": "什么是人工智能？",
  "totalModels": 2,
  "successfulResponses": 2,
  "failedResponses": 0,
  "summary": "综合总结内容...",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 详细响应 (includeDetails=true)
```json
{
  "success": true,
  "query": "什么是人工智能？",
  "totalModels": 2,
  "successfulResponses": 2,
  "failedResponses": 0,
  "summary": "综合总结内容...",
  "responses": [
    {
      "success": true,
      "model": "gpt-4o-mini",
      "content": "AI回答内容...",
      "responseTime": 1500,
      "tokenUsage": {
        "prompt_tokens": 20,
        "completion_tokens": 150,
        "total_tokens": 170
      },
      "timestamp": "2024-01-15T10:30:00.000Z"
    }
  ],
  "errors": [],
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 管理面板配置

### 在Sentra管理面板中配置

1. **导航到插件管理**
   - 进入管理面板 → 插件管理 → MultiAIConsensus

2. **配置可用模型**
   ```
   可用模型列表: gpt-4o,gpt-4o-mini,claude-3-5-sonnet-20241022
   默认模型: gpt-4o-mini,claude-3-5-sonnet-20241022
   ```

3. **API设置**
   ```
   API URL: https://yuanplus.cloud
   API Key: sk-your-api-key-here
   ```

4. **高级设置**
   ```
   最大并发数: 5
   请求超时: 30000ms
   启用总结: true
   总结模型: gpt-4o-mini
   ```

## 使用案例

### 案例1：技术问题对比分析
```
输入：MultiAIConsensus:「query」比较React和Vue.js的优缺点「/query」

输出：收集多个AI模型的分析，提供全面的技术对比
```

### 案例2：创意写作灵感
```
输入：MultiAIConsensus:「query」为科幻小说提供三个创新的故事概念「/query」「models」["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-2.5-flash-preview-05-20"]「/models」

输出：汇集多个AI的创意想法，提供丰富的写作灵感
```

### 案例3：学术研究辅助
```
输入：MultiAIConsensus:「query」分析气候变化对全球经济的影响「/query」「includeDetails」true「/includeDetails」

输出：详细的多角度分析，包含每个模型的具体观点
```

## 错误处理

### 常见错误类型
1. **API密钥无效**：检查配置文件中的API密钥
2. **模型不可用**：验证模型名称是否正确
3. **网络超时**：调整 `MULTI_AI_TIMEOUT` 设置
4. **并发限制**：降低 `MULTI_AI_MAX_CONCURRENT` 值

### 容错机制
- 部分模型失败时，仍返回成功模型的结果
- 自动重试机制（可配置）
- 详细的错误日志记录

## 性能优化

### 建议配置
- **低延迟场景**：减少模型数量，使用快速模型如 `gpt-4o-mini`
- **高质量场景**：使用多个高级模型如 `gpt-4o`, `claude-3-5-sonnet-20241022`
- **成本优化**：主要使用 mini 版本模型

### 监控指标
- 响应时间
- 成功率
- Token消耗
- 并发负载

## 开发者信息

- **版本**：1.0.0
- **作者**：VCPToolBox
- **许可证**：MIT
- **GitHub**：[VCPToolBox Repository](https://github.com/your-repo/VCPToolBox)

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持VCP和MCP双协议
- 实现多AI并发查询
- 添加智能总结功能
- 完整的配置管理系统

## 技术支持

如有问题或建议，请：
1. 查看插件日志文件
2. 检查配置文件设置
3. 提交Issue到GitHub仓库
4. 联系技术支持团队

---

**注意**：使用此插件会产生API调用费用，请根据实际需求合理配置模型数量和使用频率。 
 
 