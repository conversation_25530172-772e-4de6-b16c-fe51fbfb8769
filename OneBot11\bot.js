/**
 * OneBot11 机器人主程序
 * 类似于Wechat/bot.py的架构，提供OneBot11协议支持
 * 通过WebSocket连接到现有的VCPToolBox WebSocket服务器
 */

const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');
const http = require('http');

// 导入模块
const OneBotv11 = require('./OneBotv11');
const MessageHandler = require('./message_handler');
const WebSocketClient = require('./websocket_client');
const UserInfoManager = require('./plugins/user_info_manager');

class OneBot11Bot extends EventEmitter {
    constructor() {
        super();
        
        // 配置文件路径
        this.configFile = path.join(__dirname, 'config.json');
        this.runtimeConfigFile = path.join(__dirname, 'runtime_config.js');
        
        // 默认配置
        this.defaultConfig = {
            "ONEBOT11_HOST": "localhost",
            "ONEBOT11_PORT": 3001,
            "ONEBOT11_ACCESS_TOKEN": "",
            "VCP_WEBSOCKET_HOST": "localhost",
            "VCP_WEBSOCKET_PORT": 3000,
            "VCP_WEBSOCKET_PATH": "/onebot11",
            "ENABLE_PLUGINS": true,
            "ENABLE_VCP_INTEGRATION": true,
            "CONTINUOUS_LISTENING": true,
            "REPLY_INTERVAL": 1,
            "QUEUE_WAIT_TIME": 1,
            "PLUGIN_DIR": "plugins",
            "LOG_LEVEL": "info",
            "AUTO_RESTART_ENABLED": false,
            "AUTO_RESTART_INTERVAL": 24,
            "RECONNECT_INTERVAL": 3000,
            "HEARTBEAT_INTERVAL": 30000,
            "API_TIMEOUT": 10000
        };
        
        // 运行时状态
        this.config = {};
        this.runtimeConfig = {};
        this.isRunning = false;
        this.startTime = null;
        
        // 核心组件
        this.protocol = null;
        this.messageHandler = null;
        this.webSocketClient = null;
        this.onebotConnection = null;
        this.userInfoManager = new UserInfoManager();

        // WebSocket服务器实例（用于反向WebSocket）
        this.httpServer = null;
        this.wsServer = null;
        this.napCatClients = new Map(); // 存储连接的NapCat客户端
        
        // 插件系统
        this.plugins = new Map();
        this.pluginOrder = [];
        this.pluginInstances = [];
        
        // 消息队列
        this.messageQueue = [];
        this.isProcessingQueue = false;
        
        // 统计信息
        this.stats = {
            messagesReceived: 0,
            messagesSent: 0,
            connectTime: null,
            uptime: 0,
            errors: 0
        };
        
        // 初始化日志系统
        this.initializeLogger();
    }

    /**
     * 初始化日志系统
     */
    initializeLogger() {
        this.logger = {
            info: (component, message) => {
                const timestamp = new Date().toISOString();
                console.log(`[${timestamp}][INFO][${component}] ${message}`);
                this.writeLog('info', component, message);
            },
            error: (component, message) => {
                const timestamp = new Date().toISOString();
                console.error(`[${timestamp}][ERROR][${component}] ${message}`);
                this.writeLog('error', component, message);
                this.stats.errors++;
            },
            warning: (component, message) => {
                const timestamp = new Date().toISOString();
                console.warn(`[${timestamp}][WARN][${component}] ${message}`);
                this.writeLog('warning', component, message);
            },
            debug: (component, message) => {
                if (this.config.LOG_LEVEL === 'debug') {
                    const timestamp = new Date().toISOString();
                    console.log(`[${timestamp}][DEBUG][${component}] ${message}`);
                    this.writeLog('debug', component, message);
                }
            },
            success: (component, message) => {
                const timestamp = new Date().toISOString();
                console.log(`[${timestamp}][SUCCESS][${component}] ${message}`);
                this.writeLog('success', component, message);
            }
        };
    }

    /**
     * 写入日志文件
     */
    writeLog(level, component, message) {
        try {
            const logsDir = path.join(__dirname, 'logs');
            if (!fs.existsSync(logsDir)) {
                fs.mkdirSync(logsDir, { recursive: true });
            }
            
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}][${level.toUpperCase()}][${component}] ${message}\n`;
            
            const logFile = path.join(logsDir, 'onebot11_bot.log');
            fs.appendFileSync(logFile, logEntry);
        } catch (error) {
            console.error('写入日志失败:', error.message);
        }
    }

    /**
     * 加载配置文件
     */
    loadConfig() {
        try {
            // 加载主配置文件
            if (fs.existsSync(this.configFile)) {
                const configData = fs.readFileSync(this.configFile, 'utf8');
                this.config = { ...this.defaultConfig, ...JSON.parse(configData) };
            } else {
                this.config = { ...this.defaultConfig };
                this.saveConfig();
            }
            
            // 加载运行时配置
            this.loadRuntimeConfig();
            
            this.logger.info('Config', '配置文件加载成功');
            
        } catch (error) {
            this.logger.error('Config', `配置文件加载失败: ${error.message}`);
            this.config = { ...this.defaultConfig };
        }
    }

    /**
     * 保存配置文件
     */
    saveConfig() {
        try {
            fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 4));
            this.logger.info('Config', '配置文件保存成功');
        } catch (error) {
            this.logger.error('Config', `配置文件保存失败: ${error.message}`);
        }
    }

    /**
     * 加载运行时配置
     */
    loadRuntimeConfig() {
        try {
            if (fs.existsSync(this.runtimeConfigFile)) {
                // 清除require缓存以支持热重载
                delete require.cache[require.resolve(this.runtimeConfigFile)];
                this.runtimeConfig = require(this.runtimeConfigFile);
                this.logger.debug('Config', '运行时配置加载成功');
            } else {
                this.generateRuntimeConfig();
            }
        } catch (error) {
            this.logger.error('Config', `运行时配置加载失败: ${error.message}`);
            this.runtimeConfig = {};
        }
    }

    /**
     * 生成运行时配置文件
     */
    generateRuntimeConfig() {
        const runtimeConfigContent = `// OneBot11运行时配置文件
// 此文件由主程序自动管理，插件可以修改此文件来动态调整参数

module.exports = {
    // OneBot11连接配置
    ONEBOT11_HOST: "${this.config.ONEBOT11_HOST}",
    ONEBOT11_PORT: ${this.config.ONEBOT11_PORT},
    ONEBOT11_ACCESS_TOKEN: "${this.config.ONEBOT11_ACCESS_TOKEN}",
    
    // VCP WebSocket配置
    VCP_WEBSOCKET_HOST: "${this.config.VCP_WEBSOCKET_HOST}",
    VCP_WEBSOCKET_PORT: ${this.config.VCP_WEBSOCKET_PORT},
    VCP_WEBSOCKET_PATH: "${this.config.VCP_WEBSOCKET_PATH}",
    
    // 功能开关
    ENABLE_PLUGINS: ${this.config.ENABLE_PLUGINS},
    ENABLE_VCP_INTEGRATION: ${this.config.ENABLE_VCP_INTEGRATION},
    
    // 运行时参数
    REPLY_INTERVAL: ${this.config.REPLY_INTERVAL},
    QUEUE_WAIT_TIME: ${this.config.QUEUE_WAIT_TIME},
    
    // 连接参数
    RECONNECT_INTERVAL: ${this.config.RECONNECT_INTERVAL},
    HEARTBEAT_INTERVAL: ${this.config.HEARTBEAT_INTERVAL},
    API_TIMEOUT: ${this.config.API_TIMEOUT}
};`;

        try {
            fs.writeFileSync(this.runtimeConfigFile, runtimeConfigContent);
            this.runtimeConfig = require(this.runtimeConfigFile);
            this.logger.info('Config', '运行时配置文件生成成功');
        } catch (error) {
            this.logger.error('Config', `运行时配置文件生成失败: ${error.message}`);
        }
    }

    /**
     * 初始化核心组件
     */
    async initializeComponents() {
        try {
            // 初始化协议处理器
            this.protocol = new OneBotv11(this.logger, this.config);
            
            // 初始化消息处理器
            this.messageHandler = new MessageHandler(this.logger, this);
            
            // 初始化VCP WebSocket客户端
            this.webSocketClient = new WebSocketClient(this.logger, this.config);
            
            // 设置事件监听
            this.setupEventListeners();
            
            this.logger.success('Bot', '核心组件初始化成功');
            
        } catch (error) {
            this.logger.error('Bot', `核心组件初始化失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // OneBot11消息事件
        this.on('onebot11_message', (event) => {
            this.handleOneBotEvent(event);
        });

        // API响应事件 - 转发给协议处理器
        this.on('api_response', (response) => {
            this.logger.debug('Bot', `收到API响应事件: echo=${response.echo}`);
            if (this.protocol && this.protocol.handleApiResponse) {
                this.protocol.handleApiResponse(response);
            } else {
                this.logger.warning('Bot', 'Protocol或handleApiResponse方法不存在');
            }
        });

        // VCP WebSocket事件
        this.webSocketClient.on('connected', () => {
            this.logger.success('WebSocket', '已连接到VCP服务器');
        });

        this.webSocketClient.on('disconnected', () => {
            this.logger.warning('WebSocket', '与VCP服务器连接断开');
        });

        this.webSocketClient.on('message', (message) => {
            this.handleVCPMessage(message);
        });

        // 监听从VCP广播中提取的OneBot11事件（暂时禁用，避免重复处理）
        // this.webSocketClient.on('onebot11_event', (event) => {
        //     this.logger.debug('Bot', '收到VCP广播的OneBot11事件');
        //     this.messageHandler.handleEvent(event);
        // });

        // 错误处理
        this.on('error', (error) => {
            this.logger.error('Bot', `运行时错误: ${error.message}`);
        });
    }

    /**
     * 启动WebSocket服务器（用于NapCat反向连接）
     */
    async startWebSocketServer() {
        return new Promise((resolve, reject) => {
            try {
                // 创建HTTP服务器
                this.httpServer = http.createServer();

                // 创建WebSocket服务器
                this.wsServer = new WebSocket.Server({
                    server: this.httpServer,
                    path: '/OneBotv11'
                });

                // 设置WebSocket服务器事件监听
                this.wsServer.on('connection', (ws, request) => {
                    const clientId = this.generateClientId();
                    ws.clientId = clientId;
                    this.napCatClients.set(clientId, ws);

                    this.logger.success('OneBot11Server', `✅ NapCat客户端 ${clientId} 已连接`);

                    // 处理消息
                    ws.on('message', (data) => {
                        this.handleNapCatMessage(data, clientId);
                    });

                    // 处理断开连接
                    ws.on('close', (code, reason) => {
                        this.napCatClients.delete(clientId);
                        this.logger.warning('OneBot11Server', `NapCat客户端 ${clientId} 已断开: ${code} - ${reason}`);
                    });

                    // 处理错误
                    ws.on('error', (error) => {
                        this.logger.error('OneBot11Server', `NapCat客户端 ${clientId} 错误: ${error.message}`);
                        this.napCatClients.delete(clientId);
                    });
                });

                // 启动HTTP服务器
                const port = this.config.ONEBOT11_PORT || 5140;
                this.httpServer.listen(port, () => {
                    this.logger.success('OneBot11Server', `✅ WebSocket服务器已启动，监听端口: ${port}`);
                    this.logger.info('OneBot11Server', `等待NapCat连接到: ws://localhost:${port}/OneBotv11`);
                    resolve();
                });

                this.httpServer.on('error', (error) => {
                    this.logger.error('OneBot11Server', `HTTP服务器错误: ${error.message}`);
                    reject(error);
                });

            } catch (error) {
                this.logger.error('OneBot11Server', `启动WebSocket服务器失败: ${error.message}`);
                reject(error);
            }
        });
    }

    /**
     * 生成客户端ID
     */
    generateClientId() {
        return Math.random().toString(36).substring(2, 15);
    }

    /**
     * 格式化日志消息，简化输出
     * @param {Object} message 消息对象
     * @returns {string} 格式化的日志消息
     */
    formatLogMessage(message) {
        try {
            // API响应消息
            if (message.echo) {
                const action = message.echo.split('_')[2] || 'unknown';
                if (message.data && this.userInfoManager.isUserInfoResponse(message)) {
                    const userData = message.data;
                    const nickname = userData.nickname || userData.nick || '未知';
                    const userId = userData.user_id || userData.uin || '未知';
                    return `API响应[${action}]: 用户信息 ${nickname}(${userId}) [数据已保存]`;
                } else {
                    return `API响应[${action}]: ${message.status || 'unknown'} (retcode: ${message.retcode || 'unknown'})`;
                }
            }

            // 普通事件消息
            if (message.post_type) {
                if (message.post_type === 'message') {
                    const msgType = message.message_type || 'unknown';
                    const userId = message.user_id || 'unknown';
                    const content = message.raw_message || message.message || '[无内容]';
                    const truncated = content.length > 50 ? content.substring(0, 50) + '...' : content;
                    return `${msgType}消息: ${userId} - ${truncated}`;
                } else if (message.post_type === 'notice') {
                    const noticeType = message.notice_type || 'unknown';
                    const subType = message.sub_type || '';
                    return `通知事件: ${noticeType}${subType ? `(${subType})` : ''}`;
                } else if (message.post_type === 'meta_event') {
                    const metaType = message.meta_event_type || 'unknown';
                    return `元事件: ${metaType}`;
                }
            }

            // 其他消息类型
            return `未知消息类型: ${message.post_type || 'no_post_type'}`;

        } catch (error) {
            return `[日志格式化失败: ${error.message}]`;
        }
    }

    /**
     * 处理NapCat消息
     */
    handleNapCatMessage(data, clientId) {
        try {
            const message = JSON.parse(data.toString());

            // 优化日志输出 - 简化显示
            const logMessage = this.formatLogMessage(message);
            this.logger.debug('OneBot11Server', `收到NapCat消息 [${clientId}]: ${logMessage}`);

            // 检查是否是API响应
            if (message.echo) {
                // 处理用户信息
                this.userInfoManager.handleApiResponse(message);
                this.emit('api_response', message);
                return;
            }

            // 处理OneBot11协议消息
            this.emit('onebot11_message', {
                ...message,
                clientId: clientId,
                ws: this.napCatClients.get(clientId), // 添加WebSocket连接
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            this.logger.error('OneBot11Server', `解析NapCat消息失败 [${clientId}]: ${error.message}`);
        }
    }

    /**
     * 向NapCat发送消息
     */
    sendToNapCat(message, clientId = null) {
        try {
            const messageStr = JSON.stringify(message);

            if (clientId && this.napCatClients.has(clientId)) {
                // 发送给指定客户端
                const ws = this.napCatClients.get(clientId);
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(messageStr);
                    this.logger.debug('OneBot11Server', `已发送消息到NapCat [${clientId}]`);
                }
            } else {
                // 广播给所有连接的客户端
                this.napCatClients.forEach((ws, id) => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(messageStr);
                        this.logger.debug('OneBot11Server', `已广播消息到NapCat [${id}]`);
                    }
                });
            }
        } catch (error) {
            this.logger.error('OneBot11Server', `发送消息到NapCat失败: ${error.message}`);
        }
    }

    /**
     * 连接到OneBot11服务器
     */
    async connectToOneBot11() {
        return new Promise((resolve, reject) => {
            try {
                const wsUrl = `ws://${this.config.ONEBOT11_HOST}:${this.config.ONEBOT11_PORT}`;
                const headers = {};
                
                if (this.config.ONEBOT11_ACCESS_TOKEN) {
                    headers['Authorization'] = `Bearer ${this.config.ONEBOT11_ACCESS_TOKEN}`;
                }
                
                this.logger.info('OneBot11', `正在连接到 ${wsUrl}...`);
                
                this.onebotConnection = new WebSocket(wsUrl, { headers });
                
                this.onebotConnection.on('open', () => {
                    this.stats.connectTime = new Date();
                    this.logger.success('OneBot11', '✅ 已连接到OneBot11服务器');
                    resolve();
                });
                
                this.onebotConnection.on('message', (data) => {
                    this.handleOneBotMessage(data);
                });
                
                this.onebotConnection.on('close', (code, reason) => {
                    this.logger.warning('OneBot11', `连接已关闭: ${code} - ${reason}`);
                    this.scheduleReconnect();
                });
                
                this.onebotConnection.on('error', (error) => {
                    this.logger.error('OneBot11', `连接错误: ${error.message}`);
                    reject(error);
                });
                
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 处理OneBot11消息
     */
    handleOneBotMessage(data) {
        try {
            const message = JSON.parse(data.toString());
            this.stats.messagesReceived++;
            
            this.logger.debug('OneBot11', `收到消息: ${JSON.stringify(message)}`);
            
            // 添加到消息队列
            this.messageQueue.push(message);
            this.processMessageQueue();
            
        } catch (error) {
            this.logger.error('OneBot11', `消息解析失败: ${error.message}`);
        }
    }

    /**
     * 深度清理WebSocket对象
     */
    deepCleanWebSocketObjects(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }

        // 如果是数组，递归处理每个元素
        if (Array.isArray(obj)) {
            return obj.map(item => this.deepCleanWebSocketObjects(item));
        }

        // 创建新对象，避免修改原对象
        const cleaned = {};

        for (const [key, value] of Object.entries(obj)) {
            // 跳过WebSocket相关的属性（扩展列表）
            if (this.isWebSocketRelatedProperty(key, value)) {
                continue;
            }

            // 如果值是对象，递归清理
            if (value && typeof value === 'object') {
                // 检查是否是WebSocket对象（通过检查特征属性）
                if (this.isWebSocketObject(value)) {
                    continue; // 跳过WebSocket对象
                }
                cleaned[key] = this.deepCleanWebSocketObjects(value);
            } else {
                cleaned[key] = value;
            }
        }

        return cleaned;
    }

    /**
     * 检查是否是WebSocket相关属性
     */
    isWebSocketRelatedProperty(key, value) {
        // 基本WebSocket属性
        const basicWebSocketKeys = [
            'ws', 'websocket', 'socket', 'clientId',
            '_socket', '_receiver', '_sender', '_server', '_handle',
            '_connections', '_connectionKey', '_events', '_eventsCount',
            '_usingWorkers', '_workers', '_unref', '_listeningId',
            'parser', '_paused', '_autoPong', '_isServer'
        ];

        // 检查基本属性名
        if (basicWebSocketKeys.includes(key)) {
            return true;
        }

        // 检查包含WebSocket或Socket的属性名
        if (key.includes('WebSocket') || key.includes('Socket') ||
            key.includes('_socket') || key.includes('_server')) {
            return true;
        }

        // 检查以下划线开头的可能是内部属性
        if (key.startsWith('_') && value && typeof value === 'object') {
            // 检查是否包含WebSocket特征
            if (value.readyState !== undefined || value.send !== undefined ||
                value.listening !== undefined || value._connectionKey !== undefined) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否是WebSocket对象
     */
    isWebSocketObject(obj) {
        if (!obj || typeof obj !== 'object') {
            return false;
        }

        // WebSocket客户端特征
        if (obj.readyState !== undefined || obj.send !== undefined ||
            obj.CONNECTING !== undefined || obj.OPEN !== undefined) {
            return true;
        }

        // WebSocket服务器特征
        if (obj.listening !== undefined || obj._connectionKey !== undefined ||
            obj._handle !== undefined || obj._connections !== undefined) {
            return true;
        }

        // HTTP服务器特征（WebSocket服务器基于HTTP服务器）
        if (obj.requestTimeout !== undefined && obj.headersTimeout !== undefined &&
            obj.keepAliveTimeout !== undefined && obj._connectionKey !== undefined) {
            return true;
        }

        return false;
    }

    /**
     * 处理OneBot11事件
     */
    async handleOneBotEvent(event) {
        try {
            if (this.messageHandler) {
                // 事件现在已经包含了正确的WebSocket连接信息
                await this.messageHandler.handleEvent(event);
            }

            // 广播到VCP WebSocket服务器（移除WebSocket对象避免日志冗长）
            if (this.webSocketClient && this.config.ENABLE_VCP_INTEGRATION) {
                const cleanEvent = this.deepCleanWebSocketObjects(event);

                this.webSocketClient.broadcast({
                    type: 'onebot11_event',
                    data: cleanEvent,
                    timestamp: new Date().toISOString()
                });
            }

        } catch (error) {
            this.logger.error('Bot', `事件处理失败: ${error.message}`);
        }
    }

    /**
     * 处理VCP消息
     */
    handleVCPMessage(message) {
        try {
            this.logger.debug('VCP', `收到VCP消息: ${JSON.stringify(message)}`);
            
            // 这里可以处理来自VCP系统的消息
            // 例如：工具调用结果、系统通知等
            
        } catch (error) {
            this.logger.error('VCP', `VCP消息处理失败: ${error.message}`);
        }
    }

    /**
     * 处理消息队列
     */
    async processMessageQueue() {
        if (this.isProcessingQueue || this.messageQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        try {
            while (this.messageQueue.length > 0) {
                const message = this.messageQueue.shift();
                
                // 验证消息格式
                if (this.protocol) {
                    const validation = this.protocol.validateEvent(message);
                    if (validation.valid) {
                        this.emit('onebot11_message', message);
                    } else {
                        this.logger.warning('Bot', `消息格式验证失败: ${validation.error}`);
                    }
                }
                
                // 控制处理速度
                if (this.config.QUEUE_WAIT_TIME > 0) {
                    await new Promise(resolve => setTimeout(resolve, this.config.QUEUE_WAIT_TIME * 1000));
                }
            }
        } catch (error) {
            this.logger.error('Bot', `消息队列处理失败: ${error.message}`);
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * 计划重连
     */
    scheduleReconnect() {
        setTimeout(async () => {
            if (!this.isRunning) return;
            
            this.logger.info('OneBot11', '尝试重新连接...');
            try {
                await this.connectToOneBot11();
            } catch (error) {
                this.logger.error('OneBot11', `重连失败: ${error.message}`);
            }
        }, this.config.RECONNECT_INTERVAL);
    }

    /**
     * 启动机器人
     */
    async start() {
        try {
            this.logger.info('Bot', '正在启动OneBot11机器人...');
            
            // 加载配置
            this.loadConfig();
            
            // 初始化组件
            await this.initializeComponents();
            
            // 连接到VCP WebSocket服务器
            if (this.config.ENABLE_VCP_INTEGRATION) {
                await this.webSocketClient.connect();
            }
            
            // 启动WebSocket服务器（用于NapCat反向连接）
            await this.startWebSocketServer();

            // 连接到OneBot11服务器（如果不使用反向WebSocket）
            // await this.connectToOneBot11();
            
            // 加载插件
            if (this.config.ENABLE_PLUGINS) {
                await this.loadPlugins();
            }
            
            this.isRunning = true;
            this.startTime = new Date();
            
            this.logger.success('Bot', '✅ OneBot11机器人启动成功');
            
        } catch (error) {
            this.logger.error('Bot', `机器人启动失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 停止机器人
     */
    async stop() {
        try {
            this.logger.info('Bot', '正在停止OneBot11机器人...');
            
            this.isRunning = false;
            
            // 关闭OneBot11连接
            if (this.onebotConnection) {
                this.onebotConnection.close();
            }
            
            // 关闭VCP WebSocket连接
            if (this.webSocketClient) {
                await this.webSocketClient.disconnect();
            }
            
            // 卸载插件
            await this.unloadPlugins();
            
            this.logger.success('Bot', 'OneBot11机器人已停止');
            
        } catch (error) {
            this.logger.error('Bot', `机器人停止失败: ${error.message}`);
        }
    }

    /**
     * 加载插件
     */
    async loadPlugins() {
        try {
            // 加载内置插件
            await this.loadBuiltinPlugins();

            // 加载外部插件
            await this.loadExternalPlugins();

            // 初始化所有插件
            await this.initializePlugins();

            this.logger.success('Bot', `插件系统初始化完成，共加载 ${this.pluginInstances.length} 个插件`);

        } catch (error) {
            this.logger.error('Bot', `插件系统初始化失败: ${error.message}`);
        }
    }

    /**
     * 加载内置插件
     */
    async loadBuiltinPlugins() {
        try {
            // 加载命令插件
            const CommandPlugin = require('./plugins/command_plugin');
            const commandPlugin = new CommandPlugin(this.protocol, this.logger, this.config);
            this.pluginInstances.push(commandPlugin);

            // 加载AI对话插件
            const AIChatPlugin = require('./plugins/ai_chat_plugin');
            const aiChatPlugin = new AIChatPlugin(this.protocol, this.logger, this.config);
            this.pluginInstances.push(aiChatPlugin);

            this.logger.info('Bot', '内置插件加载完成');

        } catch (error) {
            this.logger.error('Bot', `内置插件加载失败: ${error.message}`);
        }
    }

    /**
     * 加载外部插件
     */
    async loadExternalPlugins() {
        try {
            const pluginDir = path.join(__dirname, this.config.PLUGIN_DIR || 'plugins');

            if (!fs.existsSync(pluginDir)) {
                this.logger.warning('Bot', '插件目录不存在，跳过外部插件加载');
                return;
            }

            // 读取插件加载顺序
            const orderFile = path.join(pluginDir, 'plugin_order.txt');
            let pluginOrder = [];

            if (fs.existsSync(orderFile)) {
                const orderContent = fs.readFileSync(orderFile, 'utf8');
                pluginOrder = orderContent.split('\n')
                    .map(line => line.trim())
                    .filter(line => line && !line.startsWith('#'));
            }

            // 按顺序加载插件
            for (const pluginName of pluginOrder) {
                try {
                    const pluginPath = path.join(pluginDir, pluginName);

                    // 检查是否是目录插件
                    if (fs.existsSync(pluginPath) && fs.statSync(pluginPath).isDirectory()) {
                        const mainFile = path.join(pluginPath, 'main.js');
                        if (fs.existsSync(mainFile)) {
                            const PluginClass = require(mainFile);
                            if (typeof PluginClass === 'function') {
                                const plugin = new PluginClass(this.protocol, this.logger, this.config);
                                this.pluginInstances.push(plugin);
                                this.logger.success('Bot', `目录插件加载成功: ${pluginName}`);
                            }
                        }
                    } else {
                        // 检查是否是单文件插件
                        const singleFile = path.join(pluginDir, `${pluginName}.js`);
                        if (fs.existsSync(singleFile)) {
                            const PluginClass = require(singleFile);
                            if (typeof PluginClass === 'function') {
                                const plugin = new PluginClass(this.protocol, this.logger, this.config);
                                this.pluginInstances.push(plugin);
                                this.logger.success('Bot', `单文件插件加载成功: ${pluginName}`);
                            }
                        }
                    }
                } catch (error) {
                    this.logger.error('Bot', `插件加载失败 ${pluginName}: ${error.message}`);
                }
            }

        } catch (error) {
            this.logger.error('Bot', `外部插件加载失败: ${error.message}`);
        }
    }

    /**
     * 初始化插件
     */
    async initializePlugins() {
        // 按优先级排序插件 (数值越小优先级越高)
        this.pluginInstances.sort((a, b) => (a.priority || 100) - (b.priority || 100));

        this.logger.info('Bot', '插件加载顺序 (按优先级排序):');
        for (let i = 0; i < this.pluginInstances.length; i++) {
            const plugin = this.pluginInstances[i];
            this.logger.info('Bot', `  ${i + 1}. ${plugin.name} (优先级: ${plugin.priority || 100}, 权限: ${plugin.permission || 'all'})`);
        }

        // 初始化所有插件
        for (const plugin of this.pluginInstances) {
            try {
                await plugin.initialize();
            } catch (error) {
                this.logger.error('Bot', `插件初始化失败 ${plugin.name}: ${error.message}`);
            }
        }
    }

    /**
     * 卸载插件
     */
    async unloadPlugins() {
        for (const plugin of this.pluginInstances) {
            try {
                await plugin.destroy();
            } catch (error) {
                this.logger.error('Bot', `插件卸载失败 ${plugin.name}: ${error.message}`);
            }
        }

        this.pluginInstances = [];
        this.logger.info('Plugins', '插件系统已卸载');
    }

    /**
     * 获取插件列表
     */
    getPlugins() {
        return this.pluginInstances;
    }

    /**
     * 获取插件信息列表
     */
    getPluginList() {
        return this.pluginInstances.map(plugin => plugin.getInfo());
    }

    /**
     * 根据名称获取插件
     */
    getPlugin(name) {
        return this.pluginInstances.find(plugin => plugin.name === name);
    }

    /**
     * 获取运行状态
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            startTime: this.startTime,
            uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0,
            stats: this.stats,
            config: this.config
        };
    }
}

// 如果直接运行此文件，则启动机器人
if (require.main === module) {
    const bot = new OneBot11Bot();
    
    // 处理进程信号
    process.on('SIGINT', async () => {
        console.log('\n收到停止信号，正在关闭机器人...');
        await bot.stop();
        process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
        console.log('\n收到终止信号，正在关闭机器人...');
        await bot.stop();
        process.exit(0);
    });
    
    // 启动机器人
    bot.start().catch((error) => {
        console.error('机器人启动失败:', error);
        process.exit(1);
    });
}

module.exports = OneBot11Bot;
