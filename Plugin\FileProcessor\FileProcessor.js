// Plugin/FileProcessor/FileProcessor.js - 图文件处理VCP插件
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const mime = require('mime-types');

class FileProcessor {
    constructor() {
        this.name = 'FileProcessor';
        this.description = '图文件处理插件，支持markdown链接提取、图片base64转换、文件文本提取等功能';
        this.version = '1.0.0';
        
        // 从环境变量加载配置
        this.config = {
            // OpenAI API配置
            apiUrl: process.env.FILEPROCESSOR_API_URL || process.env.API_URL || 'https://api.openai.com/v1/chat/completions',
            apiKey: process.env.FILEPROCESSOR_API_KEY || process.env.API_Key || '',
            
            // 图片处理模型列表
            imageModels: this.parseModelArray(process.env.FILEPROCESSOR_IMAGE_MODELS || 'gpt-4o,gpt-4-vision-preview,claude-3-sonnet'),
            
            // 文件处理模型列表  
            fileModels: this.parseModelArray(process.env.FILEPROCESSOR_FILE_MODELS || 'gpt-4o,gpt-4-turbo,claude-3-sonnet'),
            
            // 文件下载配置
            maxFileSize: parseInt(process.env.FILEPROCESSOR_MAX_FILE_SIZE) || 50 * 1024 * 1024, // 50MB
            downloadTimeout: (parseInt(process.env.FILEPROCESSOR_DOWNLOAD_TIMEOUT) || 30) * 1000, // 30秒转毫秒

            // API调用超时配置
            apiTimeout: (parseInt(process.env.FILEPROCESSOR_API_TIMEOUT) || 120) * 1000 // 120秒转毫秒
        };
        
        // 临时文件目录
        this.tempDir = path.join(__dirname, 'temp');
        this.ensureTempDir();
    }
    
    /**
     * 解析模型数组配置
     */
    parseModelArray(modelsString) {
        if (!modelsString) return [];
        return modelsString.split(',').map(model => model.trim()).filter(model => model.length > 0);
    }
    
    /**
     * 确保临时目录存在
     */
    ensureTempDir() {
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
    }
    
    /**
     * 主要执行方法
     */
    async execute(input) {
        try {
            const params = this.parseInput(input);

            // 验证参数
            if (!params.links || !Array.isArray(params.links) || params.links.length === 0) {
                return this.createResponse('error', '必须提供有效的链接数组');
            }

            if (!params.prompts || !Array.isArray(params.prompts) || params.prompts.length === 0) {
                return this.createResponse('error', '必须提供有效的提示词数组');
            }

            // 限制链接数量（1-4个）
            const processLinks = params.links.slice(0, 4);

            // 处理提示词和模型数组
            const prompts = this.normalizeArray(params.prompts, processLinks.length);
            const models = params.models ? this.normalizeArray(params.models, processLinks.length) : null;

            const results = [];

            for (let i = 0; i < processLinks.length; i++) {
                const url = processLinks[i];
                const prompt = prompts[i];
                const model = models ? models[i] : null;

                try {
                    // 验证文件URL格式
                    if (!this.hasValidFileExtension(url)) {
                        throw new Error(`不支持的文件格式或缺少文件后缀: ${url}`);
                    }

                    const result = await this.processFile(url, prompt, model, i + 1);
                    results.push(result);
                } catch (error) {
                    results.push({
                        url: url,
                        index: i + 1,
                        status: 'error',
                        error: error.message
                    });
                }
            }

            return this.createResponse('success', '文件处理完成', {
                processed_count: results.length,
                results: results,
                original_links: params.links,
                prompts_used: prompts,
                models_used: models
            });

        } catch (error) {
            return this.createResponse('error', error.message);
        }
    }
    
    /**
     * 解析输入参数
     */
    parseInput(input) {
        let params;

        if (typeof input === 'string') {
            try {
                params = JSON.parse(input);
            } catch (e) {
                throw new Error('输入参数必须是有效的JSON格式');
            }
        } else {
            params = input || {};
        }

        return {
            links: params.links || [],
            prompts: params.prompts || [],
            models: params.models || null,
            ...params
        };
    }

    /**
     * 标准化数组长度，确保与目标长度一致
     */
    normalizeArray(array, targetLength) {
        if (!Array.isArray(array)) {
            return new Array(targetLength).fill(array);
        }

        if (array.length === 1) {
            // 如果只有一个元素，复制到所有位置
            return new Array(targetLength).fill(array[0]);
        } else if (array.length === targetLength) {
            // 长度匹配，直接返回
            return array;
        } else if (array.length > targetLength) {
            // 长度超出，截取前面部分
            return array.slice(0, targetLength);
        } else {
            // 长度不足，用最后一个元素填充
            const result = [...array];
            const lastElement = array[array.length - 1];
            while (result.length < targetLength) {
                result.push(lastElement);
            }
            return result;
        }
    }
    

    
    /**
     * 检查URL是否有有效的文件后缀
     */
    hasValidFileExtension(url) {
        try {
            const cleanUrl = url.split('?')[0].split('#')[0];
            const ext = path.extname(cleanUrl).toLowerCase();
            return ext.length > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * 获取图片的MIME类型
     */
    getImageMimeType(url) {
        try {
            const cleanUrl = url.split('?')[0].split('#')[0];
            const mimeType = mime.lookup(cleanUrl);

            // 如果mime-types识别成功且是图片类型，直接返回
            if (mimeType && mimeType.startsWith('image/')) {
                return mimeType;
            }

            // 如果无法识别，根据扩展名手动判断
            const ext = path.extname(cleanUrl).toLowerCase();
            switch (ext) {
                case '.jpg':
                case '.jpeg':
                    return 'image/jpeg';
                case '.png':
                    return 'image/png';
                case '.gif':
                    return 'image/gif';
                case '.webp':
                    return 'image/webp';
                case '.bmp':
                    return 'image/bmp';
                case '.svg':
                    return 'image/svg+xml';
                case '.tiff':
                case '.tif':
                    return 'image/tiff';
                case '.ico':
                    return 'image/x-icon';
                default:
                    return 'image/jpeg'; // 默认使用jpeg
            }
        } catch (error) {
            return 'image/jpeg'; // 出错时默认使用jpeg
        }
    }

    /**
     * 获取文件类型
     */
    getFileType(url) {
        try {
            const cleanUrl = url.split('?')[0].split('#')[0];
            const mimeType = mime.lookup(cleanUrl);

            if (!mimeType) {
                // 对于一些特殊文件类型的处理
                const ext = path.extname(cleanUrl).toLowerCase();
                const codeExtensions = ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.r', '.sql', '.sh', '.bat', '.ps1', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.dockerfile', '.makefile', '.cmake', '.gradle'];
                const spreadsheetExtensions = ['.xlsx', '.xls', '.ods'];

                if (codeExtensions.includes(ext)) {
                    return 'code';
                } else if (spreadsheetExtensions.includes(ext)) {
                    return 'spreadsheet';
                } else if (['.log', '.properties', '.env', '.gitignore'].includes(ext)) {
                    return 'text';
                }
                return 'unknown';
            }

            if (mimeType.startsWith('image/')) {
                return 'image';
            } else if (mimeType.startsWith('text/') ||
                       mimeType === 'application/json' ||
                       mimeType === 'application/xml' ||
                       mimeType === 'application/javascript') {
                return 'text';
            } else if (mimeType === 'application/pdf' ||
                       mimeType.includes('document') ||
                       mimeType.includes('word') ||
                       mimeType.includes('rtf')) {
                return 'document';
            } else if (mimeType.includes('spreadsheet') ||
                       mimeType.includes('excel') ||
                       mimeType === 'text/csv') {
                return 'spreadsheet';
            }

            return 'document'; // 默认作为文档处理
        } catch (error) {
            return 'unknown';
        }
    }
    
    /**
     * 处理单个文件
     */
    async processFile(url, prompt, model, index) {
        const fileType = this.getFileType(url);

        if (fileType === 'image') {
            return await this.processImage(url, prompt, model, index);
        } else if (['document', 'code', 'spreadsheet', 'text'].includes(fileType)) {
            return await this.processTextFile(url, prompt, model, index, fileType);
        } else {
            throw new Error(`不支持的文件类型: ${url}`);
        }
    }
    
    /**
     * 处理图片文件
     */
    async processImage(url, prompt, model, index) {
        try {
            // 获取图片base64数据（本地文件或下载）
            const base64Data = await this.getImageAsBase64(url);

            // 获取图片的MIME类型
            //const mimeType = this.getImageMimeType(url);

            // 选择图片处理模型
            const selectedModel = model || this.selectRandomModel(this.config.imageModels);

            // 构建API请求
            const apiRequest = {
                model: selectedModel,
                messages: [
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'text',
                                text: prompt
                            },
                            {
                                type: 'image_url',
                                image_url: {
                                    url: `data:image/webp;base64,${base64Data}`
                                }
                            }
                        ]
                    }
                ],
                //max_tokens: 1000
            };

            // 调用API
            const apiResponse = await this.callAPI(apiRequest);

            return {
                url: url,
                index: index,
                type: 'image',
                status: 'success',
                model: selectedModel,
                prompt: prompt,
                base64_data: base64Data.substring(0, 1000) + '...', // 只返回前1000字符用于调试
                analysis: apiResponse.content,
                tokens_used: apiResponse.usage
            };

        } catch (error) {
            throw new Error(`图片处理失败: ${error.message}`);
        }
    }

    /**
     * 处理文本类文件（文档、代码、表格等）
     */
    async processTextFile(url, prompt, model, index, fileType) {
        try {
            // 获取文件路径（本地文件或下载）
            const filePath = await this.getFilePath(url);
            const isLocalFile = this.isLocalPath(url);

            // 提取文本内容
            const textContent = await this.extractTextFromFile(filePath);

            // 选择文件处理模型
            const selectedModel = model || this.selectRandomModel(this.config.fileModels);

            // 构建API请求
            const apiRequest = {
                model: selectedModel,
                messages: [
                    {
                        role: 'user',
                        content: `${prompt}\n\n文件类型: ${fileType}\n文件内容:\n${textContent.substring(0, 4096)}`
                    }
                ],
                //max_tokens: 1000
            };

            // 调用API
            const apiResponse = await this.callAPI(apiRequest);

            // 清理临时文件（仅对下载的文件）
            if (!isLocalFile) {
                this.cleanupFile(filePath);
            }

            return {
                url: url,
                index: index,
                type: fileType,
                status: 'success',
                model: selectedModel,
                prompt: prompt,
                text_content: textContent.substring(0, 4096), // 限制返回的文本长度
                analysis: apiResponse.content,
                tokens_used: apiResponse.usage
            };

        } catch (error) {
            throw new Error(`${fileType}文件处理失败: ${error.message}`);
        }
    }
    
    /**
     * 创建响应对象
     */
    createResponse(status, message, data = null) {
        return {
            type: 'file_processor',
            status: status,
            message: message,
            data: data,
            timestamp: new Date().toISOString()
        };
    }
    


    /**
     * 判断是否为本地路径
     */
    isLocalPath(url) {
        // 检查是否为绝对路径（Windows: C:\ 或 D:\ 等，Linux/Mac: /）
        return /^[A-Za-z]:\\/.test(url) || url.startsWith('/');
    }

    /**
     * 获取图片的base64数据（本地文件或网络下载）
     */
    async getImageAsBase64(url) {
        if (this.isLocalPath(url)) {
            // 本地文件直接读取
            return this.readLocalFileAsBase64(url);
        } else {
            // 网络文件下载
            return this.downloadAndConvertToBase64(url);
        }
    }

    /**
     * 获取文件路径（本地文件或下载后的路径）
     */
    async getFilePath(url) {
        if (this.isLocalPath(url)) {
            // 本地文件直接返回路径
            if (!fs.existsSync(url)) {
                throw new Error(`本地文件不存在: ${url}`);
            }
            return url;
        } else {
            // 网络文件下载
            return this.downloadFile(url);
        }
    }

    /**
     * 读取本地文件为base64
     */
    readLocalFileAsBase64(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }

            const buffer = fs.readFileSync(filePath);
            return buffer.toString('base64');
        } catch (error) {
            throw new Error(`读取本地文件失败: ${error.message}`);
        }
    }

    /**
     * 下载文件并转换为base64
     */
    async downloadAndConvertToBase64(url) {
        try {
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'arraybuffer',
                timeout: this.config.downloadTimeout,
                maxContentLength: this.config.maxFileSize,
                headers: {
                    'User-Agent': 'FileProcessor/1.0'
                }
            });

            const buffer = Buffer.from(response.data);
            return buffer.toString('base64');

        } catch (error) {
            throw new Error(`下载文件失败: ${error.message}`);
        }
    }

    /**
     * 下载文件到临时目录
     */
    async downloadFile(url) {
        try {
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'stream',
                timeout: this.config.downloadTimeout,
                maxContentLength: this.config.maxFileSize,
                headers: {
                    'User-Agent': 'FileProcessor/1.0'
                }
            });

            // 生成临时文件名
            const fileName = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}${path.extname(url.split('?')[0])}`;
            const filePath = path.join(this.tempDir, fileName);

            // 保存文件
            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => resolve(filePath));
                writer.on('error', reject);
            });

        } catch (error) {
            throw new Error(`下载文件失败: ${error.message}`);
        }
    }

    /**
     * 从文件中提取文本内容
     */
    async extractTextFromFile(filePath) {
        const ext = path.extname(filePath).toLowerCase();

        try {
            switch (ext) {
                // 纯文本文件
                case '.txt':
                case '.md':
                case '.csv':
                case '.json':
                case '.xml':
                case '.html':
                case '.htm':
                case '.log':
                case '.properties':
                case '.env':
                case '.gitignore':
                case '.yaml':
                case '.yml':
                case '.toml':
                case '.ini':
                case '.cfg':
                case '.conf':
                    return fs.readFileSync(filePath, 'utf8');

                // 代码文件
                case '.js':
                case '.ts':
                case '.py':
                case '.java':
                case '.cpp':
                case '.c':
                case '.h':
                case '.cs':
                case '.php':
                case '.rb':
                case '.go':
                case '.rs':
                case '.swift':
                case '.kt':
                case '.scala':
                case '.r':
                case '.sql':
                case '.sh':
                case '.bat':
                case '.ps1':
                case '.dockerfile':
                case '.makefile':
                case '.cmake':
                case '.gradle':
                    return fs.readFileSync(filePath, 'utf8');

                // PDF文件
                case '.pdf':
                    return await this.extractPdfText(filePath);

                // Word文档
                case '.doc':
                case '.docx':
                    return await this.extractDocText(filePath);

                // Excel文件
                case '.xlsx':
                case '.xls':
                    return await this.extractExcelText(filePath);

                // 其他表格文件
                case '.ods':
                case '.tsv':
                    return fs.readFileSync(filePath, 'utf8');

                default:
                    // 尝试作为UTF-8文本文件读取
                    return fs.readFileSync(filePath, 'utf8');
            }
        } catch (error) {
            throw new Error(`文本提取失败: ${error.message}`);
        }
    }

    /**
     * 提取PDF文本（需要pdf-parse库）
     */
    async extractPdfText(filePath) {
        try {
            // 尝试使用pdf-parse
            const pdfParse = require('pdf-parse');
            const dataBuffer = fs.readFileSync(filePath);
            const data = await pdfParse(dataBuffer);
            return data.text;
        } catch (error) {
            // 如果pdf-parse不可用，返回错误信息
            throw new Error('PDF解析需要安装pdf-parse库');
        }
    }

    /**
     * 提取Word文档文本（需要textract库）
     */
    async extractDocText(filePath) {
        try {
            // 尝试使用textract
            const textract = require('textract');

            return new Promise((resolve, reject) => {
                textract.fromFileWithPath(filePath, (error, text) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(text);
                    }
                });
            });
        } catch (error) {
            // 如果textract不可用，返回错误信息
            throw new Error('Word文档解析需要安装textract库');
        }
    }

    /**
     * 提取Excel文件文本（需要xlsx库）
     */
    async extractExcelText(filePath) {
        try {
            // 尝试使用xlsx
            const XLSX = require('xlsx');

            const workbook = XLSX.readFile(filePath);
            let allText = '';

            // 遍历所有工作表
            workbook.SheetNames.forEach(sheetName => {
                const worksheet = workbook.Sheets[sheetName];
                const sheetText = XLSX.utils.sheet_to_csv(worksheet);
                allText += `\n=== 工作表: ${sheetName} ===\n${sheetText}\n`;
            });

            return allText;
        } catch (error) {
            // 如果xlsx不可用，返回错误信息
            throw new Error('Excel文件解析需要安装xlsx库');
        }
    }

    /**
     * 调用API
     */
    async callAPI(requestData) {
        try {
            const response = await axios({
                method: 'POST',
                url: this.config.apiUrl,
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                    'Content-Type': 'application/json'
                },
                data: requestData,
                timeout: this.config.apiTimeout
            });

            const choice = response.data.choices?.[0];
            if (!choice) {
                throw new Error('API响应格式错误');
            }

            return {
                content: choice.message?.content || '',
                usage: response.data.usage || {}
            };

        } catch (error) {
            if (error.response) {
                throw new Error(`API调用失败: ${error.response.status} ${error.response.data?.error?.message || error.response.statusText}`);
            } else {
                throw new Error(`API调用失败: ${error.message}`);
            }
        }
    }

    /**
     * 清理临时文件
     */
    cleanupFile(filePath) {
        try {
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        } catch (error) {
            // 静默处理清理失败
        }
    }

    /**
     * 随机选择模型
     */
    selectRandomModel(models) {
        if (!models || models.length === 0) {
            throw new Error('没有可用的模型');
        }

        const randomIndex = Math.floor(Math.random() * models.length);
        return models[randomIndex];
    }
}

// VCP插件标准接口 - stdin/stdout通信
async function main() {
    // 加载环境变量
    const path = require('path');
    require('dotenv').config({ path: path.resolve(__dirname, 'config.env') });

    let inputChunks = [];
    process.stdin.setEncoding('utf8');

    for await (const chunk of process.stdin) {
        inputChunks.push(chunk);
    }
    const inputData = inputChunks.join('');

    try {
        if (!inputData.trim()) {
            console.log(JSON.stringify({
                status: "error",
                error: "FileProcessor 插件错误：未从标准输入接收到输入数据。"
            }));
            process.exit(1);
        }

        const parsedArgs = JSON.parse(inputData);
        const processor = new FileProcessor();
        const result = await processor.execute(parsedArgs);

        console.log(JSON.stringify(result));
    } catch (e) {
        const errorMessage = e.message || "FileProcessor 插件未知错误";
        console.log(JSON.stringify({
            status: "error",
            error: errorMessage.startsWith("FileProcessor 插件错误：") ? errorMessage : `FileProcessor 插件错误：${errorMessage}`
        }));
        process.exit(1);
    }
}

// 如果直接运行此文件，启动main函数
if (require.main === module) {
    main();
}

module.exports = FileProcessor;
