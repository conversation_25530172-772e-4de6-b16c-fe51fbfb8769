const BaseMcpPlugin = require('./BaseMcpPlugin');
const axios = require('axios');

class MultiAIConsensusMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'MultiAIConsensus';
        this.description = '多AI模型咨询插件 - 当需要获取其他AI模型对同一问题的看法时使用，支持同时询问多个AI模型并对比答案';
        this.vcpName = 'MultiAIConsensus';
        this.config = this.loadConfig();
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: '要询问其他AI模型的问题，建议使用清晰、具体的问题描述'
                },
                models: {
                    type: 'array',
                    items: { type: 'string' },
                    description: '指定要使用的AI模型列表，可选值: ["gpt-4o-mini", "claude-3-5-sonnet-20241022"]。不指定时使用默认模型',
                    default: this.config.defaultModels
                },
                maxModels: {
                    type: 'integer',
                    description: '最大使用的AI模型数量，建议2-3个即可',
                    default: 2,
                    minimum: 1,
                    maximum: this.config.maxConcurrent
                }
            },
            required: ['query']
        };
    }

    validateArgs(args) {
        if (!args || typeof args !== 'object') {
            throw new Error('参数必须是一个对象');
        }
        if (!args.query || typeof args.query !== 'string' || !args.query.trim()) {
            throw new Error('缺少必需参数：query（要询问的问题）');
        }
        if (args.models !== undefined) {
            if (!Array.isArray(args.models)) {
                throw new Error('models参数必须是一个数组');
            }
            if (args.models.some(model => typeof model !== 'string')) {
                throw new Error('models数组中的每个元素必须是字符串');
            }
        }
        if (args.maxModels !== undefined) {
            const maxModels = parseInt(args.maxModels);
            if (isNaN(maxModels) || maxModels < 1 || maxModels > this.config.maxConcurrent) {
                throw new Error(`maxModels必须是1到${this.config.maxConcurrent}之间的整数`);
            }
        }
    }

    async execute(args) {
        try {
            this.validateArgs(args);
            
            const {
                query,
                models = this.config.defaultModels,
                maxModels = 2,
            } = args;

            this.log('info', `开始咨询其他AI模型，问题: ${query}`);

            // 调用VCP插件执行核心逻辑
            const result = await this.callVcpPlugin({ query, models, maxModels });

            // 解析VCP插件返回的JSON结果
            let parsedResult;
            if (typeof result === 'string') {
                 try {
                    parsedResult = JSON.parse(result);
                 } catch (e) {
                    throw new Error(`VCP插件返回了无效的JSON: ${result}`);
                 }
            } else {
                parsedResult = result;
            }

            if (parsedResult.status !== 'success') {
                throw new Error(`AI模型咨询失败: ${parsedResult.error || '未知错误'}`);
            }

            this.log('success', `AI模型咨询完成`);
            
            // 返回标准格式的结果
            return {
                type: 'multi_ai_consensus',
                status: 'success',
                message: 'AI模型咨询完成',
                data: {
                    query: query,
                    models_used: parsedResult.details.selected_models,
                    responses: parsedResult.details.responses,
                    formatted_response: parsedResult.result
                }
            };

        } catch (error) {
            this.log('error', `AI模型咨询失败: ${error.message}`);
            return {
                type: 'multi_ai_consensus',
                status: 'error',
                message: `AI模型咨询失败: ${error.message}`,
                data: null
            };
        }
    }

    loadConfig() {
        return {
            defaultModels: ['gpt-4o-mini', 'claude-3-5-sonnet-20241022'],
            maxConcurrent: 3
        };
    }
}

module.exports = MultiAIConsensusMcp; 
 