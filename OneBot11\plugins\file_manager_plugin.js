/**
 * File Manager Plugin
 * 文件管理插件
 * 提供查看、搜索和管理下载文件的功能
 */

const BasePlugin = require('./base_plugin');
const fs = require('fs');
const path = require('path');

class FileManagerPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('FileManager', adapter, logger, config);

        this.priority = 50; // 中等优先级
        this.permission = 'all'; // 所有用户可用
        this.supportedTypes = ['message'];

        // 插件元信息
        this.meta = {
            version: '1.0.0',
            author: 'VCPToolBox',
            description: '文件管理插件',
            usage: '使用文件列表、文件统计、搜索文件等命令管理下载的文件',
            example: '文件列表'
        };

        // 文件管理命令
        this.commands = {
            '文件列表': this.listFiles.bind(this),
            '文件统计': this.getFileStats.bind(this),
            '搜索文件': this.searchFiles.bind(this),
            '清理文件': this.cleanFiles.bind(this),
            '文件详情': this.getFileDetails.bind(this)
        };

        this.downloadDir = './downloads';

        this.logger.info('Plugin', 'File manager plugin initialized');
    }

    /**
     * 检查是否应该处理此消息
     */
    shouldHandle(context) {
        const message = context.raw_message || context.message;
        const messageText = typeof message === 'string' ? message : 
                           Array.isArray(message) ? message.map(seg => seg.data?.text || '').join('') : '';

        return Object.keys(this.commands).some(cmd => messageText.includes(cmd));
    }

    /**
     * 处理消息
     */
    async handle(context) {
        if (!this.shouldHandle(context)) {
            return { handled: false };
        }

        try {
            const message = context.raw_message || context.message;
            const messageText = typeof message === 'string' ? message : 
                               Array.isArray(message) ? message.map(seg => seg.data?.text || '').join('') : '';

            // 查找匹配的命令
            for (const [command, handler] of Object.entries(this.commands)) {
                if (messageText.includes(command)) {
                    await handler(context, messageText);
                    return {
                        handled: true,
                        command: command
                    };
                }
            }

            return { handled: false };

        } catch (error) {
            this.logger.error('Plugin', `文件管理失败: ${error.message}`);
            await this.reply(context, `文件管理失败: ${error.message}`);
            return {
                handled: true,
                error: error.message
            };
        }
    }

    /**
     * 列出文件
     */
    async listFiles(context, messageText) {
        try {
            if (!fs.existsSync(this.downloadDir)) {
                await this.reply(context, '📁 下载目录不存在，还没有下载过任何文件。');
                return;
            }

            const fileInfo = this.loadFileInfo();
            if (!fileInfo || fileInfo.length === 0) {
                await this.reply(context, '📁 暂无文件记录。');
                return;
            }

            // 获取最近的文件（最多10个）
            const recentFiles = fileInfo.slice(-10).reverse();
            
            let response = `📁 最近下载的文件 (${recentFiles.length}/${fileInfo.length})\n\n`;

            for (const file of recentFiles) {
                const typeEmoji = this.getTypeEmoji(file.type);
                const sizeText = file.fileSize ? this.formatFileSize(parseInt(file.fileSize)) : '未知';
                const timeText = new Date(file.timestamp).toLocaleString('zh-CN');
                const senderName = file.sender?.nickname || file.sender?.card || '未知用户';

                response += `${typeEmoji} ${file.name || file.file || '未知文件'}\n`;
                response += `📏 ${sizeText} | 👤 ${senderName}\n`;
                response += `🕐 ${timeText}\n\n`;
            }

            if (fileInfo.length > 10) {
                response += `\n💡 显示最近10个文件，总共${fileInfo.length}个文件\n`;
                response += `发送"文件统计"查看详细统计信息`;
            }

            await this.reply(context, response);

        } catch (error) {
            this.logger.error('Plugin', `列出文件失败: ${error.message}`);
            await this.reply(context, `获取文件列表失败: ${error.message}`);
        }
    }

    /**
     * 获取文件统计
     */
    async getFileStats(context, messageText) {
        try {
            if (!fs.existsSync(this.downloadDir)) {
                await this.reply(context, '📁 下载目录不存在。');
                return;
            }

            const fileInfo = this.loadFileInfo();
            if (!fileInfo || fileInfo.length === 0) {
                await this.reply(context, '📊 暂无文件统计数据。');
                return;
            }

            // 统计各种类型的文件
            const stats = {
                total: fileInfo.length,
                byType: {},
                totalSize: 0,
                successfulDownloads: 0,
                failedDownloads: 0
            };

            for (const file of fileInfo) {
                // 按类型统计
                if (!stats.byType[file.type]) {
                    stats.byType[file.type] = { count: 0, size: 0 };
                }
                stats.byType[file.type].count++;

                // 大小统计
                if (file.fileSize) {
                    const size = parseInt(file.fileSize);
                    stats.totalSize += size;
                    stats.byType[file.type].size += size;
                }

                // 下载成功率统计
                if (file.downloadSuccess) {
                    stats.successfulDownloads++;
                } else if (file.downloadSuccess === false) {
                    stats.failedDownloads++;
                }
            }

            let response = `📊 文件统计报告\n\n`;
            response += `📁 总文件数: ${stats.total}\n`;
            response += `💾 总大小: ${this.formatFileSize(stats.totalSize)}\n`;
            response += `✅ 下载成功: ${stats.successfulDownloads}\n`;
            response += `❌ 下载失败: ${stats.failedDownloads}\n\n`;

            response += `📋 文件类型分布:\n`;
            for (const [type, data] of Object.entries(stats.byType)) {
                const typeEmoji = this.getTypeEmoji(type);
                response += `${typeEmoji} ${type.toUpperCase()}: ${data.count}个 (${this.formatFileSize(data.size)})\n`;
            }

            // 磁盘使用情况
            try {
                const dirStats = this.getDirStats(this.downloadDir);
                response += `\n💽 磁盘使用:\n`;
                response += `📂 文件夹数: ${dirStats.dirs}\n`;
                response += `📄 实际文件数: ${dirStats.files}\n`;
                response += `💾 实际占用: ${this.formatFileSize(dirStats.size)}\n`;
            } catch (error) {
                this.logger.warning('Plugin', `获取磁盘统计失败: ${error.message}`);
            }

            await this.reply(context, response);

        } catch (error) {
            this.logger.error('Plugin', `获取文件统计失败: ${error.message}`);
            await this.reply(context, `获取文件统计失败: ${error.message}`);
        }
    }

    /**
     * 搜索文件
     */
    async searchFiles(context, messageText) {
        try {
            // 提取搜索关键词
            const searchMatch = messageText.match(/搜索文件[：:\s]+(.+)/);
            if (!searchMatch) {
                await this.reply(context, '🔍 请指定搜索关键词，例如：搜索文件 图片');
                return;
            }

            const keyword = searchMatch[1].trim();
            const fileInfo = this.loadFileInfo();
            
            if (!fileInfo || fileInfo.length === 0) {
                await this.reply(context, '📁 暂无文件可搜索。');
                return;
            }

            // 搜索文件
            const results = fileInfo.filter(file => {
                const fileName = (file.name || file.file || '').toLowerCase();
                const fileType = file.type.toLowerCase();
                const senderName = (file.sender?.nickname || file.sender?.card || '').toLowerCase();
                const searchKey = keyword.toLowerCase();

                return fileName.includes(searchKey) || 
                       fileType.includes(searchKey) || 
                       senderName.includes(searchKey);
            });

            if (results.length === 0) {
                await this.reply(context, `🔍 未找到包含"${keyword}"的文件。`);
                return;
            }

            let response = `🔍 搜索结果 (${results.length}个文件)\n`;
            response += `关键词: "${keyword}"\n\n`;

            // 显示前5个结果
            const displayResults = results.slice(0, 5);
            for (const file of displayResults) {
                const typeEmoji = this.getTypeEmoji(file.type);
                const sizeText = file.fileSize ? this.formatFileSize(parseInt(file.fileSize)) : '未知';
                const timeText = new Date(file.timestamp).toLocaleString('zh-CN');
                const senderName = file.sender?.nickname || file.sender?.card || '未知用户';

                response += `${typeEmoji} ${file.name || file.file || '未知文件'}\n`;
                response += `📏 ${sizeText} | 👤 ${senderName}\n`;
                response += `🕐 ${timeText}\n\n`;
            }

            if (results.length > 5) {
                response += `\n💡 显示前5个结果，共找到${results.length}个文件`;
            }

            await this.reply(context, response);

        } catch (error) {
            this.logger.error('Plugin', `搜索文件失败: ${error.message}`);
            await this.reply(context, `搜索文件失败: ${error.message}`);
        }
    }

    /**
     * 清理文件
     */
    async cleanFiles(context, messageText) {
        try {
            // 检查是否有确认参数
            const confirmMatch = messageText.match(/清理文件\s+(确认|confirm)/i);
            
            if (!confirmMatch) {
                await this.reply(context, 
                    '⚠️ 文件清理操作\n\n' +
                    '此操作将删除所有下载的文件和记录！\n' +
                    '如果确认要执行，请发送：清理文件 确认'
                );
                return;
            }

            if (!fs.existsSync(this.downloadDir)) {
                await this.reply(context, '📁 下载目录不存在，无需清理。');
                return;
            }

            // 获取清理前的统计
            const beforeStats = this.getDirStats(this.downloadDir);
            
            // 删除下载目录
            fs.rmSync(this.downloadDir, { recursive: true, force: true });
            
            // 重新创建空目录
            fs.mkdirSync(this.downloadDir, { recursive: true });

            await this.reply(context, 
                '🗑️ 文件清理完成\n\n' +
                `📄 已删除文件: ${beforeStats.files}个\n` +
                `💾 释放空间: ${this.formatFileSize(beforeStats.size)}\n` +
                `📂 已删除文件夹: ${beforeStats.dirs}个`
            );

        } catch (error) {
            this.logger.error('Plugin', `清理文件失败: ${error.message}`);
            await this.reply(context, `清理文件失败: ${error.message}`);
        }
    }

    /**
     * 获取文件详情
     */
    async getFileDetails(context, messageText) {
        try {
            await this.reply(context, 
                '📋 文件管理功能说明\n\n' +
                '🔧 可用命令:\n' +
                '• 文件列表 - 查看最近下载的文件\n' +
                '• 文件统计 - 查看详细统计信息\n' +
                '• 搜索文件 [关键词] - 搜索文件\n' +
                '• 清理文件 确认 - 删除所有文件\n' +
                '• 文件详情 - 显示此帮助信息\n\n' +
                '💡 支持的文件类型:\n' +
                '🖼️ 图片 (jpg, png, gif, webp等)\n' +
                '📄 文档 (txt, pdf, doc, zip等)\n' +
                '🎥 视频 (mp4, avi等)\n' +
                '🎵 语音 (mp3, wav等)\n\n' +
                '📁 下载目录: ./downloads/\n' +
                '💾 最大文件大小: 100MB'
            );

        } catch (error) {
            this.logger.error('Plugin', `获取文件详情失败: ${error.message}`);
            await this.reply(context, `获取文件详情失败: ${error.message}`);
        }
    }

    /**
     * 加载文件信息
     */
    loadFileInfo() {
        try {
            const infoFile = path.join(this.downloadDir, 'file_info.json');
            if (!fs.existsSync(infoFile)) {
                return [];
            }

            const content = fs.readFileSync(infoFile, 'utf8');
            return JSON.parse(content);

        } catch (error) {
            this.logger.error('Plugin', `加载文件信息失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 获取目录统计信息
     */
    getDirStats(dirPath) {
        let stats = { files: 0, dirs: 0, size: 0 };

        if (!fs.existsSync(dirPath)) {
            return stats;
        }

        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const itemPath = path.join(dirPath, item);
            const itemStat = fs.statSync(itemPath);

            if (itemStat.isDirectory()) {
                stats.dirs++;
                const subStats = this.getDirStats(itemPath);
                stats.files += subStats.files;
                stats.dirs += subStats.dirs;
                stats.size += subStats.size;
            } else {
                stats.files++;
                stats.size += itemStat.size;
            }
        }

        return stats;
    }

    /**
     * 获取文件类型对应的emoji
     */
    getTypeEmoji(type) {
        const emojiMap = {
            'image': '🖼️',
            'file': '📄',
            'video': '🎥',
            'record': '🎵'
        };
        return emojiMap[type] || '📎';
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

module.exports = FileManagerPlugin;
