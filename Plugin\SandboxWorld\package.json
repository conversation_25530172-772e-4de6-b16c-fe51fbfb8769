{"name": "vcptoolbox-sandbox-world", "version": "1.0.0", "description": "VCPToolBox 沙盒世界生态系统", "main": "SandboxWorld.js", "scripts": {"start": "node SandboxWorld.js", "test": "node test.js", "install-deps": "npm install"}, "keywords": ["sandbox", "ai-agents", "simulation", "social-network", "vcptoolbox"], "author": "VCPToolBox Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/vcptoolbox/sandbox-world"}}