// Plugin/Mcp/FluxGen.js - 图片生成MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}


class FluxGenMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'FluxGen';
        this.description = '使用Flux模型生成AI图片';
        this.vcpName = 'FluxGen';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                prompt: {
                    type: 'string',
                    description: '图片生成提示词，详细描述要生成的图片内容'
                },
                resolution: {
                    type: 'string',
                    description: '图片分辨率',
                    enum: ['1024x1024', '960x1280', '768x1024', '720x1280', '720x1440'],
                    default: '1024x1024'
                },
                seed: {
                    type: 'number',
                    description: '随机种子，用于复现生成结果',
                    minimum: 0,
                    default: undefined
                }
            },
            required: ['prompt']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);
        
        this.log('info', `开始生成图片`, {
            prompt: args.prompt,
            resolution: args.resolution || '1024x1024'
        });
        
        try {
        // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);
            
            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }
            
            // 构建标准响应格式
            const response = {
                type: 'image_generation',
                status: 'success',
                message: '图片生成完成',
                data: {
                    prompt: args.prompt,
                    resolution: args.resolution || '1024x1024',
                    image_url: parsedResult?.image_url,
                    image_path: parsedResult?.image_path,
                    seed: args.seed,
                    markdown_display: `![${args.prompt.substring(0, 80)}](${parsedResult?.image_url})`,
                    generation_info: {
                        model: 'FLUX.1-schnell',
                        generation_time: parsedResult?.generation_time
                    }
                }
            };
            
            this.log('success', `图片生成完成`, {
                prompt: args.prompt,
                image_path: response.data.image_path,
                generation_time: response.data.generation_time
            });
            
            return response;
            
        } catch (error) {
            const errorResponse = {
                type: 'image_generation',
                status: 'error',
                message: error.message,
                data: {
            prompt: args.prompt,
                    error: error.message
                }
            };
            
            this.log('error', `图片生成失败`, errorResponse);
            throw errorResponse;
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查必要的环境变量
            const requiredEnvVars = ['SILICONFLOW_API_KEY'];
            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    throw new Error(`未配置${envVar}环境变量`);
                }
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = FluxGenMcp; 