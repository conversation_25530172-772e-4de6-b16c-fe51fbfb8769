/**
 * 高级约束机制系统
 * 实现基于事件程度、当前状态、模因发展的动态变化幅度控制
 * 模拟大脑皮层的自我调节机制
 */

class AdvancedConstraintSystem {
    constructor(config, logger) {
        this.config = config;
        this.logger = logger;
        
        // 事件严重程度分类
        this.eventSeverityLevels = {
            trivial: { range: [0, 0.2], name: '微不足道', baseMultiplier: 0.1 },
            minor: { range: [0.2, 0.4], name: '轻微', baseMultiplier: 0.3 },
            moderate: { range: [0.4, 0.6], name: '中等', baseMultiplier: 0.6 },
            significant: { range: [0.6, 0.8], name: '重要', baseMultiplier: 1.0 },
            major: { range: [0.8, 1.0], name: '重大', baseMultiplier: 1.5 }
        };
        
        // 心理状态阈值配置
        this.stateThresholds = {
            emotion: {
                extreme_negative: -80,
                moderate_negative: -40,
                neutral_low: -10,
                neutral_high: 10,
                moderate_positive: 40,
                extreme_positive: 80
            },
            stress: {
                extreme_relaxed: -20,
                relaxed: -10,
                normal_low: -2,
                normal_high: 2,
                stressed: 10,
                extreme_stressed: 20
            },
            affinity: {
                hostile: -60,
                unfriendly: -20,
                neutral_low: -5,
                neutral_high: 5,
                friendly: 20,
                intimate: 60
            },
            meme: {
                dormant: 0.1,
                emerging: 0.3,
                active: 0.5,
                dominant: 0.7,
                overwhelming: 0.9
            }
        };
        
        // 神经可塑性参数
        this.plasticityParameters = {
            criticalPeriod: 0.8,      // 关键期可塑性阈值
            homeostasisStrength: 0.3,  // 稳态调节强度
            adaptationRate: 0.1,       // 适应速率
            fatigueThreshold: 0.9      // 疲劳阈值
        };
    }

    /**
     * 应用高级约束机制
     */
    applyAdvancedConstraints(changes, currentStates, eventContext) {
        try {
            this.logger.info('高级约束系统', '开始应用约束机制');

            // 1. 分析事件严重程度
            const eventSeverity = this.analyzeEventSeverity(eventContext);
            
            // 2. 评估当前心理状态
            const stateAssessment = this.assessCurrentStates(currentStates);
            
            // 3. 计算动态约束参数
            const constraintParams = this.calculateDynamicConstraints(eventSeverity, stateAssessment);
            
            // 4. 应用科学的变化幅度控制
            const constrainedChanges = this.applyScientificConstraints(changes, constraintParams, currentStates);
            
            // 5. 应用神经可塑性规律
            const plasticityAdjusted = this.applyNeuroplasticityConstraints(constrainedChanges, currentStates);
            
            // 6. 应用稳态调节机制
            const homeostasisAdjusted = this.applyHomeostasisConstraints(plasticityAdjusted, currentStates);
            
            // 7. 最终安全检查
            const finalChanges = this.applySafetyConstraints(homeostasisAdjusted, currentStates);

            this.logger.success('高级约束系统', '约束机制应用完成');
            return {
                constrained_changes: finalChanges,
                constraint_analysis: {
                    event_severity: eventSeverity,
                    state_assessment: stateAssessment,
                    constraint_params: constraintParams
                }
            };

        } catch (error) {
            this.logger.error('高级约束系统', '约束应用失败:', error.message);
            return { constrained_changes: changes };
        }
    }

    /**
     * 分析事件严重程度
     */
    analyzeEventSeverity(eventContext) {
        try {
            const {
                userMessage = '',
                aiResponse = '',
                conversationHistory = [],
                emotionalIntensity = 0.5,
                topicImportance = 0.5,
                relationshipImpact = 0.5
            } = eventContext;

            // 计算消息复杂度
            const messageComplexity = this.calculateMessageComplexity(userMessage, aiResponse);
            
            // 计算情感强度影响
            const emotionalWeight = Math.pow(emotionalIntensity, 1.5); // 非线性放大
            
            // 计算话题重要性影响
            const topicWeight = topicImportance * 0.8;
            
            // 计算关系影响权重
            const relationshipWeight = relationshipImpact * 1.2;
            
            // 计算历史上下文影响
            const contextWeight = this.calculateContextualWeight(conversationHistory);
            
            // 综合计算严重程度
            const overallSeverity = (
                messageComplexity * 0.3 +
                emotionalWeight * 0.4 +
                topicWeight * 0.2 +
                relationshipWeight * 0.3 +
                contextWeight * 0.1
            ) / 1.3; // 归一化
            
            // 确定严重程度等级
            const severityLevel = this.determineSeverityLevel(overallSeverity);
            
            return {
                overall_severity: Math.min(1.0, Math.max(0.0, overallSeverity)),
                severity_level: severityLevel,
                components: {
                    message_complexity: messageComplexity,
                    emotional_weight: emotionalWeight,
                    topic_weight: topicWeight,
                    relationship_weight: relationshipWeight,
                    context_weight: contextWeight
                }
            };

        } catch (error) {
            this.logger.error('高级约束系统', '事件严重程度分析失败:', error.message);
            return { overall_severity: 0.5, severity_level: 'moderate' };
        }
    }

    /**
     * 计算消息复杂度
     */
    calculateMessageComplexity(userMessage, aiResponse) {
        const userLength = userMessage.length;
        const aiLength = aiResponse.length;
        
        // 基于长度的复杂度
        const lengthComplexity = Math.min(1.0, (userLength + aiLength) / 1000);
        
        // 基于词汇多样性的复杂度（简化版）
        const userWords = userMessage.split(/\s+/).length;
        const aiWords = aiResponse.split(/\s+/).length;
        const vocabularyComplexity = Math.min(1.0, (userWords + aiWords) / 200);
        
        // 基于标点符号的复杂度（表示情感强度）
        const punctuationCount = (userMessage.match(/[!?。！？]/g) || []).length;
        const punctuationComplexity = Math.min(1.0, punctuationCount / 5);
        
        return (lengthComplexity + vocabularyComplexity + punctuationComplexity) / 3;
    }

    /**
     * 评估当前心理状态
     */
    assessCurrentStates(currentStates) {
        try {
            const assessment = {};
            
            // 评估情绪状态
            if (currentStates.emotion) {
                assessment.emotion = this.assessEmotionState(currentStates.emotion);
            }
            
            // 评估压力状态
            if (currentStates.stress) {
                assessment.stress = this.assessStressState(currentStates.stress);
            }
            
            // 评估好感度状态
            if (currentStates.affinity) {
                assessment.affinity = this.assessAffinityState(currentStates.affinity);
            }
            
            // 评估模因状态
            if (currentStates.meme) {
                assessment.meme = this.assessMemeState(currentStates.meme);
            }
            
            // 计算整体稳定性
            assessment.overall_stability = this.calculateOverallStability(assessment);
            
            // 计算变化敏感性
            assessment.change_sensitivity = this.calculateChangeSensitivity(assessment);
            
            return assessment;

        } catch (error) {
            this.logger.error('高级约束系统', '状态评估失败:', error.message);
            return { overall_stability: 0.5, change_sensitivity: 0.5 };
        }
    }

    /**
     * 评估情绪状态
     */
    assessEmotionState(emotionState) {
        const value = emotionState.emotion_value || 0;
        const thresholds = this.stateThresholds.emotion;
        
        let category, stability, sensitivity;
        
        if (value <= thresholds.extreme_negative) {
            category = 'extreme_negative';
            stability = 0.2;
            sensitivity = 0.9;
        } else if (value <= thresholds.moderate_negative) {
            category = 'moderate_negative';
            stability = 0.4;
            sensitivity = 0.7;
        } else if (value <= thresholds.neutral_low) {
            category = 'neutral_low';
            stability = 0.7;
            sensitivity = 0.5;
        } else if (value <= thresholds.neutral_high) {
            category = 'neutral';
            stability = 0.9;
            sensitivity = 0.3;
        } else if (value <= thresholds.moderate_positive) {
            category = 'moderate_positive';
            stability = 0.6;
            sensitivity = 0.6;
        } else {
            category = 'extreme_positive';
            stability = 0.3;
            sensitivity = 0.8;
        }
        
        return {
            category,
            value,
            stability,
            sensitivity,
            extremity: Math.abs(value) / 100
        };
    }

    /**
     * 计算动态约束参数
     */
    calculateDynamicConstraints(eventSeverity, stateAssessment) {
        try {
            // 基于事件严重程度的基础乘数
            const severityMultiplier = this.eventSeverityLevels[eventSeverity.severity_level].baseMultiplier;
            
            // 基于状态稳定性的调整
            const stabilityAdjustment = 1.0 / (stateAssessment.overall_stability + 0.1);
            
            // 基于变化敏感性的调整
            const sensitivityAdjustment = stateAssessment.change_sensitivity;
            
            // 计算各维度的约束参数
            const constraintParams = {
                emotion: this.calculateDimensionConstraint('emotion', severityMultiplier, stabilityAdjustment, sensitivityAdjustment, stateAssessment),
                stress: this.calculateDimensionConstraint('stress', severityMultiplier, stabilityAdjustment, sensitivityAdjustment, stateAssessment),
                affinity: this.calculateDimensionConstraint('affinity', severityMultiplier, stabilityAdjustment, sensitivityAdjustment, stateAssessment),
                meme: this.calculateDimensionConstraint('meme', severityMultiplier, stabilityAdjustment, sensitivityAdjustment, stateAssessment)
            };
            
            return constraintParams;

        } catch (error) {
            this.logger.error('高级约束系统', '动态约束参数计算失败:', error.message);
            return {
                emotion: { max_change: 2, min_change: -2 },
                stress: { max_change: 1, min_change: -1 },
                affinity: { max_change: 1, min_change: -1 },
                meme: { max_change: 0.05, min_change: -0.05 }
            };
        }
    }

    /**
     * 计算维度约束
     */
    calculateDimensionConstraint(dimension, severityMultiplier, stabilityAdjustment, sensitivityAdjustment, stateAssessment) {
        // 基础约束值
        const baseConstraints = {
            emotion: { max: 5, min: -5 },
            stress: { max: 3, min: -3 },
            affinity: { max: 2, min: -2 },
            meme: { max: 0.1, min: -0.1 }
        };
        
        const base = baseConstraints[dimension];
        
        // 应用各种调整因子
        const adjustedMax = base.max * severityMultiplier * stabilityAdjustment * sensitivityAdjustment;
        const adjustedMin = base.min * severityMultiplier * stabilityAdjustment * sensitivityAdjustment;
        
        // 考虑当前状态的特殊约束
        const stateSpecificAdjustment = this.getStateSpecificAdjustment(dimension, stateAssessment);
        
        return {
            max_change: adjustedMax * stateSpecificAdjustment,
            min_change: adjustedMin * stateSpecificAdjustment,
            base_multiplier: severityMultiplier,
            stability_factor: stabilityAdjustment,
            sensitivity_factor: sensitivityAdjustment
        };
    }

    /**
     * 应用科学的变化幅度控制
     */
    applyScientificConstraints(changes, constraintParams, currentStates) {
        try {
            const constrainedChanges = {};
            
            // 对每个维度应用约束
            for (const [dimension, change] of Object.entries(changes)) {
                if (constraintParams[dimension]) {
                    const params = constraintParams[dimension];
                    
                    // 应用基本范围约束
                    let constrainedChange = Math.max(params.min_change, Math.min(params.max_change, change));
                    
                    // 应用非线性衰减
                    constrainedChange = this.applyNonlinearDecay(constrainedChange, dimension, currentStates);
                    
                    // 应用随机性（模拟神经系统的噪声）
                    constrainedChange = this.applyNeuralNoise(constrainedChange, dimension);
                    
                    constrainedChanges[dimension] = constrainedChange;
                } else {
                    constrainedChanges[dimension] = change;
                }
            }
            
            return constrainedChanges;

        } catch (error) {
            this.logger.error('高级约束系统', '科学约束应用失败:', error.message);
            return changes;
        }
    }

    /**
     * 应用非线性衰减
     */
    applyNonlinearDecay(change, dimension, currentStates) {
        const currentValue = this.getCurrentValue(dimension, currentStates);
        
        // 使用双曲正切函数实现软约束
        const decayFactor = Math.tanh(Math.abs(currentValue) / 50); // 调整衰减强度
        
        // 极端值时变化更困难
        return change * (1 - decayFactor * 0.5);
    }

    /**
     * 应用神经噪声
     */
    applyNeuralNoise(change, dimension) {
        // 添加小量随机噪声，模拟神经系统的随机性
        const noiseLevel = Math.abs(change) * 0.05; // 5%的噪声
        const noise = (Math.random() - 0.5) * 2 * noiseLevel;
        
        return change + noise;
    }

    /**
     * 获取当前值
     */
    getCurrentValue(dimension, currentStates) {
        switch (dimension) {
            case 'emotion':
                return currentStates.emotion?.emotion_value || 0;
            case 'stress':
                return currentStates.stress?.stress_value || 0;
            case 'affinity':
                return currentStates.affinity?.affinity_value || 0;
            case 'meme':
                return currentStates.meme?.memetic_influence || 0;
            default:
                return 0;
        }
    }

    /**
     * 应用神经可塑性约束
     */
    applyNeuroplasticityConstraints(changes, currentStates) {
        try {
            const plasticityAdjusted = {};

            for (const [dimension, change] of Object.entries(changes)) {
                // 计算可塑性因子
                const plasticityFactor = this.calculatePlasticityFactor(dimension, currentStates);

                // 应用可塑性约束
                plasticityAdjusted[dimension] = change * plasticityFactor;
            }

            return plasticityAdjusted;

        } catch (error) {
            this.logger.error('高级约束系统', '神经可塑性约束失败:', error.message);
            return changes;
        }
    }

    /**
     * 计算可塑性因子
     */
    calculatePlasticityFactor(dimension, currentStates) {
        const currentValue = this.getCurrentValue(dimension, currentStates);
        const absValue = Math.abs(currentValue);

        // 关键期效应：中等值时可塑性最高
        const criticalPeriodFactor = 1 - Math.pow((absValue - 50) / 50, 2);

        // 疲劳效应：极端值时可塑性降低
        const fatigueThreshold = this.plasticityParameters.fatigueThreshold;
        const fatigueFactor = absValue > fatigueThreshold * 100 ? 0.5 : 1.0;

        // 适应性效应：基于历史变化
        const adaptationFactor = this.plasticityParameters.adaptationRate;

        return Math.max(0.1, Math.min(1.5,
            criticalPeriodFactor * fatigueFactor * (1 + adaptationFactor)
        ));
    }

    /**
     * 应用稳态调节约束
     */
    applyHomeostasisConstraints(changes, currentStates) {
        try {
            const homeostasisAdjusted = {};

            for (const [dimension, change] of Object.entries(changes)) {
                // 计算稳态调节力
                const homeostasisForce = this.calculateHomeostasisForce(dimension, currentStates);

                // 应用稳态调节
                homeostasisAdjusted[dimension] = change + homeostasisForce;
            }

            return homeostasisAdjusted;

        } catch (error) {
            this.logger.error('高级约束系统', '稳态调节约束失败:', error.message);
            return changes;
        }
    }

    /**
     * 计算稳态调节力
     */
    calculateHomeostasisForce(dimension, currentStates) {
        const currentValue = this.getCurrentValue(dimension, currentStates);

        // 定义各维度的理想值
        const idealValues = {
            emotion: 0,      // 情绪趋向中性
            stress: 0,       // 压力趋向零
            affinity: 20,    // 好感度趋向轻微正面
            meme: 0.5        // 模因影响力趋向中等
        };

        const idealValue = idealValues[dimension] || 0;
        const deviation = currentValue - idealValue;

        // 稳态调节力与偏差成正比，但有上限
        const homeostasisStrength = this.plasticityParameters.homeostasisStrength;
        const maxForce = 0.5; // 最大调节力

        const rawForce = -deviation * homeostasisStrength * 0.01;
        return Math.max(-maxForce, Math.min(maxForce, rawForce));
    }

    /**
     * 应用安全约束
     */
    applySafetyConstraints(changes, currentStates) {
        try {
            const safeChanges = {};

            // 定义绝对安全边界
            const safetyBounds = {
                emotion: { min: -100, max: 100 },
                stress: { min: -50, max: 50 },
                affinity: { min: -100, max: 100 },
                meme: { min: 0, max: 1 }
            };

            for (const [dimension, change] of Object.entries(changes)) {
                const currentValue = this.getCurrentValue(dimension, currentStates);
                const newValue = currentValue + change;
                const bounds = safetyBounds[dimension];

                if (bounds) {
                    // 确保新值在安全范围内
                    const safeNewValue = Math.max(bounds.min, Math.min(bounds.max, newValue));
                    safeChanges[dimension] = safeNewValue - currentValue;
                } else {
                    safeChanges[dimension] = change;
                }
            }

            return safeChanges;

        } catch (error) {
            this.logger.error('高级约束系统', '安全约束应用失败:', error.message);
            return changes;
        }
    }

    /**
     * 计算整体稳定性
     */
    calculateOverallStability(assessment) {
        const stabilities = [];

        for (const [dimension, state] of Object.entries(assessment)) {
            if (state && typeof state.stability === 'number') {
                stabilities.push(state.stability);
            }
        }

        if (stabilities.length === 0) return 0.5;

        // 使用几何平均数，因为稳定性是乘性的
        const product = stabilities.reduce((acc, val) => acc * val, 1);
        return Math.pow(product, 1 / stabilities.length);
    }

    /**
     * 计算变化敏感性
     */
    calculateChangeSensitivity(assessment) {
        const sensitivities = [];

        for (const [dimension, state] of Object.entries(assessment)) {
            if (state && typeof state.sensitivity === 'number') {
                sensitivities.push(state.sensitivity);
            }
        }

        if (sensitivities.length === 0) return 0.5;

        // 使用算术平均数
        return sensitivities.reduce((acc, val) => acc + val, 0) / sensitivities.length;
    }

    /**
     * 确定严重程度等级
     */
    determineSeverityLevel(severity) {
        for (const [level, config] of Object.entries(this.eventSeverityLevels)) {
            if (severity >= config.range[0] && severity < config.range[1]) {
                return level;
            }
        }
        return 'moderate'; // 默认值
    }

    /**
     * 计算上下文权重
     */
    calculateContextualWeight(conversationHistory) {
        if (!conversationHistory || conversationHistory.length === 0) {
            return 0.3; // 默认权重
        }

        // 基于对话历史长度和最近消息的情感强度
        const historyLength = Math.min(conversationHistory.length, 10);
        const lengthWeight = historyLength / 10;

        // 简化的情感强度计算
        const recentMessages = conversationHistory.slice(-3);
        const emotionalWords = ['!', '?', '！', '？', '激动', '愤怒', '开心', '难过'];
        let emotionalCount = 0;

        recentMessages.forEach(msg => {
            emotionalWords.forEach(word => {
                if (msg.content && msg.content.includes(word)) {
                    emotionalCount++;
                }
            });
        });

        const emotionalWeight = Math.min(emotionalCount / 10, 1.0);

        return (lengthWeight + emotionalWeight) / 2;
    }

    /**
     * 获取状态特定调整
     */
    getStateSpecificAdjustment(dimension, stateAssessment) {
        const state = stateAssessment[dimension];
        if (!state) return 1.0;

        // 极端状态时减少变化幅度
        if (state.extremity > 0.8) {
            return 0.5;
        } else if (state.extremity > 0.6) {
            return 0.7;
        } else {
            return 1.0;
        }
    }

    /**
     * 评估压力状态
     */
    assessStressState(stressState) {
        const value = stressState.stress_value || 0;
        const thresholds = this.stateThresholds.stress;

        let category, stability, sensitivity;

        if (value <= thresholds.extreme_relaxed) {
            category = 'extreme_relaxed';
            stability = 0.4;
            sensitivity = 0.6;
        } else if (value <= thresholds.relaxed) {
            category = 'relaxed';
            stability = 0.7;
            sensitivity = 0.4;
        } else if (value <= thresholds.normal_low) {
            category = 'normal_low';
            stability = 0.9;
            sensitivity = 0.3;
        } else if (value <= thresholds.normal_high) {
            category = 'normal';
            stability = 1.0;
            sensitivity = 0.2;
        } else if (value <= thresholds.stressed) {
            category = 'stressed';
            stability = 0.6;
            sensitivity = 0.7;
        } else {
            category = 'extreme_stressed';
            stability = 0.3;
            sensitivity = 0.9;
        }

        return {
            category,
            value,
            stability,
            sensitivity,
            extremity: Math.abs(value) / 25
        };
    }

    /**
     * 评估好感度状态
     */
    assessAffinityState(affinityState) {
        const value = affinityState.affinity_value || 0;
        const thresholds = this.stateThresholds.affinity;

        let category, stability, sensitivity;

        if (value <= thresholds.hostile) {
            category = 'hostile';
            stability = 0.3;
            sensitivity = 0.8;
        } else if (value <= thresholds.unfriendly) {
            category = 'unfriendly';
            stability = 0.5;
            sensitivity = 0.6;
        } else if (value <= thresholds.neutral_low) {
            category = 'neutral_low';
            stability = 0.8;
            sensitivity = 0.4;
        } else if (value <= thresholds.neutral_high) {
            category = 'neutral';
            stability = 0.9;
            sensitivity = 0.3;
        } else if (value <= thresholds.friendly) {
            category = 'friendly';
            stability = 0.7;
            sensitivity = 0.5;
        } else {
            category = 'intimate';
            stability = 0.6;
            sensitivity = 0.7;
        }

        return {
            category,
            value,
            stability,
            sensitivity,
            extremity: Math.abs(value) / 100
        };
    }

    /**
     * 评估模因状态
     */
    assessMemeState(memeState) {
        const value = memeState.memetic_influence || 0;
        const thresholds = this.stateThresholds.meme;

        let category, stability, sensitivity;

        if (value <= thresholds.dormant) {
            category = 'dormant';
            stability = 0.9;
            sensitivity = 0.2;
        } else if (value <= thresholds.emerging) {
            category = 'emerging';
            stability = 0.7;
            sensitivity = 0.5;
        } else if (value <= thresholds.active) {
            category = 'active';
            stability = 0.6;
            sensitivity = 0.6;
        } else if (value <= thresholds.dominant) {
            category = 'dominant';
            stability = 0.5;
            sensitivity = 0.8;
        } else {
            category = 'overwhelming';
            stability = 0.3;
            sensitivity = 0.9;
        }

        return {
            category,
            value,
            stability,
            sensitivity,
            extremity: value
        };
    }
}

module.exports = AdvancedConstraintSystem;
