{"manifestVersion": "1.0.0", "name": "BilibiliFetch", "displayName": "Bilibili 内容获取插件", "version": "0.1.0", "description": "根据 Bilibili URL 获取视频或直播信息，支持智能token截断。", "author": "<PERSON><PERSON>", "pluginType": "synchronous", "entryPoint": {"type": "python", "command": "python BilibiliFetch.py"}, "communication": {"protocol": "stdio", "timeout": 30000}, "configSchema": {"BILIBILI_COOKIE": {"type": "string", "description": "BILIBILI_COOKIE 配置项（可选，用于访问需要登录的内容）", "required": false}, "BILIBILIFETCH_TOKEN_TRUNCATE_ENABLED": {"type": "boolean", "description": "是否启用token截断功能", "required": false, "default": true}, "BILIBILIFETCH_TOKEN_MAX_TOKENS": {"type": "integer", "description": "最大token数量限制", "required": false, "default": 16000, "minimum": 1000, "maximum": 100000}, "BILIBILIFETCH_TOKEN_TRUNCATE_MARKER": {"type": "string", "description": "内容截断时的标记文本", "required": false, "default": "...\n\n[字幕内容已截断，超过最大token限制]"}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "BilibiliFetch", "description": "调用此工具获取 Bilibili 视频或直播信息。支持智能token截断，防止内容过长。请在您的回复中，使用以下精确格式来请求，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」BilibiliFetch「末」,\nurl:「始」(必需) Bilibili 视频或直播的 URL。「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含 Bilibili 内容信息的JSON对象，包括token使用统计信息。请基于这些结果回答用户的问题或完成相关任务。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」BilibiliFetch「末」,\nurl:「始」https://www.bilibili.com/video/BV...「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}