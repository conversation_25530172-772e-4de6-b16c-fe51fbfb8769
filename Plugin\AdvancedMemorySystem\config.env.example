# 🧠 高级记忆系统 - 神经网络架构配置
# Advanced Memory System - Neural Network Configuration
# 
# 配置文档：将此文件重命名为 config.env 并根据需要修改配置值
# Documentation: Rename this file to config.env and modify the configuration values as needed

# ===== 🚀 核心功能配置 =====
# Core Functionality Configuration

# 启用高级记忆系统
# Enable Advanced Memory System
AMS_ENABLE_MEMORY_SYSTEM=true

# 每个用户最大记忆数量
# Maximum memories per user
AMS_MAX_MEMORIES_PER_USER=10000

# 最大上下文Token数量（基于js-tiktoken限制）
# Maximum context tokens (based on js-tiktoken limitation)
AMS_MAX_CONTEXT_TOKENS=4000

# 记忆重要性阈值（0.0-1.0）
# Memory importance threshold (0.0-1.0)
AMS_IMPORTANCE_THRESHOLD=0.3

# ===== 🧠 神经网络配置 =====
# Neural Network Configuration

# 神经元衰减率（每分钟）
# Neural decay rate (per minute)
AMS_NEURAL_DECAY_RATE=0.1

# 概念激活阈值
# Concept activation threshold
AMS_CONCEPT_ACTIVATION_THRESHOLD=0.4

# 工作记忆容量（3-15个概念）
# Working memory capacity (3-15 concepts)
AMS_WORKING_MEMORY_CAPACITY=10

# 神经连接强度阈值
# Neural connection strength threshold
AMS_CONNECTION_STRENGTH_THRESHOLD=0.3

# ===== 😊 情绪与学习配置 =====
# Emotion and Learning Configuration

# 情绪敏感度（0.0-1.0）
# Emotion sensitivity (0.0-1.0)
AMS_EMOTION_SENSITIVITY=0.5

# 情绪强度权重
# Emotion intensity weight
AMS_EMOTION_INTENSITY_WEIGHT=0.2

# 学习率（神经网络适应速度）
# Learning rate (neural network adaptation speed)
AMS_LEARNING_RATE=0.1

# 情绪记忆增强因子
# Emotional memory enhancement factor
AMS_EMOTIONAL_ENHANCEMENT=1.5

# ===== 🔍 检索与性能配置 =====
# Retrieval and Performance Configuration

# 记忆检索最大数量
# Maximum memories retrieved
AMS_MAX_MEMORIES_RETRIEVED=10

# 语义相似度阈值
# Semantic similarity threshold
AMS_SIMILARITY_THRESHOLD=0.3

# 重要性权重（检索时）
# Importance weight (during retrieval)
AMS_IMPORTANCE_WEIGHT=0.4

# 时间新近性权重
# Recency weight
AMS_RECENCY_WEIGHT=0.3

# 情绪匹配权重
# Emotion matching weight
AMS_EMOTION_WEIGHT=0.3

# ===== ⚙️ 高级设置 =====
# Advanced Settings

# 记忆巩固间隔（毫秒）
# Memory consolidation interval (milliseconds)
AMS_CONSOLIDATION_INTERVAL=300000

# 反思处理间隔（毫秒）
# Reflection processing interval (milliseconds)
AMS_REFLECTION_INTERVAL=600000

# 文本处理超时（毫秒）
# Text processing timeout (milliseconds)
AMS_PROCESSING_TIMEOUT=10000

# 启用调试日志
# Enable debug logging
AMS_DEBUG_MODE=false

# ===== 🎯 性能模式预设 =====
# Performance Mode Presets
# 选择一个性能模式：conservative（保守）、balanced（平衡）、aggressive（激进）
# Choose a performance mode: conservative, balanced, aggressive

# 性能模式（优先级高于单独配置）
# Performance mode (overrides individual settings)
AMS_PERFORMANCE_MODE=balanced

# conservative（保守模式）配置说明：
# - 低CPU使用率，较少的神经网络计算
# - 适合资源有限的环境
# - 衰减率高，激活阈值高

# balanced（平衡模式）配置说明：
# - 中等CPU使用率，平衡的性能
# - 适合大多数生产环境
# - 默认推荐设置

# aggressive（激进模式）配置说明：
# - 高CPU使用率，更精确的神经网络分析
# - 适合高性能服务器
# - 低衰减率，低激活阈值

# ===== 🗄️ 数据库配置 =====
# Database Configuration

# 数据库类型（sqlite）
# Database type (sqlite)
AMS_DATABASE_TYPE=sqlite

# 数据库路径前缀
# Database path prefix
AMS_DATABASE_PATH_PREFIX=data/users

# 自动清理旧记忆（天数，0为禁用）
# Auto cleanup old memories (days, 0 to disable)
AMS_AUTO_CLEANUP_DAYS=90

# 数据库连接池大小
# Database connection pool size
AMS_DB_POOL_SIZE=5

# ===== 📊 分析配置 =====
# Analysis Configuration

# 文本分析引擎（natural/simple）
# Text analysis engine (natural/simple)
AMS_TEXT_ANALYSIS_ENGINE=natural

# 启用中文分词（需要nodejieba）
# Enable Chinese word segmentation (requires nodejieba)
AMS_ENABLE_CHINESE_SEGMENTATION=true

# 概念提取最大数量
# Maximum concepts extracted
AMS_MAX_CONCEPTS_EXTRACTED=20

# 关键词提取最大数量
# Maximum keywords extracted
AMS_MAX_KEYWORDS_EXTRACTED=15

# ===== 🌐 集成配置 =====
# Integration Configuration

# 启用WebSocket推送
# Enable WebSocket push
AMS_ENABLE_WEBSOCKET_PUSH=true

# WebSocket消息类型
# WebSocket message type
AMS_WEBSOCKET_MESSAGE_TYPE=memory_update

# API端点前缀
# API endpoint prefix
AMS_API_PREFIX=/api/advanced-memory

# 跨域设置
# CORS settings
AMS_ENABLE_CORS=true

# ===== 🔒 安全配置 =====
# Security Configuration

# 启用用户隔离
# Enable user isolation
AMS_ENABLE_USER_ISOLATION=true

# 记忆内容加密（实验性）
# Memory content encryption (experimental)
AMS_ENABLE_ENCRYPTION=false

# 访问日志记录
# Access logging
AMS_ENABLE_ACCESS_LOG=true

# 敏感信息过滤
# Sensitive information filtering
AMS_ENABLE_CONTENT_FILTER=true

# ===== 📈 监控与诊断 =====
# Monitoring and Diagnostics

# 启用性能监控
# Enable performance monitoring
AMS_ENABLE_PERFORMANCE_MONITORING=true

# 内存使用报告间隔（毫秒）
# Memory usage report interval (milliseconds)
AMS_MEMORY_REPORT_INTERVAL=60000

# 启用神经网络可视化数据
# Enable neural network visualization data
AMS_ENABLE_NETWORK_VISUALIZATION=true

# 导出统计数据格式（json/csv）
# Export statistics format (json/csv)
AMS_EXPORT_FORMAT=json

# ===== 🧪 实验性功能 =====
# Experimental Features

# 启用概念关联学习
# Enable concept association learning
AMS_ENABLE_CONCEPT_LEARNING=true

# 启用情绪传播模型
# Enable emotion propagation model
AMS_ENABLE_EMOTION_PROPAGATION=true

# 启用预测性记忆检索
# Enable predictive memory retrieval
AMS_ENABLE_PREDICTIVE_RETRIEVAL=false

# 启用多模态记忆（文本+图像）
# Enable multimodal memory (text+image)
AMS_ENABLE_MULTIMODAL=false

# ===== ⚡ 优化配置 =====
# Optimization Configuration

# 启用记忆压缩
# Enable memory compression
AMS_ENABLE_MEMORY_COMPRESSION=true

# 批处理大小
# Batch processing size
AMS_BATCH_SIZE=10

# 异步处理队列大小
# Async processing queue size
AMS_ASYNC_QUEUE_SIZE=100

# 缓存TTL（秒）
# Cache TTL (seconds)
AMS_CACHE_TTL=300

# =================================================================
# 配置说明 Configuration Notes:
# 
# 1. 重要性阈值：设置为0.3意味着只有重要性≥0.3的记忆才会被检索
# 2. Token限制：基于js-tiktoken，超出限制的内容会被截断
# 3. 性能模式：选择适合您服务器的模式，可以覆盖单独的设置
# 4. 神经网络：衰减率影响概念神经元的持续时间
# 5. 情绪系统：敏感度越高，情绪分析越细致
# 
# 1. Importance threshold: 0.3 means only memories with importance ≥ 0.3 will be retrieved
# 2. Token limit: Based on js-tiktoken, content exceeding limit will be truncated
# 3. Performance mode: Choose mode suitable for your server, can override individual settings
# 4. Neural network: Decay rate affects duration of concept neurons
# 5. Emotion system: Higher sensitivity provides more detailed emotion analysis
# =================================================================

# ===== 使用说明 =====
# 1. 将此文件复制为 config.env
# 2. 根据需要修改上述配置值
# 3. 重启VCP服务器以应用新配置
# 4. 可通过管理面板的插件配置界面进行可视化配置

# ===== API访问地址 =====
# 系统状态: GET /api/advanced-memory/status
# 配置管理: GET/PUT /api/advanced-memory/config
# 记忆管理: GET /api/advanced-memory/memories
# 文本分析: POST /api/advanced-memory/tools/analyze-text
# 管理面板: GET /api/advanced-memory/admin/dashboard

# ===== 故障排除 =====
# 如遇到依赖包问题，请运行: npm install nodejieba sqlite3 natural similarity
# 如遇到权限问题，请检查data目录的读写权限
# 如遇到性能问题，可尝试调整为conservative模式 