@echo off
chcp 65001 >nul
title VCPToolBox 沙盒世界启动器

echo.
echo ========================================
echo    VCPToolBox 沙盒世界启动器
echo ========================================
echo.

cd /d "%~dp0"

echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js
    echo 请先安装Node.js 14.0.0或更高版本
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过

echo.
echo 检查依赖包...
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖包...
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已存在
)

echo.
echo 检查数据目录...
if not exist "data" mkdir data
if not exist "data\agents" mkdir data\agents
if not exist "data\events" mkdir data\events
echo ✅ 数据目录检查完成

echo.
echo 🚀 启动沙盒世界系统...
echo.
echo 启动后请访问: http://localhost:8080
echo 按 Ctrl+C 可以安全关闭系统
echo.

node start.js

echo.
echo 系统已关闭
pause
