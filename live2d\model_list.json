{"models": ["platelet/index.json", "Potion-Maker/Pio/index.json", "Potion-Maker/Tia/index.json", "bilibili-live/22/index.json", "bilibili-live/33/index.json", "ShizukuTalk/shizuku-48/index.json", "ShizukuTalk/shizuku-pajama/index.json", "KantaiCollection/murakumo/index.json", "sagiri/index.json", "chino/index.json", "小埋/index.json", "platelet-0/model.json", "pio/model_1.json", "pio/model_2.json", "pio/model_3.json", "pio/model_4.json", "pio/model_5.json", "pio/model_6.json", "pio/model_7.json", "pio/model_8.json", "pio/model_9.json", "pio/model_10.json", "pio/model_11.json", "pio/model_12.json", "pio/model_13.json", "pio/model_14.json", "pio/model_15.json", "pio/model_16.json", "pio/model_17.json", "pio/model_18.json", "pio/model_19.json", "pio/model_20.json", "pio/model_21.json", "pio/model_22.json", "pio/model_23.json", "pio/model_24.json", "pio/model_25.json", "pio/model_26.json", "pio/model_27.json", "pio/model_28.json", "pio/model_29.json", "pio/model_30.json", "pio/model_31.json", "pio/model_32.json", "pio/model_33.json", "pio/model_34.json", "pio/model_35.json", "pio/model_36.json", "pio/model_37.json", "Alice/model.json", "tororo//tororo.model.json", "bronya/model.json", "bronya_1/model.json", "date_16/date_16.model.json", "Epsilon2.1/Epsilon2.1.model.json", "fox/model.json", "hallo_16/hallo_16.model.json", "haru/haru_01.model.json", "haruto/haruto.model.json", "hijiki/hijiki.model.json", "iio/iio.model.json", "illyasviel/illyasviel.model.json", "index/model.json", "izumi/izumi.model.json", "jin/jin.model.json", "kanzaki/kanzaki.model.json", "katou_01/katou_01.model.json", "kiana/model.json", "Kobayaxi/Kobayaxi.model.json", "kuroko/kuroko.model.json", "kurumi/model.json", "liang/2.json", "live_uu/model.json", "makoto0/makoto0.model.json", "mashiro/ryoufuku.model.json", "mashiro/seifuku.model.json", "mashiro/shifuku.model.json", "mikoto/mikoto.model.json", "moon/model.json", "penchan/penchan.model.json", "ryoufuku/ryoufuku.model.json", "sakura/model.json", "saten/saten.model.json", "seele/model.json", "seifuku/seifuku.model.json", "shifuku/shifuku.model.json", "shifuku2/shifuku2.model.json", "sin/model.json", "snow_miku/model.json", "stl/stl.model.json", "theresa/model.json", "touma/touma.model.json", "uiharu/uiharu.model.json", "unitychan/unitychan.model.json", "Violet/14.json", "wanko/wanko.model.json", "wed_16/wed_16.model.json", "yukari_model/yukari_model.model.json", "yuri/model.json", "HyperdimensionNeptunia/neptune_classic/index.json", "HyperdimensionNeptunia/nepnep/index.json", "HyperdimensionNeptunia/neptune_santa/index.json", "HyperdimensionNeptunia/nepmaid/index.json", "HyperdimensionNeptunia/nepswim/index.json", "HyperdimensionNeptunia/noir_classic/index.json", "HyperdimensionNeptunia/noir/index.json", "HyperdimensionNeptunia/noir_santa/index.json", "HyperdimensionNeptunia/noireswim/index.json", "HyperdimensionNeptunia/blanc_classic/index.json", "HyperdimensionNeptunia/blanc_normal/index.json", "HyperdimensionNeptunia/blanc_swimwear/index.json", "HyperdimensionNeptunia/vert_classic/index.json", "HyperdimensionNeptunia/vert_normal/index.json", "HyperdimensionNeptunia/vert_swimwear/index.json", "HyperdimensionNeptunia/nepgear/index.json", "HyperdimensionNeptunia/nepgear_extra/index.json", "HyperdimensionNeptunia/nepgearswim/index.json", "HyperdimensionNeptunia/histoire/index.json", "HyperdimensionNeptunia/histoirenohover/index.json", "dollsfrontline/88type_1809/normal/model.json", "dollsfrontline/88type_1809/destroy/model.json", "dollsfrontline/95type_405/normal/model.json", "dollsfrontline/95type_405/destroy/model.json", "dollsfrontline/ags-30/model.json", "dollsfrontline/armor/model1.json", "dollsfrontline/armor/model2.json", "dollsfrontline/armor/model3.json", "dollsfrontline/command/model1.json", "dollsfrontline/command/model2.json", "dollsfrontline/command/model3.json", "dollsfrontline/dsr50_1801/normal/model.json", "dollsfrontline/dsr50_1801/destroy/model.json", "dollsfrontline/dsr50_2101/normal/model.json", "dollsfrontline/dsr50_2101/destroy/model.json", "dollsfrontline/fn57_2203/normal/model.json", "dollsfrontline/fortress/model1.json", "dollsfrontline/fortress/model2.json", "dollsfrontline/fortress/model3.json", "dollsfrontline/g41_2401/destroy/model.json", "dollsfrontline/g41_2401/normal/model.json", "dollsfrontline/gelina/normal/model.json", "dollsfrontline/golden/model1.json", "dollsfrontline/golden/model2.json", "dollsfrontline/golden/model3.json", "dollsfrontline/grizzly_2102/destroy/model.json", "dollsfrontline/kp31_1103/normal/model.json", "dollsfrontline/kp31_1103/destroy/model.json", "dollsfrontline/ntw20_2301/normal/model.json", "dollsfrontline/ntw20_2301/destroy/model.json", "dollsfrontline/ots14_3001/normal/model.json", "dollsfrontline/ots14_3001/destroy/model.json", "dollsfrontline/sat8_2601/normal/model.json", "dollsfrontline/sat8_2601/destroy/model.json", "dollsfrontline/shield/model1.json", "dollsfrontline/shield/model2.json", "dollsfrontline/shield/model3.json", "dollsfrontline/type64-ar_2901/destroy/model.json", "dollsfrontline/vector_1901/normal/model.json", "dollsfrontline/vector_1901/destroy/model.json", "dollsfrontline/wa2000_6/normal/model.json", "dollsfrontline/wa2000_6/destroy/model.json", "dollsfrontline/welrod_1401/normal/model.json", "dollsfrontline/welrod_1401/destroy/model.json", "Genshin Impact_RaidenShougun/RaidenShougun.pmx"], "messages": ["我是最可爱的血小板 ●'ω')", "来自 Potion Maker 的 Pio 酱 ~", "来自 Potion Maker 的 Tia 酱 ~", "来自 Bilibili Live 的 22 哦 ~", "来自 Bilibili Live 的 33 的说", "Shizuku Talk ！这里是 Shizuku ~", "Shizuku Talk ！这里是 Shizuku ~", "艦隊これくしょん / 叢雲(むらくも)", "埃罗芒阿三赛？我...我才不认识这人呢！", "欢迎光临Rabbit house ~", "我是小埋，你好主人！(●ˇ∀ˇ●)", "血小板来喽 ●'ω')", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我来喽哈哈哈φ(*￣0￣)", "我是Alice呦~ ♪", "こんにちは！トロロです。", "大鸭鸭正在想新点子 ~", "大鸭鸭又想到新点子了 ~", "你好，我是由宇宙超级无敌美少女date_16呦~", "我是Epsilon2.1，今天要做什么呢？", "来自模型 school fox 的问候", "这里是由萌萌的 hallo，16 导航的小工具", "我叫haru..请多多指教", "嗨！我是haruto 请指教", "我是有着银发的小女孩～来和我一起跳舞吧～", "我是iio，请多多指教呢～", "我是间桐樱，请指教呢", "你好呀～", "我是十六夜十六夜十六夜一六夜", "我是依照设计和花的要求运作的 <jin>", "你好，我是 kanzaki，请多多关照", "这里是桃子学园的 katou_01 酱", "我叫琪亚娜·卡斯兰娜，是某不知名后宫学园的剑术部副部长～", "你好，我是小林家的龙女仆", "黑子是萌萌的黄泉川中学的学生哦～", "请问您有何指教呢，kurumi 酱不会让您失望的", "你好，我是紲星あかり", "这里是由萌萌的 live_uu，是来共进的小工具", "这里是紲星あかり呢", "我是模拟面试的一个角色（全用三色绘制）", "我是真白，请多多指教", "我是纯白，请多多指教", "我是真白，请多多指教", "我叫mikoto，请多多指教呢～", "我是moon，请多多指教呢～", "我是penchan，请多多指教", "我是竜宮清香，请多多指教", "多重分身·我是sakura哟", "我是佐天泪子哟～请多多指教呢～", "我是希儿哟,请多多指教呢～", "我是seifuku，请多多指教", "请多多指教，我是 shifuku", "我是 shifuku2", "我是一个神秘的人物", "请多多指教，我是雪初音", "我是stl，你好啊", "我是天使学院的月宫里砂～", "我是touma，请多指教呢～", "你好，我是 uiharu ～", "大家好！我是Unity娘", "我是紫罗兰，请多多指教", "我是 wanko", "这里是由萌萌的 wed_16 导航的小工具", "<PERSON><PERSON> Miku是能够为我们唱歌的人", "你好，我是yuri，请多多指教呢～", "Neptune说：我是女神哟，要尊敬我哦~", "NepNep嗡嗡叫", "Neptune说：圣诞快乐~", "Neptune说：女仆装什么的，人家才不喜欢呢", "Neptune说：泳装...会不会太害羞了～", "诺瓦露说：我是Lastation的女神诺瓦露☆", "诺瓦露说：哼！我是黑心女神", "诺瓦露说：圣...圣诞快乐...", "诺瓦露说：泳装什么的...好害羞", "布兰说：我是Lowee的女神布兰☆", "布兰说：有什么事吗？我很忙的", "布兰说：泳装...那种羞耻的东西...", "贝尔说：我是Leanbox的女神贝尔♪", "贝尔说：你好呀~今天天气很好呢", "贝尔说：泳装...这样的话能更好地展现身材呢", "涅普汀亚说：姐姐！我来了~", "涅普汀亚说：姐姐！", "涅普汀亚说：泳装...好害羞", "伊丝托瓦尔说：请多多指教", "我是88式，请多多指教", "我坏掉了...", "95式在为您服务", "我坏掉了...", "我是AGS-30", "护甲小队待命中！", "护甲小队准备完毕！", "装甲开启！战斗准备！", "指挥小队待命中！", "指挥协调准备完毕！", "指挥模式启动！", "DSR-50为您服务", "我坏掉了...", "新年新气象，DSR-50向您问好", "系统损坏，请检修...", "FN57准备就绪", "要塞小队已就位！", "要塞防御模式启动！", "固若金汤！要塞模式！", "G41向您报到！", "系统受损...", "我是<PERSON><PERSON><PERSON>", "黄金小队已集结！", "黄金火力，全力输出！", "贵金属级别战力！", "灰熊向您问好", "KP31在此待命", "损坏状态...", "NTW20向您报告", "系统错误...", "OTs-14向您报到", "故障模式...", "SAT8已就位", "损坏检测...", "护盾小队已准备！", "护盾协调启动！", "防护模式全开！", "Type64系统故障", "Vector向您致敬", "战损状态...", "WA2000狙击就位", "受损模式...", "<PERSON><PERSON><PERSON>潜行模式", "隐蔽系统受损...", "雷电将军降临此地！稻妻之威，雷霆万钧！", "我是88式，请多多指教", "我坏掉了...", "95式在为您服务", "我坏掉了...", "我是AGS-30", "护甲小队待命中！", "护甲小队准备完毕！", "装甲开启！战斗准备！", "指挥小队待命中！", "指挥协调准备完毕！", "指挥模式启动！", "DSR-50为您服务", "我坏掉了...", "雷电将军降临此地！稻妻之威，雷霆万钧！"], "pmxModels": {"Genshin Impact_RaidenShougun/RaidenShougun.pmx": {"type": "pmx", "name": "雷电将军", "author": "原神", "textures": ["tex5.png", "tex6.png"], "description": "来自原神的雷电将军，稻妻的雷之神", "size": {"default": [480, 600], "optimal": [540, 660]}}}}