# VCP工具箱 MCP模式使用指南

## 🎯 概述

VCP工具箱现已支持两种工具调用模式：

1. **VCP模式** (传统模式): 使用自定义的VCP工具调用语法
2. **MCP模式** (新增): 兼容OpenAI Tools API标准，支持强制工具调用

## 🔧 配置要求

### 环境变量配置

在 `config.env` 文件中添加以下配置：

```env
# OpenAI Tools 配置
OPENAI_TOOLS_URL=https://your-openai-compatible-api-url.com
OPENAI_TOOLS_KEY=sk-your-openai-api-key
OPENAI_TOOLS_MODEL=gpt-4o-mini
VCP_USE_OPENAI_TOOLS=true
```

### 配置说明

- `OPENAI_TOOLS_URL`: OpenAI兼容API的URL地址
- `OPENAI_TOOLS_KEY`: API密钥
- `OPENAI_TOOLS_MODEL`: 用于工具调用的模型（推荐使用支持function calling的模型）
- `VCP_USE_OPENAI_TOOLS`: 是否启用OpenAI Tools功能

## 🚀 使用方法

### MCP模式请求格式

```json
{
  "type": "mcp",
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "user",
      "content": "用户的请求内容"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "function_name",
        "description": "函数描述",
        "parameters": {
          "type": "object",
          "properties": {
            "param1": {
              "type": "string",
              "description": "参数描述"
            }
          },
          "required": ["param1"]
        }
      }
    }
  ]
}
```

### VCP模式请求格式

```json
{
  "type": "vcp",
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "system",
      "content": "系统提示词，包含VCP工具指南: {{VarVCPGuide}}"
    },
    {
      "role": "user",
      "content": "用户的请求内容"
    }
  ]
}
```

## 🛠️ 支持的工具映射

| VCP插件 | OpenAI Function | 描述 |
|---------|----------------|------|
| FluxGen | generate_image | 使用Flux模型生成AI图片 |
| TavilySearch | search_web | 网络搜索 |
| SciCalculator | calculate | 科学计算 |
| UrlFetch | fetch_url | 获取网页内容 |
| NovelAIGen | generate_novelai_image | 生成动漫风格图片 |
| SunoGen | generate_music | 生成音乐 |
| DailyNoteWrite | write_diary | 写入日记 |
| AgentAssistant | call_agent | 调用其他AI助手 |

## 📚 API端点

### 获取可用工具列表

```bash
GET /v1/tools
```

响应示例：
```json
{
  "available": true,
  "tools_count": 8,
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "generate_image",
        "description": "使用Flux模型生成AI图片",
        "parameters": { ... }
      }
    }
  ],
  "config": {
    "openai_tools_url": "已配置",
    "openai_tools_model": "gpt-4o-mini",
    "vcp_use_openai_tools": true
  }
}
```

### 获取映射信息

```bash
GET /v1/tools/mapping
```

响应示例：
```json
{
  "mapping": {
    "FluxGen": {
      "openai_function_name": "generate_image",
      "description": "使用Flux模型生成AI图片",
      "mapped": true
    },
    "SomePlugin": {
      "mapped": false,
      "reason": "未配置OpenAI Tools映射"
    }
  },
  "total_plugins": 15,
  "mapped_plugins": 8
}
```

## 🎨 使用示例

### 1. 图片生成示例

```javascript
const request = {
  type: 'mcp',
  model: 'gpt-4o-mini',
  messages: [
    {
      role: 'user',
      content: '请生成一张美丽的风景画'
    }
  ],
  tools: [
    {
      type: 'function',
      function: {
        name: 'generate_image',
        description: '使用Flux模型生成AI图片',
        parameters: {
          type: 'object',
          properties: {
            prompt: {
              type: 'string',
              description: '图片生成提示词'
            },
            aspect_ratio: {
              type: 'string',
              description: '图片宽高比',
              enum: ['1:1', '16:9', '9:16', '4:3', '3:4']
            }
          },
          required: ['prompt']
        }
      }
    }
  ]
};
```

### 2. 多工具链式调用示例

```javascript
const request = {
  type: 'mcp',
  model: 'gpt-4o-mini',
  messages: [
    {
      role: 'user',
      content: '请搜索AI发展动态，计算2^10，然后生成相关图片'
    }
  ],
  tools: [
    {
      type: 'function',
      function: {
        name: 'search_web',
        description: '网络搜索',
        parameters: {
          type: 'object',
          properties: {
            query: { type: 'string', description: '搜索查询词' }
          },
          required: ['query']
        }
      }
    },
    {
      type: 'function',
      function: {
        name: 'calculate',
        description: '科学计算',
        parameters: {
          type: 'object',
          properties: {
            expression: { type: 'string', description: '数学表达式' }
          },
          required: ['expression']
        }
      }
    },
    {
      type: 'function',
      function: {
        name: 'generate_image',
        description: '生成图片',
        parameters: {
          type: 'object',
          properties: {
            prompt: { type: 'string', description: '图片提示词' }
          },
          required: ['prompt']
        }
      }
    }
  ]
};
```

## 🔄 工作流程

### MCP模式执行流程

1. **请求解析**: 检测到 `type: "mcp"` 和 `tools` 数组
2. **工具执行**: 按顺序依次执行所有指定的工具
3. **参数生成**: 使用OpenAI API生成每个工具的调用参数
4. **VCP转换**: 将OpenAI Function调用转换为VCP插件调用
5. **结果整合**: 收集所有工具执行结果
6. **最终响应**: 基于工具结果生成最终AI回答

### VCP模式执行流程

1. **变量替换**: 处理系统提示词中的占位符
2. **AI交互**: 与AI模型进行对话
3. **工具检测**: 检测AI回复中的VCP工具调用
4. **工具执行**: 执行检测到的VCP工具
5. **循环处理**: 支持多轮工具调用

## 🧪 测试脚本

使用提供的测试脚本来验证功能：

```bash
# 修改测试脚本中的配置
# 将 VCP_SERVER_KEY 替换为你的实际密钥

node test_mcp_mode.js
```

测试脚本将执行以下测试：
- 获取可用工具列表
- 获取映射信息
- 图片生成测试
- 网络搜索测试
- 多工具链式调用测试
- VCP模式对比测试

## ⚡ 性能特点

### MCP模式优势

- **标准化**: 兼容OpenAI Tools API标准
- **强制执行**: 确保工具按顺序执行
- **参数优化**: AI自动生成最优参数
- **错误处理**: 完善的错误处理机制

### VCP模式优势

- **灵活性**: AI自主决定是否使用工具
- **轻量级**: 无需额外的API调用
- **上下文感知**: 更好的对话上下文理解
- **自然交互**: 更自然的对话体验

## 🔧 故障排除

### 常见问题

1. **工具未启用**
   ```
   错误: OpenAI Tools功能未启用
   解决: 设置 VCP_USE_OPENAI_TOOLS=true
   ```

2. **配置不完整**
   ```
   错误: OpenAI Tools配置不完整
   解决: 检查 OPENAI_TOOLS_URL 和 OPENAI_TOOLS_KEY 配置
   ```

3. **工具映射失败**
   ```
   错误: 未找到对应的VCP插件
   解决: 检查工具名称是否在映射表中
   ```

4. **API调用失败**
   ```
   错误: OpenAI API调用失败
   解决: 检查API URL和密钥是否正确
   ```

### 调试模式

启用调试模式查看详细日志：

```env
DebugMode=true
```

## 📈 扩展指南

### 添加新的工具映射

在 `server.js` 中的 `VCP_TO_OPENAI_TOOLS_MAP` 对象中添加新映射：

```javascript
'YourPlugin': {
    name: 'your_function_name',
    description: '你的工具描述',
    parameters: {
        type: 'object',
        properties: {
            param1: {
                type: 'string',
                description: '参数描述'
            }
        },
        required: ['param1']
    }
}
```

同时在 `convertOpenAIFunctionToVCP` 函数中添加参数转换逻辑。

## 📞 技术支持

如有问题或建议，请通过以下方式联系：

- 查看日志文件: `DebugLog/` 目录
- 检查配置: `/v1/tools` 和 `/v1/tools/mapping` 端点
- 运行测试: `node test_mcp_mode.js`

## 🎉 总结

MCP模式为VCP工具箱带来了标准化的工具调用能力，同时保持了原有VCP模式的灵活性。根据你的具体需求选择合适的模式：

- **需要标准化、可预测的工具调用**: 使用MCP模式
- **需要灵活、自然的AI交互**: 使用VCP模式
- **混合使用**: 在同一个系统中同时支持两种模式 