{"name": "WorldTreeVCP", "version": "1.0.0", "description": "世界树VCP插件 - 时间架构与角色心理活动系统", "author": "VCPToolBox", "main": "WorldTreeVCP.js", "type": "vcp", "category": "psychology", "enabled": true, "dependencies": {"sqlite3": "^5.1.7", "fs": "builtin", "path": "builtin"}, "config": {"enabled": {"type": "boolean", "default": true, "description": "启用世界树VCP插件"}, "useLocalAlgorithm": {"type": "boolean", "default": true, "description": "优先使用本地算法生成心理状态"}, "psychologyUpdateInterval": {"type": "integer", "default": 300000, "description": "心理状态更新间隔（毫秒）"}, "apiUrl": {"type": "string", "default": "", "description": "API服务URL（可选）"}, "apiKey": {"type": "string", "default": "", "description": "API密钥（可选）"}, "model": {"type": "string", "default": "gpt-4o-mini", "description": "使用的AI模型"}, "timeout": {"type": "integer", "default": 120000, "description": "API请求超时时间（毫秒）"}}, "features": ["时间架构管理", "角色日程表配置", "心理状态算法计算", "心理活动内容生成", "系统消息注入", "本地算法优先", "数据库集成"], "database": {"tables": ["world_tree_configs", "psychology_activities", "time_events"], "integrates_with": "AdvancedMemorySystem"}, "api_endpoints": ["/admin_api/worldtree/configs", "/admin_api/worldtree/agents", "/admin_api/worldtree/psychology", "/admin_api/worldtree/status"], "admin_panel": {"has_interface": true, "integrated": true, "sections": ["世界树配置", "Agent管理", "心理状态监控", "时间架构设置"]}, "permissions": ["database_read", "database_write", "agent_access", "system_message_injection"], "tags": ["psychology", "worldtree", "character", "narrative", "algorithm", "local"]}