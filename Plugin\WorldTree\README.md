# 世界树VCP插件

世界树VCP插件是一个独立的时间架构与角色心理活动系统，为VCPToolBox提供丰富的角色扮演和叙事功能。

## 功能特性

### 🌍 世界背景设定
- 为每个Agent配置独特的世界背景
- 支持多种世界观设定（现实、科幻、奇幻等）
- 动态背景影响角色行为

### ⏰ 时间架构系统
- 四个时间段的独立设定（早晨、下午、傍晚、夜晚）
- 时间因素影响心理状态计算
- 本地时间格式支持

### 📅 角色日程表
- 灵活的日程配置系统
- 支持时间段和活动描述
- 动态日程影响角色状态

### 🧠 心理活动生成
- 基于本地算法的心理状态计算
- 多维度心理参数（压力、情绪、能量、专注度等）
- 实时心理活动内容生成

### 📖 叙事准则
- 自定义叙事规则系统
- 角色行为准则配置
- 一致性叙事保证

## 技术架构

### 数据库集成
- 集成现有AdvancedMemorySystem数据库
- 新增世界树专用表结构
- 兼容现有情感记忆系统

### 本地算法优先
- 优先使用本地算法计算心理状态
- 可选API支持（OpenAI兼容）
- 高性能实时计算

### 系统消息注入
- 自动注入世界树内容到system消息
- 支持VCP和MCP两种模式
- 无缝集成现有对话流程

## 安装配置

### 1. 配置文件
复制 `config.env.example` 为 `config.env` 并根据需要修改：

```bash
cp config.env.example config.env
```

### 2. 数据库
插件会自动使用AdvancedMemorySystem的数据库，无需额外配置。

### 3. 启动
插件会在服务器启动时自动初始化。

## 使用方法

### 管理面板配置
1. 访问管理面板
2. 点击"世界树VCP"菜单项
3. 在"Agent配置"标签页中为Agent创建配置
4. 填写世界背景、时间架构、角色日程表等信息
5. 保存配置

### API接口
- `GET /admin_api/worldtree/configs` - 获取所有配置
- `GET /admin_api/worldtree/configs/:agentName` - 获取特定Agent配置
- `POST /admin_api/worldtree/configs/:agentName` - 创建/更新配置
- `GET /admin_api/worldtree/psychology/:userId/:agentName` - 获取心理状态
- `GET /admin_api/worldtree/status` - 获取插件状态

### 心理状态算法
插件使用多维度算法计算心理状态：

```javascript
心理状态 = {
  压力: 基于情感记忆数据 + 时间因素,
  情绪: 基于好感度数据 + 环境因素,
  能量: 基于时间段 + 任务负荷,
  专注: 基于时间段 + 环境干扰,
  心情: 基于情绪唤醒度 + 时间因素
}
```

## 配置示例

### 世界背景
```
在一个充满魔法的奇幻世界中，你是一位博学的图书管理员，
掌管着古老的魔法图书馆。这里收藏着无数珍贵的魔法典籍和古老的智慧。
```

### 时间架构
- **早晨**: 图书馆刚刚开放，阳光透过彩色玻璃窗洒在书架上
- **下午**: 最繁忙的时段，许多学者和冒险者前来查阅资料
- **傍晚**: 准备闭馆，整理一天的借阅记录
- **夜晚**: 独自研究古老的魔法文献

### 角色日程表
- `09:00-12:00`: 整理新到的魔法典籍
- `14:00-17:00`: 协助访客查找资料
- `19:00-22:00`: 研究古代魔法理论

### 叙事准则
- `知识渊博`: 对各种魔法知识都有深入了解
- `谨慎细致`: 处理古老典籍时格外小心
- `乐于助人`: 愿意帮助真诚的求知者

## 开发说明

### 文件结构
```
Plugin/WorldTree/
├── WorldTreeVCP.js          # 主插件文件
├── plugin-manifest.json     # 插件清单
├── config.env.example       # 配置模板
├── config.env              # 实际配置
└── README.md               # 说明文档
```

### 扩展开发
插件提供了完整的API接口，可以轻松扩展功能：

1. 添加新的心理状态维度
2. 扩展时间架构系统
3. 增加更多叙事规则类型
4. 集成外部AI服务

## 故障排除

### 常见问题
1. **插件未初始化**: 检查数据库连接和配置文件
2. **心理状态异常**: 检查AdvancedMemorySystem是否正常运行
3. **配置保存失败**: 检查数据库写入权限

### 日志查看
插件日志会输出到服务器主日志中，可以在管理面板的"服务器日志"中查看。

## 版本历史

### v1.0.0
- 初始版本发布
- 基础世界树功能
- 本地算法心理状态计算
- 管理面板集成

## 许可证

本插件遵循VCPToolBox项目的许可证。
