# 多AI协商插件使用指南

## 概述
多AI协商插件允许同时询问多个AI模型，收集各自的回复并清晰展示。插件支持VCP和MCP两种协议，可以灵活配置使用的模型。

## 主要功能
- 🤖 **多模型并行查询**：同时向多个AI模型发送相同问题
- 📊 **结果收集展示**：清晰展示每个AI模型的回复和响应时间
- ⚡ **高效异步处理**：使用Promise.allSettled确保性能
- 🛡️ **错误容错机制**：部分模型失败不影响整体结果
- 🔧 **灵活配置管理**：支持自定义模型列表和参数
- 🔗 **配置继承**：自动继承主配置的API设置

## 快速开始

### VCP调用示例（基本使用）
```
{{VCPMultiAIConsensus}}
参数：{
  "query": "什么是人工智能？"
}
```

### 指定特定模型
```
{{VCPMultiAIConsensus}}
参数：{
  "query": "比较React和Vue的优缺点",
  "models": ["gpt-4o", "claude-3-5-sonnet-20241022"],
  "maxModels": 2
}
```

### 详细模式（包含Token使用信息）
```
{{VCPMultiAIConsensus}}
参数：{
  "query": "如何学习编程？",
  "includeDetails": true,
  "maxModels": 3
}
```

## MCP调用示例

### 基本调用
```json
{
  "method": "tools/call",
  "params": {
    "name": "multi_ai_consensus",
    "arguments": {
      "query": "什么是量子计算？"
    }
  }
}
```

### 高级配置
```json
{
  "method": "tools/call",
  "params": {
    "name": "multi_ai_consensus",
    "arguments": {
      "query": "解释机器学习的基本概念",
      "models": ["gpt-4o-mini", "claude-3-5-sonnet-20241022", "gemini-2.5-flash-preview-05-20"],
      "maxModels": 3,
      "includeDetails": true
    }
  }
}
```

## 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `query` | string | 是 | - | 要询问所有AI模型的问题 |
| `models` | array | 否 | 配置的默认模型 | 指定要使用的模型列表 |
| `maxModels` | number | 否 | 3 | 最大使用模型数量 |
| `includeDetails` | boolean | 否 | true | 是否包含详细响应信息 |

## 输出格式

插件会返回格式化的结果，包含：
- 📊 问题和总体统计
- 🤖 每个模型的回复内容
- ⏱️ 各模型的响应时间
- ❌ 失败模型的错误信息
- ✅ 成功响应的统计信息

## 配置说明

### 配置继承机制
- 如果插件配置文件中的API设置为空，会自动继承主配置文件（config.env）的设置
- 插件配置优先级高于主配置

### 配置项说明

| 配置项 | 说明 | 继承来源 |
|--------|------|----------|
| `MULTI_AI_API_URL` | API地址 | 主配置的 `API_URL` |
| `MULTI_AI_API_KEY` | API密钥 | 主配置的 `YUANPLUS_API_KEY` |
| `MULTI_AI_MAX_CONCURRENT` | 最大并发数 | 默认3 |
| `MULTI_AI_TIMEOUT` | 请求超时时间 | 默认30000ms |
| `MULTI_AI_AVAILABLE_MODELS` | 可用模型列表 | 用逗号分隔 |
| `MULTI_AI_DEFAULT_MODELS` | 默认模型列表 | 用逗号分隔 |

### 管理面板配置
在VCPToolBox管理面板中可以配置：
- API服务器地址和密钥
- 可用模型列表
- 默认模型选择
- 并发数和超时设置

## 注意事项

1. **模型验证**：只有在可用模型列表中的模型才会被使用
2. **错误处理**：单个模型失败不会影响其他模型的执行
3. **性能考虑**：并发请求数量受到配置限制
4. **Token消耗**：同时调用多个模型会增加Token使用量
5. **响应时间**：总体响应时间取决于最慢的模型

## 故障排除

### 常见问题
1. **所有模型都失败**：检查API配置和网络连接
2. **模型不可用**：确认模型名称在可用列表中
3. **配置继承失败**：检查主配置文件路径和格式
4. **超时错误**：调整TIMEOUT配置或网络环境

### 调试模式
查看控制台日志可以获取详细的执行信息，包括：
- 模型选择过程
- 每个请求的响应时间
- 具体的错误信息 

## 概述
多AI协商插件允许同时询问多个AI模型，收集各自的回复并清晰展示。插件支持VCP和MCP两种协议，可以灵活配置使用的模型。

## 主要功能
- 🤖 **多模型并行查询**：同时向多个AI模型发送相同问题
- 📊 **结果收集展示**：清晰展示每个AI模型的回复和响应时间
- ⚡ **高效异步处理**：使用Promise.allSettled确保性能
- 🛡️ **错误容错机制**：部分模型失败不影响整体结果
- 🔧 **灵活配置管理**：支持自定义模型列表和参数
- 🔗 **配置继承**：自动继承主配置的API设置

## 快速开始

### VCP调用示例（基本使用）
```
{{VCPMultiAIConsensus}}
参数：{
  "query": "什么是人工智能？"
}
```

### 指定特定模型
```
{{VCPMultiAIConsensus}}
参数：{
  "query": "比较React和Vue的优缺点",
  "models": ["gpt-4o", "claude-3-5-sonnet-20241022"],
  "maxModels": 2
}
```

### 详细模式（包含Token使用信息）
```
{{VCPMultiAIConsensus}}
参数：{
  "query": "如何学习编程？",
  "includeDetails": true,
  "maxModels": 3
}
```

## MCP调用示例

### 基本调用
```json
{
  "method": "tools/call",
  "params": {
    "name": "multi_ai_consensus",
    "arguments": {
      "query": "什么是量子计算？"
    }
  }
}
```

### 高级配置
```json
{
  "method": "tools/call",
  "params": {
    "name": "multi_ai_consensus",
    "arguments": {
      "query": "解释机器学习的基本概念",
      "models": ["gpt-4o-mini", "claude-3-5-sonnet-20241022", "gemini-2.5-flash-preview-05-20"],
      "maxModels": 3,
      "includeDetails": true
    }
  }
}
```

## 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `query` | string | 是 | - | 要询问所有AI模型的问题 |
| `models` | array | 否 | 配置的默认模型 | 指定要使用的模型列表 |
| `maxModels` | number | 否 | 3 | 最大使用模型数量 |
| `includeDetails` | boolean | 否 | true | 是否包含详细响应信息 |

## 输出格式

插件会返回格式化的结果，包含：
- 📊 问题和总体统计
- 🤖 每个模型的回复内容
- ⏱️ 各模型的响应时间
- ❌ 失败模型的错误信息
- ✅ 成功响应的统计信息

## 配置说明

### 配置继承机制
- 如果插件配置文件中的API设置为空，会自动继承主配置文件（config.env）的设置
- 插件配置优先级高于主配置

### 配置项说明

| 配置项 | 说明 | 继承来源 |
|--------|------|----------|
| `MULTI_AI_API_URL` | API地址 | 主配置的 `API_URL` |
| `MULTI_AI_API_KEY` | API密钥 | 主配置的 `YUANPLUS_API_KEY` |
| `MULTI_AI_MAX_CONCURRENT` | 最大并发数 | 默认3 |
| `MULTI_AI_TIMEOUT` | 请求超时时间 | 默认30000ms |
| `MULTI_AI_AVAILABLE_MODELS` | 可用模型列表 | 用逗号分隔 |
| `MULTI_AI_DEFAULT_MODELS` | 默认模型列表 | 用逗号分隔 |

### 管理面板配置
在VCPToolBox管理面板中可以配置：
- API服务器地址和密钥
- 可用模型列表
- 默认模型选择
- 并发数和超时设置

## 注意事项

1. **模型验证**：只有在可用模型列表中的模型才会被使用
2. **错误处理**：单个模型失败不会影响其他模型的执行
3. **性能考虑**：并发请求数量受到配置限制
4. **Token消耗**：同时调用多个模型会增加Token使用量
5. **响应时间**：总体响应时间取决于最慢的模型

## 故障排除

### 常见问题
1. **所有模型都失败**：检查API配置和网络连接
2. **模型不可用**：确认模型名称在可用列表中
3. **配置继承失败**：检查主配置文件路径和格式
4. **超时错误**：调整TIMEOUT配置或网络环境

### 调试模式
查看控制台日志可以获取详细的执行信息，包括：
- 模型选择过程
- 每个请求的响应时间
- 具体的错误信息 
 
 