/**
 * 测试前端修复效果
 * 验证心理状态监控数据是否正确显示
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:6005/admin_api';

async function testFrontendFix() {
    console.log('🔧 测试前端修复效果...\n');
    
    try {
        // 1. 测试世界树状态API
        console.log('1. 测试世界树状态API...');
        const statusResponse = await axios.get(`${API_BASE_URL}/worldtree/status`);
        const statusData = statusResponse.data;
        
        if (statusData.success) {
            console.log('✅ 世界树状态API正常');
            console.log(`   初始化状态: ${statusData.status.isInitialized}`);
            console.log(`   配置Agent数量: ${statusData.status.configuredAgents || statusData.status.statistics?.configuredAgents}`);
            console.log(`   运行时间: ${(statusData.status.uptime / 1000).toFixed(1)}秒`);
        } else {
            console.log(`❌ 世界树状态API失败: ${statusData.error}`);
        }
        console.log('');
        
        // 2. 测试实时心理状态API
        console.log('2. 测试实时心理状态API...');
        const realtimeResponse = await axios.get(`${API_BASE_URL}/worldtree/psychology/realtime`);
        const realtimeData = realtimeResponse.data;
        
        if (realtimeData.success) {
            console.log('✅ 实时心理状态API正常');
            console.log(`   数据时间戳: ${realtimeData.timestamp}`);
            console.log(`   Agent数据数量: ${realtimeData.data.length}`);
            
            if (realtimeData.data.length > 0) {
                console.log('\n   Agent心理状态数据:');
                realtimeData.data.forEach((agent, index) => {
                    console.log(`   Agent ${index + 1}: ${agent.agentName}`);
                    console.log(`     专注程度: ${agent.focus?.toFixed(1) || '--'}`);
                    console.log(`     精力水平: ${agent.energy?.toFixed(1) || '--'}`);
                    console.log(`     疲劳度: ${agent.fatigue?.toFixed(1) || '--'}`);
                    console.log(`     警觉性: ${agent.alertness?.toFixed(1) || '--'}`);
                    console.log(`     饥饿感: ${agent.hunger?.toFixed(1) || '--'}`);
                    console.log(`     时间段: ${agent.timePeriod || '--'}`);
                    console.log(`     最后更新: ${agent.lastUpdate || '--'}`);
                });
                
                // 计算平均值
                const totals = realtimeData.data.reduce((acc, agent) => {
                    acc.focus += agent.focus || 0;
                    acc.energy += agent.energy || 0;
                    acc.fatigue += agent.fatigue || 0;
                    acc.alertness += agent.alertness || 0;
                    return acc;
                }, { focus: 0, energy: 0, fatigue: 0, alertness: 0 });
                
                const count = realtimeData.data.length;
                console.log('\n   统计数据:');
                console.log(`     平均专注度: ${(totals.focus / count).toFixed(1)}`);
                console.log(`     平均精力: ${(totals.energy / count).toFixed(1)}`);
                console.log(`     平均疲劳度: ${(totals.fatigue / count).toFixed(1)}`);
                console.log(`     平均警觉性: ${(totals.alertness / count).toFixed(1)}`);
            } else {
                console.log('   ⚠️ 暂无Agent心理状态数据');
            }
        } else {
            console.log(`❌ 实时心理状态API失败: ${realtimeData.error}`);
        }
        console.log('');
        
        // 3. 测试心理活动日志API
        console.log('3. 测试心理活动日志API...');
        try {
            const logsResponse = await axios.get(`${API_BASE_URL}/worldtree/psychology/logs`);
            const logsData = logsResponse.data;
            
            if (logsData.success) {
                console.log('✅ 心理活动日志API正常');
                console.log(`   日志条数: ${logsData.logs.length}`);
                
                if (logsData.logs.length > 0) {
                    console.log('\n   最近的心理活动:');
                    logsData.logs.slice(0, 3).forEach((log, index) => {
                        console.log(`   ${index + 1}. ${log.agentName}: ${log.content}`);
                        console.log(`      时间: ${new Date(log.timestamp).toLocaleString()}`);
                        if (log.physicalState) {
                            console.log(`      状态: 专注${log.physicalState.focus?.toFixed(1) || '--'} 精力${log.physicalState.energy?.toFixed(1) || '--'} 疲劳${log.physicalState.fatigue?.toFixed(1) || '--'}`);
                        }
                    });
                }
            } else {
                console.log(`❌ 心理活动日志API失败: ${logsData.error}`);
            }
        } catch (error) {
            console.log(`⚠️ 心理活动日志API请求失败: ${error.message}`);
        }
        console.log('');
        
        // 4. 测试配置API
        console.log('4. 测试配置API...');
        try {
            const configsResponse = await axios.get(`${API_BASE_URL}/worldtree/configs`);
            const configsData = configsResponse.data;
            
            if (configsData.success) {
                console.log('✅ 配置API正常');
                console.log(`   配置数量: ${configsData.count}`);
                console.log(`   已配置Agent: ${configsData.configs.filter(c => c.hasConfig).length}`);
                
                configsData.configs.forEach(config => {
                    console.log(`   - ${config.agentName}: ${config.hasConfig ? '已配置' : '未配置'}`);
                });
            } else {
                console.log(`❌ 配置API失败: ${configsData.error}`);
            }
        } catch (error) {
            console.log(`⚠️ 配置API请求失败: ${error.message}`);
        }
        console.log('');
        
        console.log('🎉 前端修复测试完成！');
        console.log('\n📋 修复内容总结:');
        console.log('✅ 移除了硬编码的假数据（平均专注度75.2、平均精力68.4等）');
        console.log('✅ 修复了心理状态表格的数据更新逻辑');
        console.log('✅ 添加了真实的API数据获取和显示');
        console.log('✅ 修复了进度条更新函数');
        console.log('✅ 添加了心理活动日志的动态加载');
        console.log('✅ 修复了统计数据的实时计算');
        console.log('\n🌐 请在浏览器中访问: http://localhost:6005/AdminPanel');
        console.log('   然后点击"世界树VCP" -> "心理状态监控"查看修复效果');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
}

// 运行测试
testFrontendFix();
