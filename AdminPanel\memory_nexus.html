<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆深渊 - Memory Nexus</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/memory_nexus.css">
</head>
<body>
    <!-- 神经网络背景 -->
    <div class="neural-background">
        <canvas id="neural-canvas"></canvas>
    </div>

    <!-- 主界面容器 -->
    <div class="nexus-container">
        <!-- 顶部状态栏 -->
        <header class="nexus-header">
            <div class="header-left">
                <div class="nexus-logo">
                    <div class="logo-core"></div>
                    <span class="logo-text">MEMORY NEXUS</span>
                </div>
                <div class="system-status">
                    <div class="status-indicator active"></div>
                    <span>系统在线</span>
                </div>
            </div>
            <div class="header-center">
                <!-- 系统监控面板 -->
                <div class="system-monitor">
                    <div class="monitor-item">
                        <span class="monitor-label">CPU</span>
                        <span class="monitor-value" id="cpu-usage">0%</span>
                        <div class="monitor-bar">
                            <div class="monitor-fill" id="cpu-bar"></div>
                        </div>
                    </div>
                    <div class="monitor-item">
                        <span class="monitor-label">GPU</span>
                        <span class="monitor-value" id="gpu-usage">0%</span>
                        <div class="monitor-bar">
                            <div class="monitor-fill" id="gpu-bar"></div>
                        </div>
                    </div>
                    <div class="monitor-item">
                        <span class="monitor-label">内存</span>
                        <span class="monitor-value" id="memory-usage">0%</span>
                        <div class="monitor-bar">
                            <div class="monitor-fill" id="memory-bar"></div>
                        </div>
                    </div>
                    <div class="monitor-item">
                        <span class="monitor-label">显存</span>
                        <span class="monitor-value" id="gpu-memory">0MB</span>
                        <div class="monitor-bar">
                            <div class="monitor-fill" id="gpu-memory-bar"></div>
                        </div>
                    </div>
                    <div class="monitor-item">
                        <span class="monitor-label">运行时间</span>
                        <span class="monitor-value" id="uptime">0s</span>
                    </div>
                    <div class="monitor-item">
                        <span class="monitor-label">显卡型号</span>
                        <span class="monitor-value" id="gpu-name">检测中...</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="neural-activity">
                    <div class="activity-bar"></div>
                    <div class="activity-bar"></div>
                    <div class="activity-bar"></div>
                    <div class="activity-bar"></div>
                </div>
                <button class="sync-button" id="sync-data">
                    <span class="sync-icon">⟲</span>
                    同步数据
                </button>

            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="nexus-main">
            <!-- 左侧导航面板 -->
            <nav class="memory-nav">
                <div class="nav-header">
                    <h3>记忆深渊</h3>
                    <div class="nav-stats">
                        <span id="total-records">0</span> 条记录
                    </div>
                </div>
                <div class="navigation-panel">
                    <!-- 动态生成分类导航 -->
                </div>
            </nav>

            <!-- 中央数据面板 -->
            <section class="data-panel">
                <!-- 欢迎界面 -->
                <div class="welcome-screen" id="welcome-screen">
                    <div class="welcome-content">
                        <div class="nexus-eye">
                            <div class="eye-core"></div>
                            <div class="eye-ring"></div>
                        </div>
                        <h2>记忆深渊已激活</h2>
                        <p>选择左侧分类以探索记忆数据</p>
                        <div class="system-info">
                            <div class="info-item">
                                <span class="info-label">数据库状态</span>
                                <span class="info-value" id="db-status">检测中...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">记忆总量</span>
                                <span class="info-value" id="memory-count">计算中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格界面 -->
                <div class="table-interface" id="table-interface" style="display: none;">
                    <!-- 表格控制栏 -->
                    <div class="table-controls">
                        <div class="controls-left">
                            <h3 id="table-title">数据表</h3>
                            <div class="table-stats">
                                <span id="current-page">1</span> / <span id="total-pages">1</span>
                                <span class="separator">|</span>
                                <span id="filtered-count">0</span> 条记录
                            </div>
                        </div>
                        <div class="controls-right">
                            <div class="search-box">
                                <input type="text" id="search-input" placeholder="搜索记忆...">
                                <button class="search-btn" id="search-btn">🔍</button>
                            </div>
                            <div class="batch-operations">
                                <button class="batch-btn" id="batch-delete" disabled title="删除选中的记录">批量删除</button>
                                <button class="danger-btn" id="delete-search-results" style="display: none;" title="删除当前搜索结果">删除搜索结果</button>
                                <button class="danger-btn" id="delete-filter-results" style="display: none;" title="删除当前筛选结果">删除筛选结果</button>
                                <button class="danger-btn" id="clear-table" title="清空整个板块">清空板块</button>
                            </div>
                            <button class="add-btn" id="add-record">+ 新增</button>
                        </div>
                    </div>

                    <!-- 筛选控制栏 -->
                    <div class="filter-controls">
                        <div class="filter-group">
                            <label for="filter-user-id">用户ID:</label>
                            <div class="filter-input-group">
                                <input type="text" id="filter-user-id" placeholder="筛选用户ID">
                                <button class="filter-clear-btn" onclick="memoryNexus.clearSingleFilter('user-id')" title="清除用户ID筛选">×</button>
                            </div>
                        </div>
                        <div class="filter-group">
                            <label for="filter-persona-name">角色名称:</label>
                            <div class="filter-input-group">
                                <input type="text" id="filter-persona-name" placeholder="筛选角色名称">
                                <button class="filter-clear-btn" onclick="memoryNexus.clearSingleFilter('persona-name')" title="清除角色名称筛选">×</button>
                            </div>
                        </div>
                        <div class="filter-group">
                            <label for="filter-date-start">开始时间:</label>
                            <div class="filter-input-group">
                                <input type="datetime-local" id="filter-date-start">
                                <button class="filter-clear-btn" onclick="memoryNexus.clearSingleFilter('date-start')" title="清除开始时间">×</button>
                            </div>
                        </div>
                        <div class="filter-group">
                            <label for="filter-date-end">结束时间:</label>
                            <div class="filter-input-group">
                                <input type="datetime-local" id="filter-date-end">
                                <button class="filter-clear-btn" onclick="memoryNexus.clearSingleFilter('date-end')" title="清除结束时间">×</button>
                            </div>
                        </div>
                        <div class="filter-actions">
                            <button class="apply-filter-btn" id="apply-filters" title="应用筛选条件">🔍 应用筛选</button>
                            <button class="filter-btn" id="clear-filters" title="清除所有筛选条件">🗑️ 清除筛选</button>
                        </div>
                    </div>

                    <!-- 筛选状态显示 -->
                    <div class="filter-status" id="filter-status" style="display: none;">
                        <div class="filter-status-header">
                            <span class="filter-status-title">🔍 当前筛选条件</span>
                            <button class="filter-status-close" id="filter-status-close" title="隐藏筛选状态">×</button>
                        </div>
                        <div class="filter-status-content" id="filter-status-content">
                            <!-- 筛选条件将在这里动态显示 -->
                        </div>
                    </div>

                    <!-- 数据统计显示 -->
                    <div class="data-stats" id="data-stats">
                        <div class="stats-item">
                            <span class="stats-label">当前显示:</span>
                            <span class="stats-value" id="current-count">0</span>
                            <span class="stats-unit">条</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">总计:</span>
                            <span class="stats-value" id="total-count">0</span>
                            <span class="stats-unit">条</span>
                        </div>
                        <div class="stats-item" id="filter-stats" style="display: none;">
                            <span class="stats-label">筛选结果:</span>
                            <span class="stats-value" id="filtered-count">0</span>
                            <span class="stats-unit">条</span>
                        </div>
                        <div class="stats-item" id="search-stats" style="display: none;">
                            <span class="stats-label">搜索结果:</span>
                            <span class="stats-value" id="search-count">0</span>
                            <span class="stats-unit">条</span>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div class="table-container">
                        <table class="memory-table" id="memory-table">
                            <thead id="table-head">
                                <!-- 动态生成 -->
                            </thead>
                            <tbody id="table-body">
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控制 -->
                    <div class="pagination-controls">
                        <button class="page-btn" id="prev-page">◀ 上一页</button>
                        <div class="page-numbers" id="page-numbers">
                            <!-- 动态生成 -->
                        </div>
                        <button class="page-btn" id="next-page">下一页 ▶</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal-overlay" id="edit-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">编辑记录</h3>
                <button class="modal-close" id="modal-close">✕</button>
            </div>
            <div class="modal-body">
                <form id="edit-form">
                    <!-- 动态生成表单字段 -->
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancel-edit">取消</button>
                <button class="btn-primary" id="save-edit">保存</button>
                <button class="btn-danger" id="delete-record" style="display: none;">删除</button>
            </div>
        </div>
    </div>

    <!-- 通知系统 -->
    <div class="notification-container" id="notifications">
        <!-- 动态生成通知 -->
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="loading-text">处理中...</div>
        </div>
    </div>

    <script src="js/memory_nexus.js"></script>
</body>
</html>
