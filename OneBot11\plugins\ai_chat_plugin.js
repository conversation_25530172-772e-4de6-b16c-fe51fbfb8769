/**
 * AI对话插件
 * 基于主框架的MCP模式实现智能对话功能
 * 支持配置文件和热重载
 */

const BasePlugin = require('./base_plugin');
const { helper } = require('./plugin_helper');
const path = require('path');
const fs = require('fs');

class AIChatPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('AIChatPlugin', adapter, logger, config);

        // 插件元信息
        this.meta = {
            author: 'VCPToolBox',
            version: '3.0.0',
            description: 'AI智能对话插件 - 支持配置文件和热重载',
            usage: '发送消息与AI对话',
            example: '失语 你好'
        };

        // 配置文件路径
        this.configPath = path.join(__dirname, '..', 'config.json');
        this.lastConfigModified = 0;

        // 创建NapCat API实例
        this.napCatAPI = helper.createNapCatAPI(adapter);

        // 聊天记录目录
        this.chatLogsDir = path.join(__dirname, '..', 'chat_logs');

        // 加载配置
        this.loadConfig();

        // 启动配置文件监听
        this.startConfigWatcher();

        console.log(`✅ ${this.name} 插件初始化完成 (支持热重载)`);
    }

    /**
     * 加载配置文件
     */
    loadConfig() {
        try {
            const configData = fs.readFileSync(this.configPath, 'utf8');
            const config = JSON.parse(configData);
            const aiChatConfig = config.AI_CHAT_PLUGIN || {};

            // 更新插件基本配置
            this.priority = aiChatConfig.PRIORITY || 40;
            this.permission = aiChatConfig.PERMISSION || 'all';
            this.supportedTypes = ['message'];
            this.enabled = aiChatConfig.ENABLED !== false;

            // 更新AI配置
            const aiConfig = aiChatConfig.AI_CONFIG || {};
            this.aiConfig = {
                apiUrl: aiConfig.API_URL || 'http://localhost:6005/v1/chat/completions',
                apiKey: aiConfig.API_KEY || '114514',
                model: aiConfig.MODEL || 'gemini-2.5-pro-free',
                type: aiConfig.TYPE || 'mcp',
                enable_context: aiConfig.ENABLE_CONTEXT !== false,
                memory_tracking: aiConfig.MEMORY_TRACKING !== false,
                maxContextSize: aiConfig.MAX_CONTEXT_SIZE || 15,
                stream: aiConfig.STREAM || false,
                requestTimeout: aiConfig.REQUEST_TIMEOUT || 30000,
                maxRetries: aiConfig.MAX_RETRIES || 3,
                retryDelay: aiConfig.RETRY_DELAY || 1000,
                agent: aiConfig.AGENT || '失语'
            };

            // 更新触发配置
            const triggerConfig = aiChatConfig.TRIGGER_CONFIG || {};
            this.triggerKeywords = triggerConfig.KEYWORDS || ['失语', '失语症'];
            this.enableAtTrigger = triggerConfig.ENABLE_AT_TRIGGER !== false;
            this.enableReplyTrigger = triggerConfig.ENABLE_REPLY_TRIGGER !== false;
            this.enablePrivateTrigger = triggerConfig.ENABLE_PRIVATE_TRIGGER || false;
            this.caseSensitive = triggerConfig.CASE_SENSITIVE || false;

            // 更新响应配置
            const responseConfig = aiChatConfig.RESPONSE_CONFIG || {};
            this.maxResponseLength = responseConfig.MAX_RESPONSE_LENGTH || 0;
            this.enableTypingIndicator = responseConfig.ENABLE_TYPING_INDICATOR || false;
            this.responseDelay = responseConfig.RESPONSE_DELAY || 1000;
            this.enableSegmentedReply = responseConfig.ENABLE_SEGMENTED_REPLY !== false;
            this.maxSegments = responseConfig.MAX_SEGMENTS || 5;
            this.minSegmentLength = responseConfig.MIN_SEGMENT_LENGTH || 100;
            this.segmentDelay = responseConfig.SEGMENT_DELAY || 2000;
            this.defaultReplies = responseConfig.DEFAULT_REPLIES || {
                AT_BOT: "你@我了，有什么可以帮助你的吗？",
                REPLY_BOT: "你回复我了，有什么可以帮助你的吗？",
                EMPTY_MESSAGE: "你好",
                ERROR_MESSAGE: "抱歉，我现在无法回应，请稍后再试。",
                API_ERROR: "AI服务暂时不可用，请稍后重试。"
            };

            // 更新历史记录配置
            const historyConfig = aiChatConfig.HISTORY_CONFIG || {};
            this.enableHistory = historyConfig.ENABLE_HISTORY !== false;
            this.maxHistoryLines = historyConfig.MAX_HISTORY_LINES || 15;
            this.historyFormat = historyConfig.HISTORY_FORMAT || 'natural';
            this.includeUserInfo = historyConfig.INCLUDE_USER_INFO !== false;

            // 更新调试配置
            const debugConfig = aiChatConfig.DEBUG_CONFIG || {};
            this.enableDebug = debugConfig.ENABLE_DEBUG !== false;
            this.logRequests = debugConfig.LOG_REQUESTS !== false;
            this.logResponses = debugConfig.LOG_RESPONSES !== false;
            this.logTriggerConditions = debugConfig.LOG_TRIGGER_CONDITIONS !== false;

            // 更新OpenAI Tools配置
            const openaiConfig = aiChatConfig.OPENAI_TOOLS_CONFIG || {};
            this.openaiConfig = {
                enableTools: openaiConfig.ENABLE_TOOLS !== false,
                apiKey: openaiConfig.API_KEY || 'sk-your-openai-api-key-here',
                apiUrl: openaiConfig.API_URL || 'https://api.openai.com',
                model: openaiConfig.MODEL || 'gpt-4o-mini',
                temperature: openaiConfig.TEMPERATURE || 0.3,
                maxTokens: openaiConfig.MAX_TOKENS || 2000,
                requestTimeout: openaiConfig.REQUEST_TIMEOUT || 30000
            };

            // 更新媒体配置
            const mediaConfig = aiChatConfig.MEDIA_CONFIG || {};
            this.mediaConfig = {
                enableImageDetection: mediaConfig.ENABLE_IMAGE_DETECTION !== false,
                enableFileDetection: mediaConfig.ENABLE_FILE_DETECTION !== false,
                supportedImageFormats: mediaConfig.SUPPORTED_IMAGE_FORMATS || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
                supportedFileFormats: mediaConfig.SUPPORTED_FILE_FORMATS || ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'mp3', 'mp4', 'avi', 'mov'],
                imageUrlPatterns: mediaConfig.IMAGE_URL_PATTERNS || [
                    "!\\[.*?\\]\\((.*?)\\)",
                    "<img\\s+[^>]*src=\"([^\"]+)\"[^>]*>"
                ]
            };

            // 更新语音配置
            const voiceConfig = aiChatConfig.VOICE_CONFIG || {};
            this.voiceConfig = {
                enableVoiceMode: voiceConfig.ENABLE_VOICE_MODE !== false,
                defaultCharacter: voiceConfig.DEFAULT_CHARACTER || '0',
                voiceProbability: voiceConfig.VOICE_PROBABILITY || 0.3,
                enableVoiceKeywords: voiceConfig.ENABLE_VOICE_KEYWORDS !== false,
                voiceKeywords: voiceConfig.VOICE_KEYWORDS || ['语音', '说话', '念出来', '读一下', '语音回复'],
                maxVoiceLength: voiceConfig.MAX_VOICE_LENGTH || 200,
                voiceOnlyMode: voiceConfig.VOICE_ONLY_MODE || false,
                enableCharacterSwitch: voiceConfig.ENABLE_CHARACTER_SWITCH !== false,
                autoRefreshCharacters: voiceConfig.AUTO_REFRESH_CHARACTERS !== false,
                characterCacheTime: voiceConfig.CHARACTER_CACHE_TIME || 3600000
            };

            // 语音角色相关
            this.currentVoiceCharacter = this.voiceConfig.defaultCharacter;
            this.availableCharacters = [];
            this.charactersLastFetch = 0;

            // 更新配置修改时间
            const stats = fs.statSync(this.configPath);
            this.lastConfigModified = stats.mtime.getTime();

            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 配置已加载: 触发词=${this.triggerKeywords.length}个, AI模型=${this.aiConfig.model}`);
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 加载配置失败: ${error.message}`);
            // 使用默认配置
            this.useDefaultConfig();
        }
    }

    /**
     * 使用默认配置
     */
    useDefaultConfig() {
        this.priority = 40;
        this.permission = 'all';
        this.supportedTypes = ['message'];
        this.enabled = true;

        this.aiConfig = {
            apiUrl: 'http://localhost:6005/v1/chat/completions',
            apiKey: '114514',
            model: 'gemini-2.5-pro-free',
            type: 'mcp',
            enable_context: true,
            memory_tracking: true,
            maxContextSize: 15,
            stream: false,
            requestTimeout: 30000,
            maxRetries: 3,
            retryDelay: 1000,
            agent: '失语'
        };

        this.triggerKeywords = ['失语', '失语症'];
        this.enableAtTrigger = true;
        this.enableReplyTrigger = true;
        this.enablePrivateTrigger = false;
        this.caseSensitive = false;

        this.enableSegmentedReply = true;
        this.maxSegments = 5;
        this.minSegmentLength = 100;
        this.segmentDelay = 2000;

        this.defaultReplies = {
            AT_BOT: "你@我了，有什么可以帮助你的吗？",
            REPLY_BOT: "你回复我了，有什么可以帮助你的吗？",
            EMPTY_MESSAGE: "你好",
            ERROR_MESSAGE: "抱歉，我现在无法回应，请稍后再试。",
            API_ERROR: "AI服务暂时不可用，请稍后重试。"
        };

        this.openaiConfig = {
            enableTools: true,
            apiKey: 'sk-your-openai-api-key-here',
            apiUrl: 'https://api.openai.com',
            model: 'gpt-4o-mini',
            temperature: 0.3,
            maxTokens: 2000,
            requestTimeout: 30000
        };

        this.mediaConfig = {
            enableImageDetection: true,
            enableFileDetection: true,
            supportedImageFormats: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
            supportedFileFormats: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'mp3', 'mp4', 'avi', 'mov'],
            imageUrlPatterns: [
                "!\\[.*?\\]\\((.*?)\\)",
                "<img\\s+[^>]*src=\"([^\"]+)\"[^>]*>"
            ]
        };

        this.voiceConfig = {
            enableVoiceMode: true,
            defaultCharacter: '0',
            voiceProbability: 0.3,
            enableVoiceKeywords: true,
            voiceKeywords: ['语音', '说话', '念出来', '读一下', '语音回复'],
            maxVoiceLength: 200,
            voiceOnlyMode: false,
            enableCharacterSwitch: true,
            autoRefreshCharacters: true,
            characterCacheTime: 3600000
        };

        this.currentVoiceCharacter = this.voiceConfig.defaultCharacter;
        this.availableCharacters = [];
        this.charactersLastFetch = 0;

        this.voiceConfig = {
            enableVoiceMode: true,
            defaultCharacter: 'hutao',
            voiceProbability: 0.3,
            enableVoiceKeywords: true,
            voiceKeywords: ['语音', '说话', '念出来', '读一下'],
            availableCharacters: ['hutao', 'furina', 'nahida', 'raiden', 'zhongli', 'venti'],
            characterAliases: {
                "胡桃": "hutao", "芙宁娜": "furina", "纳西妲": "nahida",
                "雷电将军": "raiden", "钟离": "zhongli", "温迪": "venti"
            },
            maxVoiceLength: 200,
            voiceOnlyMode: false,
            enableCharacterSwitch: true
        };

        this.currentVoiceCharacter = this.voiceConfig.defaultCharacter;

        this.enableHistory = true;
        this.maxHistoryLines = 15;
        this.enableDebug = true;

        console.log(`[AIChatPlugin] 使用默认配置`);
    }

    /**
     * 启动配置文件监听
     */
    startConfigWatcher() {
        if (!fs.existsSync(this.configPath)) {
            console.warn(`[AIChatPlugin] 配置文件不存在: ${this.configPath}`);
            return;
        }

        // 每5秒检查一次配置文件是否有变化
        this.configWatcher = setInterval(() => {
            this.checkConfigChanges();
        }, 5000);

        if (this.enableDebug) {
            console.log(`[AIChatPlugin] 配置文件监听已启动`);
        }
    }

    /**
     * 检查配置文件变化
     */
    checkConfigChanges() {
        try {
            const stats = fs.statSync(this.configPath);
            const currentModified = stats.mtime.getTime();

            if (currentModified > this.lastConfigModified) {
                console.log(`[AIChatPlugin] 检测到配置文件变化，重新加载配置...`);
                this.loadConfig();
                console.log(`[AIChatPlugin] 配置热重载完成`);
            }
        } catch (error) {
            console.error(`[AIChatPlugin] 检查配置文件变化失败: ${error.message}`);
        }
    }

    /**
     * 停止配置文件监听
     */
    stopConfigWatcher() {
        if (this.configWatcher) {
            clearInterval(this.configWatcher);
            this.configWatcher = null;
            console.log(`[AIChatPlugin] 配置文件监听已停止`);
        }
    }

    /**
     * 检查是否应该处理此消息
     */
    async shouldHandle(context) {
        if (!context.message || !this.enabled) return false;

        // 先提取原始文本内容用于关键词检测
        const rawTextContent = this.extractRawTextContent(context.message, context);
        const message = context.message.trim();

        // 检查是否@机器人
        const isAtBot = this.enableAtTrigger && this.isAtBot(message, context);

        // 检查是否是回复机器人的消息（异步）
        const isReplyToBot = this.enableReplyTrigger && await this.isReplyToBot(message, context);

        // 检查是否是私聊触发
        const isPrivateTrigger = this.enablePrivateTrigger && context.type === 'private';

        // 检查是否包含触发关键词（使用原始文本内容）
        const hasKeyword = rawTextContent && rawTextContent.trim() !== '' &&
            this.triggerKeywords.some(keyword => {
                if (this.caseSensitive) {
                    return rawTextContent.includes(keyword) || rawTextContent.startsWith(keyword);
                } else {
                    const lowerMessage = rawTextContent.toLowerCase();
                    const lowerKeyword = keyword.toLowerCase();
                    return lowerMessage.includes(lowerKeyword) || lowerMessage.startsWith(lowerKeyword);
                }
            });

        // 只有在明确触发的情况下才处理
        const shouldTrigger = hasKeyword || isAtBot || isReplyToBot || isPrivateTrigger;

        if (shouldTrigger && this.logTriggerConditions) {
            console.log(`[AIChatPlugin] 触发条件: 关键词=${hasKeyword}, @机器人=${isAtBot}, 回复=${isReplyToBot}, 私聊=${isPrivateTrigger}`);
            console.log(`[AIChatPlugin] 原始文本内容: "${rawTextContent}"`);
        }

        return shouldTrigger;
    }

    /**
     * 检查是否@了机器人 - 使用helper的extractAts方法
     */
    isAtBot(message, context) {
        const selfId = this.adapter.selfId || context.selfId || '2857896171';

        // 使用helper方法提取@信息
        if (context.event && context.event.message) {
            console.log(`[AIChatPlugin] 原始消息格式:`, typeof context.event.message, context.event.message);

            // 确保消息是数组格式
            let messageArray = context.event.message;
            if (typeof messageArray === 'string') {
                // 如果是字符串，尝试解析CQ码
                console.log(`[AIChatPlugin] 消息是字符串格式，尝试解析CQ码`);
                messageArray = this.parseCQCodeToSegments(messageArray);
            }

            const ats = helper.extractAts(messageArray);
            console.log(`[AIChatPlugin] 检测@: selfId=${selfId}, ats=`, ats);

            // 检查是否@了机器人 - 确保类型匹配
            for (const at of ats) {
                if (at.qq === selfId || at.qq === selfId.toString() || at.qq.toString() === selfId.toString()) {
                    console.log(`[AIChatPlugin] @机器人检测成功: ${at.qq} === ${selfId}`);
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查是否是回复机器人的消息 - 使用helper的extractReplies方法
     */
    async isReplyToBot(message, context) {
        const selfId = this.adapter.selfId || context.selfId || '2857896171';

        // 使用helper方法提取回复信息
        if (context.event && context.event.message) {
            const replies = helper.extractReplies(context.event.message);

            if (replies.length > 0) {
                // 检查被回复的消息是否来自机器人
                for (const reply of replies) {
                    try {
                        // 使用adapter的getMsg方法获取被回复的消息信息
                        const bot = {
                            ws: context.ws || this.adapter.napCatClients?.[0],
                            selfId: selfId
                        };
                        if (!bot.ws) {
                            console.log(`[AIChatPlugin] 无法获取WebSocket连接，跳过回复验证`);
                            continue;
                        }

                        const messageInfo = await this.adapter.getMsg(bot, reply.id);
                        if (messageInfo && messageInfo.sender &&
                            (messageInfo.sender.user_id === selfId ||
                             messageInfo.sender.user_id === parseInt(selfId))) {
                            console.log(`[AIChatPlugin] 确认回复机器人消息: ${reply.id}`);
                            return true;
                        } else {
                            console.log(`[AIChatPlugin] 回复的消息来自其他用户: ${messageInfo?.sender?.user_id}`);
                        }
                    } catch (error) {
                        console.log(`[AIChatPlugin] 无法获取被回复消息信息: ${error.message}`);
                        // 如果API调用失败，暂时不认为是回复机器人
                        continue;
                    }
                }
            }
        }

        // 兼容性检查：检查原始消息中的回复CQ码
        if (message && message.includes('[CQ:reply,id=')) {
            // 提取消息ID并验证
            const replyMatch = message.match(/\[CQ:reply,id=(\d+)\]/);
            if (replyMatch) {
                try {
                    const bot = {
                        ws: context.ws || this.adapter.napCatClients?.[0],
                        selfId: selfId
                    };
                    if (bot.ws) {
                        const messageInfo = await this.adapter.getMsg(bot, replyMatch[1]);
                        if (messageInfo && messageInfo.sender &&
                            (messageInfo.sender.user_id === selfId ||
                             messageInfo.sender.user_id === parseInt(selfId))) {
                            console.log(`[AIChatPlugin] CQ码回复验证成功: ${replyMatch[1]}`);
                            return true;
                        }
                    }
                } catch (error) {
                    console.log(`[AIChatPlugin] CQ码回复验证失败: ${error.message}`);
                }
            }
            return false;
        }

        return false;
    }

    /**
     * 处理消息
     */
    async handle(context) {
        try {
            // 提取用户消息内容
            const userMessage = this.extractUserMessage(context.message, context);
            if (!userMessage || userMessage.trim() === '') {
                console.log(`[AIChatPlugin] 提取的用户消息为空，跳过处理`);
                return;
            }

            console.log(`[AIChatPlugin] 收到用户消息: ${userMessage}`);

            // 获取聊天历史记录prompt
            const historyPrompt = await this.getChatHistoryPrompt(context);

            // 获取用户显示名称（用于useragent等字段）
            const userDisplayName = await this.getUserDisplayName(context);

            // 构建当前消息的上下文格式（模仿历史记录格式）
            const userId = context.userId;
            let nickname;
            if (context.type === 'group') {
                // 群聊中优先使用群名片，其次是昵称
                nickname = context.sender?.card || context.sender?.nickname || '未知用户';
            } else {
                // 私聊中使用昵称
                nickname = context.sender?.nickname || '未知用户';
            }

            // 使用与历史记录一致的格式：昵称(QQ号:xxxxx)说: 内容
            const currentMessageContext = `${nickname}(QQ号:${userId})说: ${userMessage}`;

            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 当前消息上下文格式: ${currentMessageContext}`);
            }

            // 构建完整的用户prompt
            let fullPrompt;
            if (historyPrompt) {
                fullPrompt = `${historyPrompt}\n${currentMessageContext}`;
            } else {
                fullPrompt = currentMessageContext;
            }

            // 获取AI响应
            const aiResponse = await this.getAIResponse(fullPrompt, context);

            if (aiResponse) {
                // 使用分段回复处理响应
                await this.handleSegmentedResponse(context, aiResponse);
            } else {
                await this.reply(context, this.defaultReplies.ERROR_MESSAGE);
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 处理消息失败: ${error.message}`);
            await this.reply(context, '处理消息时出现错误，请稍后重试。');
        }
    }

    /**
     * 获取聊天历史记录并构建prompt
     */
    async getChatHistoryPrompt(context) {
        if (!this.enableHistory) {
            return '';
        }

        try {
            // 使用配置中的历史记录数量
            const historyResult = await this.readTodayChatHistory(context, this.maxHistoryLines);

            if (!historyResult.success || historyResult.history.length === 0) {
                if (this.enableDebug) {
                    console.log(`[AIChatPlugin] 未找到历史记录: ${historyResult.message}`);
                }
                return '';
            }

            // 根据配置选择历史记录格式
            let historyPrompt;
            if (this.historyFormat === 'natural') {
                historyPrompt = [
                    '以下是最近的聊天记录作为上下文参考：',
                    '=== 聊天历史 ===',
                    ...historyResult.history,
                    '=== 历史结束 ===',
                    ''
                ].join('\n');
            } else {
                // 简化格式
                historyPrompt = historyResult.history.join('\n') + '\n';
            }

            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 获取到${historyResult.history.length}条历史记录`);
            }
            return historyPrompt;

        } catch (error) {
            console.error(`[AIChatPlugin] 获取聊天历史失败: ${error.message}`);
            return '';
        }
    }

    /**
     * 读取当天聊天记录 - 参考ChatRecordPlugin的方法
     */
    async readTodayChatHistory(context, number = 15) {
        try {
            const logPath = this.getChatLogPath(context.type, context.groupId || context.userId);

            // 检查文件是否存在
            if (!require('fs').existsSync(logPath)) {
                return {
                    success: false,
                    message: '今日暂无聊天记录',
                    history: []
                };
            }

            // 读取文件内容
            const fileContent = require('fs').readFileSync(logPath, 'utf8');
            const lines = fileContent.trim().split('\n').filter(line => line.trim());

            // 获取最近的number条记录
            const recentLines = lines.slice(-number);

            return {
                success: true,
                message: `成功获取最近${recentLines.length}条聊天记录`,
                history: recentLines,
                totalCount: lines.length
            };

        } catch (error) {
            console.error(`[AIChatPlugin] 读取聊天记录失败: ${error.message}`);
            return {
                success: false,
                message: `读取聊天记录失败: ${error.message}`,
                history: []
            };
        }
    }

    /**
     * 获取聊天记录文件路径 - 参考ChatRecordPlugin的方法
     */
    getChatLogPath(type, id) {
        const baseDir = path.join(__dirname, '..', 'chat_logs');
        const typeDir = path.join(baseDir, type);
        const idDir = path.join(typeDir, id.toString());
        const today = new Date().toISOString().split('T')[0];
        const dateDir = path.join(idDir, today);
        const fileName = `${today}.log`;

        return path.join(dateDir, fileName);
    }

    /**
     * 解析CQ码为消息段数组
     */
    parseCQCodeToSegments(message) {
        const segments = [];
        let lastIndex = 0;

        // 匹配所有CQ码
        const cqRegex = /\[CQ:([^,\]]+)(?:,([^\]]+))?\]/g;
        let match;

        while ((match = cqRegex.exec(message)) !== null) {
            // 添加CQ码前的文本
            if (match.index > lastIndex) {
                const text = message.substring(lastIndex, match.index);
                if (text) {
                    segments.push({
                        type: 'text',
                        data: { text: text }
                    });
                }
            }

            // 解析CQ码参数
            const type = match[1];
            const params = {};
            if (match[2]) {
                const paramPairs = match[2].split(',');
                for (const pair of paramPairs) {
                    const [key, value] = pair.split('=');
                    if (key && value) {
                        params[key] = value;
                    }
                }
            }

            segments.push({
                type: type,
                data: params
            });

            lastIndex = cqRegex.lastIndex;
        }

        // 添加最后的文本
        if (lastIndex < message.length) {
            const text = message.substring(lastIndex);
            if (text) {
                segments.push({
                    type: 'text',
                    data: { text: text }
                });
            }
        }

        return segments;
    }

    /**
     * 获取用户信息格式化字符串
     */
    async getUserDisplayName(context) {
        try {
            const userId = context.userId;

            // 根据聊天类型优先选择显示名称
            let nickname;
            if (context.type === 'group') {
                // 群聊中优先使用群名片，其次是昵称
                nickname = context.sender?.card || context.sender?.nickname || '未知用户';
            } else {
                // 私聊中使用昵称
                nickname = context.sender?.nickname || '未知用户';
            }

            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 获取用户显示名称: ${nickname}, userId: ${userId}, type: ${context.type}`);
            }

            // 保存用户信息到Agent文件夹（不影响其他系统）
            await this.saveUserToAgentFolder(context);

            // 返回统一格式：名称_QQ号
            const result = `${nickname}_${userId}`;
            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 用户显示名称结果: ${result}`);
            }
            return result;
        } catch (error) {
            console.error(`[AIChatPlugin] 获取用户信息失败: ${error.message}`);
            return `用户_${context.userId}`;
        }
    }

    /**
     * 获取机器人信息格式化字符串
     */
    async getAssistantDisplayName(context) {
        try {
            const selfId = this.adapter.selfId || context.selfId || '123466';

            // 尝试获取机器人的真实昵称
            let botNickname = '机器人';

            try {
                // 构建bot对象（与其他方法保持一致）
                let ws = context.ws;
                if (!ws && this.adapter.napCatClients && this.adapter.napCatClients.size > 0) {
                    ws = this.adapter.napCatClients.values().next().value;
                }

                if (!ws) {
                    if (this.enableDebug) {
                        console.log(`[AIChatPlugin] 没有可用的WebSocket连接，无法获取机器人信息`);
                    }
                    throw new Error('没有可用的WebSocket连接');
                }

                const bot = {
                    ws: ws,
                    selfId: selfId
                };

                if (this.enableDebug) {
                    console.log(`[AIChatPlugin] 尝试获取机器人信息，selfId: ${selfId}, context.type: ${context.type}, ws状态: ${ws.readyState}`);
                }

                // 首先尝试获取机器人的基本登录信息
                const loginResponse = await this.napCatAPI.getLoginInfo(bot);
                if (this.enableDebug) {
                    console.log(`[AIChatPlugin] 登录信息API响应:`, loginResponse);
                }
                if (loginResponse && loginResponse.data && loginResponse.data.nickname) {
                    botNickname = loginResponse.data.nickname;
                    if (this.enableDebug) {
                        console.log(`[AIChatPlugin] 从登录信息获取到机器人昵称: ${botNickname}`);
                    }
                }

                // 如果是群聊，尝试获取群内的名片信息（可能会覆盖基本昵称）
                if (context.type === 'group' && context.groupId) {
                    if (this.enableDebug) {
                        console.log(`[AIChatPlugin] 获取群内机器人信息，groupId: ${context.groupId}`);
                    }
                    const response = await this.napCatAPI.user.getGroupMemberInfo(bot, context.groupId, selfId);
                    if (this.enableDebug) {
                        console.log(`[AIChatPlugin] 群成员信息API响应:`, response);
                    }
                    if (response && response.data && response.data.card) {
                        // 群名片优先于昵称
                        botNickname = response.data.card;
                        if (this.enableDebug) {
                            console.log(`[AIChatPlugin] 获取到群内机器人名片: ${botNickname}`);
                        }
                    } else if (response && response.data && response.data.nickname && !loginResponse?.data?.nickname) {
                        // 如果没有登录信息的昵称，使用群成员信息中的昵称
                        botNickname = response.data.nickname;
                        if (this.enableDebug) {
                            console.log(`[AIChatPlugin] 获取到群内机器人昵称: ${botNickname}`);
                        }
                    }
                }
            } catch (error) {
                // 如果获取失败，使用默认名称
                if (this.enableDebug) {
                    console.log(`[AIChatPlugin] 获取机器人信息失败，使用默认名称: ${error.message}`);
                }
            }

            // 返回统一格式：名称_QQ号
            const result = `${botNickname}_${selfId}`;
            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 最终机器人显示名称: ${result}`);
            }
            return result;
        } catch (error) {
            console.error(`[AIChatPlugin] 获取机器人信息失败: ${error.message}`);
            return `机器人_${this.adapter.selfId || '123466'}`;
        }
    }

    /**
     * 保存用户信息到Agent文件夹（角色卡格式）
     */
    async saveUserToAgentFolder(context) {
        try {
            const agentDir = path.join(__dirname, '..', '..', 'Agent');

            // 确保Agent目录存在
            if (!fs.existsSync(agentDir)) {
                fs.mkdirSync(agentDir, { recursive: true });
            }

            const userId = context.userId;

            // 根据聊天类型优先选择显示名称（与getUserDisplayName保持一致）
            let nickname;
            if (context.type === 'group') {
                // 群聊中优先使用群名片，其次是昵称
                nickname = context.sender?.card || context.sender?.nickname || '未知用户';
            } else {
                // 私聊中使用昵称
                nickname = context.sender?.nickname || '未知用户';
            }

            // 生成用户格式化名称（和getUserDisplayName保持一致）
            const userDisplayName = `${nickname}_${userId}`;

            // 使用统一格式作为文件名
            const fileName = `${userDisplayName}.txt`;
            const filePath = path.join(agentDir, fileName);

            // 读取完整的用户数据
            const fullUserData = await this.getFullUserData(userId);

            // 构建角色卡内容
            const characterCard = this.buildCharacterCard(context, fullUserData);

            // 写入文件
            fs.writeFileSync(filePath, characterCard, 'utf8');

            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 用户角色卡已保存: ${fileName}`);
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 保存用户角色卡失败: ${error.message}`);
        }
    }

    /**
     * 获取完整的用户数据
     */
    async getFullUserData(userId) {
        try {
            const userDataPath = path.join(__dirname, '..', 'user_data', `${userId}.json`);

            if (fs.existsSync(userDataPath)) {
                const userData = JSON.parse(fs.readFileSync(userDataPath, 'utf8'));
                return userData;
            }

            return null;
        } catch (error) {
            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 读取用户数据失败: ${error.message}`);
            }
            return null;
        }
    }

    /**
     * 构建角色卡内容
     */
    buildCharacterCard(context, fullUserData = null) {
        const userId = context.userId;
        const nickname = context.sender?.nickname || context.sender?.card || '未知用户';
        const role = context.sender?.role || 'member';
        const title = context.sender?.title || '';
        const level = context.sender?.level || 0;
        const currentTime = new Date().toLocaleString('zh-CN');

        const roleMap = {
            'owner': '群主',
            'admin': '管理员',
            'member': '群员'
        };

        // 使用完整用户数据或默认值
        const userData = fullUserData || {};

        return `# ${nickname}_${userId} 的角色卡

## 基本信息
- **昵称**: ${nickname}
- **QQ号**: ${userId}
- **UIN**: ${userData.uin || '未知'}
- **UID**: ${userData.uid || '未知'}
- **备注名**: ${userData.remark || '未知'}
- **性别**: ${this.formatGender(userData.sex)}
- **年龄**: ${userData.age || '未知'}
- **生日**: ${this.formatBirthday(userData.birthday_year, userData.birthday_month, userData.birthday_day)}
- **邮箱**: ${userData.email || '未知'}
- **QQ等级**: ${userData.qq_level || '未知'}
- **QID**: ${userData.qid || '未知'}
- **个性签名**: ${userData.long_nick || '未知'}
- **国家**: ${userData.country || '未知'}
- **家乡**: ${userData.hometown || '未知'}
- **学校**: ${userData.college || '未知'}
- **VIP状态**: ${userData.is_vip ? 'VIP用户' : '普通用户'}
- **VIP等级**: ${userData.vip_level || '未知'}

## 群聊信息
- **群身份**: ${roleMap[role] || '群员'}
- **群头衔**: ${title || '无'}
- **群等级**: ${level}

## 时间信息
- **注册时间**: ${this.formatTimestamp(userData.reg_time)}
- **最后更新**: ${this.formatTimestamp(userData.last_update)}
- **角色卡更新时间**: ${currentTime}

## 互动记录
- **消息类型**: ${context.type === 'group' ? '群聊' : '私聊'}
- **所在群组**: ${context.groupId || '私聊'}
- **与AI助手进行过对话交互**

## 状态信息
- **在线状态**: ${this.formatStatus(userData.status)}
- **用户状态**: ${userData.status || '未知'}

## 备注
- 此用户与AI助手有过对话记录
- 角色卡会在每次对话时自动更新
- 数据来源：用户信息管理器

---
*此角色卡由AI对话插件自动生成和更新*
*更新时间: ${currentTime}*
`;
    }

    /**
     * 格式化性别信息
     */
    formatGender(sex) {
        const genderMap = {
            'male': '男',
            'female': '女',
            'unknown': '未知'
        };
        return genderMap[sex] || '未知';
    }

    /**
     * 格式化生日信息
     */
    formatBirthday(year, month, day) {
        if (!year || !month || !day) {
            return '未知';
        }
        return `${year}年${month}月${day}日`;
    }

    /**
     * 格式化时间戳
     */
    formatTimestamp(timestamp) {
        if (!timestamp) {
            return '未知';
        }
        return new Date(timestamp * 1000).toLocaleString('zh-CN');
    }

    /**
     * 格式化状态信息
     */
    formatStatus(status) {
        const statusMap = {
            10: '在线',
            20: '离开',
            30: '隐身',
            40: '忙碌',
            50: '请勿打扰'
        };
        return statusMap[status] || '未知';
    }



    /**
     * 提取原始文本内容（用于关键词检测，不清理触发词）
     */
    extractRawTextContent(rawMessage, context) {
        if (!rawMessage) return '';

        let textContent = '';

        // 如果有消息段，使用helper方法解析
        if (context.event && context.event.message) {
            // 使用helper方法提取各种消息段
            const texts = helper.extractTexts(context.event.message);

            // 合并所有文本内容
            textContent = texts.map(t => t.text).join('').trim();
        } else {
            // 兼容性处理：如果没有消息段，使用原始消息
            textContent = rawMessage;

            // 移除CQ码但保留文本内容
            textContent = textContent.replace(/\[CQ:[^\]]+\]/g, '').trim();
        }

        return textContent;
    }

    /**
     * 提取用户消息内容 - 使用helper方法解析消息段（清理后的内容）
     */
    extractUserMessage(rawMessage, context) {
        if (!rawMessage) return '';

        let textContent = '';
        let hasReply = false;
        let hasAt = false;

        // 如果有消息段，使用helper方法解析
        if (context.event && context.event.message) {
            // 使用helper方法提取各种消息段
            const texts = helper.extractTexts(context.event.message);
            const replies = helper.extractReplies(context.event.message);
            const ats = helper.extractAts(context.event.message);

            // 合并所有文本内容
            textContent = texts.map(t => t.text).join('').trim();

            // 检查是否有回复和@
            hasReply = replies.length > 0;
            hasAt = ats.length > 0;

        } else {
            // 兼容性处理：如果没有消息段，使用原始消息
            textContent = rawMessage;

            // 移除CQ码
            textContent = textContent.replace(/\[CQ:[^\]]+\]/g, '').trim();
        }

        // 清理文本内容 - 移除文本格式的@和触发关键词
        const cleanPatterns = [
            '@失语', '@失语症', '@ 失语', '@ 失语症',
            ...this.triggerKeywords
        ];

        for (const pattern of cleanPatterns) {
            if (textContent.startsWith(pattern)) {
                textContent = textContent.substring(pattern.length).trim();
                break;
            }
        }

        // 如果消息为空，根据情况处理
        if (!textContent) {
            if (hasReply) {
                return this.defaultReplies.REPLY_BOT;
            } else if (hasAt) {
                return this.defaultReplies.AT_BOT;
            } else {
                // 如果既没有文本，也没有@或回复，返回空字符串
                // 这样可以避免误触发关键词检测
                return '';
            }
        }

        return textContent;
    }

    /**
     * 获取AI响应
     */
    async getAIResponse(prompt, context) {
        let retries = 0;

        while (retries <= this.aiConfig.maxRetries) {
            try {
                if (this.logRequests) {
                    console.log(`[AIChatPlugin] 发送AI请求，prompt长度: ${prompt.length}, 尝试次数: ${retries + 1}`);
                }

                const headers = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.aiConfig.apiKey}`
                };

                // 获取用户信息格式化字符串
                const userDisplayName = await this.getUserDisplayName(context);

                // 动态生成assistantName（机器人的格式化名称）
                const assistantName = await this.getAssistantDisplayName(context);

                const requestBody = {
                    model: this.aiConfig.model,
                    type: this.aiConfig.type,
                    enable_context: this.aiConfig.enable_context,
                    memory_tracking: this.aiConfig.memory_tracking,
                    maxContextSize: this.aiConfig.maxContextSize,
                    userId: userDisplayName,  // 使用格式化的用户信息
                    useragent: userDisplayName,  // useragent和userId相同
                    agent: this.aiConfig.agent,
                    assistantName: assistantName,  // 动态生成的机器人名称
                    assistant_id: this.adapter.selfId || 123466,
                    user_id: context.userId || 0,
                    stream: this.aiConfig.stream,
                    messages: [
                        {
                            role: "user",
                            content: prompt
                        }
                    ]
                };

                // 如果是群聊，添加群聊信息
                if (context.type === 'group' && context.groupId) {
                    requestBody.group_id = context.groupId;
                    requestBody.chat_type = 'group';
                } else {
                    requestBody.chat_type = 'private';
                }

                // 设置请求超时
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.aiConfig.requestTimeout);

                const response = await fetch(this.aiConfig.apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();

                if (data.choices && data.choices[0] && data.choices[0].message) {
                    const aiResponse = data.choices[0].message.content.trim();
                    if (this.logResponses) {
                        console.log(`[AIChatPlugin] AI响应成功，长度: ${aiResponse.length}`);
                    }
                    return aiResponse;
                } else {
                    throw new Error('AI响应格式错误');
                }

            } catch (error) {
                retries++;
                console.error(`[AIChatPlugin] AI请求失败 (尝试 ${retries}/${this.aiConfig.maxRetries + 1}): ${error.message}`);

                if (retries <= this.aiConfig.maxRetries) {
                    console.log(`[AIChatPlugin] ${this.aiConfig.retryDelay}ms后重试...`);
                    await new Promise(resolve => setTimeout(resolve, this.aiConfig.retryDelay));
                } else {
                    console.error(`[AIChatPlugin] 所有重试都失败了`);
                    return null;
                }
            }
        }

        return null;
    }

    /**
     * 获取可用的AI语音角色列表
     */
    async getAvailableCharacters(context) {
        // 检查缓存是否有效
        const now = Date.now();
        if (this.availableCharacters.length > 0 &&
            (now - this.charactersLastFetch) < this.voiceConfig.characterCacheTime) {
            return this.availableCharacters;
        }

        try {
            const bot = {
                ws: context.ws || this.adapter.napCatClients?.[0],
                selfId: this.adapter.selfId || context.selfId || '2857896171'
            };

            if (!bot.ws) {
                console.warn(`[AIChatPlugin] 无法获取WebSocket连接，使用默认角色`);
                return [{ character: this.voiceConfig.defaultCharacter, character_name: '默认角色' }];
            }

            // 调用NapCat API获取角色列表
            const result = await this.napCatAPI.group.getAiCharacters(bot, context.groupId || 0, 1);

            if (result && result.data && Array.isArray(result.data)) {
                this.availableCharacters = result.data;
                this.charactersLastFetch = now;

                if (this.enableDebug) {
                    console.log(`[AIChatPlugin] 获取到${this.availableCharacters.length}个AI语音角色`);
                }

                return this.availableCharacters;
            } else {
                console.warn(`[AIChatPlugin] 获取AI角色列表失败，使用默认角色`);
                return [{ character: this.voiceConfig.defaultCharacter, character_name: '默认角色' }];
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 获取AI角色列表异常: ${error.message}`);
            return [{ character: this.voiceConfig.defaultCharacter, character_name: '默认角色' }];
        }
    }

    /**
     * 检查是否应该使用语音回复
     */
    shouldUseVoice(userMessage, context) {
        if (!this.voiceConfig.enableVoiceMode) {
            return false;
        }

        // 检查是否包含语音关键词
        if (this.voiceConfig.enableVoiceKeywords) {
            const hasVoiceKeyword = this.voiceConfig.voiceKeywords.some(keyword =>
                userMessage.toLowerCase().includes(keyword.toLowerCase())
            );
            if (hasVoiceKeyword) {
                return true;
            }
        }

        // 语音模式专用
        if (this.voiceConfig.voiceOnlyMode) {
            return true;
        }

        // 随机概率触发
        return Math.random() < this.voiceConfig.voiceProbability;
    }

    /**
     * 解析用户指定的语音角色
     */
    parseVoiceCharacter(userMessage) {
        // 检查是否指定了特定角色
        const characterPatterns = [
            /用(.+?)的?声音/,
            /(.+?)语音/,
            /换成(.+)/,
            /角色(.+)/
        ];

        for (const pattern of characterPatterns) {
            const match = userMessage.match(pattern);
            if (match) {
                const characterName = match[1].trim();

                // 查找匹配的角色
                const character = this.availableCharacters.find(char =>
                    char.character_name && char.character_name.includes(characterName)
                );

                if (character) {
                    return character.character;
                }
            }
        }

        return null;
    }

    /**
     * 发送语音消息
     */
    async sendVoiceMessage(context, text, character = null) {
        try {
            // 检查文本长度
            if (text.length > this.voiceConfig.maxVoiceLength) {
                // 文本过长，截断并添加提示
                text = text.substring(0, this.voiceConfig.maxVoiceLength - 10) + '...';
                if (this.enableDebug) {
                    console.log(`[AIChatPlugin] 语音文本已截断到${this.voiceConfig.maxVoiceLength}字符`);
                }
            }

            const bot = {
                ws: context.ws || this.adapter.napCatClients?.[0],
                selfId: this.adapter.selfId || context.selfId || '2857896171'
            };

            if (!bot.ws) {
                console.warn(`[AIChatPlugin] 无法获取WebSocket连接，降级为文本回复`);
                await this.reply(context, text);
                return;
            }

            // 使用指定角色或当前角色
            const voiceCharacter = character || this.currentVoiceCharacter;

            if (context.type === 'group' && context.groupId) {
                // 群聊中直接发送AI语音
                const result = await this.napCatAPI.group.sendAiRecord(bot, context.groupId, voiceCharacter, text);

                if (result && result.success !== false) {
                    if (this.enableDebug) {
                        console.log(`[AIChatPlugin] 群聊语音发送成功: 角色=${voiceCharacter}, 长度=${text.length}`);
                    }
                } else {
                    console.warn(`[AIChatPlugin] 群聊语音发送失败: ${result?.error || '未知错误'}`);
                    // 降级为文本回复
                    await this.reply(context, text);
                }
            } else {
                // 私聊中先获取语音文件，再发送
                const recordResult = await this.napCatAPI.tools.getAiRecord(bot, voiceCharacter, 0, text);

                if (recordResult && recordResult.data && recordResult.data.record) {
                    // 发送语音消息
                    const voiceMessage = [
                        {
                            type: 'record',
                            data: {
                                file: recordResult.data.record
                            }
                        }
                    ];

                    await this.reply(context, voiceMessage);

                    if (this.enableDebug) {
                        console.log(`[AIChatPlugin] 私聊语音发送成功: 角色=${voiceCharacter}, 长度=${text.length}`);
                    }
                } else {
                    console.warn(`[AIChatPlugin] 获取语音文件失败: ${recordResult?.error || '未知错误'}`);
                    // 降级为文本回复
                    await this.reply(context, text);
                }
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 发送语音消息失败: ${error.message}`);
            // 降级为文本回复
            await this.reply(context, text);
        }
    }

    /**
     * 处理分段回复
     */
    async handleSegmentedResponse(context, response) {
        try {
            // 获取用户消息用于语音判断
            const userMessage = this.extractUserMessage(context.message, context);

            // 检测和提取媒体内容
            const { cleanedResponse, mediaItems } = this.extractMediaContent(response);

            // 检查是否应该使用语音回复
            const useVoice = this.shouldUseVoice(userMessage, context);

            // 解析用户指定的语音角色
            let voiceCharacter = this.currentVoiceCharacter;
            if (useVoice && this.voiceConfig.enableCharacterSwitch) {
                const parsedCharacter = this.parseVoiceCharacter(userMessage);
                if (parsedCharacter) {
                    voiceCharacter = parsedCharacter;
                    this.currentVoiceCharacter = parsedCharacter; // 记住用户选择
                }
            }

            // 如果启用分段回复且内容足够长
            if (this.enableSegmentedReply && cleanedResponse.length > this.minSegmentLength) {
                // 使用OpenAI Tools进行智能分段
                const segments = await this.segmentResponseWithAI(cleanedResponse);

                if (segments && segments.length > 1) {
                    // 发送分段回复
                    await this.sendSegmentedReply(context, segments, mediaItems, useVoice, voiceCharacter);
                    return;
                }
            }

            // 不分段，直接发送
            await this.sendSingleResponse(context, cleanedResponse, mediaItems, useVoice, voiceCharacter);

        } catch (error) {
            console.error(`[AIChatPlugin] 处理分段回复失败: ${error.message}`);
            // 降级处理：直接发送原始响应
            await this.reply(context, response);
        }
    }

    /**
     * 提取媒体内容（图片和文件）
     */
    extractMediaContent(response) {
        let cleanedResponse = response;
        const mediaItems = [];

        if (!this.mediaConfig.enableImageDetection && !this.mediaConfig.enableFileDetection) {
            return { cleanedResponse, mediaItems };
        }

        // 提取markdown格式的图片: ![alt](url)
        if (this.mediaConfig.enableImageDetection) {
            const markdownImageRegex = /!\[.*?\]\((.*?)\)/g;
            let match;
            while ((match = markdownImageRegex.exec(response)) !== null) {
                const url = match[1];
                if (this.isImageUrl(url)) {
                    mediaItems.push({
                        type: 'image',
                        url: url,
                        originalText: match[0]
                    });
                    cleanedResponse = cleanedResponse.replace(match[0], '');
                } else if (this.isFileUrl(url)) {
                    mediaItems.push({
                        type: 'file',
                        url: url,
                        originalText: match[0]
                    });
                    cleanedResponse = cleanedResponse.replace(match[0], '');
                }
            }
        }

        // 提取HTML img标签: <img src="url" ...>
        if (this.mediaConfig.enableImageDetection) {
            const htmlImageRegex = /<img\s+[^>]*src="([^"]+)"[^>]*>/g;
            let match;
            while ((match = htmlImageRegex.exec(response)) !== null) {
                const url = match[1];
                if (this.isImageUrl(url)) {
                    mediaItems.push({
                        type: 'image',
                        url: url,
                        originalText: match[0]
                    });
                    cleanedResponse = cleanedResponse.replace(match[0], '');
                } else if (this.isFileUrl(url)) {
                    mediaItems.push({
                        type: 'file',
                        url: url,
                        originalText: match[0]
                    });
                    cleanedResponse = cleanedResponse.replace(match[0], '');
                }
            }
        }

        // 清理多余的空行
        cleanedResponse = cleanedResponse.replace(/\n\s*\n/g, '\n').trim();

        if (this.enableDebug && mediaItems.length > 0) {
            console.log(`[AIChatPlugin] 提取到${mediaItems.length}个媒体项目`);
        }

        return { cleanedResponse, mediaItems };
    }

    /**
     * 判断是否为图片URL
     */
    isImageUrl(url) {
        if (!url || typeof url !== 'string') return false;
        const lowerUrl = url.toLowerCase();
        return this.mediaConfig.supportedImageFormats.some(format =>
            lowerUrl.includes(`.${format}`) || lowerUrl.endsWith(`.${format}`)
        );
    }

    /**
     * 判断是否为文件URL
     */
    isFileUrl(url) {
        if (!url || typeof url !== 'string') return false;
        const lowerUrl = url.toLowerCase();
        return this.mediaConfig.supportedFileFormats.some(format =>
            lowerUrl.includes(`.${format}`) || lowerUrl.endsWith(`.${format}`)
        );
    }

    /**
     * 使用OpenAI Tools进行智能分段
     */
    async segmentResponseWithAI(response) {
        if (!this.openaiConfig.enableTools || !this.openaiConfig.apiKey) {
            // 降级到简单分段
            return this.simpleSegmentResponse(response);
        }

        try {
            const tools = [{
                type: "function",
                function: {
                    name: "segment_response",
                    description: "将文本按自然聊天节奏分段，严禁修改原文内容，只能在合适位置分割",
                    parameters: {
                        type: "object",
                        properties: {
                            segments: {
                                type: "array",
                                description: "分段后的文本数组，所有段落拼接后必须与原文完全一致，不能有任何修改",
                                items: {
                                    type: "object",
                                    properties: {
                                        content: {
                                            type: "string",
                                            description: "段落的原始内容，必须是原文的完整片段，不能修改任何字符"
                                        },
                                        order: {
                                            type: "number",
                                            description: "段落顺序"
                                        },
                                        end_punctuation: {
                                            type: "string",
                                            description: "段落结尾的标点符号类型"
                                        },
                                        character_count: {
                                            type: "number",
                                            description: "段落字符数"
                                        }
                                    },
                                    required: ["content", "order"]
                                }
                            },
                            total_segments: {
                                type: "number",
                                description: "总段落数"
                            },
                            segmentation_strategy: {
                                type: "string",
                                description: "分段策略说明"
                            }
                        },
                        required: ["segments", "total_segments"]
                    }
                }
            }];

            const prompt = `【重要】你只能对文本进行分段处理，绝对不能修改、添加、删除或改写任何原始内容！

**核心要求：**
🚫 **严禁修改原文**：不能改变任何词汇、语句、标点符号或表达方式
🚫 **严禁添加内容**：不能添加任何新的词语、句子或符号
🚫 **严禁删除内容**：不能删除任何原有的内容
✅ **只能分段**：仅在合适的位置将文本分割成多段

**分段原则（按优先级）：**
1. **保持原文完整**：所有分段内容拼接后必须与原文完全一致
2. **标点符号断点**：优先在句号(。)、问号(？)、感叹号(！)、逗号(，)处分段
3. **语义完整性**：每段应该有相对完整的意思，避免在词语中间断开
4. **自然节奏**：模拟人类聊天时的发送节奏，避免过长或过短的段落
5. **长度适中**：每段建议30-150字符，但语义完整性优先于长度

**分段策略：**
- 在句号、问号、感叹号后分段（优先级最高）
- 在逗号、分号后分段（次优先级）
- 在连词（但是、然后、所以、因为等）前分段
- 在话题转换处分段
- 在列表项之间分段

**特殊情况处理：**
- 代码块、链接、特殊格式必须保持完整，不能分割
- 如果整段文本少于100字符且语义紧密，可以不分段
- 专有名词、成语、固定搭配不能分割
- 数字和单位不能分离

**质量检查：**
- 分段后的所有内容拼接必须与原文100%一致
- 每段都应该能独立理解或作为连续对话的一部分
- 分段数量控制在1-5段之间

**示例对比：**
❌ 错误做法：修改原文 "你好！" → "你好呀！"
❌ 错误做法：添加内容 "今天天气不错" → "今天天气不错呢~"
✅ 正确做法：仅分段 "你好！我是AI助手。今天天气不错。" → ["你好！我是AI助手。", "今天天气不错。"]

请严格按照以上要求，仅对以下文本进行分段处理，不得修改任何内容：
${response}`;

            const requestData = {
                model: this.openaiConfig.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的文本分段工具，职责是将文本按照自然的聊天节奏进行分段。\n\n【核心规则】：\n1. 绝对不能修改、添加、删除或改写原文的任何内容\n2. 只能在合适的位置将文本分割成多段\n3. 所有分段内容拼接后必须与原文100%一致\n4. 分段应该符合人类聊天的自然节奏\n\n你的任务就像用剪刀在纸上剪开文本，但不能改变纸上的任何文字。'
                    },
                    { role: 'user', content: prompt }
                ],
                tools: tools,
                tool_choice: { type: "function", function: { name: "segment_response" } },
                temperature: this.openaiConfig.temperature,
                max_tokens: this.openaiConfig.maxTokens
            };

            if (this.logRequests) {
                console.log(`[AIChatPlugin] 发送分段请求到OpenAI`);
            }

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.openaiConfig.requestTimeout);

            const response_api = await fetch(`${this.openaiConfig.apiUrl}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.openaiConfig.apiKey}`
                },
                body: JSON.stringify(requestData),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response_api.ok) {
                throw new Error(`OpenAI API请求失败: ${response_api.status} ${response_api.statusText}`);
            }

            const data = await response_api.json();

            if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.tool_calls) {
                throw new Error('OpenAI响应格式错误');
            }

            const toolCall = data.choices[0].message.tool_calls[0];
            if (toolCall.function.name !== 'segment_response') {
                throw new Error('OpenAI未调用正确的工具函数');
            }

            const result = JSON.parse(toolCall.function.arguments);
            const segments = result.segments
                .sort((a, b) => a.order - b.order)
                .map(seg => seg.content.trim())
                .filter(content => content.length > 0);

            // 验证分段内容的完整性
            const validationResult = this.validateSegments(response, segments);
            if (!validationResult.isValid) {
                console.warn(`[AIChatPlugin] OpenAI分段内容验证失败: ${validationResult.reason}`);
                console.warn(`[AIChatPlugin] 原文长度: ${response.length}, 分段拼接长度: ${validationResult.joinedLength}`);
                throw new Error(`分段验证失败: ${validationResult.reason}`);
            }

            if (this.logResponses) {
                console.log(`[AIChatPlugin] OpenAI分段成功: ${segments.length}段，内容验证通过`);
            }

            return segments;

        } catch (error) {
            console.error(`[AIChatPlugin] OpenAI分段失败: ${error.message}`);
            // 降级到简单分段
            return this.simpleSegmentResponse(response);
        }
    }

    /**
     * 验证分段内容的完整性
     */
    validateSegments(originalText, segments) {
        if (!segments || segments.length === 0) {
            return {
                isValid: false,
                reason: '分段数组为空',
                joinedLength: 0
            };
        }

        // 拼接所有分段
        const joinedText = segments.join('');

        // 移除空白字符进行比较（因为分段可能会影响空白字符）
        const normalizedOriginal = originalText.replace(/\s+/g, ' ').trim();
        const normalizedJoined = joinedText.replace(/\s+/g, ' ').trim();

        // 检查长度
        if (normalizedOriginal.length !== normalizedJoined.length) {
            return {
                isValid: false,
                reason: `长度不匹配: 原文${normalizedOriginal.length}字符，分段${normalizedJoined.length}字符`,
                joinedLength: normalizedJoined.length,
                originalLength: normalizedOriginal.length
            };
        }

        // 检查内容是否完全一致
        if (normalizedOriginal !== normalizedJoined) {
            // 找出第一个不同的位置
            let diffIndex = -1;
            for (let i = 0; i < Math.min(normalizedOriginal.length, normalizedJoined.length); i++) {
                if (normalizedOriginal[i] !== normalizedJoined[i]) {
                    diffIndex = i;
                    break;
                }
            }

            return {
                isValid: false,
                reason: `内容不匹配，第${diffIndex + 1}个字符开始不同`,
                joinedLength: normalizedJoined.length,
                originalLength: normalizedOriginal.length,
                diffIndex: diffIndex,
                originalChar: normalizedOriginal[diffIndex] || 'EOF',
                joinedChar: normalizedJoined[diffIndex] || 'EOF'
            };
        }

        // 检查分段数量是否合理
        if (segments.length > this.maxSegments) {
            return {
                isValid: false,
                reason: `分段数量过多: ${segments.length} > ${this.maxSegments}`,
                joinedLength: normalizedJoined.length
            };
        }

        // 检查是否有空分段
        const emptySegments = segments.filter(seg => !seg.trim());
        if (emptySegments.length > 0) {
            return {
                isValid: false,
                reason: `存在${emptySegments.length}个空分段`,
                joinedLength: normalizedJoined.length
            };
        }

        return {
            isValid: true,
            reason: '验证通过',
            joinedLength: normalizedJoined.length,
            segmentCount: segments.length
        };
    }

    /**
     * 简单分段方法（降级处理）- 模拟人类聊天习惯
     */
    simpleSegmentResponse(response) {
        // 如果文本很短，不分段
        if (response.length <= 80) {
            return [response];
        }

        // 首先按自然段落分段
        const paragraphs = response.split(/\n\s*\n/).filter(p => p.trim());
        if (paragraphs.length > 1 && paragraphs.length <= this.maxSegments) {
            // 验证段落分段的完整性
            const validation = this.validateSegments(response, paragraphs);
            if (validation.isValid) {
                return paragraphs;
            } else {
                console.warn(`[AIChatPlugin] 段落分段验证失败: ${validation.reason}`);
            }
        }

        // 按照人类聊天习惯的标点符号分段
        // 优先级：句号 > 问号/感叹号 > 分号/冒号 > 逗号
        const text = response.trim();

        // 先尝试按强标点分段（句号、问号、感叹号）
        const strongPunctuationPattern = /([。！？.!?]+)\s*/g;
        const strongSentences = this.splitByPattern(text, strongPunctuationPattern);

        if (strongSentences.length > 1) {
            // 合并过短的句子，分离过长的句子
            const balancedSegments = this.balanceSegments(strongSentences);
            if (balancedSegments.length <= this.maxSegments) {
                return balancedSegments;
            }
        }

        // 如果强标点分段太多，尝试按中等标点分段
        const mediumPunctuationPattern = /([；：;:]+)\s*/g;
        const mediumSentences = this.splitByPattern(text, mediumPunctuationPattern);

        if (mediumSentences.length > 1 && mediumSentences.length <= this.maxSegments) {
            return this.balanceSegments(mediumSentences);
        }

        // 最后按逗号分段（但要保证语义完整）
        const commaSentences = this.splitByCommaWithContext(text);
        if (commaSentences.length > 1) {
            return this.balanceSegments(commaSentences).slice(0, this.maxSegments);
        }

        // 如果都不行，按长度强制分段
        return this.forceSegmentByLength(text);
    }

    /**
     * 按模式分割文本
     */
    splitByPattern(text, pattern) {
        const parts = [];
        let lastIndex = 0;
        let match;

        while ((match = pattern.exec(text)) !== null) {
            const content = text.substring(lastIndex, match.index + match[0].length).trim();
            if (content) {
                parts.push(content);
            }
            lastIndex = match.index + match[0].length;
        }

        // 添加剩余部分
        const remaining = text.substring(lastIndex).trim();
        if (remaining) {
            parts.push(remaining);
        }

        return parts.filter(part => part.length > 0);
    }

    /**
     * 平衡分段长度
     */
    balanceSegments(segments) {
        const balanced = [];
        let currentSegment = '';

        for (const segment of segments) {
            // 如果当前段落为空，直接添加
            if (!currentSegment) {
                currentSegment = segment;
            }
            // 如果合并后长度合适（30-120字符），合并
            else if ((currentSegment + segment).length <= 120 && currentSegment.length < 60) {
                currentSegment += segment;
            }
            // 否则，保存当前段落，开始新段落
            else {
                balanced.push(currentSegment);
                currentSegment = segment;
            }
        }

        // 添加最后一段
        if (currentSegment) {
            balanced.push(currentSegment);
        }

        return balanced;
    }

    /**
     * 按逗号分段但保持语义完整
     */
    splitByCommaWithContext(text) {
        const parts = text.split(/，|,/).map(part => part.trim()).filter(part => part.length > 0);
        const segments = [];
        let currentSegment = '';

        for (let i = 0; i < parts.length; i++) {
            const part = parts[i];

            if (!currentSegment) {
                currentSegment = part;
            } else {
                const combined = currentSegment + '，' + part;

                // 如果合并后长度合适且语义完整，继续合并
                if (combined.length <= 100) {
                    currentSegment = combined;
                } else {
                    segments.push(currentSegment);
                    currentSegment = part;
                }
            }
        }

        if (currentSegment) {
            segments.push(currentSegment);
        }

        return segments;
    }

    /**
     * 按长度强制分段
     */
    forceSegmentByLength(text) {
        const segments = [];
        const maxLength = 100;

        for (let i = 0; i < text.length; i += maxLength) {
            segments.push(text.substring(i, i + maxLength));
        }

        return segments.slice(0, this.maxSegments);
    }

    /**
     * 发送分段回复
     */
    async sendSegmentedReply(context, segments, mediaItems, useVoice = false, voiceCharacter = null) {
        try {
            // 获取可用角色列表（如果需要语音）
            if (useVoice) {
                await this.getAvailableCharacters(context);
            }

            // 添加初始延迟
            if (this.responseDelay > 0) {
                await new Promise(resolve => setTimeout(resolve, this.responseDelay));
            }

            // 发送文本段落
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                if (segment && segment.trim()) {
                    // 决定是否使用语音（只对最后一段或随机选择）
                    const shouldVoiceThisSegment = useVoice && (
                        i === segments.length - 1 || // 最后一段
                        Math.random() < 0.5 // 50%概率
                    );

                    if (shouldVoiceThisSegment) {
                        await this.sendVoiceMessage(context, segment.trim(), voiceCharacter);
                    } else {
                        await this.reply(context, segment.trim());
                    }

                    if (this.logResponses) {
                        console.log(`[AIChatPlugin] 发送第${i + 1}段: ${segment.length}字符 ${shouldVoiceThisSegment ? '(语音)' : '(文本)'}`);
                    }

                    // 段落间延迟
                    if (i < segments.length - 1 && this.segmentDelay > 0) {
                        await new Promise(resolve => setTimeout(resolve, this.segmentDelay));
                    }
                }
            }

            // 发送媒体内容
            await this.sendMediaItems(context, mediaItems);

            if (this.logResponses) {
                console.log(`[AIChatPlugin] 分段回复完成: ${segments.length}段文本 + ${mediaItems.length}个媒体 ${useVoice ? '(含语音)' : ''}`);
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 发送分段回复失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 发送单个响应（不分段）
     */
    async sendSingleResponse(context, response, mediaItems, useVoice = false, voiceCharacter = null) {
        try {
            // 获取可用角色列表（如果需要语音）
            if (useVoice) {
                await this.getAvailableCharacters(context);
            }

            // 添加响应延迟
            if (this.responseDelay > 0) {
                await new Promise(resolve => setTimeout(resolve, this.responseDelay));
            }

            // 发送文本内容
            if (response && response.trim()) {
                if (useVoice) {
                    await this.sendVoiceMessage(context, response.trim(), voiceCharacter);
                } else {
                    await this.reply(context, response.trim());
                }
            }

            // 发送媒体内容
            await this.sendMediaItems(context, mediaItems);

            if (this.logResponses) {
                console.log(`[AIChatPlugin] 单段回复完成: ${response.length}字符 + ${mediaItems.length}个媒体 ${useVoice ? '(语音)' : '(文本)'}`);
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 发送单段回复失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 发送媒体内容（图片和文件）
     */
    async sendMediaItems(context, mediaItems) {
        if (!mediaItems || mediaItems.length === 0) {
            return;
        }

        try {
            for (const item of mediaItems) {
                if (item.type === 'image') {
                    await this.sendImage(context, item.url);
                } else if (item.type === 'file') {
                    await this.sendFile(context, item.url);
                }

                // 媒体项目间的小延迟
                if (mediaItems.length > 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 媒体内容发送完成: ${mediaItems.length}项`);
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 发送媒体内容失败: ${error.message}`);
        }
    }

    /**
     * 发送图片
     */
    async sendImage(context, imageUrl) {
        try {
            const imageMessage = [
                {
                    type: 'image',
                    data: {
                        file: imageUrl
                    }
                }
            ];

            await this.reply(context, imageMessage);

            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 图片发送成功: ${imageUrl}`);
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 发送图片失败: ${error.message}`);
            // 降级：发送文本提示
            await this.reply(context, `[图片] ${imageUrl}`);
        }
    }

    /**
     * 发送文件
     */
    async sendFile(context, fileUrl) {
        try {
            // 提取文件名
            const fileName = fileUrl.split('/').pop() || 'file';

            if (context.type === 'group') {
                // 群聊中发送文件链接
                await this.reply(context, `[文件] ${fileName}\n${fileUrl}`);
            } else {
                // 私聊中尝试发送文件
                const fileMessage = [
                    {
                        type: 'file',
                        data: {
                            file: fileUrl,
                            name: fileName
                        }
                    }
                ];

                await this.reply(context, fileMessage);
            }

            if (this.enableDebug) {
                console.log(`[AIChatPlugin] 文件发送成功: ${fileName}`);
            }

        } catch (error) {
            console.error(`[AIChatPlugin] 发送文件失败: ${error.message}`);
            // 降级：发送文本提示
            const fileName = fileUrl.split('/').pop() || 'file';
            await this.reply(context, `[文件] ${fileName}\n${fileUrl}`);
        }
    }





    /**
     * 获取插件帮助信息
     */
    getHelp() {
        const triggerMethods = [];

        if (this.triggerKeywords.length > 0) {
            triggerMethods.push(`- 包含关键词：${this.triggerKeywords.join('、')}`);
        }

        if (this.enableAtTrigger) {
            triggerMethods.push('- @机器人：使用helper.extractAts()方法精确识别');
        }

        if (this.enableReplyTrigger) {
            triggerMethods.push('- 回复机器人的消息：使用helper.extractReplies()方法识别');
        }

        if (this.enablePrivateTrigger) {
            triggerMethods.push('- 私聊消息：直接私聊机器人');
        }

        const features = [
            'AI对话插件帮助：',
            '• 支持配置文件和热重载功能',
            '• 支持的触发方式：',
            ...triggerMethods.map(method => `  ${method}`),
            '• 智能用户信息显示：昵称(QQ号:xxx)[群身份]格式',
            `• 自动携带最近${this.maxHistoryLines}条聊天记录作为上下文`,
            '• 基于plugin_helper标准方法解析消息段，稳定可靠',
            `• AI模型：${this.aiConfig.model}`,
        ];

        // 分段回复功能
        if (this.enableSegmentedReply) {
            features.push(`• 智能分段回复：最多${this.maxSegments}段，段间延迟${this.segmentDelay}ms`);
            if (this.openaiConfig.enableTools && this.openaiConfig.apiKey !== 'sk-your-openai-api-key-here') {
                features.push(`• OpenAI Tools分段：使用${this.openaiConfig.model}进行语义分段`);
            } else {
                features.push('• 简单分段：基于标点符号和段落的规则分段');
            }
        }

        // 媒体处理功能
        if (this.mediaConfig.enableImageDetection) {
            features.push(`• 智能图片识别：支持markdown和HTML格式的图片链接`);
            features.push(`• 支持图片格式：${this.mediaConfig.supportedImageFormats.join(', ')}`);
        }

        if (this.mediaConfig.enableFileDetection) {
            features.push(`• 智能文件识别：自动识别并发送文件链接`);
            features.push(`• 支持文件格式：${this.mediaConfig.supportedFileFormats.join(', ')}`);
        }

        // 语音功能
        if (this.voiceConfig.enableVoiceMode) {
            features.push(`• AI语音回复：基于NapCat的AI语音功能`);
            features.push(`• 当前语音角色：${this.currentVoiceCharacter}`);
            features.push(`• 语音触发概率：${(this.voiceConfig.voiceProbability * 100).toFixed(0)}%`);
            if (this.voiceConfig.enableVoiceKeywords) {
                features.push(`• 语音关键词：${this.voiceConfig.voiceKeywords.join('、')}`);
            }
            if (this.voiceConfig.enableCharacterSwitch) {
                features.push(`• 支持角色切换：在消息中指定角色名称`);
            }
            features.push(`• 最大语音长度：${this.voiceConfig.maxVoiceLength}字符`);
        }

        features.push('• 支持请求重试和超时控制');
        features.push('• 配置文件：config.json -> AI_CHAT_PLUGIN');

        return features.join('\n');
    }

    /**
     * 插件停止时的清理工作
     */
    async onStop() {
        this.stopConfigWatcher();
        console.log(`[AIChatPlugin] 插件已停止`);
    }

    /**
     * 插件启动时的初始化工作
     */
    async onStart() {
        if (this.enableDebug) {
            console.log(`[AIChatPlugin] 插件已启动，配置监听中...`);
        }
    }
}

module.exports = AIChatPlugin;
