/**
 * 自动调节监控系统
 * 监控和确保情绪、压力、好感度的自动调节机制正常工作
 */

class AutoRegulationMonitor {
    constructor(config, logger, database) {
        this.config = config;
        this.logger = logger;
        this.database = database;
        
        // 监控配置
        this.monitorConfig = {
            checkInterval: 60000,        // 1分钟检查一次
            alertThresholds: {
                emotion: { extreme: 80, warning: 60 },
                stress: { extreme: 15, warning: 10 },
                affinity: { extreme: -50, warning: -30 }
            },
            autoRegulationEnabled: true,
            emergencyRegulation: true
        };
        
        // 监控状态
        this.monitorState = {
            isRunning: false,
            lastCheck: null,
            regulationStats: {
                emotion: { adjustments: 0, totalAdjustment: 0 },
                stress: { adjustments: 0, totalAdjustment: 0 },
                affinity: { adjustments: 0, totalAdjustment: 0 }
            },
            alerts: []
        };
        
        // 定时器
        this.monitorTimer = null;
    }

    /**
     * 启动自动调节监控
     */
    startMonitoring() {
        if (this.monitorState.isRunning) {
            this.logger.warn('自动调节监控', '监控已在运行中');
            return;
        }
        
        this.logger.info('自动调节监控', '启动自动调节监控系统');
        this.monitorState.isRunning = true;
        
        // 立即执行一次检查
        this.performRegulationCheck();
        
        // 设置定时检查
        this.monitorTimer = setInterval(() => {
            this.performRegulationCheck();
        }, this.monitorConfig.checkInterval);
        
        this.logger.success('自动调节监控', '自动调节监控系统已启动');
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        if (!this.monitorState.isRunning) {
            return;
        }
        
        this.logger.info('自动调节监控', '停止自动调节监控系统');
        this.monitorState.isRunning = false;
        
        if (this.monitorTimer) {
            clearInterval(this.monitorTimer);
            this.monitorTimer = null;
        }
        
        this.logger.success('自动调节监控', '自动调节监控系统已停止');
    }

    /**
     * 执行调节检查
     */
    async performRegulationCheck() {
        try {
            this.monitorState.lastCheck = new Date().toISOString();
            
            // 1. 检查情绪状态
            await this.checkEmotionRegulation();
            
            // 2. 检查压力状态
            await this.checkStressRegulation();
            
            // 3. 检查好感度状态
            await this.checkAffinityRegulation();
            
            // 4. 生成监控报告
            this.generateMonitorReport();
            
        } catch (error) {
            this.logger.error('自动调节监控', '调节检查失败:', error.message);
        }
    }

    /**
     * 检查情绪调节
     */
    async checkEmotionRegulation() {
        try {
            // 获取需要调节的情绪状态 - 使用原有表结构
            const extremeEmotions = await this.database.all(`
                SELECT user_id, persona_name, emotion_value, timestamp,
                       (julianday('now') - julianday(timestamp)) * 24 * 60 as minutes_since_update
                FROM ai_emotion_states
                WHERE ABS(emotion_value) > ?
                   OR (ABS(emotion_value) > ? AND minutes_since_update > 30)
                ORDER BY ABS(emotion_value) DESC
            `, [
                this.monitorConfig.alertThresholds.emotion.extreme,
                this.monitorConfig.alertThresholds.emotion.warning
            ]) || [];

            for (const emotion of extremeEmotions) {
                const severity = Math.abs(emotion.emotion_value);
                const isExtreme = severity > this.monitorConfig.alertThresholds.emotion.extreme;

                if (isExtreme) {
                    this.addAlert('emotion', 'extreme', emotion);

                    // 应用紧急调节
                    if (this.monitorConfig.emergencyRegulation) {
                        await this.applyEmergencyEmotionRegulation(emotion);
                    }
                } else {
                    this.addAlert('emotion', 'warning', emotion);

                    // 应用常规调节
                    if (this.monitorConfig.autoRegulationEnabled) {
                        await this.applyEmotionRegulation(emotion);
                    }
                }
            }

        } catch (error) {
            this.logger.error('自动调节监控', '情绪调节检查失败:', error.message);
        }
    }

    /**
     * 检查压力调节
     */
    async checkStressRegulation() {
        try {
            // 获取需要调节的压力状态 - 使用原有表结构
            const extremeStress = await this.database.all(`
                SELECT user_id, persona_name, stress_value, timestamp,
                       (julianday('now') - julianday(timestamp)) * 24 * 60 as minutes_since_update
                FROM ai_stress_states
                WHERE ABS(stress_value) > ?
                   OR (ABS(stress_value) > ? AND minutes_since_update > 20)
                ORDER BY ABS(stress_value) DESC
            `, [
                this.monitorConfig.alertThresholds.stress.extreme,
                this.monitorConfig.alertThresholds.stress.warning
            ]) || [];

            for (const stress of extremeStress) {
                const severity = Math.abs(stress.stress_value);
                const isExtreme = severity > this.monitorConfig.alertThresholds.stress.extreme;
                
                if (isExtreme) {
                    this.addAlert('stress', 'extreme', stress);
                    
                    // 应用紧急调节
                    if (this.monitorConfig.emergencyRegulation) {
                        await this.applyEmergencyStressRegulation(stress);
                    }
                } else {
                    this.addAlert('stress', 'warning', stress);
                    
                    // 应用常规调节
                    if (this.monitorConfig.autoRegulationEnabled) {
                        await this.applyStressRegulation(stress);
                    }
                }
            }

        } catch (error) {
            this.logger.error('自动调节监控', '压力调节检查失败:', error.message);
        }
    }

    /**
     * 检查好感度调节
     */
    async checkAffinityRegulation() {
        try {
            // 获取需要调节的好感度状态 - 使用原有表结构
            const extremeAffinity = await this.database.all(`
                SELECT user_id, persona_name, current_affinity, timestamp,
                       (julianday('now') - julianday(timestamp)) * 24 * 60 as minutes_since_update
                FROM user_affinity
                WHERE current_affinity < ?
                   OR (current_affinity < ? AND minutes_since_update > 60)
                ORDER BY current_affinity ASC
            `, [
                this.monitorConfig.alertThresholds.affinity.extreme,
                this.monitorConfig.alertThresholds.affinity.warning
            ]) || [];

            for (const affinity of extremeAffinity) {
                const isExtreme = affinity.current_affinity < this.monitorConfig.alertThresholds.affinity.extreme;
                
                if (isExtreme) {
                    this.addAlert('affinity', 'extreme', affinity);
                    
                    // 应用紧急调节
                    if (this.monitorConfig.emergencyRegulation) {
                        await this.applyEmergencyAffinityRegulation(affinity);
                    }
                } else {
                    this.addAlert('affinity', 'warning', affinity);
                    
                    // 应用常规调节
                    if (this.monitorConfig.autoRegulationEnabled) {
                        await this.applyAffinityRegulation(affinity);
                    }
                }
            }

        } catch (error) {
            this.logger.error('自动调节监控', '好感度调节检查失败:', error.message);
        }
    }

    /**
     * 应用紧急情绪调节
     */
    async applyEmergencyEmotionRegulation(emotionState) {
        try {
            const currentValue = emotionState.emotion_value;
            const targetValue = 0; // 目标中性值
            const maxAdjustment = 10; // 紧急调节最大幅度

            // 计算调节量
            let adjustment = (targetValue - currentValue) * 0.3; // 30%的调节
            adjustment = Math.max(-maxAdjustment, Math.min(maxAdjustment, adjustment));

            const newValue = currentValue + adjustment;

            // 更新数据库 - 使用简化表结构
            await this.database.run(`
                UPDATE ai_emotion_states
                SET emotion_value = ?, timestamp = CURRENT_TIMESTAMP
                WHERE user_id = ? AND persona_name = ?
            `, [newValue, emotionState.user_id, emotionState.persona_name]);

            // 记录调节
            this.recordRegulation('emotion', adjustment);

            this.logger.warn('紧急情绪调节',
                `用户 ${emotionState.user_id} 情绪紧急调节: ${currentValue.toFixed(2)} → ${newValue.toFixed(2)}`
            );

        } catch (error) {
            this.logger.error('紧急情绪调节', '紧急情绪调节失败:', error.message);
        }
    }

    /**
     * 应用紧急压力调节
     */
    async applyEmergencyStressRegulation(stressState) {
        try {
            const currentValue = stressState.stress_value;
            const targetValue = 0; // 目标中性值
            const maxAdjustment = 5; // 紧急调节最大幅度
            
            // 计算调节量
            let adjustment = (targetValue - currentValue) * 0.4; // 40%的调节
            adjustment = Math.max(-maxAdjustment, Math.min(maxAdjustment, adjustment));
            
            const newValue = currentValue + adjustment;
            
            // 更新数据库
            await this.database.run(`
                UPDATE ai_stress_states 
                SET stress_value = ?, timestamp = CURRENT_TIMESTAMP 
                WHERE user_id = ? AND persona_name = ?
            `, [newValue, stressState.user_id, stressState.persona_name]);
            
            // 记录调节
            this.recordRegulation('stress', adjustment);
            
            this.logger.warn('紧急压力调节', 
                `用户 ${stressState.user_id} 压力紧急调节: ${currentValue.toFixed(2)} → ${newValue.toFixed(2)}`
            );

        } catch (error) {
            this.logger.error('紧急压力调节', '紧急压力调节失败:', error.message);
        }
    }

    /**
     * 应用紧急好感度调节
     */
    async applyEmergencyAffinityRegulation(affinityState) {
        try {
            const currentValue = affinityState.current_affinity;
            const targetValue = 20; // 目标轻微正面值
            const maxAdjustment = 8; // 紧急调节最大幅度

            // 计算调节量
            let adjustment = (targetValue - currentValue) * 0.2; // 20%的调节
            adjustment = Math.max(-maxAdjustment, Math.min(maxAdjustment, adjustment));

            const newValue = Math.max(-100, Math.min(100, currentValue + adjustment));

            // 更新数据库 - 使用原有表结构
            await this.database.run(`
                UPDATE user_affinity
                SET current_affinity = ?, timestamp = CURRENT_TIMESTAMP
                WHERE user_id = ? AND persona_name = ?
            `, [newValue, affinityState.user_id, affinityState.persona_name]);

            // 记录调节
            this.recordRegulation('affinity', adjustment);

            this.logger.warn('紧急好感度调节',
                `用户 ${affinityState.user_id} 好感度紧急调节: ${currentValue.toFixed(2)} → ${newValue.toFixed(2)}`
            );

        } catch (error) {
            this.logger.error('紧急好感度调节', '紧急好感度调节失败:', error.message);
        }
    }

    /**
     * 应用常规情绪调节
     */
    async applyEmotionRegulation(emotionState) {
        try {
            const currentValue = emotionState.emotion_value;
            const targetValue = 0;
            const maxAdjustment = 3; // 常规调节幅度较小

            let adjustment = (targetValue - currentValue) * 0.1; // 10%的调节
            adjustment = Math.max(-maxAdjustment, Math.min(maxAdjustment, adjustment));

            const newValue = currentValue + adjustment;

            await this.database.run(`
                UPDATE ai_emotion_states
                SET emotion_value = ?, timestamp = CURRENT_TIMESTAMP
                WHERE user_id = ? AND persona_name = ?
            `, [newValue, emotionState.user_id, emotionState.persona_name]);

            this.recordRegulation('emotion', adjustment);

        } catch (error) {
            this.logger.error('常规情绪调节', '常规情绪调节失败:', error.message);
        }
    }

    /**
     * 应用常规压力调节
     */
    async applyStressRegulation(stressState) {
        try {
            const currentValue = stressState.stress_value;
            const targetValue = 0;
            const maxAdjustment = 2;

            let adjustment = (targetValue - currentValue) * 0.15;
            adjustment = Math.max(-maxAdjustment, Math.min(maxAdjustment, adjustment));

            const newValue = currentValue + adjustment;

            await this.database.run(`
                UPDATE ai_stress_states
                SET stress_value = ?, timestamp = CURRENT_TIMESTAMP
                WHERE user_id = ? AND persona_name = ?
            `, [newValue, stressState.user_id, stressState.persona_name]);

            this.recordRegulation('stress', adjustment);

        } catch (error) {
            this.logger.error('常规压力调节', '常规压力调节失败:', error.message);
        }
    }

    /**
     * 应用常规好感度调节
     */
    async applyAffinityRegulation(affinityState) {
        try {
            const currentValue = affinityState.current_affinity;
            const targetValue = 30; // 目标轻微正面值
            const maxAdjustment = 3;

            let adjustment = (targetValue - currentValue) * 0.05; // 5%的调节
            adjustment = Math.max(-maxAdjustment, Math.min(maxAdjustment, adjustment));

            const newValue = Math.max(-100, Math.min(100, currentValue + adjustment));

            await this.database.run(`
                UPDATE user_affinity
                SET current_affinity = ?, timestamp = CURRENT_TIMESTAMP
                WHERE user_id = ? AND persona_name = ?
            `, [newValue, affinityState.user_id, affinityState.persona_name]);

            this.recordRegulation('affinity', adjustment);

        } catch (error) {
            this.logger.error('常规好感度调节', '常规好感度调节失败:', error.message);
        }
    }

    /**
     * 添加警报
     */
    addAlert(type, severity, state) {
        const alert = {
            type,
            severity,
            state,
            timestamp: new Date().toISOString(),
            id: `${type}_${severity}_${Date.now()}`
        };
        
        this.monitorState.alerts.push(alert);
        
        // 保持最近100个警报
        if (this.monitorState.alerts.length > 100) {
            this.monitorState.alerts.shift();
        }
        
        this.logger.warn('自动调节监控', 
            `${severity.toUpperCase()} 警报: ${type} - 用户 ${state.user_id} 值: ${state[this.getStateValueKey(type)]}`
        );
    }

    /**
     * 记录调节
     */
    recordRegulation(type, adjustment) {
        this.monitorState.regulationStats[type].adjustments++;
        this.monitorState.regulationStats[type].totalAdjustment += Math.abs(adjustment);
    }

    /**
     * 获取状态值键名
     */
    getStateValueKey(type) {
        switch (type) {
            case 'emotion': return 'emotion_value';
            case 'stress': return 'stress_value';
            case 'affinity': return 'current_affinity';
            default: return 'value';
        }
    }

    /**
     * 生成监控报告
     */
    generateMonitorReport() {
        const recentAlerts = this.monitorState.alerts.filter(alert => {
            const alertTime = new Date(alert.timestamp);
            const now = new Date();
            return (now - alertTime) < 3600000; // 最近1小时的警报
        });
        
        if (recentAlerts.length > 0) {
            this.logger.info('自动调节监控', 
                `监控报告: 最近1小时内 ${recentAlerts.length} 个警报, ` +
                `调节统计: 情绪${this.monitorState.regulationStats.emotion.adjustments}次, ` +
                `压力${this.monitorState.regulationStats.stress.adjustments}次, ` +
                `好感度${this.monitorState.regulationStats.affinity.adjustments}次`
            );
        }
    }

    /**
     * 获取监控状态
     */
    getMonitorStatus() {
        return {
            isRunning: this.monitorState.isRunning,
            lastCheck: this.monitorState.lastCheck,
            recentAlerts: this.monitorState.alerts.slice(-10), // 最近10个警报
            regulationStats: this.monitorState.regulationStats,
            config: this.monitorConfig
        };
    }

    /**
     * 更新监控配置
     */
    updateConfig(newConfig) {
        this.monitorConfig = { ...this.monitorConfig, ...newConfig };
        this.logger.info('自动调节监控', '监控配置已更新');
    }
}

module.exports = AutoRegulationMonitor;
