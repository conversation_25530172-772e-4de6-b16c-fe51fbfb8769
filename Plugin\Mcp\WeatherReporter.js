// Plugin/Mcp/WeatherReporter.js - 天气查询MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class WeatherReporterMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'WeatherReporter';
        this.description = '获取指定城市的天气信息，支持当前天气、7天预报、24小时预报和天气预警，支持token截断';
        this.vcpName = 'WeatherReporter';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                query_type: {
                    type: 'string',
                    description: '查询类型：current(当前天气), forecast(7天预报), hourly(24小时预报), warning(天气预警), all(全部信息)',
                    enum: ['current', 'forecast', 'hourly', 'warning', 'all'],
                    default: 'current'
                },
                city: {
                    type: 'string',
                    description: '要查询天气的城市名称（可选，如未提供则使用配置的默认城市）'
                }
            },
            required: []
        };
    }

    async execute(args) {
        try {
            this.validateArgs(args);
            
            const { query_type = 'current', city } = args;
            
            this.log('info', `开始查询天气: 类型=${query_type}, 城市=${city || '默认'}`);
            
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin({ query_type, city });
            
            // 解析VCP插件返回的结果
            let parsedResult;
            if (typeof result === 'string') {
                try {
                    parsedResult = JSON.parse(result);
                } catch (e) {
                    // 如果不是JSON格式，作为纯文本处理
                    parsedResult = { 
                        content: result,
                        token_info: {
                            original_tokens: Math.ceil(result.length / 4),
                            final_tokens: Math.ceil(result.length / 4),
                            truncated: false
                        }
                    };
                }
            } else {
                parsedResult = result;
            }
            
            // 记录token信息
            if (parsedResult.token_info) {
                this.log('info', `Token信息: 原始${parsedResult.token_info.original_tokens}个token, 最终${parsedResult.token_info.final_tokens}个token, 截断: ${parsedResult.token_info.truncated}`);
                
                if (parsedResult.token_info.truncated) {
                    this.log('warning', `天气信息已截断，原始token数量超过限制`);
                }
            }
            
            // 格式化返回结果
            const response = {
                type: 'weather_report',
                status: 'success',
                message: '天气信息查询完成',
                data: {
                    query_type: query_type,
                    city: city || parsedResult.token_info?.city || '默认城市',
                    content: parsedResult.result || parsedResult.content || parsedResult,
                    token_info: parsedResult.token_info || {
                        original_tokens: 0,
                        final_tokens: 0,
                        truncated: false
                    },
                    fetch_time: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString()
                }
            };
            
            this.log('success', `天气查询成功: ${query_type} - ${city || '默认城市'}`);
            return response;
            
        } catch (error) {
            this.log('error', `天气查询失败: ${error.message}`);
            return {
                type: 'weather_report',
                status: 'error',
                message: `天气查询失败: ${error.message}`,
                data: null
            };
        }
    }

    // 重写初始化方法，检查天气API配置
    async initialize() {
        await super.initialize();
        
        // 检查必需的环境变量
        const requiredEnvVars = ['WeatherKey', 'WeatherUrl', 'VarCity'];
        const missingVars = [];
        
        for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
                missingVars.push(envVar);
            }
        }
        
        if (missingVars.length > 0) {
            this.log('warning', `缺少天气API配置: ${missingVars.join(', ')}`);
        } else {
            this.log('info', '天气API配置检查通过');
        }
        
        return true;
    }
}

module.exports = WeatherReporterMcp; 