/**
 * 自主对话系统
 * 让Agent能够根据环境、心理状态、关系网络自主发起对话和互动
 */

const EventEmitter = require('events');

class AutonomousDialogue extends EventEmitter {
    constructor(worldCore) {
        super();
        this.worldCore = worldCore;
        this.logger = worldCore.logger;
        
        // 对话触发器
        this.dialogueTriggers = {
            needBased: true, // 基于需求的对话
            moodBased: true, // 基于心情的对话
            relationshipBased: true, // 基于关系的对话
            environmentBased: true, // 基于环境的对话
            eventBased: true // 基于事件的对话
        };

        // 对话主题库
        this.dialogueTopics = {
            greeting: ['你好', '早上好', '下午好', '晚上好', '最近怎么样'],
            weather: ['今天天气真不错', '外面在下雨呢', '天气有点冷', '阳光明媚的一天'],
            mood: ['你看起来很开心', '你好像有点累', '发生什么好事了吗', '需要聊聊吗'],
            activity: ['你在做什么', '要不要一起去散步', '有什么计划吗', '想做点什么'],
            memory: ['还记得上次我们', '那天真有趣', '你提到过的那件事', '我想起了'],
            help: ['需要帮助吗', '我可以帮你', '有什么我能做的', '让我来帮你'],
            compliment: ['你今天看起来很棒', '你做得很好', '我很欣赏你', '你真厉害'],
            concern: ['你还好吗', '我有点担心你', '发生什么事了', '需要谈谈吗']
        };

        // 对话历史
        this.dialogueHistory = new Map(); // agentId -> conversations[]
        
        // 对话状态
        this.activeConversations = new Map(); // conversationId -> conversation
        
        // 更新间隔
        this.updateInterval = null;
        this.updateFrequency = 45000; // 45秒检查一次对话机会
    }

    /**
     * 初始化自主对话系统
     */
    async init() {
        this.logger.info('💬 初始化自主对话系统...');
        
        // 监听Agent事件
        this.setupEventListeners();
        
        this.logger.info('✅ 自主对话系统初始化完成');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听Agent关键需求事件
        this.worldCore.agentEcosystem.on('criticalNeed', (data) => {
            this.handleCriticalNeedDialogue(data);
        });

        // 监听环境变化事件
        this.worldCore.worldEnvironment.on('weatherChanged', (data) => {
            this.handleWeatherDialogue(data);
        });

        // 监听Agent进入地点事件
        this.worldCore.worldEnvironment.on('agentEnteredLocation', (data) => {
            this.handleLocationDialogue(data);
        });
    }

    /**
     * 启动自主对话系统
     */
    async start() {
        if (this.updateInterval) {
            return;
        }

        this.updateInterval = setInterval(() => {
            this.checkDialogueOpportunities();
        }, this.updateFrequency);

        this.logger.info('💬 自主对话系统已启动');
    }

    /**
     * 暂停自主对话系统
     */
    async pause() {
        this.logger.info('⏸️ 自主对话系统已暂停');
    }

    /**
     * 恢复自主对话系统
     */
    async resume() {
        this.logger.info('▶️ 自主对话系统已恢复');
    }

    /**
     * 停止自主对话系统
     */
    async stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        this.logger.info('🛑 自主对话系统已停止');
    }

    /**
     * 检查对话机会
     */
    checkDialogueOpportunities() {
        if (this.worldCore.worldState.isPaused) {
            return;
        }

        const agents = this.worldCore.agentEcosystem.getActiveAgents();
        
        for (const agent of agents) {
            this.evaluateDialogueOpportunity(agent);
        }
    }

    /**
     * 评估对话机会
     */
    evaluateDialogueOpportunity(agent) {
        // 检查Agent是否已经在对话中
        if (this.isAgentInConversation(agent.id)) {
            return;
        }

        // 基于需求的对话触发
        if (this.dialogueTriggers.needBased) {
            this.checkNeedBasedDialogue(agent);
        }

        // 基于心情的对话触发
        if (this.dialogueTriggers.moodBased) {
            this.checkMoodBasedDialogue(agent);
        }

        // 基于关系的对话触发
        if (this.dialogueTriggers.relationshipBased) {
            this.checkRelationshipBasedDialogue(agent);
        }

        // 基于环境的对话触发
        if (this.dialogueTriggers.environmentBased) {
            this.checkEnvironmentBasedDialogue(agent);
        }
    }

    /**
     * 检查基于需求的对话
     */
    checkNeedBasedDialogue(agent) {
        const criticalNeeds = [];
        
        for (const [need, value] of Object.entries(agent.needs)) {
            const threshold = this.worldCore.agentEcosystem.needsSystem.criticalThresholds[need];
            if (value < threshold) {
                criticalNeeds.push(need);
            }
        }

        if (criticalNeeds.length > 0) {
            const targetAgent = this.findSuitableConversationPartner(agent, 'help');
            if (targetAgent) {
                this.initiateDialogue(agent, targetAgent, 'need_help', {
                    needs: criticalNeeds
                });
            }
        }
    }

    /**
     * 检查基于心情的对话
     */
    checkMoodBasedDialogue(agent) {
        if (agent.mood < 30) {
            // 心情不好，寻求安慰
            const targetAgent = this.findSuitableConversationPartner(agent, 'comfort');
            if (targetAgent) {
                this.initiateDialogue(agent, targetAgent, 'seek_comfort', {
                    mood: agent.mood
                });
            }
        } else if (agent.mood > 80) {
            // 心情很好，分享快乐
            const targetAgent = this.findSuitableConversationPartner(agent, 'share');
            if (targetAgent) {
                this.initiateDialogue(agent, targetAgent, 'share_happiness', {
                    mood: agent.mood
                });
            }
        }
    }

    /**
     * 检查基于关系的对话
     */
    checkRelationshipBasedDialogue(agent) {
        const relationships = this.worldCore.socialNetwork.getAgentRelationships(agent.id);
        
        for (const [targetId, relationship] of relationships.entries()) {
            // 检查是否很久没有互动了
            const daysSinceLastInteraction = (Date.now() - new Date(relationship.lastInteraction).getTime()) / (1000 * 60 * 60 * 24);
            
            if (daysSinceLastInteraction > 3 && relationship.strength > 50) {
                const targetAgent = this.worldCore.agentEcosystem.getAgent(targetId);
                if (targetAgent && this.areAgentsInSameLocation(agent, targetAgent)) {
                    this.initiateDialogue(agent, targetAgent, 'reconnect', {
                        daysSinceLastInteraction,
                        relationshipType: relationship.type
                    });
                    break; // 一次只发起一个对话
                }
            }
        }
    }

    /**
     * 检查基于环境的对话
     */
    checkEnvironmentBasedDialogue(agent) {
        const location = this.worldCore.worldEnvironment.getLocation(agent.currentLocation);
        if (!location) return;

        // 如果在同一地点有其他Agent，可能发起对话
        const otherAgents = location.currentOccupants.filter(id => id !== agent.id);
        
        if (otherAgents.length > 0 && Math.random() < 0.1) { // 10%概率
            const targetId = otherAgents[Math.floor(Math.random() * otherAgents.length)];
            const targetAgent = this.worldCore.agentEcosystem.getAgent(targetId);
            
            if (targetAgent && !this.isAgentInConversation(targetId)) {
                this.initiateDialogue(agent, targetAgent, 'casual_encounter', {
                    location: location.name
                });
            }
        }
    }

    /**
     * 寻找合适的对话伙伴
     */
    findSuitableConversationPartner(agent, purpose) {
        const location = this.worldCore.worldEnvironment.getLocation(agent.currentLocation);
        if (!location) return null;

        const otherAgents = location.currentOccupants
            .filter(id => id !== agent.id && !this.isAgentInConversation(id))
            .map(id => this.worldCore.agentEcosystem.getAgent(id))
            .filter(a => a);

        if (otherAgents.length === 0) return null;

        // 根据目的选择最合适的对话伙伴
        switch (purpose) {
            case 'help':
                return this.findMostHelpfulAgent(agent, otherAgents);
            case 'comfort':
                return this.findMostComfortingAgent(agent, otherAgents);
            case 'share':
                return this.findClosestAgent(agent, otherAgents);
            default:
                return otherAgents[Math.floor(Math.random() * otherAgents.length)];
        }
    }

    /**
     * 寻找最有帮助的Agent
     */
    findMostHelpfulAgent(agent, candidates) {
        return candidates.reduce((best, candidate) => {
            const relationship = this.worldCore.socialNetwork.getRelationship(agent.id, candidate.id);
            const trustworthiness = this.worldCore.socialNetwork.getSocialMetrics(candidate.id).trustworthiness;
            const score = (relationship ? relationship.strength : 0) + trustworthiness;
            
            return !best || score > best.score ? { agent: candidate, score } : best;
        }, null)?.agent;
    }

    /**
     * 寻找最能安慰的Agent
     */
    findMostComfortingAgent(agent, candidates) {
        return candidates.reduce((best, candidate) => {
            const relationship = this.worldCore.socialNetwork.getRelationship(agent.id, candidate.id);
            const agreeableness = candidate.personality.agreeableness || 50;
            const score = (relationship ? relationship.strength : 0) + agreeableness;
            
            return !best || score > best.score ? { agent: candidate, score } : best;
        }, null)?.agent;
    }

    /**
     * 寻找关系最近的Agent
     */
    findClosestAgent(agent, candidates) {
        return candidates.reduce((best, candidate) => {
            const relationship = this.worldCore.socialNetwork.getRelationship(agent.id, candidate.id);
            const strength = relationship ? relationship.strength : 0;
            
            return !best || strength > best.strength ? { agent: candidate, strength } : best;
        }, null)?.agent;
    }

    /**
     * 发起对话
     */
    initiateDialogue(initiator, target, dialogueType, context = {}) {
        const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const conversation = {
            id: conversationId,
            participants: [initiator.id, target.id],
            initiator: initiator.id,
            type: dialogueType,
            context,
            messages: [],
            startTime: new Date(),
            status: 'active'
        };

        // 生成开场白
        const openingMessage = this.generateOpeningMessage(initiator, target, dialogueType, context);
        
        conversation.messages.push({
            speaker: initiator.id,
            content: openingMessage,
            timestamp: new Date(),
            type: 'opening'
        });

        // 保存对话
        this.activeConversations.set(conversationId, conversation);
        
        // 更新关系
        this.worldCore.socialNetwork.updateRelationship(
            initiator.id, 
            target.id, 
            'conversation', 
            2
        );

        this.emit('dialogueInitiated', conversation);
        this.logger.info(`💬 ${initiator.name} 向 ${target.name} 发起对话: ${dialogueType}`);

        // 生成回应
        setTimeout(() => {
            this.generateResponse(conversation);
        }, Math.random() * 5000 + 2000); // 2-7秒后回应
    }

    /**
     * 生成开场白
     */
    generateOpeningMessage(initiator, target, dialogueType, context) {
        const relationship = this.worldCore.socialNetwork.getRelationship(initiator.id, target.id);
        const isClose = relationship && relationship.strength > 50;
        
        switch (dialogueType) {
            case 'need_help':
                return isClose ? 
                    `${target.name}，我遇到了一些困难，能帮帮我吗？` :
                    `不好意思打扰，我需要一些帮助...`;
                    
            case 'seek_comfort':
                return isClose ?
                    `${target.name}，我心情不太好，能陪我聊聊吗？` :
                    `你好，我想找个人聊聊...`;
                    
            case 'share_happiness':
                return isClose ?
                    `${target.name}！我有个好消息要告诉你！` :
                    `你好！今天真是美好的一天！`;
                    
            case 'reconnect':
                return `${target.name}，好久不见！最近怎么样？`;
                
            case 'casual_encounter':
                const weather = this.worldCore.worldEnvironment.weatherSystem.current;
                const greetings = this.dialogueTopics.greeting;
                const weatherComments = this.dialogueTopics.weather;
                
                if (Math.random() < 0.5) {
                    return greetings[Math.floor(Math.random() * greetings.length)];
                } else {
                    return weatherComments[Math.floor(Math.random() * weatherComments.length)];
                }
                
            default:
                return this.dialogueTopics.greeting[Math.floor(Math.random() * this.dialogueTopics.greeting.length)];
        }
    }

    /**
     * 生成回应
     */
    generateResponse(conversation) {
        if (conversation.status !== 'active') return;

        const lastMessage = conversation.messages[conversation.messages.length - 1];
        const responderId = conversation.participants.find(id => id !== lastMessage.speaker);
        const responder = this.worldCore.agentEcosystem.getAgent(responderId);
        
        if (!responder) return;

        // 基于性格和关系生成回应
        const response = this.generateResponseContent(conversation, responder);
        
        conversation.messages.push({
            speaker: responderId,
            content: response,
            timestamp: new Date(),
            type: 'response'
        });

        this.emit('dialogueResponse', {
            conversation,
            message: conversation.messages[conversation.messages.length - 1]
        });

        // 决定是否继续对话
        const shouldContinue = this.shouldContinueConversation(conversation);
        
        if (shouldContinue && conversation.messages.length < 10) {
            // 继续对话
            setTimeout(() => {
                this.generateFollowUp(conversation);
            }, Math.random() * 4000 + 1000); // 1-5秒后继续
        } else {
            // 结束对话
            this.endConversation(conversation.id);
        }
    }

    /**
     * 生成回应内容
     */
    generateResponseContent(conversation, responder) {
        const relationship = this.worldCore.socialNetwork.getRelationship(
            conversation.initiator, 
            responder.id
        );
        
        const agreeableness = responder.personality.agreeableness || 50;
        const extroversion = responder.personality.extroversion || 50;
        
        // 基于性格和关系生成不同类型的回应
        if (agreeableness > 70) {
            return this.generateFriendlyResponse(conversation.type);
        } else if (agreeableness < 30) {
            return this.generateDistantResponse(conversation.type);
        } else {
            return this.generateNeutralResponse(conversation.type);
        }
    }

    /**
     * 生成友好回应
     */
    generateFriendlyResponse(dialogueType) {
        const responses = {
            need_help: ['当然可以！我很乐意帮助你', '没问题，告诉我需要什么帮助', '我来帮你！'],
            seek_comfort: ['我在这里陪你，想聊什么都可以', '别担心，一切都会好起来的', '我理解你的感受'],
            share_happiness: ['太好了！我为你感到高兴！', '真是个好消息！', '你的快乐也感染了我'],
            reconnect: ['是啊，好久不见！我也想你了', '最近过得不错，你呢？', '真高兴又见到你'],
            casual_encounter: ['你好！很高兴遇到你', '是啊，今天真不错', '你也在这里啊']
        };
        
        const responseList = responses[dialogueType] || responses.casual_encounter;
        return responseList[Math.floor(Math.random() * responseList.length)];
    }

    /**
     * 生成疏远回应
     */
    generateDistantResponse(dialogueType) {
        const responses = {
            need_help: ['我现在有点忙...', '你可以试试问别人', '抱歉，我帮不了你'],
            seek_comfort: ['嗯...', '我不太会安慰人', '你会没事的'],
            share_happiness: ['哦，是吗', '好吧', '嗯'],
            reconnect: ['嗯，还行', '最近挺忙的', '是啊'],
            casual_encounter: ['嗯', '你好', '是的']
        };
        
        const responseList = responses[dialogueType] || responses.casual_encounter;
        return responseList[Math.floor(Math.random() * responseList.length)];
    }

    /**
     * 生成中性回应
     */
    generateNeutralResponse(dialogueType) {
        const responses = {
            need_help: ['我看看能不能帮你', '什么事情？', '你遇到什么困难了？'],
            seek_comfort: ['怎么了？发生什么事了？', '你还好吗？', '想聊聊吗？'],
            share_happiness: ['真的吗？太好了', '听起来不错', '我很高兴听到这个'],
            reconnect: ['是啊，最近还好', '你最近怎么样？', '时间过得真快'],
            casual_encounter: ['你好', '是啊，不错的天气', '你也在这里']
        };
        
        const responseList = responses[dialogueType] || responses.casual_encounter;
        return responseList[Math.floor(Math.random() * responseList.length)];
    }

    /**
     * 生成后续对话
     */
    generateFollowUp(conversation) {
        // 简化的后续对话生成
        const topics = ['activity', 'mood', 'memory'];
        const topic = topics[Math.floor(Math.random() * topics.length)];
        const topicMessages = this.dialogueTopics[topic];
        
        const lastSpeaker = conversation.messages[conversation.messages.length - 1].speaker;
        const nextSpeaker = conversation.participants.find(id => id !== lastSpeaker);
        
        conversation.messages.push({
            speaker: nextSpeaker,
            content: topicMessages[Math.floor(Math.random() * topicMessages.length)],
            timestamp: new Date(),
            type: 'followup'
        });

        this.emit('dialogueFollowUp', {
            conversation,
            message: conversation.messages[conversation.messages.length - 1]
        });

        // 继续对话循环
        setTimeout(() => {
            this.generateResponse(conversation);
        }, Math.random() * 3000 + 1000);
    }

    /**
     * 判断是否应该继续对话
     */
    shouldContinueConversation(conversation) {
        // 基于多种因素决定是否继续对话
        const messageCount = conversation.messages.length;
        const duration = Date.now() - new Date(conversation.startTime).getTime();
        
        // 消息太多或时间太长就结束
        if (messageCount >= 8 || duration > 300000) { // 5分钟
            return false;
        }
        
        // 基于参与者的性格决定
        const participants = conversation.participants.map(id => 
            this.worldCore.agentEcosystem.getAgent(id)
        ).filter(a => a);
        
        const avgExtroversion = participants.reduce((sum, agent) => 
            sum + (agent.personality.extroversion || 50), 0) / participants.length;
        
        // 外向的Agent更容易继续对话
        const continueChance = Math.min(0.8, avgExtroversion / 100 + 0.3);
        
        return Math.random() < continueChance;
    }

    /**
     * 结束对话
     */
    endConversation(conversationId) {
        const conversation = this.activeConversations.get(conversationId);
        if (!conversation) return;

        conversation.status = 'ended';
        conversation.endTime = new Date();
        
        // 添加结束语
        const participants = conversation.participants;
        const lastSpeaker = conversation.messages[conversation.messages.length - 1].speaker;
        const otherParticipant = participants.find(id => id !== lastSpeaker);
        
        const farewells = ['再见！', '回头聊！', '拜拜！', '下次见！', '保重！'];
        const farewell = farewells[Math.floor(Math.random() * farewells.length)];
        
        conversation.messages.push({
            speaker: otherParticipant,
            content: farewell,
            timestamp: new Date(),
            type: 'farewell'
        });

        // 保存到历史记录
        for (const participantId of participants) {
            if (!this.dialogueHistory.has(participantId)) {
                this.dialogueHistory.set(participantId, []);
            }
            this.dialogueHistory.get(participantId).push(conversation);
        }

        // 从活跃对话中移除
        this.activeConversations.delete(conversationId);
        
        // 更新关系强度
        this.worldCore.socialNetwork.updateRelationship(
            participants[0], 
            participants[1], 
            'completed_conversation', 
            3
        );

        this.emit('dialogueEnded', conversation);
        this.logger.info(`💬 对话结束: ${conversationId}`);
    }

    /**
     * 处理关键需求对话
     */
    handleCriticalNeedDialogue(data) {
        const { agent, need } = data;
        const targetAgent = this.findSuitableConversationPartner(agent, 'help');
        
        if (targetAgent) {
            this.initiateDialogue(agent, targetAgent, 'critical_need', {
                need,
                urgency: 'high'
            });
        }
    }

    /**
     * 处理天气对话
     */
    handleWeatherDialogue(data) {
        // 天气变化时，可能触发相关对话
        const agents = this.worldCore.agentEcosystem.getActiveAgents();
        
        if (agents.length >= 2 && Math.random() < 0.3) { // 30%概率
            const agent1 = agents[Math.floor(Math.random() * agents.length)];
            const agent2 = agents[Math.floor(Math.random() * agents.length)];
            
            if (agent1.id !== agent2.id && this.areAgentsInSameLocation(agent1, agent2)) {
                this.initiateDialogue(agent1, agent2, 'weather_comment', {
                    weather: data.new,
                    oldWeather: data.old
                });
            }
        }
    }

    /**
     * 处理地点对话
     */
    handleLocationDialogue(data) {
        const { agentId, locationId, location } = data;
        
        // 如果地点有其他Agent，可能发起问候
        if (location.currentOccupants.length > 1 && Math.random() < 0.4) { // 40%概率
            const agent = this.worldCore.agentEcosystem.getAgent(agentId);
            const otherAgentId = location.currentOccupants.find(id => id !== agentId);
            const otherAgent = this.worldCore.agentEcosystem.getAgent(otherAgentId);
            
            if (agent && otherAgent && !this.isAgentInConversation(agentId) && !this.isAgentInConversation(otherAgentId)) {
                this.initiateDialogue(agent, otherAgent, 'location_greeting', {
                    location: location.name
                });
            }
        }
    }

    /**
     * 检查Agent是否在对话中
     */
    isAgentInConversation(agentId) {
        for (const conversation of this.activeConversations.values()) {
            if (conversation.participants.includes(agentId) && conversation.status === 'active') {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查两个Agent是否在同一地点
     */
    areAgentsInSameLocation(agent1, agent2) {
        return agent1.currentLocation === agent2.currentLocation;
    }

    /**
     * 获取Agent的对话历史
     */
    getDialogueHistory(agentId) {
        return this.dialogueHistory.get(agentId) || [];
    }

    /**
     * 获取活跃对话
     */
    getActiveConversations() {
        return Array.from(this.activeConversations.values());
    }

    /**
     * 销毁自主对话系统
     */
    async destroy() {
        await this.stop();
        this.removeAllListeners();
        this.logger.info('🗑️ 自主对话系统已销毁');
    }
}

module.exports = { AutonomousDialogue };
