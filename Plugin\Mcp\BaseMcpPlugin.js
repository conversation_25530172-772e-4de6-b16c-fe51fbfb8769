// Plugin/Mcp/BaseMcpPlugin.js - MCP插件基础类
const logger = require('../../utils/logger.cjs');
const path = require('path');

class BaseMcpPlugin {
    constructor() {
        this.name = '';
        this.description = '';
        this.vcpName = ''; // 对应的VCP插件名称
        this.enabled = true;
        this.config = {};
        this.pluginDir = ''; // 插件目录路径
    }

    // 初始化插件 (可在子类中重写)
    async initialize() {
        // 设置插件目录路径
        this.pluginDir = path.dirname(require.main.filename);
        
        // 加载配置文件（如果存在）
        try {
            const configPath = path.join(this.pluginDir, 'config.env');
            if (require('fs').existsSync(configPath)) {
                require('dotenv').config({ path: configPath });
            }
        } catch (error) {
            this.log('warning', `配置文件加载失败: ${error.message}`);
        }
        
        return true;
    }

    // 获取插件名称 (必须实现)
    getName() {
        return this.name;
    }

    // 获取插件描述 (必须实现)
    getDescription() {
        return this.description;
    }

    // 获取插件参数定义 (必须实现)
    getParameters() {
        return {
            type: 'object',
            properties: {},
            required: []
        };
    }

    // 执行插件 (必须实现)
    async execute(args) {
        throw new Error('execute方法必须被子类实现');
    }

    // 获取插件配置 (可选实现)
    getConfig() {
        return this.config;
    }

    // 设置插件配置 (可选实现)
    setConfig(config) {
        this.config = { ...this.config, ...config };
    }

    // 验证参数 (辅助方法)
    validateArgs(args, schema = this.getParameters()) {
        if (!args || typeof args !== 'object') {
            throw new Error('参数必须是一个对象');
        }

        // 验证必需参数
        if (schema.required) {
            for (const field of schema.required) {
                if (!(field in args)) {
                    throw new Error(`缺少必需参数: ${field}`);
                }
            }
        }

        // 验证参数类型
        if (schema.properties) {
            for (const [key, value] of Object.entries(args)) {
                const propSchema = schema.properties[key];
                if (propSchema) {
                    if (propSchema.type === 'string' && typeof value !== 'string') {
                        throw new Error(`参数 ${key} 必须是字符串类型`);
                    }
                    if (propSchema.type === 'number' && typeof value !== 'number') {
                        throw new Error(`参数 ${key} 必须是数字类型`);
                    }
                    if (propSchema.type === 'boolean' && typeof value !== 'boolean') {
                        throw new Error(`参数 ${key} 必须是布尔类型`);
                    }
                    if (propSchema.type === 'array' && !Array.isArray(value)) {
                        throw new Error(`参数 ${key} 必须是数组类型`);
                    }
                }
            }
        }
    }

    // 记录日志 (辅助方法)
    log(level, message, data = null) {
        const logPrefix = `MCP-${this.name}`;
        if (data) {
            logger[level](logPrefix, message, data);
        } else {
            logger[level](logPrefix, message);
        }
    }

    // 调用对应的VCP插件 (辅助方法)
    async callVcpPlugin(args) {
        if (!this.vcpName) {
            throw new Error('未配置对应的VCP插件名称');
        }
        
        // 从全局获取VCP插件管理器
        const pluginManager = global.pluginManager;
        if (!pluginManager) {
            throw new Error('VCP插件管理器未初始化');
        }

        try {
            this.log('info', `调用VCP插件: ${this.vcpName}`, args);
            
            // 如果args是对象，转换为JSON字符串
            const vcpArgs = typeof args === 'object' ? JSON.stringify(args) : args;
            
            const result = await pluginManager.executePlugin(this.vcpName, vcpArgs);
            
            // 尝试解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }
            
            this.log('success', `VCP插件 ${this.vcpName} 执行完成`, parsedResult);
            return parsedResult;
            
        } catch (error) {
            this.log('error', `VCP插件 ${this.vcpName} 执行失败: ${error.message}`);
            throw error;
        }
    }

    // 清理资源 (可在子类中重写)
    async cleanup() {
        // 默认实现为空
        return true;
    }
}

module.exports = BaseMcpPlugin; 