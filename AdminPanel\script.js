// script.js

// 工具函数：计算时间差（支持北京时间）
function getTimeAgo(date) {
    // 获取当前时间（本地时间）
    const now = new Date();

    // 解析传入的时间戳
    let targetDate;
    if (typeof date === 'string') {
        // 直接解析时间戳，JavaScript会自动处理时区
        targetDate = new Date(date);
    } else {
        targetDate = new Date(date);
    }

    // 计算时间差（毫秒）
    const diffInMs = now - targetDate;
    const diffInSeconds = Math.floor(diffInMs / 1000);

    // 如果时间差为负数，说明是未来时间，返回"刚刚"
    if (diffInSeconds < 0) {
        return '刚刚';
    }

    if (diffInSeconds < 60) {
        return `${diffInSeconds}秒前`;
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes}分钟前`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours}小时前`;
    } else if (diffInSeconds < 2592000) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days}天前`;
    } else if (diffInSeconds < 31536000) {
        const months = Math.floor(diffInSeconds / 2592000);
        return `${months}个月前`;
    } else {
        const years = Math.floor(diffInSeconds / 31536000);
        return `${years}年前`;
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const pluginNavList = document.getElementById('plugin-nav').querySelector('ul');
    const configDetailsContainer = document.getElementById('config-details-container');
    const baseConfigForm = document.getElementById('base-config-form');
    const loadingOverlay = document.getElementById('loading-overlay');
    const messagePopup = document.getElementById('message-popup');
    const restartServerButton = document.getElementById('restart-server-button');

    // Daily Notes Manager Elements
    const dailyNotesSection = document.getElementById('daily-notes-manager-section'); // The main section for daily notes
    const notesFolderListUl = document.getElementById('notes-folder-list');
    const notesListViewDiv = document.getElementById('notes-list-view');
    const noteEditorAreaDiv = document.getElementById('note-editor-area');
    const editingNoteFolderInput = document.getElementById('editing-note-folder');
    const editingNoteFileInput = document.getElementById('editing-note-file');
    const noteContentEditorTextarea = document.getElementById('note-content-editor');
    const saveNoteButton = document.getElementById('save-note-content');
    const cancelEditNoteButton = document.getElementById('cancel-edit-note');
    const noteEditorStatusSpan = document.getElementById('note-editor-status');
    const moveSelectedNotesButton = document.getElementById('move-selected-notes');
    const moveTargetFolderSelect = document.getElementById('move-target-folder');
    const deleteSelectedNotesButton = document.getElementById('delete-selected-notes-button'); // 新增：批量删除按钮
    const notesActionStatusSpan = document.getElementById('notes-action-status');
    const searchDailyNotesInput = document.getElementById('search-daily-notes'); // 新增：搜索框

    // Agent Files Editor Elements
    const agentFileSelect = document.getElementById('agent-file-select');
    const agentFileContentEditor = document.getElementById('agent-file-content-editor');
    const saveAgentFileButton = document.getElementById('save-agent-file-button');
    const agentFileStatusSpan = document.getElementById('agent-file-status');

    // Server Log Viewer Elements
    const serverLogViewerSection = document.getElementById('server-log-viewer-section');
    const copyServerLogButton = document.getElementById('copy-server-log-button'); // Changed from refreshServerLogButton
    const serverLogPathDisplay = document.getElementById('server-log-path-display');
    const serverLogStatusSpan = document.getElementById('server-log-status');
    const serverLogContentPre = document.getElementById('server-log-content');
    let serverLogIntervalId = null; // For server log auto-refresh


    const API_BASE_URL = '/admin_api'; // Corrected API base path
    
    // 暴露API_BASE_URL到全局作用域
    window.API_BASE_URL = API_BASE_URL;

    // --- Utility Functions ---
    function showLoading(show) {
        if (show) {
            loadingOverlay.classList.remove('hidden');
        } else {
            loadingOverlay.classList.add('hidden');
        }
    }

    // 暴露showLoading到全局作用域
    window.showLoading = showLoading;

    // 暴露showMessage到全局作用域
    window.showMessage = function showMessage(message, type = 'info', duration = 3500) {
        // 清除可能存在的定时器
        if (messagePopup.hideTimer) {
            clearTimeout(messagePopup.hideTimer);
        }
        
        messagePopup.textContent = message;
        messagePopup.className = 'fixed top-4 right-4 z-50 p-4 rounded-xl shadow-lg transform transition-transform duration-300 ease-in-out';
        
        // 根据类型设置颜色
        switch(type) {
            case 'success':
                messagePopup.className += ' bg-gradient-to-r from-green-500 to-green-600 text-white';
                break;
            case 'error':
                messagePopup.className += ' bg-gradient-to-r from-red-500 to-red-600 text-white';
                break;
            case 'warning':
                messagePopup.className += ' bg-gradient-to-r from-orange-500 to-orange-600 text-white';
                break;
            default:
                messagePopup.className += ' bg-gradient-to-r from-blue-500 to-blue-600 text-white';
        }
        
        // 显示消息
        messagePopup.classList.remove('translate-x-full');
        messagePopup.classList.add('translate-x-0');
        
                // 设置自动隐藏定时器
        messagePopup.hideTimer = setTimeout(() => {
            messagePopup.classList.add('translate-x-full');
            messagePopup.classList.remove('translate-x-0');
            // 完全隐藏后清除内容和定时器
        setTimeout(() => {
                messagePopup.textContent = '';
                messagePopup.className = 'fixed top-4 right-4 z-50 p-4 rounded-xl shadow-lg transform transition-transform duration-300 ease-in-out translate-x-full';
                messagePopup.hideTimer = null;
            }, 300);
        }, duration);
    }

    // 暴露apiFetch到全局作用域
    window.apiFetch = async function apiFetch(url, options = {}) {
        // 只有非GET请求或明确要求显示loading才显示
        const shouldShowLoading = options.showLoading !== false && (options.method && options.method !== 'GET');
        
        if (shouldShowLoading) {
        showLoading(true);
        }
        
        try {
            const defaultHeaders = {
                'Content-Type': 'application/json',
            };
            options.headers = { ...defaultHeaders, ...options.headers };

            const response = await fetch(url, options); // url is already API_BASE_URL + path
            if (!response.ok) {
                let errorData = { error: `HTTP error ${response.status}`, details: response.statusText };
                try {
                    const jsonError = await response.json();
                    errorData = { ...errorData, ...jsonError };
                } catch (e) { /* Ignore if response is not JSON */ }
                throw new Error(errorData.message || errorData.error || errorData.details || `HTTP error ${response.status}`);
            }
            const contentType = response.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('API Fetch Error:', error.message, error);
            showMessage(`操作失败: ${error.message}`, 'error');
            throw error;
        } finally {
            if (shouldShowLoading) {
            showLoading(false);
            }
        }
    }

    // --- .env Parsing and Building (Adapted from user's original script) ---
    function parseEnvToList(content) {
        const lines = content.split(/\r?\n/);
        const entries = [];
        let i = 0;
        while (i < lines.length) {
            const line = lines[i];
            const trimmedLine = line.trim();
            const currentLineNum = i;

            if (trimmedLine.startsWith('#') || trimmedLine === '') {
                entries.push({
                    key: null,
                    value: line, // For comments/empty, value holds the full line
                    isCommentOrEmpty: true,
                    isMultilineQuoted: false,
                    originalLineNumStart: currentLineNum,
                    originalLineNumEnd: currentLineNum
                });
                i++;
                continue;
            }

            const eqIndex = line.indexOf('=');
            if (eqIndex === -1) {
                entries.push({ key: null, value: line, isCommentOrEmpty: true, note: 'Malformed line (no equals sign)', originalLineNumStart: currentLineNum, originalLineNumEnd: currentLineNum });
                i++;
                continue;
            }

            const key = line.substring(0, eqIndex).trim();
            let valueString = line.substring(eqIndex + 1);

            if (valueString.trim().startsWith("'")) {
                let accumulatedValue;
                let firstLineContent = valueString.substring(valueString.indexOf("'") + 1);

                if (firstLineContent.endsWith("'") && !lines.slice(i + 1).some(l => l.trim().endsWith("'") && !l.trim().startsWith("'") && l.includes("='"))) {
                    accumulatedValue = firstLineContent.substring(0, firstLineContent.length - 1);
                    entries.push({ key, value: accumulatedValue, isCommentOrEmpty: false, isMultilineQuoted: true, originalLineNumStart: currentLineNum, originalLineNumEnd: i });
                } else {
                    let multilineContent = [firstLineContent];
                    let endLineNum = i;
                    i++;
                    while (i < lines.length) {
                        const nextLine = lines[i];
                        multilineContent.push(nextLine);
                        endLineNum = i;
                        if (nextLine.trim().endsWith("'")) {
                            let lastContentLine = multilineContent.pop();
                            multilineContent.push(lastContentLine.substring(0, lastContentLine.lastIndexOf("'")));
                            break;
                        }
                        i++;
                    }
                    accumulatedValue = multilineContent.join('\n');
                    entries.push({ key, value: accumulatedValue, isCommentOrEmpty: false, isMultilineQuoted: true, originalLineNumStart: currentLineNum, originalLineNumEnd: endLineNum });
                }
            } else {
                entries.push({ key, value: valueString.trim(), isCommentOrEmpty: false, isMultilineQuoted: false, originalLineNumStart: currentLineNum, originalLineNumEnd: currentLineNum });
            }
            i++;
        }
        return entries;
    }

    function buildEnvString(formElement, originalParsedEntries) {
        const newEnvLines = [];
        const editedKeys = new Set();

        // Iterate through form elements to get edited values
        for (let i = 0; i < formElement.elements.length; i++) {
            const element = formElement.elements[i];
            if (element.name && element.dataset.originalKey) { // Ensure it's an editable field
                const key = element.dataset.originalKey;
                editedKeys.add(key);
                let value = element.value;
                const originalEntry = originalParsedEntries.find(entry => entry.key === key);
                let isMultiline = (originalEntry && originalEntry.isMultilineQuoted) || value.includes('\n');
                
                if (element.type === 'checkbox' && element.dataset.expectedType === 'boolean') {
                     value = element.checked ? 'true' : 'false';
                     isMultiline = false; // Booleans are not multiline
                } else if (element.dataset.expectedType === 'integer') {
                    const intVal = parseInt(value, 10);
                    value = isNaN(intVal) ? (value === '' ? '' : value) : String(intVal);
                    isMultiline = false; // Integers are not multiline
                }


                if (isMultiline) {
                    newEnvLines.push(`${key}='${value}'`);
                } else {
                    newEnvLines.push(`${key}=${value}`);
                }
            }
        }
        
        // Reconstruct with original comments, empty lines, and unedited/newly added custom fields
        const finalLines = [];
        const formElementsMap = new Map();
        Array.from(baseConfigForm.querySelectorAll('[data-original-key], [data-is-comment-or-empty="true"]')).forEach(el => {
            if (el.dataset.originalKey) formElementsMap.set(el.dataset.originalKey, el);
            else if (el.dataset.originalContent) formElementsMap.set(`comment-${finalLines.length}`, el); // Unique key for comments
        });


        originalParsedEntries.forEach(entry => {
            if (entry.isCommentOrEmpty) {
                finalLines.push(entry.value); // Push original comment or empty line
            } else {
                const inputElement = formElementsMap.get(entry.key);
                if (inputElement && inputElement.closest('form') === baseConfigForm) { // Check if element is part of the current form
                    let value = inputElement.value;
                     if (inputElement.type === 'checkbox' && inputElement.dataset.expectedType === 'boolean') {
                        value = inputElement.checked ? 'true' : 'false';
                    } else if (inputElement.dataset.expectedType === 'integer') {
                        const intVal = parseInt(value, 10);
                        value = isNaN(intVal) ? (value === '' ? '' : value) : String(intVal);
                    }

                    const isMultiline = entry.isMultilineQuoted || value.includes('\n');
                    if (isMultiline) {
                        finalLines.push(`${entry.key}='${value}'`);
                    } else {
                        finalLines.push(`${entry.key}=${value}`);
                    }
                } else {
                    // Key was in original but not in UI (e.g. filtered out, or error)
                    // Fallback to original representation
                    if (entry.isMultilineQuoted) {
                        finalLines.push(`${entry.key}='${entry.value}'`);
                    } else {
                        finalLines.push(`${entry.key}=${entry.value}`);
                    }
                }
            }
        });
        
        // Add any new custom fields from the form that were not in originalParsedEntries
        // This part is more relevant for plugin configs with "add custom field"
        // For base config, we generally edit existing or rely on server to add new ones if needed.

        return finalLines.join('\n');
    }


    // --- Load Initial Data ---
    async function loadInitialData() {
        try {
            await loadBaseConfig();
            await loadPluginList();
            const firstLink = pluginNavList.querySelector('a');
            if (firstLink) {
                // Ensure the correct section ID is used if it's different from data-target
                const sectionId = firstLink.dataset.target.endsWith('-section') ? firstLink.dataset.target : `${firstLink.dataset.target}-section`;
                navigateTo(sectionId, firstLink.dataset.pluginName, firstLink.dataset.target);
                firstLink.classList.add('active');
            }
        } catch (error) { /* Error already shown by apiFetch */ }
    }

    // --- Base Configuration ---
    let originalBaseConfigEntries = []; // Store parsed entries for saving

    async function loadBaseConfig() {
        try {
            const data = await apiFetch(`${API_BASE_URL}/config/main`); // Use correct endpoint
            originalBaseConfigEntries = parseEnvToList(data.content);
            
            // Load current environment values for better display
            let currentEnvValues = {};
            try {
                currentEnvValues = await apiFetch(`${API_BASE_URL}/env-values`);
            } catch (error) {
                console.warn('Could not load current environment values for base config:', error);
            }
            
            baseConfigForm.innerHTML = ''; // Clear previous form

            originalBaseConfigEntries.forEach((entry, index) => {
                let formGroup;
                if (entry.isCommentOrEmpty) {
                    formGroup = createCommentOrEmptyElement(entry.value, index);
                } else {
                    let inferredType = 'string';
                    if (typeof entry.value === 'boolean' || /^(true|false)$/i.test(entry.value)) inferredType = 'boolean';
                    else if (!isNaN(parseFloat(entry.value)) && isFinite(entry.value) && !entry.value.includes('.')) inferredType = 'integer';
                    
                    // Enhanced description with current status
                    let description = `根目录 config.env 配置项: ${entry.key}`;
                    if (currentEnvValues[entry.key]) {
                        const envData = currentEnvValues[entry.key];
                        if (envData.status === 'configured') {
                            description += ` <span class="defined-in">(当前值: ${envData.display})</span>`;
                        }
                    }
                    
                    formGroup = createFormGroup(
                        entry.key,
                        entry.value,
                        inferredType,
                        description,
                        false, // isPluginConfig
                        null,  // pluginName
                        false, // isCustomDeletableField
                        entry.isMultilineQuoted // Pass multiline info
                    );
                }
                baseConfigForm.appendChild(formGroup);
            });

            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'flex items-center justify-center mt-8 pt-6 border-t border-gray-200';
            const submitButton = document.createElement('button');
            submitButton.type = 'submit';
            submitButton.className = 'save-config-btn';
            submitButton.innerHTML = `
                <i class="fas fa-save mr-2"></i>
                保存全局配置
            `;
            actionsDiv.appendChild(submitButton);
            baseConfigForm.appendChild(actionsDiv);
        } catch (error) {
            baseConfigForm.innerHTML = `<p class="error-message">加载全局配置失败: ${error.message}</p>`;
        }
    }

    baseConfigForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        const newConfigString = buildEnvString(baseConfigForm, originalBaseConfigEntries);
        try {
            await apiFetch(`${API_BASE_URL}/config/main`, { // Use correct endpoint
                method: 'POST',
                body: JSON.stringify({ content: newConfigString })
            });
            showMessage('全局配置已保存！部分更改可能需要重启服务生效。', 'success');
            loadBaseConfig(); // Reload to reflect changes and ensure consistency
        } catch (error) { /* Error handled by apiFetch */ }
    });


    // --- Plugin Configuration ---
    let originalPluginConfigs = {}; // Store original parsed entries for each plugin

    async function loadPluginList() {
        try {
            const plugins = await apiFetch(`${API_BASE_URL}/plugins`);
            // Clear existing DYNAMIC plugin nav items
            const dynamicNavItems = pluginNavList.querySelectorAll('li.dynamic-plugin-nav-item');
            dynamicNavItems.forEach(item => item.remove());
            // Clear existing DYNAMIC plugin sections
            const dynamicPluginSections = configDetailsContainer.querySelectorAll('section.dynamic-plugin-section');
            dynamicPluginSections.forEach(sec => sec.remove());

            plugins.sort((a, b) => (a.manifest.displayName || a.manifest.name).localeCompare(b.manifest.displayName || b.manifest.name));

            plugins.forEach(plugin => {
                const li = document.createElement('li');
                li.classList.add('dynamic-plugin-nav-item'); // Add class for dynamic items
                const a = document.createElement('a');
                a.href = '#';
                a.className = 'flex items-center justify-between p-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-anime-green hover:to-anime-blue hover:text-white transition-all duration-200 group animate-slide-in';
                
                const nameContainer = document.createElement('div');
                nameContainer.className = 'flex items-center space-x-2';
                
                // 插件图标
                const pluginIcon = document.createElement('svg');
                pluginIcon.className = 'w-4 h-4 group-hover:animate-pulse-slow';
                pluginIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1v1z"/>';
                pluginIcon.setAttribute('fill', 'none');
                pluginIcon.setAttribute('stroke', 'currentColor');
                pluginIcon.setAttribute('viewBox', '0 0 24 24');
                
                const nameSpan = document.createElement('span');
                nameSpan.className = 'text-sm font-medium';
                nameSpan.textContent = plugin.manifest.displayName || plugin.manifest.name;
                
                nameContainer.appendChild(pluginIcon);
                nameContainer.appendChild(nameSpan);
                
                const statusContainer = document.createElement('div');
                statusContainer.className = 'flex items-center space-x-1';
                
                // 状态徽章
                if (!plugin.enabled) {
                    const disabledBadge = document.createElement('span');
                    disabledBadge.className = 'inline-block w-2 h-2 bg-red-400 rounded-full';
                    disabledBadge.title = '插件已禁用';
                    statusContainer.appendChild(disabledBadge);
                } else {
                    const enabledBadge = document.createElement('span');
                    enabledBadge.className = 'inline-block w-2 h-2 bg-green-400 rounded-full animate-pulse-slow';
                    enabledBadge.title = '插件已启用';
                    statusContainer.appendChild(enabledBadge);
                }
                
                // 分布式插件图标
                if (plugin.isDistributed) {
                    const cloudIcon = document.createElement('span');
                    cloudIcon.className = 'text-xs';
                    cloudIcon.textContent = '☁️';
                    cloudIcon.title = `分布式插件 (来自: ${plugin.serverId || '未知'})`;
                    statusContainer.appendChild(cloudIcon);
                }
                
                a.appendChild(nameContainer);
                a.appendChild(statusContainer);
                a.dataset.target = `plugin-${plugin.manifest.name}-config`;
                a.dataset.pluginName = plugin.manifest.name;
                li.appendChild(a);
                
                // 添加到插件列表容器而不是pluginNavList
                const pluginListContainer = document.getElementById('plugin-list');
                if (pluginListContainer) {
                    pluginListContainer.appendChild(li);
                } else {
                    // 兜底方案
                pluginNavList.appendChild(li);
                }

                const pluginSection = document.createElement('section');
                pluginSection.id = `plugin-${plugin.manifest.name}-config-section`;
                pluginSection.classList.add('config-section', 'dynamic-plugin-section', 'flex', 'flex-col', 'h-full');
                pluginSection.style.display = 'none';
                
                let descriptionHtml = plugin.manifest.description || '暂无描述';
                if (plugin.manifest.version) descriptionHtml += ` (版本: ${plugin.manifest.version})`;
                if (plugin.isDistributed) descriptionHtml += ` (来自节点: ${plugin.serverId || '未知'})`;
                
                // 创建插件头部
                const headerDiv = document.createElement('div');
                headerDiv.className = 'bg-gradient-to-r from-emerald-600 to-blue-600 p-6 text-white';
                headerDiv.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1v1z"/>
                            </svg>
                            <div>
                                <h2 class="text-2xl font-bold">${plugin.manifest.displayName || plugin.manifest.name} 配置</h2>
                                <p class="text-white text-opacity-80">${descriptionHtml}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            ${!plugin.enabled ? '<span class="px-3 py-1 bg-red-500 bg-opacity-20 text-red-100 rounded-full text-sm">已禁用</span>' : '<span class="px-3 py-1 bg-green-500 bg-opacity-20 text-green-100 rounded-full text-sm">已启用</span>'}
                            ${plugin.isDistributed ? '<span class="px-3 py-1 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm">☁️ 分布式</span>' : ''}
                        </div>
                    </div>
                `;

                // Add a control area for plugin actions like toggle
                const pluginControlsDiv = document.createElement('div');
                pluginControlsDiv.className = 'p-4 bg-white border-b border-gray-200';

                const toggleButton = document.createElement('button');
                toggleButton.id = `toggle-plugin-${plugin.manifest.name}-button`;
                toggleButton.textContent = plugin.enabled ? '禁用插件' : '启用插件';
                toggleButton.classList.add('toggle-plugin-button');
                if (!plugin.enabled) {
                    toggleButton.classList.add('disabled-state');
                }

                // 禁用分布式插件的管理功能
                if (plugin.isDistributed) {
                    toggleButton.disabled = true;
                    toggleButton.title = '分布式插件的状态由其所在的节点管理，无法在此处直接启停。';
                }

                toggleButton.addEventListener('click', async () => {
                    const currentEnabledState = !toggleButton.classList.contains('disabled-state'); // Determine current state from class
                    const enable = !currentEnabledState; // Target state is the opposite

                    // Optional: Confirm action
                    if (!confirm(`确定要${enable ? '启用' : '禁用'}插件 "${plugin.manifest.displayName || plugin.manifest.name}" 吗？更改可能需要重启服务才能生效。`)) {
                        return;
                    }

                    toggleButton.disabled = true; // Disable button during operation
                    toggleButton.textContent = enable ? '正在启用...' : '正在禁用...';

                    try {
                        const result = await apiFetch(`${API_BASE_URL}/plugins/${plugin.manifest.name}/toggle`, {
                            method: 'POST',
                            body: JSON.stringify({ enable: enable })
                        });
                        showMessage(result.message, 'success');

                        // Refresh the plugin list and the current plugin's config
                        loadPluginList(); // Refresh sidebar status
                        loadPluginConfig(plugin.manifest.name); // Refresh current section

                    } catch (error) {
                         // apiFetch already shows error message
                         console.error(`Failed to toggle plugin ${plugin.manifest.name}:`, error);
                         // Restore button state on error
                         toggleButton.disabled = false;
                         toggleButton.textContent = currentEnabledState ? '禁用插件' : '启用插件'; // Revert text
                         if (!currentEnabledState) {
                             toggleButton.classList.add('disabled-state');
                         } else {
                             toggleButton.classList.remove('disabled-state');
                         }
                    }
                });

                pluginControlsDiv.appendChild(toggleButton);
                
                // 创建表单容器
                const configFormDiv = document.createElement('div');
                configFormDiv.id = `plugin-${plugin.manifest.name}-config-form-container`;
                configFormDiv.className = 'flex-1 bg-gray-50 flex flex-col overflow-hidden';

                const scrollableDiv = document.createElement('div');
                scrollableDiv.className = 'flex-1 overflow-y-auto p-6 main-content-scroll';

                const form = document.createElement('form');
                form.id = `plugin-${plugin.manifest.name}-config-form`;
                form.className = 'space-y-4 max-w-none';
                
                scrollableDiv.appendChild(form);
                configFormDiv.appendChild(scrollableDiv);
                
                // 组装插件区域
                pluginSection.appendChild(headerDiv);
                pluginSection.appendChild(pluginControlsDiv);
                pluginSection.appendChild(configFormDiv);
                
                configDetailsContainer.appendChild(pluginSection);

                // Store original config if available (for saving later)
                if (plugin.configEnvContent) {
                    originalPluginConfigs[plugin.manifest.name] = parseEnvToList(plugin.configEnvContent);
                } else {
                    originalPluginConfigs[plugin.manifest.name] = []; // Empty if no config.env
                }
            });
        } catch (error) {
            pluginNavList.innerHTML += `<li><p class="error-message">加载插件列表失败: ${error.message}</p></li>`;
        }
    }

    async function loadPluginConfig(pluginName) {
        const form = document.getElementById(`plugin-${pluginName}-config-form`);
        if (!form) {
            console.error(`Form not found for plugin ${pluginName}`);
            return;
        }
        form.innerHTML = ''; // Clear previous form content

        try {
            // Fetch fresh plugin details, including manifest and config.env content
            // The /api/plugins endpoint in server.js already provides this,
            // but if we need more detailed schema vs custom, we might need a specific endpoint
            // For now, let's assume we use the initially loaded originalPluginConfigs[pluginName]
            
            const pluginData = (await apiFetch(`${API_BASE_URL}/plugins`)).find(p => p.manifest.name === pluginName);
            if (!pluginData) {
                throw new Error(`Plugin data for ${pluginName} not found.`);
            }
            
            const manifest = pluginData.manifest;
            const configEnvContent = pluginData.configEnvContent || "";
            originalPluginConfigs[pluginName] = parseEnvToList(configEnvContent);

            const schemaFieldsContainer = document.createElement('div');
            const customFieldsContainer = document.createElement('div');
            let hasSchemaFields = false;
            let hasCustomFields = false;

            const configSchema = manifest.configSchema || {};
            const presentInEnv = new Set(originalPluginConfigs[pluginName].filter(e => !e.isCommentOrEmpty).map(e => e.key));
            
            // Load current environment values from server
            let currentEnvValues = {};
            try {
                const envResponse = await apiFetch(`${API_BASE_URL}/env-values`);
                currentEnvValues = envResponse || {};
            } catch (error) {
                console.warn('Could not load current environment values:', error);
            }

            // Display schema-defined fields first
            for (const key in configSchema) {
                hasSchemaFields = true;
                const schemaConfig = configSchema[key];
                const expectedType = (typeof schemaConfig === 'object') ? schemaConfig.type : schemaConfig;
                const isRequired = (typeof schemaConfig === 'object') ? schemaConfig.required : false;
                const schemaDescription = (typeof schemaConfig === 'object') ? schemaConfig.description : '';
                const entry = originalPluginConfigs[pluginName].find(e => e.key === key && !e.isCommentOrEmpty);
                
                // Determine current value - priority: plugin config > global env > defaults > empty
                let value = '';
                let valueSource = '';
                let displayValue = '';
                let statusBadge = '';
                
                if (entry) {
                    value = entry.value;
                    displayValue = entry.value;
                    valueSource = '当前在插件 .env 中定义';
                    statusBadge = '<span class="config-status-badge configured">已配置</span>';
                } else if (currentEnvValues[key] && currentEnvValues[key].status === 'configured') {
                    const envData = currentEnvValues[key];
                    // 对于继承的配置，我们显示实际值（除非是敏感字段）
                    value = envData.isSensitive ? envData.value : envData.value; // 显示完整值，让用户可以编辑
                    displayValue = envData.display;
                    valueSource = `继承自${envData.source === 'process.env' ? '系统环境变量' : '全局配置文件'}`;
                    statusBadge = '<span class="config-status-badge inherited">已继承</span>';
                } else if (schemaConfig && schemaConfig.default !== undefined) {
                    value = schemaConfig.default;
                    displayValue = schemaConfig.default;
                    valueSource = '使用插件默认值';
                    statusBadge = '<span class="config-status-badge default">默认值</span>';
                } else {
                    valueSource = isRequired ? '必需配置 - 未设置' : '可选配置 - 未设置';
                    statusBadge = isRequired ? '<span class="config-status-badge required">必需</span>' : '<span class="config-status-badge empty">未设置</span>';
                }
                
                const isMultiline = entry ? entry.isMultilineQuoted : (String(value).includes('\n'));
                
                let descriptionHtml = '';
                if (schemaDescription) {
                    descriptionHtml = `<strong>${schemaDescription}</strong><br/>`;
                }
                descriptionHtml += `<div class="flex items-center justify-between mt-2">`;
                descriptionHtml += `<span class="text-xs text-gray-500">🏷️ ${valueSource}</span>`;
                descriptionHtml += statusBadge;
                descriptionHtml += `</div>`;
                
                if (displayValue && displayValue !== value) {
                    descriptionHtml += `<div class="mt-1 text-xs text-blue-600">💡 当前显示值: ${displayValue}</div>`;
                }

                const formGroup = createFormGroup(key, value, expectedType, descriptionHtml, true, pluginName, false, isMultiline);
                schemaFieldsContainer.appendChild(formGroup);
                presentInEnv.delete(key); // Remove from set as it's handled
            }

            // Display remaining .env fields (custom or not in schema) and comments/empty lines
            originalPluginConfigs[pluginName].forEach((entry, index) => {
                if (entry.isCommentOrEmpty) {
                    customFieldsContainer.appendChild(createCommentOrEmptyElement(entry.value, `${pluginName}-comment-${index}`));
                } else if (presentInEnv.has(entry.key)) { // Custom field (was in .env but not in schema)
                    hasCustomFields = true;
                    const descriptionHtml = `自定义配置项: ${entry.key} <span class="defined-in">(当前在插件 .env 中定义)</span>`;
                    const formGroup = createFormGroup(entry.key, entry.value, 'string', descriptionHtml, true, pluginName, true, entry.isMultilineQuoted);
                    customFieldsContainer.appendChild(formGroup);
                }
            });


            if (hasSchemaFields) {
                const schemaTitle = document.createElement('h3');
                schemaTitle.className = 'text-xl font-bold text-anime-dark mb-6 flex items-center';
                schemaTitle.innerHTML = `
                    <svg class="w-6 h-6 mr-2 text-anime-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Schema 定义的配置
                `;
                form.appendChild(schemaTitle);
                form.appendChild(schemaFieldsContainer);
            }
            if (hasCustomFields || originalPluginConfigs[pluginName].some(e => e.isCommentOrEmpty)) {
                const customTitle = document.createElement('h3');
                customTitle.className = 'text-xl font-bold text-anime-dark mb-6 mt-8 flex items-center';
                customTitle.innerHTML = `
                    <svg class="w-6 h-6 mr-2 text-anime-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                    </svg>
                    自定义 .env 配置项
                `;
                form.appendChild(customTitle);
                form.appendChild(customFieldsContainer);
            }

            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'form-actions';

            const addConfigButton = document.createElement('button');
            addConfigButton.type = 'button';
            addConfigButton.className = 'add-config-btn';
            addConfigButton.innerHTML = `
                <i class="fas fa-plus mr-2"></i>
                添加自定义配置项
            `;
            addConfigButton.addEventListener('click', () => addCustomConfigFieldToPluginForm(form, pluginName, customFieldsContainer, originalPluginConfigs[pluginName]));
            actionsDiv.appendChild(addConfigButton);

            const submitButton = document.createElement('button');
            submitButton.type = 'submit';
            submitButton.className = 'save-config-btn';
            submitButton.innerHTML = `
                <i class="fas fa-save mr-2"></i>
                保存 ${pluginName} 配置
            `;
            actionsDiv.appendChild(submitButton);
            form.appendChild(actionsDiv);

            form.removeEventListener('submit', handlePluginFormSubmit);
            form.addEventListener('submit', handlePluginFormSubmit);

            // --- Add Invocation Commands Editor ---
            if (manifest.capabilities && manifest.capabilities.invocationCommands && manifest.capabilities.invocationCommands.length > 0) {
                const commandsSection = document.createElement('div');
                commandsSection.className = 'invocation-commands-section';
                const commandsTitle = document.createElement('h3');
                    commandsTitle.className = 'section-title';
                commandsTitle.textContent = '调用命令 AI 指令编辑';
                commandsSection.appendChild(commandsTitle);

                manifest.capabilities.invocationCommands.forEach(cmd => {
                    const commandIdentifier = cmd.commandIdentifier || cmd.command; // Use commandIdentifier or fallback to command
                    if (!commandIdentifier) return; // Skip if no identifier

                    const commandItem = document.createElement('div');
                    commandItem.className = 'command-item';
                    commandItem.dataset.commandIdentifier = commandIdentifier;

                    const commandHeader = document.createElement('h4');
                    commandHeader.className = 'subsection-title';
                    commandHeader.textContent = `命令: ${commandIdentifier}`;
                    commandItem.appendChild(commandHeader);

                    const cmdFormGroup = document.createElement('div');
                    cmdFormGroup.className = 'command-description-container'; // 使用新的容器样式

                    const descLabel = document.createElement('label');
                    const descTextareaId = `cmd-desc-${pluginName}-${commandIdentifier.replace(/\s+/g, '_')}`;
                    descLabel.htmlFor = descTextareaId;
                    descLabel.className = 'command-description-label';
                    descLabel.textContent = '指令描述 (AI Instructions):';
                    cmdFormGroup.appendChild(descLabel);

                    const descTextarea = document.createElement('textarea');
                    descTextarea.id = descTextareaId;
                    descTextarea.className = 'command-description-edit w-full';
                    descTextarea.rows = Math.max(8, (cmd.description || '').split('\n').length + 3); // 增加默认行数
                    descTextarea.value = cmd.description || '';
                    descTextarea.placeholder = '请输入该命令的详细描述，这将帮助AI更好地理解和执行命令...';
                    cmdFormGroup.appendChild(descTextarea);
                    
                    const cmdActionsDiv = document.createElement('div');
                    cmdActionsDiv.className = 'form-actions'; // Reuse form-actions styling for consistency

                    const saveCmdDescButton = document.createElement('button');
                    saveCmdDescButton.type = 'button';
                    saveCmdDescButton.className = 'save-config-btn';
                    saveCmdDescButton.innerHTML = `
                        <i class="fas fa-save mr-2"></i>
                        保存此指令描述
                    `;
                    
                    const cmdStatusP = document.createElement('p');
                    cmdStatusP.className = 'status command-status'; // For feedback

                    saveCmdDescButton.addEventListener('click', async () => {
                        await saveInvocationCommandDescription(pluginName, commandIdentifier, descTextarea, cmdStatusP);
                    });
                    cmdActionsDiv.appendChild(saveCmdDescButton);
                    cmdFormGroup.appendChild(cmdActionsDiv);
                    cmdFormGroup.appendChild(cmdStatusP);
                    commandItem.appendChild(cmdFormGroup);
                    commandsSection.appendChild(commandItem);
                });
                // Append commands section after the main plugin config form's content, but before its actions
                const pluginFormActions = form.querySelector('.form-actions');
                if (pluginFormActions) {
                    form.insertBefore(commandsSection, pluginFormActions);
                } else {
                    form.appendChild(commandsSection);
                }
            }

        } catch (error) {
            form.innerHTML = `<p class="error-message">加载插件 ${pluginName} 配置失败: ${error.message}</p>`;
        }
    }

    async function saveInvocationCommandDescription(pluginName, commandIdentifier, textareaElement, statusElement) {
        const newDescription = textareaElement.value;
        statusElement.textContent = '正在保存描述...';
        statusElement.className = 'status command-status info'; // Reset and indicate processing

        const apiUrl = `${API_BASE_URL}/plugins/${pluginName}/commands/${commandIdentifier}/description`;
        // Log the values before making the API call
        console.log(`[saveInvocationCommandDescription] Attempting to save:
Plugin Name: ${pluginName}
Command Identifier: ${commandIdentifier}
API URL: ${apiUrl}
Description Length: ${newDescription.length}`);

        if (!pluginName || !commandIdentifier) {
            const errorMsg = `保存描述失败: 插件名称或命令标识符为空。Plugin: '${pluginName}', Command: '${commandIdentifier}'`;
            console.error(errorMsg);
            showMessage(errorMsg, 'error');
            statusElement.textContent = '保存失败: 内部错误 (缺少标识符)';
            statusElement.className = 'status command-status error';
            return;
        }

        try {
            await apiFetch(apiUrl, {
                method: 'POST',
                body: JSON.stringify({ description: newDescription })
            });
            showMessage(`指令 "${commandIdentifier}" 的描述已成功保存!`, 'success');
            statusElement.textContent = '描述已保存!';
            statusElement.classList.remove('info', 'error');
            statusElement.classList.add('success');

            // Optionally, update the manifest in memory if needed, or rely on next full load
            // For now, we assume a full reload/navigation will pick up changes.
            // Or, update the textarea's original value if we want to track "dirty" state.
        } catch (error) {
            // showMessage is already called by apiFetch on error
            statusElement.textContent = `保存失败: ${error.message}`;
            statusElement.classList.remove('info', 'success');
            statusElement.classList.add('error');
        }
    }
    
    function addCustomConfigFieldToPluginForm(form, pluginName, containerToAddTo, currentParsedEntries) {
        const key = prompt("请输入新自定义配置项的键名 (例如 MY_PLUGIN_VAR):");
        if (!key || !key.trim()) return;
        const normalizedKey = key.trim().replace(/\s+/g, '_');

        if (currentParsedEntries.some(entry => entry.key === normalizedKey) || form.elements[normalizedKey]) {
            showMessage(`配置项 "${normalizedKey}" 已存在！`, 'error');
            return;
        }

        const descriptionHtml = `自定义配置项: ${normalizedKey} <span class="defined-in">(新添加)</span>`;
        const formGroup = createFormGroup(normalizedKey, '', 'string', descriptionHtml, true, pluginName, true, false);
        
        // Add to currentParsedEntries so buildEnvString can find it
        currentParsedEntries.push({ key: normalizedKey, value: '', isCommentOrEmpty: false, isMultilineQuoted: false });

        let targetContainer = containerToAddTo;
         if (!targetContainer || !form.contains(targetContainer)) {
            const customSectionTitle = Array.from(form.querySelectorAll('h3')).find(h => h.textContent.includes('自定义 .env 配置项'));
            if (customSectionTitle) {
                targetContainer = customSectionTitle.nextElementSibling; // Assuming div container follows h3
                if (!targetContainer || targetContainer.classList.contains('form-actions')) { // If no div or it's the actions
                    targetContainer = form.querySelector('.form-actions') || form;
                }
            } else {
                 targetContainer = form.querySelector('.form-actions') || form;
            }
        }
        
        const actionsDiv = form.querySelector('.form-actions');
        if (actionsDiv && targetContainer.contains(actionsDiv)) { // Insert before actions if actions are in target
            targetContainer.insertBefore(formGroup, actionsDiv);
        } else if (actionsDiv && form.contains(actionsDiv)) { // Insert before actions if actions are in form (but not target)
             form.insertBefore(formGroup, actionsDiv);
        } else { // Append to target or form if no actions div
            targetContainer.appendChild(formGroup);
        }
    }


    async function handlePluginFormSubmit(event) {
        event.preventDefault();
        const form = event.target;
        const pluginName = form.id.match(/plugin-(.*?)-config-form/)[1];
        
        // Rebuild the .env string from the form, preserving comments and order
        const currentPluginEntries = originalPluginConfigs[pluginName] || [];
        const newConfigString = buildEnvStringForPlugin(form, currentPluginEntries);

        try {
            await apiFetch(`${API_BASE_URL}/plugins/${pluginName}/config`, {
                method: 'POST',
                body: JSON.stringify({ content: newConfigString })
            });
            showMessage(`${pluginName} 配置已保存！更改可能需要重启插件或服务生效。`, 'success');
            loadPluginConfig(pluginName); // Reload to reflect changes
        } catch (error) { /* Error handled by apiFetch */ }
    }

    function buildEnvStringForPlugin(formElement, originalParsedEntries) {
        const finalLines = [];
        const editedKeysInForm = new Set();
        const processedKeys = new Set();
        
        // Extract plugin name from form ID
        const pluginName = formElement.id.match(/plugin-(.*?)-config-form/)?.[1] || '';

        // Collect all keys that are actually present as editable fields in the form
        Array.from(formElement.elements).forEach(el => {
            if (el.dataset.originalKey) {
                editedKeysInForm.add(el.dataset.originalKey);
            }
        });

        // Process existing entries (comments, empty lines, and existing config items)
        originalParsedEntries.forEach(entry => {
            if (entry.isCommentOrEmpty) {
                finalLines.push(entry.value);
            } else {
                const inputElement = formElement.elements[`${pluginName}-${entry.key.replace(/\./g, '_')}`] || formElement.elements[entry.key];
                if (inputElement && editedKeysInForm.has(entry.key)) {
                    let value = inputElement.value;
                    if (inputElement.type === 'checkbox' && inputElement.dataset.expectedType === 'boolean') {
                        value = inputElement.checked ? 'true' : 'false';
                    } else if (inputElement.dataset.expectedType === 'integer') {
                        const intVal = parseInt(value, 10);
                        value = isNaN(intVal) ? (value === '' ? '' : value) : String(intVal);
                    }
                    
                    // Only include non-empty values or explicitly set boolean false
                    if (value !== '' || (inputElement.type === 'checkbox' && inputElement.dataset.expectedType === 'boolean')) {
                    const isMultiline = entry.isMultilineQuoted || value.includes('\n');
                    if (isMultiline) {
                        finalLines.push(`${entry.key}='${value}'`);
                    } else {
                        finalLines.push(`${entry.key}=${value}`);
                    }
                    }
                    processedKeys.add(entry.key);
                }
            }
        });
        
        // Process schema-defined fields that weren't in the original config.env
        // This handles the case where the plugin config.env is empty but the form has schema fields
        editedKeysInForm.forEach(key => {
            if (!processedKeys.has(key)) {
                const inputElement = formElement.elements[`${pluginName}-${key.replace(/\./g, '_')}`] || formElement.elements[key];
                if (inputElement) {
                    let value = inputElement.value;
                     if (inputElement.type === 'checkbox' && inputElement.dataset.expectedType === 'boolean') {
                        value = inputElement.checked ? 'true' : 'false';
                    } else if (inputElement.dataset.expectedType === 'integer') {
                        const intVal = parseInt(value, 10);
                        value = isNaN(intVal) ? (value === '' ? '' : value) : String(intVal);
                    }
                    
                    // Only include non-empty values or explicitly set boolean false
                    if (value !== '' || (inputElement.type === 'checkbox' && inputElement.dataset.expectedType === 'boolean')) {
                    const isMultiline = value.includes('\n');
                     if (isMultiline) {
                            finalLines.push(`${key}='${value}'`);
                    } else {
                            finalLines.push(`${key}=${value}`);
                    }
                    }
                    processedKeys.add(key);
                 }
            }
        });

        // If no lines were generated and we have form elements, ensure we don't return empty content
        if (finalLines.length === 0 && editedKeysInForm.size > 0) {
            // Add a comment to indicate this is generated
            finalLines.push('# Plugin configuration generated by VCP Admin Panel');
        }

        return finalLines.join('\n');
    }


    function createCommentOrEmptyElement(lineContent, uniqueId) {
        const group = document.createElement('div');
        group.className = 'form-group-comment'; // Different class for styling
        const commentPre = document.createElement('pre');
        commentPre.textContent = lineContent;
        commentPre.dataset.isCommentOrEmpty = "true";
        commentPre.dataset.originalContent = lineContent; // Store for saving
        commentPre.id = `comment-${uniqueId}`;
        group.appendChild(commentPre);
        return group;
    }


    function createFormGroup(key, value, type, descriptionHtml, isPluginConfig = false, pluginName = null, isCustomDeletableField = false, isMultiline = false) {
        const group = document.createElement('div');
        group.className = 'plugin-config-card p-6 rounded-xl border border-gray-200 animate-slide-in';
        const elementIdSuffix = key.replace(/\./g, '_');
        const elementId = `${isPluginConfig && pluginName ? pluginName + '-' : ''}${elementIdSuffix}`;

        // 创建标题行
        const headerDiv = document.createElement('div');
        headerDiv.className = 'flex items-center justify-between mb-4';
        
        const labelDiv = document.createElement('div');
        labelDiv.className = 'flex items-center space-x-3';

        const label = document.createElement('label');
        label.htmlFor = elementId;
        label.className = 'config-title';
        label.textContent = key;
        labelDiv.appendChild(label);

        // 添加配置类型徽章
        const typeBadge = document.createElement('span');
        typeBadge.className = `config-type-badge ${type}`;
        if (type === 'boolean') {
            typeBadge.textContent = '布尔值';
        } else if (type === 'integer') {
            typeBadge.textContent = '数字';
        } else {
            typeBadge.textContent = '文本';
        }
        labelDiv.appendChild(typeBadge);

        headerDiv.appendChild(labelDiv);

        if (isPluginConfig && isCustomDeletableField) {
            const deleteButton = document.createElement('button');
            deleteButton.type = 'button';
            deleteButton.className = 'delete-config-btn';
            deleteButton.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/></svg>';
            deleteButton.title = `删除自定义项 ${key}`;
            deleteButton.onclick = (e) => {
                e.stopPropagation();
                if (confirm(`确定要删除自定义配置项 "${key}" 吗？更改将在保存后生效。`)) {
                    group.classList.add('opacity-0', 'scale-95');
                    setTimeout(() => {
                    group.remove();
                    // Also remove from originalPluginConfigs[pluginName] to prevent re-adding on save
                    if (pluginName && originalPluginConfigs[pluginName]) {
                        originalPluginConfigs[pluginName] = originalPluginConfigs[pluginName].filter(entry => entry.key !== key);
                    } else if (!pluginName && originalBaseConfigEntries) { // For base config if ever needed
                        originalBaseConfigEntries = originalBaseConfigEntries.filter(entry => entry.key !== key);
                    }
                    }, 300);
                }
            };
            headerDiv.appendChild(deleteButton);
        }
        
        group.appendChild(headerDiv);

        if (descriptionHtml) {
            const descDiv = document.createElement('div');
            descDiv.className = 'config-description mb-4 description-text';
            descDiv.innerHTML = descriptionHtml; // Use innerHTML for spans from server
            group.appendChild(descDiv);
        }

        let input;
        const isSensitiveField = key.includes('API_KEY') || key.includes('Key') || key.includes('PASSWORD') || key.includes('SECRET') || key.includes('TOKEN');
        
        // 创建输入控件容器
        const inputContainer = document.createElement('div');
        inputContainer.className = 'space-y-2';

        if (type === 'boolean') {
            const switchContainer = document.createElement('div');
            switchContainer.className = 'switch-container flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-200 transition-all duration-300';
            
            const switchLabel = document.createElement('label');
            switchLabel.className = 'switch';
            input = document.createElement('input');
            input.type = 'checkbox';
            input.checked = String(value).toLowerCase() === 'true';
            
            const sliderSpan = document.createElement('span');
            sliderSpan.className = 'slider';
            switchLabel.appendChild(input);
            switchLabel.appendChild(sliderSpan);
            switchContainer.appendChild(switchLabel);
            
            const valueDisplay = document.createElement('span');
            valueDisplay.className = 'text-sm font-medium text-gray-700 transition-colors duration-300';
            valueDisplay.textContent = input.checked ? '✅ 启用' : '❌ 禁用';
            input.onchange = () => { 
                valueDisplay.textContent = input.checked ? '✅ 启用' : '❌ 禁用';
                switchContainer.classList.toggle('bg-green-50', input.checked);
                switchContainer.classList.toggle('border-green-300', input.checked);
                valueDisplay.classList.toggle('text-green-700', input.checked);
            };
            
            // 初始状态样式
            if (input.checked) {
                switchContainer.classList.add('bg-green-50', 'border-green-300');
                valueDisplay.classList.add('text-green-700');
            }
            
            switchContainer.appendChild(valueDisplay);
            inputContainer.appendChild(switchContainer);
        } else if (type === 'integer') {
            input = document.createElement('input');
            input.type = 'number';
            input.className = 'modern-input w-full px-4 py-3 rounded-lg transition-all duration-300';
            input.value = value !== null && value !== undefined ? value : '';
            input.step = '1';
            input.placeholder = '请输入数字...';
            inputContainer.appendChild(input);
        } else if (isMultiline || String(value).includes('\n') || (typeof value === 'string' && value.length > 60)) {
            input = document.createElement('textarea');
            input.className = 'modern-input w-full px-4 py-3 rounded-lg transition-all duration-300 font-mono';
            input.value = value !== null && value !== undefined ? value : '';
            input.rows = Math.min(10, Math.max(3, String(value).split('\n').length + 1));
            input.placeholder = '请输入配置值...';
            inputContainer.appendChild(input);
        } else {
            const fieldContainer = document.createElement('div');
            fieldContainer.className = 'relative';
            
            input = document.createElement('input');
            input.type = isSensitiveField ? 'password' : 'text';
            input.className = 'modern-input w-full px-4 py-3 rounded-lg transition-all duration-300';
            input.value = value !== null && value !== undefined ? value : '';
            input.placeholder = isSensitiveField ? '请输入密钥或令牌...' : '请输入配置值...';
            
            // For sensitive fields, add a toggle button to show/hide
            if (isSensitiveField) {
                input.className += ' pr-12'; // Make room for toggle button
                
                const toggleButton = document.createElement('button');
                toggleButton.type = 'button';
                toggleButton.className = 'absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-anime-pink transition-colors duration-200';
                toggleButton.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/></svg>';
                toggleButton.title = '显示/隐藏密钥';
                
                toggleButton.onclick = () => {
                    if (input.type === 'password') {
                        input.type = 'text';
                        toggleButton.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L12.5 12.5m0 0l3.5 3.5"/></svg>';
                    } else {
                        input.type = 'password';
                        toggleButton.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/></svg>';
                    }
                };
                
                fieldContainer.appendChild(input);
                fieldContainer.appendChild(toggleButton);
                inputContainer.appendChild(fieldContainer);
            } else {
                inputContainer.appendChild(input);
            }
        }
        
        group.appendChild(inputContainer);

        // Set common properties for all input types
        input.id = elementId;
        input.name = elementId;
        input.dataset.originalKey = key;
        input.dataset.expectedType = type;
        
        return group;
    }


    // 将 navigateTo 函数设置为全局函数，避免作用域问题
    window.navigateTo = function(sectionIdToActivate, pluginName = null, navLinkDataTarget = null) {
        // If navigating to a section that is NOT the server log viewer,
        // and an interval is active, clear it.
        if (sectionIdToActivate !== 'server-log-viewer-section' && serverLogIntervalId) {
            clearInterval(serverLogIntervalId);
            serverLogIntervalId = null;
            console.log('Server log auto-refresh interval cleared due to navigation.');
        }
        
        // 清除所有活跃状态
        document.querySelectorAll('#plugin-nav a').forEach(link => link.classList.remove('active'));
        document.querySelectorAll('.config-section').forEach(section => {
            section.style.display = 'none';
            section.classList.remove('active-section');
        });

        // 查找并激活对应的导航链接
        const linkSelector = navLinkDataTarget ? `a[data-target="${navLinkDataTarget}"]` : `a[data-target="${sectionIdToActivate.replace('-section', '')}"]`;
        const activeLink = document.querySelector(linkSelector);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // 显示目标区域
        const targetSection = document.getElementById(sectionIdToActivate);
        if (targetSection) {
            targetSection.style.display = 'block';
            targetSection.classList.add('active-section');
            
            // 添加淡入动画
            targetSection.style.opacity = '0';
            setTimeout(() => {
                targetSection.style.opacity = '1';
            }, 50);
            
            if (pluginName) {
                loadPluginConfig(pluginName).catch(err => console.error(`Failed to load config for ${pluginName}`, err));
            } else if (sectionIdToActivate === 'base-config-section') {
                // Base config already loaded
            } else if (sectionIdToActivate === 'daily-notes-manager-section') {
                initializeDailyNotesManager();
            } else if (sectionIdToActivate === 'agent-files-editor-section') {
                initializeAgentFilesEditor();
            } else if (sectionIdToActivate === 'server-log-viewer-section') {
                initializeServerLogViewer();
            } else if (sectionIdToActivate === 'wechat-adapter-section') {
                initializeWeChatAdapter();
            } else if (sectionIdToActivate === 'config-hot-reload-section') {
                initializeHotReloadSection();
            }
        } else {
            console.warn(`[navigateTo] Target section with ID '${sectionIdToActivate}' not found.`);
        }
    }

    // 使用事件委托处理所有导航点击
    document.addEventListener('click', (event) => {
        const anchor = event.target.closest('a[data-target]');
        if (anchor) {
            event.preventDefault();
            const dataTarget = anchor.dataset.target;
            const pluginName = anchor.dataset.pluginName;
            const sectionIdToActivate = `${dataTarget}-section`;
            navigateTo(sectionIdToActivate, pluginName, dataTarget);
        }
    });

    // --- Server Restart Function ---
    async function restartServer() {
        if (!confirm('您确定要重启服务器吗？')) {
            return;
        }
        try {
            showMessage('正在发送重启服务器命令...', 'info');
            const response = await apiFetch(`${API_BASE_URL}/server/restart`, { method: 'POST' });
            // The server typically closes the connection upon successful restart command,
            // so a successful JSON response might not always come.
            // We'll rely on the HTTP status or a simple text message if provided.
            if (typeof response === 'string' && response.includes('重启命令已发送')) {
                 showMessage(response, 'success', 5000);
            } else if (response && response.message) {
                showMessage(response.message, 'success', 5000);
            }
            else {
                showMessage('服务器重启命令已发送。请稍后检查服务器状态。', 'success', 5000);
            }
        } catch (error) {
            // Error is already shown by apiFetch, but we can add a specific console log
            console.error('Restart server failed:', error);
        }
    }

    if (restartServerButton) {
        restartServerButton.addEventListener('click', restartServer);
    }

    loadInitialData();

    // 默认显示全局配置
    setTimeout(() => {
        navigateTo('base-config-section', null, 'base-config');
    }, 100);

    // 主题切换功能
    const themeToggleButton = document.getElementById('切换主题');
    if (themeToggleButton) {
        // 检查当前主题
        const currentTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', currentTheme);
        
        themeToggleButton.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // 添加切换动画
            document.body.style.transition = 'all 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
            
            showMessage(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}主题`, 'success');
        });
    }

    // --- Daily Notes Manager Functions ---
    let currentNotesFolder = null;
    let selectedNotes = new Set();

    async function initializeDailyNotesManager() {
        console.log('Initializing Daily Notes Manager...');
        notesListViewDiv.innerHTML = ''; // Clear previous notes
        noteEditorAreaDiv.style.display = 'none'; // Hide editor
        notesActionStatusSpan.textContent = '';
        moveSelectedNotesButton.disabled = true;
        if (deleteSelectedNotesButton) deleteSelectedNotesButton.disabled = true; // 新增：禁用删除按钮
        if (searchDailyNotesInput) searchDailyNotesInput.value = ''; // 清空搜索框
        await loadNotesFolders();
        // Optionally, load notes from the first folder automatically or show a placeholder
    }

    async function loadNotesFolders() {
        try {
            const data = await apiFetch(`${API_BASE_URL}/dailynotes/folders`);
            console.log('[DailyNotes] loadNotesFolders - API response data:', data); // 调试输出
            console.log('[DailyNotes] loadNotesFolders - typeof data:', typeof data); // 调试输出
            if (data && typeof data === 'object') { // 调试输出
                console.log('[DailyNotes] loadNotesFolders - data.folders:', data.folders);
            }
            notesFolderListUl.innerHTML = '';
            moveTargetFolderSelect.innerHTML = '<option value="">选择目标文件夹...</option>';

            if (data.folders && data.folders.length > 0) {
                data.folders.forEach(folder => {
                    const li = document.createElement('li');
                    li.textContent = folder;
                    li.dataset.folderName = folder;
                    li.addEventListener('click', () => {
                        loadNotesForFolder(folder);
                        // Update active class
                        notesFolderListUl.querySelectorAll('li').forEach(item => item.classList.remove('active'));
                        li.classList.add('active');
                    });
                    notesFolderListUl.appendChild(li);

                    const option = document.createElement('option');
                    option.value = folder;
                    option.textContent = folder;
                    moveTargetFolderSelect.appendChild(option);
                });
                // Automatically select and load the first folder if none is current or if current is no longer valid
                if (!currentNotesFolder || !data.folders.includes(currentNotesFolder)) {
                    if (notesFolderListUl.firstChild) {
                         notesFolderListUl.firstChild.click(); // Simulate click to load notes and set active
                    }
                } else {
                    // Reselect current folder if it still exists
                     const currentFolderLi = notesFolderListUl.querySelector(`li[data-folder-name="${currentNotesFolder}"]`);
                     if (currentFolderLi) currentFolderLi.classList.add('active');
                }
            } else {
                notesFolderListUl.innerHTML = '<li>没有找到日记文件夹。</li>';
                notesListViewDiv.innerHTML = '<p>没有日记可以显示。</p>';
            }
        } catch (error) {
            notesFolderListUl.innerHTML = '<li>加载文件夹列表失败。</li>';
            showMessage('加载文件夹列表失败: ' + error.message, 'error');
        }
    }

    async function loadNotesForFolder(folderName) {
        currentNotesFolder = folderName;
        selectedNotes.clear(); // Clear selection when changing folder
        updateMoveButtonStatus();
        notesListViewDiv.innerHTML = '<p>正在加载日记...</p>'; // Loading state
        noteEditorAreaDiv.style.display = 'none'; // Ensure editor is hidden
        if(searchDailyNotesInput) searchDailyNotesInput.value = ''; // Clear search input when loading a folder

        try {
            const data = await apiFetch(`${API_BASE_URL}/dailynotes/folder/${folderName}`);
            notesListViewDiv.innerHTML = ''; // Clear loading state
            if (data.notes && data.notes.length > 0) {
                data.notes.reverse(); // Add this line to reverse the order
                data.notes.forEach(note => {
                    // renderNoteCard now expects note.folderName to be part of the note object for consistency
                    // or pass folderName explicitly if the endpoint doesn't return it per note
                    const card = renderNoteCard(note, folderName); // Pass folderName explicitly
                    notesListViewDiv.appendChild(card);
                });
            } else {
                notesListViewDiv.innerHTML = `<p>文件夹 "${folderName}" 中没有日记。</p>`;
            }
        } catch (error) {
            notesListViewDiv.innerHTML = `<p>加载文件夹 "${folderName}" 中的日记失败。</p>`;
            showMessage(`加载日记失败: ${error.message}`, 'error');
        }
        // No longer call filterNotesBySearch here, search is independent or triggered by input
    }

    async function filterNotesBySearch() {
        if (!searchDailyNotesInput) return;
        const searchTerm = searchDailyNotesInput.value.trim();

        if (searchTerm === '') {
            // If search term is empty, reload notes for the current folder
            if (currentNotesFolder) {
                loadNotesForFolder(currentNotesFolder);
            } else {
                notesListViewDiv.innerHTML = '<p>请输入搜索词或选择一个文件夹。</p>';
            }
            return;
        }

        notesListViewDiv.innerHTML = '<p>正在搜索日记...</p>';
        try {
            // Use currentNotesFolder if available for targeted search, otherwise global (if API supports)
            const searchUrl = currentNotesFolder
                ? `${API_BASE_URL}/dailynotes/search?term=${encodeURIComponent(searchTerm)}&folder=${encodeURIComponent(currentNotesFolder)}`
                : `${API_BASE_URL}/dailynotes/search?term=${encodeURIComponent(searchTerm)}`; // Global search

            const data = await apiFetch(searchUrl);
            notesListViewDiv.innerHTML = ''; // Clear loading/previous results

            if (data.notes && data.notes.length > 0) {
                data.notes.reverse(); // Add this line to reverse the order
                data.notes.forEach(note => {
                    // The search API now returns folderName with each note
                    const card = renderNoteCard(note, note.folderName);
                    notesListViewDiv.appendChild(card);
                });
            } else {
                notesListViewDiv.innerHTML = `<p>没有找到与 "${searchTerm}" 相关的日记。</p>`;
            }
        } catch (error) {
            notesListViewDiv.innerHTML = `<p>搜索日记失败: ${error.message}</p>`;
            showMessage(`搜索失败: ${error.message}`, 'error');
        }
    }

    function renderNoteCard(note, folderName) { // folderName is passed explicitly or from note object
        const card = document.createElement('div');
        card.className = 'note-card-modern rounded-xl shadow-md p-4 border cursor-pointer animate-fade-in';
        card.dataset.fileName = note.name;
        card.dataset.folderName = folderName;

        // 卡片头部
        const headerDiv = document.createElement('div');
        headerDiv.className = 'flex items-center justify-between mb-3';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'w-4 h-4 text-anime-pink bg-gray-100 border-gray-300 rounded focus:ring-anime-pink focus:ring-2';
        checkbox.addEventListener('change', (e) => {
            const noteId = `${folderName}/${note.name}`;
            if (e.target.checked) {
                selectedNotes.add(noteId);
                card.classList.add('ring-2', 'ring-anime-pink', 'bg-anime-pink', 'bg-opacity-5');
            } else {
                selectedNotes.delete(noteId);
                card.classList.remove('ring-2', 'ring-anime-pink', 'bg-anime-pink', 'bg-opacity-5');
            }
            updateMoveButtonStatus();
        });
        
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'flex items-center space-x-2';
        
        const editButton = document.createElement('button');
        editButton.className = 'inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-anime-blue hover:bg-anime-blue hover:text-white transition-colors duration-200';
        editButton.innerHTML = `
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
            </svg>
            编辑
        `;
        editButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent card click if button is separate
            openNoteForEditing(folderName, note.name);
        });

        actionsDiv.appendChild(editButton);
        headerDiv.appendChild(checkbox);
        headerDiv.appendChild(actionsDiv);
        
        // 文件名
        const fileNameDiv = document.createElement('div');
        fileNameDiv.className = 'mb-2';
        const fileNameP = document.createElement('p');
        fileNameP.className = 'text-sm font-semibold text-anime-dark truncate';
        fileNameP.textContent = note.name;
        fileNameP.title = note.name; // 完整文件名tooltip
        fileNameDiv.appendChild(fileNameP);
        
        // 预览内容
        const previewP = document.createElement('p');
        previewP.className = 'text-xs text-gray-600 line-clamp-3 leading-relaxed';
        // Use the preview from the note object, fallback to lastModified if preview is not available
        previewP.textContent = note.preview || `修改于: ${new Date(note.lastModified).toLocaleString()}`;
        
        // 元信息
        const metaDiv = document.createElement('div');
        metaDiv.className = 'flex items-center justify-between mt-3 pt-3 border-t border-gray-100';
        
        const folderBadge = document.createElement('span');
        folderBadge.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-anime-blue bg-opacity-10 text-anime-blue';
        folderBadge.innerHTML = `
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"/>
            </svg>
            ${folderName}
        `;
        
        const dateSpan = document.createElement('span');
        dateSpan.className = 'text-xs text-gray-400';
        dateSpan.textContent = new Date(note.lastModified).toLocaleDateString();
        
        metaDiv.appendChild(folderBadge);
        metaDiv.appendChild(dateSpan);
        
        card.appendChild(headerDiv);
        card.appendChild(fileNameDiv);
        card.appendChild(previewP);
        card.appendChild(metaDiv);

        // Card click also opens for editing (if not clicking checkbox or button)
        card.addEventListener('click', (e) => {
            if (e.target !== checkbox && !actionsDiv.contains(e.target)) {
                 openNoteForEditing(folderName, note.name);
            }
        });
        return card;
    }
    
    function updateMoveButtonStatus() {
        const hasSelection = selectedNotes.size > 0;
        moveSelectedNotesButton.disabled = !hasSelection;
        moveTargetFolderSelect.disabled = !hasSelection;
        if (deleteSelectedNotesButton) deleteSelectedNotesButton.disabled = !hasSelection; // 新增：更新删除按钮状态
    }

    async function openNoteForEditing(folderName, fileName) {
        notesActionStatusSpan.textContent = '';
        try {
            const data = await apiFetch(`${API_BASE_URL}/dailynotes/note/${folderName}/${fileName}`);
            editingNoteFolderInput.value = folderName;
            editingNoteFileInput.value = fileName;
            noteContentEditorTextarea.value = data.content;
            
            // 显示模态框编辑器
            noteEditorAreaDiv.style.display = 'flex';
            noteEditorStatusSpan.textContent = `正在编辑: ${folderName}/${fileName}`;
            
            // 聚焦到文本区域
            setTimeout(() => {
                noteContentEditorTextarea.focus();
            }, 100);
        } catch (error) {
            showMessage(`打开日记 ${fileName} 失败: ${error.message}`, 'error');
        }
    }

    async function saveNoteChanges() {
        const folderName = editingNoteFolderInput.value;
        const fileName = editingNoteFileInput.value;
        const content = noteContentEditorTextarea.value;

        if (!folderName || !fileName) {
            showMessage('无法保存日记，缺少文件信息。', 'error');
            return;
        }
        noteEditorStatusSpan.textContent = '正在保存...';
        try {
            await apiFetch(`${API_BASE_URL}/dailynotes/note/${folderName}/${fileName}`, {
                method: 'POST',
                body: JSON.stringify({ content })
            });
            showMessage(`日记 ${fileName} 已成功保存!`, 'success');
            closeNoteEditor(); // This will also trigger a refresh of the notes list if current folder matches
            if (currentNotesFolder === folderName) {
                loadNotesForFolder(folderName); // Refresh the list
            }
        } catch (error) {
            noteEditorStatusSpan.textContent = `保存失败: ${error.message}`;
            // showMessage is handled by apiFetch
        }
    }

    function closeNoteEditor() {
        noteEditorAreaDiv.style.display = 'none';
        editingNoteFolderInput.value = '';
        editingNoteFileInput.value = '';
        noteContentEditorTextarea.value = '';
        noteEditorStatusSpan.textContent = '';
        
        // 由于编辑器现在是模态框，不需要恢复其他元素的显示状态
        // 模态框关闭后，原有的布局会自动恢复
    }
    
    async function moveSelectedNotesHandler() { // Renamed to avoid conflict if any
        const targetFolder = moveTargetFolderSelect.value;
        if (!targetFolder) {
            showMessage('请选择一个目标文件夹。', 'error');
            return;
        }
        if (selectedNotes.size === 0) {
            showMessage('没有选中的日记。', 'error');
            return;
        }

        const notesToMove = Array.from(selectedNotes).map(noteId => {
            const [folder, file] = noteId.split('/');
            return { folder, file };
        });

        notesActionStatusSpan.textContent = '正在移动...';
        try {
            const response = await apiFetch(`${API_BASE_URL}/dailynotes/move`, {
                method: 'POST',
                body: JSON.stringify({ sourceNotes: notesToMove, targetFolder })
            });
            showMessage(response.message || `${notesToMove.length} 个日记已移动。`, response.errors && response.errors.length > 0 ? 'error' : 'success');
            if (response.errors && response.errors.length > 0) {
                console.error('移动日记时发生错误:', response.errors);
                notesActionStatusSpan.textContent = `部分移动失败: ${response.errors.map(e => e.error).join(', ')}`;
            } else {
                 notesActionStatusSpan.textContent = '';
            }
            
            // Refresh current folder and folder list (as folders might have changed)
            const folderToReload = currentNotesFolder; // Store before clearing
            selectedNotes.clear();
            updateMoveButtonStatus();
            await loadNotesFolders(); // This will re-populate target folder select and folder list
            
            // Try to reselect the previously active folder, or the target folder if current was source
            let reselectFolder = folderToReload;
            if (notesToMove.some(n => n.folder === folderToReload)) { // If we moved from current folder
                // Check if current folder still exists or if it became empty and should switch
                // For now, just reload it. loadNotesFolders will handle active state.
            }
            
            // If current folder was one of the source folders, reload it.
            // If the target folder is now the current folder, also good.
            // loadNotesFolders will try to re-activate currentNotesFolder if it exists.
            // If not, it loads the first one.
            if (folderToReload) {
                 const currentFolderLi = notesFolderListUl.querySelector(`li[data-folder-name="${folderToReload}"]`);
                 if (currentFolderLi) {
                    currentFolderLi.click(); // This will trigger loadNotesForFolder
                 } else if (notesFolderListUl.firstChild) { // If original folder gone, click first
                    notesFolderListUl.firstChild.click();
                 } else {
                    notesListViewDiv.innerHTML = '<p>请选择一个文件夹。</p>'; // No folders left
                 }
            }


        } catch (error) {
            notesActionStatusSpan.textContent = `移动失败: ${error.message}`;
            // showMessage is handled by apiFetch
        }
    }

    // Event Listeners for Daily Notes
    if (saveNoteButton) saveNoteButton.addEventListener('click', saveNoteChanges);
    if (cancelEditNoteButton) cancelEditNoteButton.addEventListener('click', closeNoteEditor);
    if (moveSelectedNotesButton) moveSelectedNotesButton.addEventListener('click', moveSelectedNotesHandler);
    if (deleteSelectedNotesButton) deleteSelectedNotesButton.addEventListener('click', deleteSelectedNotesHandler); // 新增：删除按钮事件
    if (searchDailyNotesInput) searchDailyNotesInput.addEventListener('input', filterNotesBySearch);


    // --- End Daily Notes Manager Functions ---

    // --- New Function for Deleting Selected Notes ---
    async function deleteSelectedNotesHandler() {
        if (selectedNotes.size === 0) {
            showMessage('没有选中的日记。', 'error');
            return;
        }

        if (!confirm(`您确定要删除选中的 ${selectedNotes.size} 个日记吗？此操作无法撤销。`)) {
            return;
        }

        const notesToDelete = Array.from(selectedNotes).map(noteId => {
            const [folder, file] = noteId.split('/');
            return { folder, file };
        });

        notesActionStatusSpan.textContent = '正在删除...';
        try {
            const response = await apiFetch(`${API_BASE_URL}/dailynotes/delete-batch`, {
                method: 'POST', // Changed to POST as per server.js implementation
                body: JSON.stringify({ notesToDelete })
            });
            showMessage(response.message || `${notesToDelete.length} 个日记已删除。`, response.errors && response.errors.length > 0 ? 'warning' : 'success');
            
            if (response.errors && response.errors.length > 0) {
                console.error('删除日记时发生错误:', response.errors);
                notesActionStatusSpan.textContent = `部分删除失败: ${response.errors.map(e => e.error).join(', ')}`;
            } else {
                notesActionStatusSpan.textContent = '';
            }

            const folderToReload = currentNotesFolder;
            selectedNotes.clear();
            updateMoveButtonStatus(); // This will disable buttons again

            // Refresh folder list (in case a folder becomes empty and might be handled differently by UI, though unlikely for delete)
            // and notes list for the current folder.
            await loadNotesFolders(); // Reloads all folders and their counts, re-populates move target

            // Try to reselect the previously active folder
            if (folderToReload) {
                const currentFolderLi = notesFolderListUl.querySelector(`li[data-folder-name="${folderToReload}"]`);
                if (currentFolderLi) {
                    currentFolderLi.click(); // This will trigger loadNotesForFolder
                } else if (notesFolderListUl.firstChild) { // If original folder gone (e.g., it was deleted), click first
                    notesFolderListUl.firstChild.click();
                } else { // No folders left
                    notesListViewDiv.innerHTML = '<p>请选择一个文件夹。</p>';
                }
            } else if (notesFolderListUl.firstChild) { // If no folder was current, click first
                 notesFolderListUl.firstChild.click();
            } else {
                notesListViewDiv.innerHTML = '<p>没有日记可以显示。</p>';
            }

        } catch (error) {
            notesActionStatusSpan.textContent = `删除失败: ${error.message}`;
            // showMessage is handled by apiFetch
        }
    }
    // --- End New Function ---

    // --- Agent Files Editor Functions ---
    let currentEditingAgentFile = null;

    async function initializeAgentFilesEditor() {
        console.log('Initializing Agent Files Editor...');
        agentFileContentEditor.value = '';
        agentFileStatusSpan.textContent = '';
        saveAgentFileButton.disabled = true;
        currentEditingAgentFile = null;
        
        await loadAgentFilesList();
        await loadAgentFilesGrid(); // 新增：加载Agent文件网格
        
        // 新增：刷新按钮事件
        const refreshAgentListButton = document.getElementById('refresh-agent-list');
        if (refreshAgentListButton) {
            refreshAgentListButton.addEventListener('click', async () => {
                await loadAgentFilesList();
                await loadAgentFilesGrid();
                showMessage('Agent文件列表已刷新', 'success');
            });
        }

        // 新增：创建Agent按钮事件
        const createAgentButton = document.getElementById('create-agent-button');
        const createAgentDialog = document.getElementById('create-agent-dialog');
        const closeCreateDialog = document.getElementById('close-create-dialog');
        const cancelCreateAgent = document.getElementById('cancel-create-agent');
        const confirmCreateAgent = document.getElementById('confirm-create-agent');
        const newAgentName = document.getElementById('new-agent-name');
        const newAgentContent = document.getElementById('new-agent-content');

        if (createAgentButton && createAgentDialog) {
            // 打开对话框
            createAgentButton.addEventListener('click', () => {
                createAgentDialog.classList.remove('hidden');
                newAgentName.value = '';
                newAgentContent.value = '';
            });

            // 关闭对话框的多种方式
            [closeCreateDialog, cancelCreateAgent].forEach(button => {
                if (button) {
                    button.addEventListener('click', () => {
                        createAgentDialog.classList.add('hidden');
                    });
                }
            });

            // 点击对话框外部关闭
            createAgentDialog.addEventListener('click', (e) => {
                if (e.target === createAgentDialog) {
                    createAgentDialog.classList.add('hidden');
                }
            });

            // 创建Agent文件
            if (confirmCreateAgent) {
                confirmCreateAgent.addEventListener('click', async () => {
                    const name = newAgentName.value.trim();
                    const content = newAgentContent.value.trim();

                    if (!name) {
                        showMessage('请输入Agent名称', 'error');
                        return;
                    }

                    try {
                        // 确保文件名以.txt结尾
                        const fileName = name.endsWith('.txt') ? name : `${name}.txt`;
                        
                        await apiFetch(`${API_BASE_URL}/agents/${fileName}`, {
                            method: 'POST',
                            body: JSON.stringify({ content })
                        });

                        showMessage(`Agent文件 '${fileName}' 创建成功!`, 'success');
                        createAgentDialog.classList.add('hidden');
                        
                        // 刷新文件列表
                        await loadAgentFilesList();
                        await loadAgentFilesGrid();
                        
                        // 自动选择并加载新创建的文件
                        agentFileSelect.value = fileName;
                        await loadAgentFileContent(fileName);
                    } catch (error) {
                        showMessage(`创建Agent文件失败: ${error.message}`, 'error');
                    }
                });
            }
        }
    }

    // 新增：加载Agent文件网格
    async function loadAgentFilesGrid() {
        const agentFilesGrid = document.getElementById('agent-files-grid');
        if (!agentFilesGrid) return;
        
        try {
            const data = await apiFetch(`${API_BASE_URL}/agents`);
            agentFilesGrid.innerHTML = '';
            
            if (data.success && data.agents && data.agents.length > 0) {
                data.agents.forEach(agent => {
                    const agentCard = createAgentCard(agent);
                    agentFilesGrid.appendChild(agentCard);
                });
            } else {
                agentFilesGrid.innerHTML = '<div class="col-span-full text-center text-gray-500 py-8">没有找到Agent文件</div>';
            }
        } catch (error) {
            agentFilesGrid.innerHTML = `<div class="col-span-full text-center text-red-500 py-8">加载Agent文件失败: ${error.message}</div>`;
        }
    }

    // 新增：创建Agent卡片
    function createAgentCard(agent) {
        const card = document.createElement('div');
        card.className = 'agent-card p-4 rounded-lg relative group';
        
        // 删除按钮
        const deleteButton = document.createElement('button');
        deleteButton.className = 'absolute top-2 right-2 text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200';
        deleteButton.innerHTML = `
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
        `;
        
        // 添加删除事件
        deleteButton.addEventListener('click', async (e) => {
            e.stopPropagation(); // 阻止事件冒泡到卡片点击事件
            
            if (confirm(`确定要删除Agent文件 "${agent.name}" 吗？此操作不可恢复。`)) {
                try {
                    await apiFetch(`${API_BASE_URL}/agents/${agent.filename}`, {
                        method: 'DELETE'
                    });
                    
                    showMessage(`Agent文件 '${agent.name}' 已成功删除!`, 'success');
                    
                    // 如果当前正在编辑这个文件，清空编辑器
                    if (currentEditingAgentFile === agent.filename) {
                        agentFileContentEditor.value = '';
                        agentFileStatusSpan.textContent = '';
                        saveAgentFileButton.disabled = true;
                        currentEditingAgentFile = null;
                    }
                    
                    // 刷新文件列表
                    await loadAgentFilesList();
                    await loadAgentFilesGrid();
                } catch (error) {
                    showMessage(`删除Agent文件失败: ${error.message}`, 'error');
                }
            }
        });
        
        // 卡片点击事件（编辑功能）
        card.addEventListener('click', () => {
            agentFileSelect.value = agent.filename;
            loadAgentFileContent(agent.filename);
        });
        
        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };
        
        const formatDate = (dateString) => {
            return new Date(dateString).toLocaleString('zh-CN');
        };
        
        card.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-anime-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                </div>
                <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-semibold text-gray-900 truncate">${agent.name}</h4>
                    <p class="text-xs text-gray-500 mt-1">大小: ${formatFileSize(agent.size)}</p>
                    <p class="text-xs text-gray-400 mt-1">修改: ${formatDate(agent.lastModified)}</p>
                    <div class="mt-2 text-xs text-gray-600 line-clamp-2">
                        ${agent.content.substring(0, 100)}${agent.content.length > 100 ? '...' : ''}
                    </div>
                </div>
            </div>
        `;
        
        // 添加删除按钮到卡片
        card.appendChild(deleteButton);
        
        return card;
    }

    async function loadAgentFilesList() {
        try {
            const data = await apiFetch(`${API_BASE_URL}/agents`);
            agentFileSelect.innerHTML = '<option value="">请选择一个文件...</option>'; // Reset
            
            if (data.success && data.agents && data.agents.length > 0) {
                data.agents.sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically
                data.agents.forEach(agent => {
                    const option = document.createElement('option');
                    option.value = agent.filename;
                    option.textContent = agent.name;
                    agentFileSelect.appendChild(option);
                });
            } else {
                agentFileSelect.innerHTML = '<option value="">没有找到 Agent 文件</option>';
                agentFileContentEditor.placeholder = '没有 Agent 文件可供编辑。';
            }
        } catch (error) {
            agentFileSelect.innerHTML = '<option value="">加载 Agent 文件列表失败</option>';
            showMessage('加载 Agent 文件列表失败: ' + error.message, 'error');
            agentFileContentEditor.placeholder = '加载 Agent 文件列表失败。';
        }
    }

    async function loadAgentFileContent(fileName) {
        if (!fileName) {
            agentFileContentEditor.value = '';
            agentFileStatusSpan.textContent = '请选择一个文件。';
            saveAgentFileButton.disabled = true;
            currentEditingAgentFile = null;
            agentFileContentEditor.placeholder = '选择一个 Agent 文件以编辑其内容...';
            return;
        }
        agentFileStatusSpan.textContent = `正在加载 ${fileName}...`;
        try {
            const data = await apiFetch(`${API_BASE_URL}/agents/${fileName}`);
            agentFileContentEditor.value = data.content;
            agentFileStatusSpan.textContent = `当前编辑: ${fileName}`;
            saveAgentFileButton.disabled = false;
            currentEditingAgentFile = fileName;
        } catch (error) {
            agentFileStatusSpan.textContent = `加载文件 ${fileName} 失败。`;
            showMessage(`加载文件 ${fileName} 失败: ${error.message}`, 'error');
            agentFileContentEditor.value = `无法加载文件: ${fileName}\n\n错误: ${error.message}`;
            saveAgentFileButton.disabled = true;
            currentEditingAgentFile = null;
        }
    }

    async function saveAgentFileContent() {
        if (!currentEditingAgentFile) {
            showMessage('没有选择要保存的文件。', 'error');
            return;
        }
        const content = agentFileContentEditor.value;
        agentFileStatusSpan.textContent = `正在保存 ${currentEditingAgentFile}...`;
        saveAgentFileButton.disabled = true;

        try {
            await apiFetch(`${API_BASE_URL}/agents/${currentEditingAgentFile}`, {
                method: 'POST',
                body: JSON.stringify({ content })
            });
            showMessage(`Agent 文件 '${currentEditingAgentFile}' 已成功保存!`, 'success');
            agentFileStatusSpan.textContent = `Agent 文件 '${currentEditingAgentFile}' 已保存。`;
        } catch (error) {
            agentFileStatusSpan.textContent = `保存文件 ${currentEditingAgentFile} 失败。`;
            // showMessage is handled by apiFetch
        } finally {
            saveAgentFileButton.disabled = false;
        }
    }

    // Event Listeners for Agent Files Editor
    if (agentFileSelect) {
        agentFileSelect.addEventListener('change', (event) => {
            loadAgentFileContent(event.target.value);
        });
    }
    if (saveAgentFileButton) {
        saveAgentFileButton.addEventListener('click', saveAgentFileContent);
    }

    // --- End Agent Files Editor Functions ---

    // --- Server Log Viewer Functions ---
    async function initializeServerLogViewer() {
        console.log('Initializing Server Log Viewer...');
        // Clear any existing interval before starting a new one or performing the initial load
        if (serverLogIntervalId) {
            clearInterval(serverLogIntervalId);
            serverLogIntervalId = null;
            console.log('Cleared existing server log auto-refresh interval during initialization.');
        }

        if (!serverLogViewerSection) {
            console.error('Server Log Viewer section not found in DOM.');
            return;
        }
        serverLogPathDisplay.textContent = '';
        serverLogStatusSpan.textContent = '';
        serverLogContentPre.innerHTML = '<div class="flex items-center space-x-2 text-gray-500"><div class="loading-spinner"></div><span>正在加载日志...</span></div>';

        await loadServerLog(); // Perform the initial load

        // Start polling after the initial load
        if (!serverLogIntervalId) {
            serverLogIntervalId = setInterval(loadServerLog, 2000); // Poll every 2 seconds
            console.log('Started server log auto-refresh interval.');
        }
        
        // 新增：绑定日志控制事件
        bindLogControls();
    }
    
    // 新增：绑定日志控制事件
    function bindLogControls() {
        const refreshLogsButton = document.getElementById('refresh-logs-button');
        const logLinesSelect = document.getElementById('log-lines-select');
        const logLevelFilter = document.getElementById('log-level-filter');
        const clearLogDisplay = document.getElementById('clear-log-display');
        
        if (refreshLogsButton) {
            refreshLogsButton.addEventListener('click', async () => {
                await loadServerLog();
                showMessage('日志已刷新', 'success');
            });
        }
        
        if (logLinesSelect) {
            logLinesSelect.addEventListener('change', loadServerLog);
        }
        
        if (logLevelFilter) {
            logLevelFilter.addEventListener('change', filterLogsByLevel);
        }
        
        if (clearLogDisplay) {
            clearLogDisplay.addEventListener('click', () => {
                serverLogContentPre.innerHTML = '<div class="text-gray-500">日志显示已清空</div>';
                showMessage('日志显示已清空', 'info');
            });
        }
    }
    
    // 新增：存储当前过滤状态
    let currentLogFilter = '';
    let rawLogContent = ''; // 存储原始日志内容

    // 新增：清理ANSI转义序列
    function cleanAnsiCodes(text) {
        // 移除ANSI转义序列 (如 [39m, [90m, [1m, [22m 等)
        return text.replace(/\x1b\[[0-9;]*[a-zA-Z]/g, '');
    }

    // 新增：根据级别过滤日志
    function filterLogsByLevel() {
        const logLevelFilter = document.getElementById('log-level-filter');
        const filterLevel = logLevelFilter ? logLevelFilter.value : '';
        currentLogFilter = filterLevel; // 保存当前过滤状态
        
        if (!filterLevel) {
            // 显示全部日志，使用已存储的原始内容
            if (rawLogContent) {
                const formattedLogs = rawLogContent.split('\n').map(line => formatLogLine(cleanAnsiCodes(line))).join('\n');
                serverLogContentPre.innerHTML = formattedLogs;
            }
            return;
        }
        
        if (!rawLogContent) {
            showMessage('请先加载日志内容', 'warning');
            return;
        }
        
        const logLines = rawLogContent.split('\n');
        const filteredLines = logLines.filter(line => {
            const cleanLine = cleanAnsiCodes(line);
            const lowerLine = cleanLine.toLowerCase();
            switch(filterLevel) {
                case 'error':
                    return lowerLine.includes('error') || lowerLine.includes('错误') || lowerLine.includes('[!]');
                case 'warning':
                    return lowerLine.includes('warning') || lowerLine.includes('warn') || lowerLine.includes('警告');
                case 'info':
                    return lowerLine.includes('info') || lowerLine.includes('[i]') || lowerLine.includes('请求');
                case 'success':
                    return lowerLine.includes('success') || lowerLine.includes('成功') || lowerLine.includes('[✓]');
                default:
                    return true;
            }
        });
        
        const processedLines = filteredLines.map(line => formatLogLine(cleanAnsiCodes(line))).join('\n');
        serverLogContentPre.innerHTML = processedLines || '<div class="text-gray-500">没有找到匹配的日志</div>';
    }
    
    // 新增：格式化日志行
    function formatLogLine(line) {
        if (!line.trim()) return '';
        
        // 确保清理了ANSI代码
        const cleanLine = cleanAnsiCodes(line);
        const lowerLine = cleanLine.toLowerCase();
        let className = 'log-line';
        
        if (lowerLine.includes('error') || lowerLine.includes('错误') || lowerLine.includes('[!]')) {
            className += ' error';
        } else if (lowerLine.includes('warning') || lowerLine.includes('warn') || lowerLine.includes('警告')) {
            className += ' warning';
        } else if (lowerLine.includes('success') || lowerLine.includes('成功') || lowerLine.includes('[✓]')) {
            className += ' success';
        } else if (lowerLine.includes('info') || lowerLine.includes('[i]') || lowerLine.includes('请求')) {
            className += ' info';
        }
        
        // HTML转义防止XSS
        const escapedLine = cleanLine
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
            
        return `<div class="${className}">${escapedLine}</div>`;
    }

    async function loadServerLog() {
        if (!serverLogContentPre || !serverLogStatusSpan || !serverLogPathDisplay) {
            console.error('Server log display elements not found.');
            return;
        }
        
        try {
            const logLinesSelect = document.getElementById('log-lines-select');
            const lines = logLinesSelect ? logLinesSelect.value : '100';
            
            const data = await apiFetch(`${API_BASE_URL}/server-logs?lines=${lines}`, { showLoading: false });
            
            if (data.success && data.logs && data.logs.length > 0) {
                // 存储原始日志内容（包含ANSI代码）
                rawLogContent = data.logs.join('\n');
                
                // 应用当前过滤器
                if (currentLogFilter) {
                    filterLogsByLevel();
                } else {
                    // 清理ANSI代码并格式化日志行
                    const formattedLogs = data.logs.map(line => formatLogLine(cleanAnsiCodes(line))).join('\n');
                    serverLogContentPre.innerHTML = formattedLogs;
                }
                
                serverLogPathDisplay.textContent = `当前日志文件: ${data.logFile || '未知'}`;
                serverLogStatusSpan.textContent = `实时更新中 (${data.returnedLines}/${data.totalLines} 行)`;
                serverLogStatusSpan.className = 'text-green-600 font-medium';
            } else {
                rawLogContent = '';
                serverLogContentPre.innerHTML = '<div class="text-gray-500">日志内容为空</div>';
                serverLogPathDisplay.textContent = '当前日志文件: 未知';
                serverLogStatusSpan.textContent = '无日志数据';
                serverLogStatusSpan.className = 'text-yellow-600 font-medium';
            }
            
            // Scroll to bottom
            const logContainer = serverLogContentPre.parentElement;
            if (logContainer) {
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        } catch (error) {
            rawLogContent = '';
            serverLogContentPre.innerHTML = `
                <div class="text-red-400">
                    <div class="font-semibold">加载服务器日志失败:</div>
                    <div class="mt-2">${error.message}</div>
                    <div class="mt-2 text-sm text-gray-400">
                        可能原因：服务器重启、日志文件路径更改或权限问题
                    </div>
                </div>
            `;
            serverLogPathDisplay.textContent = '当前日志文件: 未知';
            serverLogStatusSpan.textContent = '连接失败';
            serverLogStatusSpan.className = 'text-red-600 font-medium';
        }
    }

    // Event Listeners for Server Log Viewer
    if (copyServerLogButton) { // Changed from refreshServerLogButton
        copyServerLogButton.addEventListener('click', copyServerLogToClipboard);
    }

    async function copyServerLogToClipboard() {
        if (!serverLogContentPre) {
            showMessage('日志内容元素未找到。', 'error');
            return;
        }
        const logContent = serverLogContentPre.textContent;
        if (!logContent || logContent === '正在加载日志...' || logContent.startsWith('加载服务器日志失败')) {
            showMessage('没有可复制的日志内容。', 'info');
            return;
        }

        try {
            await navigator.clipboard.writeText(logContent);
            showMessage('日志内容已复制到剪贴板！', 'success');
            serverLogStatusSpan.textContent = '日志已复制!';
            serverLogStatusSpan.className = 'status-message success';
            setTimeout(() => {
                if (serverLogStatusSpan.textContent === '日志已复制!') {
                     serverLogStatusSpan.textContent = '日志已加载。'; // Revert after a few seconds
                }
            }, 3000);
        } catch (err) {
            console.error('无法自动复制日志: ', err);
            // Fallback: Try to select the text for manual copying
            try {
                serverLogContentPre.focus(); // Focus the element
                const range = document.createRange();
                range.selectNodeContents(serverLogContentPre);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
                showMessage('自动复制失败。日志内容已选中，请按 Ctrl+C (或 Cmd+C) 手动复制。', 'info', 5000);
                serverLogStatusSpan.textContent = '请手动复制';
            } catch (selectErr) {
                console.error('选择文本以便手动复制失败: ', selectErr);
                showMessage('自动复制失败，也无法选中内容供手动复制。请尝试手动选择并复制。', 'error', 5000);
                serverLogStatusSpan.textContent = '复制失败';
            }
            serverLogStatusSpan.className = 'status-message error'; // Keep error class for status
        }
    }
    // --- End Server Log Viewer Functions ---

    // 初始化性能检测和Live2D
    initializePerformanceAndLive2D();

    // 初始化世界树VCP状态检查
    initializeWorldTreeVCP();
});

// 初始化世界树VCP状态
async function initializeWorldTreeVCP() {
    try {
        const response = await fetch(`${window.API_BASE_URL}/worldtree/status`);
        const data = await response.json();

        const statusBadge = document.getElementById('worldtree-status-badge');
        if (statusBadge) {
            if (data.success && data.status.isInitialized) {
                statusBadge.textContent = '运行中';
                statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800';
            } else {
                statusBadge.textContent = '未初始化';
                statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800';
            }
        }
    } catch (error) {
        console.error('世界树VCP状态检查失败:', error);
        const statusBadge = document.getElementById('worldtree-status-badge');
        if (statusBadge) {
            statusBadge.textContent = '错误';
            statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800';
        }
    }
}

// === 微信适配器管理功能 ===

// 微信适配器状态管理
let wechatStatusInterval = null;
let currentWeChatTab = 'status';

// 切换微信适配器标签页 - 设置为全局函数
window.switchWeChatTab = function(tabName) {
    // 更新标签按钮状态
    document.querySelectorAll('.wechat-tab-btn').forEach(btn => {
        btn.classList.remove('border-green-500', 'text-green-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });

    const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeBtn) {
        activeBtn.classList.remove('border-transparent', 'text-gray-500');
        activeBtn.classList.add('border-green-500', 'text-green-600');
    }

    // 隐藏所有标签页内容
    document.querySelectorAll('.wechat-tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // 显示当前标签页内容
    const activeContent = document.getElementById(`wechat-tab-${tabName}`);
    if (activeContent) {
        activeContent.classList.remove('hidden');
    }

    currentWeChatTab = tabName;

    // 根据标签页加载相应数据
    switch (tabName) {
        case 'status':
            window.loadWeChatStatus();
            // 启动智能缓存监控
            setTimeout(() => {
                if (window.cacheMonitorManager) {
                    window.cacheMonitorManager.startMonitoring();
                }
            }, 500);
            break;
        case 'config':
            window.loadWeChatConfig();
            break;
        case 'logs':
            loadWeChatLogUsers();
            break;
        case 'listeners':
            loadWeChatListeners();
            break;
    }
}

// 刷新微信状态 - 设置为全局函数
window.refreshWeChatStatus = async function() {
    await window.loadWeChatStatus();
    window.showMessage('微信适配器状态已刷新', 'success');
}

// 加载微信适配器状态 - 设置为全局函数
window.loadWeChatStatus = async function loadWeChatStatus() {
    console.log('🔍 开始加载微信适配器状态...');
    try {
        // 同时检查进程状态和API状态
        console.log('📡 发起状态检查请求...');
        const [processStatus, apiStatus] = await Promise.allSettled([
            window.getWeChatProcessStatus(),
            window.apiFetch(`${window.API_BASE_URL}/wechat/status`)
        ]);

        console.log('📊 状态检查结果:', { processStatus, apiStatus });

        // 解析进程状态
        const processRunning = processStatus.status === 'fulfilled' && processStatus.value.running;
        const processPid = processRunning ? processStatus.value.pid : null;
        console.log('🔧 进程状态:', { processRunning, processPid });

        // 解析API状态 - 处理双层嵌套格式
        let apiData = null;
        let apiConnected = false;
        if (apiStatus.status === 'fulfilled' && apiStatus.value.success) {
            console.log('✅ API响应成功，解析数据...');
            // 处理双层嵌套：{success: true, data: {success: true, data: {...}}}
            if (apiStatus.value.data && apiStatus.value.data.data) {
                apiData = apiStatus.value.data.data;
                apiConnected = true;
                console.log('📦 使用双层嵌套数据:', apiData);
            } else if (apiStatus.value.data) {
                // 兼容单层格式
                apiData = apiStatus.value.data;
                apiConnected = true;
                console.log('📦 使用单层数据:', apiData);
            }
        } else {
            console.error('❌ API状态检查失败:', apiStatus);
        }

        // 更新状态指示器
        console.log('🎯 查找状态显示元素...');
        const statusIndicator = document.getElementById('wechat-status-indicator');
        const statusText = document.getElementById('wechat-status-text');
        const statusBadge = document.getElementById('wechat-status-badge');

        console.log('🔍 状态元素查找结果:', {
            statusIndicator: !!statusIndicator,
            statusText: !!statusText,
            statusBadge: !!statusBadge
        });

        if (!statusIndicator || !statusText || !statusBadge) {
            console.error('❌ 状态显示元素未找到！');
            return;
        }

        // 综合判断状态
        const hasListeners = apiData && apiData.listeners && apiData.listeners.length > 0;
        const botRunning = apiData && apiData.bot_running === true;
        const wechatConnected = apiData && apiData.wechat_connected === true;

        console.log('⚖️ 综合判断状态:', {
            processRunning,
            apiConnected,
            botRunning,
            wechatConnected,
            hasListeners,
            listeners_count: apiData?.listeners_count,
            raw_data: apiData
        });

        if (processRunning && apiConnected && botRunning && wechatConnected && hasListeners) {
            // 完全在线 - 进程+API+机器人+微信+监听器都正常
            console.log('🟢 状态：完全在线运行');
            statusIndicator.className = 'w-3 h-3 rounded-full mr-2 bg-green-400';
            statusText.textContent = '在线运行';
            statusBadge.textContent = '在线';
            statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800';
        } else if (processRunning && apiConnected && botRunning && wechatConnected) {
            // 服务运行，微信连接，但没有监听器
            console.log('🟡 状态：服务运行，等待监听');
            statusIndicator.className = 'w-3 h-3 rounded-full mr-2 bg-yellow-400';
            statusText.textContent = '等待监听';
            statusBadge.textContent = '等待监听';
            statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800';
        } else if (processRunning && apiConnected && botRunning) {
            // 服务运行但微信未连接
            console.log('🔵 状态：微信未连接');
            statusIndicator.className = 'w-3 h-3 rounded-full mr-2 bg-blue-400';
            statusText.textContent = '微信未连接';
            statusBadge.textContent = '微信未连接';
            statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
        } else if (processRunning && apiConnected) {
            // 进程和API都在线但机器人未启动
            console.log('🟠 状态：机器人未启动');
            statusIndicator.className = 'w-3 h-3 rounded-full mr-2 bg-orange-400';
            statusText.textContent = '机器人未启动';
            statusBadge.textContent = '未启动';
            statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800';
        } else if (processRunning) {
            // 只有进程在运行
            console.log('🟠 状态：进程启动中');
            statusIndicator.className = 'w-3 h-3 rounded-full mr-2 bg-orange-400';
            statusText.textContent = '进程启动中';
            statusBadge.textContent = '启动中';
            statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800';
        } else {
            // 完全离线
            console.log('🔴 状态：完全离线');
            statusIndicator.className = 'w-3 h-3 rounded-full mr-2 bg-red-400';
            statusText.textContent = '离线';
            statusBadge.textContent = '离线';
            statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800';
        }

        // 更新状态卡片
        const serviceStatus = document.getElementById('wechat-service-status');
        const connectionStatus = document.getElementById('wechat-connection-status');
        const listenersCount = document.getElementById('wechat-listeners-count');

        if (serviceStatus) {
            if (processRunning && apiConnected && apiData.bot_running) {
                serviceStatus.textContent = '服务正常运行';
            } else if (processRunning && apiConnected) {
                serviceStatus.textContent = '服务已启动，监听未开始';
            } else if (processRunning) {
                serviceStatus.textContent = `进程运行中 (PID: ${processPid})`;
            } else {
                serviceStatus.textContent = '服务未运行';
            }
        }

        if (connectionStatus) {
            if (apiConnected && apiData && apiData.wechat_connected) {
                connectionStatus.textContent = '微信已连接';
            } else if (apiConnected) {
                connectionStatus.textContent = 'API已连接，微信未连接';
            } else if (processRunning) {
                connectionStatus.textContent = '进程启动中，等待API响应';
            } else {
                connectionStatus.textContent = '微信未连接';
            }
        }

        if (listenersCount) {
            const count = apiData ? (apiData.listeners_count || 0) : 0;
            listenersCount.textContent = `${count} 个`;
        }

        // 更新监听状态列表 - 需要获取详细的监听器状态
        if (apiData && apiData.listeners && apiData.listeners.length > 0) {
            // 异步获取详细的监听器状态
            loadDetailedListenersStatus();
        } else {
            updateWeChatListenersStatus([]);
        }

        // 输出详细状态信息到控制台
        console.log('微信适配器状态更新:', {
            processRunning,
            processPid,
            apiConnected,
            apiData: apiData ? {
                bot_running: apiData.bot_running,
                wechat_connected: apiData.wechat_connected,
                listeners_count: apiData.listeners_count
            } : null
        });

    } catch (error) {
        console.error('加载微信状态失败:', error);
        // 设置为错误状态
        const statusIndicator = document.getElementById('wechat-status-indicator');
        const statusText = document.getElementById('wechat-status-text');
        const statusBadge = document.getElementById('wechat-status-badge');

        if (statusIndicator) statusIndicator.className = 'w-3 h-3 rounded-full mr-2 bg-gray-400';
        if (statusText) statusText.textContent = '状态检查失败';
        if (statusBadge) {
            statusBadge.textContent = '错误';
            statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800';
        }
    }
}

// 加载详细的监听器状态
async function loadDetailedListenersStatus() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/listeners/status`);
        // 处理双层嵌套格式
        let data = response.data;
        if (data && data.data) {
            data = data.data;
        }

        if (data && data.listeners) {
            updateWeChatListenersStatus(data.listeners);
        } else {
            updateWeChatListenersStatus([]);
        }
    } catch (error) {
        console.error('获取详细监听器状态失败:', error);
        updateWeChatListenersStatus([]);
    }
}

// 更新监听状态显示
function updateWeChatListenersStatus(listeners) {
    const container = document.getElementById('wechat-listeners-status');

    if (!listeners || listeners.length === 0) {
        container.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <p class="mt-2">暂无监听对象</p>
            </div>
        `;
        return;
    }

    container.innerHTML = listeners.map(listener => `
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center">
                <div class="w-3 h-3 rounded-full mr-3 ${listener.is_active ? 'bg-green-400' : 'bg-gray-400'}"></div>
                <div>
                    <h4 class="font-medium text-gray-900">${listener.user_name}</h4>
                    <p class="text-sm text-gray-500">${listener.user_type === 'private' ? '私聊' : '群聊'}</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-sm font-medium text-gray-900">${listener.message_count || 0} 条消息</p>
                <p class="text-xs text-gray-500">${listener.last_message_time ? new Date(listener.last_message_time).toLocaleString() : '无消息'}</p>
            </div>
        </div>
    `).join('');
}

// Live2D 功能
let live2dModel = null;
let currentModelIndex = 0;
let isLive2DVisible = true;
let modelList = [];
let messageList = [];
let waifuMessages = {};

// 从配置文件中加载的模型列表
const live2dModelPaths = [
    'Alice/model.json',
    'platelet-0/model.json', 
    'sagiri/index.json',
    'chino/index.json',
    '小埋/index.json',
    'tororo/tororo.model.json',
    'bronya/model.json',
    'sakura/model.json',
    'unitychan/unitychan.model.json',
    'Violet/14.json'
];

// 初始化性能检测和Live2D的综合函数
async function initializePerformanceAndLive2D() {
    try {
        console.log('开始初始化性能检测和Live2D系统...');
        
        // 执行性能检测
        await devicePerformanceDetector.performFullDetection();
        
        // 创建设置面板
        devicePerformanceDetector.createSettingsPanel();
        
        // 判断是否应该启用Live2D
        const shouldEnable = devicePerformanceDetector.shouldEnableLive2D();
        
        if (shouldEnable) {
            console.log('设备性能良好，启用Live2D');
            await initializeLive2D();
        } else {
            console.log('检测到低端设备，禁用Live2D以优化性能');
            disableLive2DForLowEndDevice();
        }
        
        // 添加设置按钮（无论是否启用Live2D都添加）
        setTimeout(() => {
            devicePerformanceDetector.addSettingsButton();
        }, 1000);
        
    } catch (error) {
        console.error('性能检测和Live2D初始化失败:', error);
        // 回退到默认Live2D初始化
        await initializeLive2D();
    }
}

// 为低端设备禁用Live2D
function disableLive2DForLowEndDevice() {
    const widget = document.getElementById('live2d-widget');
    if (widget) {
        widget.style.display = 'none';
        
        // 创建性能优化提示
        const notice = document.createElement('div');
        notice.id = 'low-end-device-notice';
        notice.className = 'fixed bottom-4 right-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded shadow-lg z-30';
        notice.style.maxWidth = '300px';
        notice.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm">
                        <strong>性能优化提示</strong><br>
                        检测到您的设备配置较低，已自动禁用Live2D以提升页面性能。
                        您可以通过设置按钮手动启用。
                    </p>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" class="mt-2 text-xs text-yellow-600 hover:text-yellow-800 underline">关闭提示</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(notice);
        
        // 5秒后自动隐藏提示
        setTimeout(() => {
            if (notice.parentNode) {
                notice.remove();
            }
        }, 8000);
    }
}

async function initializeLive2D() {
    try {
        // 加载配置文件
        await loadLive2DConfig();
        
        // 控制面板由Live2DManager处理
        
        // 检查Live2D库是否加载
        if (typeof Live2D === 'undefined') {
            console.warn('Live2D库未加载，使用增强版本');
            await initializeEnhancedLive2D();
            return;
        }

        const canvas = document.getElementById('live2d-canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        if (!gl) {
            console.warn('WebGL不支持，使用增强版本');
            await initializeEnhancedLive2D();
            return;
        }

        // 初始化Live2D框架
        Live2D.init();
        
        // 加载第一个模型
        await loadRealLive2DModel(live2dModelPaths[currentModelIndex]);
        
        console.log('Live2D初始化成功');
    } catch (error) {
        console.error('Live2D初始化失败，使用增强版本:', error);
        console.error('错误详情:', error.stack);
        await initializeEnhancedLive2D();
    }
    
    // 控制面板由Live2DManager处理
    
    // 只有在按钮没有绑定时才绑定事件
    const toggleBtn = document.getElementById('live2d-toggle');
    if (toggleBtn && !toggleBtn._live2dBound) {
        bindLive2DControls();
    }
}

// 加载Live2D配置文件
async function loadLive2DConfig() {
    try {
        // 加载模型列表
        const modelListResponse = await fetch('/live2d/model_list.json');
        const modelListData = await modelListResponse.json();
        modelList = modelListData.models || [];
        messageList = modelListData.messages || [];
        
        // 加载交互消息
        try {
            const waifuResponse = await fetch('/live2d/waifu.json');
            waifuMessages = await waifuResponse.json();
        } catch (error) {
            console.warn('waifu.json加载失败，使用默认消息:', error);
            waifuMessages = {
                "mouseover": [{"text": "你好呀！"}, {"text": "点击我试试看！"}],
                "click": [{"text": "哇！你点了我！"}, {"text": "好痒啊！"}]
            };
        }
        
        console.log('Live2D配置加载成功');
        console.log(`✅ 加载了 ${modelList.length} 个模型配置`);
        console.log(`✅ 加载了 ${messageList.length} 条消息`);
        console.log(`✅ 加载了 ${Object.keys(waifuMessages).length} 种交互类型`);
    } catch (error) {
        console.error('Live2D配置加载失败:', error);
    }
}

async function initializeEnhancedLive2D() {
    // 控制面板由Live2DManager处理

    // 首先尝试初始化真正的Live2D
    const success = await initializeRealLive2D();
    if (!success) {
        console.log('Live2D库未加载，显示提示信息');
        showLive2DFallback();
    }

    // 控制面板由Live2DManager处理
}

// 初始化真正的Live2D渲染
async function initializeRealLive2D() {
    try {
        // 检查是否有L2Dwidget
        if (typeof L2Dwidget !== 'undefined') {
            console.log('使用L2Dwidget初始化Live2D');
            
            // 等待DOM完全加载
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });
            
            L2Dwidget.init({
                "model": {
                    "jsonPath": `/live2d/model/${live2dModelPaths[currentModelIndex]}`,
                    "scale": 1
                },
                "display": {
                    "position": "right", 
                    "width": 280,
                    "height": 250,
                    "hOffset": 0,
                    "vOffset": 0
                },
                "mobile": {
                    "show": true,
                    "scale": 0.8
                },
                "react": {
                    "opacity": 0.8
                },
                "dev": {
                    "border": false
                },
                "dialog": {
                    "enable": false
                },
                "log": false
            });
            
            // 移动L2Dwidget到我们的容器中
            setTimeout(() => {
                const l2dCanvas = document.querySelector('#L2dCanvas');
                const widget = document.getElementById('live2d-widget');
                if (l2dCanvas && widget) {
                    // 创建新的canvas替换我们的
                    const ourCanvas = document.getElementById('live2d-canvas');
                    if (ourCanvas) {
                        ourCanvas.style.display = 'none';
                    }
                    
                    // 调整L2Dwidget样式，使其在我们的容器内
                    l2dCanvas.style.position = 'absolute';
                    l2dCanvas.style.bottom = '0';
                    l2dCanvas.style.right = '0';
                    l2dCanvas.style.width = '280px';
                    l2dCanvas.style.height = '250px';
                    l2dCanvas.style.pointerEvents = 'auto';
                    l2dCanvas.style.zIndex = '1';
                    
                    // 将L2D canvas移动到我们的widget容器中
                    widget.appendChild(l2dCanvas);
                    console.log('L2Dwidget已移动到widget容器中');
                }
            }, 2000);
            
            return true;
        }
        
        // 检查是否有传统的loadlive2d函数
        if (typeof loadlive2d !== 'undefined') {
            console.log('使用传统loadlive2d加载模型');
            try {
                loadlive2d('live2d-canvas', `/live2d/model/${live2dModelPaths[currentModelIndex]}`);
                console.log(`模型加载成功: ${live2dModelPaths[currentModelIndex]}`);
                return true;
            } catch (error) {
                console.error('传统loadlive2d加载失败:', error);
            }
        }
        
        // 尝试手动加载Live2D库
        try {
            await loadLive2DLibrary();
            
            // 再次检查
            if (typeof loadlive2d !== 'undefined') {
                loadlive2d('live2d-canvas', `/live2d/model/${live2dModelPaths[currentModelIndex]}`);
                console.log(`手动加载模型成功: ${live2dModelPaths[currentModelIndex]}`);
                return true;
            }
        } catch (error) {
            console.error('手动加载Live2D库失败:', error);
        }
        
        console.log('所有Live2D加载方式都失败，将显示占位符');
        return false;
        
    } catch (error) {
        console.error('Live2D初始化失败:', error);
        return false;
    }
}

// 动态加载Live2D库
async function loadLive2DLibrary() {
    return new Promise((resolve, reject) => {
        // 检查是否已经加载
        if (typeof loadlive2d !== 'undefined') {
            resolve();
            return;
        }
        
        // 创建script标签加载Live2D widget
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/gh/fghrsh/live2d_demo@master/assets/autoload.js';
        script.onload = () => {
            console.log('Live2D库加载成功');
            resolve();
        };
        script.onerror = () => {
            console.log('从CDN加载失败，尝试本地库');
            // 尝试加载本地库
            loadLocalLive2D().then(resolve).catch(reject);
        };
        document.head.appendChild(script);
    });
}

// 加载本地Live2D库
async function loadLocalLive2D() {
    return new Promise((resolve, reject) => {
        // 创建简化的Live2D Widget
        window.loadlive2d = function(canvasId, modelPath) {
            console.log(`加载Live2D模型: ${modelPath}`);
            const canvas = document.getElementById(canvasId);
            if (!canvas) {
                reject(new Error('Canvas not found'));
                return;
            }
            
            // 设置透明背景
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 加载模型数据
            loadModelData(modelPath).then(modelData => {
                renderLive2DModel(canvas, modelData);
                resolve();
            }).catch(reject);
        };
        resolve();
    });
}

// 加载模型数据
async function loadModelData(modelPath) {
    try {
        // 确保路径正确，modelPath应该已经包含../live2d/model/前缀
        const response = await fetch(modelPath);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const modelData = await response.json();
        
        // 预加载纹理
        if (modelData.textures && modelData.textures.length > 0) {
            const baseDir = modelPath.substring(0, modelPath.lastIndexOf('/') + 1);
            const texturePromises = modelData.textures.map(texturePath => {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    img.onload = () => resolve(img);
                    img.onerror = reject;
                    img.src = baseDir + texturePath;
                });
            });
            
            modelData.loadedTextures = await Promise.all(texturePromises);
        }
        
        return modelData;
    } catch (error) {
        console.error('加载模型数据失败:', error);
        throw error;
    }
}

// 渲染Live2D模型
function renderLive2DModel(canvas, modelData) {
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // 清除画布
    ctx.clearRect(0, 0, width, height);
    
    // 如果有纹理，绘制纹理
    if (modelData.loadedTextures && modelData.loadedTextures.length > 0) {
        const texture = modelData.loadedTextures[0];
        
        // 计算缩放和位置
        const layout = modelData.layout || {};
        const scale = Math.min(width / texture.width, height / texture.height) * 0.8;
        const x = (width - texture.width * scale) / 2;
        const y = (height - texture.height * scale) / 2;
        
        // 绘制纹理
        ctx.save();
        ctx.drawImage(texture, x, y, texture.width * scale, texture.height * scale);
        ctx.restore();
        
        // 添加动画效果
        let animationFrame = 0;
        const animate = () => {
            ctx.clearRect(0, 0, width, height);
            
            // 添加轻微的浮动效果
            const floatY = Math.sin(animationFrame * 0.02) * 5;
            ctx.drawImage(texture, x, y + floatY, texture.width * scale, texture.height * scale);
            
            animationFrame++;
            requestAnimationFrame(animate);
        };
        
        animate();
        
        console.log('Live2D模型渲染成功');
    } else {
        // 如果没有纹理，显示占位符
        showModelPlaceholder(ctx, width, height, modelData);
    }
}

// 显示模型占位符
function showModelPlaceholder(ctx, width, height, modelData) {
    const currentModel = getCurrentModelInfo();
    
    // 绘制渐变背景
    const gradient = ctx.createRadialGradient(width/2, height/2, 0, width/2, height/2, Math.min(width, height)/2);
    gradient.addColorStop(0, 'rgba(255, 107, 157, 0.1)');
    gradient.addColorStop(1, 'rgba(196, 181, 253, 0.1)');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    
    // 绘制模型名称
    ctx.fillStyle = '#ff6b9d';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(currentModel.name, width/2, height/2 - 20);
    
    // 绘制表情符号
    ctx.font = '48px Arial';
    ctx.fillText(currentModel.emoji, width/2, height/2 + 30);
    
    // 添加浮动动画
    let animationFrame = 0;
    const animate = () => {
        ctx.clearRect(0, 0, width, height);
        
        // 重绘背景
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        
        // 浮动效果
        const floatY = Math.sin(animationFrame * 0.03) * 8;
        
        // 重绘文本
        ctx.fillStyle = '#ff6b9d';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(currentModel.name, width/2, height/2 - 20 + floatY);
        
        ctx.font = '48px Arial';
        ctx.fillText(currentModel.emoji, width/2, height/2 + 30 + floatY);
        
        animationFrame++;
        requestAnimationFrame(animate);
    };
    
    animate();
}

// 旧的ensureLive2DControls函数已移除 - 现在由Live2DManager处理

// 显示Live2D回退界面
function showLive2DFallback() {
    const widget = document.getElementById('live2d-widget');
    
    // 先清空widget的内容，但保留控制面板
    const existingControls = document.getElementById('live2d-controls');
    const existingBubble = document.getElementById('live2d-message-bubble');
    
    // 创建fallback内容
    const fallbackDiv = document.createElement('div');
    fallbackDiv.className = 'live2d-fallback';
    fallbackDiv.style.cssText = `
        width: 100%; 
        height: 100%; 
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #666;
        font-size: 14px;
        text-align: center;
        padding: 20px;
        backdrop-filter: blur(10px);
        border: 2px dashed rgba(255, 107, 157, 0.3);
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
    `;
    
    fallbackDiv.innerHTML = `
        <div style="font-size: 32px; margin-bottom: 10px;">🤖</div>
        <div style="margin-bottom: 8px; font-weight: bold;">Live2D 未加载</div>
        <div style="font-size: 12px; line-height: 1.4;">
            请确保 live2d.min.js<br/>
            库文件已正确加载
        </div>
    `;
    
    // 清空widget但保留canvas
    const canvas = document.getElementById('live2d-canvas');
    widget.innerHTML = '';
    
    // 重新添加canvas和fallback
    if (canvas) {
        widget.appendChild(canvas);
    }
    widget.appendChild(fallbackDiv);
    
    // 控制面板由Live2DManager处理
}

// 获取当前模型信息
function getCurrentModelInfo() {
    const modelPath = live2dModelPaths[currentModelIndex];
    const modelName = modelPath.split('/')[0];
    const displayNames = {
        'Alice': '爱丽丝',
        'platelet-0': '血小板',
        'sagiri': '纱雾',
        'chino': '智乃',
        '小埋': '小埋',
        'tororo': '白猫',
        'bronya': '布洛妮娅',
        'sakura': '樱花',
        'unitychan': 'Unity酱',
        'Violet': '紫罗兰'
    };
    
    return {
        name: displayNames[modelName] || modelName,
        path: modelPath,
        emoji: getModelEmoji()
    };
}

// 获取模型对应的表情符号
function getModelEmoji() {
    const emojis = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
    return emojis[currentModelIndex % emojis.length];
}

// 加载真实Live2D模型（如果支持）
async function loadRealLive2DModel(modelPath) {
    try {
        console.log(`切换到模型: ${modelPath}`);
        
        // 如果有L2Dwidget，使用它
        if (typeof L2Dwidget !== 'undefined') {
            console.log(`使用L2Dwidget切换模型: ${modelPath}`);
            
            try {
                // 销毁现有实例
                if (window.L2Dwidget && window.L2Dwidget.theRealInit) {
                    const existingCanvas = document.querySelector('#L2dCanvas');
                    if (existingCanvas && existingCanvas.parentNode) {
                        existingCanvas.parentNode.removeChild(existingCanvas);
                    }
                }
                
                L2Dwidget.init({
                    "model": {
                        "jsonPath": `/live2d/model/${modelPath}`,
                        "scale": 1
                    },
                    "display": {
                        "position": "right",
                        "width": 280,
                        "height": 250,
                        "hOffset": 0,
                        "vOffset": 0
                    },
                    "mobile": {
                        "show": true,
                        "scale": 0.8
                    },
                    "react": {
                        "opacity": 0.8
                    },
                    "dev": {
                        "border": false
                    },
                    "dialog": {
                        "enable": false
                    },
                    "log": false
                });
                
                // 重新移动到我们的容器
                setTimeout(() => {
                    const l2dCanvas = document.querySelector('#L2dCanvas');
                    const widget = document.getElementById('live2d-widget');
                    if (l2dCanvas && widget) {
                        l2dCanvas.style.position = 'absolute';
                        l2dCanvas.style.bottom = '0';
                        l2dCanvas.style.right = '0';
                        l2dCanvas.style.width = '280px';
                        l2dCanvas.style.height = '250px';
                        l2dCanvas.style.pointerEvents = 'auto';
                        l2dCanvas.style.zIndex = '1';
                        widget.appendChild(l2dCanvas);
                        console.log(`L2Dwidget模型 ${modelPath} 切换成功`);
                    }
                }, 1500);
                
                return true;
            } catch (error) {
                console.error('L2Dwidget切换失败:', error);
                return false;
            }
        }
        
        // 如果有传统的loadlive2d函数，直接使用
        if (typeof loadlive2d !== 'undefined') {
            console.log('使用传统loadlive2d切换模型');
            loadlive2d('live2d-canvas', `/live2d/model/${modelPath}`);
            return true;
        }
        
        // 否则使用我们自己的加载函数
        if (typeof window.loadlive2d !== 'undefined') {
            console.log('使用自定义loadlive2d切换模型');
            window.loadlive2d('live2d-canvas', `/live2d/model/${modelPath}`);
            return true;
        }
        
        console.log('所有Live2D库都不可用，使用Canvas渲染');
        return false;
    } catch (error) {
        console.error(`Live2D模型 ${modelPath} 加载失败:`, error);
        return false;
    }
}

// 更新增强版本的模型显示
async function updateEnhancedModel() {
    const currentModel = getCurrentModelInfo();
    const modelAvatar = document.querySelector('.model-avatar');
    const modelName = document.querySelector('.model-name');
    
    if (modelAvatar) {
        // 更新表情符号
        modelAvatar.textContent = currentModel.emoji;
        
        // 添加切换动画
        modelAvatar.style.transform = 'scale(1.2) rotate(360deg)';
        setTimeout(() => {
            modelAvatar.style.transform = 'scale(1) rotate(0deg)';
        }, 300);
    }
    
    if (modelName) {
        modelName.textContent = currentModel.name;
    }
    
    // 显示切换消息
    showModelMessage(`你好，我是${currentModel.name}！`);
    
    return true;
}

function bindLive2DControls() {
    console.log('🔄 开始绑定Live2D控制事件...');
    
    // 控制面板由Live2DManager处理
    
    // 等待DOM元素准备就绪
    setTimeout(() => {
        const toggleBtn = document.getElementById('live2d-toggle');
        const switchBtn = document.getElementById('live2d-switch');
        const chooseBtn = document.getElementById('live2d-choose');
        const homeBtn = document.getElementById('live2d-home');
        const messageBtn = document.getElementById('live2d-message');
        const widget = document.getElementById('live2d-widget');
        
        console.log('🔍 检查控制按钮:', {
            toggle: !!toggleBtn,
            switch: !!switchBtn,
            choose: !!chooseBtn,
            home: !!homeBtn,
            message: !!messageBtn,
            widget: !!widget
        });
        
        if (!toggleBtn || !switchBtn || !homeBtn || !widget) {
            console.error('❌ Live2D控制按钮未找到，稍后重试');
            setTimeout(() => bindLive2DControls(), 500);
            return;
        }
        
        // 清除现有事件监听器，避免重复绑定
        if (toggleBtn._live2dBound) {
            console.log('⚠️ 按钮已绑定，跳过重复绑定');
            return;
        }
        
        // 显示/隐藏按钮
        toggleBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🎯 显示/隐藏按钮被点击，当前状态:', isLive2DVisible);
            
            const canvas = document.getElementById('live2d-canvas');
            const l2dCanvas = document.querySelector('#L2dCanvas');
            const fallback = document.querySelector('.live2d-fallback');
            const placeholder = document.querySelector('.live2d-placeholder');
            
            if (isLive2DVisible) {
                // 隐藏Live2D
                console.log('🙈 隐藏Live2D');
                [canvas, l2dCanvas, fallback, placeholder].forEach(el => {
                    if (el) el.style.display = 'none';
                });
                isLive2DVisible = false;
                toggleBtn.textContent = '👻';
                toggleBtn.title = '显示';
                showMessage('Live2D已隐藏', 'info', 1500);
            } else {
                // 显示Live2D
                console.log('👁️ 显示Live2D');
                [canvas, l2dCanvas, fallback, placeholder].forEach(el => {
                    if (el) el.style.display = 'block';
                });
                isLive2DVisible = true;
                toggleBtn.textContent = '👁';
                toggleBtn.title = '隐藏';
                showMessage('Live2D已显示', 'info', 1500);
            }
        });
        
        // 切换模型按钮
        switchBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🎯 切换模型按钮被点击');
            
            if (switchBtn.disabled) {
                console.log('⚠️ 按钮被禁用，忽略点击');
                return;
            }
            
            currentModelIndex = (currentModelIndex + 1) % live2dModelPaths.length;
            switchBtn.disabled = true;
            switchBtn.textContent = '⏳';
            
            try {
                console.log(`🔄 切换到模型索引: ${currentModelIndex}`);
                const success = await loadRealLive2DModel(live2dModelPaths[currentModelIndex]);
                if (!success) {
                    console.log('🔄 使用增强版本更新模型');
                    await updateEnhancedModel();
                }
                
                const currentModel = getCurrentModelInfo();
                showMessage(`已切换到 ${currentModel.name} 模型`, 'success', 2000);
                console.log(`✅ 模型切换成功: ${currentModel.name}`);
            } catch (error) {
                console.error('❌ 模型切换失败:', error);
                showMessage('模型切换失败', 'error', 2000);
            } finally {
                switchBtn.disabled = false;
                switchBtn.textContent = '🔄';
            }
        });
        
        // 重新加载按钮
        homeBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🎯 重新加载按钮被点击');
            
            if (homeBtn.disabled) {
                console.log('⚠️ 按钮被禁用，忽略点击');
                return;
            }
            
            homeBtn.disabled = true;
            homeBtn.textContent = '⏳';
            
            try {
                console.log('🔄 重新初始化Live2D系统...');
                showModelMessage('正在重新加载...');
                
                // 重新初始化Live2D
                await initializeLive2D();
                
                showModelMessage('重新加载完成！');
                showMessage('Live2D系统重新加载成功', 'success', 2000);
                console.log('✅ Live2D系统重新加载成功');
            } catch (error) {
                console.error('❌ 重新加载失败:', error);
                showModelMessage('重新加载失败！');
                showMessage('Live2D系统重新加载失败', 'error', 2000);
            } finally {
                homeBtn.disabled = false;
                homeBtn.textContent = '🔄';
                homeBtn.title = '重新加载';
            }
        });
        
        // 选择模型按钮
        if (chooseBtn) {
            chooseBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🎯 选择模型按钮被点击');
                showModelSelector();
            });
        }
        
        // 说话按钮
        if (messageBtn) {
            messageBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🎯 说话按钮被点击');
                showRandomMessage();
            });
        }
        
        // 标记已绑定
        toggleBtn._live2dBound = true;
        switchBtn._live2dBound = true;
        homeBtn._live2dBound = true;
        if (chooseBtn) chooseBtn._live2dBound = true;
        if (messageBtn) messageBtn._live2dBound = true;
        
        console.log('✅ Live2D控制事件绑定完成');
        
        // 初始化模型选择器
        initializeModelSelector();
        
        // 绑定模型点击交互
        setTimeout(() => {
            const clickableElements = [
                document.querySelector('.live2d-placeholder'),
                document.querySelector('#live2d-canvas'),
                document.querySelector('#L2dCanvas'),
                document.querySelector('.live2d-fallback')
            ].filter(Boolean);
            
            clickableElements.forEach(element => {
                if (element && !element._live2dClickBound) {
                    console.log(`✅ 绑定模型交互事件: ${element.className || element.id}`);
                    
                    element.addEventListener('click', (e) => {
                        e.stopPropagation();
                        if (isLive2DVisible) {
                            console.log('🎯 模型被点击');
                            handleModelClick();
                        }
                    });
                    
                    element.addEventListener('mouseenter', () => {
                        if (isLive2DVisible) {
                            console.log('🎯 鼠标悬停在模型上');
                            handleModelHover();
                        }
                    });
                    
                    element._live2dClickBound = true;
                }
            });
        }, 200);
        
    }, 50);
}

// 显示模型消息
function showModelMessage(message) {
    const bubble = document.getElementById('live2d-message-bubble');
    const text = document.getElementById('live2d-message-text');
    
    if (bubble && text) {
        text.textContent = message;
        bubble.style.opacity = '1';
        bubble.style.transform = 'scale(1)';
        
        setTimeout(() => {
            bubble.style.opacity = '0';
            bubble.style.transform = 'scale(0.75)';
        }, 3000);
    }
}

// 显示随机消息
function showRandomMessage() {
    const messages = [
        '你好呀～',
        '今天天气不错呢',
        '要一起聊天吗？',
        '我今天很开心哦',
        '你在忙什么呢？',
        '记得要好好休息哦',
        '要不要一起玩游戏？',
        '今天学到什么新东西了吗？'
    ];
    
    // 如果有加载的waifu消息，使用那些
    if (waifuMessages.click && waifuMessages.click.length > 0) {
        const clickMessages = waifuMessages.click.find(item => 
            item.selector === '#waifu #live2d' || item.selector === '.live2d-placeholder'
        );
        if (clickMessages && clickMessages.text) {
            const randomMsg = clickMessages.text[Math.floor(Math.random() * clickMessages.text.length)];
            showModelMessage(randomMsg);
            return;
        }
    }
    
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    showModelMessage(randomMessage);
}

// 处理模型点击
function handleModelClick() {
    const modelAvatar = document.querySelector('.model-avatar');
    if (modelAvatar) {
        // 点击动画
        modelAvatar.style.transform = 'scale(1.1)';
        setTimeout(() => {
            modelAvatar.style.transform = 'scale(1)';
        }, 200);
    }
    
    showRandomMessage();
}

// 处理模型悬停
function handleModelHover() {
    const modelAvatar = document.querySelector('.model-avatar');
    if (modelAvatar) {
        modelAvatar.style.filter = 'brightness(1.1) saturate(1.2)';
        setTimeout(() => {
            modelAvatar.style.filter = 'brightness(1) saturate(1)';
        }, 1000);
    }
    
    // 显示悬停消息
    const hoverMessages = ['你好呀～', '想和我聊天吗？', '要点击我哦～'];
    const randomMsg = hoverMessages[Math.floor(Math.random() * hoverMessages.length)];
    showModelMessage(randomMsg);
}

// 初始化模型选择器
function initializeModelSelector() {
    const modelGrid = document.getElementById('model-grid');
    const closeBtn = document.getElementById('close-model-selector');
    const selector = document.getElementById('live2d-model-selector');
    
    if (!modelGrid) return;
    
    // 清空现有内容
    modelGrid.innerHTML = '';
    
    // 生成模型选项
    live2dModelPaths.forEach((modelPath, index) => {
        const modelName = modelPath.split('/')[0];
        const displayNames = {
            'Alice': '爱丽丝',
            'platelet-0': '血小板',
            'sagiri': '纱雾',
            'chino': '智乃',
            '小埋': '小埋',
            'tororo': '白猫',
            'bronya': '布洛妮娅',
            'sakura': '樱花',
            'unitychan': 'Unity酱',
            'Violet': '紫罗兰'
        };
        
        const emojis = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
        const emoji = emojis[index % emojis.length];
        const displayName = displayNames[modelName] || modelName;
        
        const modelCard = document.createElement('div');
        modelCard.className = `model-card p-4 bg-gray-50 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:shadow-lg ${index === currentModelIndex ? 'ring-2 ring-anime-pink bg-pink-50' : ''}`;
        modelCard.innerHTML = `
            <div class="text-center">
                <div class="text-4xl mb-2">${emoji}</div>
                <div class="text-sm font-medium text-gray-700">${displayName}</div>
                <div class="text-xs text-gray-500 mt-1">${modelName}</div>
            </div>
        `;
        
        modelCard.addEventListener('click', () => {
            selectModel(index);
        });
        
        modelGrid.appendChild(modelCard);
    });
    
    // 绑定关闭按钮
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            hideModelSelector();
        });
    }
    
    // 点击背景关闭
    if (selector) {
        selector.addEventListener('click', (e) => {
            if (e.target === selector) {
                hideModelSelector();
            }
        });
    }
}

// 显示模型选择器
function showModelSelector() {
    const selector = document.getElementById('live2d-model-selector');
    if (selector) {
        selector.style.display = 'flex';
        // 更新当前选中状态
        const cards = selector.querySelectorAll('.model-card');
        cards.forEach((card, index) => {
            if (index === currentModelIndex) {
                card.classList.add('ring-2', 'ring-anime-pink', 'bg-pink-50');
            } else {
                card.classList.remove('ring-2', 'ring-anime-pink', 'bg-pink-50');
            }
        });
    }
}

// 隐藏模型选择器
function hideModelSelector() {
    const selector = document.getElementById('live2d-model-selector');
    if (selector) {
        selector.style.display = 'none';
    }
}

// 选择模型
async function selectModel(index) {
    if (index === currentModelIndex) {
        hideModelSelector();
        return;
    }
    
    const oldIndex = currentModelIndex;
    currentModelIndex = index;
    hideModelSelector();
    
    try {
        console.log(`切换模型: ${oldIndex} -> ${index}`);
        const modelPath = live2dModelPaths[index];
        console.log(`模型路径: ${modelPath}`);
        
        const success = await loadRealLive2DModel(modelPath);
        if (!success) {
            console.log('真实Live2D切换失败，使用Canvas渲染');
            await updateEnhancedModel();
        }
        
        const currentModel = getCurrentModelInfo();
        console.log(`模型切换成功: ${currentModel.name}`);
        showMessage(`已选择 ${currentModel.name} 模型`, 'success', 2000);
        showModelMessage(`你好～我是${currentModel.name}！`);
        
    } catch (error) {
        console.error('模型切换失败:', error);
        currentModelIndex = oldIndex; // 恢复原来的索引
        showMessage('模型切换失败，请查看控制台错误信息', 'error', 3000);
    }
}

// 设备性能检测和Live2D控制系统
class DevicePerformanceDetector {
    constructor() {
        this.cpuCores = 0;
        this.memory = 0;
        this.gpuSupport = false;
        this.canvasPerformance = 0;
        this.isLowEndDevice = false;
        this.performanceScore = 0;
        this.userSettings = this.loadUserSettings();
    }

    // 加载用户设置
    loadUserSettings() {
        const settings = localStorage.getItem('live2d-settings');
        if (settings) {
            return JSON.parse(settings);
        }
        return {
            autoDetect: true,
            forceEnable: false,
            forceDisable: false,
            showPerformanceInfo: false
        };
    }

    // 保存用户设置
    saveUserSettings() {
        localStorage.setItem('live2d-settings', JSON.stringify(this.userSettings));
    }

    // 检测CPU核心数
    async detectCPUCores() {
        try {
            // 使用navigator.hardwareConcurrency获取逻辑处理器数量
            this.cpuCores = navigator.hardwareConcurrency || 2;
            
            // 通过Web Worker进行更精确的性能测试
            const workerPerformance = await this.testWorkerPerformance();
            
            console.log(`检测到CPU核心数: ${this.cpuCores}, Worker性能: ${workerPerformance}ms`);
            return this.cpuCores;
        } catch (error) {
            console.warn('CPU检测失败，使用默认值:', error);
            this.cpuCores = 2;
            return this.cpuCores;
        }
    }

    // 通过Web Worker测试性能
    testWorkerPerformance() {
        return new Promise((resolve) => {
            const startTime = performance.now();
            const worker = new Worker(URL.createObjectURL(new Blob([`
                // CPU密集型计算测试
                let result = 0;
                for (let i = 0; i < 100000; i++) {
                    result += Math.sqrt(i) * Math.sin(i);
                }
                postMessage(result);
            `], { type: 'application/javascript' })));

            worker.onmessage = () => {
                const endTime = performance.now();
                const executionTime = endTime - startTime;
                worker.terminate();
                resolve(executionTime);
            };

            worker.onerror = () => {
                worker.terminate();
                resolve(1000); // 默认较差性能
            };

            // 超时保护
            setTimeout(() => {
                worker.terminate();
                resolve(1000);
            }, 3000);
        });
    }

    // 检测内存信息
    detectMemory() {
        try {
            // 检测设备内存（如果支持）
            if (navigator.deviceMemory) {
                this.memory = navigator.deviceMemory; // GB
                console.log(`检测到设备内存: ${this.memory}GB`);
            } else {
                // 通过JS堆大小估算
                if (performance.memory) {
                    const jsHeapSizeLimit = performance.memory.jsHeapSizeLimit;
                    this.memory = Math.round(jsHeapSizeLimit / (1024 * 1024 * 1024 * 4)); // 估算总内存
                    console.log(`通过JS堆估算内存: ${this.memory}GB`);
                } else {
                    this.memory = 4; // 默认4GB
                    console.log('无法检测内存，使用默认值: 4GB');
                }
            }
        } catch (error) {
            console.warn('内存检测失败:', error);
            this.memory = 4;
        }
        return this.memory;
    }

    // 检测GPU支持
    detectGPUSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (gl) {
                const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                if (debugInfo) {
                    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                    console.log(`GPU: ${renderer}`);
                    
                    // 检测是否为集成显卡或低端GPU
                    const lowEndGPUs = ['intel', 'integrated', 'software', 'llvmpipe', 'mesa'];
                    this.gpuSupport = !lowEndGPUs.some(gpu => 
                        renderer.toLowerCase().includes(gpu)
                    );
                } else {
                    this.gpuSupport = true; // 假设支持
                }
            } else {
                this.gpuSupport = false;
            }
            
            console.log(`GPU支持: ${this.gpuSupport}`);
        } catch (error) {
            console.warn('GPU检测失败:', error);
            this.gpuSupport = false;
        }
        return this.gpuSupport;
    }

    // 测试Canvas性能
    async testCanvasPerformance() {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            canvas.width = 280;
            canvas.height = 250;
            const ctx = canvas.getContext('2d');
            
            const startTime = performance.now();
            let frameCount = 0;
            const targetFrames = 60;
            
            function drawTestFrame() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制复杂图形模拟Live2D渲染
                for (let i = 0; i < 10; i++) {
                    ctx.beginPath();
                    ctx.arc(
                        Math.random() * canvas.width,
                        Math.random() * canvas.height,
                        Math.random() * 20 + 5,
                        0,
                        Math.PI * 2
                    );
                    ctx.fillStyle = `hsl(${Math.random() * 360}, 50%, 50%)`;
                    ctx.fill();
                }
                
                frameCount++;
                if (frameCount < targetFrames) {
                    requestAnimationFrame(drawTestFrame);
                } else {
                    const endTime = performance.now();
                    const avgFrameTime = (endTime - startTime) / targetFrames;
                    resolve(avgFrameTime);
                }
            }
            
            drawTestFrame();
        });
    }

    // 综合性能评估
    async performFullDetection() {
        console.log('开始设备性能检测...');
        
        await this.detectCPUCores();
        this.detectMemory();
        this.detectGPUSupport();
        this.canvasPerformance = await this.testCanvasPerformance();
        
        // 计算性能分数 (0-100)
        let score = 0;
        
        // CPU分数 (0-30分)
        if (this.cpuCores >= 8) score += 30;
        else if (this.cpuCores >= 4) score += 20;
        else if (this.cpuCores >= 2) score += 10;
        
        // 内存分数 (0-25分)
        if (this.memory >= 8) score += 25;
        else if (this.memory >= 4) score += 15;
        else if (this.memory >= 2) score += 8;
        
        // GPU分数 (0-25分)
        if (this.gpuSupport) score += 25;
        else score += 5;
        
        // Canvas性能分数 (0-20分)
        if (this.canvasPerformance < 16) score += 20; // 60fps
        else if (this.canvasPerformance < 33) score += 15; // 30fps
        else if (this.canvasPerformance < 50) score += 10; // 20fps
        else score += 5;
        
        this.performanceScore = score;
        this.isLowEndDevice = score < 40; // 40分以下认为是低端设备
        
        console.log(`设备性能评估完成:
        CPU核心: ${this.cpuCores}
        内存: ${this.memory}GB
        GPU支持: ${this.gpuSupport}
        Canvas性能: ${this.canvasPerformance.toFixed(2)}ms/frame
        性能分数: ${this.performanceScore}/100
        低端设备: ${this.isLowEndDevice}`);
        
        return {
            cpuCores: this.cpuCores,
            memory: this.memory,
            gpuSupport: this.gpuSupport,
            canvasPerformance: this.canvasPerformance,
            performanceScore: this.performanceScore,
            isLowEndDevice: this.isLowEndDevice
        };
    }

    // 判断是否应该启用Live2D
    shouldEnableLive2D() {
        // 用户强制设置优先
        if (this.userSettings.forceDisable) {
            console.log('Live2D被用户强制禁用');
            return false;
        }
        
        if (this.userSettings.forceEnable) {
            console.log('Live2D被用户强制启用');
            return true;
        }
        
        // 自动检测模式
        if (this.userSettings.autoDetect) {
            const shouldEnable = !this.isLowEndDevice;
            console.log(`自动检测模式: ${shouldEnable ? '启用' : '禁用'} Live2D`);
            return shouldEnable;
        }
        
        // 默认启用
        return true;
    }

    // 创建设置面板
    createSettingsPanel() {
        const panel = document.createElement('div');
        panel.id = 'live2d-settings-panel';
        panel.className = 'fixed top-4 left-4 bg-white bg-opacity-95 rounded-lg p-4 shadow-lg z-40';
        panel.style.display = 'none';
        panel.style.minWidth = '300px';
        
        panel.innerHTML = `
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800">Live2D 设置</h3>
                <button id="close-live2d-settings" class="text-gray-500 hover:text-gray-700">✕</button>
            </div>
            
            <div class="space-y-4">
                <!-- 性能信息 -->
                <div class="bg-blue-50 rounded-lg p-3">
                    <h4 class="font-semibold text-blue-800 mb-2">设备性能信息</h4>
                    <div class="text-sm text-blue-700 space-y-1">
                        <div>CPU核心: <span id="perf-cpu">${this.cpuCores}</span></div>
                        <div>内存: <span id="perf-memory">${this.memory}GB</span></div>
                        <div>GPU支持: <span id="perf-gpu">${this.gpuSupport ? '是' : '否'}</span></div>
                        <div>性能分数: <span id="perf-score">${this.performanceScore}/100</span></div>
                        <div>设备等级: <span id="perf-level" class="${this.isLowEndDevice ? 'text-red-600' : 'text-green-600'}">${this.isLowEndDevice ? '低端设备' : '性能良好'}</span></div>
                    </div>
                </div>
                
                <!-- Live2D控制选项 -->
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="radio" name="live2d-mode" value="auto" ${this.userSettings.autoDetect ? 'checked' : ''} class="mr-2">
                        <span class="text-sm">自动检测（推荐）</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="live2d-mode" value="force-enable" ${this.userSettings.forceEnable ? 'checked' : ''} class="mr-2">
                        <span class="text-sm">强制启用</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="live2d-mode" value="force-disable" ${this.userSettings.forceDisable ? 'checked' : ''} class="mr-2">
                        <span class="text-sm">强制禁用</span>
                    </label>
                </div>
                
                <!-- 按钮组 -->
                <div class="flex space-x-2 pt-2">
                    <button id="apply-live2d-settings" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">应用设置</button>
                    <button id="refresh-performance" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm">重新检测</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        this.bindSettingsPanelEvents();
    }

    // 绑定设置面板事件
    bindSettingsPanelEvents() {
        const panel = document.getElementById('live2d-settings-panel');
        const closeBtn = document.getElementById('close-live2d-settings');
        const applyBtn = document.getElementById('apply-live2d-settings');
        const refreshBtn = document.getElementById('refresh-performance');
        
        closeBtn.addEventListener('click', () => {
            panel.style.display = 'none';
        });
        
        applyBtn.addEventListener('click', () => {
            const selectedMode = document.querySelector('input[name="live2d-mode"]:checked').value;
            
            this.userSettings.autoDetect = selectedMode === 'auto';
            this.userSettings.forceEnable = selectedMode === 'force-enable';
            this.userSettings.forceDisable = selectedMode === 'force-disable';
            
            this.saveUserSettings();
            
            // 应用新设置
            const shouldEnable = this.shouldEnableLive2D();
            if (shouldEnable) {
                this.enableLive2D();
            } else {
                this.disableLive2D();
            }
            
            panel.style.display = 'none';
            
            // 显示提示
            this.showNotification(`Live2D设置已应用: ${shouldEnable ? '启用' : '禁用'}`);
        });
        
        refreshBtn.addEventListener('click', async () => {
            refreshBtn.textContent = '检测中...';
            refreshBtn.disabled = true;
            
            await this.performFullDetection();
            this.updatePerformanceDisplay();
            
            refreshBtn.textContent = '重新检测';
            refreshBtn.disabled = false;
            
            this.showNotification('性能检测已完成');
        });
    }

    // 更新性能显示
    updatePerformanceDisplay() {
        document.getElementById('perf-cpu').textContent = this.cpuCores;
        document.getElementById('perf-memory').textContent = this.memory + 'GB';
        document.getElementById('perf-gpu').textContent = this.gpuSupport ? '是' : '否';
        document.getElementById('perf-score').textContent = this.performanceScore + '/100';
        
        const levelElement = document.getElementById('perf-level');
        levelElement.textContent = this.isLowEndDevice ? '低端设备' : '性能良好';
        levelElement.className = this.isLowEndDevice ? 'text-red-600' : 'text-green-600';
    }

    // 启用Live2D - 现在通过Live2DManager处理
    enableLive2D() {
        if (window.live2dManager) {
            console.log('✅ [Detector] -> Manager: show()');
            window.live2dManager.show();
        } else {
            // 如果管理器不存在，则尝试重新初始化
            console.log('🤔 [Detector] Live2DManager不存在，尝试重新初始化。');
            initializePerformanceAndLive2D();
        }
    }

    // 禁用Live2D - 现在通过Live2DManager处理
    disableLive2D() {
        if (window.live2dManager) {
            console.log('🛑 [Detector] -> Manager: hide()');
            window.live2dManager.hide();
        }
        // 总是显示通知，即使用户之前关闭过
        this.showLive2DDisabledNotice(true);
    }
    
    // 显示Live2D禁用通知和重新启用选项
    showLive2DDisabledNotice(forceShow = false) {
        const existingNotice = document.getElementById('live2d-disabled-notice');
        if (existingNotice && !forceShow) return;
        if (existingNotice) existingNotice.remove();
        
        const notice = document.createElement('div');
        notice.id = 'live2d-disabled-notice';
        notice.className = 'fixed bottom-4 right-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg z-50 max-w-sm';
        notice.style.cssText = `
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(245, 158, 11, 0.2);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        `;
        
        notice.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="text-2xl">⚠️</div>
                <div class="flex-1">
                    <h4 class="font-semibold text-yellow-800 mb-2">Live2D 已禁用</h4>
                    <p class="text-sm text-yellow-700 mb-3">
                        根据您的设备性能，Live2D已被自动禁用以优化体验。
                    </p>
                    <div class="flex space-x-2">
                        <button id="reenable-live2d" class="px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600 transition-colors">
                            重新启用
                        </button>
                        <button id="live2d-settings-from-notice" class="px-3 py-1 bg-gray-500 text-white rounded text-xs hover:bg-gray-600 transition-colors">
                            设置
                        </button>
                        <button id="dismiss-live2d-notice" class="px-3 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600 transition-colors">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(notice);
        
        // 绑定事件
        document.getElementById('reenable-live2d').addEventListener('click', () => {
            console.log('🔄 [Detector] 用户手动请求重新启用Live2D');
            this.userSettings.forceEnable = true;
            this.userSettings.autoDetect = false;
            this.userSettings.forceDisable = false;
            this.saveUserSettings();
            
            // 调用启用逻辑
            this.enableLive2D();

            notice.remove();
            this.showNotification('Live2D已强制启用');
        });
        
        document.getElementById('live2d-settings-from-notice').addEventListener('click', () => {
            const panel = document.getElementById('live2d-settings-panel');
            if (panel) {
                panel.style.display = 'block';
            }
            notice.remove();
        });
        
        document.getElementById('dismiss-live2d-notice').addEventListener('click', () => {
            notice.remove();
        });
        
        // 10秒后自动消失
        setTimeout(() => {
            if (notice.parentNode) {
                notice.remove();
            }
        }, 10000);
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 bg-${type}-500 text-white px-4 py-2 rounded-lg shadow-lg z-50`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 添加设置按钮到Live2D控制面板
    addSettingsButton() {
        // 增加重试计数，避免无限重试
        let retryCount = 0;
        const maxRetries = 5;
        
        const tryAddButton = () => {
            retryCount++;
            
            // 检查Live2DManager和控制面板是否存在
            const controlsPanel = document.getElementById('live2d-controls');
            
            if (!controlsPanel) {
                if (retryCount < maxRetries) {
                    console.warn(`⚠️ [Detector] 控制面板尚未准备好，${1 + retryCount}秒后重试 (${retryCount}/${maxRetries})`);
                    setTimeout(tryAddButton, 1000 * retryCount);
                } else {
                    console.error('❌ [Detector] 控制面板创建失败，停止重试添加设置按钮');
                }
                return;
            }

            // 检查设置按钮是否已存在
            let settingsBtn = document.getElementById('live2d-settings-btn');
            if (!settingsBtn) {
                settingsBtn = document.createElement('button');
                settingsBtn.id = 'live2d-settings-btn';
                settingsBtn.dataset.action = 'open-settings';
                settingsBtn.title = 'Live2D设置';
                settingsBtn.className = 'w-8 h-8 bg-gray-500/80 text-white rounded-full hover:bg-gray-600 transition-all duration-200 flex items-center justify-center text-sm backdrop-blur-sm';
                settingsBtn.textContent = '⚙️';
                
                controlsPanel.appendChild(settingsBtn);
                console.log('✅ [Detector] Live2D设置按钮已添加到管理器面板。');
            }
        };
        
        // 初始延迟，给予DOM创建时间
        setTimeout(tryAddButton, 2000);
    }
}

// 创建全局性能检测器实例
const devicePerformanceDetector = new DevicePerformanceDetector();

// 全局函数：重新启用Live2D（供外部调用）
window.reEnableLive2D = function() {
    console.log('🔄 外部请求重新启用Live2D');
    devicePerformanceDetector.userSettings.forceEnable = true;
    devicePerformanceDetector.userSettings.autoDetect = false;
    devicePerformanceDetector.userSettings.forceDisable = false;
    devicePerformanceDetector.saveUserSettings();
    devicePerformanceDetector.enableLive2D();
};

// ===================================================================================
// 全新的 Live2D 管理器
// 所有的 Live2D 逻辑将被封装在这里
// ===================================================================================

class Live2DManager {
    constructor(performanceDetector) {
        this.detector = performanceDetector; // 性能检测器
        this.widget = null;
        this.controls = null;
        this.messageBubble = null;
        this.modelSelector = null;
        this.currentModelIndex = 0;
        this.isInitialized = false;
        this.isVisible = true;
        this.isDragging = false;
        this.isSwitching = false; // Single flag to prevent concurrent operations
        this.dragOffsetX = 0;
        this.dragOffsetY = 0;

        // Three.js 相关属性（用于PMX模型）
        this.isThreeJSMode = false;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.mesh = null;
        this.mixer = null;
        this.clock = null;
        this.helper = null;
        this.mmdLoader = null;

        // 从localStorage加载配置
        const savedIndex = localStorage.getItem('live2d_currentModelIndex');
        if (savedIndex) {
            this.currentModelIndex = parseInt(savedIndex, 10);
        }

        this.config = {
            models: [],
            messages: {},
            pmxModels: {} // PMX模型配置
        };
    }

    async init() {
        console.log('✨ [Live2DManager] 开始初始化...');
        
        await this.loadConfig();
        
        // The first load will set up everything.
        // We wrap it in a try-finally block to ensure the state is unlocked.
        this.isSwitching = true;
        try {
            await this.loadModelByIndex(this.currentModelIndex);
        } finally {
            this.isSwitching = false;
        }

        this.isInitialized = true;
        console.log('✅ [Live2DManager] 初始化完成。');
    }

    async loadConfig() {
        try {
            const response = await fetch('/live2d/model_list.json');
            const data = await response.json();
            this.config.models = data.models || [];
            this.config.pmxModels = data.pmxModels || {};
            
            const waifuResponse = await fetch('/live2d/waifu.json');
            this.config.messages = await waifuResponse.json();

            console.log(`📚 [Live2DManager] 配置加载成功: ${this.config.models.length}个模型, ${Object.keys(this.config.pmxModels).length}个PMX模型, ${Object.keys(this.config.messages).length}种交互。`);
        } catch (error) {
            console.error('❌ [Live2DManager] 配置文件加载失败:', error);
        }
    }

    setupDOM() {
        // 首先清理所有旧的Live2D相关DOM元素
        this.cleanupOldDOM();
        
        // 创建主挂件 - 使用更大的自适应尺寸
        this.widget = document.createElement('div');
        this.widget.id = 'live2d-widget';
        this.widget.className = 'fixed bottom-4 right-4 z-30 transition-all duration-300';
        this.widget.style.cssText = `
            width: 400px;
            height: 350px;
            min-width: 280px;
            min-height: 250px;
            max-width: 500px;
            max-height: 450px;
            border-radius: 15px;
            overflow: visible;
            background: transparent;
            border: none;
        `;
        document.body.appendChild(this.widget);

        // 创建控制面板 - 移到左侧避免遮挡模型
        this.controls = document.createElement('div');
        this.controls.id = 'live2d-controls';
        this.controls.className = 'absolute top-2 left-2 flex flex-col space-y-1 p-2 bg-black/20 backdrop-blur-md rounded-lg shadow-lg transition-all duration-300 hover:bg-black/30';
        this.controls.style.cssText = `
            z-index: 10;
            pointer-events: auto;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        `;
        this.controls.innerHTML = `
            <button data-action="toggle-visibility" title="隐藏/显示" class="w-8 h-8 bg-pink-500/80 text-white rounded-full hover:bg-pink-600 transition-all duration-200 flex items-center justify-center text-sm backdrop-blur-sm">👁️</button>
            <button data-action="switch-model" title="切换模型" class="w-8 h-8 bg-blue-500/80 text-white rounded-full hover:bg-blue-600 transition-all duration-200 flex items-center justify-center text-sm backdrop-blur-sm">M</button>
            <button data-action="choose-model" title="选择模型" class="w-8 h-8 bg-orange-500/80 text-white rounded-full hover:bg-orange-600 transition-all duration-200 flex items-center justify-center text-sm backdrop-blur-sm">📋</button>
            <button data-action="reload" title="重新加载" class="w-8 h-8 bg-green-500/80 text-white rounded-full hover:bg-green-600 transition-all duration-200 flex items-center justify-center text-sm backdrop-blur-sm">🏠</button>
            <button data-action="resize-model" title="调整尺寸" class="w-8 h-8 bg-yellow-500/80 text-white rounded-full hover:bg-yellow-600 transition-all duration-200 flex items-center justify-center text-sm backdrop-blur-sm">📏</button>
            <button data-action="say-hi" title="打个招呼" class="w-8 h-8 bg-purple-500/80 text-white rounded-full hover:bg-purple-600 transition-all duration-200 flex items-center justify-center text-sm backdrop-blur-sm">💬</button>
            <div id="model-type-indicator" class="text-xs text-white/80 px-2 py-1 bg-black/40 rounded backdrop-blur-sm">2D</div>
        `;
        
        // 控制面板悬停效果（添加安全检查）
        this.controls.addEventListener('mouseenter', () => {
            if (this.controls && this.controls.style) {
                this.controls.style.opacity = '1';
            }
        });
        this.controls.addEventListener('mouseleave', () => {
            if (this.controls && this.controls.style) {
                this.controls.style.opacity = '0.7';
            }
        });
        
        this.widget.appendChild(this.controls);
        
        // 创建消息气泡
        this.messageBubble = document.createElement('div');
        this.messageBubble.id = 'live2d-message-bubble';
        this.messageBubble.className = 'absolute -top-14 left-0 w-full px-2 opacity-0 transition-all duration-300 pointer-events-none';
        this.messageBubble.innerHTML = `
            <div class="p-2 bg-white/90 dark:bg-gray-700/90 backdrop-blur-md rounded-lg shadow-md text-center text-sm text-gray-800 dark:text-gray-200"></div>
        `;
        this.widget.appendChild(this.messageBubble);

        // 恢复保存的位置
        this.restorePosition();
        
        // 添加拖动提示
        this.widget.style.cursor = 'grab';
        this.widget.title = '按住Shift键并拖动可移动位置';
    }

    restorePosition() {
        const savedPos = localStorage.getItem('live2d_widget_pos');
        if (savedPos) {
            try {
                const pos = JSON.parse(savedPos);
                if (pos.left && pos.top) {
                    this.widget.style.left = pos.left;
                    this.widget.style.top = pos.top;
                    this.widget.style.bottom = 'auto';
                    this.widget.style.right = 'auto';
                    console.log('✅ [Live2DManager] 已恢复保存的位置');
                }
            } catch (e) {
                console.warn('⚠️ 位置数据格式错误，使用默认位置');
            }
        }
    }

    getSavedSize() {
        const savedSize = localStorage.getItem('live2d_widget_size');
        if (savedSize) {
            try {
                const size = JSON.parse(savedSize);
                if (size.width && size.height) {
                    return size;
                }
            } catch (e) {
                console.warn('⚠️ 尺寸数据格式错误，使用默认尺寸');
            }
        }
        return { width: '400px', height: '350px' };
    }

    saveSize(width, height) {
        localStorage.setItem('live2d_widget_size', JSON.stringify({
            width: width,
            height: height
        }));
    }

    // 检测模型实际渲染尺寸并自适应
    async detectAndAdaptModelSize(canvas) {
        return new Promise((resolve) => {
            // 等待模型渲染几帧后再检测
            let frameCount = 0;
            const checkSize = () => {
                frameCount++;
                if (frameCount < 30) { // 等待30帧确保模型完全加载
                    requestAnimationFrame(checkSize);
                    return;
                }

                try {
                    // 获取canvas的渲染内容
                    const ctx = canvas.getContext('2d');
                    if (!ctx) {
                        resolve({ width: 400, height: 350 });
                        return;
                    }

                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    const data = imageData.data;
                    
                    let minX = canvas.width, maxX = 0;
                    let minY = canvas.height, maxY = 0;
                    let hasContent = false;

                    // 扫描非透明像素找到模型边界
                    for (let y = 0; y < canvas.height; y++) {
                        for (let x = 0; x < canvas.width; x++) {
                            const index = (y * canvas.width + x) * 4;
                            const alpha = data[index + 3];
                            
                            if (alpha > 10) { // 非透明像素
                                hasContent = true;
                                minX = Math.min(minX, x);
                                maxX = Math.max(maxX, x);
                                minY = Math.min(minY, y);
                                maxY = Math.max(maxY, y);
                            }
                        }
                    }

                    if (hasContent) {
                        const contentWidth = maxX - minX;
                        const contentHeight = maxY - minY;
                        
                        // 计算实际显示尺寸（考虑canvas的缩放）
                        const scaleX = canvas.offsetWidth / canvas.width;
                        const scaleY = canvas.offsetHeight / canvas.height;
                        
                        const actualWidth = Math.ceil(contentWidth * scaleX);
                        const actualHeight = Math.ceil(contentHeight * scaleY);
                        
                        // 添加边距
                        const paddedWidth = Math.max(280, actualWidth + 40);
                        const paddedHeight = Math.max(250, actualHeight + 40);
                        
                        // 限制最大尺寸
                        const finalWidth = Math.min(500, paddedWidth);
                        const finalHeight = Math.min(450, paddedHeight);
                        
                        resolve({ width: finalWidth, height: finalHeight });
                        console.log(`📏 [Live2DManager] 检测到模型尺寸: ${finalWidth}x${finalHeight}`);
                    } else {
                        resolve({ width: 400, height: 350 });
                    }
                } catch (error) {
                    console.warn('⚠️ 模型尺寸检测失败:', error);
                    resolve({ width: 400, height: 350 });
                }
            };

            requestAnimationFrame(checkSize);
        });
    }

    // 应用自适应尺寸
    async adaptToModelSize(canvas) {
        if (!canvas || !this.isVisible) return;

        try {
            console.log('🔍 [Live2DManager] 开始检测模型尺寸...');
            
            // 对于WebGL canvas，我们需要用不同的方法
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                // 使用更简单的方式：基于模型路径和经验值调整
                const modelPath = this.config.models[this.currentModelIndex];
                const optimalSize = this.getOptimalSizeForModel(modelPath);
                
                this.widget.style.width = `${optimalSize.width}px`;
                this.widget.style.height = `${optimalSize.height}px`;
                
                // 保存尺寸
                this.saveSize(`${optimalSize.width}px`, `${optimalSize.height}px`);
                
                console.log(`📐 [Live2DManager] 已调整为最佳尺寸: ${optimalSize.width}x${optimalSize.height}`);
                this.showMessage(`已优化尺寸: ${optimalSize.width}×${optimalSize.height}`, 'info', 2000);
            }
        } catch (error) {
            console.warn('⚠️ 自适应尺寸调整失败:', error);
        }
    }

    // 根据模型路径获取最佳尺寸
    getOptimalSizeForModel(modelPath) {
        const modelName = modelPath.split('/')[0].toLowerCase();
        
        // 特定模型的优化尺寸配置
        const modelSizeConfig = {
            // 较大的模型
            'dollsfrontline': { width: 480, height: 400 },
            'hyperdimensionneptunia': { width: 460, height: 380 },
            'kantaicollection': { width: 450, height: 370 },
            'mashiro': { width: 440, height: 360 },
            'rem': { width: 420, height: 350 },
            'violet': { width: 430, height: 360 },
            'kiana': { width: 420, height: 350 },
            
            // 中等大小的模型
            'alice': { width: 400, height: 330 },
            'bronya': { width: 390, height: 320 },
            'sagiri': { width: 380, height: 310 },
            'chino': { width: 370, height: 300 },
            'unitychan': { width: 380, height: 310 },
            'len': { width: 360, height: 290 },
            'miku': { width: 370, height: 300 },
            
            // 较小的模型
            'platelet': { width: 340, height: 280 },
            'tororo': { width: 330, height: 270 },
            '小埋': { width: 350, height: 280 },
            'chiaki': { width: 340, height: 270 },
            'hibiki': { width: 350, height: 280 }
        };
        
        // 查找匹配的配置
        for (const [key, size] of Object.entries(modelSizeConfig)) {
            if (modelName.includes(key) || modelPath.includes(key)) {
                return size;
            }
        }
        
        // 默认尺寸
        return { width: 400, height: 350 };
    }

    bindEvents() {
        // 使用事件委托处理所有控制按钮的点击
        this.controls.addEventListener('click', (e) => {
            const button = e.target.closest('button');
            if (!button) return;

            const action = button.dataset.action;
            console.log(`🖱️ [Live2DManager] Action: ${action}`);

            switch (action) {
                case 'toggle-visibility':
                    this.toggleVisibility();
                    break;
                case 'switch-model':
                    this.switchModel();
                    break;
                case 'choose-model':
                    this.showModelSelector();
                    break;
                case 'reload':
                    this.reload();
                    break;
                case 'resize-model':
                    this.showResizeOptions();
                    break;
                case 'say-hi':
                    this.showRandomMessage('click');
                    break;
                case 'open-settings':
                    this.openSettings();
                    break;
            }
        });
        
        // 拖动功能 - 改进，避免与隐藏功能冲突
        this.widget.addEventListener('mousedown', (e) => {
            // 只在按住Shift键、点击非按钮区域、且模型可见时触发拖动
            if (e.shiftKey && e.target.closest('button') === null && this.isVisible) {
                e.preventDefault(); // 防止选中文本
                this.isDragging = true;
                this.dragOffsetX = e.clientX - this.widget.getBoundingClientRect().left;
                this.dragOffsetY = e.clientY - this.widget.getBoundingClientRect().top;
                this.widget.style.cursor = 'grabbing';
                this.widget.style.transition = 'none'; // 关闭拖动时的动画
                this.showMessage('拖动中...松开Shift停止拖动', 'info', 1500);
            }
        });
        
        // 添加键盘事件监听，释放Shift键时停止拖动
        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift' && this.isDragging) {
                this.isDragging = false;
                this.widget.style.transition = 'all 0.3s ease';
                this.widget.style.cursor = 'grab';
                this.showMessage('拖动结束', 'info', 1000);
                
                // 保存位置
                localStorage.setItem('live2d_widget_pos', JSON.stringify({
                    left: this.widget.style.left,
                    top: this.widget.style.top
                }));
            }
        });

        window.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                let newX = e.clientX - this.dragOffsetX;
                let newY = e.clientY - this.dragOffsetY;

                // 边界检测
                newX = Math.max(0, Math.min(newX, window.innerWidth - this.widget.offsetWidth));
                newY = Math.max(0, Math.min(newY, window.innerHeight - this.widget.offsetHeight));
                
                this.widget.style.left = `${newX}px`;
                this.widget.style.top = `${newY}px`;
                this.widget.style.bottom = 'auto';
                this.widget.style.right = 'auto';
            }
        });

        window.addEventListener('mouseup', () => {
            if (this.isDragging) {
                this.isDragging = false;
                this.widget.style.transition = 'all 0.3s ease'; // 恢复动画
                this.widget.style.cursor = 'grab';
                
                // 保存位置
                localStorage.setItem('live2d_widget_pos', JSON.stringify({
                    left: this.widget.style.left,
                    top: this.widget.style.top
                }));
            }
        });
    }
    
    // 清理旧的DOM元素
    cleanupOldDOM() {
        console.log('🧹 [Live2DManager] 清理旧的DOM元素...');
        
        // 移除所有可能存在的旧Live2D元素
        const oldElements = [
            '#live2d-widget',
            '#live2d-controls', 
            '#live2d-model-selector',
            '#live2d-settings-panel',
            '#L2dCanvas',
            '.live2d-fallback',
            '.live2d-placeholder'
        ];
        
        oldElements.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                console.log(`🗑️ 移除旧元素: ${selector}`);
                el.remove();
            });
        });
    }

    destroyCurrentModel() {
        console.log('🗑️ [Live2DManager] 销毁当前模型...');
        
        // 立即标记为销毁状态，防止新的渲染请求
        this.isDestroying = true;
        
        // 找到当前的canvas
        const currentCanvas = this.widget?.querySelector('#live2d-canvas-managed');
        
        if (currentCanvas) {
            try {
                // 立即移除canvas以停止渲染循环
                currentCanvas.remove();
                console.log('✅ Canvas DOM已立即移除');
                
                // 尝试获取WebGL上下文（如果还存在）
                setTimeout(() => {
                    try {
                        // 清理任何残留的WebGL上下文
                        const tempCanvas = document.createElement('canvas');
                        const gl = tempCanvas.getContext('webgl') || tempCanvas.getContext('experimental-webgl');
                        if (gl) {
                            const loseContext = gl.getExtension('WEBGL_lose_context');
                            if (loseContext) {
                                loseContext.loseContext();
                                console.log('✅ WebGL上下文已立即释放');
                            }
                        }
                    } catch (e) {
                        // 忽略清理错误
                    }
                }, 50);
                
            } catch (error) {
                console.warn('⚠️ Canvas清理过程中出现警告:', error);
            }
        }
        
        // 清理其他可能的Live2D canvas
        const otherCanvases = [
            document.querySelector('#L2dCanvas'),
            document.querySelector('#live2d-canvas'),
            ...document.querySelectorAll('canvas[data-live2d]:not(#live2d-canvas-managed)')
        ];
        
        otherCanvases.forEach(canvas => {
            if (canvas) {
                canvas.remove();
                console.log('✅ 清理了额外的Live2D canvas');
            }
        });
        
        // 重置销毁状态
        setTimeout(() => {
            this.isDestroying = false;
        }, 100);
    }

    async loadModelByIndex(index) {
        console.log(`🚀 [Live2DManager] 全新加载流程启动, 模型 #${index}...`);
    
        // 1. 完全销毁旧的一切
        this.destroyWidget();
        await new Promise(resolve => setTimeout(resolve, 200)); // Pause for DOM cleanup and GC
    
        // 3. 重新创建基础DOM结构
        this.setupDOM();
        this.bindEvents();
        if (this.detector) {
            this.detector.addSettingsButton();
        }
    
        // 4. 加载新模型
        this.currentModelIndex = index;
        localStorage.setItem('live2d_currentModelIndex', this.currentModelIndex);
        const modelPath = this.config.models[this.currentModelIndex];
        console.log(`... 正在加载路径: ${modelPath}`);
        
        // 检查是否为PMX模型
        const isPMXModel = modelPath.endsWith('.pmx');
        console.log(`模型类型: ${isPMXModel ? 'PMX (3D)' : 'Live2D (2D)'}`);
        
        // 更新模型类型指示器
        this.updateModelTypeIndicator(isPMXModel);
        
        if (isPMXModel) {
            // 使用Three.js加载PMX模型
            return this.loadPMXModel(modelPath);
        } else {
            // 使用传统Live2D库加载2D模型
            return this.loadLive2DModel(modelPath);
        }
    }

    async loadLive2DModel(modelPath) {
        // 2. 重新加载Live2D核心库
        try {
            await this.loadLibrary();
        } catch (e) {
            this.showMessage('Live2D核心库加载失败', 'error');
            return Promise.reject(e); 
        }

        this.isThreeJSMode = false;
        
        const currentSize = this.getSavedSize();
        const displayWidth = parseInt(currentSize.width, 10);
        const displayHeight = parseInt(currentSize.height, 10);

        const canvas = document.createElement('canvas');
        canvas.id = 'live2d-canvas-managed';
        canvas.width = displayWidth * 2;
        canvas.height = displayHeight * 2;
        canvas.style.cssText = `
            width: ${displayWidth}px;
            height: ${displayHeight}px;
            position: absolute;
            top: 0;
            left: 0;
            object-fit: contain;
            pointer-events: auto;
            z-index: 1;
            background: transparent;
        `;
        canvas.setAttribute('data-live2d', 'true');
        
        try {
            const gl = canvas.getContext('webgl', { alpha: true, premultipliedAlpha: true, antialias: true }) || 
                      canvas.getContext('experimental-webgl', { alpha: true, premultipliedAlpha: true, antialias: true });
            if (gl) {
                gl.clearColor(0, 0, 0, 0);
                gl.clear(gl.COLOR_BUFFER_BIT);
                console.log('✅ WebGL上下文已为新模型准备就绪');
            }
        } catch (e) {
            console.error('⚠️ 创建新WebGL上下文失败', e);
        }

        this.widget.appendChild(canvas);

        return new Promise((resolve, reject) => {
            try {
                if (typeof window.loadlive2d === 'function') {
                    requestAnimationFrame(() => {
                        // 正确编码模型路径中的空格和特殊字符
                        const encodedModelPath = encodeURIComponent(modelPath).replace(/%2F/g, '/');
                        const fullModelPath = `/live2d/model/${encodedModelPath}`;
                        console.log(`🔧 [Live2DManager] 加载Live2D模型: ${fullModelPath}`);
                        
                        window.loadlive2d(canvas.id, fullModelPath);
                        
                        setTimeout(() => {
                            this.showMessage(`加载成功: ${this.getCurrentModelInfo().name}`, 'success');
                            resolve();
                        }, 800);
                    });
                } else {
                    throw new Error('loadlive2d 函数未定义或未加载。');
                }
            } catch (error) {
                console.error('❌ [Live2DManager] 加载模型失败:', error);
                this.showMessage('模型加载失败', 'error');
                if (canvas.parentNode) canvas.remove();
                reject(error);
            }
        });
    }

    async loadPMXModel(modelPath) {
        // 检查Three.js是否可用
        if (typeof THREE === 'undefined') {
            console.warn('⚠️ [Live2DManager] Three.js未加载，等待加载...');
            await this.waitForThreeJS();
        }
        
        this.isThreeJSMode = true;
        
        // 获取PMX模型配置
        const pmxConfig = this.config.pmxModels[modelPath];
        const sizeConfig = pmxConfig?.size || { default: [480, 600], optimal: [540, 660] };
        
        const currentSize = this.getSavedSize();
        let displayWidth = parseInt(currentSize.width, 10);
        let displayHeight = parseInt(currentSize.height, 10);
        
        // 对于PMX模型，使用特殊的尺寸设置
        if (displayWidth === 400 && displayHeight === 350) {
            displayWidth = sizeConfig.default[0];
            displayHeight = sizeConfig.default[1];
            this.saveSize(`${displayWidth}px`, `${displayHeight}px`);
        }

        // 创建Three.js容器
        const container = document.createElement('div');
        container.id = 'threejs-container';
        container.style.cssText = `
            width: ${displayWidth}px;
            height: ${displayHeight}px;
            position: absolute;
            top: 0;
            left: 0;
            overflow: hidden;
            z-index: 1;
            background: transparent;
        `;
        this.widget.appendChild(container);

        try {
            // 初始化Three.js场景
            await this.initThreeJSScene(container, displayWidth, displayHeight);
            
            // 加载PMX模型
            await this.loadMMDModel(modelPath);
            
            // 启动渲染循环（仅在模型加载成功后）
            this.startRenderLoop();
            
            this.showMessage(`PMX模型加载成功: ${pmxConfig?.name || '雷电将军'}`, 'success');
            
            return Promise.resolve();
        } catch (error) {
            console.error('❌ [Live2DManager] PMX模型加载失败:', error);
            this.showMessage('PMX模型加载失败', 'error');
            if (container.parentNode) container.remove();
            return Promise.reject(error);
        }
    }

    async initThreeJSScene(container, width, height) {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = null; // 透明背景

        // 创建相机
        this.camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
        this.camera.position.set(0, 10, 20);

        // 创建基础渲染器
        const baseRenderer = new THREE.WebGLRenderer({ 
            alpha: true, 
            antialias: true,
            powerPreference: "high-performance"
        });
        baseRenderer.setSize(width, height);
        baseRenderer.setClearColor(0x000000, 0); // 透明背景
        baseRenderer.shadowMap.enabled = true;
        baseRenderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 为MMD模型创建OutlineEffect
        if (typeof THREE.OutlineEffect !== 'undefined') {
            this.renderer = new THREE.OutlineEffect(baseRenderer);
            console.log('✅ OutlineEffect已启用，支持MMD轮廓线');
        } else {
            this.renderer = baseRenderer;
            console.log('⚠️ OutlineEffect未加载，使用基础渲染器');
        }
        
        container.appendChild(this.renderer.domElement);

        // 添加适合MMD的光照设置（动态配置）
        this.setupLighting(width, height);

        // 创建时钟
        this.clock = new THREE.Clock();

        // 创建OrbitControls（延迟初始化以确保库已加载）
        this.initControls();
        
        console.log('✅ Three.js MMD场景初始化完成');
    }

    setupLighting(width, height) {
        // 默认光照配置
        const defaultLighting = {
            ambient: 0.4,
            directional: 0.8,
            fill: 0.3
        };
        
        // 如果有保存的模型配置，使用它
        const lightingConfig = this.currentLightingConfig || defaultLighting;
        
        // 环境光 - 提供基础照明
        const ambientLight = new THREE.AmbientLight(0xffffff, lightingConfig.ambient);
        this.scene.add(ambientLight);

        // 主光源 - 模拟阳光
        const directionalLight = new THREE.DirectionalLight(0xffffff, lightingConfig.directional);
        directionalLight.position.set(-1, 1, 1);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.top = 18;
        directionalLight.shadow.camera.bottom = -10;
        directionalLight.shadow.camera.left = -12;
        directionalLight.shadow.camera.right = 12;
        this.scene.add(directionalLight);

        // 补光 - 减少阴影过深
        if (lightingConfig.fill > 0) {
            const fillLight = new THREE.DirectionalLight(0xffffff, lightingConfig.fill);
            fillLight.position.set(1, 1, -1);
            this.scene.add(fillLight);
        }
        
        console.log('💡 [Live2DManager] 光照系统已配置:', lightingConfig);
    }

    isUserInteracting() {
        // 检查用户是否正在通过OrbitControls进行交互
        if (this.controls) {
            // OrbitControls有一个内部状态来跟踪是否正在交互
            return this.controls.autoRotate === false && 
                   (this.controls._state !== undefined && this.controls._state !== this.controls.STATE.NONE);
        }
        return false;
    }

    render2DControls() {
        return `
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">选择适合的尺寸：</div>
            <button class="w-full p-3 bg-green-500 text-white rounded hover:bg-green-600 transition-colors text-left" data-size="tiny">📱 迷你尺寸 (280×240) - 节省空间</button>
            <button class="w-full p-3 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-left" data-size="small">💻 小尺寸 (320×280) - 适合小屏</button>
            <button class="w-full p-3 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors text-left" data-size="medium">🖥️ 中等尺寸 (400×350) - 默认推荐</button>
            <button class="w-full p-3 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors text-left" data-size="large">📺 大尺寸 (480×400) - 大型模型</button>
            <button class="w-full p-3 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-left" data-size="xlarge">🖼️ 超大尺寸 (560×450) - 完整显示</button>
            <button class="w-full p-3 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors text-left" data-size="giant">巨大尺寸 (640×500) - 最大显示</button>
            <hr class="my-3 border-gray-300 dark:border-gray-600">
            <button class="w-full p-3 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition-colors text-left" data-size="auto">🤖 智能适配 - 基于模型自动选择</button>
        `;
    }

    render3DControls() {
        return `
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">3D模型专用控制：</div>
            <div class="grid grid-cols-2 gap-2">
                <button class="p-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-xs" data-size="medium">标准尺寸</button>
                <button class="p-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors text-xs" data-size="large">大型尺寸</button>
            </div>
            <hr class="my-2 border-gray-300 dark:border-gray-600">
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">快速调整：</div>
            <div class="grid grid-cols-3 gap-2 mb-3">
                <button id="scale-down-3d" class="p-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-xs">缩小</button>
                <button id="scale-reset-3d" class="p-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-xs">重置</button>
                <button id="scale-up-3d" class="p-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors text-xs">放大</button>
            </div>
            <div class="grid grid-cols-2 gap-2">
                <button id="pos-up-3d" class="p-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors text-xs">上移</button>
                <button id="pos-down-3d" class="p-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors text-xs">下移</button>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 text-center mt-3">
                💡 也可以直接用鼠标拖拽旋转和滚轮缩放
            </div>
        `;
    }

    bind3DControls(modal) {
        const scaleDownBtn = modal.querySelector('#scale-down-3d');
        const scaleUpBtn = modal.querySelector('#scale-up-3d');
        const scaleResetBtn = modal.querySelector('#scale-reset-3d');
        const posUpBtn = modal.querySelector('#pos-up-3d');
        const posDownBtn = modal.querySelector('#pos-down-3d');

        if (scaleDownBtn) {
            scaleDownBtn.addEventListener('click', () => this.adjust3DModel('scale', -0.01));
        }
        if (scaleUpBtn) {
            scaleUpBtn.addEventListener('click', () => this.adjust3DModel('scale', 0.01));
        }
        if (scaleResetBtn) {
            scaleResetBtn.addEventListener('click', () => this.adjust3DModel('reset'));
        }
        if (posUpBtn) {
            posUpBtn.addEventListener('click', () => this.adjust3DModel('posY', 1));
        }
        if (posDownBtn) {
            posDownBtn.addEventListener('click', () => this.adjust3DModel('posY', -1));
        }
    }

    adjust3DModel(type, value) {
        if (!this.mesh) {
            this.showMessage('3D模型未加载', 'error');
            return;
        }

        const currentModelPath = this.config.models[this.currentModelIndex];
        const pmxConfig = this.config.pmxModels[currentModelPath];
        
        switch (type) {
            case 'scale':
                const newScale = Math.max(0.05, Math.min(0.25, this.mesh.scale.x + value));
                this.mesh.scale.set(newScale, newScale, newScale);
                this.showMessage(`缩放: ${Math.round(newScale * 100)}%`, 'info', 1500);
                break;
                
            case 'posY':
                this.mesh.position.y += value;
                this.showMessage(`位置Y: ${this.mesh.position.y.toFixed(1)}`, 'info', 1500);
                break;
                
            case 'reset':
                if (pmxConfig) {
                    this.mesh.scale.set(pmxConfig.scale, pmxConfig.scale, pmxConfig.scale);
                    this.mesh.position.set(0, pmxConfig.position_y, 0);
                    this.showMessage('已重置到默认状态', 'success', 1500);
                }
                break;
        }
    }

    updateModelTypeIndicator(isPMXModel) {
        const indicator = document.getElementById('model-type-indicator');
        if (indicator) {
            if (isPMXModel) {
                indicator.textContent = '3D';
                indicator.style.backgroundColor = 'rgba(34, 197, 94, 0.6)'; // 绿色表示3D
                indicator.title = 'PMX 3D模型';
            } else {
                indicator.textContent = '2D';
                indicator.style.backgroundColor = 'rgba(59, 130, 246, 0.6)'; // 蓝色表示2D
                indicator.title = 'Live2D 2D模型';
            }
        }
    }

    async initControls() {
        try {
            // 等待MMD相关库（包括OrbitControls）加载
            await this.waitForMMDLoader();
            
            // 检查OrbitControls是否可用
            if (typeof THREE.OrbitControls === 'undefined') {
                throw new Error('OrbitControls未加载');
            }
            
            // 创建OrbitControls
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.enableZoom = true;
            this.controls.enableRotate = true;
            this.controls.enablePan = false;
            this.controls.target.set(0, 0, 0);
            this.controls.update();
            console.log('✅ OrbitControls相机控制已启用');
        } catch (error) {
            console.warn('⚠️ OrbitControls加载失败，将使用自动旋转:', error.message);
            this.controls = null;
        }
    }

    async loadMMDModel(modelPath) {
        return new Promise(async (resolve, reject) => {
            try {
                // 等待MMDLoader和mmdparser加载
                await this.waitForMMDLoader();
                
                // 检查MMDLoader和mmdparser是否可用
                if (typeof THREE.MMDLoader === 'undefined') {
                    console.error('❌ MMDLoader未加载');
                    reject(new Error('MMDLoader未加载'));
                    return;
                }
                
                if (typeof MMDParser === 'undefined') {
                    console.error('❌ MMDParser未加载');
                    reject(new Error('MMDParser未加载'));
                    return;
                }

                // 加载模型配置
                let modelConfig = null;
                try {
                    // 从PMX文件路径提取目录，并加载其中的model.json
                    const modelDir = modelPath.substring(0, modelPath.lastIndexOf('/'));
                    // 正确编码路径中的空格和特殊字符
                    const encodedModelDir = encodeURIComponent(modelDir).replace(/%2F/g, '/');
                    const configPath = `/live2d/model/${encodedModelDir}/model.json`;
                    console.log(`🔧 [Live2DManager] 尝试加载PMX配置: ${configPath}`);
                    
                    const configResponse = await fetch(configPath);
                    if (configResponse.ok) {
                        modelConfig = await configResponse.json();
                        console.log('✅ PMX模型配置加载成功');
                    } else {
                        console.log(`⚠️ 配置文件未找到 (${configResponse.status}): ${configPath}`);
                    }
                } catch (e) {
                    console.log('⚠️ 未找到PMX模型配置文件，使用默认设置:', e.message);
                }

                // 使用配置或默认值
                const pmxConfig = modelConfig?.pmx_config || {
                    scale: 0.08,
                    position_y: -8,
                    auto_rotate: true,
                    rotate_speed: 0.005,
                    camera_distance: 20,
                    camera_height: 10
                };
                
                // 应用渲染配置
                const renderConfig = pmxConfig.rendering || {};
                const lightingConfig = pmxConfig.lighting || {};
                
                // 保存光照配置供setupLighting使用
                this.currentLightingConfig = lightingConfig;

                // 创建MMDLoader实例
                this.mmdLoader = new THREE.MMDLoader();
                
                // 构建完整的PMX文件路径
                const encodedModelPath = encodeURIComponent(modelPath).replace(/%2F/g, '/');
                const fullModelPath = `/live2d/model/${encodedModelPath}`;
                console.log(`🎭 [Live2DManager] 开始加载PMX模型: ${fullModelPath}`);

                // 加载PMX模型
                this.mmdLoader.load(
                    fullModelPath,
                    (mesh) => {
                        console.log('✅ PMX模型加载成功:', mesh);
                        
                        // 保存模型引用
                        this.mesh = mesh;
                        
                        // 应用配置
                        mesh.position.set(0, pmxConfig.position_y, 0);
                        mesh.scale.set(pmxConfig.scale, pmxConfig.scale, pmxConfig.scale);
                        
                        // 添加到场景
                        this.scene.add(mesh);
                        
                        // 创建动画混合器（如果有动画）
                        if (mesh.animations && mesh.animations.length > 0) {
                            this.mixer = new THREE.AnimationMixer(mesh);
                            console.log(`🎬 发现 ${mesh.animations.length} 个动画`);
                        }
                        
                        // 调整相机位置和目标
                        if (this.camera) {
                            this.camera.position.set(0, pmxConfig.camera_height, pmxConfig.camera_distance);
                            const targetY = pmxConfig.camera_target_y || pmxConfig.position_y;
                            this.camera.lookAt(0, targetY, 0);
                            
                            // 如果有OrbitControls，也设置其目标
                            if (this.controls) {
                                this.controls.target.set(0, targetY, 0);
                                this.controls.update();
                            }
                        }
                        
                        // 保存配置以供渲染循环使用
                        this.pmxConfig = pmxConfig;
                        
                        resolve();
                    },
                    (progress) => {
                        const percent = Math.round((progress.loaded / progress.total) * 100);
                        console.log(`📊 PMX模型加载进度: ${percent}%`);
                    },
                    (error) => {
                        console.error('❌ PMX模型加载失败:', error);
                        reject(error);
                    }
                );
                
            } catch (error) {
                console.error('❌ 模型加载过程中发生错误:', error);
                reject(error);
            }
        });
    }

    startRenderLoop() {
        let errorCount = 0;
        const maxErrors = 10;
        
        const animate = () => {
            if (!this.isThreeJSMode || !this.scene || !this.renderer) {
                return; // 停止渲染循环
            }
            
            // 防止错误过多时继续循环
            if (errorCount >= maxErrors) {
                console.error('❌ 渲染错误过多，停止渲染循环');
                return;
            }
            
            try {
                requestAnimationFrame(animate);
                
                const delta = this.clock ? this.clock.getDelta() : 0.016;
                
                // 更新相机控制（安全检查）
                if (this.controls && typeof this.controls.update === 'function') {
                    try {
                        this.controls.update();
                    } catch (e) {
                        console.warn('⚠️ Controls更新错误:', e.message);
                        this.controls = null; // 禁用有问题的controls
                    }
                }
                
                // 更新动画混合器
                if (this.mixer && typeof this.mixer.update === 'function') {
                    this.mixer.update(delta);
                }
                
                // 使用配置的自动旋转（考虑用户交互暂停）
                if (this.mesh && this.pmxConfig?.auto_rotate) {
                    const animConfig = this.pmxConfig.animation || {};
                    const shouldRotate = !animConfig.pause_on_interaction || !this.isUserInteracting();
                    
                    if (shouldRotate) {
                        const rotateSpeed = this.pmxConfig.rotate_speed || 0.005;
                        const rotateAxis = animConfig.rotate_axis || 'y';
                        this.mesh.rotation[rotateAxis] += rotateSpeed;
                    }
                }
                
                // 渲染场景
                this.renderer.render(this.scene, this.camera);
                
                // 重置错误计数器（如果成功渲染）
                errorCount = 0;
                
            } catch (error) {
                errorCount++;
                console.error(`❌ 渲染错误 (${errorCount}/${maxErrors}):`, error);
                
                if (errorCount >= maxErrors) {
                    console.error('❌ 停止Three.js渲染循环');
                    this.isThreeJSMode = false;
                }
            }
        };
        
        animate();
    }

    getCurrentModelInfo() {
        const path = this.config.models[this.currentModelIndex];
        
        // 检查是否为PMX模型
        if (path && path.endsWith('.pmx') && this.config.pmxModels[path]) {
            const pmxConfig = this.config.pmxModels[path];
            return { 
                path, 
                name: pmxConfig.name || '未知PMX模型',
                type: 'PMX (3D)',
                mode: '3D模型',
                author: pmxConfig.author,
                description: pmxConfig.description,
                isThreeJS: true
            };
        } else {
            // Live2D模型
            const name = path ? path.split('/')[1] || '未知模型' : '未知模型';
            return { 
                path, 
                name,
                type: 'Live2D (2D)',
                mode: '2D模型',
                isThreeJS: false
            };
        }
    }

    toggleVisibility() {
        // 检查必要的DOM元素是否存在
        if (!this.widget || !this.controls) {
            console.warn('⚠️ [Live2DManager] toggleVisibility: 挂件或控制面板不存在');
            return;
        }
        
        this.isVisible = !this.isVisible;
        
        // 隐藏模型相关元素，只保留控制面板
        const canvas = this.widget.querySelector('#live2d-canvas-managed');
        const threejsContainer = this.widget.querySelector('#threejs-container');
        const messageBubble = this.widget.querySelector('#live2d-message-bubble');
        
        if (canvas) {
            canvas.style.display = this.isVisible ? 'block' : 'none';
        }
        if (threejsContainer) {
            threejsContainer.style.display = this.isVisible ? 'block' : 'none';
        }
        if (messageBubble) {
            messageBubble.style.display = this.isVisible ? 'block' : 'none';
        }
        
        // 调整挂件大小 - 隐藏时只显示控制面板
        if (this.widget) {
            if (this.isVisible) {
                // 恢复之前的尺寸或使用默认尺寸
                const savedSize = this.getSavedSize();
                this.widget.style.width = savedSize.width;
                this.widget.style.height = savedSize.height;
                this.widget.style.transform = 'none';
            } else {
                this.widget.style.width = '48px'; // 控制面板宽度
                this.widget.style.height = 'auto';
            }
        }
        
        const button = this.controls?.querySelector('[data-action="toggle-visibility"]');
        if (button) {
            button.textContent = this.isVisible ? '👁️' : '🙈';
            button.title = this.isVisible ? '隐藏模型' : '显示模型';
        }
        
        // 只在显示状态才显示消息
        if (this.isVisible) {
            this.showMessage('我回来啦！', 'info');
        }
    }

    async switchModel() {
        if (this.isSwitching) {
            this.showMessage('操作太快了，请稍等...', 'warning', 1000);
            return;
        }
        this.isSwitching = true;
        try {
            const newIndex = (this.currentModelIndex + 1) % this.config.models.length;
            await this.loadModelByIndex(newIndex);
        } finally {
            this.isSwitching = false;
        }
    }
    
    async reload() {
        if (this.isSwitching) {
            this.showMessage('操作太快了，请稍等...', 'warning', 1000);
            return;
        }
        this.isSwitching = true;
        try {
            this.showMessage('正在重新加载...', 'info');
            await this.loadModelByIndex(this.currentModelIndex);
        } finally {
            this.isSwitching = false;
        }
    }

    showMessage(text, type = 'info', duration = 3000) {
        // 在挂件销毁后，不再显示任何消息
        if (!this.widget) {
            console.log(`[Live2DManager] 消息被阻止，因为挂件已销毁: "${text}"`);
            return;
        }
        // 使用全局的showMessage函数，因为它更强大
        if (typeof showMessage === 'function') {
            showMessage(text, type, duration);
        } else {
            // Fallback to simple bubble message
            const bubbleText = this.messageBubble.querySelector('div');
            if (bubbleText) {
                bubbleText.textContent = text;
                this.messageBubble.style.opacity = '1';
                setTimeout(() => {
                    if (this.messageBubble) {
                        this.messageBubble.style.opacity = '0';
                    }
                }, duration);
            }
        }
    }

    showRandomMessage(type) {
        const messages = this.config.messages[type];
        if (messages && messages.length > 0) {
            const messageObj = messages[Math.floor(Math.random() * messages.length)];
            let text = '';
            
            if (typeof messageObj === 'string') {
                text = messageObj;
            } else if (messageObj.text) {
                if (Array.isArray(messageObj.text)) {
                    text = messageObj.text[Math.floor(Math.random() * messageObj.text.length)];
                } else {
                    text = messageObj.text;
                }
            }
            
            if (text) {
                this.showMessage(text, 'info');
            }
        } else {
            // 默认消息
            const defaultMessages = [
                '你好呀～', '今天天气不错呢', '要一起聊天吗？', 
                '我今天很开心哦', '你在忙什么呢？', '记得要好好休息哦'
            ];
            const randomMsg = defaultMessages[Math.floor(Math.random() * defaultMessages.length)];
            this.showMessage(randomMsg, 'info');
        }
    }

    show() {
        this.widget.style.display = 'block';
    }

    hide() {
        this.widget.style.display = 'none';
    }
    
    openSettings() {
        console.log('🎯 [Live2DManager] 打开设置面板');
        const panel = document.getElementById('live2d-settings-panel');
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        } else {
            console.log('📋 [Live2DManager] 设置面板不存在，创建新的');
            this.detector.createSettingsPanel();
            // 延迟显示，确保创建完成
            setTimeout(() => {
                const newPanel = document.getElementById('live2d-settings-panel');
                if (newPanel) {
                    newPanel.style.display = 'block';
                }
            }, 100);
        }
    }
    
    showModelSelector() {
        console.log('🎯 [Live2DManager] 显示模型选择器');
        
        // 创建模型选择器（如果不存在）
        let selector = document.getElementById('live2d-model-selector-managed');
        if (!selector) {
            selector = document.createElement('div');
            selector.id = 'live2d-model-selector-managed';
            selector.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            selector.style.display = 'none';
            
            const modal = document.createElement('div');
            modal.className = 'bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-96 overflow-y-auto';
            modal.innerHTML = `
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">选择 Live2D 模型</h3>
                    <button id="close-model-selector-managed" class="text-gray-500 hover:text-gray-700">✕</button>
                </div>
                <div id="model-grid-managed" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"></div>
            `;
            selector.appendChild(modal);
            document.body.appendChild(selector);
            
            // 绑定关闭事件
            document.getElementById('close-model-selector-managed').addEventListener('click', () => {
                selector.style.display = 'none';
            });
            
            selector.addEventListener('click', (e) => {
                if (e.target === selector) {
                    selector.style.display = 'none';
                }
            });
        }
        
        // 填充模型网格
        const grid = document.getElementById('model-grid-managed');
        grid.innerHTML = '';
        
        this.config.models.forEach((modelPath, index) => {
            const modelName = modelPath.split('/')[0];
            const isPMXModel = modelPath.endsWith('.pmx');
            
            let displayName, emoji, modelType = '';
            
            if (isPMXModel && this.config.pmxModels[modelPath]) {
                // PMX模型使用配置中的信息
                const pmxConfig = this.config.pmxModels[modelPath];
                displayName = pmxConfig.name;
                emoji = '⚡'; // 统一使用雷电图标表示PMX模型
                modelType = ' (3D)';
            } else {
                // Live2D模型使用原有逻辑
                const displayNames = {
                    'Alice': '爱丽丝',
                    'platelet-0': '血小板',
                    'sagiri': '纱雾',
                    'chino': '智乃',
                    '小埋': '小埋',
                    'tororo': '白猫',
                    'bronya': '布洛妮娅',
                    'sakura': '樱花',
                    'unitychan': 'Unity酱',
                    'Violet': '紫罗兰'
                };
                
                const emojis = ['🌸', '💖', '🎀', '🌟', '💫', '🦋', '🎭', '🎨', '🌺', '💝'];
                emoji = emojis[index % emojis.length];
                displayName = displayNames[modelName] || modelName;
                modelType = ' (2D)';
            }
            
            const card = document.createElement('div');
            card.className = `model-card p-4 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:shadow-lg ${index === this.currentModelIndex ? 'ring-2 ring-pink-500 bg-pink-50' : ''}`;
            card.innerHTML = `
                <div class="text-center">
                    <div class="text-4xl mb-2">${emoji}</div>
                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300">${displayName}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">${modelType}</div>
                </div>
            `;
            
            card.addEventListener('click', async () => {
                if (index !== this.currentModelIndex) {
                    await this.loadModelByIndex(index);
                }
                selector.style.display = 'none';
            });
            
            grid.appendChild(card);
        });
        
        selector.style.display = 'flex';
    }

    showResizeOptions() {
        console.log('📏 [Live2DManager] 显示尺寸调整选项');
        
        // 检查是否为3D模型
        const currentModelPath = this.config.models[this.currentModelIndex];
        const is3DModel = currentModelPath && currentModelPath.endsWith('.pmx');
        
        // 创建尺寸选择器
        let resizer = document.getElementById('live2d-resize-options');
        if (!resizer) {
            resizer = document.createElement('div');
            resizer.id = 'live2d-resize-options';
            resizer.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            resizer.style.display = 'none';
            
            const modal = document.createElement('div');
            modal.className = 'bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md';
            modal.innerHTML = `
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">${is3DModel ? '🎭 3D模型控制' : '调整模型尺寸'}</h3>
                    <button id="close-resize-options" class="text-gray-500 hover:text-gray-700">✕</button>
                </div>
                <div class="space-y-3">
                    ${is3DModel ? this.render3DControls() : this.render2DControls()}
                </div>
            `;
            resizer.appendChild(modal);
            document.body.appendChild(resizer);
            
            // 绑定关闭事件
            document.getElementById('close-resize-options').addEventListener('click', () => {
                resizer.style.display = 'none';
            });
            
            resizer.addEventListener('click', (e) => {
                if (e.target === resizer) {
                    resizer.style.display = 'none';
                }
            });

            // 绑定预设尺寸选择事件
            modal.addEventListener('click', async (e) => {
                const button = e.target.closest('button[data-size]');
                if (button) {
                    const sizeType = button.dataset.size;
                    await this.applySize(sizeType);
                    resizer.style.display = 'none';
                }
            });

            // 绑定3D模型专用控制事件
            if (is3DModel) {
                this.bind3DControls(modal);
            }
        }
        
        resizer.style.display = 'flex';
    }

    async applySize(sizeType, customSize = {}) {
        if (this.isSwitching) {
            this.showMessage('正在执行其他操作，请稍后...', 'warning');
            return;
        }
        this.isSwitching = true;
        
        try {
            let width, height;
            let sizeName;
    
            const sizeMap = {
                'tiny': { w: 280, h: 240, name: '迷你' },
                'small': { w: 320, h: 280, name: '小' },
                'medium': { w: 400, h: 350, name: '中等' },
                'large': { w: 480, h: 400, name: '大' },
                'xlarge': { w: 560, h: 450, name: '超大' },
                'giant': { w: 640, h: 500, name: '巨大' }
            };
            
            if (sizeType === 'auto') {
                const modelPath = this.config.models[this.currentModelIndex];
                const optimalSize = this.getOptimalSizeForModel(modelPath);
                width = optimalSize.width;
                height = optimalSize.height;
                sizeName = '智能适配';
            } else if (sizeType === 'custom') {
                width = parseInt(customSize.width, 10);
                height = parseInt(customSize.height, 10);
                sizeName = '自定义';
            } else if (sizeMap[sizeType]) {
                width = sizeMap[sizeType].w;
                height = sizeMap[sizeType].h;
                sizeName = sizeMap[sizeType].name;
            } else {
                width = 400;
                height = 350;
                sizeName = '默认';
            }
            
            // 应用尺寸
            const widthPx = `${width}px`;
            const heightPx = `${height}px`;
            
            this.saveSize(widthPx, heightPx);
            this.showMessage(`尺寸已调整为 ${sizeName} (${width}×${height})`, 'success', 2500);
            console.log(`📐 [Live2DManager] 尺寸调整: ${sizeName} ${width}x${height}`);
            
            // 重新加载模型以适应新画布大小
            await this.loadModelByIndex(this.currentModelIndex);
        } finally {
            this.isSwitching = false;
        }
    }

    destroyWidget() {
        console.log('🧹 [Live2DManager] 正在完全销毁挂件...');
        
        // 如果是Three.js模式，清理Three.js资源
        if (this.isThreeJSMode) {
            this.destroyThreeJSResources();
        } else {
            // 卸载Live2D库并清除其全局变量和循环
            this.unloadLibrary();
        }
        
        if (this.widget) {
            this.widget.remove();
            this.widget = null;
            this.controls = null;
            this.messageBubble = null;
            console.log('✅ [Live2DManager] 挂件已从DOM中移除。');
        }
        
        // 清理可能残留在body中的弹窗
        const selector = document.getElementById('live2d-model-selector-managed');
        if (selector) selector.remove();

        const resizer = document.getElementById('live2d-resize-options');
        if (resizer) resizer.remove();

        // 重置状态
        this.isThreeJSMode = false;
    }

    destroyThreeJSResources() {
        console.log('🗑️ 清理Three.js MMD资源...');
        
        // 停止动画混合器
        if (this.mixer) {
            this.mixer.stopAllAction();
            this.mixer = null;
        }
        
        // 清理模型网格
        if (this.mesh) {
            if (this.scene) {
                this.scene.remove(this.mesh);
            }
            // 清理几何体和材质
            if (this.mesh.geometry) {
                this.mesh.geometry.dispose();
            }
            if (this.mesh.material) {
                if (Array.isArray(this.mesh.material)) {
                    this.mesh.material.forEach(material => this.disposeMaterial(material));
                } else {
                    this.disposeMaterial(this.mesh.material);
                }
            }
            this.mesh = null;
        }
        
        // 清理相机控制
        if (this.controls) {
            this.controls.dispose();
            this.controls = null;
        }
        
        // 深度清理MMD网格和材质
        if (this.mesh) {
            // MMD模型可能有复杂的嵌套结构
            this.mesh.traverse((child) => {
                if (child.isMesh) {
                    if (child.geometry) {
                        child.geometry.dispose();
                    }
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => {
                                this.disposeMaterial(material);
                            });
                        } else {
                            this.disposeMaterial(child.material);
                        }
                    }
                }
            });
            
            if (this.scene) {
                this.scene.remove(this.mesh);
            }
            this.mesh = null;
        }
        
        // 清理渲染器
        if (this.renderer) {
            // 如果是OutlineEffect，需要正确清理
            if (this.renderer.dispose) {
                this.renderer.dispose();
            }
            if (this.renderer.domElement && this.renderer.domElement.parentNode) {
                this.renderer.domElement.remove();
            }
            this.renderer = null;
        }
        
        // 清理场景
        if (this.scene) {
            // 正确的方法是遍历移除所有子对象
            while(this.scene.children.length > 0) {
                this.scene.remove(this.scene.children[0]);
            }
            this.scene = null;
        }
        
        // 清理其他资源
        this.camera = null;
        this.clock = null;
        this.mmdLoader = null;
        
        console.log('✅ Three.js MMD资源清理完成');
    }

    disposeMaterial(material) {
        // 清理材质的所有纹理
        if (material.map) material.map.dispose();
        if (material.normalMap) material.normalMap.dispose();
        if (material.specularMap) material.specularMap.dispose();
        if (material.envMap) material.envMap.dispose();
        if (material.alphaMap) material.alphaMap.dispose();
        if (material.aoMap) material.aoMap.dispose();
        if (material.displacementMap) material.displacementMap.dispose();
        if (material.emissiveMap) material.emissiveMap.dispose();
        if (material.gradientMap) material.gradientMap.dispose();
        if (material.lightMap) material.lightMap.dispose();
        if (material.bumpMap) material.bumpMap.dispose();
        if (material.metalnessMap) material.metalnessMap.dispose();
        if (material.roughnessMap) material.roughnessMap.dispose();
        material.dispose();
    }

    async loadLibrary() {
        if (window.loadlive2d) {
            console.log('📚 [Live2DManager] Live2D library already loaded.');
            return Promise.resolve();
        }
        console.log('📚 [Live2DManager] Loading Live2D library...');
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = '/live2d/live2d.min.js';
            script.id = 'live2d-library-script';
            script.onload = () => {
                console.log('✅ [Live2DManager] Live2D library loaded successfully.');
                resolve();
            };
            script.onerror = (err) => {
                console.error('❌ [Live2DManager] Failed to load Live2D library.', err);
                reject(err);
            };
            document.head.appendChild(script);
        });
    }

    unloadLibrary() {
        console.log('💀 [Live2DManager] Unloading Live2D library...');
        
        // Nuke the global variables
        window.loadlive2d = undefined;
        window.Live2D = undefined;
        
        // Remove the script tag
        const script = document.getElementById('live2d-library-script');
        if (script) {
            script.remove();
            console.log('✅ [Live2DManager] Library script tag removed.');
        }

        // Aggressively cancel all animation frames
        let id = window.requestAnimationFrame(function(){});
        while(id--){
            window.cancelAnimationFrame(id);
        }
        console.log('✅ [Live2DManager] All pending animation frames cancelled.');
    }

    async waitForThreeJS() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 50; // 5秒超时
            
            const checkThreeJS = () => {
                attempts++;
                if (typeof THREE !== 'undefined') {
                    console.log('✅ [Live2DManager] Three.js加载完成');
                    resolve();
                } else if (attempts >= maxAttempts) {
                    console.error('❌ [Live2DManager] Three.js加载超时');
                    reject(new Error('Three.js加载超时'));
                } else {
                    setTimeout(checkThreeJS, 100);
                }
            };
            
            checkThreeJS();
        });
    }

    async waitForMMDLoader() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 50; // 5秒超时 (本地文件应该更快)
            
            const checkMMDLoader = () => {
                attempts++;
                
                // 核心检查项
                const hasThree = typeof THREE !== 'undefined';
                const hasMMDLoader = hasThree && typeof THREE.MMDLoader !== 'undefined';
                const hasTGALoader = hasThree && typeof THREE.TGALoader !== 'undefined';
                const hasAnimationHelper = hasThree && typeof THREE.MMDAnimationHelper !== 'undefined';
                const hasOutlineEffect = hasThree && typeof THREE.OutlineEffect !== 'undefined';
                const hasOrbitControls = hasThree && typeof THREE.OrbitControls !== 'undefined';
                const hasCCDIKSolver = hasThree && typeof THREE.CCDIKSolver !== 'undefined';
                const hasMMDPhysics = hasThree && typeof THREE.MMDPhysics !== 'undefined';
                
                // 检查mmdparser全局变量（r106版本使用MMDParser）
                const hasmmdParser = typeof MMDParser !== 'undefined' || 
                                   typeof window?.MMDParser !== 'undefined' ||
                                   typeof mmdParser !== 'undefined';
                
                // 计算加载进度
                const components = [
                    hasThree, hasMMDLoader, hasTGALoader, hasAnimationHelper,
                    hasOutlineEffect, hasOrbitControls, hasCCDIKSolver, hasMMDPhysics, hasmmdParser
                ];
                const loadedCount = components.filter(Boolean).length;
                const progress = Math.round((loadedCount / components.length) * 100);
                
                // 每2秒详细记录一次，每0.5秒简单记录
                if (attempts % 20 === 0) {
                    console.log(`🔍 [waitForMMDLoader] 详细检查 (${attempts}/${maxAttempts}) - 进度: ${progress}%`, {
                        hasThree, hasMMDLoader, hasTGALoader, hasAnimationHelper,
                        hasOutlineEffect, hasOrbitControls, hasCCDIKSolver, hasMMDPhysics, hasmmdParser
                    });
                } else if (attempts % 5 === 0) {
                    console.log(`⏳ [waitForMMDLoader] 进度: ${progress}% (${loadedCount}/9) - 第${attempts}次检查`);
                }
                
                // 检查核心必需组件（MMDLoader必需的最小集合）
                const coreLoaded = hasThree && hasMMDLoader && hasTGALoader && hasmmdParser;
                
                if (coreLoaded) {
                    console.log(`✅ MMD核心库加载完成 (进度: ${progress}%)`);
                    // 给其他组件更多时间加载，但不强制等待
                    if (hasOutlineEffect && hasOrbitControls) {
                        console.log('✅ 所有MMD增强功能可用');
                    } else {
                        console.log('⚠️ 部分增强功能缺失，但核心功能可用');
                    }
                    resolve();
                } else if (attempts >= maxAttempts) {
                    const missingComponents = [];
                    if (!hasThree) missingComponents.push('Three.js');
                    if (!hasMMDLoader) missingComponents.push('MMDLoader');
                    if (!hasTGALoader) missingComponents.push('TGALoader');
                    if (!hasmmdParser) missingComponents.push('mmdParser');
                    
                    const errorMsg = `MMD库加载超时 - 缺失组件: ${missingComponents.join(', ')}`;
                    console.error(`❌ ${errorMsg}`);
                    reject(new Error(errorMsg));
                } else {
                    setTimeout(checkMMDLoader, 100);
                }
            };
            
            // 立即开始检查
            checkMMDLoader();
        });
    }
}


async function initializePerformanceAndLive2D() {
    console.log('🚀 开始性能检测和Live2D初始化流程...');
    
    // 清理可能存在的旧Live2D全局状态
    if (window.live2dManager) {
        console.log('🧹 清理旧的Live2DManager实例');
        window.live2dManager.destroyCurrentModel();
        delete window.live2dManager;
    }
    
    // 初始化性能检测器
    const detector = new DevicePerformanceDetector();
    await detector.performFullDetection();
    detector.createSettingsPanel(); // 先创建好面板

    // 根据性能决定是否启用Live2D
    if (detector.shouldEnableLive2D()) {
        console.log('✅ 性能良好，初始化Live2D。');
        // 动态加载Live2D核心库
        try {
            // 检查是否已经加载过
            if (!window.loadlive2d) {
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = '/live2d/live2d.min.js';
                    script.onload = resolve;
                    script.onerror = reject;
                    document.body.appendChild(script);
                });
                console.log('✅ Live2D核心库加载成功。');
            }

            // 实例化并初始化管理器
            window.live2dManager = new Live2DManager(detector);
            await window.live2dManager.init();

        } catch (error) {
            console.error('❌ Live2D核心库加载失败，Live2D无法启动。', error);
            detector.showNotification('Live2D库加载失败', 'error');
        }
    } else {
        console.log('⚠️ 设备性能较低，已自动禁用Live2D。');
        detector.disableLive2D(); // 这会显示一个带重新启用选项的通知
    }
}

// ================== 微信适配器管理功能 ==================

// 初始化微信适配器
async function initializeWeChatAdapter() {
    console.log('🚀 开始初始化微信适配器管理界面...');

    try {
        // 设置默认标签页为状态监控
        console.log('📋 切换到状态监控标签页...');
        window.switchWeChatTab('status');

        // 立即加载状态（强制执行）
        console.log('📊 立即加载微信适配器状态...');
        await window.loadWeChatStatus();
        console.log('✅ 初始状态加载完成');

        // 立即加载配置
        console.log('📊 立即加载微信配置...');
        await window.loadWeChatConfig();
        console.log('✅ 初始配置加载完成');

        // 确保所有数组字段都已初始化
        console.log('🔧 确保数组字段初始化...');
        window.initializeAllArrays();
        console.log('✅ 数组字段初始化完成');

        // 检查虚拟环境状态
        console.log('🔧 检查虚拟环境状态...');
        await window.checkEnvironmentStatus();
        console.log('✅ 环境状态检查完成');

        // 初始化WebSocket连接
        console.log('🔌 初始化WebSocket连接...');
        initializeWeChatWebSocket();

        // 设置定时刷新状态（每10秒，更频繁）
        if (window.wechatStatusInterval) {
            clearInterval(window.wechatStatusInterval);
        }
        window.wechatStatusInterval = setInterval(async () => {
            const section = document.getElementById('wechat-adapter-section');
            if (currentWeChatTab === 'status' && section && section.style.display !== 'none') {
                console.log('🔄 定时刷新微信适配器状态...');
                try {
                    await window.loadWeChatStatus();
                } catch (error) {
                    console.error('❌ 定时状态刷新失败:', error);
                }
            }
        }, 10000); // 改为10秒刷新一次

        console.log('✅ 微信适配器管理界面初始化完成');
    } catch (error) {
        console.error('❌ 微信适配器初始化失败:', error);
        // 即使初始化失败，也要设置基本的定时器
        if (!window.wechatStatusInterval) {
            window.wechatStatusInterval = setInterval(async () => {
                if (currentWeChatTab === 'status') {
                    try {
                        await window.loadWeChatStatus();
                    } catch (e) {
                        console.error('定时状态刷新失败:', e);
                    }
                }
            }, 10000);
        }
    }
}

// WebSocket相关
let wechatWebSocket = null;

function initializeWeChatWebSocket() {
    if (wechatWebSocket && wechatWebSocket.readyState === WebSocket.OPEN) {
        return; // 已连接
    }

    // 使用微信适配器专用的WebSocket路径
    const wsProtocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${wsProtocol}//${location.host}/wechat-adapter`;

    try {
        wechatWebSocket = new WebSocket(wsUrl);

        wechatWebSocket.onopen = function() {
            console.log('微信适配器WebSocket连接已建立');
            // 连接成功后立即刷新状态
            if (currentWeChatTab === 'status') {
                window.loadWeChatStatus();
            }
        };

        wechatWebSocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                // 检查是否是微信相关消息
                if (data.source === 'WeChat' || data.type === 'wechat_message' ||
                    data.type === 'bot_status_changed' || data.type === 'listeners_changed') {
                    handleWeChatWebSocketMessage(data);
                }
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };

        wechatWebSocket.onclose = function(event) {
            console.log('微信适配器WebSocket连接已断开', event.code, event.reason);
            wechatWebSocket = null;
            // 3秒后尝试重连
            setTimeout(() => {
                if (currentWeChatTab && document.getElementById('wechat-adapter-section').style.display !== 'none') {
                    console.log('尝试重新连接微信适配器WebSocket...');
                    initializeWeChatWebSocket();
                }
            }, 3000);
        };

        wechatWebSocket.onerror = function(error) {
            console.error('微信适配器WebSocket连接错误:', error);
        };
    } catch (error) {
        console.error('创建WebSocket连接失败:', error);
    }
}

function handleWeChatWebSocketMessage(data) {
    console.log('收到微信WebSocket消息:', data);

    switch (data.type) {
        case 'message_received':
        case 'wechat_message':
            // 实时显示接收到的消息
            if (data.data) {
                displayRealtimeMessage(data.data);
            }
            // 刷新状态
            if (currentWeChatTab === 'status') {
                window.loadWeChatStatus();
            }
            break;
        case 'bot_status_changed':
        case 'process_status_changed':
            // 机器人状态变化
            console.log('微信机器人状态变化:', data.data);
            window.loadWeChatStatus();
            if (data.data && data.data.status) {
                const statusText = data.data.status === 'started' ? '已启动' : '已停止';
                window.showMessage(`微信机器人状态: ${statusText}`, 'info');
            }
            break;
        case 'listeners_changed':
            // 监听列表变化
            console.log('监听列表变化:', data.data);
            if (currentWeChatTab === 'listeners') {
                loadWeChatListeners();
            }
            window.loadWeChatStatus(); // 更新状态页面的监听统计
            break;
        case 'config_updated':
            // 配置更新
            console.log('配置已更新:', data.data);
            window.showMessage('微信适配器配置已更新', 'success');
            if (currentWeChatTab === 'config') {
                window.loadWeChatConfig();
            }
            break;
        default:
            console.log('未处理的微信WebSocket消息类型:', data.type);
            break;
    }
}

function displayRealtimeMessage(messageData) {
    // 在状态页面显示实时消息（如果当前在状态页面）
    if (currentWeChatTab === 'status') {
        const container = document.querySelector('#wechat-listeners-status');
        if (container) {
            // 可以在这里添加实时消息显示逻辑
            console.log('收到实时消息:', messageData);
        }
    }
}

// 测试AI回复功能
window.testAIReply = async function() {
    try {
        const testMessage = prompt('请输入测试消息:', '你好，这是一个测试消息');
        const testUser = prompt('请输入测试用户名:', '测试用户');
        
        if (!testMessage || !testUser) {
            window.showMessage('请输入有效的测试消息和用户名', 'warning');
            return;
        }
        
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/ai/test`, {
            method: 'POST',
            body: JSON.stringify({
                message: testMessage,
                user_name: testUser
            })
        });
        
        if (response.success) {
            window.showMessage('AI回复测试成功: ' + (response.data?.reply || '无回复'), 'success');
        } else {
            window.showMessage('AI回复测试失败: ' + response.error, 'error');
        }
    } catch (error) {
        console.error('测试AI回复失败:', error);
        window.showMessage('测试AI回复失败: ' + error.message, 'error');
    }
}

// 重置AI配置
window.resetAIConfig = function() {
    if (!confirm('确定要重置AI配置为默认值吗？这将清除所有自定义设置。')) {
        return;
    }
    
    const form = document.getElementById('wechat-config-form');
    if (form) {
        // 重置AI配置字段
        form['AI_AUTO_REPLY.enabled'].checked = false;
        form['AI_AUTO_REPLY.reply_delay'].value = '2000';
        form['AI_AUTO_REPLY.min_message_length'].value = '1';
        form['AI_AUTO_REPLY.exclude_bot_names'].value = '雨安,self,AI助手,机器人';

        // 重置新的分段配置
        form['AI_AUTO_REPLY.segmented_reply.enabled'].checked = true;
        form['AI_AUTO_REPLY.segmented_reply.min_length'].value = '50';
        form['AI_AUTO_REPLY.segmented_reply.max_segments'].value = '5';
        form['AI_AUTO_REPLY.segmented_reply.target_length'].value = '100';
        form['AI_AUTO_REPLY.segmented_reply.min_segment_length'].value = '30';
        form['AI_AUTO_REPLY.segmented_reply.delay'].value = '1.0';
        form['AI_AUTO_REPLY.segmented_reply.separators.custom'].value = '###,---';
        form['AI_AUTO_REPLY.segmented_reply.separators.sentence_endings'].value = '。,！,？,.,!,?';
        form['AI_AUTO_REPLY.segmented_reply.separators.clause_separators'].value = '；,;,：,:';
        form['AI_AUTO_REPLY.segmented_reply.separators.comma_separators'].value = '，,,,、';
        form['AI_AUTO_REPLY.segmented_reply.separators.line_separators'].value = '\\n\\n,\\n';
        form['AI_AUTO_REPLY.segmented_reply.separators.remove_punctuation'].checked = true;

        // 重置私聊模式配置
        form['AI_AUTO_REPLY.private_chat.reply_probability'].value = '0.85';
        form['AI_AUTO_REPLY.private_chat.reply_probability_range'].value = '0.85';
        form['AI_AUTO_REPLY.private_chat.keyword_must_trigger'].checked = false;
        form['AI_AUTO_REPLY.private_chat.trigger_keywords'].value = '';
        form['AI_AUTO_REPLY.private_chat.target_users'].value = '';

        // 重置群聊模式配置
        form['AI_AUTO_REPLY.group_chat.reply_probability'].value = '0.3';
        form['AI_AUTO_REPLY.group_chat.reply_probability_range'].value = '0.3';
        form['AI_AUTO_REPLY.group_chat.keyword_must_trigger'].checked = true;
        form['AI_AUTO_REPLY.group_chat.trigger_keywords'].value = '雨安';
        form['AI_AUTO_REPLY.group_chat.target_users'].value = '';
        form['AI_AUTO_REPLY.trigger_keywords'].value = '';
        
        // 新增配置选项
        form['AI_AUTO_REPLY.keyword_must_trigger'].checked = true;
        form['AI_AUTO_REPLY.auto_filter_bots'].checked = true;
        
        // 分段发送设置已移至新的配置区域
        
        // API设置
        form['AI_AUTO_REPLY.api_timeout'].value = '30';
        form['AI_AUTO_REPLY.api_retry_count'].value = '3';
        
        // 高级设置
        form['AI_AUTO_REPLY.maxContextSize'].value = '20';
        form['AI_AUTO_REPLY.enable_context'].checked = true;
        form['AI_AUTO_REPLY.memory_tracking'].checked = true;
        form['AI_AUTO_REPLY.debug'].checked = false;
        
        window.showMessage('AI配置已重置为默认值', 'success');
    }
}

// 测试分段发送
window.testSegmentedSend = async function() {
    try {
        const testMessage = prompt('请输入要测试智能分段发送的长消息:', '这是一个很长的测试消息。这个消息会被智能分段发送，优先在句号、问号、感叹号等标点符号处分段。这样可以让发送的消息看起来更自然，模拟人类发送消息的习惯！你觉得这个功能怎么样？');
        const testUser = prompt('请输入目标用户名:', '测试用户');
        
        if (!testMessage || !testUser) {
            window.showMessage('请输入有效的消息和用户名', 'warning');
            return;
        }
        
        // 从表单获取当前配置（使用新的字段名）
        const form = document.getElementById('wechat-config-form');
        const maxSegments = form ? (parseInt(form['AI_AUTO_REPLY.segmented_reply.max_segments']?.value) || 5) : 5;
        const customSeparators = form ? (form['AI_AUTO_REPLY.segmented_reply.separators.custom']?.value || '') : '';
        const segmentDelay = form ? (parseFloat(form['AI_AUTO_REPLY.segmented_reply.delay']?.value) || 1.0) : 1.0;
        
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/message/send-segmented`, {
            method: 'POST',
            body: JSON.stringify({
                user_name: testUser,
                message: testMessage,
                max_segments: maxSegments,
                custom_separators: customSeparators,
                segment_delay: segmentDelay * 1000 // 转换为毫秒
            })
        });
        
        if (response.success) {
            window.showMessage(`智能分段发送测试成功，共分为 ${response.data.segments_count} 段`, 'success');
            console.log('分段预览:', response.data.segments_preview);
            console.log('完整分段:', response.data.full_segments);
            
            // 显示分段详情
            const detailMessage = `分段详情:\n${response.data.segments_preview.join('\n')}`;
            if (confirm('查看详细分段结果？')) {
                alert(detailMessage);
            }
        } else {
            window.showMessage('智能分段发送测试失败: ' + response.error, 'error');
        }
    } catch (error) {
        console.error('测试智能分段发送失败:', error);
        window.showMessage('测试智能分段发送失败: ' + error.message, 'error');
    }
}

// 加载微信配置
async function loadWeChatConfig() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/config`);
        // 处理双层嵌套格式
        let config = response.data;
        if (config && config.data) {
            config = config.data;
        }

        // 填充配置表单
        const form = document.getElementById('wechat-config-form');
        if (form) {
            form.WEB_PORT.value = config.WEB_PORT || 7702;
            form.LOG_DIR.value = config.LOG_DIR || 'chat_logs';
            form.AUTO_START_LISTENING.checked = config.AUTO_START_LISTENING || false;

            // AI自动回复配置
            const aiConfig = config.AI_AUTO_REPLY || {};
            form['AI_AUTO_REPLY.enabled'].checked = aiConfig.enabled || false;

            // 私聊配置
            const privateChatConfig = aiConfig.private_chat || {};
            form['AI_AUTO_REPLY.private_chat.model'].value = privateChatConfig.model || aiConfig.model || 'gemini-2.5-pro-free';
            form['AI_AUTO_REPLY.private_chat.assistantName'].value = privateChatConfig.assistantName || aiConfig.assistantName || '雨安安';
            form['AI_AUTO_REPLY.private_chat.agent'].value = privateChatConfig.agent || aiConfig.agent || '雨安安';
            form['AI_AUTO_REPLY.private_chat.useragent'].value = privateChatConfig.useragent || aiConfig.useragent || '静';
            form['AI_AUTO_REPLY.private_chat.assistant_id'].value = privateChatConfig.assistant_id || aiConfig.assistant_id || 2857896171;
            form['AI_AUTO_REPLY.private_chat.user_id'].value = privateChatConfig.user_id || aiConfig.user_id || 2166683295;
            form['AI_AUTO_REPLY.private_chat.render_as_image'].checked = privateChatConfig.render_as_image || aiConfig.render_as_image || false;
            form['AI_AUTO_REPLY.private_chat.history_count'].value = privateChatConfig.history_count || 5;

            // 群聊配置
            const groupChatConfig = aiConfig.group_chat || {};
            form['AI_AUTO_REPLY.group_chat.model'].value = groupChatConfig.model || aiConfig.model || 'gemini-2.5-pro-free';
            form['AI_AUTO_REPLY.group_chat.assistantName'].value = groupChatConfig.assistantName || aiConfig.assistantName || '雨安安';
            form['AI_AUTO_REPLY.group_chat.agent'].value = groupChatConfig.agent || aiConfig.agent || '雨安安';
            form['AI_AUTO_REPLY.group_chat.assistant_id'].value = groupChatConfig.assistant_id || aiConfig.assistant_id || 2857896171;
            form['AI_AUTO_REPLY.group_chat.user_id'].value = groupChatConfig.user_id || aiConfig.user_id || 2166683295;
            form['AI_AUTO_REPLY.group_chat.render_as_image'].checked = groupChatConfig.render_as_image || aiConfig.render_as_image || false;
            form['AI_AUTO_REPLY.group_chat.history_count'].value = groupChatConfig.history_count || 10;

            form['AI_AUTO_REPLY.reply_delay'].value = aiConfig.reply_delay || 2000;
            form['AI_AUTO_REPLY.min_message_length'].value = aiConfig.min_message_length || 1;
            
            // API配置
            form['AI_AUTO_REPLY.api_timeout'].value = aiConfig.api_timeout || 300;
            form['AI_AUTO_REPLY.api_retry_count'].value = aiConfig.api_retry_count || 3;
            form['AI_AUTO_REPLY.maxContextSize'].value = aiConfig.maxContextSize || 20;
            form['AI_AUTO_REPLY.enable_context'].checked = aiConfig.enable_context !== false;
            form['AI_AUTO_REPLY.memory_tracking'].checked = aiConfig.memory_tracking !== false;
            form['AI_AUTO_REPLY.debug'].checked = aiConfig.debug || false;

            // 消息缓存配置
            const cacheConfig = aiConfig.message_cache || {};
            form['AI_AUTO_REPLY.message_cache.enabled'].checked = cacheConfig.enabled || false;
            form['AI_AUTO_REPLY.message_cache.wait_time'].value = cacheConfig.wait_time || 5;
            form['AI_AUTO_REPLY.message_cache.max_wait_cycles'].value = cacheConfig.max_wait_cycles || 3;
            form['AI_AUTO_REPLY.message_cache.debug'].checked = cacheConfig.debug || false;

            // 过滤设置
            form['AI_AUTO_REPLY.auto_filter_bots'].checked = aiConfig.auto_filter_bots !== false; // 默认开启

            // 私聊模式配置 - 确保所有字段都正确设置
            const privateProb = privateChatConfig.reply_probability !== undefined ? privateChatConfig.reply_probability : 0.8;
            
            // 设置私聊回复概率（数字输入框和滑块同步）
            if (form['AI_AUTO_REPLY.private_chat.reply_probability']) {
                form['AI_AUTO_REPLY.private_chat.reply_probability'].value = privateProb;
            }
            if (form['AI_AUTO_REPLY.private_chat.reply_probability_range']) {
                form['AI_AUTO_REPLY.private_chat.reply_probability_range'].value = privateProb;
            }
            
            // 私聊其他设置
            if (form['AI_AUTO_REPLY.private_chat.keyword_must_trigger']) {
                form['AI_AUTO_REPLY.private_chat.keyword_must_trigger'].checked = privateChatConfig.keyword_must_trigger || false;
            }

            // 加载私聊数组配置
            window.setArrayFieldData('private_trigger_keywords', privateChatConfig.trigger_keywords || []);
            window.setArrayFieldData('private_target_users', privateChatConfig.target_users || []);

            // 群聊模式配置 - 确保所有字段都正确设置
            const groupProb = groupChatConfig.reply_probability !== undefined ? groupChatConfig.reply_probability : 0.3;
            
            // 设置群聊回复概率（数字输入框和滑块同步）
            if (form['AI_AUTO_REPLY.group_chat.reply_probability']) {
                form['AI_AUTO_REPLY.group_chat.reply_probability'].value = groupProb;
            }
            if (form['AI_AUTO_REPLY.group_chat.reply_probability_range']) {
                form['AI_AUTO_REPLY.group_chat.reply_probability_range'].value = groupProb;
            }
            
            // 群聊其他设置
            if (form['AI_AUTO_REPLY.group_chat.keyword_must_trigger']) {
                form['AI_AUTO_REPLY.group_chat.keyword_must_trigger'].checked = groupChatConfig.keyword_must_trigger !== false; // 默认开启
            }

            // 加载群聊数组配置
            window.setArrayFieldData('group_trigger_keywords', groupChatConfig.trigger_keywords || ['雨安安']);
            window.setArrayFieldData('group_target_users', groupChatConfig.target_users || []);




            // 新的分段配置 - 使用数组形式
            const segmentedConfig = aiConfig.segmented_reply || {};

            // 基础参数配置
            const enabledField = form['AI_AUTO_REPLY.segmented_reply.enabled'];
            if (enabledField) enabledField.checked = segmentedConfig.enabled || false;

            const minLengthField = form['AI_AUTO_REPLY.segmented_reply.min_length'];
            if (minLengthField) minLengthField.value = segmentedConfig.min_length || 50;

            const maxSegmentsField = form['AI_AUTO_REPLY.segmented_reply.max_segments'];
            if (maxSegmentsField) maxSegmentsField.value = segmentedConfig.max_segments || 5;

            const targetLengthField = form['AI_AUTO_REPLY.segmented_reply.target_length'];
            if (targetLengthField) targetLengthField.value = segmentedConfig.target_length || 100;

            const minSegmentLengthField = form['AI_AUTO_REPLY.segmented_reply.min_segment_length'];
            if (minSegmentLengthField) minSegmentLengthField.value = segmentedConfig.min_segment_length || 30;

            const delayField = form['AI_AUTO_REPLY.segmented_reply.delay'];
            if (delayField) delayField.value = segmentedConfig.delay || 1.0;

            // 分段符号配置 - 使用数组形式
            const separators = segmentedConfig.separators || {};

            // 向后兼容：如果是旧格式，转换为新格式
            if (aiConfig.enable_segmented_reply !== undefined && !segmentedConfig.enabled) {
                // 旧格式兼容
                separators.custom = aiConfig.custom_separators ?
                    aiConfig.custom_separators.split(',').map(s => s.trim()).filter(s => s) : [];
            }

            // 设置所有数组数据
            const arrayData = {
                exclude_bot_names: aiConfig.exclude_bot_names || [],
                private_trigger_keywords: privateChatConfig.trigger_keywords || [],
                private_target_users: privateChatConfig.target_users || [],
                group_trigger_keywords: groupChatConfig.trigger_keywords || [],
                group_target_users: groupChatConfig.target_users || [],
                separators: separators
            };

            window.setAllArrayData(arrayData);

            // 在设置完数组数据后，再单独设置 remove_punctuation 字段
            const removePunctuationField = form['AI_AUTO_REPLY.segmented_reply.separators.remove_punctuation'];
            if (removePunctuationField) removePunctuationField.checked = separators.remove_punctuation !== false; // 默认为true
            
            // 科学算法配置加载
            const scientificConfig = aiConfig.scientific_algorithms || {};
            
            // 总开关
            const scientificEnabledField = form['AI_AUTO_REPLY.scientific_algorithms.enabled'];
            if (scientificEnabledField) scientificEnabledField.checked = scientificConfig.enabled !== false; // 默认开启
            
            // 情绪分析算法
            const emotionConfig = scientificConfig.emotion_analysis || {};
            const russellField = form['AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.russell_model'];
            if (russellField) russellField.checked = emotionConfig.russell_model !== false;
            
            const padField = form['AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.pad_model'];
            if (padField) padField.checked = emotionConfig.pad_model !== false;
            
            const plutchikField = form['AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.plutchik_wheel'];
            if (plutchikField) plutchikField.checked = emotionConfig.plutchik_wheel !== false;
            
            // 压力分析算法
            const stressConfig = scientificConfig.stress_analysis || {};
            const yerkesField = form['AI_AUTO_REPLY.scientific_algorithms.stress_analysis.yerkes_dodson'];
            if (yerkesField) yerkesField.checked = stressConfig.yerkes_dodson !== false;
            
            const gasField = form['AI_AUTO_REPLY.scientific_algorithms.stress_analysis.gas_theory'];
            if (gasField) gasField.checked = stressConfig.gas_theory !== false;
            
            const lazarusField = form['AI_AUTO_REPLY.scientific_algorithms.stress_analysis.lazarus_appraisal'];
            if (lazarusField) lazarusField.checked = stressConfig.lazarus_appraisal !== false;
            
            // 关系分析算法
            const relationshipConfig = scientificConfig.relationship_analysis || {};
            const sternbergField = form['AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.sternberg_theory'];
            if (sternbergField) sternbergField.checked = relationshipConfig.sternberg_theory !== false;
            
            const levingerField = form['AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.levinger_stages'];
            if (levingerField) levingerField.checked = relationshipConfig.levinger_stages !== false;
            
            const socialField = form['AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.social_penetration'];
            if (socialField) socialField.checked = relationshipConfig.social_penetration !== false;
            
            // 认知分析算法
            const cognitionConfig = scientificConfig.cognition_analysis || {};
            const dawkinsField = form['AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.dawkins_memes'];
            if (dawkinsField) dawkinsField.checked = cognitionConfig.dawkins_memes !== false;
            
            const cognitiveLoadField = form['AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.cognitive_load'];
            if (cognitiveLoadField) cognitiveLoadField.checked = cognitionConfig.cognitive_load !== false;
            
            const banduraField = form['AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.bandura_theory'];
            if (banduraField) banduraField.checked = cognitionConfig.bandura_theory !== false;
            
            console.log('科学算法配置已加载:', scientificConfig);
        }

        // 验证所有字段都正确设置了值（调试用）
        window.validateConfigFields(config);

        console.log('微信配置加载成功:', config);
    } catch (error) {
        console.error('加载微信配置失败:', error);
        window.showMessage('加载微信配置失败: ' + error.message, 'error');
        
        // 即使加载失败也要初始化数组字段
        window.initializeAllArrays();
    }
}

// ================== 动态数组管理功能 ==================

// 通用数组类型配置
const arrayTypes = {
    // 排除机器人名称
    exclude_bot_names: {
        name: '排除机器人名称',
        placeholder: '例如: 雨安',
        defaultValues: ['雨安', 'self', 'AI助手', '机器人'],
        containerSuffix: 'exclude-bot-names'
    },
    // 私聊触发关键词
    private_trigger_keywords: {
        name: '私聊关键词',
        placeholder: '例如: AI',
        defaultValues: ['雨安', 'AI', '助手'],
        containerSuffix: 'private-trigger-keywords'
    },
    // 私聊目标用户
    private_target_users: {
        name: '私聊用户',
        placeholder: '例如: 静',
        defaultValues: ['静'],
        containerSuffix: 'private-target-users'
    },
    // 群聊触发关键词
    group_trigger_keywords: {
        name: '群聊关键词',
        placeholder: '例如: @雨安',
        defaultValues: ['雨安', '@雨安', 'AI', '助手', '机器人'],
        containerSuffix: 'group-trigger-keywords'
    },
    // 群聊目标群组
    group_target_users: {
        name: '群聊群组',
        placeholder: '例如: 测试群',
        defaultValues: [],
        containerSuffix: 'group-target-users'
    }
};

// 分隔符类型配置
const separatorTypes = {
    custom: {
        name: '自定义分隔符',
        placeholder: '例如: ###',
        defaultValues: ['###', '---']
    },
    sentence_endings: {
        name: '句末标点',
        placeholder: '例如: 。',
        defaultValues: ['。', '！', '？', '.', '!', '?']
    },
    clause_separators: {
        name: '分句标点',
        placeholder: '例如: ；',
        defaultValues: ['；', ';', '：', ':']
    },
    comma_separators: {
        name: '逗号顿号',
        placeholder: '例如: ，',
        defaultValues: ['，', ',', '、']
    },
    line_separators: {
        name: '换行符',
        placeholder: '例如: \\n',
        defaultValues: ['\\n\\n', '\\n']
    }
};

// 添加分隔符项目
window.addSeparatorItem = function(type, value = '') {
    // 定义容器ID映射
    const containerIds = {
        'custom': 'custom-separators-container',
        'sentence_endings': 'sentence-endings-container',
        'clause_separators': 'clause-separators-container',
        'comma_separators': 'comma-separators-container',
        'line_separators': 'line-separators-container'
    };

    const containerId = containerIds[type] || `${type.replace('_', '-')}-container`;
    const container = document.getElementById(containerId);
    
    if (!container) {
        console.error(`找不到容器: ${containerId} (类型: ${type})`);
        return;
    }

    const config = separatorTypes[type];
    const index = container.children.length;

    const itemDiv = document.createElement('div');
    itemDiv.className = 'flex items-center space-x-2';
    itemDiv.innerHTML = `
        <input type="text"
               name="AI_AUTO_REPLY.segmented_reply.separators.${type}[]"
               value="${value}"
               placeholder="${config.placeholder}"
               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
        <button type="button"
                onclick="removeSeparatorItem(this)"
                class="px-2 py-2 bg-red-100 text-red-600 rounded-md hover:bg-red-200 text-sm">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    container.appendChild(itemDiv);
};

// 删除分隔符项目
window.removeSeparatorItem = function(button) {
    const itemDiv = button.parentElement;
    itemDiv.remove();
};

// 初始化分隔符数组
window.initializeSeparatorArrays = function() {
    console.log('🔧 初始化分隔符数组...');

    // 定义容器ID映射
    const containerIds = {
        'custom': 'custom-separators-container',
        'sentence_endings': 'sentence-endings-container',
        'clause_separators': 'clause-separators-container',
        'comma_separators': 'comma-separators-container',
        'line_separators': 'line-separators-container'
    };

    Object.keys(separatorTypes).forEach(type => {
        const containerId = containerIds[type] || `${type.replace('_', '-')}-container`;
        const container = document.getElementById(containerId);
        if (container) {
            // 清空容器
            container.innerHTML = '';

            // 添加默认值
            const defaultValues = separatorTypes[type].defaultValues;
            defaultValues.forEach(value => {
                window.addSeparatorItem(type, value);
            });
        } else {
            console.warn(`找不到容器: ${containerId} (类型: ${type})`);
        }
    });

    console.log('✅ 分隔符数组初始化完成');
};

// 获取分隔符数组数据
window.getSeparatorArrayData = function() {
    const data = {};

    Object.keys(separatorTypes).forEach(type => {
        const inputs = document.querySelectorAll(`[name="AI_AUTO_REPLY.segmented_reply.separators.${type}[]"]`);
        data[type] = Array.from(inputs).map(input => input.value.trim()).filter(value => value);
    });

    // 获取 remove_punctuation 字段
    const removePunctuationField = document.querySelector('input[name="AI_AUTO_REPLY.segmented_reply.separators.remove_punctuation"]');
    if (removePunctuationField) {
        data.remove_punctuation = removePunctuationField.checked;
    }

    return data;
};

// 设置分隔符数组数据
window.setSeparatorArrayData = function(data) {
    // 定义容器ID映射
    const containerIds = {
        'custom': 'custom-separators-container',
        'sentence_endings': 'sentence-endings-container',
        'clause_separators': 'clause-separators-container',
        'comma_separators': 'comma-separators-container',
        'line_separators': 'line-separators-container'
    };

    Object.keys(separatorTypes).forEach(type => {
        const containerId = containerIds[type] || `${type.replace('_', '-')}-container`;
        const container = document.getElementById(containerId);
        if (container) {
            // 清空容器
            container.innerHTML = '';

            // 添加数据中的值 - 只有当数据不存在时才使用默认值
            const values = data[type] !== undefined ? data[type] : separatorTypes[type].defaultValues;

            // 如果有值才添加，允许空数组
            if (values && values.length > 0) {
                values.forEach(value => {
                    window.addSeparatorItem(type, value);
                });
            }
        } else {
            console.warn(`找不到容器: ${containerId} (类型: ${type})`);
        }
    });

    // 设置 remove_punctuation 字段
    const removePunctuationField = document.querySelector('input[name="AI_AUTO_REPLY.segmented_reply.separators.remove_punctuation"]');
    if (removePunctuationField) {
        removePunctuationField.checked = data.remove_punctuation !== false; // 默认为true
    }
};

// ================== 通用数组管理功能 ==================

// 添加通用数组项目
window.addArrayItem = function(type, value = '') {
    const config = arrayTypes[type];
    if (!config) return;

    const container = document.getElementById(`${config.containerSuffix}-container`);
    if (!container) return;

    const itemDiv = document.createElement('div');
    itemDiv.className = 'flex items-center space-x-2';
    itemDiv.innerHTML = `
        <input type="text"
               name="AI_AUTO_REPLY.${type}[]"
               value="${value}"
               placeholder="${config.placeholder}"
               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
        <button type="button"
                onclick="removeArrayItem(this)"
                class="px-2 py-2 bg-red-100 text-red-600 rounded-md hover:bg-red-200 text-sm">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    container.appendChild(itemDiv);
};

// 删除通用数组项目
window.removeArrayItem = function(button) {
    const itemDiv = button.parentElement;
    itemDiv.remove();
};

// 初始化所有数组
window.initializeAllArrays = function() {
    console.log('🔧 初始化所有数组...');

    // 初始化通用数组
    Object.keys(arrayTypes).forEach(type => {
        const config = arrayTypes[type];
        const container = document.getElementById(`${config.containerSuffix}-container`);
        if (container) {
            // 清空容器
            container.innerHTML = '';

            // 添加默认值
            config.defaultValues.forEach(value => {
                window.addArrayItem(type, value);
            });
        }
    });

    // 初始化分隔符数组
    window.initializeSeparatorArrays();

    console.log('✅ 所有数组初始化完成');
};

// 获取所有数组数据
window.getAllArrayData = function() {
    const data = {};

    // 获取通用数组数据
    Object.keys(arrayTypes).forEach(type => {
        const inputs = document.querySelectorAll(`[name="AI_AUTO_REPLY.${type}[]"]`);
        data[type] = Array.from(inputs).map(input => input.value.trim()).filter(value => value);
    });

    // 获取分隔符数组数据
    data.separators = window.getSeparatorArrayData();

    return data;
};

// 设置所有数组数据
window.setAllArrayData = function(data) {
    // 设置通用数组数据
    Object.keys(arrayTypes).forEach(type => {
        const config = arrayTypes[type];
        const container = document.getElementById(`${config.containerSuffix}-container`);
        if (container) {
            // 清空容器
            container.innerHTML = '';

            // 添加数据中的值
            const values = data[type] || config.defaultValues;
            values.forEach(value => {
                window.addArrayItem(type, value);
            });
        }
    });

    // 设置分隔符数组数据
    if (data.separators) {
        window.setSeparatorArrayData(data.separators);
    }
};

// 获取单个数组字段数据
window.getArrayFieldData = function(type) {
    const inputs = document.querySelectorAll(`[name="AI_AUTO_REPLY.${type}[]"]`);
    return Array.from(inputs).map(input => input.value.trim()).filter(value => value);
};

// 设置单个数组字段数据
window.setArrayFieldData = function(type, dataArray) {
    // 清空现有的输入框
    const containerMap = {
        'private_trigger_keywords': 'private-trigger-keywords-container',
        'private_target_users': 'private-target-users-container',
        'group_trigger_keywords': 'group-trigger-keywords-container',
        'group_target_users': 'group-target-users-container'
    };

    const containerId = containerMap[type];
    if (!containerId) {
        console.error('未知的数组字段类型:', type);
        return;
    }

    const container = document.getElementById(containerId);
    if (!container) {
        console.error('找不到容器:', containerId);
        return;
    }

    // 清空容器
    container.innerHTML = '';

    // 添加数据项
    if (dataArray && dataArray.length > 0) {
        dataArray.forEach(value => {
            if (value && value.trim()) {
                window.addArrayItem(type, value.trim());
            }
        });
    }

    // 如果没有数据，添加一个空的输入框
    if (!dataArray || dataArray.length === 0) {
        window.addArrayItem(type, '');
    }
};

// 验证配置字段是否正确设置了值 - 设置为全局函数
window.validateConfigFields = function(config) {
    console.log('🔍 验证配置字段值设置...');

    const form = document.getElementById('wechat-config-form');
    if (!form) return;

    const aiConfig = config.AI_AUTO_REPLY || {};
    const privateChatConfig = aiConfig.private_chat || {};
    const groupChatConfig = aiConfig.group_chat || {};
    const segmentedConfig = aiConfig.segmented_reply || {};

    // 验证关键数字字段
    const fieldsToCheck = [
        {
            name: 'reply_delay',
            elementName: 'AI_AUTO_REPLY.reply_delay',
            configValue: aiConfig.reply_delay || 2000,
            description: '回复延迟'
        },
        {
            name: 'min_message_length',
            elementName: 'AI_AUTO_REPLY.min_message_length',
            configValue: aiConfig.min_message_length || 1,
            description: '最小消息长度'
        },
        {
            name: 'api_timeout',
            elementName: 'AI_AUTO_REPLY.api_timeout',
            configValue: aiConfig.api_timeout || 300,
            description: 'API超时时间'
        },
        {
            name: 'api_retry_count',
            elementName: 'AI_AUTO_REPLY.api_retry_count',
            configValue: aiConfig.api_retry_count || 3,
            description: 'API重试次数'
        },
        {
            name: 'maxContextSize',
            elementName: 'AI_AUTO_REPLY.maxContextSize',
            configValue: aiConfig.maxContextSize || 20,
            description: '上下文大小'
        },
        {
            name: 'private_chat.reply_probability',
            elementName: 'AI_AUTO_REPLY.private_chat.reply_probability',
            configValue: privateChatConfig.reply_probability !== undefined ? privateChatConfig.reply_probability : 0.8,
            description: '私聊回复概率'
        },
        {
            name: 'group_chat.reply_probability',
            elementName: 'AI_AUTO_REPLY.group_chat.reply_probability',
            configValue: groupChatConfig.reply_probability !== undefined ? groupChatConfig.reply_probability : 0.3,
            description: '群聊回复概率'
        },
        {
            name: 'private_chat.history_count',
            elementName: 'AI_AUTO_REPLY.private_chat.history_count',
            configValue: privateChatConfig.history_count || 5,
            description: '私聊历史记录数量'
        },
        {
            name: 'group_chat.history_count',
            elementName: 'AI_AUTO_REPLY.group_chat.history_count',
            configValue: groupChatConfig.history_count || 10,
            description: '群聊历史记录数量'
        }
    ];

    let issues = [];

    fieldsToCheck.forEach(field => {
        const element = form[field.elementName];
        if (element) {
            const actualValue = parseFloat(element.value) || element.value;
            const expectedValue = field.configValue;
            
            if (actualValue != expectedValue) {
                issues.push(`${field.description}: 期望 ${expectedValue}, 实际 ${actualValue}`);
                console.warn(`⚠️ ${field.description}字段值不匹配:`, {
                    expected: expectedValue,
                    actual: actualValue,
                    element: element
                });
            } else {
                console.log(`✅ ${field.description}: ${actualValue}`);
            }
        } else {
            issues.push(`${field.description}: 字段不存在`);
            console.error(`❌ 找不到字段: ${field.elementName}`);
        }
    });

    if (issues.length > 0) {
        console.warn('配置字段验证发现问题:', issues);
    } else {
        console.log('✅ 所有配置字段验证通过');
    }

    return issues;
};

// 调试表单字段 - 设置为全局函数
window.debugFormFields = function() {
    console.log('🔧 开始调试表单字段...');

    const form = document.getElementById('wechat-config-form');
    if (!form) {
        console.error('❌ 表单不存在: wechat-config-form');
        alert('❌ 表单不存在: wechat-config-form');
        return;
    }

    console.log('✅ 表单存在:', form);

    // 检查新的分段配置字段
    const newFields = [
        'AI_AUTO_REPLY.segmented_reply.enabled',
        'AI_AUTO_REPLY.segmented_reply.min_length',
        'AI_AUTO_REPLY.segmented_reply.max_segments',
        'AI_AUTO_REPLY.segmented_reply.target_length',
        'AI_AUTO_REPLY.segmented_reply.min_segment_length',
        'AI_AUTO_REPLY.segmented_reply.delay',
        'AI_AUTO_REPLY.segmented_reply.separators.custom',
        'AI_AUTO_REPLY.segmented_reply.separators.sentence_endings',
        'AI_AUTO_REPLY.segmented_reply.separators.clause_separators',
        'AI_AUTO_REPLY.segmented_reply.separators.comma_separators',
        'AI_AUTO_REPLY.segmented_reply.separators.line_separators'
    ];

    console.log('🔍 检查新的分段配置字段:');

    let existingFields = [];
    let missingFields = [];

    newFields.forEach(fieldName => {
        const field = form[fieldName];
        if (field) {
            console.log(`✅ ${fieldName}:`, field);
            existingFields.push(fieldName);
        } else {
            console.error(`❌ ${fieldName}: 不存在`);
            missingFields.push(fieldName);

            // 尝试通过querySelector查找
            const selector = `[name="${fieldName}"]`;
            const element = document.querySelector(selector);
            if (element) {
                console.log(`🔍 通过querySelector找到 ${fieldName}:`, element);
            }
        }
    });

    const message = `调试结果:\n✅ 存在字段: ${existingFields.length}\n❌ 缺失字段: ${missingFields.length}\n\n缺失字段列表:\n${missingFields.join('\n')}`;
    alert(message);

    console.log('🔧 调试完成');
    return { existingFields, missingFields };
};

// 保存微信配置 - 设置为全局函数
window.saveWeChatConfig = async function() {
    try {
        const form = document.getElementById('wechat-config-form');
        const formData = new FormData(form);
        
        const config = {
            WEB_PORT: parseInt(formData.get('WEB_PORT')) || 7702,
            LOG_DIR: formData.get('LOG_DIR') || 'chat_logs',
            AUTO_START_LISTENING: formData.has('AUTO_START_LISTENING'),
            LISTEN_LIST: [], // 保持现有的监听列表
            AI_AUTO_REPLY: {
                // 基础设置
                enabled: formData.has('AI_AUTO_REPLY.enabled'),
                debug: formData.has('AI_AUTO_REPLY.debug'),
                api_url: "http://localhost:6005/v1/chat/completions",
                api_key: "114514",
                type: "mcp",

                // 私聊配置
                private_chat: {
                    enabled: true,
                    model: formData.get('AI_AUTO_REPLY.private_chat.model') || 'gemini-2.5-pro-free',
                    assistantName: formData.get('AI_AUTO_REPLY.private_chat.assistantName') || '雨安安',
                    agent: formData.get('AI_AUTO_REPLY.private_chat.agent') || '雨安安',
                    useragent: formData.get('AI_AUTO_REPLY.private_chat.useragent') || '静',
                    assistant_id: parseInt(formData.get('AI_AUTO_REPLY.private_chat.assistant_id')) || 2857896171,
                    user_id: parseInt(formData.get('AI_AUTO_REPLY.private_chat.user_id')) || 2166683295,
                    render_as_image: formData.has('AI_AUTO_REPLY.private_chat.render_as_image'),
                    reply_probability: parseFloat(formData.get('AI_AUTO_REPLY.private_chat.reply_probability')) || 0.8,
                    keyword_must_trigger: formData.has('AI_AUTO_REPLY.private_chat.keyword_must_trigger'),
                    trigger_keywords: window.getArrayFieldData('private_trigger_keywords'),
                    target_users: window.getArrayFieldData('private_target_users'),
                    history_count: parseInt(formData.get('AI_AUTO_REPLY.private_chat.history_count')) || 5
                },

                // 群聊配置
                group_chat: {
                    enabled: true,
                    model: formData.get('AI_AUTO_REPLY.group_chat.model') || 'gemini-2.5-pro-free',
                    assistantName: formData.get('AI_AUTO_REPLY.group_chat.assistantName') || '雨安安',
                    agent: formData.get('AI_AUTO_REPLY.group_chat.agent') || '雨安安',
                    // 群聊中useragent使用发送者名称，不在配置中保存
                    assistant_id: parseInt(formData.get('AI_AUTO_REPLY.group_chat.assistant_id')) || 2857896171,
                    user_id: parseInt(formData.get('AI_AUTO_REPLY.group_chat.user_id')) || 2166683295,
                    render_as_image: formData.has('AI_AUTO_REPLY.group_chat.render_as_image'),
                    reply_probability: parseFloat(formData.get('AI_AUTO_REPLY.group_chat.reply_probability')) || 0.3,
                    keyword_must_trigger: formData.has('AI_AUTO_REPLY.group_chat.keyword_must_trigger'),
                    trigger_keywords: window.getArrayFieldData('group_trigger_keywords'),
                    target_users: window.getArrayFieldData('group_target_users'),
                    history_count: parseInt(formData.get('AI_AUTO_REPLY.group_chat.history_count')) || 10
                },
                
                // 基础配置
                reply_delay: parseInt(formData.get('AI_AUTO_REPLY.reply_delay')) || 2000,
                min_message_length: parseInt(formData.get('AI_AUTO_REPLY.min_message_length')) || 1,
                auto_filter_bots: formData.has('AI_AUTO_REPLY.auto_filter_bots'),
                exclude_bot_names: window.getArrayFieldData('exclude_bot_names') || ['雨安', 'self', 'AI助手', '机器人'],
                
                // 新的高级分段配置
                segmented_reply: {
                    enabled: formData.has('AI_AUTO_REPLY.segmented_reply.enabled'),
                    min_length: parseInt(formData.get('AI_AUTO_REPLY.segmented_reply.min_length')) || 50,
                    max_segments: parseInt(formData.get('AI_AUTO_REPLY.segmented_reply.max_segments')) || 5,
                    target_length: parseInt(formData.get('AI_AUTO_REPLY.segmented_reply.target_length')) || 100,
                    min_segment_length: parseInt(formData.get('AI_AUTO_REPLY.segmented_reply.min_segment_length')) || 30,
                    delay: parseFloat(formData.get('AI_AUTO_REPLY.segmented_reply.delay')) || 1.0,
                    separators: window.getSeparatorArrayData()
                },
                
                // API配置
                api_timeout: parseInt(formData.get('AI_AUTO_REPLY.api_timeout')) || 300,
                api_retry_count: parseInt(formData.get('AI_AUTO_REPLY.api_retry_count')) || 1,
                
                // 高级设置
                enable_context: formData.has('AI_AUTO_REPLY.enable_context'),
                memory_tracking: formData.has('AI_AUTO_REPLY.memory_tracking'),
                maxContextSize: parseInt(formData.get('AI_AUTO_REPLY.maxContextSize')) || 20,
                
                // 消息缓存配置
                message_cache: {
                    enabled: formData.has('AI_AUTO_REPLY.message_cache.enabled'),
                    wait_time: parseInt(formData.get('AI_AUTO_REPLY.message_cache.wait_time')) || 5,
                    max_wait_cycles: parseInt(formData.get('AI_AUTO_REPLY.message_cache.max_wait_cycles')) || 3,
                    debug: formData.has('AI_AUTO_REPLY.message_cache.debug')
                },

                // 科学算法配置
                scientific_algorithms: {
                    enabled: formData.has('AI_AUTO_REPLY.scientific_algorithms.enabled'),
                    emotion_analysis: {
                        russell_model: formData.has('AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.russell_model'),
                        pad_model: formData.has('AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.pad_model'),
                        plutchik_wheel: formData.has('AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.plutchik_wheel')
                    },
                    stress_analysis: {
                        yerkes_dodson: formData.has('AI_AUTO_REPLY.scientific_algorithms.stress_analysis.yerkes_dodson'),
                        gas_theory: formData.has('AI_AUTO_REPLY.scientific_algorithms.stress_analysis.gas_theory'),
                        lazarus_appraisal: formData.has('AI_AUTO_REPLY.scientific_algorithms.stress_analysis.lazarus_appraisal')
                    },
                    relationship_analysis: {
                        sternberg_theory: formData.has('AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.sternberg_theory'),
                        levinger_stages: formData.has('AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.levinger_stages'),
                        social_penetration: formData.has('AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.social_penetration')
                    },
                    cognition_analysis: {
                        dawkins_memes: formData.has('AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.dawkins_memes'),
                        cognitive_load: formData.has('AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.cognitive_load'),
                        bandura_theory: formData.has('AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.bandura_theory')
                    }
                }
            }
        };
        
        // 获取现有的监听列表
        try {
            const currentConfig = await window.apiFetch(`${window.API_BASE_URL}/wechat/config`);
            if (currentConfig.data && currentConfig.data.LISTEN_LIST) {
                config.LISTEN_LIST = currentConfig.data.LISTEN_LIST;
            }
        } catch (error) {
            console.log('无法获取现有监听列表，使用空列表');
        }
        
        await window.apiFetch(`${window.API_BASE_URL}/wechat/config`, {
            method: 'POST',
            body: JSON.stringify(config)
        });
        
        window.showMessage('微信适配器配置保存成功', 'success');
    } catch (error) {
        window.showMessage('保存微信配置失败: ' + error.message, 'error');
    }
}

// 加载微信配置 - 设置为全局函数
window.loadWeChatConfig = loadWeChatConfig;

// 缓存监控相关函数
async function refreshCacheStats() {
    try {
        // 首先检查微信适配器状态
        const statusCheck = await checkWeChatAdapterStatus();
        updateCacheServiceStatus(statusCheck.isRunning);

        if (!statusCheck.isRunning) {
            updateCacheStatsDisplay({});
            updateActiveCachesList({});
            console.log('微信适配器未运行，跳过缓存统计刷新');
            return;
        }

        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/cache/stats`);
        if (response.success) {
            updateCacheStatsDisplay(response.data);
        }

        // 同时获取缓存状态详情
        const statusResponse = await window.apiFetch(`${window.API_BASE_URL}/wechat/cache/status`);
        if (statusResponse.success) {
            updateActiveCachesList(statusResponse.data.cache_details);
        }

        console.log('缓存统计已刷新');
    } catch (error) {
        console.error('刷新缓存统计失败:', error);
        // 如果是连接错误，显示服务未运行状态
        updateCacheServiceStatus(false);
        if (error.message.includes('微信适配器服务未运行') || error.message.includes('Failed to fetch')) {
            updateCacheStatsDisplay({});
            updateActiveCachesList({});
            console.log('微信适配器服务未运行，显示空状态');
        }
    }
}

// 检查微信适配器状态
async function checkWeChatAdapterStatus() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/status`);
        return {
            isRunning: response.success && response.data && response.data.bot_running,
            data: response.data
        };
    } catch (error) {
        return {
            isRunning: false,
            error: error.message
        };
    }
}

async function cleanupCache() {
    try {
        // 检查微信适配器状态
        const statusCheck = await checkWeChatAdapterStatus();
        if (!statusCheck.isRunning) {
            console.log('微信适配器未运行，无法执行缓存清理');
            return;
        }

        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/cache/cleanup`, {
            method: 'POST'
        });

        if (response.success) {
            console.log('缓存清理完成');
            // 刷新统计
            await refreshCacheStats();
        } else {
            console.error('缓存清理失败:', response.message);
        }
    } catch (error) {
        console.error('清理缓存失败:', error);
    }
}

async function resetCacheStats() {
    try {
        // 检查微信适配器状态
        const statusCheck = await checkWeChatAdapterStatus();
        if (!statusCheck.isRunning) {
            console.log('微信适配器未运行，无法重置缓存统计');
            return;
        }

        const confirmed = confirm('确定要重置缓存统计吗？此操作不可撤销。');
        if (!confirmed) return;

        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/cache/reset`, {
            method: 'POST'
        });

        if (response.success) {
            console.log('缓存统计已重置');
            // 刷新统计
            await refreshCacheStats();
        } else {
            console.error('重置缓存统计失败:', response.message);
        }
    } catch (error) {
        console.error('重置缓存统计失败:', error);
    }
}

function updateCacheStatsDisplay(stats) {
    // 更新统计卡片
    const totalMessages = document.getElementById('cache-total-messages');
    const totalBatches = document.getElementById('cache-total-batches');
    const avgBatchSize = document.getElementById('cache-avg-batch-size');
    const hitRate = document.getElementById('cache-hit-rate');

    if (totalMessages) totalMessages.textContent = stats.total_messages_cached || '-';
    if (totalBatches) totalBatches.textContent = stats.total_batches_processed || '-';
    if (avgBatchSize) avgBatchSize.textContent = stats.average_batch_size ? stats.average_batch_size.toFixed(1) : '-';
    if (hitRate) hitRate.textContent = stats.cache_hit_rate ? ((stats.cache_hit_rate * 100).toFixed(1) + '%') : '-';
}

function updateActiveCachesList(cacheDetails) {
    const container = document.getElementById('active-caches-list');

    if (!container) return;

    if (!cacheDetails || Object.keys(cacheDetails).length === 0) {
        container.innerHTML = `
            <div class="text-center text-gray-500 py-4">
                <p class="text-sm">暂无活跃缓存</p>
            </div>
        `;
        return;
    }

    const cacheItems = Object.entries(cacheDetails).map(([cacheId, details]) => {
        const statusColor = details.processing ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800';
        const statusText = details.processing ? '处理中' : '等待中';

        return `
            <div class="bg-white p-3 rounded-lg border border-gray-200 flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-gray-900">${cacheId}</span>
                        <span class="px-2 py-1 text-xs rounded-full ${statusColor}">${statusText}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        队列消息: ${details.queued_messages} |
                        等待周期: ${details.wait_cycles} |
                        总消息数: ${details.total_messages} |
                        已处理批次: ${details.processed_batches}
                    </div>
                </div>
                <div class="text-xs text-gray-400">
                    ${details.has_timer ? '⏰' : '⏸️'}
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = cacheItems;
}

function updateCacheServiceStatus(isRunning) {
    const statusElement = document.getElementById('cache-monitor-status');
    if (!statusElement) return;

    const dot = statusElement.querySelector('.w-2.h-2');
    const text = statusElement.querySelector('span');

    if (isRunning) {
        dot.className = 'w-2 h-2 bg-green-500 rounded-full mr-2';
        text.textContent = '监控状态：运行中';
        text.className = 'text-green-600';
    } else {
        dot.className = 'w-2 h-2 bg-red-500 rounded-full mr-2';
        text.textContent = '监控状态：服务未运行';
        text.className = 'text-red-600';
    }
}

// 自动缓存监控管理器
class CacheMonitorManager {
    constructor() {
        this.isMonitoring = false;
        this.retryInterval = null;
        this.maxRetries = 10;
        this.currentRetries = 0;
        this.retryDelay = 3000; // 3秒重试一次
    }

    async startMonitoring() {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        this.currentRetries = 0;

        console.log('开始缓存监控...');
        this.updateStatusIndicator('checking', '检查中...');
        await this.tryRefreshStats();
    }

    async tryRefreshStats() {
        try {
            const statusCheck = await checkWeChatAdapterStatus();

            if (statusCheck.isRunning) {
                // 微信适配器运行中，正常刷新统计
                await refreshCacheStats();
                this.currentRetries = 0;

                // 设置定期刷新（每30秒）
                if (this.retryInterval) {
                    clearInterval(this.retryInterval);
                }
                this.retryInterval = setInterval(async () => {
                    if (this.isMonitoring) {
                        await this.tryRefreshStats();
                    }
                }, 30000);

                console.log('缓存监控已启动，每30秒自动刷新');
                this.updateStatusIndicator('running', '运行中');
                return;
            }

            // 微信适配器未运行，显示等待状态
            this.showWaitingStatus();

            // 如果还在重试范围内，继续重试
            if (this.currentRetries < this.maxRetries) {
                this.currentRetries++;
                console.log(`微信适配器未运行，${this.retryDelay/1000}秒后重试 (${this.currentRetries}/${this.maxRetries})`);

                setTimeout(() => {
                    if (this.isMonitoring) {
                        this.tryRefreshStats();
                    }
                }, this.retryDelay);
            } else {
                console.log('达到最大重试次数，停止自动监控');
                this.stopMonitoring();
            }

        } catch (error) {
            console.error('缓存监控检查失败:', error);
            this.showErrorStatus();

            // 继续重试
            if (this.currentRetries < this.maxRetries) {
                this.currentRetries++;
                setTimeout(() => {
                    if (this.isMonitoring) {
                        this.tryRefreshStats();
                    }
                }, this.retryDelay);
            }
        }
    }

    stopMonitoring() {
        this.isMonitoring = false;
        this.currentRetries = 0;

        if (this.retryInterval) {
            clearInterval(this.retryInterval);
            this.retryInterval = null;
        }

        console.log('缓存监控已停止');
    }

    showWaitingStatus() {
        updateCacheStatsDisplay({});
        updateActiveCachesList({});

        // 在活跃缓存列表中显示等待状态
        const container = document.getElementById('active-caches-list');
        if (container) {
            container.innerHTML = `
                <div class="text-center text-gray-500 py-4">
                    <div class="animate-spin inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full mb-2"></div>
                    <p class="text-sm">等待微信适配器启动...</p>
                    <p class="text-xs text-gray-400">重试 ${this.currentRetries}/${this.maxRetries}</p>
                </div>
            `;
        }
    }

    showErrorStatus() {
        const container = document.getElementById('active-caches-list');
        if (container) {
            container.innerHTML = `
                <div class="text-center text-red-500 py-4">
                    <p class="text-sm">❌ 连接失败</p>
                    <p class="text-xs text-gray-400">重试 ${this.currentRetries}/${this.maxRetries}</p>
                </div>
            `;
        }
    }
}

// 创建全局缓存监控管理器
window.cacheMonitorManager = new CacheMonitorManager();

// 缓存监控自动检测机制
let cacheMonitorInterval = null;

function startCacheMonitoring() {
    // 如果已经在监控中，先停止
    if (cacheMonitorInterval) {
        clearInterval(cacheMonitorInterval);
    }

    // 每30秒检查一次微信适配器状态并更新缓存统计
    cacheMonitorInterval = setInterval(async () => {
        const currentTab = document.querySelector('.wechat-tab-button.active')?.dataset.tab;
        if (currentTab === 'status') {
            await refreshCacheStats();
        }
    }, 30000);

    console.log('缓存监控已启动，每30秒自动刷新');
}

function stopCacheMonitoring() {
    if (cacheMonitorInterval) {
        clearInterval(cacheMonitorInterval);
        cacheMonitorInterval = null;
        console.log('缓存监控已停止');
    }
}

// 页面加载时启动缓存监控
document.addEventListener('DOMContentLoaded', () => {
    startCacheMonitoring();
});

// 页面卸载时停止缓存监控
window.addEventListener('beforeunload', () => {
    stopCacheMonitoring();
});

// 自动刷新缓存统计
let cacheStatsInterval = null;

function startCacheStatsAutoRefresh() {
    // 清除现有的定时器
    if (cacheStatsInterval) {
        clearInterval(cacheStatsInterval);
    }

    // 每30秒自动刷新一次
    cacheStatsInterval = setInterval(async () => {
        // 只在状态监控标签页激活时才自动刷新
        const statusTab = document.querySelector('[data-tab="status"]');
        if (statusTab && statusTab.classList.contains('active')) {
            await refreshCacheStats();
        }
    }, 30000);

    console.log('缓存统计自动刷新已启动（30秒间隔）');
}

function stopCacheStatsAutoRefresh() {
    if (cacheStatsInterval) {
        clearInterval(cacheStatsInterval);
        cacheStatsInterval = null;
        console.log('缓存统计自动刷新已停止');
    }
}

// 缓存监控管理器
window.cacheMonitorManager = {
    isMonitoring: false,
    interval: null,

    async startMonitoring() {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        console.log('启动缓存监控');

        // 立即刷新一次
        await refreshCacheStats();

        // 启动定时刷新
        this.interval = setInterval(async () => {
            await refreshCacheStats();
        }, 30000); // 30秒刷新一次
    },

    stopMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        console.log('停止缓存监控');
    }
};

// 设置为全局函数
window.refreshCacheStats = refreshCacheStats;
window.cleanupCache = cleanupCache;
window.resetCacheStats = resetCacheStats;
window.startCacheStatsAutoRefresh = startCacheStatsAutoRefresh;
window.stopCacheStatsAutoRefresh = stopCacheStatsAutoRefresh;
window.startCacheMonitoring = startCacheMonitoring;
window.stopCacheMonitoring = stopCacheMonitoring;

// 启动微信机器人进程 - 设置为全局函数（自动化命令控制）
window.startWeChatBot = async function() {
    try {
        window.showMessage('正在启动微信适配器进程...', 'info');

        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/process/start`, {
            method: 'POST'
        });

        if (response.success) {
            window.showMessage(`微信适配器进程启动成功 (PID: ${response.data.pid})`, 'success');
            console.log('启动命令:', response.data.command);
            console.log('工作目录:', response.data.workingDirectory);

            // 等待一段时间后刷新状态
            setTimeout(async () => {
                await window.loadWeChatStatus();
            }, 3000);
        } else {
            window.showMessage('启动失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        window.showMessage('启动微信适配器进程失败: ' + error.message, 'error');
    }
}

// 停止微信机器人进程 - 设置为全局函数（自动化命令控制）
window.stopWeChatBot = async function() {
    try {
        if (!confirm('确定要停止微信适配器进程吗？这将完全关闭微信机器人服务。')) {
            return;
        }

        window.showMessage('正在停止微信适配器进程...', 'info');

        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/process/stop`, {
            method: 'POST'
        });

        if (response.success) {
            window.showMessage('微信适配器进程已停止', 'success');

            // 等待一段时间后刷新状态
            setTimeout(async () => {
                await window.loadWeChatStatus();
            }, 2000);
        } else {
            window.showMessage('停止失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        window.showMessage('停止微信适配器进程失败: ' + error.message, 'error');
    }
}

// 获取微信适配器进程状态 - 设置为全局函数
window.getWeChatProcessStatus = async function() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/process/status`);

        if (response.success) {
            return response.data;
        } else {
            console.error('获取进程状态失败:', response.error);
            return { running: false, pid: null };
        }
    } catch (error) {
        console.error('获取微信适配器进程状态失败:', error);
        return { running: false, pid: null };
    }
}

// 检查并显示微信适配器进程状态 - 设置为全局函数
window.checkWeChatProcessStatus = async function() {
    try {
        showMessage('正在检查微信适配器进程状态...', 'info');

        const processStatus = await window.getWeChatProcessStatus();

        if (processStatus.running) {
            showMessage(`微信适配器进程正在运行 (PID: ${processStatus.pid})`, 'success');
            console.log('进程状态:', processStatus);
        } else {
            showMessage('微信适配器进程未运行', 'warning');
        }

        // 同时检查API连接状态
        try {
            const apiStatus = await window.apiFetch(`${window.API_BASE_URL}/wechat/status`);
            if (apiStatus.success && apiStatus.data.bot_running) {
                showMessage('微信适配器API连接正常，监听功能已启用', 'success');
            } else {
                showMessage('微信适配器进程运行中，但监听功能未启用', 'warning');
            }
        } catch (apiError) {
            showMessage('无法连接到微信适配器API服务', 'error');
        }

    } catch (error) {
        showMessage('检查进程状态失败: ' + error.message, 'error');
    }
}

// 启动微信机器人监听 - 设置为全局函数（仅控制监听，不启动进程）
window.startWeChatBotListening = async function() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/bot/start`, {
            method: 'POST'
        });

        if (response.success) {
            window.showMessage('微信机器人监听启动成功', 'success');
            await window.loadWeChatStatus(); // 刷新状态
        } else {
            window.showMessage('启动监听失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        window.showMessage('启动微信机器人监听失败: ' + error.message, 'error');
    }
}

// 停止微信机器人监听 - 设置为全局函数（仅停止监听，不关闭进程）
window.stopWeChatBotListening = async function() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/bot/stop`, {
            method: 'POST'
        });

        if (response.success) {
            window.showMessage('微信机器人监听已停止', 'success');
            await window.loadWeChatStatus(); // 刷新状态
        } else {
            window.showMessage('停止监听失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        window.showMessage('停止微信机器人监听失败: ' + error.message, 'error');
    }
}

// 发送测试消息 - 设置为全局函数
window.sendTestMessage = async function() {
    const message = prompt('请输入要发送的测试消息:', 'Hello from VCPToolBox!');
    if (!message) return;
    
    const target = prompt('请输入目标用户名或群名:', '');
    if (!target) return;
    
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/message/send`, {
            method: 'POST',
            body: JSON.stringify({
                user_name: target,
                message: message
            })
        });
        
        if (response.success) {
            window.showMessage('测试消息发送成功', 'success');
        } else {
            window.showMessage('发送失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        window.showMessage('发送测试消息失败: ' + error.message, 'error');
    }
}

// 加载微信日志用户列表
async function loadWeChatLogUsers() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/logs/users`);
        // 处理双层嵌套格式
        let users = response.data;
        if (users && users.data) {
            users = users.data;
        }

        console.log('日志用户列表加载成功:', users);
        displayWeChatLogUsers(users);
    } catch (error) {
        console.error('加载日志用户列表失败:', error);
        window.showMessage('加载日志用户列表失败: ' + error.message, 'error');
    }
}

function displayWeChatLogUsers(users) {
    const container = document.getElementById('wechat-tab-logs');
    if (!container) return;
    
    container.innerHTML = 
        '<div class="bg-white rounded-lg shadow-sm border border-gray-200">' +
            '<div class="px-6 py-4 border-b border-gray-200">' +
                '<h3 class="text-lg font-medium text-gray-900">聊天记录</h3>' +
                '<p class="text-sm text-gray-500 mt-1">查看和管理微信聊天记录</p>' +
            '</div>' +
            '<div class="p-6">' +
                '<div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="wechat-log-users-grid">' +
                    users.map(user => 
                        '<div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer" onclick="loadWeChatUserLogs(\'' + user.name + '\', \'' + user.type + '\')">' +
                            '<div class="flex items-center justify-between">' +
                                '<div class="flex items-center">' +
                                    '<div class="w-10 h-10 rounded-full ' + (user.type === 'private' ? 'bg-blue-100' : 'bg-green-100') + ' flex items-center justify-center">' +
                                        '<svg class="w-5 h-5 ' + (user.type === 'private' ? 'text-blue-600' : 'text-green-600') + '" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                                            (user.type === 'private' ? 
                                                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>' :
                                                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>'
                                            ) +
                                        '</svg>' +
                                    '</div>' +
                                    '<div class="ml-3">' +
                                        '<p class="text-sm font-medium text-gray-900">' + user.name + '</p>' +
                                        '<p class="text-xs text-gray-500">' + (user.type === 'private' ? '私聊' : '群聊') + '</p>' +
                                    '</div>' +
                                '</div>' +
                                '<div class="text-right">' +
                                    '<p class="text-sm text-gray-900">' + user.log_count + ' 个记录文件</p>' +
                                    '<p class="text-xs text-gray-500">' + (user.latest_log || '无记录') + '</p>' +
                                '</div>' +
                            '</div>' +
                        '</div>'
                    ).join('') +
                '</div>' +
                (users.length === 0 ? '<div class="text-center text-gray-500 py-8">暂无聊天记录</div>' : '') +
            '</div>' +
        '</div>' +
        
        '<!-- 日志查看器模态框 -->' +
        '<div id="wechat-log-viewer-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">' +
            '<div class="flex items-center justify-center min-h-screen px-4">' +
                '<div class="fixed inset-0 bg-black opacity-50" onclick="closeWeChatLogViewer()"></div>' +
                '<div class="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[80vh] relative">' +
                    '<div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">' +
                        '<h3 class="text-lg font-medium text-gray-900" id="wechat-log-viewer-title">聊天记录查看器</h3>' +
                        '<button onclick="closeWeChatLogViewer()" class="text-gray-400 hover:text-gray-500">' +
                            '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                        '</button>' +
                    '</div>' +
                    '<div class="p-6 overflow-y-auto max-h-96">' +
                        '<div id="wechat-log-content" class="space-y-2">' +
                            '<!-- 日志内容将在这里显示 -->' +
                        '</div>' +
                    '</div>' +
                    '<div class="px-6 py-4 border-t border-gray-200 flex justify-between">' +
                        '<div class="flex space-x-2">' +
                            '<input type="number" id="wechat-log-count" placeholder="消息数量" value="20" class="px-3 py-1 border border-gray-300 rounded text-sm" min="1" max="1000">' +
                            '<button onclick="refreshWeChatUserLogs()" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">刷新</button>' +
                        '</div>' +
                        '<button onclick="closeWeChatLogViewer()" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">关闭</button>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>';
}

// 当前查看的日志信息
let currentLogUser = null;
let currentLogType = null;

// 加载用户聊天记录 - 设置为全局函数
window.loadWeChatUserLogs = async function(userName, userType) {
    currentLogUser = userName;
    currentLogType = userType;
    
    const modal = document.getElementById('wechat-log-viewer-modal');
    const title = document.getElementById('wechat-log-viewer-title');
    
    title.textContent = (userType === 'private' ? '私聊' : '群聊') + ': ' + userName;
    modal.classList.remove('hidden');
    
    await refreshWeChatUserLogs();
}

// 刷新用户聊天记录 - 设置为全局函数
window.refreshWeChatUserLogs = async function() {
    if (!currentLogUser || !currentLogType) return;

    try {
        console.log('刷新用户聊天记录:', { currentLogUser, currentLogType });

        // 首先获取用户的日志文件列表
        const usersResponse = await window.apiFetch(`${window.API_BASE_URL}/wechat/logs/users`);
        if (!usersResponse.success || !usersResponse.data) {
            throw new Error('获取日志列表失败');
        }

        // 找到对应用户的日志信息
        const userLogs = usersResponse.data.find(user => user.name === currentLogUser && user.type === currentLogType);
        if (!userLogs || !userLogs.latest_log) {
            const content = document.getElementById('wechat-log-content');
            content.innerHTML = '<div class="text-center text-gray-500 py-4">该用户暂无聊天记录</div>';
            return;
        }

        // 读取最新的日志文件内容
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/logs/content/${encodeURIComponent(currentLogUser)}/${encodeURIComponent(userLogs.latest_log)}?type=${currentLogType}`);

        const content = document.getElementById('wechat-log-content');
        if (response.success && response.data && response.data.content) {
            // 显示格式化的日志内容
            const logContent = response.data.content;
            content.innerHTML = `
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="mb-4 text-sm text-gray-600">
                        <span class="font-medium">文件:</span> ${response.data.file_name} |
                        <span class="font-medium">类型:</span> ${response.data.chat_type}
                    </div>
                    <pre class="whitespace-pre-wrap text-sm text-gray-800 max-h-96 overflow-y-auto font-mono">${logContent}</pre>
                </div>
            `;
        } else {
            content.innerHTML = '<div class="text-center text-gray-500 py-4">无法读取聊天记录内容</div>';
        }
    } catch (error) {
        console.error('加载聊天记录失败:', error);
        const content = document.getElementById('wechat-log-content');
        content.innerHTML = '<div class="text-center text-red-500 py-4">加载失败: ' + error.message + '</div>';
    }
}

// 关闭日志查看器 - 设置为全局函数
window.closeWeChatLogViewer = function() {
    const modal = document.getElementById('wechat-log-viewer-modal');
    modal.classList.add('hidden');
    currentLogUser = null;
    currentLogType = null;
}

// 加载监听管理
async function loadWeChatListeners() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/listeners/status`);
        // 处理双层嵌套格式
        let data = response.data;
        if (data && data.data) {
            data = data.data;
        }

        console.log('监听器列表加载成功:', data);
        displayWeChatListeners(data);
    } catch (error) {
        console.error('加载监听管理失败:', error);
        window.showMessage('加载监听管理失败: ' + error.message, 'error');
    }
}

// 显示添加监听对话框 - 设置为全局函数
window.showAddListenerDialog = function() {
    const userName = prompt('请输入要监听的用户名或群名:', '');
    if (!userName) return;
    
    const userType = confirm('点击"确定"选择私聊，点击"取消"选择群聊') ? 'private' : 'group';
    
    addWeChatListener(userName, userType);
}

// 添加微信监听对象
async function addWeChatListener(userName, userType) {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/listeners/add`, {
            method: 'POST',
            body: JSON.stringify({
                user_name: userName,
                user_type: userType
            })
        });
        
        if (response.success) {
            window.showMessage(`成功添加监听对象: ${userName}`, 'success');
            loadWeChatListeners(); // 刷新列表
        } else {
            window.showMessage('添加失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        window.showMessage('添加监听对象失败: ' + error.message, 'error');
    }
}

// 移除微信监听对象 - 设置为全局函数
window.removeWeChatListener = async function(userName, userType) {
    if (!confirm(`确认移除监听对象: ${userName}?`)) return;
    
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/listeners/remove`, {
            method: 'POST',
            body: JSON.stringify({
                user_name: userName,
                user_type: userType
            })
        });
        
        if (response.success) {
            window.showMessage(`成功移除监听对象: ${userName}`, 'success');
            loadWeChatListeners(); // 刷新列表
        } else {
            window.showMessage('移除失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        window.showMessage('移除监听对象失败: ' + error.message, 'error');
    }
}

// 清空所有监听对象 - 设置为全局函数
window.clearAllWeChatListeners = async function() {
    if (!confirm('确认清空所有监听对象?')) return;
    
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/listeners/clear`, {
            method: 'POST'
        });
        
        if (response.success) {
            window.showMessage('已清空所有监听对象', 'success');
            loadWeChatListeners(); // 刷新列表
        } else {
            window.showMessage('清空失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        window.showMessage('清空监听对象失败: ' + error.message, 'error');
    }
}

function displayWeChatListeners(data) {
    const container = document.getElementById('wechat-tab-listeners');
    if (!container) return;
    
    const listeners = data.listeners || [];
    
    container.innerHTML = 
        '<div class="space-y-6">' +
            '<!-- 添加监听对象 -->' +
            '<div class="bg-white rounded-lg shadow-sm border border-gray-200">' +
                '<div class="px-6 py-4 border-b border-gray-200">' +
                    '<h3 class="text-lg font-medium text-gray-900">添加监听对象</h3>' +
                '</div>' +
                '<div class="p-6">' +
                    '<form id="add-wechat-listener-form" class="flex flex-wrap gap-4 items-end">' +
                        '<div>' +
                            '<label class="block text-sm font-medium text-gray-700 mb-1">用户名/群名</label>' +
                            '<input type="text" name="user_name" required class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" placeholder="输入用户名或群名">' +
                        '</div>' +
                        '<div>' +
                            '<label class="block text-sm font-medium text-gray-700 mb-1">类型</label>' +
                            '<select name="user_type" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">' +
                                '<option value="private">私聊</option>' +
                                '<option value="group">群聊</option>' +
                            '</select>' +
                        '</div>' +
                        '<button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">' +
                            '添加监听' +
                        '</button>' +
                    '</form>' +
                '</div>' +
            '</div>' +
            
            '<!-- 监听列表 -->' +
            '<div class="bg-white rounded-lg shadow-sm border border-gray-200">' +
                '<div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">' +
                    '<h3 class="text-lg font-medium text-gray-900">当前监听对象 (' + listeners.length + ')</h3>' +
                    '<button onclick="clearAllWeChatListeners()" class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700" ' + (listeners.length === 0 ? 'disabled' : '') + '>' +
                        '清空所有' +
                    '</button>' +
                '</div>' +
                '<div class="p-6">' +
                    (listeners.length === 0 ? 
                        '<div class="text-center text-gray-500 py-8">暂无监听对象</div>' :
                        '<div class="space-y-3">' +
                            listeners.map(listener => 
                                '<div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">' +
                                    '<div class="flex items-center">' +
                                        '<div class="w-3 h-3 rounded-full mr-3 ' + (listener.is_active ? 'bg-green-400' : 'bg-gray-400') + '"></div>' +
                                        '<div>' +
                                            '<h4 class="font-medium text-gray-900">' + listener.user_name + '</h4>' +
                                            '<p class="text-sm text-gray-500">' + (listener.user_type === 'private' ? '私聊' : '群聊') + ' • ' + (listener.message_count || 0) + ' 条消息</p>' +
                                        '</div>' +
                                    '</div>' +
                                    '<div class="flex items-center space-x-2">' +
                                        '<span class="text-xs px-2 py-1 rounded ' + (listener.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800') + '">' +
                                            (listener.is_active ? '活跃' : '非活跃') +
                                        '</span>' +
                                        '<button onclick="removeWeChatListener(\'' + listener.user_name + '\', \'' + listener.user_type + '\')" class="text-red-600 hover:text-red-800">' +
                                            '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                                                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>' +
                                            '</svg>' +
                                        '</button>' +
                                    '</div>' +
                                '</div>'
                            ).join('') +
                        '</div>'
                    ) +
                '</div>' +
            '</div>' +
        '</div>';
    
    // 绑定添加监听表单事件
    const form = document.getElementById('add-wechat-listener-form');
    if (form) {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = {
                user_name: formData.get('user_name'),
                user_type: formData.get('user_type')
            };
            
            try {
                await window.apiFetch(`${window.API_BASE_URL}/wechat/listeners/add`, {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
                window.showMessage('成功添加监听对象: ' + data.user_name, 'success');
                form.reset();
                loadWeChatListeners(); // 刷新列表
            } catch (error) {
                window.showMessage('添加监听对象失败: ' + error.message, 'error');
            }
        });
    }
}

// ==================== 配置热更新管理功能 ====================

// 配置热更新相关变量
let hotReloadUpdateLogs = [];
let hotReloadStatusInterval = null;

/**
 * 切换配置热更新标签页
 */
function switchHotReloadTab(tabName) {
    // 隐藏所有标签页内容
    document.querySelectorAll('.hot-reload-tab-content').forEach(tab => {
        tab.style.display = 'none';
    });

    // 移除所有标签按钮的激活状态
    document.querySelectorAll('.hot-reload-tab-btn').forEach(btn => {
        btn.classList.remove('border-emerald-500', 'text-emerald-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });

    // 显示选中的标签页
    const targetTab = document.getElementById(`hot-reload-${tabName}-tab`);
    if (targetTab) {
        targetTab.style.display = 'block';
    }

    // 激活选中的标签按钮
    const targetBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (targetBtn) {
        targetBtn.classList.remove('border-transparent', 'text-gray-500');
        targetBtn.classList.add('border-emerald-500', 'text-emerald-600');
    }

    // 根据标签页加载相应内容
    switch (tabName) {
        case 'status':
            loadHotReloadStatus();
            break;
        case 'configs':
            loadHotReloadConfigs();
            break;
        case 'logs':
            loadHotReloadLogs();
            break;
    }
}

/**
 * 加载配置热更新状态
 */
async function loadHotReloadStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/config/hot-reload/status`);
        const result = await response.json();

        if (result.success) {
            const status = result.data;

            // 更新状态显示
            document.getElementById('hot-reload-status-text').textContent =
                status.isInitialized ? '运行中' : '未初始化';
            document.getElementById('hot-reload-watched-count').textContent = status.watchersCount;
            document.getElementById('hot-reload-callbacks-count').textContent = status.callbacksCount;

            // 更新系统状态
            document.getElementById('hot-reload-system-status').textContent =
                status.isInitialized ? '运行中' : '未初始化';

            // 更新状态徽章
            const statusBadge = document.getElementById('hot-reload-status-badge');
            if (status.isInitialized) {
                statusBadge.textContent = '启用';
                statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800';
            } else {
                statusBadge.textContent = '禁用';
                statusBadge.className = 'ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800';
            }

        } else {
            window.showMessage('获取配置热更新状态失败: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('加载配置热更新状态失败:', error);
        window.showMessage('加载配置热更新状态失败: ' + error.message, 'error');
    }
}

/**
 * 加载配置监控列表
 */
async function loadHotReloadConfigs() {
    try {
        const response = await fetch(`${API_BASE_URL}/config/hot-reload/configs`);
        const result = await response.json();

        if (result.success) {
            const configs = result.data;
            const configsList = document.getElementById('hot-reload-configs-list');

            if (Object.keys(configs).length === 0) {
                configsList.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <p>暂无监控的配置文件</p>
                    </div>
                `;
                return;
            }

            let configsHtml = '';
            for (const [configKey, configInfo] of Object.entries(configs)) {
                // 适配新的API返回格式
                const configData = configInfo.config || configInfo;
                const hasWatcher = configInfo.hasWatcher !== undefined ? configInfo.hasWatcher : true;
                const hasCallback = configInfo.hasCallback !== undefined ? configInfo.hasCallback : false;
                const watcherKey = configInfo.watcherKey || configKey;

                const configCount = Object.keys(configData).length;
                const statusColor = hasWatcher ? 'emerald' : 'red';
                const statusText = hasWatcher ? '监控中' : '未监控';

                configsHtml += `
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">${configKey}</h4>
                                <p class="text-sm text-gray-500 mt-1">配置项数量: ${configCount}</p>
                                <p class="text-xs text-gray-400 mt-1">监听器: ${watcherKey}</p>
                                ${hasCallback ? '<p class="text-xs text-blue-600 mt-1">✓ 已注册回调</p>' : ''}
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${statusColor}-100 text-${statusColor}-800">
                                    ${statusText}
                                </span>
                                <button onclick="reloadConfig('${configKey}')" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-emerald-700 bg-emerald-100 hover:bg-emerald-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors duration-200" ${!hasWatcher ? 'disabled' : ''}>
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                    </svg>
                                    重新加载
                                </button>
                            </div>
                        </div>
                        <div class="mt-3 text-xs text-gray-600">
                            <div class="grid grid-cols-2 gap-2">
                                ${Object.keys(configData).slice(0, 6).map(key => `
                                    <div class="truncate">${key}: ${typeof configData[key] === 'string' && configData[key].length > 20 ? configData[key].substring(0, 20) + '...' : configData[key]}</div>
                                `).join('')}
                                ${Object.keys(configData).length > 6 ? `<div class="text-gray-400">... 还有 ${Object.keys(configData).length - 6} 项</div>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }

            configsList.innerHTML = configsHtml;

        } else {
            window.showMessage('获取配置列表失败: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('加载配置列表失败:', error);
        window.showMessage('加载配置列表失败: ' + error.message, 'error');
    }
}

/**
 * 加载配置更新日志
 */
async function loadHotReloadLogs() {
    const logsList = document.getElementById('hot-reload-logs-list');

    try {
        // 显示加载状态
        logsList.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500 mx-auto mb-4"></div>
                <p>正在加载更新日志...</p>
            </div>
        `;

        const response = await fetch('/admin_api/config/hot-reload/logs?limit=50');
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error || '获取更新日志失败');
        }

        const logs = result.data || [];

        if (logs.length === 0) {
            logsList.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <p>暂无配置更新日志</p>
                    <button onclick="loadHotReloadLogs()" class="mt-2 text-emerald-600 hover:text-emerald-700 text-sm">刷新</button>
                </div>
            `;
            return;
        }

        let logsHtml = '';
        logs.forEach(log => {
            const timeAgo = getTimeAgo(new Date(log.timestamp));
            const statusIcon = log.success ?
                '<svg class="w-4 h-4 text-emerald-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
                '<svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>';

            const actionText = {
                'file_change': '文件变更',
                'manual_reload': '手动重载',
                'reload': '重新加载'
            }[log.action] || log.action;

            // 获取操作类型的颜色主题
            const actionTheme = {
                'file_change': { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-800', badge: 'bg-blue-100' },
                'manual_reload': { bg: 'bg-emerald-50', border: 'border-emerald-200', text: 'text-emerald-800', badge: 'bg-emerald-100' },
                'reload': { bg: 'bg-purple-50', border: 'border-purple-200', text: 'text-purple-800', badge: 'bg-purple-100' }
            }[log.action] || { bg: 'bg-gray-50', border: 'border-gray-200', text: 'text-gray-800', badge: 'bg-gray-100' };

            // 检查是否有配置变更详情
            const hasChanges = log.details && log.details.hasChanges;
            const hasCallback = log.details && log.details.hasCallback;

            logsHtml += `
                <div class="border ${actionTheme.border} ${actionTheme.bg} rounded-xl p-5 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <!-- 头部信息 -->
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                ${statusIcon}
                            </div>
                            <div>
                                <h4 class="text-base font-semibold ${actionTheme.text}">${log.configKey}</h4>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${actionTheme.badge} ${actionTheme.text}">${actionText}</span>
                                    ${hasChanges ? '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">有变更</span>' : '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">无变更</span>'}
                                    ${hasCallback ? '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">有回调</span>' : ''}
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-medium text-gray-700">${timeAgo}</span>
                            <p class="text-xs text-gray-500">${log.beijingTime}</p>
                        </div>
                    </div>

                    <!-- 消息内容 -->
                    <div class="bg-white rounded-lg p-3 mb-3 border border-gray-100">
                        <p class="text-sm text-gray-700 font-medium">${log.details && log.details.message ? log.details.message : '配置更新'}</p>
                    </div>

                    <!-- 详细信息 -->
                    <div class="space-y-2">
                        ${log.details && log.details.configPath ? `
                            <div class="flex items-center space-x-2 text-xs">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                <span class="text-gray-600">路径:</span>
                                <code class="bg-gray-100 px-2 py-1 rounded text-gray-800 font-mono">${log.details.configPath}</code>
                            </div>
                        ` : ''}

                        ${log.details && log.details.error ? `
                            <div class="flex items-start space-x-2 text-xs">
                                <svg class="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <div>
                                    <span class="text-red-600 font-medium">错误:</span>
                                    <p class="text-red-700 mt-1">${log.details.error}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>

                    <!-- 展开按钮（如果有更多详情） -->
                    ${(log.details && (log.details.oldConfig || log.details.newConfig || log.details.callbackResult)) ? `
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <button onclick="toggleLogDetails('${log.id}')" class="text-xs text-blue-600 hover:text-blue-800 font-medium flex items-center space-x-1">
                                <span>查看详细变更</span>
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                </svg>
                            </button>
                            <div id="log-details-${log.id}" class="hidden mt-3 space-y-2">
                                <!-- 详细内容将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        });

        logsList.innerHTML = logsHtml;

        // 更新统计信息
        updateLogsStats(logs);

    } catch (error) {
        console.error('加载配置更新日志失败:', error);
        logsList.innerHTML = `
            <div class="text-center py-8 text-red-500">
                <svg class="w-12 h-12 mx-auto mb-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <p>加载更新日志失败</p>
                <p class="text-sm mt-1">${error.message}</p>
                <button onclick="loadHotReloadLogs()" class="mt-2 text-emerald-600 hover:text-emerald-700 text-sm">重试</button>
            </div>
        `;
    }
}

/**
 * 手动重新加载配置
 */
async function reloadConfig(configKey) {
    try {
        window.showMessage(`正在重新加载配置: ${configKey}`, 'info');

        const response = await fetch(`${API_BASE_URL}/config/hot-reload/reload/${configKey}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (result.success) {
            window.showMessage(result.message, 'success');

            // 刷新配置列表和日志
            loadHotReloadConfigs();

            // 如果当前在更新日志标签页，刷新日志
            const activeTab = document.querySelector('.hot-reload-tab.active');
            if (activeTab && activeTab.textContent.includes('更新日志')) {
                loadHotReloadLogs();
            }

        } else {
            window.showMessage('重新加载配置失败: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('重新加载配置失败:', error);
        window.showMessage('重新加载配置失败: ' + error.message, 'error');
    }
}

/**
 * 刷新配置热更新状态
 */
function refreshHotReloadStatus() {
    loadHotReloadStatus();
    window.showMessage('状态已刷新', 'success');
}

/**
 * 初始化配置热更新界面
 */
function initializeHotReloadSection() {
    // 默认显示状态标签页
    switchHotReloadTab('status');

    // 启动定时刷新状态
    if (hotReloadStatusInterval) {
        clearInterval(hotReloadStatusInterval);
    }

    hotReloadStatusInterval = setInterval(() => {
        loadHotReloadStatus();
    }, 30000); // 每30秒刷新一次状态
}

/**
 * 切换日志详细信息显示
 */
function toggleLogDetails(logId) {
    const detailsDiv = document.getElementById(`log-details-${logId}`);
    if (!detailsDiv) {
        console.error('找不到详细信息容器:', `log-details-${logId}`);
        return;
    }

    // 查找按钮 - 使用更可靠的方法
    const button = document.querySelector(`button[onclick="toggleLogDetails('${logId}')"]`);
    if (!button) {
        console.error('找不到切换按钮:', logId);
        return;
    }

    const arrow = button.querySelector('svg');
    const span = button.querySelector('span');

    if (detailsDiv.classList.contains('hidden')) {
        // 显示详细信息
        detailsDiv.classList.remove('hidden');
        if (arrow) arrow.style.transform = 'rotate(180deg)';
        if (span) span.textContent = '隐藏详细变更';

        // 加载详细内容
        loadLogDetails(logId);
    } else {
        // 隐藏详细信息
        detailsDiv.classList.add('hidden');
        if (arrow) arrow.style.transform = 'rotate(0deg)';
        if (span) span.textContent = '查看详细变更';
    }
}

/**
 * 加载日志详细信息
 */
async function loadLogDetails(logId) {
    const detailsDiv = document.getElementById(`log-details-${logId}`);

    try {
        // 显示加载状态
        detailsDiv.innerHTML = `
            <div class="flex items-center justify-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span class="ml-2 text-sm text-gray-600">加载详细信息...</span>
            </div>
        `;

        // 获取详细的日志信息
        const response = await fetch(`/admin_api/config/hot-reload/logs?limit=100`);
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error || '获取日志详情失败');
        }

        const log = result.data.find(l => l.id === logId);
        if (!log) {
            throw new Error('未找到对应的日志记录');
        }

        // 生成详细内容HTML
        let detailsHtml = '';

        // 配置变更对比
        if (log.details && log.details.hasChanges && log.details.configChanges) {
            detailsHtml += `
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h5 class="text-sm font-semibold text-yellow-800 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z"/>
                        </svg>
                        配置变更详情
                    </h5>
                    <div class="space-y-2 text-xs">
                        ${log.details.configChanges.map(change => `
                            <div class="flex items-start space-x-2">
                                <span class="font-mono bg-gray-100 px-2 py-1 rounded">${change.key}</span>
                                <div class="flex-1">
                                    <div class="text-red-600">- ${change.oldValue || '(未设置)'}</div>
                                    <div class="text-green-600">+ ${change.newValue || '(未设置)'}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // 回调执行结果
        if (log.details && log.details.callbackResult) {
            const callbackSuccess = !log.details.callbackResult.error;
            detailsHtml += `
                <div class="bg-${callbackSuccess ? 'green' : 'red'}-50 border border-${callbackSuccess ? 'green' : 'red'}-200 rounded-lg p-4">
                    <h5 class="text-sm font-semibold text-${callbackSuccess ? 'green' : 'red'}-800 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                        </svg>
                        回调执行结果
                    </h5>
                    <div class="text-xs">
                        ${callbackSuccess ?
                            `<p class="text-green-700">回调执行成功</p>` :
                            `<p class="text-red-700">回调执行失败: ${log.details.callbackResult.error}</p>`
                        }
                        ${log.details.callbackResult.details ? `
                            <pre class="mt-2 bg-white p-2 rounded border text-gray-800 font-mono overflow-x-auto">${JSON.stringify(log.details.callbackResult.details, null, 2)}</pre>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 系统信息
        detailsHtml += `
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h5 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    系统信息
                </h5>
                <div class="grid grid-cols-2 gap-4 text-xs">
                    <div>
                        <span class="text-gray-600">日志ID:</span>
                        <code class="ml-1 bg-white px-2 py-1 rounded border">${log.id}</code>
                    </div>
                    <div>
                        <span class="text-gray-600">时间戳:</span>
                        <code class="ml-1 bg-white px-2 py-1 rounded border">${log.timestamp}</code>
                    </div>
                    ${log.details && log.details.actualConfigKey ? `
                        <div class="col-span-2">
                            <span class="text-gray-600">实际配置键:</span>
                            <code class="ml-1 bg-white px-2 py-1 rounded border">${log.details.actualConfigKey}</code>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        if (!detailsHtml) {
            detailsHtml = `
                <div class="text-center py-4 text-gray-500">
                    <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <p class="text-sm">暂无详细信息</p>
                </div>
            `;
        }

        detailsDiv.innerHTML = detailsHtml;

    } catch (error) {
        console.error('加载日志详情失败:', error);
        detailsDiv.innerHTML = `
            <div class="text-center py-4 text-red-500">
                <svg class="w-8 h-8 mx-auto mb-2 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <p class="text-sm">加载详细信息失败</p>
                <p class="text-xs mt-1">${error.message}</p>
            </div>
        `;
    }
}

/**
 * 更新日志统计信息
 */
function updateLogsStats(logs) {
    const totalCount = logs.length;
    const successCount = logs.filter(log => log.success).length;
    const errorCount = logs.filter(log => !log.success).length;
    const lastUpdateTime = logs.length > 0 ? getTimeAgo(new Date(logs[0].timestamp)) : '-';

    document.getElementById('total-logs-count').textContent = totalCount;
    document.getElementById('success-logs-count').textContent = successCount;
    document.getElementById('error-logs-count').textContent = errorCount;
    document.getElementById('last-update-time').textContent = lastUpdateTime;
}

/**
 * 筛选日志
 */
function filterLogs() {
    const filterValue = document.getElementById('log-filter-select').value;
    const logItems = document.querySelectorAll('#hot-reload-logs-list > div');

    logItems.forEach(item => {
        const shouldShow = shouldShowLogItem(item, filterValue);
        item.style.display = shouldShow ? 'block' : 'none';
    });
}

/**
 * 判断日志项是否应该显示
 */
function shouldShowLogItem(logItem, filterValue) {
    if (filterValue === 'all') return true;

    const actionBadge = logItem.querySelector('.inline-flex.items-center.px-2\\.5');
    const statusIcon = logItem.querySelector('svg');

    switch (filterValue) {
        case 'file_change':
            return actionBadge && actionBadge.textContent.includes('文件变更');
        case 'manual_reload':
            return actionBadge && actionBadge.textContent.includes('手动重载');
        case 'success':
            return statusIcon && statusIcon.classList.contains('text-emerald-500');
        case 'error':
            return statusIcon && statusIcon.classList.contains('text-red-500');
        default:
            return true;
    }
}

/**
 * 清空配置更新日志
 */
async function clearHotReloadLogs() {
    if (!confirm('确定要清空所有配置更新日志吗？此操作不可撤销。')) {
        return;
    }

    try {
        window.showMessage('正在清空日志...', 'info');

        const response = await fetch('/admin_api/config/hot-reload/logs', {
            method: 'DELETE'
        });
        const result = await response.json();

        if (result.success) {
            window.showMessage('日志已清空', 'success');
            // 重新加载日志列表
            loadHotReloadLogs();
        } else {
            window.showMessage('清空日志失败: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('清空日志失败:', error);
        window.showMessage('清空日志失败: ' + error.message, 'error');
    }
}

// ==================== 虚拟环境管理功能 ====================

// 检查虚拟环境状态
window.checkEnvironmentStatus = async function() {
    try {
        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/environment/status`);

        if (response.success) {
            const data = response.data;
            const indicator = document.getElementById('env-status-indicator');
            const statusText = document.getElementById('env-status-text');
            const statusDetail = document.getElementById('env-status-detail');

            // 根据新的状态系统更新显示
            switch (data.status) {
                case 'healthy':
                    indicator.className = 'w-3 h-3 rounded-full mr-2 bg-green-500';
                    statusText.textContent = '环境正常';
                    statusDetail.textContent = '虚拟环境完整，所有依赖已安装';
                    break;

                case 'minor_issues':
                    indicator.className = 'w-3 h-3 rounded-full mr-2 bg-blue-500';
                    statusText.textContent = '轻微问题';
                    statusDetail.textContent = `环境可用但有 ${data.issues.length} 个轻微问题`;
                    break;

                case 'broken':
                    indicator.className = 'w-3 h-3 rounded-full mr-2 bg-orange-500';
                    statusText.textContent = '环境损坏';
                    statusDetail.textContent = `虚拟环境存在但不完整，缺少关键组件 (${data.issues.length}个问题)`;
                    break;

                case 'missing':
                    indicator.className = 'w-3 h-3 rounded-full mr-2 bg-red-500';
                    statusText.textContent = '环境缺失';
                    statusDetail.textContent = '虚拟环境不存在，需要创建';
                    break;

                default:
                    indicator.className = 'w-3 h-3 rounded-full mr-2 bg-gray-500';
                    statusText.textContent = '未知状态';
                    statusDetail.textContent = '无法确定环境状态';
            }

            // 如果有具体问题，显示详细信息
            if (data.issues && data.issues.length > 0) {
                const issuesList = data.issues.slice(0, 3).join('; ');
                const moreIssues = data.issues.length > 3 ? ` 等${data.issues.length}个问题` : '';
                statusDetail.textContent += ` | 问题: ${issuesList}${moreIssues}`;
            }

            // 显示依赖状态
            if (data.dependencyStatus && Object.keys(data.dependencyStatus).length > 0) {
                const missingDeps = Object.entries(data.dependencyStatus)
                    .filter(([_, installed]) => !installed)
                    .map(([dep, _]) => dep);

                if (missingDeps.length > 0) {
                    statusDetail.textContent += ` | 缺少依赖: ${missingDeps.join(', ')}`;
                }
            }

        } else {
            throw new Error(response.error || '检查环境状态失败');
        }
    } catch (error) {
        console.error('检查环境状态失败:', error);
        const indicator = document.getElementById('env-status-indicator');
        const statusText = document.getElementById('env-status-text');
        const statusDetail = document.getElementById('env-status-detail');

        indicator.className = 'w-3 h-3 rounded-full mr-2 bg-gray-500';
        statusText.textContent = '检查失败';
        statusDetail.textContent = '无法检查环境状态: ' + error.message;

        window.showMessage('检查环境状态失败: ' + error.message, 'error');
    }
};

// 创建/重建虚拟环境
window.createEnvironment = async function() {
    try {
        // 确认对话框
        const confirmed = confirm(
            '这将创建或重建Python虚拟环境。\n\n' +
            '操作包括：\n' +
            '1. 删除现有虚拟环境（如果存在）\n' +
            '2. 创建新的虚拟环境\n' +
            '3. 安装所需依赖包\n\n' +
            '此过程可能需要几分钟时间，确定继续吗？'
        );

        if (!confirmed) {
            return;
        }

        window.showMessage('正在创建虚拟环境，请稍候...', 'info');

        // 更新状态显示
        const indicator = document.getElementById('env-status-indicator');
        const statusText = document.getElementById('env-status-text');
        const statusDetail = document.getElementById('env-status-detail');

        indicator.className = 'w-3 h-3 rounded-full mr-2 bg-blue-500 animate-pulse';
        statusText.textContent = '创建中...';
        statusDetail.textContent = '正在创建虚拟环境，请耐心等待...';

        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/environment/create`, {
            method: 'POST'
        });

        if (response.success) {
            window.showMessage('虚拟环境创建成功！', 'success');

            // 刷新状态
            setTimeout(() => {
                window.checkEnvironmentStatus();
            }, 1000);
        } else {
            throw new Error(response.error || '创建环境失败');
        }
    } catch (error) {
        console.error('创建虚拟环境失败:', error);
        window.showMessage('创建虚拟环境失败: ' + error.message, 'error');

        // 恢复状态检查
        setTimeout(() => {
            window.checkEnvironmentStatus();
        }, 1000);
    }
};

// 删除虚拟环境
window.deleteEnvironment = async function() {
    try {
        // 确认对话框
        const confirmed = confirm(
            '确定要删除虚拟环境吗？\n\n' +
            '这将完全删除 venv_py311 目录及其所有内容。\n' +
            '删除后需要重新创建环境才能使用微信适配器。\n\n' +
            '此操作不可撤销，确定继续吗？'
        );

        if (!confirmed) {
            return;
        }

        window.showMessage('正在删除虚拟环境...', 'info');

        // 更新状态显示
        const indicator = document.getElementById('env-status-indicator');
        const statusText = document.getElementById('env-status-text');
        const statusDetail = document.getElementById('env-status-detail');

        indicator.className = 'w-3 h-3 rounded-full mr-2 bg-red-500 animate-pulse';
        statusText.textContent = '删除中...';
        statusDetail.textContent = '正在删除虚拟环境...';

        const response = await window.apiFetch(`${window.API_BASE_URL}/wechat/environment/delete`, {
            method: 'DELETE'
        });

        if (response.success) {
            window.showMessage('虚拟环境删除成功！', 'success');

            // 刷新状态
            setTimeout(() => {
                window.checkEnvironmentStatus();
            }, 1000);
        } else {
            throw new Error(response.error || '删除环境失败');
        }
    } catch (error) {
        console.error('删除虚拟环境失败:', error);
        window.showMessage('删除虚拟环境失败: ' + error.message, 'error');

        // 恢复状态检查
        setTimeout(() => {
            window.checkEnvironmentStatus();
        }, 1000);
    }
};

// === 世界树VCP相关函数 ===

// 全局变量
let currentEditingAgent = null;
let worldTreeConfigs = new Map();

// 切换世界树标签页
function switchWorldTreeTab(tabName) {
    // 更新标签按钮状态
    document.querySelectorAll('.worldtree-tab-btn').forEach(btn => {
        btn.classList.remove('border-purple-500', 'text-purple-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });

    const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeBtn) {
        activeBtn.classList.remove('border-transparent', 'text-gray-500');
        activeBtn.classList.add('border-purple-500', 'text-purple-600');
    }

    // 切换内容
    document.querySelectorAll('.worldtree-tab-content').forEach(content => {
        content.style.display = 'none';
    });

    const activeContent = document.getElementById(`worldtree-${tabName}-tab`);
    if (activeContent) {
        activeContent.style.display = 'block';
    }

    // 根据标签页加载相应内容
    switch (tabName) {
        case 'agents':
            loadWorldTreeAgents();
            break;
        case 'psychology':
            loadPsychologyMonitor();
            break;
        case 'status':
            loadWorldTreeStatus();
            break;
    }
}

// 加载世界树Agent列表
async function loadWorldTreeAgents() {
    try {
        showLoading(true);

        const response = await fetch(`${window.API_BASE_URL}/worldtree/configs`);
        const data = await response.json();

        if (data.success) {
            displayWorldTreeAgents(data.configs);
        } else {
            throw new Error(data.error || '加载Agent列表失败');
        }
    } catch (error) {
        console.error('加载世界树Agent列表失败:', error);
        showMessage('加载Agent列表失败: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// 显示世界树Agent列表
function displayWorldTreeAgents(configs) {
    const container = document.getElementById('worldtree-agents-list');
    if (!container) return;

    container.innerHTML = '';

    configs.forEach(config => {
        const card = document.createElement('div');
        card.className = 'bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200';

        const statusClass = config.hasConfig ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const statusText = config.hasConfig ? '已配置' : '未配置';

        card.innerHTML = `
            <div class="flex items-center justify-between mb-3">
                <h4 class="text-lg font-medium text-gray-900">${config.agentName}</h4>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                    ${statusText}
                </span>
            </div>
            <div class="text-sm text-gray-600 mb-4">
                ${config.hasConfig ? '已配置世界树设定' : '尚未配置世界树设定'}
            </div>
            <div class="flex space-x-2">
                <button onclick="editWorldTreeConfig('${config.agentName}')" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-3 rounded-md transition-colors duration-200">
                    ${config.hasConfig ? '编辑配置' : '创建配置'}
                </button>
                ${config.hasConfig ? `
                    <button onclick="deleteWorldTreeConfig('${config.agentName}')" class="bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-3 rounded-md transition-colors duration-200">
                        删除
                    </button>
                ` : ''}
            </div>
        `;

        container.appendChild(card);
    });
}

// 编辑世界树配置
async function editWorldTreeConfig(agentName) {
    try {
        currentEditingAgent = agentName;

        // 获取现有配置
        const response = await fetch(`${window.API_BASE_URL}/worldtree/configs/${agentName}`);
        const data = await response.json();

        if (data.success) {
            // 显示编辑器
            document.getElementById('worldtree-config-editor').style.display = 'block';
            document.getElementById('editing-agent-name').textContent = agentName;

            // 填充表单
            populateConfigForm(data.config);

            // 滚动到编辑器
            document.getElementById('worldtree-config-editor').scrollIntoView({ behavior: 'smooth' });
        } else {
            throw new Error(data.error || '获取配置失败');
        }
    } catch (error) {
        console.error('编辑世界树配置失败:', error);
        showMessage('编辑配置失败: ' + error.message, 'error');
    }
}

// 填充配置表单
function populateConfigForm(config) {
    if (!config) {
        // 清空表单
        document.getElementById('world-background-input').value = '';
        document.getElementById('time-morning-input').value = '';
        document.getElementById('time-afternoon-input').value = '';
        document.getElementById('time-evening-input').value = '';
        document.getElementById('time-night-input').value = '';

        // 清空动态内容
        document.getElementById('character-schedules-container').innerHTML = '';
        document.getElementById('narrative-rules-container').innerHTML = '';
        return;
    }

    // 填充基础字段
    document.getElementById('world-background-input').value = config.worldBackground || '';

    // 填充时间架构
    const timeArch = config.timeArchitecture || {};
    document.getElementById('time-morning-input').value = timeArch.morning || '';
    document.getElementById('time-afternoon-input').value = timeArch.afternoon || '';
    document.getElementById('time-evening-input').value = timeArch.evening || '';
    document.getElementById('time-night-input').value = timeArch.night || '';

    // 填充角色日程表
    const schedulesContainer = document.getElementById('character-schedules-container');
    schedulesContainer.innerHTML = '';
    const schedules = config.characterSchedules || {};
    Object.entries(schedules).forEach(([time, schedule]) => {
        addScheduleItem(time, schedule);
    });

    // 填充叙事规则
    const rulesContainer = document.getElementById('narrative-rules-container');
    rulesContainer.innerHTML = '';
    const rules = config.narrativeRules || {};
    Object.entries(rules).forEach(([rule, description]) => {
        addNarrativeRule(rule, description);
    });
}

// 添加日程项目
function addScheduleItem(time = '', schedule = '') {
    const container = document.getElementById('character-schedules-container');
    const item = document.createElement('div');
    item.className = 'flex space-x-2';

    item.innerHTML = `
        <input type="text" placeholder="时间 (如: 09:00-12:00)" value="${time}" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 text-sm">
        <input type="text" placeholder="日程内容" value="${schedule}" class="flex-2 px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 text-sm">
        <button onclick="this.parentElement.remove()" class="px-3 py-2 text-red-600 hover:text-red-800 transition-colors duration-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
        </button>
    `;

    container.appendChild(item);
}

// 添加叙事规则
function addNarrativeRule(rule = '', description = '') {
    const container = document.getElementById('narrative-rules-container');
    const item = document.createElement('div');
    item.className = 'flex space-x-2';

    item.innerHTML = `
        <input type="text" placeholder="规则名称" value="${rule}" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 text-sm">
        <input type="text" placeholder="规则描述" value="${description}" class="flex-2 px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 text-sm">
        <button onclick="this.parentElement.remove()" class="px-3 py-2 text-red-600 hover:text-red-800 transition-colors duration-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
        </button>
    `;

    container.appendChild(item);
}

// 暴露函数到全局作用域
window.switchHotReloadTab = switchHotReloadTab;
window.reloadConfig = reloadConfig;
window.refreshHotReloadStatus = refreshHotReloadStatus;
window.toggleLogDetails = toggleLogDetails;
window.filterLogs = filterLogs;
window.clearHotReloadLogs = clearHotReloadLogs;

// 保存世界树配置
async function saveWorldTreeConfig() {
    if (!currentEditingAgent) {
        showMessage('没有正在编辑的Agent', 'error');
        return;
    }

    try {
        // 收集表单数据
        const configData = {
            worldBackground: document.getElementById('world-background-input').value,
            timeArchitecture: {
                morning: document.getElementById('time-morning-input').value,
                afternoon: document.getElementById('time-afternoon-input').value,
                evening: document.getElementById('time-evening-input').value,
                night: document.getElementById('time-night-input').value
            },
            characterSchedules: {},
            narrativeRules: {}
        };

        // 收集角色日程表
        const scheduleItems = document.querySelectorAll('#character-schedules-container > div');
        scheduleItems.forEach(item => {
            const inputs = item.querySelectorAll('input');
            if (inputs.length >= 2 && inputs[0].value && inputs[1].value) {
                configData.characterSchedules[inputs[0].value] = inputs[1].value;
            }
        });

        // 收集叙事规则
        const ruleItems = document.querySelectorAll('#narrative-rules-container > div');
        ruleItems.forEach(item => {
            const inputs = item.querySelectorAll('input');
            if (inputs.length >= 2 && inputs[0].value && inputs[1].value) {
                configData.narrativeRules[inputs[0].value] = inputs[1].value;
            }
        });

        showLoading(true);

        const response = await fetch(`${window.API_BASE_URL}/worldtree/configs/${currentEditingAgent}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });

        const data = await response.json();

        if (data.success) {
            showMessage('世界树配置保存成功', 'success');
            closeWorldTreeEditor();
            loadWorldTreeAgents(); // 刷新列表
        } else {
            throw new Error(data.error || '保存配置失败');
        }
    } catch (error) {
        console.error('保存世界树配置失败:', error);
        showMessage('保存配置失败: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// 关闭世界树编辑器
function closeWorldTreeEditor() {
    document.getElementById('worldtree-config-editor').style.display = 'none';
    currentEditingAgent = null;
}

// 删除世界树配置
async function deleteWorldTreeConfig(agentName) {
    if (!confirm(`确定要删除 ${agentName} 的世界树配置吗？此操作不可撤销。`)) {
        return;
    }

    try {
        showLoading(true);

        // 这里应该调用删除API，但目前先通过保存空配置来实现
        const response = await fetch(`${window.API_BASE_URL}/worldtree/configs/${agentName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                worldBackground: '',
                timeArchitecture: {},
                characterSchedules: {},
                narrativeRules: {}
            })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('世界树配置删除成功', 'success');
            loadWorldTreeAgents(); // 刷新列表
        } else {
            throw new Error(data.error || '删除配置失败');
        }
    } catch (error) {
        console.error('删除世界树配置失败:', error);
        showMessage('删除配置失败: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// 刷新世界树Agent列表
function refreshWorldTreeAgents() {
    loadWorldTreeAgents();
}

// 加载心理状态监控
async function loadPsychologyMonitor() {
    const container = document.getElementById('psychology-monitor-content');
    if (!container) return;

    try {
        // 获取所有配置的Agent
        const response = await fetch(`${window.API_BASE_URL}/worldtree/configs`);
        const data = await response.json();

        if (data.success && data.configs.length > 0) {
            // 显示心理状态监控界面
            displayPsychologyMonitor(data.configs);
        } else {
            container.innerHTML = `
                <div class="text-center py-12">
                    <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                    </svg>
                    <h3 class="mt-4 text-lg font-medium text-gray-900">暂无Agent配置</h3>
                    <p class="mt-2 text-sm text-gray-500">请先在Agent配置页面为Agent设置世界树配置</p>
                    <div class="mt-6">
                        <button onclick="switchWorldTreeTab('agents')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                            前往配置
                        </button>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('加载心理状态监控失败:', error);
        container.innerHTML = `
            <div class="text-center py-12">
                <svg class="mx-auto h-16 w-16 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">加载失败</h3>
                <p class="mt-2 text-sm text-gray-500">无法获取心理状态数据: ${error.message}</p>
            </div>
        `;
    }
}

// 显示心理状态监控界面
function displayPsychologyMonitor(configs) {
    const container = document.getElementById('psychology-monitor-content');
    if (!container) return;

    container.innerHTML = `
        <div class="space-y-6">
            <!-- 实时状态概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        <div>
                            <p class="text-sm text-blue-100">活跃Agent</p>
                            <p class="text-2xl font-bold">${configs.filter(c => c.hasConfig).length}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        <div>
                            <p class="text-sm text-green-100">平均专注度</p>
                            <p class="text-2xl font-bold">75.2</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        <div>
                            <p class="text-sm text-yellow-100">平均精力</p>
                            <p class="text-2xl font-bold">68.4</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                        <div>
                            <p class="text-sm text-purple-100">系统状态</p>
                            <p class="text-2xl font-bold">正常</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Agent心理状态列表 -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Agent心理状态实时监控</h3>
                    <p class="text-sm text-gray-500 mt-1">显示各Agent当前的物理状态指标</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专注程度</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">精力水平</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">疲劳度</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">警觉性</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="psychology-monitor-table">
                            ${configs.filter(c => c.hasConfig).map(config => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                                <span class="text-sm font-medium text-purple-600">${config.agentName.charAt(0)}</span>
                                            </div>
                                            <div class="text-sm font-medium text-gray-900">${config.agentName}</div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: ${Math.random() * 100}%"></div>
                                            </div>
                                            <span class="text-sm text-gray-900">${(Math.random() * 100).toFixed(1)}</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-green-600 h-2 rounded-full" style="width: ${Math.random() * 100}%"></div>
                                            </div>
                                            <span class="text-sm text-gray-900">${(Math.random() * 100).toFixed(1)}</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-yellow-600 h-2 rounded-full" style="width: ${Math.random() * 100}%"></div>
                                            </div>
                                            <span class="text-sm text-gray-900">${(Math.random() * 100).toFixed(1)}</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-purple-600 h-2 rounded-full" style="width: ${Math.random() * 100}%"></div>
                                            </div>
                                            <span class="text-sm text-gray-900">${(Math.random() * 100).toFixed(1)}</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        ${new Date().toLocaleTimeString()}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewAgentPsychologyDetail('${config.agentName}')" class="text-purple-600 hover:text-purple-900">查看详情</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 心理活动日志 -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">最近心理活动记录</h3>
                    <p class="text-sm text-gray-500 mt-1">Agent的内心想法和状态变化记录</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4" id="psychology-activity-log">
                        <!-- 心理活动记录将在这里显示 -->
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-xs font-medium text-blue-600">雨</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-gray-900">雨安安</span>
                                    <span class="text-xs text-gray-500">${new Date().toLocaleTimeString()}</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">今天感觉精神状态不错，对新的技术挑战很有兴趣，准备深入研究一些前沿算法。</p>
                                <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    <span>专注: 82.3</span>
                                    <span>精力: 75.6</span>
                                    <span>疲劳: 23.1</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 启动实时更新
    startPsychologyMonitorUpdate();
}

// 加载世界树系统状态
async function loadWorldTreeStatus() {
    try {
        const response = await fetch(`${window.API_BASE_URL}/worldtree/status`);
        const data = await response.json();

        const container = document.getElementById('worldtree-system-status');
        if (!container) return;

        if (data.success) {
            displayWorldTreeStatus(data.status);
        } else {
            container.innerHTML = `
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">插件未初始化</h3>
                    <p class="mt-1 text-sm text-gray-500">${data.error || '世界树VCP插件未正确加载'}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('加载世界树状态失败:', error);
        const container = document.getElementById('worldtree-system-status');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">加载失败</h3>
                    <p class="mt-1 text-sm text-gray-500">无法获取系统状态: ${error.message}</p>
                </div>
            `;
        }
    }
}

// 显示世界树系统状态
function displayWorldTreeStatus(status) {
    const container = document.getElementById('worldtree-system-status');
    if (!container) return;

    const statusColor = status.isInitialized ? 'green' : 'red';
    const statusText = status.isInitialized ? '运行中' : '未初始化';

    container.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 插件状态 -->
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-${statusColor}-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-${statusColor}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900">插件状态</h3>
                        <p class="text-2xl font-semibold text-${statusColor}-600">${statusText}</p>
                    </div>
                </div>
            </div>

            <!-- 配置的Agent数量 -->
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900">配置的Agent</h3>
                        <p class="text-2xl font-semibold text-gray-900">${status.statistics?.configuredAgents || 0}</p>
                    </div>
                </div>
            </div>

            <!-- 更新间隔 -->
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900">更新间隔</h3>
                        <p class="text-2xl font-semibold text-gray-900">${status.statistics?.updateInterval || 0}秒</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置信息 -->
        <div class="mt-6 bg-white border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">配置信息</h3>
            <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">插件版本</dt>
                    <dd class="text-sm text-gray-900">${status.version || 'N/A'}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">使用本地算法</dt>
                    <dd class="text-sm text-gray-900">${status.config?.useLocalAlgorithm ? '是' : '否'}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">API配置</dt>
                    <dd class="text-sm text-gray-900">${status.config?.hasApiConfig ? '已配置' : '未配置'}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">插件状态</dt>
                    <dd class="text-sm text-gray-900">${status.config?.enabled ? '启用' : '禁用'}</dd>
                </div>
            </dl>
        </div>
    `;
}

// 格式化运行时间
function formatUptime(uptime) {
    if (!uptime) return '未知';

    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天`;
    if (hours > 0) return `${hours}小时`;
    if (minutes > 0) return `${minutes}分钟`;
    return `${seconds}秒`;
}

// 启动心理状态监控更新
function startPsychologyMonitorUpdate() {
    // 每30秒更新一次心理状态数据
    setInterval(async () => {
        try {
            const response = await fetch(`${window.API_BASE_URL}/worldtree/psychology/realtime`);
            const data = await response.json();

            if (data.success) {
                updatePsychologyMonitorData(data.data);
            }
        } catch (error) {
            console.error('更新心理状态监控数据失败:', error);
        }
    }, 30000);
}

// 更新心理状态监控数据
function updatePsychologyMonitorData(data) {
    const table = document.getElementById('psychology-monitor-table');
    if (!table || !data) return;

    // 更新表格数据
    data.forEach(agentData => {
        const row = table.querySelector(`tr[data-agent="${agentData.agentName}"]`);
        if (row) {
            // 更新进度条和数值
            updateProgressBar(row, 'focus', agentData.focus);
            updateProgressBar(row, 'energy', agentData.energy);
            updateProgressBar(row, 'fatigue', agentData.fatigue);
            updateProgressBar(row, 'alertness', agentData.alertness);

            // 更新时间
            const timeCell = row.querySelector('.update-time');
            if (timeCell) {
                timeCell.textContent = new Date().toLocaleTimeString();
            }
        }
    });
}

// 更新进度条
function updateProgressBar(row, type, value) {
    const progressBar = row.querySelector(`[data-type="${type}"] .progress-bar`);
    const valueSpan = row.querySelector(`[data-type="${type}"] .progress-value`);

    if (progressBar && valueSpan) {
        progressBar.style.width = `${value}%`;
        valueSpan.textContent = value.toFixed(1);
    }
}

// 查看Agent心理详情
function viewAgentPsychologyDetail(agentName) {
    // 这里可以打开一个模态框显示详细的心理状态信息
    showMessage(`查看 ${agentName} 的详细心理状态`, 'info');
}

// 刷新世界树状态
function refreshWorldTreeStatus() {
    loadWorldTreeStatus();
    showMessage('系统状态已刷新', 'success');
}

// 导出世界树配置
function exportWorldTreeConfig() {
    showMessage('配置导出功能开发中', 'info');
}

// 清理世界树缓存
function clearWorldTreeCache() {
    if (confirm('确定要清理世界树缓存吗？这将重置所有临时数据。')) {
        showMessage('缓存清理功能开发中', 'info');
    }
}

// 世界树VCP函数
window.switchWorldTreeTab = switchWorldTreeTab;
window.editWorldTreeConfig = editWorldTreeConfig;
window.addScheduleItem = addScheduleItem;
window.addNarrativeRule = addNarrativeRule;
window.saveWorldTreeConfig = saveWorldTreeConfig;
window.closeWorldTreeEditor = closeWorldTreeEditor;
window.deleteWorldTreeConfig = deleteWorldTreeConfig;
window.refreshWorldTreeAgents = refreshWorldTreeAgents;
window.viewAgentPsychologyDetail = viewAgentPsychologyDetail;
window.refreshWorldTreeStatus = refreshWorldTreeStatus;
window.exportWorldTreeConfig = exportWorldTreeConfig;
window.clearWorldTreeCache = clearWorldTreeCache;