/**
 * 聊天记录插件
 * 自动记录所有群聊和私聊消息到本地文件
 */

const BasePlugin = require('./base_plugin');
const { helper } = require('./plugin_helper');
const path = require('path');
const fs = require('fs');
const https = require('https');
const http = require('http');

class ChatRecordPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('chat_record_plugin', adapter, logger, config);

        // 设置优先级和权限
        this.priority = 1; // 最高优先级，确保最先处理
        this.permission = 'all'; // 所有用户都可以触发记录
        this.supportedTypes = ['message', 'notice'];

        // 插件元信息
        this.meta = {
            author: 'VCPToolBox',
            version: '1.0.0',
            description: '实时记录保存群聊和私聊消息到本地文件',
            usage: '自动记录所有消息，支持"聊天记录状态"查询',
            example: '聊天记录状态'
        };

        // 设置总是触发
        this.triggers.always = true;

        // 创建NapCat API实例
        this.napCatAPI = helper.createNapCatAPI(adapter);

        // 调试模式设置
        this.DEBUG_MODE = false; // 设置为true启用调试输出

        // 配置选项
        this.recordConfig = {
            enableGroupRecord: true,    // 是否记录群聊
            enablePrivateRecord: true,  // 是否记录私聊
            enableMediaDownload: true,  // 是否下载媒体文件到本地
            enableNoticeRecord: true,   // 是否记录notice事件
            excludeGroups: [],          // 排除的群号列表
            excludeUsers: [],           // 排除的用户QQ号列表
            maxLogFileSize: 10 * 1024 * 1024, // 最大日志文件大小 (10MB)
            maxMessageLength: 240,      // 最大消息长度
        };

        console.log(`✅ ${this.name} 插件初始化完成`);
    }

    /**
     * 处理消息 - BasePlugin要求的方法
     * @param {Object} context 消息上下文
     * @returns {Object} 处理结果
     */
    async handle(context) {
        try {
            // 添加简化的调试日志
            if (this.DEBUG_MODE) {
                console.log(`[DEBUG] 聊天记录插件收到事件:`, {
                    type: context.event?.post_type,
                    messageType: context.type,
                    userId: context.userId,
                    message: context.message?.substring(0, 50) + (context.message?.length > 50 ? '...' : '')
                });
            }

            // 异步保存聊天记录，不阻塞其他插件
            setImmediate(async () => {
                try {
                    if (context.event && context.event.post_type === 'message') {
                        if (this.DEBUG_MODE) console.log(`[DEBUG] 开始保存聊天记录`);
                        await this.saveCompleteRecord(context);
                    } else if (context.event && context.event.post_type === 'notice') {
                        if (this.DEBUG_MODE) console.log(`[DEBUG] 开始保存notice记录`);
                        await this.handleNoticeEvent(context);
                    }
                } catch (error) {
                    console.error(`[ERROR] 聊天记录保存异常:`, error);
                }
            });

            // 处理管理命令
            if (context.event && context.event.post_type === 'message' && this.isManagementCommand(context.message)) {
                if (this.DEBUG_MODE) console.log(`[DEBUG] 处理管理命令:`, context.message);
                return await this.handleManagementCommand(context);
            }

            // 返回未处理，让其他插件继续处理
            return { handled: false };

        } catch (error) {
            console.error(`[ERROR] 聊天记录插件处理异常:`, error);
            return { handled: false };
        }
    }

    /**
     * 检查是否应该记录此消息
     * @param {Object} context 消息上下文
     * @returns {boolean} 是否记录
     */
    shouldRecord(context) {
        // 记录所有消息类型，包括bot自己的消息
        return context.type === 'group' || context.type === 'private';
    }

    /**
     * 处理notice事件（戳一戳等），统一记录到聊天记录中
     * @param {Object} context 消息上下文
     */
    async handleNoticeEvent(context) {
        try {
            const event = context.event;
            // 创建简化的事件信息用于调试输出
            const debugEvent = {
                time: event.time,
                self_id: event.self_id,
                post_type: event.post_type,
                notice_type: event.notice_type,
                sub_type: event.sub_type,
                user_id: event.user_id,
                target_id: event.target_id,
                group_id: event.group_id,
                status_text: event.status_text,
                event_type: event.event_type
            };
            if (this.DEBUG_MODE) {
                console.log(`[DEBUG] 处理notice事件:`, JSON.stringify(debugEvent, null, 2));
            }

            // 处理戳一戳事件 - 转换为聊天记录格式
            if (event.notice_type === 'notify' && event.sub_type === 'poke') {
                await this.handlePokeEvent(context);
            }
            // 处理其他notice事件类型
            else if (event.notice_type === 'group_recall') {
                // 群消息撤回
                if (this.DEBUG_MODE) {
                    console.log(`[DEBUG] 群消息撤回事件: 群${event.group_id}, 操作者${event.operator_id}, 消息ID${event.message_id}`);
                }
                await this.handleRecallEvent(context);
            }
            else if (event.notice_type === 'friend_recall') {
                // 好友消息撤回
                if (this.DEBUG_MODE) {
                    console.log(`[DEBUG] 好友消息撤回事件: 用户${event.user_id}, 消息ID${event.message_id}`);
                }
                await this.handleRecallEvent(context);
            }
        } catch (error) {
            console.error(`[ERROR] 处理notice事件失败:`, error);
        }
    }



    /**
     * 获取用户昵称
     * @param {string} userId 用户ID
     * @param {string} groupId 群ID（可选）
     * @returns {string} 用户昵称
     */
    async getUserNickname(userId, groupId = null, context = null) {
        try {
            // 优先使用context中的WebSocket连接，否则尝试获取第一个可用连接
            let ws = null;
            let selfId = null;

            if (context && context.ws && context.selfId) {
                ws = context.ws;
                selfId = context.selfId;
            } else {
                // 尝试获取第一个可用的NapCat连接
                const clients = this.adapter.napCatClients;
                if (clients && clients.size > 0) {
                    const firstClient = clients.values().next().value;
                    ws = firstClient;
                    // 从配置或其他地方获取selfId
                    selfId = this.adapter.config?.SELF_ID || '123456789';
                }
            }

            if (!ws) {
                // 静默处理，不输出错误日志，直接返回默认值
                return `用户${userId}`;
            }

            const bot = { ws: ws, selfId: selfId };

            if (groupId) {
                // 群聊中获取群名片
                const memberInfo = await this.napCatAPI.user.getGroupMemberInfo(bot, groupId, userId);
                if (memberInfo.retcode === 0 && memberInfo.data) {
                    return memberInfo.data.card || memberInfo.data.nickname || `用户${userId}`;
                }
            }

            // 获取用户信息
            const userInfo = await this.napCatAPI.user.getInfo(bot, userId);
            if (userInfo.retcode === 0 && userInfo.data) {
                return userInfo.data.nickname || `用户${userId}`;
            }

            return `用户${userId}`;
        } catch (error) {
            console.error(`[ERROR] 获取用户昵称失败:`, error);
            return `用户${userId}`;
        }
    }

    /**
     * 获取群信息
     * @param {string} groupId 群ID
     * @param {Object} context 上下文对象（可选）
     * @returns {Object|null} 群信息
     */
    async getGroupInfo(groupId, context = null) {
        try {
            // 优先使用context中的WebSocket连接，否则尝试获取第一个可用连接
            let ws = null;
            let selfId = null;

            if (context && context.ws && context.selfId) {
                ws = context.ws;
                selfId = context.selfId;
            } else {
                // 尝试获取第一个可用的NapCat连接
                const clients = this.adapter.napCatClients;
                if (clients && clients.size > 0) {
                    const firstClient = clients.values().next().value;
                    ws = firstClient;
                    // 从配置或其他地方获取selfId
                    selfId = this.adapter.bot.config?.SELF_ID || '123456789';
                }
            }

            if (!ws) {
                // 静默处理，不输出错误日志，直接返回默认值
                return null;
            }

            const bot = { ws: ws, selfId: selfId };
            const groupInfo = await this.napCatAPI.group.getInfo(bot, groupId);
            if (groupInfo.retcode === 0 && groupInfo.data) {
                return groupInfo.data;
            }
            return null;
        } catch (error) {
            console.error(`[ERROR] 获取群信息失败:`, error);
            return null;
        }
    }



    /**
     * 处理At信息，获取真实用户名
     * @param {Array} atInfo At信息数组
     * @param {string} groupId 群ID
     * @param {Object} context 上下文对象
     * @returns {Array} 处理后的At信息
     */
    async processAtInfo(atInfo, groupId, context) {
        const result = [];
        for (const at of atInfo) {
            try {
                // 提取QQ号
                const qqMatch = at.match(/\((\d+)\)/);
                if (qqMatch) {
                    const qq = qqMatch[1];
                    const nickname = await this.getUserNickname(qq, groupId, context);
                    result.push(`${nickname}(QQ号:${qq})`);
                } else {
                    result.push(at);
                }
            } catch (error) {
                result.push(at);
            }
        }
        return result;
    }

    /**
     * 检查是否是管理命令
     * @param {string} message 消息内容
     * @returns {boolean} 是否是管理命令
     */
    isManagementCommand(message) {
        const decodedMessage = this.htmlDecode(message);
        return decodedMessage === '聊天记录状态' ||
               decodedMessage === 'chat_log_status' ||
               decodedMessage.startsWith('聊天记录配置') ||
               decodedMessage.startsWith('chat_log_config') ||
               decodedMessage.startsWith('查看聊天记录') ||
               decodedMessage.startsWith('chat_history');
    }

    /**
     * 保存完整的聊天记录（包括文件、图片等）
     * @param {Object} context 消息上下文
     */
    async saveCompleteRecord(context) {
        try {
            // 处理消息段，提取所有内容信息
            const processedContent = await this.processMessageSegments(context);

            // 格式化聊天记录
            const logEntry = await this.formatChatLog(context, processedContent);

            // 保存到文件
            await this.saveToFile(context, logEntry);

        } catch (error) {
            console.error(`[ERROR] 保存聊天记录失败:`, error);
        }
    }

    /**
     * 处理戳一戳事件，转换为聊天记录格式
     * @param {Object} context 消息上下文
     */
    async handlePokeEvent(context) {
        try {
            const event = context.event;
            const operatorId = event.operator_id || event.user_id;
            const targetId = event.target_id || event.self_id;

            // 获取用户昵称
            const operatorName = await this.getUserNickname(operatorId, context.groupId, context);
            const targetName = await this.getUserNickname(targetId, context.groupId, context);

            // 创建模拟的消息上下文
            const pokeContext = {
                ...context,
                userId: operatorId,
                sender: { nickname: operatorName },
                message: `互动：戳了戳${targetName}(QQ号:${targetId})`,
                type: context.groupId ? 'group' : 'private'
            };

            // 格式化并保存记录
            const timestamp = helper.formatTimestamp(event.time);
            let logEntry = '';

            if (context.groupId) {
                const groupInfo = await this.getGroupInfo(context.groupId, context);
                const groupName = groupInfo ? groupInfo.group_name : '未知群聊';
                logEntry = `[群聊:${context.groupId}(${groupName})][${timestamp}] ${operatorName}(QQ号:${operatorId}) 互动：戳了戳${targetName}(QQ号:${targetId})`;
            } else {
                logEntry = `[${operatorName}和${targetName}的私聊][${timestamp}] ${operatorName}(QQ号:${operatorId}) 互动：戳了戳${targetName}(QQ号:${targetId})`;
            }

            await this.saveToFile(pokeContext, logEntry);

        } catch (error) {
            console.error(`[ERROR] 处理戳一戳事件失败:`, error);
        }
    }

    /**
     * 处理撤回事件，转换为聊天记录格式
     * @param {Object} context 消息上下文
     */
    async handleRecallEvent(context) {
        try {
            const event = context.event;
            const timestamp = helper.formatTimestamp(event.time);

            let logEntry = '';
            if (event.notice_type === 'group_recall') {
                const operatorId = event.operator_id;
                const userId = event.user_id;
                const messageId = event.message_id;

                const operatorName = await this.getUserNickname(operatorId, event.group_id, context);
                const userName = await this.getUserNickname(userId, event.group_id, context);

                const groupInfo = await this.getGroupInfo(event.group_id, context);
                const groupName = groupInfo ? groupInfo.group_name : '未知群聊';

                if (operatorId === userId) {
                    logEntry = `[群聊:${event.group_id}(${groupName})][${timestamp}] ${userName}(QQ号:${userId}) 撤回了自己的消息(ID:${messageId})`;
                } else {
                    logEntry = `[群聊:${event.group_id}(${groupName})][${timestamp}] ${operatorName}(QQ号:${operatorId}) 撤回了${userName}(QQ号:${userId})的消息(ID:${messageId})`;
                }

                const recallContext = { ...context, type: 'group', groupId: event.group_id };
                await this.saveToFile(recallContext, logEntry);

            } else if (event.notice_type === 'friend_recall') {
                const userId = event.user_id;
                const messageId = event.message_id;

                const userName = await this.getUserNickname(userId, null, context);
                logEntry = `[私聊][${timestamp}] ${userName}(QQ号:${userId}) 撤回了消息(ID:${messageId})`;

                const recallContext = { ...context, type: 'private', userId: userId };
                await this.saveToFile(recallContext, logEntry);
            }

        } catch (error) {
            console.error(`[ERROR] 处理撤回事件失败:`, error);
        }
    }



    /**
     * 处理消息段，提取所有内容信息
     * @param {Object} context 消息上下文
     * @returns {Object} 处理后的内容信息
     */
    async processMessageSegments(context) {
        const messageSegments = context.segments || [];
        const bot = { ws: context.ws, selfId: context.selfId };

        const result = {
            textContent: '',
            mediaInfo: [],
            atInfo: [],
            faceInfo: [],
            otherInfo: []
        };

        // 提取当前消息的所有内容
        let allFiles = helper.extractFiles(messageSegments);
        let allImages = helper.extractImages(messageSegments);
        let allVideos = messageSegments.filter(seg => seg.type === 'video');
        let allRecords = messageSegments.filter(seg => seg.type === 'record');
        let allTexts = helper.extractTexts(messageSegments);
        let allAts = helper.extractAts(messageSegments);
        let allFaces = helper.extractFaces(messageSegments);
        let allOthers = helper.extractOthers(messageSegments);

        // 检查是否有引用消息
        const replies = helper.extractReplies(messageSegments);
        if (replies.length > 0) {
            for (const reply of replies) {
                try {
                    const referencedMsg = await this.napCatAPI.message.get(bot, reply.id);
                    if (referencedMsg && referencedMsg.retcode === 0 && referencedMsg.data) {
                        const refMessage = referencedMsg.data.message || [];

                        // 合并引用消息的内容
                        allFiles = allFiles.concat(helper.extractFiles(refMessage));
                        allImages = allImages.concat(helper.extractImages(refMessage));
                        allVideos = allVideos.concat(refMessage.filter(seg => seg.type === 'video'));
                        allRecords = allRecords.concat(refMessage.filter(seg => seg.type === 'record'));
                        allTexts = allTexts.concat(helper.extractTexts(refMessage));
                        allAts = allAts.concat(helper.extractAts(refMessage));
                        allFaces = allFaces.concat(helper.extractFaces(refMessage));
                        allOthers = allOthers.concat(helper.extractOthers(refMessage));
                    }
                } catch (error) {
                    console.error(`[ERROR] 获取引用消息异常:`, error);
                }
            }
        }

        // 处理文本内容
        result.textContent = allTexts.map(t => t.text).join('');

        // 处理At信息
        result.atInfo = allAts.map(at => `@${at.name}(${at.qq})`);

        // 处理表情信息
        result.faceInfo = allFaces.map(face => `[表情:${face.name || face.id}]`);

        // 处理媒体文件
        await this.processMediaFiles(bot, allFiles, allImages, allVideos, allRecords, result, context);

        return result;
    }

    /**
     * 处理媒体文件，获取本地路径
     * @param {Object} bot Bot实例
     * @param {Array} allFiles 文件列表
     * @param {Array} allImages 图片列表
     * @param {Array} allVideos 视频列表
     * @param {Array} allRecords 语音列表
     * @param {Object} result 结果对象
     * @param {Object} context 消息上下文
     */
    async processMediaFiles(bot, allFiles, allImages, allVideos, allRecords, result, context) {
        // 处理文件
        for (const file of allFiles) {
            try {
                const fileId = file.file_id || file.file;
                if (fileId) {
                    const fileInfo = await this.napCatAPI.file.getInfo(bot, fileId);
                    if (fileInfo.retcode === 0 && fileInfo.data) {
                        const fileName = fileInfo.data.file_name || file.name || '未知文件';
                        let localPath = fileInfo.data.url;

                        // 如果是网络地址，下载到本地
                        if (localPath && (localPath.startsWith('http://') || localPath.startsWith('https://'))) {
                            localPath = await this.downloadMediaFile(localPath, fileName, 'file', context);
                        }

                        result.mediaInfo.push(`文件![${fileName}](${localPath || '下载失败'})`);
                    }
                }
            } catch (error) {
                result.mediaInfo.push(`文件![获取失败](错误:${error.message})`);
            }
        }

        // 处理图片
        for (const image of allImages) {
            try {
                const fileName = image.file || '未知图片';
                let localPath = image.url;

                // 如果是网络地址，下载到本地
                if (localPath && (localPath.startsWith('http://') || localPath.startsWith('https://'))) {
                    localPath = await this.downloadMediaFile(localPath, fileName, 'image', context);
                }

                result.mediaInfo.push(`图片![${fileName}](${localPath || '下载失败'})`);
            } catch (error) {
                result.mediaInfo.push(`图片![处理失败](错误:${error.message})`);
            }
        }

        // 处理视频
        for (const video of allVideos) {
            try {
                const fileName = video.data?.file || '未知视频';
                let localPath = video.data?.url;

                // 如果是网络地址，下载到本地
                if (localPath && (localPath.startsWith('http://') || localPath.startsWith('https://'))) {
                    localPath = await this.downloadMediaFile(localPath, fileName, 'video', context);
                }

                result.mediaInfo.push(`视频![${fileName}](${localPath || '下载失败'})`);
            } catch (error) {
                result.mediaInfo.push(`视频![处理失败](错误:${error.message})`);
            }
        }

        // 处理语音
        for (const record of allRecords) {
            try {
                const fileName = record.data?.file || '未知语音';
                let localPath = record.data?.url;

                // 如果是网络地址，下载到本地
                if (localPath && (localPath.startsWith('http://') || localPath.startsWith('https://'))) {
                    localPath = await this.downloadMediaFile(localPath, fileName, 'record', context);
                }

                result.mediaInfo.push(`语音![${fileName}](${localPath || '下载失败'})`);
            } catch (error) {
                result.mediaInfo.push(`语音![处理失败](错误:${error.message})`);
            }
        }
    }

    /**
     * 下载媒体文件到本地
     * @param {string} url 文件URL
     * @param {string} originalName 原始文件名
     * @param {string} type 文件类型 (image/video/record/file)
     * @param {Object} context 消息上下文
     * @returns {string} 本地文件路径
     */
    async downloadMediaFile(url, originalName, type, context) {
        try {
            if (!this.recordConfig.enableMediaDownload) {
                return url; // 如果禁用下载，返回原URL
            }

            // 生成文件名和扩展名
            const { fileName, extension } = this.generateFileName(originalName, type, url);

            // 创建保存目录
            const saveDir = this.getMediaSaveDir(type, context);
            if (!fs.existsSync(saveDir)) {
                fs.mkdirSync(saveDir, { recursive: true });
            }

            const localPath = path.join(saveDir, `${fileName}${extension}`);

            // 如果文件已存在，直接返回路径
            if (fs.existsSync(localPath)) {
                return localPath;
            }

            // 下载文件
            await this.downloadFile(url, localPath);

            return localPath;

        } catch (error) {
            console.error(`[ERROR] 下载媒体文件失败:`, error);
            return url; // 下载失败时返回原URL
        }
    }

    /**
     * 生成文件名和扩展名
     * @param {string} originalName 原始文件名
     * @param {string} type 文件类型
     * @param {string} url 文件URL
     * @returns {Object} {fileName, extension}
     */
    generateFileName(originalName, type, url) {
        // 生成唯一文件名（基于时间戳和随机数）
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        const baseFileName = `${timestamp}_${random}`;

        let extension = '';

        // 根据类型设置默认扩展名
        switch (type) {
            case 'image':
                extension = '.webp';
                break;
            case 'video':
                extension = '.mp4';
                break;
            case 'record':
                extension = '.mp3';
                break;
            case 'file':
                // 尝试从原始文件名获取扩展名
                if (originalName && originalName.includes('.')) {
                    const parts = originalName.split('.');
                    extension = '.' + parts[parts.length - 1];
                } else {
                    // 从URL尝试获取扩展名
                    const urlParts = url.split('.');
                    if (urlParts.length > 1) {
                        const lastPart = urlParts[urlParts.length - 1].split('?')[0];
                        if (lastPart.length <= 5) {
                            extension = '.' + lastPart;
                        }
                    }
                    if (!extension) {
                        extension = '.bin'; // 默认二进制文件
                    }
                }
                break;
        }

        return { fileName: baseFileName, extension };
    }

    /**
     * 获取媒体文件保存目录
     * @param {string} type 文件类型
     * @param {Object} context 消息上下文
     * @returns {string} 保存目录路径
     */
    getMediaSaveDir(type, context) {
        const baseDir = path.join(__dirname, '..', 'media_files');
        const typeDir = path.join(baseDir, type);

        // 按聊天类型和ID分类
        const chatType = context.type; // group 或 private
        const chatId = context.groupId || context.userId;
        const today = new Date().toISOString().split('T')[0];

        return path.join(typeDir, chatType, chatId.toString(), today);
    }

    /**
     * 下载文件
     * @param {string} url 文件URL
     * @param {string} localPath 本地保存路径
     */
    async downloadFile(url, localPath) {
        return new Promise((resolve, reject) => {
            const protocol = url.startsWith('https:') ? https : http;

            const request = protocol.get(url, (response) => {
                if (response.statusCode === 200) {
                    const fileStream = fs.createWriteStream(localPath);
                    response.pipe(fileStream);

                    fileStream.on('finish', () => {
                        fileStream.close();
                        resolve();
                    });

                    fileStream.on('error', (error) => {
                        fs.unlink(localPath, () => {}); // 删除不完整的文件
                        reject(error);
                    });
                } else {
                    reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                }
            });

            request.on('error', (error) => {
                reject(error);
            });

            request.setTimeout(30000, () => {
                request.abort();
                reject(new Error('下载超时'));
            });
        });
    }

    /**
     * 格式化戳一戳事件记录
     * @param {Object} context 消息上下文
     * @returns {string} 格式化的戳一戳记录
     */
    async formatPokeLog(context) {
        const timestamp = helper.formatTimestamp(Math.floor(context.timestamp.getTime() / 1000));

        // 获取用户信息
        const operatorInfo = await this.getUserInfo(context.operatorId, context.groupId, context);
        const targetInfo = await this.getUserInfo(context.targetId, context.groupId, context);

        let logEntry = '';

        if (context.groupId) {
            // 群聊戳一戳
            const groupInfo = await this.getGroupInfo(context.groupId);
            logEntry = `[群聊:${context.groupId}(${groupInfo.name})][${timestamp}] 互动：${operatorInfo.name}(QQ号:${context.operatorId})戳了戳${targetInfo.name}(QQ号:${context.targetId})`;
        } else {
            // 私聊戳一戳
            const chatInfo = await this.getPrivateChatInfo(context.operatorId, context.targetId);
            logEntry = `[${chatInfo.description}][${timestamp}] 互动：${operatorInfo.name}(QQ号:${context.operatorId})戳了戳${targetInfo.name}(QQ号:${context.targetId})`;
        }

        return logEntry;
    }

    /**
     * 获取用户信息
     * @param {string} userId 用户ID
     * @param {string} groupId 群组ID（可选）
     * @returns {Object} 用户信息
     */
    async getUserInfo(userId, groupId = null, context = null) {
        try {
            // 优先使用context中的WebSocket连接，否则尝试获取第一个可用连接
            let ws = null;
            let selfId = null;

            if (context && context.ws && context.selfId) {
                ws = context.ws;
                selfId = context.selfId;
            } else {
                // 尝试获取第一个可用的NapCat连接
                const clients = this.adapter.napCatClients;
                if (clients && clients.size > 0) {
                    const firstClient = clients.values().next().value;
                    ws = firstClient;
                    // 从配置或其他地方获取selfId
                    selfId = this.adapter.config?.SELF_ID || '123456789';
                }
            }

            if (!ws) {
                // 静默处理，不输出错误日志，直接返回默认值
                return { name: `用户${userId}`, id: userId };
            }

            const bot = { ws: ws, selfId: selfId };

            if (groupId) {
                // 获取群成员信息
                const memberInfo = await this.napCatAPI.user.getGroupMemberInfo(bot, groupId, userId);
                if (memberInfo.retcode === 0 && memberInfo.data) {
                    return {
                        name: memberInfo.data.card || memberInfo.data.nickname || `用户${userId}`,
                        role: memberInfo.data.role || 'member',
                        id: userId
                    };
                }
            } else {
                // 获取用户信息
                const userInfo = await this.napCatAPI.user.getInfo(bot, userId);
                if (userInfo.retcode === 0 && userInfo.data) {
                    return {
                        name: userInfo.data.nickname || `用户${userId}`,
                        id: userId
                    };
                }
            }
        } catch (error) {
            console.error(`[ERROR] 获取用户信息失败:`, error);
        }

        return { name: `用户${userId}`, id: userId };
    }



    /**
     * 获取私聊信息
     * @param {string} operatorId 操作者ID
     * @param {string} targetId 目标ID
     * @returns {Object} 私聊信息
     */
    async getPrivateChatInfo(operatorId, targetId) {
        const operatorInfo = await this.getUserInfo(operatorId, null, null);
        const targetInfo = await this.getUserInfo(targetId, null, null);

        return {
            description: `${operatorInfo.name}和${targetInfo.name}的私聊`
        };
    }

    /**
     * 格式化聊天记录
     * @param {Object} context 消息上下文
     * @param {Object} processedContent 处理后的内容
     * @returns {string} 格式化的日志条目
     */
    async formatChatLog(context, processedContent) {
        const timestamp = helper.formatTimestamp(Math.floor(context.timestamp.getTime() / 1000));
        const userId = context.userId;
        const nickname = context.sender?.nickname || context.sender?.card || '未知用户';

        let logEntry = '';

        if (context.type === 'group') {
            const groupId = context.groupId;
            const role = helper.getGroupRole(context.sender?.role || 'member');

            // 获取群名称
            const groupInfo = await this.getGroupInfo(groupId);
            const groupName = groupInfo ? groupInfo.group_name : '未知群聊';
            logEntry = `[群聊:${groupId}(${groupName})][${timestamp}] ${nickname}(QQ号:${userId})[群身份: ${role}]`;
        } else if (context.type === 'private') {
            // 获取私聊对方信息
            const otherUserId = context.selfId.toString() === userId ?
                (context.targetId || '未知') : context.selfId.toString();
            const otherUserInfo = await this.getUserInfo(otherUserId, null, context);
            const currentUserInfo = await this.getUserInfo(userId, null, context);

            logEntry = `[${currentUserInfo.name}和${otherUserInfo.name}的私聊][${timestamp}] ${nickname}(QQ号:${userId})`;
        }

        // 添加内容
        const contentParts = [];

        // 添加文本内容（需要截断处理）
        if (processedContent.textContent) {
            const truncatedText = this.truncateMessage(processedContent.textContent);
            contentParts.push(truncatedText);
        }

        // 添加At信息（使用真实用户名）
        if (processedContent.atInfo.length > 0) {
            const atInfoWithNames = await this.processAtInfo(processedContent.atInfo, context.groupId, context);
            contentParts.push(`艾特了${atInfoWithNames.join('、')}`);
        }

        // 添加表情信息
        if (processedContent.faceInfo.length > 0) {
            contentParts.push(`发送了${processedContent.faceInfo.join('、')}`);
        }

        // 添加媒体文件信息（不截断文件路径）
        if (processedContent.mediaInfo.length > 0) {
            contentParts.push(`发送了${processedContent.mediaInfo.join('、')}`);
        }

        // 根据是否有文本内容决定格式
        if (processedContent.textContent && contentParts.length > 1) {
            // 有文本内容且有其他内容
            logEntry += `说: ${contentParts[0]}，并且${contentParts.slice(1).join('，')}`;
        } else if (processedContent.textContent) {
            // 只有文本内容
            logEntry += `说: ${contentParts[0]}`;
        } else if (contentParts.length > 0) {
            // 没有文本内容但有其他内容
            logEntry += ` ${contentParts.join('，')}`;
        } else {
            // 空消息
            logEntry += ' [空消息]';
        }

        return logEntry;
    }

    /**
     * 截断消息内容，保持markdown文件路径完整
     * @param {string} text 原始文本
     * @param {number} maxLength 最大长度（默认240）
     * @returns {string} 截断后的文本
     */
    truncateMessage(text, maxLength = 240) {
        if (text.length <= maxLength) {
            return text;
        }

        // 查找所有markdown格式的文件路径 ![filename](path)
        const markdownPattern = /!\[([^\]]*)\]\(([^)]*)\)/g;
        const markdownMatches = [];
        let match;

        while ((match = markdownPattern.exec(text)) !== null) {
            markdownMatches.push({
                start: match.index,
                end: match.index + match[0].length,
                content: match[0]
            });
        }

        // 如果没有markdown内容，直接截断
        if (markdownMatches.length === 0) {
            return text.substring(0, maxLength) + '...';
        }

        // 检查截断位置是否会破坏markdown路径
        let truncatePos = maxLength;

        for (const mdMatch of markdownMatches) {
            if (truncatePos > mdMatch.start && truncatePos < mdMatch.end) {
                // 截断位置在markdown内容中，调整到markdown开始位置
                truncatePos = mdMatch.start;
                break;
            }
        }

        if (truncatePos < text.length) {
            return text.substring(0, truncatePos) + '...';
        }

        return text;
    }



    /**
     * 保存到文件
     * @param {Object} context 消息上下文
     * @param {string} logEntry 日志条目
     */
    async saveToFile(context, logEntry) {
        try {
            const logPath = this.getChatLogPath(context.type, context.groupId || context.userId);

            // 确保目录存在
            const logDir = path.dirname(logPath);
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
            }

            // 追加写入文件
            fs.appendFileSync(logPath, logEntry + '\n', 'utf8');

        } catch (error) {
            this.logger.error('ChatRecord', `文件写入失败: ${error.message}`);
        }
    }

    /**
     * 获取聊天记录文件路径
     * @param {string} type 消息类型 (group/private)
     * @param {string} id 群号或用户ID
     * @returns {string} 文件路径
     */
    getChatLogPath(type, id) {
        const baseDir = path.join(__dirname, '..', 'chat_logs');
        const typeDir = path.join(baseDir, type);
        const idDir = path.join(typeDir, id.toString());
        const today = new Date().toISOString().split('T')[0];
        const dateDir = path.join(idDir, today);
        const fileName = `${today}.log`;

        // 确保目录存在
        if (!fs.existsSync(dateDir)) {
            fs.mkdirSync(dateDir, { recursive: true });
        }

        return path.join(dateDir, fileName);
    }

    /**
     * 读取当天聊天记录
     * @param {string} chatType 聊天类型 (group/private)
     * @param {string} chatId 群号或用户ID
     * @param {number} number 要获取的最近消息条数
     * @param {string} currentMessage 当前用户发言内容
     * @returns {Object} 聊天记录结果
     */
    async readTodayChatHistory(chatType, chatId, number = 10, currentMessage = '') {
        try {
            const logPath = this.getChatLogPath(chatType, chatId);

            // 检查文件是否存在
            if (!fs.existsSync(logPath)) {
                return {
                    success: false,
                    message: '今日暂无聊天记录',
                    history: [],
                    currentMessage: currentMessage
                };
            }

            // 读取文件内容
            const fileContent = fs.readFileSync(logPath, 'utf8');
            const lines = fileContent.trim().split('\n').filter(line => line.trim());

            // 获取最近的number条记录
            const recentLines = lines.slice(-number);

            return {
                success: true,
                message: `成功获取最近${recentLines.length}条聊天记录`,
                history: recentLines,
                currentMessage: currentMessage,
                totalCount: lines.length
            };

        } catch (error) {
            console.error(`[ERROR] 读取聊天记录失败:`, error);
            return {
                success: false,
                message: `读取聊天记录失败: ${error.message}`,
                history: [],
                currentMessage: currentMessage
            };
        }
    }

    /**
     * 格式化聊天历史记录输出
     * @param {Object} historyResult 历史记录结果
     * @returns {string} 格式化的输出
     */
    formatChatHistoryOutput(historyResult) {
        let output = '';

        if (!historyResult.success) {
            output += `❌ ${historyResult.message}\n`;
        } else {
            output += `📋 聊天记录查询结果\n`;
            output += `📊 今日总计: ${historyResult.totalCount}条消息\n`;
            output += `📝 最近记录: ${historyResult.history.length}条\n\n`;

            if (historyResult.history.length > 0) {
                output += `=== 历史记录 ===\n`;
                historyResult.history.forEach((line, index) => {
                    output += `${index + 1}. ${line}\n`;
                });
            }
        }

        if (historyResult.currentMessage) {
            output += `\n=== 最新消息 ===\n`;
            output += `🆕 ${historyResult.currentMessage}\n`;
        }

        return output;
    }

    /**
     * HTML解码
     * @param {string} str 需要解码的字符串
     * @returns {string} 解码后的字符串
     */
    htmlDecode(str) {
        return str.replace(/&#(\d+);/g, (match, dec) => String.fromCharCode(dec))
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/&amp;/g, '&')
                  .replace(/&quot;/g, '"')
                  .replace(/&#x([0-9A-Fa-f]+);/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)));
    }

    /**
     * 处理管理命令
     * @param {Object} context 消息上下文
     * @returns {Object} 处理结果
     */
    async handleManagementCommand(context) {
        const message = this.htmlDecode(context.message || '');

        // 聊天记录管理命令
        if (message === '聊天记录状态' || message === 'chat_log_status') {
            return await this.handleStatusCommand(context);
        }

        if (message.startsWith('聊天记录配置') || message.startsWith('chat_log_config')) {
            return await this.handleConfigCommand(context, message);
        }

        // 查看聊天记录命令
        if (message.startsWith('查看聊天记录') || message.startsWith('chat_history')) {
            return await this.handleHistoryCommand(context, message);
        }

        return { handled: false };
    }

    /**
     * 处理状态查询命令
     * @param {Object} context 消息上下文
     * @returns {Object} 处理结果
     */
    async handleStatusCommand(context) {
        try {
            const status = `聊天记录插件状态:

配置信息:
• 群聊记录: ${this.recordConfig.enableGroupRecord ? '启用' : '禁用'}
• 私聊记录: ${this.recordConfig.enablePrivateRecord ? '启用' : '禁用'}
• 媒体下载: ${this.recordConfig.enableMediaDownload ? '启用' : '禁用'}
• 排除群聊: ${this.recordConfig.excludeGroups.length}个
• 排除用户: ${this.recordConfig.excludeUsers.length}个

存储路径:
• 群聊记录: OneBot11/chat_logs/group/
• 私聊记录: OneBot11/chat_logs/private/
• 媒体文件: OneBot11/media_files/

记录格式:
[群聊:123456][04-07 12:26:49] 昵称(QQ号:123456666)[群身份: 管理员]说: 消息内容
[私聊][04-07 12:26:49] 昵称(QQ号:123456666)说: 消息内容

💡 可用命令:
• 聊天记录状态 - 查看插件状态
• 聊天记录配置 - 查看配置信息
• 查看聊天记录 [数量] - 查看最近的聊天记录 (默认10条，最多50条)`;

            await this.reply(context, status);
            return { handled: true, message: '状态查询完成' };

        } catch (error) {
            this.logger.error('ChatRecord', `状态查询失败: ${error.message}`);
            await this.reply(context, `状态查询失败: ${error.message}`);
            return { handled: true, message: '状态查询失败' };
        }
    }

    /**
     * 处理历史记录命令
     * @param {Object} context 消息上下文
     * @param {string} message 命令消息
     * @returns {Object} 处理结果
     */
    async handleHistoryCommand(context, message) {
        try {
            // 解析命令参数
            let number = 10; // 默认10条
            const match = message.match(/(\d+)/);
            if (match) {
                number = Math.min(parseInt(match[1]), 50); // 最多50条
            }

            // 获取当前用户的发言内容（处理后的完整内容）
            const processedContent = await this.processMessageSegments(context);
            const currentUserMessage = await this.formatChatLog(context, processedContent);

            // 读取聊天历史
            const historyResult = await this.readTodayChatHistory(
                context.type,
                context.groupId || context.userId,
                number,
                currentUserMessage
            );

            // 格式化输出
            const output = this.formatChatHistoryOutput(historyResult);

            await this.reply(context, output);

            return {
                handled: true,
                message: `聊天记录查询完成，返回${historyResult.history.length}条记录`
            };

        } catch (error) {
            console.error(`[ERROR] 处理历史记录命令失败:`, error);
            await this.reply(context, `❌ 查询聊天记录失败: ${error.message}`);

            return {
                handled: true,
                message: '聊天记录查询失败'
            };
        }
    }

    /**
     * 处理配置命令
     * @param {Object} context 消息上下文
     * @param {string} message 消息内容
     * @returns {Object} 处理结果
     */
    async handleConfigCommand(context, message) {
        try {
            // 简单的配置命令解析
            if (message.includes('群聊记录 关闭')) {
                this.recordConfig.enableGroupRecord = false;
                await this.sendReply(context, '群聊记录已关闭');
            } else if (message.includes('群聊记录 开启')) {
                this.recordConfig.enableGroupRecord = true;
                await this.sendReply(context, '群聊记录已开启');
            } else if (message.includes('私聊记录 关闭')) {
                this.recordConfig.enablePrivateRecord = false;
                await this.sendReply(context, '私聊记录已关闭');
            } else if (message.includes('私聊记录 开启')) {
                this.recordConfig.enablePrivateRecord = true;
                await this.sendReply(context, '私聊记录已开启');
            } else {
                await this.sendReply(context, `配置命令格式:
• 聊天记录配置 群聊记录 开启/关闭
• 聊天记录配置 私聊记录 开启/关闭
• 聊天记录状态 - 查看当前状态`);
            }

            return { handled: true, message: '配置命令处理完成' };

        } catch (error) {
            this.logger.error('ChatRecord', `配置命令处理失败: ${error.message}`);
            await this.sendReply(context, `配置失败: ${error.message}`);
            return { handled: true, message: '配置命令处理失败' };
        }
    }

    /**
     * 发送回复消息
     * @param {Object} context 消息上下文
     * @param {string} message 回复内容
     */
    async sendReply(context, message) {
        try {
            if (context.type === 'private') {
                await this.protocol.sendPrivateMessage(context.userId, message);
            } else if (context.type === 'group') {
                await this.protocol.sendGroupMessage(context.groupId, message);
            }
        } catch (error) {
            this.logger.error('ChatRecord', `发送回复失败: ${error.message}`);
        }
    }

    /**
     * 获取插件信息
     * @returns {Object} 插件信息
     */
    getInfo() {
        return {
            name: this.name,
            description: this.description,
            version: '1.0.0',
            author: 'VCPToolBox',
            priority: this.priority,
            features: [
                '自动记录群聊消息',
                '自动记录私聊消息', 
                '支持媒体文件路径记录',
                '按日期分文件存储',
                '支持配置管理',
                '格式化消息记录'
            ],
            commands: [
                '聊天记录状态 - 查看插件状态',
                '聊天记录配置 - 配置插件选项'
            ]
        };
    }
}

module.exports = ChatRecordPlugin;
