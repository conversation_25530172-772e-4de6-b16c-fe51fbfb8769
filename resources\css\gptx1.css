@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap');
@import url('fontawesome-free-6.6.0-web/css/all.min.css');

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: "RuanMengKeAiShouXieZi", sans-serif;
  font-weight: 700;
  background-color: #f5f5f5;
  color: #000000;
  line-height: 1.6;
  font-size: 24px;
}

/* Markdown内容容器 */
.markdown-content {
  font-family: "RuanMengKeAiShouXieZi", sans-serif;
  font-weight: 400;
  font-size: 22px;
  padding: 5px;
  border-radius: 8px;
  color: #000000;
  overflow-wrap: break-word;
}

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-family: 'jty', sans-serif;
  color: #2c3e50;
  margin-top: 24px;
  margin-bottom: 16px;
  line-height: 1.3;
  text-shadow: none;
}

.markdown-content h1 {
  font-size: 32px;
}

.markdown-content h2 {
  font-size: 28px;
}

.markdown-content h3 {
  font-size: 25px;
}

.markdown-content h4 {
  font-size: 22px;
}

.markdown-content h5 {
  font-size: 20px;
}

.markdown-content h6 {
  font-size: 18px;
}

/* 段落样式 */
.markdown-content p {
  font-family: "RuanMengKeAiShouXieZi", sans-serif;
  color: #000000;
  font-size: 20px;
  text-indent: 2em;
  margin-bottom: 16px;
  transition: color 0.3s ease;
  white-space: pre-wrap;
  font-weight: 500;
}

.markdown-content p:hover {
  color: #2c3e50;
}

.markdown-content a {
  color: #00c3ff;
  font-family: "JetBrains Mono", "Fira Code", "Inconsolata", monospace;
  text-decoration: none;
  position: relative;
  padding: 2px 4px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(90deg, rgba(0, 195, 255, 0.1) 0%, rgba(0, 195, 255, 0) 100%);
  border-radius: 3px;
}

.markdown-content a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, #00c3ff 0%, #00ffd5 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.markdown-content a:hover {
  color: #00ffd5;
  text-shadow: 0 0 8px rgba(0, 195, 255, 0.4);
  background: linear-gradient(90deg, rgba(0, 195, 255, 0.15) 0%, rgba(0, 255, 213, 0.05) 100%);
}

.markdown-content a:hover::before {
  transform: scaleX(1);
}

.markdown-content a:active {
  transform: translateY(1px);
}

/* 添加小图标 */
.markdown-content a::after {
  content: '↗';
  font-size: 0.85em;
  margin-left: 3px;
  opacity: 0;
  transform: translateX(-5px);
  transition: all 0.2s ease;
}

.markdown-content a:hover::after {
  opacity: 1;
  transform: translateX(0);
}

/* 列表样式 */
.markdown-content ol,
.markdown-content ul {
  padding-left: 40px;
  margin-bottom: 16px;
}

.markdown-content ol li,
.markdown-content ul li {
  margin-bottom: 8px;
}

/* 内联代码样式 */
.markdown-content code {
  font-size: 20px;
  background-color: #ecf0f1;
  padding: 2px 4px;
  border-radius: 4px;
  color: #e74c3c;
  font-family: "SourceCodePro-Italic-VariableFont_wght", "Inconsolata", sans-serif;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 代码块样式 */
.markdown-content pre {
  padding-left: 40px; /* 调整以适应行号宽度 */
  position: relative;
  /* 相对定位以便伪元素定位 */
  background-color: #202020;
  /* 更深的背景色，模仿终端 */
  color: #d4d4d4;
  padding: 40px 20px 20px 20px;
  /* 增加顶部内边距以容纳终端圆点 */
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
  font-size: 20px;
  font-family: "SourceCodePro-Italic-VariableFont_wght", "Inconsolata", sans-serif;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
  text-indent: 0;
  text-shadow: none;
  border: 1px solid #444444;
  /* 边框颜色调整 */
}

/* 代码块顶部语言标签 */
.markdown-content pre[data-lang]::after {
  content: attr(data-lang);
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #000000;
  color: #ffffff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 1em;
  line-height: 1.5;
  text-transform: uppercase;
  pointer-events: none;
  height: auto;
  max-height: 24px;
  /* 限制最大高度 */
  display: inline-block;
  /* 使元素的宽度自适应内容 */
  white-space: nowrap;
  /* 防止文本换行 */
}

.markdown-content pre[data-lang]:not([data-lang=""])::before {
  display: block;
}

/* 代码块顶部的红绿黄圆点 */
.markdown-content pre::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 20px;
  width: 17px;
  height: 17px;
  border-radius: 50%;
  background-color: #ff5f56;
  /* 红色圆点 */
  box-shadow: 30px 0 0 #ffbd2e, 60px 0 0 #27c93f;
  /* 黄、绿色圆点 */
}

/* 高亮样式 */
.markdown-content .hljs {
  background-color: transparent;
  color: #d4d4d4;
  padding: 0;
  border-radius: 0;
  font-size: 20px;
  font-family: "SourceCodePro-Italic-VariableFont_wght", "Inconsolata", sans-serif;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
  text-indent: 0;
  text-shadow: none;
}

.markdown-content .hljs-comment,
.markdown-content .hljs-quote {
  color: #6a9955;
  font-style: italic;
}

.markdown-content .hljs-keyword,
.markdown-content .hljs-selector-tag,
.markdown-content .hljs-subst {
  color: #569cd6;
  font-weight: bold;
}

.markdown-content .hljs-literal,
.markdown-content .hljs-number,
.markdown-content .hljs-tag .hljs-attr {
  color: #b5cea8;
}

.markdown-content .hljs-string,
.markdown-content .hljs-doctag {
  color: #ce9178;
}

.markdown-content .hljs-title,
.markdown-content .hljs-section,
.markdown-content .hljs-selector-id {
  color: #dcdcaa;
  font-weight: bold;
}

.markdown-content .hljs-type,
.markdown-content .hljs-class .hljs-title {
  color: #4ec9b0;
}

.markdown-content .hljs-tag,
.markdown-content .hljs-name,
.markdown-content .hljs-attribute {
  color: #9cdcfe;
}

.markdown-content .hljs-regexp,
.markdown-content .hljs-link {
  color: #d16969;
}

.markdown-content .hljs-symbol,
.markdown-content .hljs-bullet {
  color: #d7ba7d;
}

.markdown-content .hljs-built_in,
.markdown-content .hljs-builtin-name {
  color: #569cd6;
}

.markdown-content .hljs-meta {
  color: #c586c0;
  font-weight: bold;
}

.markdown-content .hljs-deletion {
  background: #f44747;
  color: #ffffff;
  text-decoration: none;
}

.markdown-content .hljs-addition {
  background: #4ec9b0;
  color: #ffffff;
  text-decoration: none;
}

.markdown-content .hljs-emphasis {
  font-style: italic;
}

.markdown-content .hljs-strong {
  font-weight: bold;
}

/* 为所有 code 块提供基本样式 */
.markdown-content pre code {
  display: block;
  padding: 20px;
  background-color: #202020;
  color: #d4d4d4;
  border-radius: 8px;
  overflow-x: auto;
  font-family: "SourceCodePro-Italic-VariableFont_wght", "Inconsolata", sans-serif;
  font-size: 20px;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 如果没有特定的语言类，应用默认样式 */
.markdown-content pre:not([data-lang]) {
  background-color: #2d2d2d;
  color: #d4d4d4;
}

/* 特定语言的代码块样式 */
.markdown-content pre code.html,
.markdown-content pre code.python,
.markdown-content pre code.css,
.markdown-content pre code.javascript,
.markdown-content pre code.java,
.markdown-content pre code.php,
.markdown-content pre code.ruby,
.markdown-content pre code.swift,
.markdown-content pre code.csharp,
.markdown-content pre code.go,
.markdown-content pre code.vue {
  color: inherit;
  background: none;
}

/* 滚动条样式 */
.markdown-content pre::-webkit-scrollbar {
  width: 8px;
  background-color: #2d2d2d;
  /* 滚动条背景色与终端背景一致 */
}

.markdown-content pre::-webkit-scrollbar-thumb {
  background-color: #3c3c3c;
  border-radius: 4px;
}

.markdown-content pre::-webkit-scrollbar-thumb:hover {
  background-color: #555555;
}

.markdown-content pre::-webkit-scrollbar-track {
  background-color: #2d2d2d;
}

/* 引用块样式 */
.markdown-content blockquote {
  border-left: 4px solid #2980b9;
  padding-left: 12px;
  margin-left: 0;
  font-style: italic;
  color: #7f8c8d;
  background-color: #ecf0f1;
  border-radius: 4px;
}

/* 图片样式 */
.markdown-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em auto;
}

/* 用户头像样式 */
.user img,
.user2 img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 20px auto;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

/* 容器样式 */
.container {
  background-image: url('樱花.png');
  background-size: contain;
  max-width: 1000px;
  margin: 20px auto;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: space-evenly;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
}

/* 用户消息气泡样式 */
.user,
.user2 {
  background-color: rgba(255, 255, 255, 0.75);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06);
  margin-bottom: 40px;
  position: relative;
  overflow: visible;
  border: 1px solid #e0e0e0;
}

.user2 {
  align-items: flex-end;
  background-color: rgba(240, 248, 255, 0.5);
  border: 1px solid #b0c4de;
  background-image: linear-gradient(135deg, #f0f8ff 25%, #e6f2ff 25%, #e6f2ff 50%, #f0f8ff 50%, #f0f8ff 75%, #e6f2ff 75%, #e6f2ff);
  background-size: 20px 20px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: border 0.3s ease, box-shadow 0.3s ease;
}

.user2:hover {
  border: 1px solid #87cefa;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.1), inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 内容区域样式 */
.content {
  position: relative;
  z-index: 1;
  width: 100%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  font-family: "Inconsolata", 'Chinese';
  font-size: 18px;
  color: #333;
}

/* 气泡顶部和底部装饰 */
.user::before,
.user2::before,
.user::after,
.user2::after {
  content: '';
  position: absolute;
  height: 20px;
}

.user::before,
.user2::before {
  top: -10px;
  left: 0;
  right: 0;
  background-image: linear-gradient(45deg, transparent 25%, #ffffff 25%, #ffffff 50%, transparent 50%, transparent 75%, #ffffff 75%, #ffffff);
  background-size: 40px 40px;
  background-position: 0 -10px;
}

.user::after,
.user2::after {
  bottom: -10px;
  left: 0;
  right: 0;
  background-image: linear-gradient(45deg, transparent 25%, #ffffff 25%, #ffffff 50%, transparent 50%, transparent 75%, #ffffff 75%, #ffffff);
  background-size: 40px 40px;
}

/* 用户2特有的顶部和底部装饰 */
.user2::before,
.user2::after {
  background-image:
    linear-gradient(45deg, transparent 25%, #f0f8ff 25%, #f0f8ff 50%, transparent 50%, transparent 75%, #f0f8ff 75%, #f0f8ff),
    radial-gradient(circle at 10px 10px, rgba(255, 255, 255, 0.3) 0%, transparent 80%);
  background-size: 40px 40px, 40px 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {

  .user,
  .user2 {
    padding: 15px;
    margin-bottom: 30px;
  }

  .content {
    font-size: 14px;
  }
}

@media (max-width: 480px) {

  .user,
  .user2 {
    padding: 10px;
    margin-bottom: 20px;
  }

  .content {
    font-size: 13px;
  }
}

/* 用户头像样式调整 */
.user2 #title img,
.user #title img {
  width: 75px;
  height: 75px;
  object-fit: cover;
  border-radius: 50%;
  margin-right: 15px;
  box-shadow: 0 2px 8px rgb(89, 124, 124);
}

.user #title img {
  box-shadow: 0 2px 8px rgb(87, 85, 85);
}

/* 用户名样式 */
.user .name,
.user2 .name {
  font-size: 26px;
  padding-bottom: 10px;
  font-family: "RuanMengKeAiShouXieZi", sans-serif;
}

.user .name {
  color: #ff8fae;
}

.user2 .name {
  text-align: right;
  color: #60fcf4;
}

/* 消息气泡样式 */
.user .message,
.user2 .message {
  position: relative;
  padding: 12px 15px;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  line-height: 1.6;
  margin-top: 10px;
  max-width: 100%;
  word-wrap: break-word;
}

.user .message {
  background-color: #ecfffe;
}

.user2 .message {
  background-color: #fedde6;
}

/* 消息气泡三角形指示 */
.user .message::after,
.user2 .message::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 10px solid transparent;
}

.user .message::after {
  left: -18px;
  top: 10px;
  border-right-color: #ecfffe;
}

.user2 .message::after {
  right: -18px;
  top: 10px;
  border-left-color: #fedde6;
}

.user .message p,
.user2 .message p {
  font-size: 24px;
  margin: 0;
}

/* 头部样式 */
.header {
  font-family: "NZBZ", sans-serif;
  text-align: center;
  margin: 15px auto;
  padding: 15px;
  width: 90%;
  max-width: 800px;
  position: relative;
  background: #f0f8ff;
  border: 3px solid #daeeff;
  border-radius: 50% 20% 50% 20% / 20% 50% 20% 50%;
}

.header::before,
.header::after {
  content: "";
  position: absolute;
  background: #f0f8ff;
  border: 3px solid #dbefff;
  border-radius: 50%;
}

.header::before {
  top: -15px;
  left: 20%;
  width: 100px;
  height: 50px;
}

.header::after {
  bottom: -15px;
  right: 20%;
  width: 80px;
  height: 40px;
}

.header h1 {
  font-family: 'Chinese', sans-serif;
  font-weight: 600;
  font-size: 28px;
  text-transform: uppercase;
  letter-spacing: 3px;
  color: #2c3e50;
  margin: 0;
  text-shadow: 2px 2px 0 rgba(194, 228, 255, 0.758);
  position: relative;
  z-index: 1;
  transform: translateX(25px);
}

/* 头部下方的渐变条 */
.header-bar {
  height: 6px;
  background: linear-gradient(135deg, #4682b4, #87ceeb, #4682b4);
  background-size: 200% 200%;
  animation: gradient 8s ease infinite;
  border-radius: 3px;
  margin: 15px auto;
  width: 70%;
  position: relative;
  z-index: 1;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* 像素化边角样式 */
.pixel-corners {
  clip-path:
    polygon(0 10px, 10px 10px, 10px 0,
      calc(100% - 10px) 0, calc(100% - 10px) 10px, 100% 10px,
      100% calc(100% - 10px), calc(100% - 10px) calc(100% - 10px), calc(100% - 10px) 100%,
      10px 100%, 10px calc(100% - 10px), 0 calc(100% - 10px));
}

.katex {
  font-family: "KaTeX_Caligraphic", "KaTeX_Main", "KaTeX_AMS", Arial !important;
  font-size: 1em !important;
  white-space: normal;
  display: inline-block !important;
  max-width: 100%;
  min-width: 0;
}

/* 字体引入 */
@font-face {
  font-family: "jty";
  src: url("jty.OTF");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Inconsolata";
  src: url("Inconsolata-VariableFont_wdth,wght.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "NZBZ";
  src: url("NZBZ.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "RuanMengKeAiShouXieZi";
  src: url("TengXiangJiaLiDaYuanJian-1.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "TengXiangJiaLiZhongYuanJian";
  src: url("SanJiYuanTiJian-Zhong-2.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Chinese";
  src: url("ZCOOLQingKeHuangYou-Regular.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "SourceCodePro-Italic-VariableFont_wght";
  src: url("SourceCodePro-Italic-VariableFont_wght.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "KaTeX_Main";
  src: url("KaTeX_Main-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "KaTeX_Caligraphic";
  src: url("KaTeX_Caligraphic-Bold.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

/* 新增部分 */

/* 对话形式的文本，使用 <q> 标签，应用古朴咖啡色风格 */
.markdown-content q {
  font-family: "RuanMengKeAiShouXieZi", sans-serif;
  font-size: 20px;
  color: #6f4e37;
  /* 古朴咖啡色 */
  background-color: #fdf6e3;
  padding: 2px 4px;
  border-radius: 4px;
  border: 1px solid #e0c68a;
  position: relative;
  /* 添加引号样式 */
}

.markdown-content q::before {
  content: '“';
  font-size: 24px;
  color: #6f4e37;
  position: absolute;
  left: -10px;
  top: -5px;
}

.markdown-content q::after {
  content: '”';
  font-size: 24px;
  color: #6f4e37;
  position: absolute;
  right: -10px;
  bottom: -5px;
}

/* 引用块样式增强，适用于对话框等特殊引用 */
.markdown-content blockquote {
  border-left: 4px solid #8b4513;
  /* 棕色边框 */
  background-color: #f5f5dc;
  /* 米色背景 */
  color: #333333;
  padding: 10px 20px;
  border-radius: 8px;
  font-style: normal;
  position: relative;
}

/* 引用块顶部添加装饰性图标 */
.markdown-content blockquote::before {
  font-size: 24px;
  position: absolute;
  top: 10px;
  left: 10px;
  /* 这里可以添加装饰性内容，如图标 */
}

/* 强调文本样式，适用于特殊标注 */
.markdown-content .highlight {
  background-color: #fffacd;
  /* 柠檬绸色背景 */
  color: #ff4500;
  /* 橙红色文字 */
  padding: 2px 4px;
  border-radius: 4px;
}

/* 特定符号包裹的文本样式 */
/* 使用自定义的 span 标签包裹不同符号的文本 */
/* 例如，使用双星号 **text** 表示重点文本 */
.markdown-content .double-star {
  color: #8b4513;
  /* 棕色 */
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: bold;
}

/* 使用单下划线 _text_ 表示强调文本 */
.markdown-content .single-underline {
  text-decoration: underline;
  color: #2e8b57;
  /* 海洋绿色 */
}

/* 使用尖括号 <text> 表示提示文本 */
.markdown-content .angle-bracket {
  background-color: #e6e6fa;
  /* 薰衣草色 */
  color: #4b0082;
  /* 靛青色 */
  padding: 2px 4px;
  border-radius: 4px;
}

/* 图片文本样式 */
.markdown-content .image-text {
  text-align: center;
  font-style: italic;
  color: #696969;
  margin-top: 10px;
}

/* 表格样式优化 */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 16px;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

.markdown-content th {
  background-color: #f2f2f2;
}

/* 响应式图片 */
.markdown-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em auto;
}

/* 图标样式 */
.model-container i {
  font-size: 2rem;
  /* 图标大小适中 */
  color: #a892a8;
  /* 浅灰紫色，更柔和 */
  margin-right: 0.5rem;
  /* 略微减少间距 */
  vertical-align: middle;
  /* 垂直居中图标 */
}

/* 标题样式 */
.model-container h1 {
  font-size: 1.5rem;
  /* 略微减小字体 */
  color: #555;
  /* 深灰色，更接近信笺墨色 */
  margin: 0;
  /* 清除默认外边距 */
  font-weight: normal;
  /* 正常字体粗细，更显轻盈 */
  line-height: 1.4;
  /* 增加行高，更舒适 */
}

/* 模型名称的样式 */
.model-container span {
  color: #e91e63;
  /* 浪漫的粉色 */
  font-weight: bold;
  /* 加粗，更突出 */
  font-style: normal;
  /* 取消斜体，更正式 */
  border-bottom: 1px dotted #e91e63;
  /* 粉色虚线下划线，增加可爱感 */
}

/* 整体容器样式 */
.model-container {
  padding: 1rem;
  /* 增加内边距 */
  border: 1px solid #ddd;
  /* 浅灰色边框，模拟信笺 */
  border-radius: 5px;
  /* 圆角边框，更柔和 */
  background-color: #f9f9f9;
  /* 浅灰色背景，增加层次感 */
  font-family: sans-serif;
  /* 使用通用字体 */
}