/* 引入字体和图标 */
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;700&display=swap');
@import url('fontawesome-free-6.6.0-web/css/all.min.css');

/* 自定义字体 */
@font-face {
  font-family: "NZBZ";
  src: url("NZBZ.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "jty";
  src: url("jty.OTF") format("opentype");
  font-weight: normal;
  font-style: normal;
}

/* CSS变量定义 */
:root {
  --primary-color: #00bfff;
  --secondary-color: #1e90ff;
  --accent-color: #ffd700;
  --tech-purple: #8a2be2;
  --tech-cyan: #00ffff;
  --background-image: url('background.jpg'); /* 本地背景图片路径 */
  --background-overlay: rgba(0, 0, 0, 0.6);
  --card-bg: rgba(15, 15, 15, 0.95);
  --section-bg: rgba(30, 30, 30, 0.85);
  --text-color: #e0e0e0;
  --label-color: #00ffff;
  --value-color: #ff69b4;
  --description-color: #add8e6;
  --shadow-color: rgba(0, 0, 0, 0.9);
  --border-color-primary: #00bfff;
  --border-color-secondary: #1e90ff;
  --border-color-accent: #ffd700;
  --parallelogram-angle: 10deg;
  --card-width: 100%;
  --avatar-size: 200px;
  --cube-size: 60px;
}

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-size: 2rem;
  font-family: 'NZBZ', 'Roboto Mono', monospace;
  background: var(--background-image) no-repeat center center fixed;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: var(--text-color);
  padding: 20px;
  overflow: hidden;
  position: relative;
}

/* 幕布效果 */
.curtain-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(10px);
  background: var(--background-overlay);
  pointer-events: none;
  z-index: 1;
  animation: curtainFade 15s infinite alternate;
}

@keyframes curtainFade {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 0.8;
  }
}

/* 卡片样式 */
.profile-card {
  background: var(--card-bg);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  box-shadow: 0 40px 80px var(--shadow-color);
  width: 100%;
  max-width: var(--card-width);
  padding: 40px;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  z-index: 2;
  display: flex;
  flex-direction: column;
  color: var(--text-color);
}

/* 卡片悬停效果 */
.profile-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 50px 100px rgba(0, 0, 0, 0.95);
}

/* 动态背景元素 */
.taiji-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: radial-gradient(circle at bottom left, rgba(0, 191, 255, 0.5) 25%, transparent 25%),
    radial-gradient(circle at bottom right, rgba(0, 191, 255, 0.5) 25%, transparent 25%);
  background-size: 250px 250px;
  opacity: 0.6;
  pointer-events: none;
}

.dynamic-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
  opacity: 0.05;
  z-index: -3;
}

/* 头像容器 */
.avatar-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头像样式 */
.avatar {
  width: var(--avatar-size);
  height: var(--avatar-size);
  border-radius: 15px;
  overflow: hidden;
  border: 5px solid var(--primary-color);
  transition: border 0.3s ease, transform 0.3s ease;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar:hover {
  border-color: var(--secondary-color);
  transform: scale(1.05);
}

/* 复杂魔方集群样式 */
.complex-cube {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  perspective: 800px;
}

.complex-cube .cube {
  width: var(--cube-size);
  height: var(--cube-size);
  position: relative;
  transform-style: preserve-3d;
  animation: rotateCube 5s infinite linear;
}

.complex-cube .cube .face {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 204, 255, 0.6);
  border: 2px solid var(--tech-cyan);
  backface-visibility: hidden;
}

.complex-cube .cube .front {
  transform: rotateY(0deg) translateZ(calc(var(--cube-size) / 2));
}

.complex-cube .cube .back {
  transform: rotateY(180deg) translateZ(calc(var(--cube-size) / 2));
}

.complex-cube .cube .right {
  transform: rotateY(90deg) translateZ(calc(var(--cube-size) / 2));
}

.complex-cube .cube .left {
  transform: rotateY(-90deg) translateZ(calc(var(--cube-size) / 2));
}

.complex-cube .cube .top {
  transform: rotateX(90deg) translateZ(calc(var(--cube-size) / 2));
}

.complex-cube .cube .bottom {
  transform: rotateX(-90deg) translateZ(calc(var(--cube-size) / 2));
}

@keyframes rotateCube {
  from {
    transform: rotateX(0deg) rotateY(0deg);
  }
  to {
    transform: rotateX(360deg) rotateY(360deg);
  }
}

/* 配置区域 */
.configurations {
  margin-left: 240px; /* 留出头像的空间 */
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  overflow-y: auto;
}

/* 头部样式 */
.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.intro {
  display: flex;
  flex-direction: column;
}

.name {
  font-size: 3rem;
  color: var(--primary-color);
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 10px;
  font-family: 'jty', sans-serif;
}

.role {
  font-size: 1.8rem;
  color: #7fffd4;
  margin-bottom: 5px;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.status {
  font-size: 1.4rem;
  color: #32cd32;
  display: flex;
  align-items: center;
}

.status i {
  margin-right: 8px;
  color: #32cd32;
}

/* 主体内容样式 */
.profile-body {
  display: flex;
  flex-direction: row;
  gap: 30px;
  opacity: 0.98;
  z-index: 3;
  flex: 1;
}

/* 左侧板块样式 */
.left-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 3;
}

/* 右侧板块样式 */
.right-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex: 1;
}

/* 通用模块样式 */
.section {
  background: var(--section-bg);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 3px solid transparent;
  clip-path: polygon(0 0, 100% 0, 100% 95%, 100% 100%, 0 100%, 0 95%);
}

/* 不同板块不同风格 */
.settings-section {
  border-top: 5px solid var(--primary-color);
}

.settings-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.1), rgba(30, 144, 255, 0.1));
  z-index: -1;
}

.memory-section {
  border-top: 5px solid var(--secondary-color);
}

.memory-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(30, 144, 255, 0.1), rgba(0, 191, 255, 0.1));
  z-index: -1;
}

.proxy-section {
  border-top: 5px solid var(--accent-color);
}

.proxy-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
  z-index: -1;
}

/* 动画效果 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(0, 191, 255, 0.6);
  }
  to {
    box-shadow: 0 0 30px rgba(0, 191, 255, 0.2);
  }
}

@keyframes rotateCube {
  from { transform: rotateX(0deg) rotateY(0deg); }
  to { transform: rotateX(360deg) rotateY(360deg); }
}

.section:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.7);
  transform: translateY(-5px);
}

/* 标题样式 */
.section h2 {
  font-size: 1.8rem;
  color: #ffffff;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.section h2 i {
  margin-right: 10px;
  font-size: 1.6em;
  animation: glow 2s infinite alternate;
}

/* 防止文字换行但不截断 */
.section h2,
.function-item,
.function-left,
.function-right {
  white-space: nowrap;
}

/* 功能项样式 */
.function-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.function-left {
  display: flex;
  align-items: center;
  flex: 3;
}

.function-left .label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  flex: 2;
  font-family: 'jty', sans-serif;
  font-size: 2rem;
  font-weight: bold;
  letter-spacing: 1px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  transition: background 0.3s ease;
  transform: skew(-var(--parallelogram-angle));
}

.function-left .label:hover {
  background: rgba(255, 255, 255, 0.3);
}

p {
  font-size: 20px; /* 根据需要调整字体大小 */
}

.function-left .value {
  flex: 3;
  color: var(--value-color);
  font-size: 1.2rem;
  font-weight: 500;
  white-space: nowrap;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  transition: background 0.3s ease;
  transform: skew(-var(--parallelogram-angle));
}

.function-left .value:hover {
  background: rgba(255, 255, 255, 0.3);
}

.function-right {
  flex: 2;
  margin-left: 15px;
}

.function-right .description {
  font-size: 1rem;
  color: var(--description-color);
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 12px;
  border-radius: 8px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);
  transition: background 0.3s ease, transform 0.3s ease;
  white-space: nowrap;
  transform: skew(-var(--parallelogram-angle));
}

.function-right .description:hover {
  background: rgba(0, 0, 0, 0.6);
  transform: translateY(-3px) skew(-var(--parallelogram-angle));
}

/* 页脚按钮样式 */
.profile-footer {
  display: flex;
  justify-content: flex-start;
  gap: 15px;
  margin-top: 20px;
  position: relative;
  z-index: 3;
}

.btn {
  background-color: var(--primary-color);
  color: #ffffff;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.btn:hover {
  background-color: var(--secondary-color);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 191, 255, 0.6);
}

.btn.secondary {
  background-color: var(--accent-color);
  color: #000000;
}

.btn.secondary:hover {
  background-color: #ffb300;
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.6);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  :root {
    --card-width: 1000px;
    --avatar-size: 180px;
    --cube-size: 50px;
  }

  .configurations {
    margin-left: 200px;
  }

  .name {
    font-size: 2.5rem;
  }

  .role {
    font-size: 1.6rem;
  }

  .status {
    font-size: 1.2rem;
  }

  .section {
    padding: 18px;
  }

  .section h2 {
    font-size: 1.6rem;
  }

  .function-left .label,
  .function-left .value {
    font-size: 1rem;
    padding: 6px 10px;
  }

  .function-right .description {
    font-size: 0.9rem;
  }

  .btn {
    padding: 10px 25px;
    font-size: 0.9rem;
    gap: 6px;
  }

  .complex-cube {
    margin-top: 15px;
    gap: 8px;
  }

  .complex-cube .cube {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 1024px) {
  :root {
    --card-width: 800px;
    --avatar-size: 160px;
    --cube-size: 45px;
  }

  .profile-card {
    padding: 30px;
  }

  .avatar-container {
    top: 10px;
    left: 10px;
  }

  .configurations {
    margin-left: 180px;
  }

  .profile-header {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  .name {
    font-size: 2rem;
  }

  .role {
    font-size: 1.4rem;
  }

  .status {
    font-size: 1rem;
  }

  .profile-body {
    flex-direction: column;
    gap: 15px;
  }

  .section {
    padding: 15px;
  }

  .section h2 {
    font-size: 1.4rem;
  }

  .function-item {
    flex-direction: row;
    align-items: center;
  }

  .function-right {
    margin-left: 10px;
  }

  .complex-cube {
    margin-top: 10px;
    gap: 6px;
  }

  .complex-cube .cube {
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 768px) {
  :root {
    --card-width: 100%;
    --avatar-size: 140px;
    --cube-size: 40px;
  }

  .profile-card {
    padding: 20px;
  }

  .avatar-container {
    top: 10px;
    left: 10px;
  }

  .configurations {
    margin-left: 160px;
  }

  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 10px;
  }

  .avatar {
    width: var(--avatar-size);
    height: var(--avatar-size);
    margin-right: 0;
    margin-bottom: 10px;
  }

  .name {
    font-size: 1.8rem;
  }

  .role {
    font-size: 1.2rem;
  }

  .status {
    font-size: 0.9rem;
  }

  .profile-body {
    flex-direction: column;
    gap: 10px;
  }

  .section {
    flex: 1 1 100%;
    width: 100%;
  }

  .section h2 {
    justify-content: center;
    text-align: center;
    font-size: 1.2rem;
  }

  .function-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .function-left {
    flex: 5;
  }

  .function-right {
    flex: 4;
    margin-left: 0;
    margin-top: 5px;
  }

  .function-right .description {
    width: 100%;
  }

  .function-left .label,
  .function-left .value {
    font-size: 1rem;
    padding: 6px 10px;
  }

  .btn {
    width: 100%;
    justify-content: center;
    padding: 10px 20px;
    font-size: 0.8rem;
    gap: 5px;
  }

  .complex-cube {
    margin-top: 8px;
    gap: 4px;
  }

  .complex-cube .cube {
    width: 40px;
    height: 40px;
  }
}