{"name": "SandboxWorld", "version": "1.0.0", "description": "沙盒世界生态系统 - 创建一个充满活力的虚拟社会，让AI Agent在其中自由互动、成长和发展", "author": "VCPToolBox Team", "license": "MIT", "main": "SandboxWorld.js", "type": "ecosystem", "category": "simulation", "tags": ["sandbox", "simulation", "ai-agents", "social-network", "ecosystem", "virtual-world", "autonomous-dialogue", "emergent-behavior"], "requirements": {"node": ">=14.0.0", "memory": ">=512MB", "storage": ">=100MB"}, "dependencies": {"express": "^4.18.0", "socket.io": "^4.7.0", "fs": "builtin", "path": "builtin", "events": "builtin"}, "features": {"core": ["世界环境系统 (时间、天气、地点、资源)", "Agent生态管理 (生存需求、技能发展、性格演化)", "社交网络系统 (关系管理、影响力计算、群体动态)", "自主对话引擎 (智能对话触发、情感表达、话题生成)", "事件驱动系统 (随机事件、定期事件、用户触发事件)"], "advanced": ["实时Web管理界面", "WebSocket实时更新", "RESTful API接口", "数据持久化存储", "统计分析系统"], "integration": ["与现有Agent系统集成", "心理状态系统同步", "记忆系统集成", "OneBot11消息推送"]}, "configuration": {"configFile": "config.json", "webInterface": {"enabled": true, "port": 8080, "theme": "dark"}, "world": {"maxAgents": 50, "timeSpeed": 1, "autoSave": true}, "performance": {"updateFrequencies": {"environment": 60000, "agents": 30000, "social": 60000, "dialogue": 45000, "events": 120000}}}, "api": {"commands": [{"name": "init", "description": "初始化沙盒世界系统", "parameters": []}, {"name": "start", "description": "启动沙盒世界", "parameters": []}, {"name": "stop", "description": "停止沙盒世界", "parameters": []}, {"name": "pause", "description": "暂停沙盒世界", "parameters": []}, {"name": "resume", "description": "恢复沙盒世界", "parameters": []}, {"name": "addAgent", "description": "添加新Agent到世界", "parameters": [{"name": "agentConfig", "type": "object", "description": "Agent配置信息"}]}, {"name": "removeAgent", "description": "从世界移除Agent", "parameters": [{"name": "agentId", "type": "string", "description": "Agent ID"}]}, {"name": "triggerEvent", "description": "手动触发事件", "parameters": [{"name": "eventType", "type": "string", "description": "事件类型"}, {"name": "context", "type": "object", "description": "事件上下文", "optional": true}]}, {"name": "setTimeSpeed", "description": "设置时间流速", "parameters": [{"name": "speed", "type": "number", "description": "时间流速倍数"}]}, {"name": "getStatus", "description": "获取世界状态和统计信息", "parameters": []}]}, "webInterface": {"enabled": true, "routes": [{"path": "/", "description": "主控制面板"}, {"path": "/api/world/status", "method": "GET", "description": "获取世界状态"}, {"path": "/api/agents", "method": "GET", "description": "获取所有Agent"}, {"path": "/api/relationships", "method": "GET", "description": "获取关系网络"}, {"path": "/api/events/active", "method": "GET", "description": "获取活跃事件"}, {"path": "/api/conversations", "method": "GET", "description": "获取活跃对话"}, {"path": "/api/statistics", "method": "GET", "description": "获取统计信息"}]}, "events": {"emitted": ["worldStarted", "worldStopped", "worldPaused", "worldResumed", "agentAdded", "agent<PERSON><PERSON><PERSON>d", "dialogueInitiated", "dialogueEnded", "eventTriggered", "environmentChanged"], "listened": ["userMessage", "agentInteraction", "systemEvent"]}, "dataStructure": {"storage": ["data/world.json - 世界状态数据", "data/agents/ - Agent个体数据", "data/relationships.json - 关系网络数据", "data/events/ - 事件历史数据", "data/statistics.json - 统计信息"], "backup": {"enabled": true, "interval": "daily", "retention": "30 days"}}, "performance": {"memoryUsage": "预估 100-500MB", "cpuUsage": "低到中等", "networkUsage": "WebSocket连接 + HTTP API", "storageGrowth": "约 1-10MB/天"}, "compatibility": {"vcpToolBox": ">=1.0.0", "oneBot11": ">=1.0.0", "memorySystem": ">=1.0.0", "psychologySystem": ">=1.0.0"}, "examples": {"basicUsage": ["初始化: {\"action\": \"init\"}", "启动世界: {\"action\": \"start\"}", "添加Agent: {\"action\": \"addAgent\", \"agentConfig\": {\"name\": \"测试Agent\", \"templateId\": \"雨安_2166683295\"}}", "触发事件: {\"action\": \"triggerEvent\", \"eventType\": \"festival_announcement\"}", "获取状态: {\"action\": \"getStatus\"}"]}, "documentation": {"readme": "README.md", "api": "docs/API.md", "configuration": "docs/Configuration.md", "examples": "docs/Examples.md"}}