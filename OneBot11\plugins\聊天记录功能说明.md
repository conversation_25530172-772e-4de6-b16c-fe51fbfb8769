# 聊天记录功能说明

## 🎯 功能概述

聊天记录插件实现了实时记录和保存群聊、私聊消息到本地文件的功能，支持格式化输出、媒体文件路径记录、按日期分文件存储等特性。

## 📁 文件结构

```
OneBot11/
├── chat_logs/                    # 聊天记录目录
│   ├── group/                   # 群聊记录
│   │   ├── 123456_2024-04-07.log
│   │   └── 789012_2024-04-07.log
│   └── private/                 # 私聊记录
│       ├── 111111_2024-04-07.log
│       └── 222222_2024-04-07.log
├── media_files/                 # 媒体文件目录
│   ├── images/                  # 图片文件
│   ├── files/                   # 普通文件
│   ├── videos/                  # 视频文件
│   └── records/                 # 语音文件
└── plugins/
    ├── chat_logger.js           # 聊天记录管理器
    └── chat_record_plugin.js    # 聊天记录插件
```

## 📝 记录格式

### 群聊消息格式：
```
[群聊:123456][04-07 12:26:49] 昵称(QQ号:123456666)[群身份: 管理员]说: 你好，大家好
[群聊:123456][04-07 12:27:15] 张三(QQ号:987654321)[群身份: 群员]说: 你好，并且发送了图片![image_123.jpg](../media_files/images/1712456835_image_123.jpg)
[群聊:123456][04-07 12:28:30] 李四(QQ号:555666777)[群身份: 群员]说: 大家看看这个文件，并且发送了文件![document.pdf](../media_files/files/1712456910_document.pdf)
```

### 私聊消息格式：
```
[私聊][04-07 12:26:49] 昵称(QQ号:123456666)说: 你好
[私聊][04-07 12:27:15] 张三(QQ号:987654321)说: 你好，并且发送了图片![photo.jpg](../media_files/images/1712456835_photo.jpg)
```

### 复合消息格式：
```
[群聊:123456][04-07 12:30:00] 王五(QQ号:888999000)[群身份: 管理员]说: @张三(987654321)，并且发送了[表情:微笑]，并且发送了图片![screenshot.png](../media_files/images/1712457000_screenshot.png)、文件![report.docx](../media_files/files/1712457000_report.docx)
```

## 🔧 核心功能

### 1. 自动记录功能
- ✅ **实时记录**：所有消息自动实时记录
- ✅ **分类存储**：群聊和私聊分开存储
- ✅ **按日分文件**：每天生成新的日志文件
- ✅ **高优先级**：优先级为1，确保最先处理

### 2. 消息内容分析
- ✅ **文本内容**：完整记录文本消息
- ✅ **At信息**：记录@用户的QQ号和昵称
- ✅ **表情信息**：记录QQ表情的ID和名称
- ✅ **媒体文件**：记录图片、文件、视频、语音
- ✅ **特殊类型**：记录分享、位置等特殊消息

### 3. 媒体文件处理
- ✅ **路径记录**：记录媒体文件的本地相对路径
- ✅ **分类存储**：按类型分目录存储媒体文件
- ✅ **时间戳命名**：避免文件名冲突
- 🔄 **文件下载**：暂时关闭，仅记录路径

### 4. 用户身份识别
- ✅ **群身份**：群主、管理员、群员
- ✅ **用户信息**：QQ号、昵称、群名片
- ✅ **时间戳**：精确到秒的时间记录

## 🎮 管理命令

### 状态查询
```
命令：聊天记录状态 或 chat_log_status
功能：查看插件当前配置和状态
```

### 配置管理
```
命令：聊天记录配置 群聊记录 开启/关闭
功能：开启或关闭群聊记录

命令：聊天记录配置 私聊记录 开启/关闭
功能：开启或关闭私聊记录
```

## 🔧 技术实现

### 插件系统集成
```javascript
class ChatRecordPlugin extends BasePlugin {
    constructor(protocol, logger, config) {
        super();
        this.name = 'chat_record_plugin';
        this.priority = 1; // 最高优先级
        this.permission = 'all';
    }

    async shouldHandle(context) {
        return context.event && context.event.post_type === 'message';
    }

    async execute(context) {
        // 异步保存，不阻塞其他插件
        setImmediate(() => this.saveChatRecord(context));
        return { handled: false }; // 不阻止其他插件处理
    }
}
```

### 消息段处理
```javascript
async processMessageSegments(messageSegments) {
    const result = {
        textContent: '',
        mediaInfo: [],
        atInfo: [],
        faceInfo: [],
        otherInfo: []
    };

    for (const segment of messageSegments) {
        switch (segment.type) {
            case 'text':
                result.textContent += segment.data?.text || '';
                break;
            case 'image':
                // 处理图片信息和路径
                break;
            // ... 其他类型处理
        }
    }
}
```

### 格式化输出
```javascript
formatChatLog(context, processedContent) {
    const timestamp = this.helper.formatTimestamp(context.time);
    const userId = context.user_id;
    const nickname = context.sender?.nickname;
    
    if (context.message_type === 'group') {
        const groupId = context.group_id;
        const role = this.helper.getGroupRole(context.sender?.role);
        logEntry = `[群聊:${groupId}][${timestamp}] ${nickname}(QQ号:${userId})[群身份: ${role}]说: `;
    }
    
    // 添加各种内容类型...
}
```

## 📊 配置选项

### 记录配置
```javascript
recordConfig = {
    enableGroupRecord: true,     // 群聊记录开关
    enablePrivateRecord: true,   // 私聊记录开关
    enableMediaDownload: false,  // 媒体下载开关
    excludeGroups: [],          // 排除群号列表
    excludeUsers: [],           // 排除用户列表
    maxLogFileSize: 10 * 1024 * 1024 // 最大文件大小
};
```

### 过滤规则
- ✅ **自消息过滤**：不记录机器人自己的消息
- ✅ **用户过滤**：支持排除指定用户
- ✅ **群组过滤**：支持排除指定群组
- ✅ **类型过滤**：可选择记录的消息类型

## 🧪 测试场景

### 场景1：基础文本消息
```
发送：普通文本消息
预期：[群聊:123456][04-07 12:26:49] 昵称(QQ号:123456666)[群身份: 群员]说: 普通文本消息
```

### 场景2：复合消息
```
发送：文本 + @某人 + 表情 + 图片
预期：[群聊:123456][04-07 12:27:00] 昵称(QQ号:123456666)[群身份: 群员]说: 文本内容，并且@了张三(987654321)，并且发送了[表情:微笑]，并且发送了图片![image.jpg](../media_files/images/1712456820_image.jpg)
```

### 场景3：媒体文件
```
发送：文件 + 视频 + 语音
预期：[群聊:123456][04-07 12:28:00] 昵称(QQ号:123456666)[群身份: 群员]说: 发送了文件![document.pdf](../media_files/files/1712456880_document.pdf)、视频![video.mp4](../media_files/videos/1712456880_video.mp4)、语音![voice.amr](../media_files/records/1712456880_voice.amr)
```

### 场景4：管理命令
```
发送：聊天记录状态
预期：返回插件状态信息，同时记录该命令消息
```

## ⚠️ 注意事项

1. **存储空间**：长期使用需要定期清理日志文件
2. **权限要求**：需要文件系统写入权限
3. **性能影响**：大量消息可能影响性能，已使用异步处理
4. **隐私保护**：记录的消息包含敏感信息，需要妥善保管

## 🔧 故障排除

### 常见问题：
1. **文件写入失败**：检查目录权限和磁盘空间
2. **插件未加载**：检查plugin_order.txt配置
3. **记录格式异常**：查看日志确认消息段解析
4. **媒体路径错误**：检查媒体文件目录结构

现在聊天记录功能已经完全集成到OneBot11插件系统中，支持实时记录、格式化输出、媒体文件管理等全面功能！
