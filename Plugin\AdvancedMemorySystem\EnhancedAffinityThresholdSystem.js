/**
 * 增强好感度阈值处理系统
 * 实现更细致的好感度等级区分和不同阈值下的差异化处理
 */

class EnhancedAffinityThresholdSystem {
    constructor(config, logger) {
        this.config = config;
        this.logger = logger;
        
        // 详细的好感度阈值配置
        this.affinityThresholds = {
            // 负面关系区间
            hostile: { 
                range: [-100, -60], 
                name: '敌对', 
                description: '强烈敌意，拒绝合作',
                responseModifier: -0.8,
                trustLevel: 0.1,
                cooperationWillingness: 0.1,
                emotionalResonance: 0.2,
                communicationStyle: 'defensive'
            },
            very_unfriendly: { 
                range: [-60, -30], 
                name: '非常不友好', 
                description: '明显不满，消极态度',
                responseModifier: -0.6,
                trustLevel: 0.2,
                cooperationWillingness: 0.3,
                emotionalResonance: 0.3,
                communicationStyle: 'cold'
            },
            unfriendly: { 
                range: [-30, -10], 
                name: '不友好', 
                description: '轻微不满，保持距离',
                responseModifier: -0.3,
                trustLevel: 0.4,
                cooperationWillingness: 0.5,
                emotionalResonance: 0.4,
                communicationStyle: 'formal'
            },
            
            // 中性关系区间
            neutral_negative: { 
                range: [-10, 10], 
                name: '中性偏冷', 
                description: '中性态度，略显冷淡',
                responseModifier: -0.1,
                trustLevel: 0.5,
                cooperationWillingness: 0.6,
                emotionalResonance: 0.5,
                communicationStyle: 'neutral'
            },
            neutral: { 
                range: [10, 30], 
                name: '中性', 
                description: '正常交往，无特殊情感',
                responseModifier: 0.0,
                trustLevel: 0.6,
                cooperationWillingness: 0.7,
                emotionalResonance: 0.6,
                communicationStyle: 'friendly'
            },
            neutral_positive: { 
                range: [30, 50], 
                name: '中性偏暖', 
                description: '友善态度，愿意交流',
                responseModifier: 0.1,
                trustLevel: 0.7,
                cooperationWillingness: 0.8,
                emotionalResonance: 0.7,
                communicationStyle: 'warm'
            },
            
            // 正面关系区间
            friendly: { 
                range: [50, 70], 
                name: '友好', 
                description: '友好关系，相互信任',
                responseModifier: 0.3,
                trustLevel: 0.8,
                cooperationWillingness: 0.9,
                emotionalResonance: 0.8,
                communicationStyle: 'enthusiastic'
            },
            very_friendly: { 
                range: [70, 85], 
                name: '非常友好', 
                description: '深度友谊，高度信任',
                responseModifier: 0.5,
                trustLevel: 0.9,
                cooperationWillingness: 0.95,
                emotionalResonance: 0.9,
                communicationStyle: 'caring'
            },
            intimate: { 
                range: [85, 100], 
                name: '亲密', 
                description: '亲密关系，无条件信任',
                responseModifier: 0.7,
                trustLevel: 1.0,
                cooperationWillingness: 1.0,
                emotionalResonance: 1.0,
                communicationStyle: 'intimate'
            }
        };
        
        // 好感度变化的动态调节参数
        this.dynamicAdjustment = {
            // 不同阈值下的变化敏感性
            changeSensitivity: {
                hostile: 0.5,           // 敌对状态下变化较慢
                very_unfriendly: 0.6,
                unfriendly: 0.7,
                neutral_negative: 0.8,
                neutral: 1.0,           // 中性状态变化正常
                neutral_positive: 1.1,
                friendly: 1.2,          // 友好状态变化较快
                very_friendly: 1.0,
                intimate: 0.8           // 亲密状态变化较慢（稳定）
            },
            
            // 不同阈值下的恢复速度
            recoveryRate: {
                hostile: 0.3,           // 从敌对恢复很慢
                very_unfriendly: 0.5,
                unfriendly: 0.7,
                neutral_negative: 0.9,
                neutral: 1.0,
                neutral_positive: 1.1,
                friendly: 1.2,
                very_friendly: 1.0,
                intimate: 0.9
            },
            
            // 阈值跨越的额外影响
            thresholdCrossing: {
                positive: 0.2,          // 跨越到更高阈值的奖励
                negative: -0.3          // 跨越到更低阈值的惩罚
            }
        };
    }

    /**
     * 获取当前好感度等级
     */
    getAffinityLevel(affinityValue) {
        for (const [level, config] of Object.entries(this.affinityThresholds)) {
            if (affinityValue >= config.range[0] && affinityValue < config.range[1]) {
                return {
                    level,
                    config,
                    value: affinityValue
                };
            }
        }
        
        // 边界情况处理
        if (affinityValue >= 100) {
            return {
                level: 'intimate',
                config: this.affinityThresholds.intimate,
                value: affinityValue
            };
        } else {
            return {
                level: 'hostile',
                config: this.affinityThresholds.hostile,
                value: affinityValue
            };
        }
    }

    /**
     * 应用好感度阈值调节
     */
    applyAffinityThresholdAdjustment(rawChange, currentValue, context = {}) {
        try {
            const currentLevel = this.getAffinityLevel(currentValue);
            const newValue = currentValue + rawChange;
            const newLevel = this.getAffinityLevel(newValue);
            
            // 1. 应用当前阈值的敏感性调节
            let adjustedChange = rawChange * this.dynamicAdjustment.changeSensitivity[currentLevel.level];
            
            // 2. 检查阈值跨越
            const thresholdCrossing = this.detectThresholdCrossing(currentLevel, newLevel);
            if (thresholdCrossing.crossed) {
                adjustedChange += thresholdCrossing.adjustment;
                this.logger.info('好感度阈值', 
                    `阈值跨越: ${currentLevel.level} → ${newLevel.level}, 调节: ${thresholdCrossing.adjustment.toFixed(3)}`
                );
            }
            
            // 3. 应用上下文相关的调节
            adjustedChange = this.applyContextualAdjustment(adjustedChange, currentLevel, context);
            
            // 4. 应用非线性阈值效应
            adjustedChange = this.applyNonlinearThresholdEffects(adjustedChange, currentValue, newValue);
            
            // 5. 计算最终的好感度等级信息
            const finalValue = Math.max(-100, Math.min(100, currentValue + adjustedChange));
            const finalLevel = this.getAffinityLevel(finalValue);
            
            return {
                adjusted_change: adjustedChange,
                original_change: rawChange,
                current_level: currentLevel,
                new_level: finalLevel,
                threshold_crossed: thresholdCrossing.crossed,
                adjustment_factors: {
                    sensitivity: this.dynamicAdjustment.changeSensitivity[currentLevel.level],
                    threshold_crossing: thresholdCrossing.adjustment,
                    contextual: context.contextual_modifier || 0
                }
            };

        } catch (error) {
            this.logger.error('好感度阈值', '阈值调节失败:', error.message);
            return {
                adjusted_change: rawChange,
                original_change: rawChange,
                current_level: this.getAffinityLevel(currentValue),
                new_level: this.getAffinityLevel(currentValue + rawChange),
                threshold_crossed: false
            };
        }
    }

    /**
     * 检测阈值跨越
     */
    detectThresholdCrossing(currentLevel, newLevel) {
        if (currentLevel.level === newLevel.level) {
            return { crossed: false, adjustment: 0, direction: 'none' };
        }
        
        // 获取阈值等级的数值顺序
        const levelOrder = Object.keys(this.affinityThresholds);
        const currentIndex = levelOrder.indexOf(currentLevel.level);
        const newIndex = levelOrder.indexOf(newLevel.level);
        
        let adjustment = 0;
        let direction = 'none';
        
        if (newIndex > currentIndex) {
            // 向上跨越（好感度提升）
            direction = 'positive';
            adjustment = this.dynamicAdjustment.thresholdCrossing.positive * (newIndex - currentIndex);
        } else if (newIndex < currentIndex) {
            // 向下跨越（好感度下降）
            direction = 'negative';
            adjustment = this.dynamicAdjustment.thresholdCrossing.negative * (currentIndex - newIndex);
        }
        
        return {
            crossed: true,
            adjustment,
            direction,
            levels_crossed: Math.abs(newIndex - currentIndex)
        };
    }

    /**
     * 应用上下文相关调节
     */
    applyContextualAdjustment(change, currentLevel, context) {
        let contextualModifier = 1.0;
        
        // 基于当前关系状态的上下文调节
        if (context.interaction_quality) {
            switch (context.interaction_quality) {
                case 'excellent':
                    contextualModifier *= currentLevel.config.trustLevel > 0.7 ? 1.3 : 1.1;
                    break;
                case 'good':
                    contextualModifier *= 1.1;
                    break;
                case 'poor':
                    contextualModifier *= currentLevel.config.trustLevel < 0.5 ? 0.6 : 0.8;
                    break;
                case 'terrible':
                    contextualModifier *= 0.5;
                    break;
            }
        }
        
        // 基于情感共鸣的调节
        if (context.emotional_resonance) {
            const resonanceBonus = context.emotional_resonance * currentLevel.config.emotionalResonance * 0.2;
            contextualModifier += resonanceBonus;
        }
        
        // 基于信任水平的调节
        if (context.trust_level) {
            const trustAlignment = Math.abs(context.trust_level - currentLevel.config.trustLevel);
            if (trustAlignment < 0.2) {
                contextualModifier *= 1.1; // 信任水平匹配时增强效果
            }
        }
        
        return change * contextualModifier;
    }

    /**
     * 应用非线性阈值效应
     */
    applyNonlinearThresholdEffects(change, currentValue, newValue) {
        // 1. 边界阻力效应（接近极值时变化困难）
        const boundaryResistance = this.calculateBoundaryResistance(currentValue, newValue);
        
        // 2. 中性区域促进效应（在中性区域变化相对容易）
        const neutralZoneBonus = this.calculateNeutralZoneBonus(currentValue);
        
        // 3. 稳定性效应（高好感度和低好感度都有稳定性）
        const stabilityEffect = this.calculateStabilityEffect(currentValue);
        
        return change * boundaryResistance * neutralZoneBonus * stabilityEffect;
    }

    /**
     * 计算边界阻力
     */
    calculateBoundaryResistance(currentValue, newValue) {
        const extremeThreshold = 85; // 极值阈值
        
        // 检查是否接近边界
        const distanceFromLowerBound = Math.abs(currentValue - (-100));
        const distanceFromUpperBound = Math.abs(currentValue - 100);
        const minDistance = Math.min(distanceFromLowerBound, distanceFromUpperBound);
        
        if (minDistance < 15) {
            // 接近边界时增加阻力
            const resistance = 0.5 + (minDistance / 15) * 0.5;
            return resistance;
        }
        
        return 1.0; // 正常阻力
    }

    /**
     * 计算中性区域奖励
     */
    calculateNeutralZoneBonus(currentValue) {
        // 在中性区域（-10到50）变化相对容易
        if (currentValue >= -10 && currentValue <= 50) {
            return 1.2;
        }
        return 1.0;
    }

    /**
     * 计算稳定性效应
     */
    calculateStabilityEffect(currentValue) {
        // 极端值（很高或很低好感度）具有稳定性
        if (currentValue >= 80 || currentValue <= -60) {
            return 0.8; // 稳定状态变化较慢
        }
        return 1.0;
    }

    /**
     * 生成好感度状态报告
     */
    generateAffinityReport(affinityLevel, adjustmentResult) {
        return {
            current_level: affinityLevel.level,
            level_name: affinityLevel.config.name,
            level_description: affinityLevel.config.description,
            affinity_value: affinityLevel.value,
            response_modifier: affinityLevel.config.responseModifier,
            trust_level: affinityLevel.config.trustLevel,
            cooperation_willingness: affinityLevel.config.cooperationWillingness,
            emotional_resonance: affinityLevel.config.emotionalResonance,
            communication_style: affinityLevel.config.communicationStyle,
            adjustment_summary: {
                original_change: adjustmentResult.original_change,
                adjusted_change: adjustmentResult.adjusted_change,
                threshold_crossed: adjustmentResult.threshold_crossed,
                sensitivity_factor: adjustmentResult.adjustment_factors.sensitivity
            },
            recommendations: this.generateRecommendations(affinityLevel)
        };
    }

    /**
     * 生成建议
     */
    generateRecommendations(affinityLevel) {
        const recommendations = [];
        
        if (affinityLevel.config.trustLevel < 0.5) {
            recommendations.push('需要建立信任关系');
        }
        
        if (affinityLevel.config.cooperationWillingness < 0.7) {
            recommendations.push('提高合作意愿');
        }
        
        if (affinityLevel.config.emotionalResonance < 0.6) {
            recommendations.push('增强情感共鸣');
        }
        
        if (affinityLevel.level === 'hostile' || affinityLevel.level === 'very_unfriendly') {
            recommendations.push('优先修复关系，避免进一步恶化');
        }
        
        return recommendations;
    }
}

module.exports = EnhancedAffinityThresholdSystem;
