const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, 'config.env') });

module.exports = {
  apps: [
    {
      name: "MainService",
      script: "server.js",
      instances: 1,
      exec_mode: "fork",
      watch: true,
      ignore_watch: ["node_modules", "logs", "*.log"],
      max_memory_restart: "1G",
      env: {
        NODE_ENV: "development",
        PORT: process.env.PORT || 6005,
        FORCE_COLOR: "true"
      },
      env_production: {
        NODE_ENV: "production",
        PORT: process.env.PORT || 6005,
        FORCE_COLOR: "true"
      },
      error_file: "logs/main_service_error.log",
      out_file: "logs/main_service_out.log",
      merge_logs: true,
      log_date_format: "",
      autorestart: true,
      exp_backoff_restart_delay: 100
    },
    {
      name: "WechatBot",
      script: "bot.py",
      interpreter: "python",
      cwd: "./Wechat",
      watch: false,
      interpreter_args: ["-u"],
      windowsHide: true,
      env: {
        PYTHONUNBUFFERED: "1",
        PYTHONIOENCODING: "utf-8",
        LANG: "zh_CN.UTF-8",
        FORCE_COLOR: "true",
        VIRTUAL_ENV: path.resolve(__dirname, "Wechat/venv"),
        PATH: path.resolve(__dirname, "Wechat/venv/Scripts") + path.delimiter + process.env.PATH,
        ...process.env,
        PORT: process.env.PORT || 6005,
        API_Key: process.env.API_Key,
        API_URL: process.env.API_URL,
        VCP_Key: process.env.VCP_Key,
        Key: process.env.Key,
        Image_Key: process.env.Image_Key
      },
      error_file: "logs/wechat_bot_error.log",
      out_file: "logs/wechat_bot_out.log",
      merge_logs: true,
      log_date_format: "",
      autorestart: true,
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      windowsHide: false
    }
  ]
};
