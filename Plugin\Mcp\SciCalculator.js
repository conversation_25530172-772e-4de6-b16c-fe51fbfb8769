// Plugin/Mcp/SciCalculator.js - 科学计算MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}


class SciCalculatorMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'SciCalculator';
        this.description = '执行科学计算和数学运算';
        this.vcpName = 'SciCalculator';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                expression: {
                    type: 'string',
                    description: '要计算的数学表达式，支持基本运算、科学函数、三角函数等'
                },
                precision: {
                    type: 'number',
                    description: '计算结果的精度（小数位数）',
                    minimum: 0,
                    maximum: 16,
                    default: 6
                },
                format: {
                    type: 'string',
                    description: '结果格式化方式',
                    enum: ['decimal', 'scientific', 'engineering'],
                    default: 'decimal'
                },
                variables: {
                    type: 'object',
                    description: '变量定义，用于表达式中的变量替换',
                    additionalProperties: {
                        type: 'number'
                    },
                    default: {}
                },
                unit_system: {
                    type: 'string',
                    description: '单位制',
                    enum: ['metric', 'imperial'],
                    default: 'metric'
                }
            },
            required: ['expression']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);
        
        this.log('info', `开始计算`, {
            expression: args.expression,
            precision: args.precision || 6
        });
        
        try {
        // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);
            
            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }
            
            // 构建标准响应格式
            const response = {
                type: 'calculation',
                status: 'success',
                message: '计算完成',
                data: {
                    expression: args.expression,
                    result: parsedResult?.result,
                    formatted_result: parsedResult?.formatted_result,
                    precision: args.precision || 6,
                    format: args.format || 'decimal',
                    calculation_info: {
                        steps: parsedResult?.steps || [],
                        execution_time: parsedResult?.execution_time,
                        memory_usage: parsedResult?.memory_usage
                    },
                    error_margin: parsedResult?.error_margin,
                    units: parsedResult?.units
                }
            };
        
            this.log('success', `计算完成`, {
                expression: args.expression,
                result: response.data.formatted_result || response.data.result
            });
        
            return response;
            
        } catch (error) {
            const errorResponse = {
            type: 'calculation',
                status: 'error',
                message: error.message,
                data: {
            expression: args.expression,
                    error: error.message
                }
            };
            
            this.log('error', `计算失败`, errorResponse);
            throw errorResponse;
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查Python环境和依赖
            try {
                const { execSync } = require('child_process');
                execSync('python -c "import numpy; import scipy"');
            } catch (error) {
                throw new Error('缺少必要的Python依赖（numpy, scipy）');
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = SciCalculatorMcp; 