/**
 * 集成心理状态分析器
 * 结合世界树物理状态与情感记忆算法的深度分析
 * 
 * 集成算法理论：
 * - Russell环形模型 (情绪维度分析)
 * - PAD情感模型 (Pleasure-Arousal-Dominance)
 * - Plutchik情绪轮盘 (基础情绪分类)
 * - Yerkes-<PERSON><PERSON>定律 (压力-表现关系)
 * - <PERSON><PERSON><PERSON><PERSON>人格理论 (情绪稳定性)
 * - Lazarus认知评价理论 (压力认知)
 * - Sternberg三元爱情理论 (关系分析)
 * - Dawkins适应度理论 (认知成熟度)
 * - 模因动力学 (思维模式传播)
 */

const moment = require('moment');

class IntegratedPsychologyAnalyzer {
    constructor(config = {}) {
        this.config = {
            // 情绪分析参数
            emotionSensitivity: config.emotionSensitivity || 0.7,
            stressThreshold: config.stressThreshold || 0.6,
            relationshipDepth: config.relationshipDepth || 0.8,
            
            // 认知分析参数
            cognitiveComplexity: config.cognitiveComplexity || 0.75,
            memoryEfficiency: config.memoryEfficiency || 0.85,
            adaptiveFitness: config.adaptiveFitness || 0.80,
            
            ...config
        };
        
        // Russell环形模型象限定义
        this.russellQuadrants = {
            'high_arousal_positive': '高唤醒积极象限',
            'high_arousal_negative': '高唤醒消极象限', 
            'low_arousal_positive': '低唤醒积极象限',
            'low_arousal_negative': '低唤醒消极象限'
        };
        
        // Plutchik基础情绪轮盘
        this.plutchikEmotions = {
            joy: { intensity: [0.8, 1.0], name: 'Joy/Ecstasy' },
            trust: { intensity: [0.6, 0.8], name: 'Trust/Admiration' },
            fear: { intensity: [0.7, 0.9], name: 'Fear/Terror' },
            surprise: { intensity: [0.5, 0.7], name: 'Surprise/Amazement' },
            sadness: { intensity: [0.6, 0.8], name: 'Sadness/Grief' },
            disgust: { intensity: [0.5, 0.7], name: 'Disgust/Loathing' },
            anger: { intensity: [0.7, 0.9], name: 'Anger/Rage' },
            anticipation: { intensity: [0.6, 0.8], name: 'Anticipation/Vigilance' }
        };
        
        // Yerkes-Dodson压力区间
        this.stressZones = {
            'under_arousal': { range: [-1, 0.3], performance: 'low', description: '低压力区间 - 可能表现不足' },
            'optimal': { range: [0.3, 0.7], performance: 'peak', description: '最优压力区间 - 峰值表现' },
            'over_arousal': { range: [0.7, 1], performance: 'declining', description: '高压力区间 - 表现下降' }
        };
    }

    /**
     * 综合分析心理状态
     * 结合物理状态指标与情感算法
     */
    analyzeIntegratedPsychology(physicalState, emotionalContext = {}, relationshipContext = {}) {
        const analysis = {
            timestamp: moment().format('YYYY/MM/DD HH:mm:ss'),
            physical: this.analyzePhysicalState(physicalState),
            emotion: this.analyzeEmotionalState(physicalState, emotionalContext),
            stress: this.analyzeStressState(physicalState, emotionalContext),
            relationship: this.analyzeRelationshipState(relationshipContext),
            cognitive: this.analyzeCognitiveState(physicalState, emotionalContext),
            conversationalImpact: this.analyzeConversationalImpact(physicalState, emotionalContext)
        };
        
        return analysis;
    }

    /**
     * 分析物理状态（基于世界树指标）
     */
    analyzePhysicalState(state) {
        const { focus, energy, fatigue, alertness, hunger } = state;
        
        // 计算综合生理状态
        const physiologicalBalance = (energy + (100 - fatigue) + alertness) / 3;
        const cognitiveCapacity = (focus + alertness) / 2;
        const metabolicState = 100 - hunger;
        
        return {
            raw: {
                focus: focus.toFixed(1),
                energy: energy.toFixed(1), 
                fatigue: fatigue.toFixed(1),
                alertness: alertness.toFixed(1),
                hunger: hunger.toFixed(1)
            },
            computed: {
                physiologicalBalance: physiologicalBalance.toFixed(1),
                cognitiveCapacity: cognitiveCapacity.toFixed(1),
                metabolicState: metabolicState.toFixed(1),
                overallWellbeing: ((physiologicalBalance + cognitiveCapacity + metabolicState) / 3).toFixed(1)
            },
            interpretation: this.interpretPhysicalState(state)
        };
    }

    /**
     * 情绪状态分析（Russell + PAD + Plutchik）
     */
    analyzeEmotionalState(physicalState, emotionalContext) {
        const { focus, energy, fatigue, alertness } = physicalState;
        
        // 计算PAD维度
        const pleasure = this.calculatePleasure(energy, fatigue, emotionalContext);
        const arousal = this.calculateArousal(alertness, focus, emotionalContext);
        const dominance = this.calculateDominance(focus, energy, emotionalContext);
        
        // Russell环形模型定位
        const russellQuadrant = this.getRussellQuadrant(pleasure, arousal);
        
        // Plutchik情绪识别
        const plutchikEmotion = this.identifyPlutchikEmotion(pleasure, arousal, dominance);
        
        // 情绪稳定性（Eysenck理论）
        const stability = this.calculateEmotionalStability(physicalState, emotionalContext);
        
        // 行为倾向预测
        const behaviorTendency = this.predictBehaviorTendency(pleasure, arousal, dominance);
        
        return {
            pad: {
                pleasure: pleasure.toFixed(2),
                arousal: arousal.toFixed(2),
                dominance: dominance.toFixed(2)
            },
            russell: {
                quadrant: russellQuadrant,
                description: this.russellQuadrants[russellQuadrant]
            },
            plutchik: {
                primary: plutchikEmotion.name,
                intensity: plutchikEmotion.intensity.toFixed(2)
            },
            stability: {
                score: stability.toFixed(2),
                level: stability > 0.7 ? '高稳定性' : stability > 0.4 ? '中等稳定性' : '低稳定性',
                theory: 'Eysenck人格理论'
            },
            behavior: behaviorTendency
        };
    }

    /**
     * 压力状态分析（Yerkes-Dodson + Lazarus + GAS）
     */
    analyzeStressState(physicalState, emotionalContext) {
        const { focus, energy, fatigue, alertness } = physicalState;
        
        // 计算压力水平
        const stressLevel = this.calculateStressLevel(physicalState, emotionalContext);
        
        // Yerkes-Dodson区间判定
        const yerkesZone = this.getYerkesZone(stressLevel);
        
        // GAS阶段识别
        const gasStage = this.identifyGASStage(fatigue, energy, stressLevel);
        
        // Lazarus认知评价
        const cognitiveAppraisal = this.performCognitiveAppraisal(physicalState, emotionalContext);
        
        // 认知影响分析
        const cognitiveImpact = this.analyzeCognitiveImpact(stressLevel, physicalState);
        
        // 应激源识别
        const stressors = this.identifyStressors(physicalState, emotionalContext);
        
        return {
            level: stressLevel.toFixed(2),
            yerkes: {
                zone: yerkesZone.description,
                performance: yerkesZone.performance,
                theory: 'Yerkes-Dodson定律'
            },
            gas: {
                stage: gasStage,
                theory: 'Selye一般适应综合征'
            },
            appraisal: {
                type: cognitiveAppraisal,
                theory: 'Lazarus认知评价理论'
            },
            impact: cognitiveImpact,
            stressors: stressors
        };
    }

    /**
     * 关系状态分析（Sternberg + 社交渗透理论）
     */
    analyzeRelationshipState(relationshipContext) {
        const { affinity = 0.8, relationshipType = 'friend', interactionHistory = [] } = relationshipContext;
        
        // Sternberg三元分析
        const sternberg = this.analyzeSternbergTriangle(affinity, relationshipType, interactionHistory);
        
        // 社交渗透分析
        const socialPenetration = this.analyzeSocialPenetration(interactionHistory, affinity);
        
        // 关系阶段识别
        const relationshipStage = this.identifyRelationshipStage(affinity, interactionHistory);
        
        return {
            affinity: affinity.toFixed(2),
            type: relationshipType,
            sternberg: {
                intimacy: sternberg.intimacy.toFixed(2),
                passion: sternberg.passion.toFixed(2),
                commitment: sternberg.commitment.toFixed(2),
                loveType: sternberg.loveType,
                theory: 'Sternberg三元爱情理论'
            },
            penetration: {
                breadth: socialPenetration.breadth.toFixed(2),
                depth: socialPenetration.depth.toFixed(2),
                theory: 'Altman-Taylor社交渗透理论'
            },
            stage: relationshipStage,
            behavior: this.predictSocialBehavior(sternberg, socialPenetration)
        };
    }

    /**
     * 认知状态分析（Dawkins + 模因动力学 + 信息处理）
     */
    analyzeCognitiveState(physicalState, emotionalContext) {
        const { focus, energy, alertness } = physicalState;
        
        // Dawkins适应度计算
        const adaptiveFitness = this.calculateAdaptiveFitness(physicalState, emotionalContext);
        
        // 认知负荷分析
        const cognitiveLoad = this.analyzeCognitiveLoad(focus, energy, emotionalContext);
        
        // 信息处理能力
        const informationProcessing = this.analyzeInformationProcessing(physicalState);
        
        // 社会认知能力
        const socialCognition = this.analyzeSocialCognition(physicalState, emotionalContext);
        
        // 模因动力学
        const memeDynamics = this.analyzeMemeDynamics(physicalState, emotionalContext);
        
        return {
            maturity: adaptiveFitness.toFixed(2),
            fitness: {
                score: adaptiveFitness.toFixed(2),
                theory: 'Dawkins适应度理论'
            },
            load: cognitiveLoad,
            processing: informationProcessing,
            social: socialCognition,
            memes: memeDynamics
        };
    }

    /**
     * 对话影响分析
     */
    analyzeConversationalImpact(physicalState, emotionalContext) {
        const { focus, energy, fatigue, alertness } = physicalState;
        
        // 对话能力评估
        const conversationalCapacity = (focus + energy + alertness - fatigue) / 3;
        
        // 表达倾向
        const expressionTendency = this.analyzeExpressionTendency(physicalState, emotionalContext);
        
        // 理解能力
        const comprehensionAbility = this.analyzeComprehensionAbility(physicalState);
        
        // 情感共鸣能力
        const empathyCapacity = this.analyzeEmpathyCapacity(physicalState, emotionalContext);
        
        // 对话策略建议
        const conversationStrategy = this.suggestConversationStrategy(physicalState, emotionalContext);
        
        return {
            capacity: conversationalCapacity.toFixed(1),
            expression: expressionTendency,
            comprehension: comprehensionAbility,
            empathy: empathyCapacity,
            strategy: conversationStrategy,
            recommendations: this.generateConversationRecommendations(physicalState, emotionalContext)
        };
    }

    // 辅助计算方法
    calculatePleasure(energy, fatigue, context) {
        const baseP = (energy - fatigue + 100) / 200; // 0-1范围
        const contextP = (context.achievement || 0.5) * 0.3;
        return Math.max(-1, Math.min(1, (baseP - 0.5) * 2 + contextP));
    }

    calculateArousal(alertness, focus, context) {
        const baseA = (alertness + focus) / 200; // 0-1范围
        const contextA = (context.novelty || 0.3) * 0.2;
        return Math.max(-1, Math.min(1, (baseA - 0.5) * 2 + contextA));
    }

    calculateDominance(focus, energy, context) {
        const baseD = (focus + energy) / 200; // 0-1范围
        const contextD = (1 - (context.uncertainty || 0.3)) * 0.3;
        return Math.max(-1, Math.min(1, (baseD - 0.5) * 2 + contextD));
    }

    getRussellQuadrant(pleasure, arousal) {
        if (arousal > 0 && pleasure > 0) return 'high_arousal_positive';
        if (arousal > 0 && pleasure <= 0) return 'high_arousal_negative';
        if (arousal <= 0 && pleasure > 0) return 'low_arousal_positive';
        return 'low_arousal_negative';
    }

    identifyPlutchikEmotion(pleasure, arousal, dominance) {
        // 简化的Plutchik情绪识别
        if (pleasure > 0.3 && arousal > 0) return { name: 'Joy/Excitement', intensity: Math.abs(pleasure + arousal) / 2 };
        if (pleasure > 0.3 && arousal <= 0) return { name: 'Trust/Acceptance', intensity: Math.abs(pleasure) };
        if (pleasure <= 0 && arousal > 0.5) return { name: 'Fear/Anxiety', intensity: Math.abs(pleasure + arousal) / 2 };
        if (pleasure <= 0 && arousal <= 0) return { name: 'Sadness/Melancholy', intensity: Math.abs(pleasure) };
        return { name: 'Neutral/Calm', intensity: 0.3 };
    }

    calculateEmotionalStability(physicalState, context) {
        const { energy, fatigue } = physicalState;
        const physiologicalStability = (energy + (100 - fatigue)) / 200;
        const contextualStability = 1 - (context.uncertainty || 0.3);
        return (physiologicalStability + contextualStability) / 2;
    }

    // 更多辅助方法...
    interpretPhysicalState(state) {
        const { focus, energy, fatigue, alertness } = state;
        
        let interpretation = [];
        if (fatigue > 80) interpretation.push('严重疲劳状态');
        if (energy < 20) interpretation.push('精力严重不足');
        if (focus > 80) interpretation.push('高度专注状态');
        if (alertness < 40) interpretation.push('警觉性下降');
        
        return interpretation.length > 0 ? interpretation : ['正常生理状态'];
    }

    // 省略其他辅助方法的具体实现...
    calculateStressLevel(physicalState, context) { return 0.3; }
    getYerkesZone(level) { return this.stressZones.under_arousal; }
    identifyGASStage(fatigue, energy, stress) { return '警觉期'; }
    performCognitiveAppraisal(state, context) { return '威胁性评价'; }
    analyzeCognitiveImpact(stress, state) { return ['注意力分散', '动机不足', '反应迟缓']; }
    identifyStressors(state, context) { return ['任务明确性', '情感交流缺失']; }
    
    // 其他方法的简化实现...
    analyzeSternbergTriangle() { return { intimacy: 0.8, passion: 0.4, commitment: 0.9, loveType: '伴侣之爱' }; }
    analyzeSocialPenetration() { return { breadth: 0.9, depth: 0.8 }; }
    identifyRelationshipStage() { return 'Levinger结束阶段'; }
    predictSocialBehavior() { return ['深度自我披露', '情感支持', '无条件接纳']; }
    
    calculateAdaptiveFitness() { return 0.83; }
    analyzeCognitiveLoad() { return { type: '相关负荷占主导', description: '复杂认知建构' }; }
    analyzeInformationProcessing() { return { encoding: 0.75, storage: 0.95, retrieval: 0.66 }; }
    analyzeSocialCognition() { return { observational: '优化', imitation: '增强', efficacy: '高' }; }
    analyzeMemeDynamics() { return { replication: 1.00, mutation: 0.14, activeMemes: ['固定行程安排'], state: '共生演化状态' }; }
    
    predictBehaviorTendency() { return ['温和积极', '合作倾向', '开放态度']; }
    analyzeExpressionTendency() { return { verbosity: 'moderate', depth: 'high', emotional: 'controlled' }; }
    analyzeComprehensionAbility() { return { level: 'high', processing: 'analytical', context: 'strong' }; }
    analyzeEmpathyCapacity() { return { cognitive: 0.8, affective: 0.7, compassionate: 0.9 }; }
    suggestConversationStrategy() { return 'deep_technical_discussion'; }
    generateConversationRecommendations() { return ['保持技术深度', '适当情感支持', '尊重疲劳状态']; }
}

module.exports = IntegratedPsychologyAnalyzer;
