/**
 * 自动调节算法服务
 * 使用复杂算法自动调节AI心理状态，包括压力、情绪和好感度的自动恢复机制
 */

class AutoRegulationService {
    constructor(config, openaiService, logger) {
        this.config = config;
        this.openaiService = openaiService; // 可选，自动调节主要使用本地算法
        this.logger = logger;
        this.isInitialized = false;
        this.db = null;
        
        // 自动调节参数配置 - 优化的指数函数调节机制，更精细的阈值控制
        this.regulationConfig = {
            // 压力自动调节配置（范围-100到100）
            stress: {
                lowThreshold: -5,        // 压力低于-5开始自动增加（负压力过度）
                highThreshold: 10,       // 压力高于10开始自动缓解
                targetValue: 0,          // 目标压力值（平衡点）
                normalRange: [-5, 10],   // 正常范围
                baseDecayRate: 0.08,     // 基础衰减率
                maxDecayRate: 0.03,      // 最大衰减率
                decayInterval: 300000,   // 调节间隔（5分钟）
                exponentialFactor: 0.9,  // 指数调节因子
                dampingFactor: 0.85,     // 阻尼因子
                adaptiveRate: 0.02       // 自适应调节率
            },

            // 情绪自动调节配置
            emotion: {
                lowThreshold: -25,       // 情绪低于-25开始自动回升（更宽松的阈值）
                highThreshold: 35,       // 情绪高于50开始自动下降（更宽松的阈值）
                targetValue: 0,          // 目标情绪值（保持中性）
                normalRange: [-25, 35],  // 正常范围（扩大范围）
                baseRecoveryRate: 0.10,  // 基础恢复率（进一步降低）
                maxRecoveryRate: 0.04,   // 最大恢复率（进一步降低）
                recoveryInterval: 360000, // 调节间隔（6分钟，减少频率）
                exponentialFactor: 0.90, // 指数调节因子（增加）
                dampingFactor: 0.85,     // 阻尼因子（增加）
                adaptiveRate: 0.012      // 自适应调节率（降低）
            },

            // 好感度自动调节配置
            affinity: {
                lowThreshold: -30,       // 好感度低于-30开始自动回升
                highThreshold: 50,       // 好感度高于40开始自动下降
                targetValue: 5,          // 目标好感度值（调整为轻微正值，更自然）
                normalRange: [-30, 50],  // 正常范围（调整为-30到40）
                baseRecoveryRate: 0.08,  // 基础恢复率（进一步降低）
                maxRecoveryRate: 0.03,   // 最大恢复率（进一步降低）
                recoveryInterval: 720000, // 调节间隔（12分钟，减少频率）
                exponentialFactor: 0.87, // 指数调节因子（增加）
                dampingFactor: 0.83,     // 阻尼因子（增加）
                adaptiveRate: 0.008      // 自适应调节率（降低）
            }
        };
        
        // 定时器存储
        this.timers = new Map();
        
        // 线性回归模型参数
        this.regressionModels = {
            stress: { slope: -0.1, intercept: 0, r2: 0 },
            emotion: { slope: 0.15, intercept: 0, r2: 0 },
            affinity: { slope: 0.12, intercept: 0, r2: 0 }
        };
    }

    /**
     * 初始化服务
     */
    async initialize() {
        try {
            // 自动调节服务不依赖OpenAI服务，使用本地算法
            this.isInitialized = true;
            this.logger.success('自动调节算法', '✅ 自动调节算法服务初始化成功（本地算法模式）');

            // 注意：不在初始化时启动自动调节，由外部控制启动时机

            return { success: true };

        } catch (error) {
            this.logger.error('自动调节算法', '初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 设置数据库连接
     */
    setDatabase(db) {
        this.db = db;
        // 为sqlite3数据库创建promisify包装器
        const { promisify } = require('util');
        this.dbRun = promisify(this.db.run.bind(this.db));
        this.dbGet = promisify(this.db.get.bind(this.db));
        this.dbAll = promisify(this.db.all.bind(this.db));
    }

    /**
     * 启动自动调节系统
     */
    startAutoRegulation() {
        this.logger.info('自动调节算法', '启动自动调节系统');
        
        // 启动压力自动缓解
        this.startStressRegulation();
        
        // 启动情绪自动回升
        this.startEmotionRegulation();
        
        // 启动好感度自动回升
        this.startAffinityRegulation();
        
        // 启动线性回归模型更新
        this.startRegressionModelUpdate();
    }

    /**
     * 停止自动调节系统
     */
    stopAutoRegulation() {
        this.logger.info('自动调节算法', '停止自动调节系统');

        // 清除所有定时器
        for (const [name, timer] of this.timers) {
            clearInterval(timer);
            this.logger.info('自动调节算法', `${name}调节定时器已停止`);
        }

        this.timers.clear();
        this.logger.success('自动调节算法', '自动调节系统已停止');
    }

    /**
     * 启动压力自动缓解
     */
    startStressRegulation() {
        const timer = setInterval(async () => {
            try {
                await this.regulateStressLevels();
            } catch (error) {
                this.logger.error('压力调节', `压力自动调节失败: ${error.message}`);
            }
        }, this.regulationConfig.stress.decayInterval);
        
        this.timers.set('stress', timer);
        this.logger.info('自动调节算法', '压力自动缓解系统已启动');
    }

    /**
     * 启动情绪自动回升
     */
    startEmotionRegulation() {
        const timer = setInterval(async () => {
            try {
                await this.regulateEmotionLevels();
            } catch (error) {
                this.logger.error('情绪调节', `情绪自动调节失败: ${error.message}`);
            }
        }, this.regulationConfig.emotion.recoveryInterval);
        
        this.timers.set('emotion', timer);
        this.logger.info('自动调节算法', '情绪自动回升系统已启动');
    }

    /**
     * 启动好感度自动回升
     */
    startAffinityRegulation() {
        const timer = setInterval(async () => {
            try {
                await this.regulateAffinityLevels();
            } catch (error) {
                this.logger.error('好感度调节', `好感度自动调节失败: ${error.message}`);
            }
        }, this.regulationConfig.affinity.recoveryInterval);
        
        this.timers.set('affinity', timer);
        this.logger.info('自动调节算法', '好感度自动回升系统已启动');
    }

    /**
     * 启动线性回归模型更新
     */
    startRegressionModelUpdate() {
        const timer = setInterval(async () => {
            try {
                await this.updateRegressionModels();
            } catch (error) {
                this.logger.error('回归模型', `线性回归模型更新失败: ${error.message}`);
            }
        }, 1800000); // 30分钟更新一次
        
        this.timers.set('regression', timer);
        this.logger.info('自动调节算法', '线性回归模型更新系统已启动');
    }

    /**
     * 调节压力水平 - 双向调节机制
     */
    async regulateStressLevels() {
        if (!this.db || !this.dbAll) return;

        try {
            // 获取所有需要调节的压力状态（包括过高和过低）
            const stressStates = await this.dbAll(`
                SELECT user_id, persona_name as assistant_name, stress_value, timestamp as last_updated
                FROM ai_stress_states
                WHERE stress_value > ? OR stress_value < ?
            `, [this.regulationConfig.stress.highThreshold, this.regulationConfig.stress.lowThreshold]);

            this.logger.debug('压力调节',
                `查询阈值: 高于${this.regulationConfig.stress.highThreshold} 或 低于${this.regulationConfig.stress.lowThreshold}`
            );
            this.logger.debug('压力调节', `找到 ${stressStates ? stressStates.length : 0} 个需要调节的压力状态`);

            // 确保stressStates是数组
            if (!Array.isArray(stressStates)) {
                this.logger.warning('压力调节', 'stressStates不是数组，跳过调节');
                return;
            }

            if (stressStates.length === 0) {
                this.logger.debug('压力调节', '没有需要调节的压力状态');
                return;
            }

            for (const state of stressStates) {
                const timeSinceUpdate = Date.now() - new Date(state.last_updated).getTime();

                // 计算新的压力值（双向调节）
                const newStressValue = this.calculateStressRegulation(
                    state.stress_value,
                    timeSinceUpdate,
                    this.regulationConfig.stress
                );

                const stressChange = newStressValue - state.stress_value;
                this.logger.debug('压力调节',
                    `用户 ${state.user_id}: 当前值=${state.stress_value.toFixed(3)}, ` +
                    `新值=${newStressValue.toFixed(3)}, 变化=${stressChange.toFixed(3)}, ` +
                    `时间=${(timeSinceUpdate/1000).toFixed(1)}秒`
                );

                // 更新数据库 - 降低更新阈值，使小变化也能被记录
                if (Math.abs(newStressValue - state.stress_value) > 0.01) {
                    await this.dbRun(`
                        UPDATE ai_stress_states
                        SET stress_value = ?, timestamp = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND persona_name = ?
                    `, [newStressValue, state.user_id, state.assistant_name]);

                    const direction = newStressValue > state.stress_value ? '增加' : '减少';
                    this.logger.info('压力调节',
                        `用户 ${state.user_id} (${state.assistant_name}) 压力值${direction}: ${state.stress_value.toFixed(2)} → ${newStressValue.toFixed(2)}`
                    );
                }
            }

        } catch (error) {
            this.logger.error('压力调节', `压力调节失败: ${error.message}`);
        }
    }

    /**
     * 调节情绪水平 - 双向调节机制（同时调节两个表）
     */
    async regulateEmotionLevels() {
        if (!this.db || !this.dbAll) return;

        try {
            // 1. 调节user_affinity表中的emotion_valence
            await this.regulateUserAffinityEmotions();

            // 2. 调节ai_emotion_states表中的emotion_value
            await this.regulateAIEmotionStates();

        } catch (error) {
            this.logger.error('情绪调节', `情绪调节失败: ${error.message}`);
        }
    }

    /**
     * 调节user_affinity表中的emotion_valence
     */
    async regulateUserAffinityEmotions() {
        try {
            // 获取所有需要调节的情绪状态（包括过低和过高）
            const emotionStates = await this.dbAll(`
                SELECT user_id, persona_name as assistant_name, emotion_valence as emotion_value, last_interaction as last_updated
                FROM user_affinity
                WHERE emotion_valence < ? OR emotion_valence > ?
            `, [this.regulationConfig.emotion.lowThreshold, this.regulationConfig.emotion.highThreshold]);

            this.logger.debug('用户情绪调节',
                `查询阈值: 低于${this.regulationConfig.emotion.lowThreshold} 或 高于${this.regulationConfig.emotion.highThreshold}`
            );
            this.logger.debug('用户情绪调节', `找到 ${emotionStates ? emotionStates.length : 0} 个需要调节的用户情绪状态`);

            // 确保emotionStates是数组
            if (!Array.isArray(emotionStates)) {
                this.logger.warning('用户情绪调节', 'emotionStates不是数组，跳过调节');
                return;
            }

            if (emotionStates.length === 0) {
                this.logger.debug('用户情绪调节', '没有需要调节的用户情绪状态');
                return;
            }

            for (const state of emotionStates) {
                const timeSinceUpdate = Date.now() - new Date(state.last_updated).getTime();

                // 计算新的情绪值（双向调节）
                const newEmotionValue = this.calculateEmotionRegulation(
                    state.emotion_value,
                    timeSinceUpdate,
                    this.regulationConfig.emotion
                );

                const emotionChange = newEmotionValue - state.emotion_value;
                this.logger.debug('用户情绪调节',
                    `用户 ${state.user_id}: 当前值=${state.emotion_value.toFixed(3)}, ` +
                    `新值=${newEmotionValue.toFixed(3)}, 变化=${emotionChange.toFixed(3)}, ` +
                    `时间=${(timeSinceUpdate/1000).toFixed(1)}秒`
                );

                // 更新数据库 - 降低更新阈值
                if (Math.abs(newEmotionValue - state.emotion_value) > 0.01) {
                    await this.dbRun(`
                        UPDATE user_affinity
                        SET emotion_valence = ?, last_interaction = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND persona_name = ?
                    `, [newEmotionValue, state.user_id, state.assistant_name]);

                    const direction = newEmotionValue > state.emotion_value ? '提升' : '降低';
                    this.logger.info('用户情绪调节',
                        `用户 ${state.user_id} (${state.assistant_name}) 用户情绪值${direction}: ${state.emotion_value.toFixed(2)} → ${newEmotionValue.toFixed(2)}`
                    );
                }
            }

        } catch (error) {
            this.logger.error('用户情绪调节', `用户情绪调节失败: ${error.message}`);
        }
    }

    /**
     * 调节ai_emotion_states表中的emotion_value
     */
    async regulateAIEmotionStates() {
        try {
            // 获取所有需要调节的AI情绪状态（包括过低和过高）- 简化版查询
            const aiEmotionStates = await this.dbAll(`
                SELECT user_id, persona_name as assistant_name, emotion_value, timestamp as last_updated
                FROM ai_emotion_states
                WHERE emotion_value < ? OR emotion_value > ?
            `, [this.regulationConfig.emotion.lowThreshold, this.regulationConfig.emotion.highThreshold]);

            this.logger.debug('AI情绪调节',
                `查询阈值: 低于${this.regulationConfig.emotion.lowThreshold} 或 高于${this.regulationConfig.emotion.highThreshold}`
            );
            this.logger.debug('AI情绪调节', `找到 ${aiEmotionStates ? aiEmotionStates.length : 0} 个需要调节的AI情绪状态`);

            // 确保aiEmotionStates是数组
            if (!Array.isArray(aiEmotionStates)) {
                this.logger.warning('AI情绪调节', 'aiEmotionStates不是数组，跳过调节');
                return;
            }

            if (aiEmotionStates.length === 0) {
                this.logger.debug('AI情绪调节', '没有需要调节的AI情绪状态');
                return;
            }

            for (const state of aiEmotionStates) {
                const timeSinceUpdate = Date.now() - new Date(state.last_updated).getTime();

                // 计算新的情绪值（双向调节）
                const newEmotionValue = this.calculateEmotionRegulation(
                    state.emotion_value,
                    timeSinceUpdate,
                    this.regulationConfig.emotion
                );

                const emotionChange = newEmotionValue - state.emotion_value;
                this.logger.debug('AI情绪调节',
                    `用户 ${state.user_id}: AI当前值=${state.emotion_value.toFixed(3)}, ` +
                    `新值=${newEmotionValue.toFixed(3)}, 变化=${emotionChange.toFixed(3)}, ` +
                    `时间=${(timeSinceUpdate/1000).toFixed(1)}秒`
                );

                // 更新数据库 - 降低更新阈值
                if (Math.abs(newEmotionValue - state.emotion_value) > 0.01) {
                    await this.dbRun(`
                        UPDATE ai_emotion_states
                        SET emotion_value = ?, timestamp = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND persona_name = ?
                    `, [newEmotionValue, state.user_id, state.assistant_name]);

                    const direction = newEmotionValue > state.emotion_value ? '提升' : '降低';
                    this.logger.info('AI情绪调节',
                        `用户 ${state.user_id} (${state.assistant_name}) AI情绪值${direction}: ${state.emotion_value.toFixed(2)} → ${newEmotionValue.toFixed(2)}`
                    );
                }
            }

        } catch (error) {
            this.logger.error('AI情绪调节', `AI情绪调节失败: ${error.message}`);
        }
    }

    /**
     * 调节好感度水平 - 双向调节机制
     */
    async regulateAffinityLevels() {
        if (!this.db || !this.dbAll) return;

        try {
            // 获取所有需要调节的好感度状态（包括过低和过高）
            const affinityStates = await this.dbAll(`
                SELECT user_id, persona_name as assistant_name, current_affinity as affinity_value, last_interaction as last_updated
                FROM user_affinity
                WHERE current_affinity < ? OR current_affinity > ?
            `, [this.regulationConfig.affinity.lowThreshold, this.regulationConfig.affinity.highThreshold]);

            // 确保affinityStates是数组
            if (!Array.isArray(affinityStates)) {
                this.logger.warning('好感度调节', 'affinityStates不是数组，跳过调节');
                return;
            }

            for (const state of affinityStates) {
                const timeSinceUpdate = Date.now() - new Date(state.last_updated).getTime();

                // 计算新的好感度值（双向调节）
                const newAffinityValue = this.calculateAffinityRegulation(
                    state.affinity_value,
                    timeSinceUpdate,
                    this.regulationConfig.affinity
                );

                // 更新数据库 - 降低更新阈值
                if (Math.abs(newAffinityValue - state.affinity_value) > 0.01) {
                    await this.dbRun(`
                        UPDATE user_affinity
                        SET current_affinity = ?, last_interaction = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND persona_name = ?
                    `, [newAffinityValue, state.user_id, state.assistant_name]);

                    const direction = newAffinityValue > state.affinity_value ? '提升' : '降低';
                    this.logger.info('好感度调节',
                        `用户 ${state.user_id} (${state.assistant_name}) 好感度${direction}: ${state.affinity_value.toFixed(2)} → ${newAffinityValue.toFixed(2)}`
                    );
                }
            }
            
        } catch (error) {
            this.logger.error('好感度调节', `好感度调节失败: ${error.message}`);
        }
    }

    /**
     * 计算压力衰减值 - 使用指数衰减算法
     */
    calculateStressDecay(currentValue, timeSinceUpdate, config) {
        if (currentValue <= config.threshold) return currentValue;

        // 时间因子（小时）
        const timeHours = timeSinceUpdate / (1000 * 60 * 60);

        // 计算衰减率：压力越高，衰减越慢
        const stressRatio = Math.min(currentValue / 50, 1); // 标准化到0-1
        const decayRate = config.baseDecayRate * (1 - stressRatio * config.exponentialFactor);

        // 指数衰减公式：V(t) = V0 * e^(-λt) + target * (1 - e^(-λt))
        const lambda = decayRate;
        const exponentialFactor = Math.exp(-lambda * timeHours);
        const newValue = currentValue * exponentialFactor + config.targetValue * (1 - exponentialFactor);

        // 确保不会低于目标值
        return Math.max(newValue, config.targetValue);
    }

    /**
     * 计算情绪恢复值 - 使用对数恢复算法
     */
    calculateEmotionRecovery(currentValue, timeSinceUpdate, config) {
        if (currentValue >= config.threshold) return currentValue;

        // 时间因子（小时）
        const timeHours = timeSinceUpdate / (1000 * 60 * 60);

        // 计算恢复率：情绪越低，恢复越慢
        const emotionDepth = Math.abs(Math.min(currentValue, -100) / 100); // 标准化到0-1
        const recoveryRate = config.baseRecoveryRate * (1 - emotionDepth * config.exponentialFactor);

        // 对数恢复公式：V(t) = target - (target - V0) * e^(-λt)
        const lambda = recoveryRate;
        const exponentialFactor = Math.exp(-lambda * timeHours);
        const newValue = config.targetValue - (config.targetValue - currentValue) * exponentialFactor;

        // 确保不会超过目标值
        return Math.min(newValue, config.targetValue);
    }

    /**
     * 计算好感度恢复值 - 使用线性回归预测算法
     */
    calculateAffinityRecovery(currentValue, timeSinceUpdate, config) {
        if (currentValue >= config.threshold) return currentValue;

        // 时间因子（小时）
        const timeHours = timeSinceUpdate / (1000 * 60 * 60);

        // 计算恢复率：好感度越低，恢复越慢
        const affinityDepth = Math.abs(Math.min(currentValue, -100) / 100); // 标准化到0-1
        const recoveryRate = config.baseRecoveryRate * (1 - affinityDepth * config.exponentialFactor);

        // 使用线性回归模型预测恢复
        const model = this.regressionModels.affinity;
        const predictedRecovery = model.slope * timeHours + model.intercept;

        // 结合指数恢复和线性预测
        const lambda = recoveryRate;
        const exponentialFactor = Math.exp(-lambda * timeHours);
        const exponentialRecovery = config.targetValue - (config.targetValue - currentValue) * exponentialFactor;

        // 加权平均（70%指数恢复 + 30%线性预测）
        const newValue = exponentialRecovery * 0.7 + (currentValue + predictedRecovery) * 0.3;

        // 确保不会超过目标值
        return Math.min(newValue, config.targetValue);
    }

    /**
     * 计算压力双向调节值 - 重新设计的指数函数调节算法
     */
    calculateStressRegulation(currentValue, timeSinceUpdate, config) {
        // 检查是否在正常范围内
        if (currentValue >= config.normalRange[0] && currentValue <= config.normalRange[1]) {
            return currentValue;
        }

        // 🚀 优化的时间因子：使用分钟而不是小时，并设置最小时间
        const timeMinutes = Math.max(timeSinceUpdate / (1000 * 60), 0.1); // 最少0.1分钟

        // 计算与目标值的距离和方向
        const distanceFromTarget = Math.abs(currentValue - config.targetValue);
        const direction = currentValue > config.targetValue ? -1 : 1;

        // 🚀 基于距离的指数调节强度
        // 距离越远，调节越强；距离越近，调节越温和
        const maxDistance = 100; // 假设最大可能距离
        const normalizedDistance = Math.min(distanceFromTarget / maxDistance, 1);

        // 🚀 指数强度函数：距离远时调节强，距离近时调节弱
        const intensityFactor = 1 - Math.exp(-normalizedDistance * 3); // 3是调节敏感度

        // 🚀 基础调节率增强 - 大幅提升调节强度
        const enhancedBaseRate = config.baseDecayRate * 50; // 增强50倍，使变化更明显

        // 🚀 时间衰减因子：时间越长，调节越多
        const timeDecayFactor = 1 - Math.exp(-timeMinutes * 0.2); // 提升时间敏感度

        // 🚀 计算最终调节量
        const baseAdjustment = distanceFromTarget * enhancedBaseRate * intensityFactor * timeDecayFactor;

        // 🚀 应用方向和限制
        let adjustmentAmount = baseAdjustment * direction;

        // 🚀 限制单次调节的最大幅度，防止震荡 - 提升调节幅度
        const maxSingleAdjustment = Math.min(distanceFromTarget * 0.5, 10); // 最多调节50%或10个单位
        adjustmentAmount = Math.max(-maxSingleAdjustment, Math.min(maxSingleAdjustment, adjustmentAmount));

        const newValue = currentValue + adjustmentAmount;

        // 🚀 确保不会越过目标值
        if (direction > 0) {
            return Math.min(newValue, config.targetValue);
        } else {
            return Math.max(newValue, config.targetValue);
        }
    }

    /**
     * 计算情绪双向调节值 - 重新设计的指数函数调节算法
     */
    calculateEmotionRegulation(currentValue, timeSinceUpdate, config) {
        // 检查是否在正常范围内
        if (currentValue >= config.normalRange[0] && currentValue <= config.normalRange[1]) {
            return currentValue;
        }

        // 🚀 优化的时间因子：使用分钟而不是小时
        const timeMinutes = Math.max(timeSinceUpdate / (1000 * 60), 0.1); // 最少0.1分钟

        // 计算与目标值的距离和方向
        const distanceFromTarget = Math.abs(currentValue - config.targetValue);
        const direction = currentValue > config.targetValue ? -1 : 1;

        // 🚀 基于距离的指数调节强度
        const maxDistance = 100; // 假设最大可能距离
        const normalizedDistance = Math.min(distanceFromTarget / maxDistance, 1);

        // 🚀 指数强度函数：距离远时调节强，距离近时调节弱
        const intensityFactor = 1 - Math.exp(-normalizedDistance * 2.5); // 2.5是调节敏感度

        // 🚀 情绪特殊处理：极端负面情绪需要更强的恢复
        let emotionBoost = 1;
        if (currentValue < -50) {
            emotionBoost = 1.5; // 极端负面情绪增强恢复
        } else if (currentValue > 60) {
            emotionBoost = 1.3; // 极端正面情绪适度调节
        }

        // 🚀 基础调节率增强 - 大幅提升情绪调节强度
        const enhancedBaseRate = config.baseRecoveryRate * 30 * emotionBoost; // 增强30倍并应用情绪加成

        // 🚀 时间衰减因子：时间越长，调节越多
        const timeDecayFactor = 1 - Math.exp(-timeMinutes * 0.15); // 提升时间敏感度

        // 🚀 计算最终调节量
        const baseAdjustment = distanceFromTarget * enhancedBaseRate * intensityFactor * timeDecayFactor;

        // 🚀 应用方向和限制
        let adjustmentAmount = baseAdjustment * direction;

        // 🚀 限制单次调节的最大幅度 - 提升调节幅度
        const maxSingleAdjustment = Math.min(distanceFromTarget * 0.4, 15); // 最多调节40%或15个单位
        adjustmentAmount = Math.max(-maxSingleAdjustment, Math.min(maxSingleAdjustment, adjustmentAmount));

        const newValue = currentValue + adjustmentAmount;

        // 🚀 确保不会越过目标值
        if (direction > 0) {
            return Math.min(newValue, config.targetValue);
        } else {
            return Math.max(newValue, config.targetValue);
        }
    }

    /**
     * 计算好感度双向调节值 - 重新设计的指数函数调节算法
     */
    calculateAffinityRegulation(currentValue, timeSinceUpdate, config) {
        // 检查是否在正常范围内
        if (currentValue >= config.normalRange[0] && currentValue <= config.normalRange[1]) {
            return currentValue;
        }

        // 🚀 优化的时间因子：使用分钟而不是小时
        const timeMinutes = Math.max(timeSinceUpdate / (1000 * 60), 0.1); // 最少0.1分钟

        // 计算与目标值的距离和方向
        const distanceFromTarget = Math.abs(currentValue - config.targetValue);
        const direction = currentValue > config.targetValue ? -1 : 1;

        // 🚀 基于距离的指数调节强度
        const maxDistance = 100; // 假设最大可能距离
        const normalizedDistance = Math.min(distanceFromTarget / maxDistance, 1);

        // 🚀 指数强度函数：距离远时调节强，距离近时调节弱
        const intensityFactor = 1 - Math.exp(-normalizedDistance * 2); // 2是调节敏感度

        // 🚀 好感度特殊处理：负面好感度需要更强的恢复
        let affinityBoost = 1;
        if (currentValue < -40) {
            affinityBoost = 2.0; // 极端负面好感度强力恢复
        } else if (currentValue < -10) {
            affinityBoost = 1.5; // 中等负面好感度增强恢复
        } else if (currentValue > 80) {
            affinityBoost = 0.8; // 极高好感度适度调节
        }

        // 🚀 基础调节率增强 - 保持好感度调节强度适中
        const enhancedBaseRate = config.baseRecoveryRate * 15 * affinityBoost; // 增强15倍并应用好感度加成

        // 🚀 时间衰减因子：时间越长，调节越多
        const timeDecayFactor = 1 - Math.exp(-timeMinutes * 0.1); // 提升时间敏感度

        // 🚀 计算最终调节量
        const baseAdjustment = distanceFromTarget * enhancedBaseRate * intensityFactor * timeDecayFactor;

        // 🚀 应用方向和限制
        let adjustmentAmount = baseAdjustment * direction;

        // 🚀 限制单次调节的最大幅度 - 提升调节幅度
        const maxSingleAdjustment = Math.min(distanceFromTarget * 0.3, 8); // 最多调节30%或8个单位
        adjustmentAmount = Math.max(-maxSingleAdjustment, Math.min(maxSingleAdjustment, adjustmentAmount));

        const newValue = currentValue + adjustmentAmount;

        // 🚀 确保不会越过目标值
        if (direction > 0) {
            return Math.min(newValue, config.targetValue);
        } else {
            return Math.max(newValue, config.targetValue);
        }
    }

    /**
     * 更新线性回归模型
     */
    async updateRegressionModels() {
        if (!this.db) return;

        try {
            // 更新压力回归模型
            await this.updateStressRegressionModel();

            // 更新情绪回归模型
            await this.updateEmotionRegressionModel();

            // 更新好感度回归模型
            await this.updateAffinityRegressionModel();

            this.logger.info('回归模型', '线性回归模型更新完成');

        } catch (error) {
            this.logger.error('回归模型', `回归模型更新失败: ${error.message}`);
        }
    }

    /**
     * 更新压力回归模型
     */
    async updateStressRegressionModel() {
        try {
            // 获取最近30天的压力数据
            const stressData = await this.dbAll(`
                SELECT stress_value,
                       (julianday('now') - julianday(timestamp)) * 24 as hours_ago
                FROM ai_stress_states
                WHERE timestamp > datetime('now', '-30 days')
                  AND stress_value > 0
                ORDER BY timestamp DESC
                LIMIT 1000
            `);

            if (stressData.length > 10) {
                const model = this.calculateLinearRegression(
                    stressData.map(d => d.hours_ago),
                    stressData.map(d => d.stress_value)
                );

                this.regressionModels.stress = model;
                this.logger.info('回归模型', `压力回归模型更新: slope=${model.slope.toFixed(4)}, r²=${model.r2.toFixed(4)}`);
            }

        } catch (error) {
            this.logger.error('回归模型', `压力回归模型更新失败: ${error.message}`);
        }
    }

    /**
     * 更新情绪回归模型
     */
    async updateEmotionRegressionModel() {
        try {
            // 获取最近30天的情绪数据 (使用user_affinity表的emotion_valence字段)
            const emotionData = await this.dbAll(`
                SELECT emotion_valence as emotion_value,
                       (julianday('now') - julianday(last_interaction)) * 24 as hours_ago
                FROM user_affinity
                WHERE last_interaction > datetime('now', '-30 days')
                  AND emotion_valence < 0
                ORDER BY last_interaction DESC
                LIMIT 1000
            `);

            if (emotionData.length > 10) {
                const model = this.calculateLinearRegression(
                    emotionData.map(d => d.hours_ago),
                    emotionData.map(d => d.emotion_value)
                );

                this.regressionModels.emotion = model;
                this.logger.info('回归模型', `情绪回归模型更新: slope=${model.slope.toFixed(4)}, r²=${model.r2.toFixed(4)}`);
            }

        } catch (error) {
            this.logger.error('回归模型', `情绪回归模型更新失败: ${error.message}`);
        }
    }

    /**
     * 更新好感度回归模型
     */
    async updateAffinityRegressionModel() {
        try {
            // 获取最近30天的好感度数据
            const affinityData = await this.dbAll(`
                SELECT current_affinity as affinity_value,
                       (julianday('now') - julianday(last_interaction)) * 24 as hours_ago
                FROM user_affinity
                WHERE last_interaction > datetime('now', '-30 days')
                  AND current_affinity < 0
                ORDER BY last_interaction DESC
                LIMIT 1000
            `);

            if (affinityData.length > 10) {
                const model = this.calculateLinearRegression(
                    affinityData.map(d => d.hours_ago),
                    affinityData.map(d => d.affinity_value)
                );

                this.regressionModels.affinity = model;
                this.logger.info('回归模型', `好感度回归模型更新: slope=${model.slope.toFixed(4)}, r²=${model.r2.toFixed(4)}`);
            }

        } catch (error) {
            this.logger.error('回归模型', `好感度回归模型更新失败: ${error.message}`);
        }
    }

    /**
     * 计算线性回归模型
     */
    calculateLinearRegression(xValues, yValues) {
        const n = xValues.length;
        if (n < 2) return { slope: 0, intercept: 0, r2: 0 };

        // 计算均值
        const xMean = xValues.reduce((sum, x) => sum + x, 0) / n;
        const yMean = yValues.reduce((sum, y) => sum + y, 0) / n;

        // 计算斜率和截距
        let numerator = 0;
        let denominator = 0;
        let totalSumSquares = 0;

        for (let i = 0; i < n; i++) {
            const xDiff = xValues[i] - xMean;
            const yDiff = yValues[i] - yMean;

            numerator += xDiff * yDiff;
            denominator += xDiff * xDiff;
            totalSumSquares += yDiff * yDiff;
        }

        const slope = denominator !== 0 ? numerator / denominator : 0;
        const intercept = yMean - slope * xMean;

        // 计算R²
        let residualSumSquares = 0;
        for (let i = 0; i < n; i++) {
            const predicted = slope * xValues[i] + intercept;
            const residual = yValues[i] - predicted;
            residualSumSquares += residual * residual;
        }

        const r2 = totalSumSquares !== 0 ? 1 - (residualSumSquares / totalSumSquares) : 0;

        return {
            slope: slope,
            intercept: intercept,
            r2: Math.max(0, r2) // 确保R²不为负
        };
    }

    /**
     * 获取调节统计信息
     */
    getRegulationStats() {
        return {
            isActive: this.isInitialized,
            timersCount: this.timers.size,
            config: this.regulationConfig,
            regressionModels: this.regressionModels,
            lastUpdate: new Date().toISOString()
        };
    }

    /**
     * 执行一次完整的调节
     */
    async performRegulation() {
        return await this.manualRegulation('all');
    }

    /**
     * 手动触发调节
     */
    async manualRegulation(type = 'all') {
        try {
            switch (type) {
                case 'stress':
                    await this.regulateStressLevels();
                    break;
                case 'emotion':
                    await this.regulateEmotionLevels();
                    break;
                case 'affinity':
                    await this.regulateAffinityLevels();
                    break;
                case 'all':
                default:
                    await this.regulateStressLevels();
                    await this.regulateEmotionLevels();
                    await this.regulateAffinityLevels();
                    break;
            }

            this.logger.info('手动调节', `手动调节完成: ${type}`);
            return { success: true, type };

        } catch (error) {
            this.logger.error('手动调节', `手动调节失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 更新调节配置
     */
    updateConfig(newConfig) {
        try {
            // 深度合并配置
            this.regulationConfig = this.deepMerge(this.regulationConfig, newConfig);
            this.logger.info('配置更新', '自动调节配置已更新');
            return { success: true };

        } catch (error) {
            this.logger.error('配置更新', `配置更新失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 深度合并对象
     */
    deepMerge(target, source) {
        const result = { ...target };

        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }

        return result;
    }

    /**
     * 关闭自动调节服务
     */
    shutdown() {
        try {
            // 清除所有定时器
            for (const [name, timer] of this.timers) {
                clearInterval(timer);
                this.logger.info('自动调节算法', `已停止${name}调节定时器`);
            }

            this.timers.clear();
            this.isInitialized = false;

            this.logger.info('自动调节算法', '自动调节服务已关闭');
            return { success: true };

        } catch (error) {
            this.logger.error('自动调节算法', `关闭服务失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
}

module.exports = AutoRegulationService;
