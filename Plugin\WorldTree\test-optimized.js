/**
 * 世界树VCP插件优化后测试脚本
 * 测试科学算法和无emoji的system消息格式
 */

const WorldTreeVCP = require('./WorldTreeVCP.js');

// 模拟日志记录器
const mockLogger = {
    info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
    error: (tag, ...args) => console.error(`[ERROR] [${tag}]`, ...args),
    warning: (tag, ...args) => console.warn(`[WARNING] [${tag}]`, ...args),
    debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args),
    success: (tag, ...args) => console.log(`[SUCCESS] [${tag}]`, ...args),
    system: (...args) => console.log(`[SYSTEM]`, ...args)
};

async function testOptimizedFeatures() {
    console.log('🔬 测试优化后的世界树VCP插件功能...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 初始化插件
        console.log('1. 初始化插件...');
        worldTreeVCP = new WorldTreeVCP();
        const initResult = await worldTreeVCP.initialize(mockLogger);
        
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        console.log('✅ 插件初始化成功\n');
        
        // 2. 创建测试配置
        console.log('2. 创建测试世界树配置...');
        const testConfig = {
            worldBackground: '你是一位专业的AI研究员，专注于大语言模型和智能体技术的研究。你在一家前沿的AI实验室工作，每天都在探索人工智能的边界。',
            timeArchitecture: {
                morning: '早晨是你最有创造力的时候，大脑清醒，适合进行复杂的算法设计和理论研究。',
                afternoon: '下午时分，你通常会进行实验验证和数据分析，与团队成员讨论研究进展。',
                evening: '傍晚时光，你会整理一天的研究成果，撰写论文或技术文档。',
                night: '夜深人静时，你喜欢阅读最新的研究论文，思考未来的研究方向。'
            },
            characterSchedules: {
                '09:00-12:00': '深度学习模型训练和优化',
                '14:00-17:00': '实验数据分析和结果验证',
                '19:00-22:00': '论文撰写和技术文档整理'
            },
            narrativeRules: {
                '专业严谨': '对技术问题保持严谨的科学态度，基于事实和数据进行分析',
                '创新思维': '善于从不同角度思考问题，提出创新性的解决方案',
                '团队协作': '重视与同事的合作，乐于分享知识和经验',
                '持续学习': '保持对新技术的敏感度，不断更新知识体系'
            }
        };
        
        const configResult = await worldTreeVCP.createOrUpdateWorldTreeConfig('雨安安', testConfig);
        if (configResult) {
            console.log('✅ 测试配置创建成功\n');
        } else {
            throw new Error('配置创建失败');
        }
        
        // 3. 测试科学的物理状态计算
        console.log('3. 测试科学的物理状态计算...');
        const physicalState = await worldTreeVCP.calculatePsychologyState('test_user', '雨安安', {
            cognitiveLoad: 0.6,
            hasRecentConversation: true
        });
        
        console.log('物理状态计算结果:');
        console.log(`  专注程度: ${physicalState.focus.toFixed(1)}/100`);
        console.log(`  精力水平: ${physicalState.energy.toFixed(1)}/100`);
        console.log(`  饥饿感: ${physicalState.hunger.toFixed(1)}/100`);
        console.log(`  疲劳度: ${physicalState.fatigue.toFixed(1)}/100`);
        console.log(`  警觉性: ${physicalState.alertness.toFixed(1)}/100`);
        console.log('✅ 科学物理状态计算成功\n');
        
        // 4. 测试心理活动生成（基于物理状态）
        console.log('4. 测试基于物理状态的心理活动生成...');
        const psychologyActivity = await worldTreeVCP.generatePsychologyActivity('test_user', '雨安安', {
            hasRecentConversation: true,
            conversationLength: 200
        });
        
        if (psychologyActivity) {
            console.log('生成的心理活动内容:');
            console.log(`"${psychologyActivity.content}"`);
            console.log('✅ 心理活动生成成功\n');
        } else {
            throw new Error('心理活动生成失败');
        }
        
        // 5. 测试优化后的system消息生成（无emoji，放在最前面）
        console.log('5. 测试优化后的system消息生成...');
        const systemMessage = await worldTreeVCP.generateSystemMessage('test_user', '雨安安', '你好，我想了解一些关于AI研究的问题。');
        
        console.log('生成的优化system消息:');
        console.log('═'.repeat(80));
        console.log(systemMessage);
        console.log('═'.repeat(80));
        console.log('✅ 优化system消息生成成功\n');
        
        // 6. 验证消息格式特点
        console.log('6. 验证优化特点...');
        const hasEmoji = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(systemMessage);
        const startsWithWorldTree = systemMessage.startsWith('=== 世界树角色设定与状态信息 ===');
        const hasPhysicalStates = systemMessage.includes('[你的物理状态指标]');
        const hasNoStressEmotion = !systemMessage.includes('压力程度') && !systemMessage.includes('情绪状态');
        
        console.log(`  无emoji: ${!hasEmoji ? '✅' : '❌'}`);
        console.log(`  以世界树开头: ${startsWithWorldTree ? '✅' : '❌'}`);
        console.log(`  包含物理状态: ${hasPhysicalStates ? '✅' : '❌'}`);
        console.log(`  移除压力情绪: ${hasNoStressEmotion ? '✅' : '❌'}`);
        console.log('✅ 优化特点验证完成\n');
        
        // 7. 测试时间因子计算
        console.log('7. 测试科学时间因子计算...');
        const timeFactors = worldTreeVCP.getTimeFactors();
        console.log('时间因子计算结果:');
        console.log(`  当前时间比例: ${timeFactors.timeOfDay.toFixed(3)}`);
        console.log(`  生理节律: ${timeFactors.circadianRhythm.toFixed(3)}`);
        console.log(`  能量修正: ${timeFactors.energyModifier.toFixed(2)}`);
        console.log(`  专注修正: ${timeFactors.focusModifier.toFixed(2)}`);
        console.log('✅ 时间因子计算成功\n');
        
        console.log('🎉 所有优化功能测试通过！');
        console.log('\n📋 优化总结:');
        console.log('• 移除了所有emoji，使用纯文本格式');
        console.log('• 世界树内容放在system消息最前面作为准则');
        console.log('• 使用科学算法计算物理状态（专注、精力、饥饿、疲劳、警觉）');
        console.log('• 移除了硬编码的压力和情绪判断');
        console.log('• 结合对话记录和时间因子进行智能分析');
        console.log('• 基于认知负荷理论、生理节律等科学原理');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error.stack);
    } finally {
        // 清理
        if (worldTreeVCP) {
            try {
                await worldTreeVCP.cleanup();
                console.log('\n🧹 插件清理完成');
            } catch (cleanupError) {
                console.error('清理失败:', cleanupError.message);
            }
        }
    }
}

// 运行测试
if (require.main === module) {
    testOptimizedFeatures().then(() => {
        console.log('\n测试完成，退出程序。');
        process.exit(0);
    }).catch(error => {
        console.error('\n测试过程中发生未捕获的错误:', error);
        process.exit(1);
    });
}

module.exports = { testOptimizedFeatures };
