/**
 * 世界树VCP插件
 * 独立的世界树系统，支持时间架构、角色日程表和心理活动生成
 * 集成现有的AdvancedMemorySystem数据库，使用本地算法生成心理状态
 */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const { promisify } = require('util');
const moment = require('moment');
const natural = require('natural');
const { Matrix } = require('ml-matrix');
const similarity = require('similarity');
const axios = require('axios');
const dotenv = require('dotenv');

class WorldTreeVCP {
    constructor() {
        this.pluginName = 'WorldTreeVCP';
        this.version = '1.0.0';
        this.description = '世界树VCP插件 - 时间架构与角色心理活动系统';
        this.isInitialized = false;
        this.logger = null;

        // 数据库连接
        this.db = null;
        this.dbPath = path.join(__dirname, '../AdvancedMemorySystem/data/emotion.db');

        // 加载配置（优先使用插件专用配置）
        this.config = this.loadPluginConfig();
        
        // 世界树配置存储
        this.worldTreeConfigs = new Map();
        
        // 心理状态算法参数
        this.psychologyAlgorithm = {
            // 基础心理状态权重
            baseWeights: {
                stress: 0.3,
                emotion: 0.25,
                energy: 0.2,
                mood: 0.15,
                focus: 0.1
            },
            
            // 时间因素影响
            timeFactors: {
                morning: { energy: 1.2, focus: 1.1, mood: 1.0 },
                afternoon: { energy: 0.9, focus: 1.0, mood: 1.1 },
                evening: { energy: 0.7, focus: 0.8, mood: 1.2 },
                night: { energy: 0.5, focus: 0.6, mood: 0.9 }
            },
            
            // 任务类型影响
            taskFactors: {
                creative: { energy: 0.8, focus: 1.2, mood: 1.1 },
                analytical: { energy: 1.0, focus: 1.3, mood: 0.9 },
                social: { energy: 1.1, focus: 0.9, mood: 1.3 },
                routine: { energy: 0.9, focus: 1.0, mood: 1.0 }
            }
        };
        
        // 初始化标志
        this.isInitialized = false;
        
        // 日志记录器
        this.logger = null;
    }

    /**
     * 插件初始化
     */
    async initialize(logger) {
        try {
            this.logger = logger || console;

            // 重新加载配置（现在有logger了，可以记录详细信息）
            this.config = this.loadPluginConfig();

            // 记录配置信息
            this.logger.info('世界树VCP', '配置加载完成:', {
                apiUrl: this.config.apiUrl,
                model: this.config.model,
                useLocalAlgorithm: this.config.useLocalAlgorithm,
                hasApiKey: !!this.config.apiKey,
                timeout: this.config.timeout
            });

            // 初始化数据库连接
            await this.initializeDatabase();

            // 创建必要的表结构
            await this.createTables();

            // 启动心理状态更新定时器
            this.startPsychologyUpdateTimer();

            this.isInitialized = true;
            this.logger.info('世界树VCP', '插件初始化成功');

            return true;
        } catch (error) {
            this.logger.error('世界树VCP', '插件初始化失败:', error.message);
            return false;
        }
    }

    /**
     * 初始化数据库连接
     */
    async initializeDatabase() {
        return new Promise((resolve, reject) => {
            // 确保数据库目录存在
            const dbDir = path.dirname(this.dbPath);
            fs.mkdir(dbDir, { recursive: true }).then(() => {
                this.db = new sqlite3.Database(this.dbPath, (err) => {
                    if (err) {
                        reject(new Error(`数据库连接失败: ${err.message}`));
                    } else {
                        // 启用外键约束
                        this.db.run('PRAGMA foreign_keys = ON');
                        resolve();
                    }
                });
            }).catch(reject);
        });
    }

    /**
     * 数据库操作封装
     */
    dbRun(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ lastID: this.lastID, changes: this.changes });
            });
        });
    }

    dbGet(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }

    dbAll(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    /**
     * 创建世界树相关表结构
     */
    async createTables() {
        const tables = [
            // 世界树配置表
            `CREATE TABLE IF NOT EXISTS world_tree_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_name TEXT NOT NULL,
                config_data TEXT NOT NULL,
                time_architecture TEXT,
                character_schedules TEXT,
                world_background TEXT,
                narrative_rules TEXT,
                created_time TEXT NOT NULL,
                updated_time TEXT NOT NULL,
                UNIQUE(agent_name)
            )`,

            // 心理活动记录表
            `CREATE TABLE IF NOT EXISTS psychology_activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                psychology_state TEXT NOT NULL,
                generated_content TEXT NOT NULL,
                context_factors TEXT,
                algorithm_version TEXT DEFAULT 'v1.0',
                created_time TEXT NOT NULL
            )`,

            // 时间事件表
            `CREATE TABLE IF NOT EXISTS time_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_name TEXT NOT NULL,
                event_name TEXT NOT NULL,
                event_type TEXT NOT NULL,
                event_time TEXT NOT NULL,
                event_description TEXT,
                impact_factors TEXT,
                is_active INTEGER DEFAULT 1,
                created_time TEXT NOT NULL,
                FOREIGN KEY (agent_name) REFERENCES world_tree_configs(agent_name)
            )`,

            // 世界树心理状态表（专用JSON存储）
            `CREATE TABLE IF NOT EXISTS worldtree_psychology_states (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                psychology_data TEXT NOT NULL,
                emotion_factors TEXT,
                context_summary TEXT,
                last_updated TEXT NOT NULL,
                created_time TEXT NOT NULL,
                UNIQUE(user_id, agent_name)
            )`
        ];
        
        for (const table of tables) {
            await this.dbRun(table);
        }

        // 创建索引
        const indexes = [
            `CREATE INDEX IF NOT EXISTS idx_psychology_activities_user_agent_time
             ON psychology_activities(user_id, agent_name, created_time)`,
            `CREATE INDEX IF NOT EXISTS idx_time_events_agent_active
             ON time_events(agent_name, is_active)`
        ];

        for (const index of indexes) {
            await this.dbRun(index);
        }
    }

    /**
     * 加载插件配置，优先使用插件自己的config.env
     */
    loadPluginConfig() {
        const pluginConfigPath = path.join(__dirname, 'config.env');

        let pluginConfig = {};

        // 加载插件配置
        try {
            const configContent = fsSync.readFileSync(pluginConfigPath, 'utf8');
            pluginConfig = this.parseEnvConfig(configContent);
            // 如果有logger，记录加载信息
            if (this.logger) {
                this.logger.info('世界树VCP', `从插件config.env加载了 ${Object.keys(pluginConfig).length} 个配置项`);
            }
        } catch (error) {
            // 插件配置不存在时，使用主服务器配置作为备用
            if (this.logger) {
                this.logger.info('世界树VCP', '插件config.env不存在或读取失败，使用主服务器配置作为备用');
            }
        }

        // 加载主服务器配置作为备用
        try {
            dotenv.config({ path: path.resolve(__dirname, '../../config.env') });
        } catch (error) {
            // 主配置文件不存在也没关系，使用默认值
        }

        // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
        const mergedConfig = {
            enabled: this.getConfigValue(pluginConfig.WORLDTREE_ENABLED, 'true').toLowerCase() === 'true',
            apiUrl: this.getConfigValue(
                pluginConfig.WORLDTREE_API_URL,
                process.env.API_URL || process.env.OPENAI_TOOLS_URL,
                'https://yuanplus.cloud'
            ),
            apiKey: this.getConfigValue(
                pluginConfig.WORLDTREE_API_KEY,
                process.env.API_Key || process.env.OPENAI_TOOLS_KEY,
                ''
            ),
            model: this.getConfigValue(
                pluginConfig.WORLDTREE_MODEL,
                process.env.OPENAI_TOOLS_MODEL,
                'gpt-4o-mini'
            ),
            useLocalAlgorithm: this.getConfigValue(
                pluginConfig.WORLDTREE_USE_LOCAL_ALGORITHM,
                'false'
            ).toLowerCase() === 'true',
            timeout: parseInt(this.getConfigValue(
                pluginConfig.WORLDTREE_TIMEOUT,
                '120000'
            )) || 120000,
            psychologyUpdateInterval: parseInt(this.getConfigValue(
                pluginConfig.WORLDTREE_PSYCHOLOGY_UPDATE_INTERVAL,
                '300000'
            )) || 300000,
            timeFormat: this.getConfigValue(
                pluginConfig.WORLDTREE_TIME_FORMAT,
                'local'
            ),
            debugMode: this.getConfigValue(
                pluginConfig.WORLDTREE_DEBUG_MODE,
                'false'
            ).toLowerCase() === 'true',
            logLevel: this.getConfigValue(
                pluginConfig.WORLDTREE_LOG_LEVEL,
                'info'
            )
        };

        return mergedConfig;
    }

    /**
     * 获取配置值，支持优先级
     */
    getConfigValue(pluginValue, mainValue, defaultValue = '') {
        return (pluginValue && pluginValue.trim()) ||
               (mainValue && mainValue.trim()) ||
               defaultValue;
    }

    /**
     * 解析.env格式的配置文件
     */
    parseEnvConfig(content) {
        const config = {};
        content.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const equalIndex = line.indexOf('=');
                if (equalIndex > 0) {
                    const key = line.substring(0, equalIndex).trim();
                    const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                    config[key] = value;
                }
            }
        });
        return config;
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            enabled: true,
            apiUrl: 'https://yuanplus.cloud',
            apiKey: '',
            model: 'gpt-4o-mini',
            useLocalAlgorithm: true,
            timeout: 120000,
            psychologyUpdateInterval: 300000,
            timeFormat: 'local',
            debugMode: false,
            logLevel: 'info'
        };
    }

    /**
     * 启动心理状态更新定时器
     */
    startPsychologyUpdateTimer() {
        if (this.psychologyTimer) {
            clearInterval(this.psychologyTimer);
        }

        this.psychologyTimer = setInterval(async () => {
            try {
                await this.updateAllPsychologyStates();
            } catch (error) {
                this.logger.error('世界树VCP', '心理状态更新失败:', error.message);
            }
        }, this.config.psychologyUpdateInterval);

        this.logger.info('世界树VCP', `心理状态更新定时器已启动，间隔: ${this.config.psychologyUpdateInterval / 1000}秒`);
    }

    /**
     * 获取当前本地时间
     */
    getCurrentLocalTime() {
        const now = new Date();
        return now.toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 获取时间段
     */
    getTimePeriod() {
        const hour = new Date().getHours();
        if (hour >= 6 && hour < 12) return 'morning';
        if (hour >= 12 && hour < 18) return 'afternoon';
        if (hour >= 18 && hour < 22) return 'evening';
        return 'night';
    }

    /**
     * 创建或更新世界树配置
     */
    async createOrUpdateWorldTreeConfig(agentName, configData) {
        try {
            const currentTime = this.getCurrentLocalTime();

            const existingConfig = await this.dbGet(
                'SELECT * FROM world_tree_configs WHERE agent_name = ?',
                [agentName]
            );

            if (existingConfig) {
                // 更新现有配置
                await this.dbRun(`
                    UPDATE world_tree_configs
                    SET config_data = ?,
                        time_architecture = ?,
                        character_schedules = ?,
                        world_background = ?,
                        narrative_rules = ?,
                        updated_time = ?
                    WHERE agent_name = ?
                `, [
                    JSON.stringify(configData.config || {}),
                    JSON.stringify(configData.timeArchitecture || {}),
                    JSON.stringify(configData.characterSchedules || {}),
                    configData.worldBackground || '',
                    JSON.stringify(configData.narrativeRules || {}),
                    currentTime,
                    agentName
                ]);

                this.logger.info('世界树VCP', `更新Agent配置: ${agentName}`);
            } else {
                // 创建新配置
                await this.dbRun(`
                    INSERT INTO world_tree_configs
                    (agent_name, config_data, time_architecture, character_schedules, world_background, narrative_rules, created_time, updated_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    agentName,
                    JSON.stringify(configData.config || {}),
                    JSON.stringify(configData.timeArchitecture || {}),
                    JSON.stringify(configData.characterSchedules || {}),
                    configData.worldBackground || '',
                    JSON.stringify(configData.narrativeRules || {}),
                    currentTime,
                    currentTime
                ]);

                this.logger.info('世界树VCP', `创建Agent配置: ${agentName}`);
            }

            // 更新内存缓存
            this.worldTreeConfigs.set(agentName, configData);

            return true;
        } catch (error) {
            this.logger.error('世界树VCP', `配置保存失败 [${agentName}]:`, error.message);
            return false;
        }
    }

    /**
     * 获取世界树配置
     */
    async getWorldTreeConfig(agentName) {
        try {
            // 先检查内存缓存
            if (this.worldTreeConfigs.has(agentName)) {
                return this.worldTreeConfigs.get(agentName);
            }

            // 从数据库加载
            const config = await this.dbGet(
                'SELECT * FROM world_tree_configs WHERE agent_name = ?',
                [agentName]
            );

            if (config) {
                const configData = {
                    config: JSON.parse(config.config_data || '{}'),
                    timeArchitecture: JSON.parse(config.time_architecture || '{}'),
                    characterSchedules: JSON.parse(config.character_schedules || '{}'),
                    worldBackground: config.world_background || '',
                    narrativeRules: JSON.parse(config.narrative_rules || '{}'),
                    createdTime: config.created_time,
                    updatedTime: config.updated_time
                };

                // 更新内存缓存
                this.worldTreeConfigs.set(agentName, configData);

                return configData;
            }

            return null;
        } catch (error) {
            this.logger.error('世界树VCP', `配置加载失败 [${agentName}]:`, error.message);
            return null;
        }
    }

    /**
     * 生成心理活动内容
     */
    async generatePsychologyActivity(userId, agentName, contextFactors = {}) {
        try {
            // 获取现有的心理状态数据
            const psychologyState = await this.calculatePsychologyState(userId, agentName, contextFactors);

            // 获取世界树配置
            const worldTreeConfig = await this.getWorldTreeConfig(agentName);

            // 生成心理活动内容
            let psychologyContent = '';

            if (this.config.useLocalAlgorithm) {
                // 使用本地算法生成
                psychologyContent = await this.generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors);
            } else {
                // 使用API生成智能心理分析
                psychologyContent = await this.generateIntelligentPsychologyContent(
                    userId,
                    agentName,
                    psychologyState,
                    worldTreeConfig,
                    contextFactors
                );
            }

            // 保存心理活动记录
            await this.savePsychologyActivity(userId, agentName, psychologyState, psychologyContent, contextFactors);

            return {
                psychologyState,
                content: psychologyContent,
                timestamp: this.getCurrentLocalTime()
            };

        } catch (error) {
            this.logger.error('世界树VCP', `心理活动生成失败 [${userId}/${agentName}]:`, error.message);
            return null;
        }
    }

    /**
     * 计算物理状态（使用科学算法）
     */
    async calculatePsychologyState(userId, agentName, contextFactors = {}) {
        try {
            // 获取最近对话记录进行分析
            const recentConversations = await this.getRecentConversations(userId, agentName, 10);

            // 获取时间因子
            const timeFactors = this.getTimeFactors();

            // 使用机器学习矩阵进行状态计算
            const stateMatrix = await this.buildStateMatrix(recentConversations, timeFactors, contextFactors);

            // 计算物理层属性
            const physicalState = this.computePhysicalAttributes(stateMatrix);

            return {
                ...physicalState,
                timePeriod: this.getTimePeriod(),
                contextFactors,
                timestamp: this.getCurrentLocalTime()
            };

        } catch (error) {
            this.logger.error('世界树VCP', '物理状态计算失败:', error.message);
            return {
                focus: 50,
                energy: 50,
                hunger: 30,
                fatigue: 20,
                alertness: 60,
                timePeriod: this.getTimePeriod(),
                contextFactors,
                timestamp: this.getCurrentLocalTime()
            };
        }
    }

    /**
     * 获取情感记忆数据
     */
    async getEmotionMemoryData(userId, agentName) {
        try {
            // 检查表是否存在并获取数据
            const tables = await this.dbAll(`
                SELECT name FROM sqlite_master
                WHERE type='table' AND (name='ai_stress_states' OR name='user_affinity' OR name='emotion_analysis')
            `);

            const existingTables = tables.map(t => t.name);
            let stressValue = 0;
            let emotionValue = 0;
            let moodValue = 0;
            let affinityValue = 50;

            // 尝试从不同的表获取数据
            if (existingTables.includes('ai_stress_states')) {
                const stressData = await this.dbGet(`
                    SELECT stress_value, timestamp
                    FROM ai_stress_states
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY timestamp DESC LIMIT 1
                `, [userId, agentName]);
                stressValue = stressData?.stress_value || 0;
            }

            if (existingTables.includes('emotion_analysis')) {
                const emotionData = await this.dbGet(`
                    SELECT emotion_valence, emotion_arousal, emotion_dominance
                    FROM emotion_analysis
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY timestamp DESC LIMIT 1
                `, [userId, agentName]);

                if (emotionData) {
                    emotionValue = emotionData.emotion_valence || 0;
                    moodValue = emotionData.emotion_arousal || 0;
                }
            }

            return {
                stress: stressValue,
                emotion: emotionValue,
                mood: moodValue,
                affinity: affinityValue,
                dominance: 0
            };

        } catch (error) {
            this.logger.warning('世界树VCP', `情感数据获取失败:`, error.message);
            return { stress: 0, emotion: 0, mood: 0, affinity: 50, dominance: 0 };
        }
    }

    /**
     * 获取最近对话记录
     */
    async getRecentConversations(userId, agentName, limit = 10) {
        try {
            // 尝试从不同的可能表中获取对话记录
            let conversations = [];

            // 首先尝试从recent_conversations表获取
            try {
                conversations = await this.dbAll(`
                    SELECT content, creation_time as timestamp, 'user' as role
                    FROM recent_conversations
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY creation_time DESC LIMIT ?
                `, [userId, agentName, limit]);
            } catch (e1) {
                // 如果失败，尝试从其他可能的表
                try {
                    conversations = await this.dbAll(`
                        SELECT message_content as content, timestamp, 'user' as role
                        FROM conversation_logs
                        WHERE user_id = ? AND agent_name = ?
                        ORDER BY timestamp DESC LIMIT ?
                    `, [userId, agentName, limit]);
                } catch (e2) {
                    this.logger.debug('世界树VCP', '未找到对话记录表，使用空数据');
                }
            }

            return conversations || [];
        } catch (error) {
            this.logger.warning('世界树VCP', '获取对话记录失败:', error.message);
            return [];
        }
    }

    /**
     * 构建状态计算矩阵
     */
    async buildStateMatrix(conversations, timeFactors, contextFactors) {
        // 分析对话内容的复杂度和情感倾向
        const conversationAnalysis = this.analyzeConversations(conversations);

        // 时间衰减函数
        const timeDecay = this.calculateTimeDecay(conversations);

        // 构建特征矩阵
        const features = [
            conversationAnalysis.complexity,      // 对话复杂度
            conversationAnalysis.frequency,       // 对话频率
            conversationAnalysis.length,          // 对话长度
            timeFactors.circadianRhythm,         // 生理节律
            timeFactors.timeOfDay,               // 时间因子
            contextFactors.cognitiveLoad || 0,   // 认知负荷
            timeDecay.average                    // 时间衰减
        ];

        return new Matrix([features]);
    }

    /**
     * 分析对话内容
     */
    analyzeConversations(conversations) {
        if (!conversations || conversations.length === 0) {
            return { complexity: 0.3, frequency: 0.2, length: 0.1 };
        }

        let totalComplexity = 0;
        let totalLength = 0;
        const timeSpan = this.calculateTimeSpan(conversations);

        conversations.forEach(conv => {
            // 使用natural库分析文本复杂度
            const tokens = natural.WordTokenizer.tokenize(conv.content || '');
            const sentences = natural.SentenceTokenizer.tokenize(conv.content || '');

            // 计算复杂度指标
            const avgWordsPerSentence = tokens.length / Math.max(sentences.length, 1);
            const uniqueWords = new Set(tokens).size;
            const lexicalDiversity = uniqueWords / Math.max(tokens.length, 1);

            totalComplexity += (avgWordsPerSentence * 0.3 + lexicalDiversity * 0.7);
            totalLength += (conv.content || '').length;
        });

        return {
            complexity: Math.min(1, totalComplexity / conversations.length / 10),
            frequency: Math.min(1, conversations.length / Math.max(timeSpan, 1)),
            length: Math.min(1, totalLength / conversations.length / 100)
        };
    }

    /**
     * 计算时间跨度（小时）
     */
    calculateTimeSpan(conversations) {
        if (!conversations || conversations.length < 2) return 1;

        const timestamps = conversations.map(c => moment(c.timestamp));
        const earliest = moment.min(timestamps);
        const latest = moment.max(timestamps);

        return Math.max(1, latest.diff(earliest, 'hours'));
    }

    /**
     * 计算时间衰减
     */
    calculateTimeDecay(conversations) {
        if (!conversations || conversations.length === 0) {
            return { average: 0.5, recent: 0.3 };
        }

        const now = moment();
        const decayValues = conversations.map(conv => {
            const timeDiff = now.diff(moment(conv.timestamp), 'hours');
            return Math.exp(-timeDiff / 24); // 24小时半衰期
        });

        return {
            average: decayValues.reduce((a, b) => a + b, 0) / decayValues.length,
            recent: decayValues.slice(0, 3).reduce((a, b) => a + b, 0) / Math.min(3, decayValues.length)
        };
    }

    /**
     * 计算专注水平
     */
    calculateFocusLevel(timePeriod, contextFactors) {
        let baseFocus = 60; // 基础专注水平

        // 时间因素
        const timeMultiplier = this.psychologyAlgorithm.timeFactors[timePeriod].focus;
        baseFocus *= timeMultiplier;

        // 环境干扰因素
        if (contextFactors.distractionLevel) {
            baseFocus *= (1 - contextFactors.distractionLevel * 0.4);
        }

        // 任务复杂度因素
        if (contextFactors.taskComplexity) {
            baseFocus *= (0.8 + contextFactors.taskComplexity * 0.2);
        }

        return this.normalizeValue(baseFocus, 0, 100);
    }

    /**
     * 计算物理属性
     */
    computePhysicalAttributes(stateMatrix) {
        const features = stateMatrix.getRow(0);

        // 使用科学公式计算各项物理指标
        const focus = this.calculateFocus(features);
        const energy = this.calculateEnergy(features);
        const hunger = this.calculateHunger(features);
        const fatigue = this.calculateFatigue(features);
        const alertness = this.calculateAlertness(features);

        return { focus, energy, hunger, fatigue, alertness };
    }

    /**
     * 计算专注度（基于认知负荷理论）
     */
    calculateFocus(features) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad, timeDecay] = features;

        // Yerkes-Dodson定律：适度的刺激提高专注度
        const stimulation = (complexity + frequency) * 0.5;
        const optimalStimulation = 0.6;
        const stimulationEffect = 1 - Math.abs(stimulation - optimalStimulation);

        // 生理节律影响
        const circadianEffect = Math.sin(circadian * Math.PI * 2) * 0.2 + 0.8;

        // 认知负荷影响
        const cognitiveEffect = Math.max(0.2, 1 - cognitiveLoad * 0.8);

        const focus = (stimulationEffect * 0.4 + circadianEffect * 0.3 + cognitiveEffect * 0.3) * 100;
        return Math.max(10, Math.min(100, focus));
    }

    /**
     * 计算精力水平（基于能量消耗模型）
     */
    calculateEnergy(features) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad, timeDecay] = features;

        // 基础能量（基于生理节律）
        const baseEnergy = (Math.sin(circadian * Math.PI * 2 - Math.PI / 2) + 1) * 50;

        // 活动消耗
        const activityCost = (complexity * 15 + frequency * 10 + length * 5);

        // 恢复因子（基于时间衰减）
        const recoveryFactor = timeDecay * 20;

        const energy = baseEnergy - activityCost + recoveryFactor;
        return Math.max(5, Math.min(100, energy));
    }

    /**
     * 计算饥饿感（基于时间和活动强度）
     */
    calculateHunger(features) {
        const [complexity, frequency, length, circadian, timeOfDay] = features;

        // 基于时间的饥饿感（每4小时增加）
        const timeBasedHunger = (timeOfDay % 4) * 25;

        // 活动强度影响代谢
        const metabolicRate = (complexity + frequency + length) * 10;

        const hunger = timeBasedHunger + metabolicRate;
        return Math.max(0, Math.min(100, hunger));
    }

    /**
     * 计算疲劳度（基于累积负荷）
     */
    calculateFatigue(features) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad] = features;

        // 累积疲劳
        const cumulativeFatigue = (complexity * 8 + frequency * 12 + cognitiveLoad * 15);

        // 生理节律的疲劳影响
        const circadianFatigue = (1 - Math.sin(circadian * Math.PI * 2)) * 30;

        const fatigue = cumulativeFatigue + circadianFatigue;
        return Math.max(0, Math.min(100, fatigue));
    }

    /**
     * 计算警觉性（基于注意力资源理论）
     */
    calculateAlertness(features) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad, timeDecay] = features;

        // 基础警觉性（生理节律）
        const baseAlertness = (Math.sin(circadian * Math.PI * 2) + 1) * 40 + 20;

        // 刺激维持警觉性
        const stimulationEffect = Math.min(30, (frequency + complexity) * 20);

        // 疲劳降低警觉性
        const fatigueEffect = -cognitiveLoad * 25;

        // 时间衰减影响
        const timeEffect = timeDecay * 15;

        const alertness = baseAlertness + stimulationEffect + fatigueEffect + timeEffect;
        return Math.max(10, Math.min(100, alertness));
    }

    /**
     * 获取时间因子（科学的生理节律计算）
     */
    getTimeFactors() {
        const now = moment();
        const hour = now.hour();
        const minute = now.minute();

        // 计算一天中的时间比例 (0-1)
        const timeOfDay = (hour + minute / 60) / 24;

        // 计算生理节律（基于人体生物钟）
        // 使用双峰模型：上午10点和下午3点为峰值
        const morningPeak = Math.exp(-Math.pow((hour - 10) / 3, 2));
        const afternoonPeak = Math.exp(-Math.pow((hour - 15) / 3, 2));
        const circadianRhythm = Math.max(morningPeak, afternoonPeak);

        return {
            timeOfDay,
            circadianRhythm,
            hour,
            minute,
            energyModifier: this.calculateEnergyModifier(hour),
            focusModifier: this.calculateFocusModifier(hour),
            stressModifier: this.calculateStressModifier(hour)
        };
    }

    /**
     * 计算能量修正因子
     */
    calculateEnergyModifier(hour) {
        // 基于人体皮质醇分泌规律
        if (hour >= 6 && hour <= 9) return 1.2;   // 早晨皮质醇高峰
        if (hour >= 10 && hour <= 12) return 1.0; // 上午稳定期
        if (hour >= 13 && hour <= 15) return 0.8; // 午后低谷
        if (hour >= 16 && hour <= 18) return 1.1; // 下午回升
        if (hour >= 19 && hour <= 21) return 0.9; // 傍晚下降
        return 0.6; // 夜间低谷
    }

    /**
     * 计算专注修正因子
     */
    calculateFocusModifier(hour) {
        // 基于认知能力的昼夜节律
        if (hour >= 8 && hour <= 11) return 1.3;  // 上午专注高峰
        if (hour >= 14 && hour <= 16) return 1.1; // 下午次高峰
        if (hour >= 17 && hour <= 19) return 0.9; // 傍晚下降
        if (hour >= 20 && hour <= 22) return 0.7; // 晚间低谷
        return 0.5; // 深夜最低
    }

    /**
     * 计算压力修正因子
     */
    calculateStressModifier(hour) {
        // 基于压力激素分泌模式
        if (hour >= 7 && hour <= 9) return 1.2;   // 早晨压力高峰
        if (hour >= 10 && hour <= 16) return 0.8; // 白天相对稳定
        if (hour >= 17 && hour <= 19) return 1.1; // 下班时间压力
        return 0.6; // 其他时间较低
    }

    /**
     * 数值标准化
     */
    normalizeValue(value, min, max) {
        return Math.max(min, Math.min(max, value));
    }

    /**
     * 获取默认心理状态
     */
    getDefaultPsychologyState() {
        return {
            stress: 0,
            emotion: 0,
            energy: 70,
            mood: 0,
            focus: 60,
            overallScore: 65,
            timePeriod: this.getTimePeriod(),
            contextFactors: {},
            timestamp: this.getCurrentLocalTime()
        };
    }

    /**
     * 使用本地算法生成心理活动内容
     */
    async generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors) {
        try {
            const { focus, energy, hunger, fatigue, alertness, timePeriod } = psychologyState;

            // 基础心理活动模板 - 更加人性化和自然
            const templates = {
                high_energy: [
                    "今天感觉特别有活力，脑子转得很快，想要做点什么有意思的事情。",
                    "精神状态很棒，感觉可以应对任何挑战，充满了干劲。",
                    "能量满满的感觉真好，思路清晰，对接下来要做的事情很期待。",
                    "今天的我状态在线，感觉什么困难都难不倒我。"
                ],
                low_energy: [
                    "有点累了，感觉需要慢慢来，不想太急躁。",
                    "今天的精力有限，但还是会尽力而为，只是节奏可能会慢一些。",
                    "感觉有些疲惫，需要更多的耐心来处理事情，不过没关系。",
                    "虽然有点疲倦，但还是想认真对待每一件事。"
                ],
                high_stress: [
                    "感觉有些紧张，但这种紧张感也让我更加专注和谨慎。",
                    "压力确实存在，不过我会更仔细地思考每个决定。",
                    "虽然有压力，但我相信自己能够处理好，一步一步来。",
                    "有点紧张，但这也提醒我要更加认真对待当前的情况。"
                ],
                low_stress: [
                    "心情很平静，感觉可以轻松地面对各种问题。",
                    "没什么压力，可以更自然地表达自己的想法。",
                    "很放松的状态，能够更好地倾听和理解别人。",
                    "心态很平和，感觉今天可以很从容地处理事情。"
                ],
                positive_mood: [
                    "心情挺好的，对和人交流感到很开心。",
                    "今天的心境很积极，很愿意分享自己的想法和感受。",
                    "感觉很愉快，希望这种好心情也能感染到别人。",
                    "心情不错，对今天发生的事情都很有兴趣。"
                ],
                negative_mood: [
                    "心情有些低落，但还是会认真对待每一次对话。",
                    "情绪不太高，可能会更倾向于安静地思考。",
                    "今天可能不太想说太多话，更喜欢深入地思考问题。",
                    "心情有点沉重，但不会影响我对事情的认真态度。"
                ]
            };

            let content = "";

            // 根据物理状态选择合适的内容
            if (energy > 70) {
                content += this.getRandomTemplate(templates.high_energy) + " ";
            } else if (energy < 40) {
                content += this.getRandomTemplate(templates.low_energy) + " ";
            }

            if (fatigue > 60) {
                content += this.getRandomTemplate(templates.high_stress) + " ";
            } else {
                content += this.getRandomTemplate(templates.low_stress) + " ";
            }

            if (alertness > 60 && focus > 60) {
                content += this.getRandomTemplate(templates.positive_mood);
            } else if (alertness < 40 || focus < 40) {
                content += this.getRandomTemplate(templates.negative_mood);
            }

            // 添加时间相关的心理活动
            content += this.generateTimePeriodContent(timePeriod);

            // 添加世界树背景相关内容
            if (worldTreeConfig && worldTreeConfig.worldBackground) {
                content += this.generateWorldBackgroundContent(worldTreeConfig.worldBackground, psychologyState);
            }

            return content.trim();

        } catch (error) {
            this.logger.error('世界树VCP', '本地心理内容生成失败:', error.message);
            return "正在思考中...";
        }
    }

    /**
     * 获取随机模板
     */
    getRandomTemplate(templates) {
        return templates[Math.floor(Math.random() * templates.length)];
    }

    /**
     * 生成时间段相关内容
     */
    generateTimePeriodContent(timePeriod) {
        const timeContent = {
            morning: [
                " 早晨的感觉真好，头脑清醒，整个人都很有精神。",
                " 清晨的空气很清新，让我感觉思路特别清晰。",
                " 新的一天开始了，感觉充满了可能性。",
                " 早上的时光总是让我感到平静和专注。"
            ],
            afternoon: [
                " 下午的阳光很温暖，让人感觉很舒服，适合好好聊聊。",
                " 午后时光，不急不躁，正好可以深入地交流一些想法。",
                " 下午的节奏刚好，既不会太匆忙，也不会太慵懒。",
                " 这个时候的我状态正好，可以专心地处理各种事情。"
            ],
            evening: [
                " 傍晚了，一天下来有了不少感悟和体会。",
                " 夕阳西下的时候，总是让我想要回顾和思考。",
                " 傍晚时分，心情会变得更加沉静和深刻。",
                " 这个时候的我会更加感性一些，容易被触动。"
            ],
            night: [
                " 夜深了，周围很安静，正是深度思考的好时候。",
                " 夜晚的宁静让我能够更专注地思考问题。",
                " 深夜时分，思绪会变得更加深邃和敏感。",
                " 夜晚的我会更加内省，喜欢探讨一些深层的话题。"
            ]
        };

        const options = timeContent[timePeriod];
        return options ? options[Math.floor(Math.random() * options.length)] : "";
    }

    /**
     * 生成世界背景相关内容
     */
    generateWorldBackgroundContent(worldBackground, psychologyState) {
        if (!worldBackground) return "";

        // 简单的背景影响逻辑
        const backgroundInfluence = " 在当前的环境设定下，我会以更符合角色设定的方式来思考和回应。";

        return backgroundInfluence;
    }

    /**
     * 使用API生成智能心理活动内容
     */
    async generateIntelligentPsychologyContent(userId, agentName, physicalState, worldTreeConfig, contextFactors) {
        try {
            // 获取最近对话记录
            const recentConversations = await this.getRecentConversations(userId, agentName, 5);

            // 构建心理分析请求
            const analysisPrompt = this.buildPsychologyAnalysisPrompt(
                agentName,
                physicalState,
                worldTreeConfig,
                recentConversations,
                contextFactors
            );

            // 调用API进行心理分析
            const response = await this.callPsychologyAPI(analysisPrompt);

            return response || this.generateFallbackPsychologyContent(physicalState);

        } catch (error) {
            this.logger.warning('世界树VCP', `API心理分析失败，使用备用方案:`, error.message);
            return this.generateFallbackPsychologyContent(physicalState);
        }
    }

    /**
     * 构建心理分析提示词
     */
    buildPsychologyAnalysisPrompt(agentName, physicalState, worldTreeConfig, recentConversations, contextFactors) {
        const currentTime = this.getCurrentLocalTime();
        const timePeriod = this.getTimePeriodName(this.getTimePeriod());

        let prompt = `你是${agentName}，请以第一人称的角度，根据当前状态和情况，写出你此刻的内心想法。

【当前时间】${currentTime} (${timePeriod}时段)

【你的物理状态】
- 专注程度: ${physicalState.focus.toFixed(1)}/100
- 精力水平: ${physicalState.energy.toFixed(1)}/100
- 饥饿感: ${physicalState.hunger.toFixed(1)}/100
- 疲劳度: ${physicalState.fatigue.toFixed(1)}/100
- 警觉性: ${physicalState.alertness.toFixed(1)}/100

`;

        // 添加世界背景
        if (worldTreeConfig?.worldBackground) {
            prompt += `【你的身份背景】\n${worldTreeConfig.worldBackground}\n\n`;
        }

        // 添加时间段设定
        if (worldTreeConfig?.timeArchitecture) {
            const currentPeriod = this.getTimePeriod();
            if (worldTreeConfig.timeArchitecture[currentPeriod]) {
                prompt += `【${timePeriod}时段的你】\n${worldTreeConfig.timeArchitecture[currentPeriod]}\n\n`;
            }
        }

        // 添加最近对话
        if (recentConversations && recentConversations.length > 0) {
            prompt += `【最近的对话内容】\n`;
            recentConversations.slice(0, 3).forEach((conv, index) => {
                prompt += `${index + 1}. ${conv.content}\n`;
            });
            prompt += `\n`;
        }

        // 添加上下文因子
        if (contextFactors.hasRecentConversation) {
            prompt += `【当前情况】刚刚有人和你对话\n\n`;
        }

        prompt += `请以第一人称写出你此刻的内心想法，要求：
1. 体现当前的物理状态（专注度、精力、疲劳等）
2. 符合你的身份背景和性格
3. 考虑当前时间段的特点
4. 自然真实，就像真正的内心独白
5. 控制在50-80字之间
6. 不要使用引号或其他格式符号

请直接输出内心想法：`;

        return prompt;
    }

    /**
     * 调用心理分析API
     */
    async callPsychologyAPI(prompt) {
        try {
            const response = await axios.post(
                `${this.config.apiUrl}/v1/chat/completions`,
                {
                    model: this.config.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: 200,
                    temperature: 0.8,
                    top_p: 0.9
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.config.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: this.config.timeout
                }
            );

            if (response.data?.choices?.[0]?.message?.content) {
                return response.data.choices[0].message.content.trim();
            }

            throw new Error('API响应格式错误');

        } catch (error) {
            this.logger.error('世界树VCP', 'API调用失败:', error.message);
            throw error;
        }
    }

    /**
     * 生成备用心理内容
     */
    generateFallbackPsychologyContent(physicalState) {
        const templates = {
            high_energy: [
                "感觉精神很好，思路清晰，准备好应对各种挑战。",
                "今天状态不错，充满活力，对接下来的事情很期待。"
            ],
            low_energy: [
                "有些疲惫，需要调整节奏，慢慢来处理事情。",
                "精力有限，但会尽力保持专注，认真对待每件事。"
            ],
            high_focus: [
                "注意力很集中，能够深入思考问题。",
                "专注度很高，适合处理复杂的任务。"
            ],
            low_focus: [
                "注意力有些分散，需要更多耐心。",
                "专注度不够，可能需要休息一下。"
            ]
        };

        let selectedTemplate;
        if (physicalState.energy > 60) {
            selectedTemplate = templates.high_energy;
        } else if (physicalState.energy < 40) {
            selectedTemplate = templates.low_energy;
        } else if (physicalState.focus > 60) {
            selectedTemplate = templates.high_focus;
        } else {
            selectedTemplate = templates.low_focus;
        }

        return selectedTemplate[Math.floor(Math.random() * selectedTemplate.length)];
    }

    /**
     * 使用API生成心理活动内容（旧版本，保留兼容性）
     */
    async generateAPIPsychologyContent(psychologyState, worldTreeConfig, contextFactors) {
        try {
            if (!this.config.apiUrl || !this.config.apiKey) {
                this.logger.warning('世界树VCP', 'API配置不完整，回退到本地算法');
                return await this.generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors);
            }

            const prompt = this.buildPsychologyPrompt(psychologyState, worldTreeConfig, contextFactors);

            // 这里可以添加API调用逻辑
            // 暂时回退到本地算法
            return await this.generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors);

        } catch (error) {
            this.logger.error('世界树VCP', 'API心理内容生成失败:', error.message);
            return await this.generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors);
        }
    }

    /**
     * 构建心理活动提示词
     */
    buildPsychologyPrompt(psychologyState, worldTreeConfig, contextFactors) {
        const { stress, emotion, energy, mood, focus, timePeriod } = psychologyState;

        let prompt = `请根据以下心理状态生成简短的内心独白：\n`;
        prompt += `压力水平: ${stress}\n`;
        prompt += `情绪状态: ${emotion}\n`;
        prompt += `能量水平: ${energy}\n`;
        prompt += `心情状态: ${mood}\n`;
        prompt += `专注程度: ${focus}\n`;
        prompt += `时间段: ${timePeriod}\n`;

        if (worldTreeConfig && worldTreeConfig.worldBackground) {
            prompt += `世界背景: ${worldTreeConfig.worldBackground}\n`;
        }

        prompt += `\n请生成50-100字的内心独白，体现当前的心理状态。`;

        return prompt;
    }

    /**
     * 保存心理活动记录
     */
    async savePsychologyActivity(userId, agentName, psychologyState, content, contextFactors) {
        try {
            const currentTime = this.getCurrentLocalTime();

            // 保存到心理活动记录表
            await this.dbRun(`
                INSERT INTO psychology_activities
                (user_id, agent_name, psychology_state, generated_content, context_factors, created_time)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [
                userId,
                agentName,
                JSON.stringify(psychologyState),
                content,
                JSON.stringify(contextFactors),
                currentTime
            ]);

            // 保存到世界树专用心理状态表（使用REPLACE实现更新或插入）
            await this.dbRun(`
                REPLACE INTO worldtree_psychology_states
                (user_id, agent_name, psychology_data, emotion_factors, context_summary, last_updated, created_time)
                VALUES (?, ?, ?, ?, ?, ?, COALESCE(
                    (SELECT created_time FROM worldtree_psychology_states WHERE user_id = ? AND agent_name = ?),
                    ?
                ))
            `, [
                userId,
                agentName,
                JSON.stringify({
                    psychologyState: psychologyState,
                    generatedContent: content,
                    timestamp: currentTime
                }),
                JSON.stringify(contextFactors),
                content.substring(0, 200) + (content.length > 200 ? '...' : ''),
                currentTime,
                userId,
                agentName,
                currentTime
            ]);

        } catch (error) {
            this.logger.error('世界树VCP', '心理活动保存失败:', error.message);
        }
    }

    /**
     * 获取世界树心理状态数据
     */
    async getWorldTreePsychologyState(userId, agentName) {
        try {
            const result = await this.dbGet(`
                SELECT psychology_data, emotion_factors, context_summary, last_updated
                FROM worldtree_psychology_states
                WHERE user_id = ? AND agent_name = ?
            `, [userId, agentName]);

            if (result) {
                return {
                    psychologyData: JSON.parse(result.psychology_data),
                    emotionFactors: JSON.parse(result.emotion_factors || '{}'),
                    contextSummary: result.context_summary,
                    lastUpdated: result.last_updated
                };
            }

            return null;
        } catch (error) {
            this.logger.error('世界树VCP', '心理状态数据获取失败:', error.message);
            return null;
        }
    }

    /**
     * 更新所有心理状态
     */
    async updateAllPsychologyStates() {
        try {
            // 获取所有配置了世界树的Agent
            const configuredAgents = await this.dbAll(`
                SELECT agent_name FROM world_tree_configs
            `);

            // 为每个配置的Agent生成默认的心理状态更新
            for (const agentConfig of configuredAgents) {
                try {
                    // 使用默认用户ID进行心理状态更新
                    await this.generatePsychologyActivity('system_update', agentConfig.agent_name, {
                        updateType: 'scheduled',
                        timestamp: this.getCurrentLocalTime()
                    });
                } catch (error) {
                    this.logger.warning('世界树VCP', `Agent心理状态更新失败 [${agentConfig.agent_name}]:`, error.message);
                }
            }

            this.logger.debug('世界树VCP', `完成 ${configuredAgents.length} 个Agent的心理状态更新`);

        } catch (error) {
            this.logger.error('世界树VCP', '批量心理状态更新失败:', error.message);
        }
    }

    /**
     * 获取Agent列表（从server.js的Agent目录）
     */
    async getAgentList() {
        try {
            const agentDir = path.join(__dirname, '../../Agent');
            const files = await fs.readdir(agentDir);

            const agents = [];
            for (const file of files) {
                if (path.extname(file).toLowerCase() === '.txt') {
                    const agentName = path.basename(file, '.txt');
                    agents.push({
                        name: agentName,
                        filename: file,
                        hasWorldTreeConfig: this.worldTreeConfigs.has(agentName)
                    });
                }
            }

            return agents;
        } catch (error) {
            this.logger.error('世界树VCP', 'Agent列表获取失败:', error.message);
            return [];
        }
    }

    /**
     * 生成系统消息内容（用于注入到system消息）
     */
    async generateSystemMessage(userId, agentName, recentConversation = '') {
        try {
            // 检查是否有该Agent的世界树配置
            const worldTreeConfig = await this.getWorldTreeConfig(agentName);
            if (!worldTreeConfig) {
                return ''; // 没有配置则不注入内容
            }

            // 生成当前心理活动
            const psychologyActivity = await this.generatePsychologyActivity(userId, agentName, {
                hasRecentConversation: !!recentConversation,
                conversationLength: recentConversation.length
            });

            const currentTime = this.getCurrentLocalTime();
            const currentPeriod = this.getTimePeriod();

            // 构建格式化的世界树架构 - 从人类视角描述，无emoji
            let systemContent = '=== 世界树角色设定与状态信息 ===\n';
            systemContent += `当前时间: ${currentTime} (${this.getTimePeriodName(currentPeriod)}时段)\n`;
            systemContent += `你的身份: ${agentName}\n`;
            systemContent += '---\n';

            // 世界背景设定
            if (worldTreeConfig.worldBackground) {
                systemContent += '\n[你所处的世界]\n';
                systemContent += `${worldTreeConfig.worldBackground}\n`;
                systemContent += '---\n';
            }

            // 当前时间段的特殊设定
            if (worldTreeConfig.timeArchitecture && worldTreeConfig.timeArchitecture[currentPeriod]) {
                systemContent += `\n[${this.getTimePeriodName(currentPeriod)}时段的你]\n`;
                systemContent += `${worldTreeConfig.timeArchitecture[currentPeriod]}\n`;
                systemContent += '---\n';
            }

            // 角色日程表
            if (worldTreeConfig.characterSchedules && Object.keys(worldTreeConfig.characterSchedules).length > 0) {
                systemContent += '\n[你的日程安排]\n';
                for (const [timeSlot, schedule] of Object.entries(worldTreeConfig.characterSchedules)) {
                    systemContent += `${timeSlot}: ${schedule}\n`;
                }
                systemContent += '---\n';
            }

            // 当前物理状态
            if (psychologyActivity) {
                systemContent += '\n[你当前的内心状态]\n';
                systemContent += `此刻你的内心想法: ${psychologyActivity.content}\n\n`;

                systemContent += '[你的物理状态指标]\n';
                const ps = psychologyActivity.psychologyState;
                systemContent += `专注程度: ${ps.focus.toFixed(1)}/100\n`;
                systemContent += `精力水平: ${ps.energy.toFixed(1)}/100\n`;
                systemContent += `饥饿感: ${ps.hunger.toFixed(1)}/100\n`;
                systemContent += `疲劳度: ${ps.fatigue.toFixed(1)}/100\n`;
                systemContent += `警觉性: ${ps.alertness.toFixed(1)}/100\n`;
                systemContent += '---\n';
            }

            // 叙事准则
            if (worldTreeConfig.narrativeRules && Object.keys(worldTreeConfig.narrativeRules).length > 0) {
                systemContent += '\n[你的性格特质与行为准则]\n';
                for (const [rule, description] of Object.entries(worldTreeConfig.narrativeRules)) {
                    systemContent += `${rule}: ${description}\n`;
                }
                systemContent += '---\n';
            }

            systemContent += '\n[角色扮演指导]\n';
            systemContent += '请完全沉浸在这个角色中，以第一人称的视角进行对话\n';
            systemContent += '你的回应应该反映当前的物理状态变化\n';
            systemContent += '根据你的精力和专注程度调整回应的详细程度和语气\n';
            systemContent += '保持角色的一致性，体现你的性格特质和行为准则\n';
            systemContent += '考虑当前时段的特殊情况和你的日程安排\n';
            systemContent += '让对话感觉自然真实，就像真正的人类在交流\n';
            systemContent += '=== 世界树设定结束 ===\n\n';

            return systemContent;

        } catch (error) {
            this.logger.error('世界树VCP', `系统消息生成失败 [${userId}/${agentName}]:`, error.message);
            return '';
        }
    }

    /**
     * 获取时间段中文名称
     */
    getTimePeriodName(period) {
        const names = {
            morning: '早晨',
            afternoon: '下午',
            evening: '傍晚',
            night: '夜晚'
        };
        return names[period] || period;
    }

    /**
     * 根据数值获取状态表情符号
     */
    getStatusEmoji(value, type) {
        if (type === 'stress') {
            if (value < 20) return '😌';
            if (value < 40) return '😐';
            if (value < 60) return '😰';
            if (value < 80) return '😫';
            return '🤯';
        }

        if (type === 'emotion') {
            if (value < -20) return '😢';
            if (value < 0) return '😔';
            if (value < 20) return '😐';
            if (value < 40) return '🙂';
            if (value < 60) return '😊';
            if (value < 80) return '😄';
            return '🤩';
        }

        if (value < 20) return '🔴';
        if (value < 40) return '🟠';
        if (value < 60) return '🟡';
        if (value < 80) return '🟢';
        return '🔵';
    }

    /**
     * 插件清理
     */
    async cleanup() {
        try {
            // 停止定时器
            if (this.psychologyTimer) {
                clearInterval(this.psychologyTimer);
                this.psychologyTimer = null;
            }

            // 关闭数据库连接
            if (this.db) {
                await new Promise((resolve) => {
                    this.db.close((err) => {
                        if (err) {
                            this.logger.error('世界树VCP', '数据库关闭失败:', err.message);
                        }
                        resolve();
                    });
                });
                this.db = null;
            }

            this.isInitialized = false;
            this.logger.info('世界树VCP', '插件清理完成');

        } catch (error) {
            this.logger.error('世界树VCP', '插件清理失败:', error.message);
        }
    }

    /**
     * 获取插件状态
     */
    getStatus() {
        return {
            name: this.pluginName,
            version: this.version,
            description: this.description,
            isInitialized: this.isInitialized,
            config: {
                enabled: this.config.enabled,
                useLocalAlgorithm: this.config.useLocalAlgorithm,
                hasApiConfig: !!(this.config.apiUrl && this.config.apiKey)
            },
            statistics: {
                configuredAgents: this.worldTreeConfigs.size,
                updateInterval: this.config.psychologyUpdateInterval / 1000
            }
        };
    }
}

module.exports = WorldTreeVCP;
