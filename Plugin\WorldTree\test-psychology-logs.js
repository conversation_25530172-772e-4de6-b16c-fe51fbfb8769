/**
 * 直接测试心理活动日志获取功能
 */

const axios = require('axios');

async function testPsychologyLogs() {
    console.log('🔍 测试心理活动日志API...\n');
    
    try {
        // 测试心理活动日志API
        const response = await axios.get('http://localhost:6005/admin_api/worldtree/psychology/logs?limit=10');
        const data = response.data;
        
        console.log('API响应:', JSON.stringify(data, null, 2));
        
        if (data.success) {
            console.log(`✅ 获取心理活动日志成功，共${data.logs.length}条记录`);
            
            if (data.logs.length > 0) {
                console.log('\n📝 心理活动记录:');
                data.logs.forEach((log, index) => {
                    console.log(`\n${index + 1}. ${log.agentName}:`);
                    console.log(`   内容: ${log.content}`);
                    console.log(`   时间: ${log.timestamp}`);
                    if (log.physicalState) {
                        console.log(`   心理状态: 专注${log.physicalState.focus || '--'} 精力${log.physicalState.energy || '--'} 疲劳${log.physicalState.fatigue || '--'}`);
                    }
                });
            } else {
                console.log('⚠️ 暂无心理活动记录');
            }
        } else {
            console.log(`❌ 获取心理活动日志失败: ${data.error}`);
        }
        
    } catch (error) {
        console.error('❌ 请求失败:', error.message);
    }
}

testPsychologyLogs();
