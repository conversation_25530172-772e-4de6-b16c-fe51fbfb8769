# OneBot11 插件开发辅助工具

## 📋 概述

OneBot11插件系统提供了一套完整的开发辅助工具，让插件开发变得更加简单和高效。这些工具包括：

- **PluginHelper** - 通用工具函数库
- **MessageBuilder** - 消息构建器
- **PermissionManager** - 权限管理器

## 🔧 PluginHelper - 通用工具函数库

### 导入和使用

```javascript
const { helper } = require('./plugin_helper');

// 或者导入类
const { PluginHelper } = require('./plugin_helper');
const myHelper = new PluginHelper();
```

### 主要功能

#### 📁 文件操作
```javascript
// 读取文件
const content = helper.readFile('path/to/file.txt');

// 写入文件
helper.writeFile('path/to/file.txt', 'content');

// 追加文件
helper.appendFile('path/to/file.txt', 'new content');

// 检查文件是否存在
if (helper.fileExists('path/to/file.txt')) {
    console.log('文件存在');
}

// 获取文件大小
const size = helper.getFileSize('path/to/file.txt');
const formattedSize = helper.formatFileSize(size);
```

#### ⏰ 时间工具
```javascript
// 获取时间戳
const timestamp = helper.getTimestamp();

// 格式化时间
const formatted = helper.formatTime(); // 当前时间
const customFormat = helper.formatTime(new Date(), 'YYYY年MM月DD日 HH:mm:ss');

// 延迟执行
await helper.sleep(1000); // 延迟1秒
```

#### 🆔 工具函数
```javascript
// 生成唯一ID
const id = helper.generateId(8); // 8位随机ID

// 安全JSON解析
const data = helper.safeJsonParse(jsonString, {});

// 格式化文件大小
const size = helper.formatFileSize(1024000); // "1000.00 KB"
```

#### 📝 消息处理
```javascript
// 提取文本内容
const text = helper.extractText(message);

// 提取图片信息
const images = helper.extractImages(message);

// 提取文件信息
const files = helper.extractFiles(message);

// 检查关键词
if (helper.containsKeywords(message, ['关键词1', '关键词2'])) {
    // 处理逻辑
}

// 正则匹配
if (helper.matchesPattern(message, /^\d+$/)) {
    // 处理数字消息
}
```

#### 💾 简单存储
```javascript
// 创建存储实例
const storage = helper.createStorage('my_plugin_data');

// 存储数据
storage.set('key', 'value');
storage.set('user_123', { name: '用户', score: 100 });

// 读取数据
const value = storage.get('key', 'default_value');
const user = storage.get('user_123', {});

// 删除数据
storage.delete('key');

// 清空所有数据
storage.clear();

// 获取所有数据
const allData = storage.getAll();
```

## 📨 MessageBuilder - 消息构建器

### 基础使用

```javascript
const MessageBuilder = require('./message_builder');

// 创建消息构建器
const msg = new MessageBuilder();

// 链式调用构建消息
const message = msg
    .text('Hello ')
    .at(userId)
    .text('!')
    .newLine()
    .image('image.jpg')
    .build();

// 发送消息
await adapter.sendMessage(context, message);
```

### 快速构建方法

```javascript
// 快速文本消息
const textMsg = MessageBuilder.text('Hello World');

// 快速图片消息
const imageMsg = MessageBuilder.image('image.jpg', '图片说明');

// 快速@消息
const atMsg = MessageBuilder.at(userId, '你好');
```

### 支持的消息类型

```javascript
const msg = new MessageBuilder()
    .text('文本内容')                    // 文本
    .newLine(2)                         // 换行
    .at(userId)                         // @用户
    .atAll()                           // @全体成员
    .image('image.jpg')                // 图片
    .record('voice.mp3')               // 语音
    .video('video.mp4')                // 视频
    .face(1)                           // 表情
    .share(url, title, content, image) // 链接分享
    .location(lat, lon, title, content) // 位置
    .music('qq', 'songId')             // 音乐分享
    .reply(messageId)                  // 回复消息
    .poke(userId)                      // 戳一戳
    .custom('type', { data })          // 自定义消息段
    .build();
```

### 实用方法

```javascript
// 获取纯文本
const text = msg.toText();

// 检查是否为空
if (msg.isEmpty()) {
    console.log('消息为空');
}

// 获取消息长度
const length = msg.length();

// 克隆消息构建器
const cloned = msg.clone();

// 清空消息
msg.clear();

// JSON序列化
const json = msg.toJSON();
const restored = MessageBuilder.fromJSON(json);
```

## 🔐 PermissionManager - 权限管理器

### 初始化

```javascript
const PermissionManager = require('./permission_manager');

// 在插件构造函数中初始化
constructor(adapter, logger, config) {
    super('MyPlugin', adapter, logger, config);
    this.permissionManager = new PermissionManager(config);
}
```

### 权限检查

```javascript
// 检查各种权限级别
const isSuperAdmin = this.permissionManager.isSuperAdmin(userId);
const isBotAdmin = this.permissionManager.isBotAdmin(userId);
const isGroupAdmin = this.permissionManager.isGroupAdmin(userId, groupId);
const hasAdminPermission = this.permissionManager.hasAdminPermission(userId, groupId, sender);

// 根据权限级别检查
const hasPermission = this.permissionManager.checkPermission(userId, 'admin', {
    groupId: groupId,
    sender: sender
});

// 完整权限检查（包括黑白名单）
const result = this.permissionManager.fullPermissionCheck(userId, 'admin', {
    groupId: groupId,
    sender: sender
});

if (!result.allowed) {
    const message = this.permissionManager.getPermissionDeniedMessage(result.reason);
    await this.reply(context, message);
    return;
}
```

### 权限管理

```javascript
// 添加/移除超级管理员
this.permissionManager.addSuperAdmin(userId);
this.permissionManager.removeSuperAdmin(userId);

// 添加/移除群管理员
this.permissionManager.addGroupAdmin(userId, groupId);
this.permissionManager.removeGroupAdmin(userId, groupId);

// 获取权限统计
const stats = this.permissionManager.getPermissionStats();
console.log(`超级管理员: ${stats.superAdmins}人`);
```

## 🎯 完整插件示例

```javascript
const BasePlugin = require('./base_plugin');
const { helper } = require('./plugin_helper');
const MessageBuilder = require('./message_builder');
const PermissionManager = require('./permission_manager');

class MyPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('MyPlugin', adapter, logger, config);
        
        this.priority = 50;
        this.permission = 'all';
        
        // 初始化辅助工具
        this.permissionManager = new PermissionManager(config);
        this.storage = helper.createStorage('my_plugin_data');
        
        this.keywords = ['测试', 'test'];
    }

    shouldHandle(context) {
        return helper.containsKeywords(context.message, this.keywords);
    }

    async handle(context) {
        try {
            // 权限检查
            const permCheck = this.permissionManager.fullPermissionCheck(
                context.userId, 
                this.permission, 
                context
            );
            
            if (!permCheck.allowed) {
                const msg = this.permissionManager.getPermissionDeniedMessage(permCheck.reason);
                await this.reply(context, msg);
                return { handled: true, error: 'permission_denied' };
            }

            // 提取消息内容
            const text = helper.extractText(context.message);
            const images = helper.extractImages(context.message);
            
            // 更新用户数据
            const visitCount = this.storage.get(`visits_${context.userId}`, 0) + 1;
            this.storage.set(`visits_${context.userId}`, visitCount);
            
            // 构建回复消息
            const reply = new MessageBuilder()
                .text(`你好 `)
                .at(context.userId)
                .text(`!\n`)
                .text(`这是你第 ${visitCount} 次使用此功能\n`)
                .text(`消息内容: ${text}\n`)
                .text(`图片数量: ${images.length}`)
                .build();
            
            await this.adapter.sendMessage(context, reply);
            
            return { handled: true, message: 'success' };
            
        } catch (error) {
            this.logger.error('Plugin', `处理失败: ${error.message}`);
            await this.reply(context, `❌ 处理失败: ${error.message}`);
            return { handled: true, error: error.message };
        }
    }
}

module.exports = MyPlugin;
```

## 🚀 最佳实践

### 1. 错误处理
```javascript
// 使用helper的安全函数
const data = helper.safeJsonParse(jsonString, {});

// 文件操作检查
if (helper.fileExists(filePath)) {
    const content = helper.readFile(filePath);
}
```

### 2. 性能优化
```javascript
// 使用存储缓存数据
const cache = helper.createStorage('cache');
const cachedData = cache.get('expensive_data');
if (!cachedData) {
    const data = await expensiveOperation();
    cache.set('expensive_data', data);
}
```

### 3. 消息构建
```javascript
// 分步构建复杂消息
const msg = new MessageBuilder();

if (hasImage) {
    msg.image(imageUrl);
}

if (needAt) {
    msg.at(userId);
}

msg.text('最终文本');
```

### 4. 权限分层
```javascript
// 不同功能使用不同权限级别
if (this.permissionManager.hasMasterPermission(userId)) {
    // 主人专用功能
} else if (this.permissionManager.hasAdminPermission(userId, groupId, sender)) {
    // 管理员功能
} else {
    // 普通用户功能
}
```

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看演示插件** - `helper_demo_plugin.js` 包含了所有工具的使用示例
2. **发送"工具演示"** - 在聊天中测试各种功能
3. **查看日志输出** - 检查插件运行日志
4. **参考现有插件** - 学习其他插件的实现方式

## 🎉 功能特色

- ✅ **简化开发** - 减少重复代码，提高开发效率
- ✅ **类型安全** - 提供安全的数据处理函数
- ✅ **链式调用** - 优雅的消息构建方式
- ✅ **权限控制** - 完整的权限管理体系
- ✅ **持久存储** - 简单易用的数据存储
- ✅ **错误处理** - 完善的错误处理机制
- ✅ **性能优化** - 高效的工具函数实现
