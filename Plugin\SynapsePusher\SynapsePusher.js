const WebSocket = require('ws');
const axios = require('axios');
const path = require('path'); // May not be strictly needed but good for future path operations

let pluginConfigInstance;
let wsClient;
let synapseTxnId = 0;
let wsReconnectTimer;
let parsedMaidAccessTokens = {}; // To store the parsed JSON from MaidAccessTokensJSON
let parsedMaidToolWhitelists = {}; // To store parsed JSON for tool whitelists

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}

async function sendToSynapse(logEvent) {
    const logData = logEvent.data; 
    const serverBroadcastTimestamp = logEvent.timestamp;
    
    let maidName = logData.extractedMaidName; // 从 onMessage 预处理中获取
    const toolName = logData.tool_name;
    let accessToken;

    if (!maidName) {
        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
            logger.warning('Synapse推送', `sendToSynapse: 无法从logData.content提取MaidName。跳过工具'${toolName || 'N/A'}'的发送`, logData);
        }
        return;
    }

    // 检查是否启用测试模式
    if (pluginConfigInstance.BypassWhitelistForTesting === true) {
        if (pluginConfigInstance.DebugMode) {
            logger.debug('Synapse推送', '调试: 测试白名单绕过已启用。使用SynapseAccessTokenForTestingOnly');
        }
        accessToken = pluginConfigInstance.SynapseAccessTokenForTestingOnly;
        if (!accessToken) {
            if (pluginConfigInstance.DebugMode) {
                logger.warning('Synapse推送', 'BypassWhitelistForTesting为true，但SynapseAccessTokenForTestingOnly未配置。无法发送');
            }
            return;
        }
    } else {
        // 正常严格匹配逻辑
        // 1. Maid 必须在 MaidAccessTokensJSON 中有明确配置的 AccessToken
        accessToken = parsedMaidAccessTokens[maidName];
        if (!accessToken) {
            if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                logger.warning('Synapse推送', `严格模式: 在MaidAccessTokensJSON中未找到Maid '${maidName || 'Unknown'}' 的特定Synapse访问令牌。跳过工具'${toolName || 'N/A'}'的发送`);
            }
            return;
        }

        // 3. Maid 必须在 MaidToolWhitelistJSON 中有明确配置的白名单，且 tool_name 必须在其中
        const maidWhitelist = parsedMaidToolWhitelists[maidName];
        if (maidWhitelist) {
            if (!maidWhitelist.includes(toolName)) {
                if (pluginConfigInstance.DebugMode) {
                    logger.debug('Synapse推送', `严格模式: Maid '${maidName || 'Unknown'}' 的工具'${toolName || 'N/A'}'不在其白名单中。跳过Synapse推送`);
                }
                return;
            }
        } else {
            if (pluginConfigInstance.DebugMode) {
                logger.debug('Synapse推送', `严格模式: Maid '${maidName || 'Unknown'}' 在MaidToolWhitelistJSON中没有配置的工具白名单。跳过工具'${toolName || 'N/A'}'的Synapse推送`);
            }
            return;
        }
    }

    // 共同的检查点：基础 Synapse 配置和最终确定的 accessToken
    if (!pluginConfigInstance || !pluginConfigInstance.SynapseHomeserver || !pluginConfigInstance.SynapseRoomID) {
        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
            logger.warning('Synapse推送', `基本Synapse配置（Homeserver、RoomID）缺失。跳过Maid: '${maidName || 'Unknown'}'，工具: '${toolName || 'N/A'}'的发送`);
        }
        return;
    }
    if (!accessToken) { // 这个检查理论上在上面分支中已覆盖，但双重保险
        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
            logger.warning('Synapse推送', `未确定有效的AccessToken（如果上述逻辑正确，这不应该发生）。Maid: '${maidName || 'Unknown'}'。跳过`);
        }
        return;
    }

    // 如果代码执行到这里，意味着：
    // 1. Maid 在 MaidAccessTokensJSON 中有特定的 accessToken。
    // 2. 基础 Synapse 配置存在。
    // 3. Maid 在 MaidToolWhitelistJSON 中有配置白名单，并且 tool_name 在白名单内。

    const { status, content, source } = logData;
    let formattedMessage = `**VCP Log Event (${source || 'N/A'})** [${serverBroadcastTimestamp || new Date().toISOString()}] (Maid: ${maidName || 'N/A'})\\n`;
    formattedMessage += `**Tool:** ${toolName || 'N/A'}\\n`;
    formattedMessage += `**Status:** ${status || 'N/A'}\\n`;
    
    let contentString = content;
    if (typeof content === 'object') {
        contentString = JSON.stringify(content, null, 2);
    }

    if (contentString && contentString.length > 3000) {
        contentString = contentString.substring(0, 3000) + "... (truncated)";
    }
    formattedMessage += `**Content:**\\n\`\`\`\\n${contentString || 'N/A'}\\n\`\`\``;

    const synapseUrl = `${pluginConfigInstance.SynapseHomeserver}/_matrix/client/r0/rooms/${encodeURIComponent(pluginConfigInstance.SynapseRoomID)}/send/m.room.message/${synapseTxnId++}`;

    try {
        if (pluginConfigInstance.DebugMode) {
            logger.debug('Synapse推送', `发送到Synapse房间 ${pluginConfigInstance.SynapseRoomID}，Maid '${maidName || 'Unknown'}'，工具'${toolName || 'N/A'}': ${formattedMessage.substring(0, 100)}...`);
        }
        await axios.put(synapseUrl, {
            msgtype: 'm.text',
            body: formattedMessage,
            format: "org.matrix.custom.html",
            formatted_body: formattedMessage.replace(/\\n/g, '<br/>')
        }, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });
        if (pluginConfigInstance.DebugMode) {
            logger.debug('Synapse推送', `成功向Synapse发送日志，Maid '${maidName || 'Unknown'}'，工具'${toolName || 'N/A'}'`);
        }
    } catch (error) {
        const errorMessage = error.response ? JSON.stringify(error.response.data) : error.message;
        logger.error('Synapse推送', `向Synapse发送日志时出错，Maid '${maidName || 'Unknown'}'，工具'${toolName || 'N/A'}'`, errorMessage);
    }
}

function connectToWebSocketLogSource() {
    if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
        logger.debug('Synapse推送', `调试: 配置中的当前VCP_Key: "${pluginConfigInstance.VCP_Key}"`);
        logger.debug('Synapse推送', `调试: 配置中的当前SERVER_PORT: "${pluginConfigInstance.SERVER_PORT}"`);
    }
    if (!pluginConfigInstance || !pluginConfigInstance.VCP_Key || !pluginConfigInstance.SERVER_PORT) {
        logger.error('Synapse推送', '无法连接到WebSocket服务器: 配置中缺少VCP_Key或SERVER_PORT');
        clearTimeout(wsReconnectTimer);
        wsReconnectTimer = setTimeout(connectToWebSocketLogSource, 15000);
        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
             logger.debug('Synapse推送', '由于缺少VCP_Key或SERVER_PORT，15秒后重试WebSocket连接');
        }
        return;
    }

    // Corrected path to /VCPlog/ (lowercase L) to match WebSocketServer.js regex
    const wsUrl = `ws://localhost:${pluginConfigInstance.SERVER_PORT}/VCPlog/VCP_Key=${pluginConfigInstance.VCP_Key}`;
    if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
        logger.debug('Synapse推送', `尝试连接到VCPLog WebSocket源: ${wsUrl}`);
    }

    if (wsClient && (wsClient.readyState === WebSocket.OPEN || wsClient.readyState === WebSocket.CONNECTING)) {
        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
            logger.debug('Synapse推送', 'WebSocket客户端已打开或正在连接到日志源');
        }
        return;
    }

    wsClient = new WebSocket(wsUrl);

    wsClient.on('open', () => {
        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
            logger.debug('Synapse推送', '成功连接到VCPLog WebSocket源');
        }
        clearTimeout(wsReconnectTimer);
    });

    wsClient.on('message', async (data) => {
        try {
            const messageString = data.toString();
            const message = JSON.parse(messageString);
            if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                logger.debug('Synapse推送', '从VCPLog WebSocket源收到消息', message);
            }

            if (message.type === 'vcp_log') {
                let extractedMaidName = null;
                if (message.data && typeof message.data.content === 'string') {
                    try {
                        const contentData = JSON.parse(message.data.content);
                        if (contentData && contentData.MaidName) {
                            extractedMaidName = contentData.MaidName;
                            if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                                logger.debug('Synapse推送', `从message.data.content提取的MaidName: '${extractedMaidName}'`);
                            }
                        } else {
                            if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                                logger.warning('Synapse推送', '在解析的message.data.content中未找到MaidName或contentData为null');
                            }
                        }
                    } catch (e) {
                        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                            logger.error('Synapse推送', '解析message.data.content为JSON时出错', `${e.message} 内容为: ${message.data.content}`);
                        }
                    }
                } else {
                    if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                        logger.warning('Synapse推送', 'message.data.content缺失或不是字符串。无法提取MaidName');
                    }
                }
                
                // 将提取的 MaidName (可能为 null) 附加到 message.data 以便 sendToSynapse 使用
                if (message.data) {
                    message.data.extractedMaidName = extractedMaidName;
                } else {
                     // 如果 message.data 本身就不存在，创建一个包含 extractedMaidName 的对象
                     // 这种情况理论上不应发生，因为 tool_name 等也需要从 message.data 获取
                    message.data = { extractedMaidName: extractedMaidName };
                     if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                        logger.warning('Synapse推送', '原始message.data为null或undefined。创建了新的数据对象');
                    }
                }

                // 移除之前对顶层 maid 和 tool_name 的检查，因为 maidName 现在从 content 提取
                // tool_name 的检查仍然保留，因为它直接来自顶层 logData.tool_name
                 if (!message.data || !message.data.tool_name) { 
                    if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                        logger.warning('Synapse推送', '收到的vcp_log数据中没有tool_name。白名单检查可能受到影响', message.data);
                    }
                }
                await sendToSynapse(message);
            } else if (message.type === 'connection_ack') {
                 if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                    logger.debug('Synapse推送', `来自VCPLog源的连接确认: ${message.message}`);
                }
            }
        } catch (error) {
            logger.error('Synapse推送', '处理来自VCPLog WebSocket源的消息时出错', error);
        }
    });

    wsClient.on('close', (code, reason) => {
        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
            logger.warning('Synapse推送', `与VCPLog源的WebSocket客户端连接断开。代码: ${code}，原因: ${String(reason)}。尝试重新连接...`);
        }
        wsClient = null;
        clearTimeout(wsReconnectTimer);
        wsReconnectTimer = setTimeout(connectToWebSocketLogSource, 5000); 
    });

    wsClient.on('error', (error) => {
        logger.error('Synapse推送', '连接到VCPLog源时WebSocket客户端出错', error.message);
        if (wsClient && wsClient.readyState !== WebSocket.OPEN && wsClient.readyState !== WebSocket.CONNECTING) {
             wsClient = null;
             clearTimeout(wsReconnectTimer);
             wsReconnectTimer = setTimeout(connectToWebSocketLogSource, 7000);
             if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
                 logger.debug('Synapse推送', '由于直接错误尝试重新连接到VCPLog源的WebSocket');
             }
        }
    });
}

// registerRoutes is the entry point for 'service' plugins with 'direct' communication.
function registerRoutes(app, config, projectBasePath) {
    pluginConfigInstance = config;

    if (pluginConfigInstance.MaidAccessTokensJSON) {
        try {
            parsedMaidAccessTokens = JSON.parse(pluginConfigInstance.MaidAccessTokensJSON);
            if (pluginConfigInstance.DebugMode) {
                logger.debug('Synapse推送', '成功解析MaidAccessTokensJSON', parsedMaidAccessTokens);
            }
        } catch (e) {
            logger.error('Synapse推送', '解析MaidAccessTokensJSON失败', e.message);
            parsedMaidAccessTokens = {}; 
        }
    } else {
        if (pluginConfigInstance.DebugMode) {
            logger.warning('Synapse推送', '配置中未定义MaidAccessTokensJSON');
        }
        parsedMaidAccessTokens = {};
    }

    // Parse MaidToolWhitelistJSON
    if (pluginConfigInstance.MaidToolWhitelistJSON) {
        try {
            parsedMaidToolWhitelists = JSON.parse(pluginConfigInstance.MaidToolWhitelistJSON);
            if (pluginConfigInstance.DebugMode) {
                logger.debug('Synapse推送', '成功解析MaidToolWhitelistJSON', parsedMaidToolWhitelists);
            }
        } catch (e) {
            logger.error('Synapse推送', '解析MaidToolWhitelistJSON失败', e.message);
            parsedMaidToolWhitelists = {}; // Use empty object on error, meaning default allow for all maids
        }
    } else {
        if (pluginConfigInstance.DebugMode) {
            logger.warning('Synapse推送', '配置中未定义MaidToolWhitelistJSON，默认允许所有工具');
        }
        parsedMaidToolWhitelists = {}; // Default to empty, meaning allow all tools for all maids
    }

    if (!pluginConfigInstance.SERVER_PORT && process.env.PORT) {
        pluginConfigInstance.SERVER_PORT = process.env.PORT;
    }
    if (!pluginConfigInstance.PROJECT_BASE_PATH) {
        pluginConfigInstance.PROJECT_BASE_PATH = projectBasePath;
    }

    if (pluginConfigInstance.DebugMode) {
        logger.plugin('Synapse推送', '调用registerRoutes。已初始化配置', pluginConfigInstance);
    }

    logger.plugin('Synapse推送', '插件已加载。如果配置存在，尝试连接到WebSocket日志源');
    connectToWebSocketLogSource();
}

function shutdown() {
    if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
        logger.plugin('Synapse推送', '正在关闭...');
    }
    clearTimeout(wsReconnectTimer);
    if (wsClient) {
        wsClient.removeAllListeners();
        wsClient.close();
        wsClient = null;
        if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
            logger.debug('Synapse推送', '与VCPLog源的WebSocket客户端连接已关闭');
        }
    }
    if (pluginConfigInstance && pluginConfigInstance.DebugMode) {
        logger.plugin('Synapse推送', '关闭完成');
    }
}

module.exports = {
    registerRoutes,
    shutdown
}; 