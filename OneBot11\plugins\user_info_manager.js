/**
 * 用户信息管理器
 * 自动保存和管理用户详细信息
 */

const fs = require('fs');
const path = require('path');

class UserInfoManager {
    constructor() {
        this.userDataDir = path.join(__dirname, '..', 'user_data');
        this.ensureDirectoryExists();
    }

    /**
     * 确保用户数据目录存在
     */
    ensureDirectoryExists() {
        if (!fs.existsSync(this.userDataDir)) {
            fs.mkdirSync(this.userDataDir, { recursive: true });
        }
    }

    /**
     * 检查是否是用户信息API响应
     * @param {Object} message API响应消息
     * @returns {boolean} 是否是用户信息
     */
    isUserInfoResponse(message) {
        if (!message.data || !message.echo) return false;
        
        // 检查是否包含用户信息字段
        const userData = message.data;
        return (
            userData.user_id || 
            userData.uin || 
            (userData.nickname && userData.uid) ||
            (userData.nick && userData.uin)
        );
    }

    /**
     * 过滤和清理用户信息
     * @param {Object} rawData 原始用户数据
     * @returns {Object} 清理后的用户信息
     */
    filterUserInfo(rawData) {
        const filtered = {
            // 基本信息
            user_id: rawData.user_id || rawData.uin,
            uin: rawData.uin,
            uid: rawData.uid,
            nickname: rawData.nickname || rawData.nick,
            remark: rawData.remark,
            
            // 个人信息
            sex: rawData.sex,
            age: rawData.age,
            birthday_year: rawData.birthday_year,
            birthday_month: rawData.birthday_month,
            birthday_day: rawData.birthday_day,
            
            // 联系信息
            email: rawData.eMail,
            phone: rawData.phoneNum,
            
            // QQ相关
            qq_level: rawData.qqLevel,
            qid: rawData.qid,
            long_nick: rawData.longNick || rawData.long_nick,
            
            // 地理信息
            country: rawData.country,
            province: rawData.province,
            city: rawData.city,
            hometown: rawData.homeTown,
            
            // 教育信息
            college: rawData.college,
            
            // 状态信息
            status: rawData.status,
            is_vip: rawData.is_vip,
            vip_level: rawData.vip_level,
            
            // 时间信息
            reg_time: rawData.reg_time,
            last_update: Math.floor(Date.now() / 1000)
        };

        // 移除空值和无效值
        Object.keys(filtered).forEach(key => {
            if (filtered[key] === undefined || 
                filtered[key] === null || 
                filtered[key] === '' || 
                filtered[key] === '-' ||
                filtered[key] === 0) {
                delete filtered[key];
            }
        });

        return filtered;
    }

    /**
     * 保存用户信息到文件
     * @param {Object} userInfo 用户信息
     */
    async saveUserInfo(userInfo) {
        try {
            const userId = userInfo.user_id || userInfo.uin;
            if (!userId) return;

            const filePath = path.join(this.userDataDir, `${userId}.json`);
            
            // 读取现有数据（如果存在）
            let existingData = {};
            if (fs.existsSync(filePath)) {
                try {
                    const fileContent = fs.readFileSync(filePath, 'utf8');
                    existingData = JSON.parse(fileContent);
                } catch (error) {
                    console.error(`[UserInfoManager] 读取现有用户数据失败: ${error.message}`);
                }
            }

            // 合并数据（新数据优先）
            const mergedData = {
                ...existingData,
                ...userInfo,
                update_history: [
                    ...(existingData.update_history || []),
                    {
                        timestamp: userInfo.last_update,
                        date: new Date().toLocaleString('zh-CN')
                    }
                ].slice(-10) // 只保留最近10次更新记录
            };

            // 保存到文件
            fs.writeFileSync(filePath, JSON.stringify(mergedData, null, 2), 'utf8');
            
            console.log(`[UserInfoManager] 用户信息已保存: ${userId} (${userInfo.nickname || '未知'})`);
            
        } catch (error) {
            console.error(`[UserInfoManager] 保存用户信息失败: ${error.message}`);
        }
    }

    /**
     * 处理API响应消息
     * @param {Object} message API响应消息
     */
    async handleApiResponse(message) {
        try {
            if (!this.isUserInfoResponse(message)) return;

            const filteredInfo = this.filterUserInfo(message.data);
            if (Object.keys(filteredInfo).length > 2) { // 至少有基本信息
                await this.saveUserInfo(filteredInfo);
            }
            
        } catch (error) {
            console.error(`[UserInfoManager] 处理API响应失败: ${error.message}`);
        }
    }

    /**
     * 获取用户信息
     * @param {string} userId 用户ID
     * @returns {Object|null} 用户信息
     */
    getUserInfo(userId) {
        try {
            const filePath = path.join(this.userDataDir, `${userId}.json`);
            if (!fs.existsSync(filePath)) return null;

            const fileContent = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(fileContent);
            
        } catch (error) {
            console.error(`[UserInfoManager] 获取用户信息失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 获取所有用户列表
     * @returns {Array} 用户列表
     */
    getAllUsers() {
        try {
            const files = fs.readdirSync(this.userDataDir);
            const users = [];

            files.forEach(file => {
                if (file.endsWith('.json')) {
                    const userId = file.replace('.json', '');
                    const userInfo = this.getUserInfo(userId);
                    if (userInfo) {
                        users.push({
                            user_id: userId,
                            nickname: userInfo.nickname,
                            last_update: userInfo.last_update
                        });
                    }
                }
            });

            return users.sort((a, b) => (b.last_update || 0) - (a.last_update || 0));
            
        } catch (error) {
            console.error(`[UserInfoManager] 获取用户列表失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 清理过期数据
     * @param {number} daysOld 保留天数（默认30天）
     */
    cleanupOldData(daysOld = 30) {
        try {
            const cutoffTime = Math.floor(Date.now() / 1000) - (daysOld * 24 * 60 * 60);
            const files = fs.readdirSync(this.userDataDir);
            let cleanedCount = 0;

            files.forEach(file => {
                if (file.endsWith('.json')) {
                    const filePath = path.join(this.userDataDir, file);
                    const userInfo = this.getUserInfo(file.replace('.json', ''));
                    
                    if (userInfo && userInfo.last_update < cutoffTime) {
                        fs.unlinkSync(filePath);
                        cleanedCount++;
                    }
                }
            });

            console.log(`[UserInfoManager] 清理完成，删除了 ${cleanedCount} 个过期用户数据文件`);
            
        } catch (error) {
            console.error(`[UserInfoManager] 清理数据失败: ${error.message}`);
        }
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        try {
            const users = this.getAllUsers();
            const now = Math.floor(Date.now() / 1000);
            const dayAgo = now - (24 * 60 * 60);
            const weekAgo = now - (7 * 24 * 60 * 60);

            return {
                total_users: users.length,
                updated_today: users.filter(u => u.last_update > dayAgo).length,
                updated_this_week: users.filter(u => u.last_update > weekAgo).length,
                data_directory: this.userDataDir
            };
            
        } catch (error) {
            console.error(`[UserInfoManager] 获取统计信息失败: ${error.message}`);
            return { error: error.message };
        }
    }
}

module.exports = UserInfoManager;
