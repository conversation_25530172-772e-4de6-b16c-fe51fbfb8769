# 文件演示功能优化说明

## 🔧 最新优化 (2025-07-05)

### 核心问题修复：
- ✅ **修复消息段使用错误**：从`context.message`改为`context.segments`
- ✅ **优化字段映射**：修正OneBot11文件消息段字段映射
- ✅ **扩展媒体支持**：新增视频(video)和语音(record)类型支持
- ✅ **新增调试工具**：添加"消息段调试"功能

### 字段映射优化：
```javascript
// 修复前（错误）
extractFiles(message) {
    return message.filter(seg => seg.type === 'file')
        .map(seg => ({
            file: seg.data?.file || '',
            name: seg.data?.name || '',
            size: seg.data?.size || 0
        }));
}

// 修复后（正确）
extractFiles(message) {
    return message.filter(seg => seg.type === 'file')
        .map(seg => ({
            file: seg.data?.file_id || seg.data?.file || '',  // 优先file_id
            name: seg.data?.file || seg.data?.name || '',     // 文件名
            size: seg.data?.file_size || seg.data?.size || 0  // 文件大小
        }));
}
```

## 📋 使用方法

### 1. 文件演示功能
**触发方式**：
- 发送文件/图片/视频/语音 + "文件演示"
- 或在发送媒体文件时同时包含"文件演示"文字

**支持类型**：
- 📁 文件 (file)
- 🖼️ 图片 (image) 
- 🎬 视频 (video)
- 🎵 语音 (record)

**显示内容**：
- 媒体文件统计信息
- 每个文件的详细信息（名称、大小、链接等）
- NapCat API调用结果

### 2. 消息段调试功能（新增）
**触发方式**：发送"消息段调试"

**功能**：
- 显示原始消息段结构
- 统计各类型消息段数量
- 帮助排查消息解析问题

## 🧪 测试步骤

### 基础测试：
1. **消息段结构测试**：
   ```
   发送：消息段调试
   预期：显示消息段详细结构
   ```

2. **文件识别测试**：
   ```
   发送：[上传文件] + 文件演示
   预期：显示文件信息和API调用结果
   ```

3. **多媒体测试**：
   ```
   发送：[图片+视频+语音] + 文件演示
   预期：显示所有媒体文件的统计和详情
   ```

### 问题排查：
如果文件仍然无法识别：

1. **检查消息段结构**：
   - 发送"消息段调试"查看实际的segment.type
   - 确认是否为'file'、'image'、'video'、'record'

2. **检查控制台日志**：
   ```
   [DEBUG] 文件演示 - 消息段数量: X
   [DEBUG] 文件演示 - 消息段内容: {...}
   [DEBUG] 文件演示 - 提取到 X个文件, X个图片, X个视频, X个语音
   ```

3. **验证OneBot11协议**：
   - 确认OneBot11适配器正确解析了文件上传事件
   - 检查是否需要监听`notice.group_upload`事件而非message事件

## 🔍 技术细节

### OneBot11文件消息段结构：
```javascript
{
  type: "file",
  data: {
    file: "文件名",           // 文件名
    file_id: "文件ID",        // 文件唯一标识
    file_size: 1024          // 文件大小(字节)
  }
}
```

### 图片消息段结构：
```javascript
{
  type: "image", 
  data: {
    file: "图片文件名",
    url: "图片URL",
    file_size: 1024,
    summary: "图片描述"
  }
}
```

### 视频消息段结构：
```javascript
{
  type: "video",
  data: {
    file: "视频文件名",
    url: "视频URL", 
    file_size: 1024
  }
}
```

### 语音消息段结构：
```javascript
{
  type: "record",
  data: {
    file: "语音文件名",
    path: "文件路径",
    file_size: 1024
  }
}
```

## 🚀 预期效果

修复后的文件演示功能应该能够：
- ✅ 正确识别所有类型的媒体文件
- ✅ 显示准确的文件信息和统计
- ✅ 提供详细的调试信息
- ✅ 支持NapCat API文件信息获取

如果问题仍然存在，可能需要检查OneBot11协议层面的文件事件处理逻辑。
