{"manifestVersion": "1.0.0", "name": "WeatherReporter", "version": "1.0.0", "displayName": "天气预报员", "description": "提供实时的天气信息，支持当前天气、7天预报、24小时预报和天气预警，支持智能token截断。", "author": "System", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node weather-reporter.js"}, "communication": {"protocol": "stdio", "timeout": 30000}, "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{VCPWeatherInfo}}", "description": "当前的实时天气信息。"}], "invocationCommands": [{"commandIdentifier": "WeatherReporter", "description": "调用此工具获取指定城市的天气信息。支持当前天气、7天预报、24小时预报和天气预警查询。请在您的回复中，使用以下精确格式来请求，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」WeatherReporter「末」,\nquery_type:「始」(可选, 默认为 'current') 查询类型：'current'(当前天气), 'forecast'(7天预报), 'hourly'(24小时预报), 'warning'(天气预警), 'all'(全部信息)「末」,\ncity:「始」(可选，如未提供则使用配置的默认城市) 要查询天气的城市名称「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含天气信息的JSON对象，包括token使用统计信息。请基于这些结果回答用户的问题或完成相关任务。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」WeatherReporter「末」,\nquery_type:「始」current「末」,\ncity:「始」北京「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}, "configSchema": {"VarCity": {"type": "string", "description": "要查询天气的城市名称", "required": true}, "WeatherKey": {"type": "string", "description": "和风天气API密钥", "required": true}, "WeatherUrl": {"type": "string", "description": "和风天气API域名", "required": true}, "WEATHERREPORTER_TOKEN_TRUNCATE_ENABLED": {"type": "boolean", "description": "是否启用token截断功能", "required": false, "default": true}, "WEATHERREPORTER_TOKEN_MAX_TOKENS": {"type": "integer", "description": "最大token数量限制", "required": false, "default": 16000, "minimum": 1000, "maximum": 100000}, "WEATHERREPORTER_TOKEN_TRUNCATE_MARKER": {"type": "string", "description": "内容截断时的标记文本", "required": false, "default": "...\n\n[天气信息已截断，超过最大token限制]"}}, "refreshIntervalCron": "0 */8 * * *"}