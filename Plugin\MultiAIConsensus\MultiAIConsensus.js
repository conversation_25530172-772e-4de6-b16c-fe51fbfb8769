#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// VCP日志系统
const logger = {
    log: (level, component, msg, data) => {
        const output = `[${new Date().toLocaleString('zh-CN')}] [${level}] [${component}] ${msg}`;
        // VCP插件通过stderr输出日志，避免污染stdout
        console.error(output);
        if (data) console.error(data);
    },
    info: (component, msg, data) => logger.log('i', component, msg, data),
    error: (component, msg, data) => logger.log('x', component, msg, data),
    warning: (component, msg, data) => logger.log('!', component, msg, data)
};

class MultiAIConsensusVCP {
    constructor() {
        this.name = 'MultiAIConsensus';
        this.config = this.loadConfig();
    }

    loadConfig() {
        try {
            const mainConfigPath = path.join(__dirname, '../../config.env');
            let mainConfig = {};
            if (fs.existsSync(mainConfigPath)) {
                mainConfig = this.parseEnvConfig(fs.readFileSync(mainConfigPath, 'utf8'));
            }

            const pluginConfigPath = path.join(__dirname, './config.env');
            let pluginConfig = {};
            if (fs.existsSync(pluginConfigPath)) {
                pluginConfig = this.parseEnvConfig(fs.readFileSync(pluginConfigPath, 'utf8'));
            }

            return {
                apiUrl: this.getConfigValue(pluginConfig.MULTI_AI_API_URL, mainConfig.API_URL, 'https://yuanplus.cloud'),
                apiKey: this.getConfigValue(pluginConfig.MULTI_AI_API_KEY, mainConfig.YUANPLUS_API_KEY, ''),
                maxConcurrent: parseInt(pluginConfig.MULTI_AI_MAX_CONCURRENT) || 3,
                timeout: parseInt(pluginConfig.MULTI_AI_TIMEOUT) || 30000,
                availableModels: this.parseModelList(pluginConfig.MULTI_AI_AVAILABLE_MODELS) || ['gpt-4o', 'gpt-4o-mini', 'claude-3-5-sonnet-20241022'],
                defaultModels: this.parseModelList(pluginConfig.MULTI_AI_DEFAULT_MODELS) || ['gpt-4o-mini', 'claude-3-5-sonnet-20241022']
            };
        } catch (error) {
            logger.error(this.name, `配置加载错误: ${error.message}`);
            return this.getDefaultConfig();
        }
    }

    getConfigValue(pluginValue, mainValue, defaultValue) {
        return (pluginValue && pluginValue.trim()) || (mainValue && mainValue.trim()) || defaultValue;
    }

    parseModelList(value) {
        if (!value) return null;
        return value.split(',').map(s => s.trim()).filter(Boolean);
    }

    parseEnvConfig(content) {
        const config = {};
        content.split('\n').forEach(line => {
            if (line.trim() && !line.startsWith('#')) {
                const [key, ...valueParts] = line.split('=');
                if (key) {
                    config[key.trim()] = valueParts.join('=').trim().replace(/^["']|["']$/g, '');
                }
            }
        });
        return config;
    }

    getDefaultConfig() {
        return { apiUrl: 'https://yuanplus.cloud', apiKey: '', maxConcurrent: 3, timeout: 30000, availableModels: [], defaultModels: [] };
    }

    selectModels(requestedModels, maxModels) {
        let modelList = Array.isArray(requestedModels) ? requestedModels : [requestedModels];
        const validModels = modelList.filter(model => this.config.availableModels.includes(model));
        if (validModels.length === 0) {
            validModels.push(...this.config.defaultModels);
        }
        return [...new Set(validModels)].slice(0, Math.min(maxModels, this.config.maxConcurrent));
    }

    async querySingleAI(query, model) {
        const startTime = Date.now();
        try {
            logger.info(this.name, `向模型 ${model} 发送请求...`);
            const response = await axios.post(`${this.config.apiUrl}/v1/chat/completions`, {
                model,
                messages: [{ role: 'user', content: query }],
                temperature: 0.7,
                max_tokens: 2000
            }, {
                headers: { 'Authorization': `Bearer ${this.config.apiKey}`, 'Content-Type': 'application/json' },
                timeout: this.config.timeout
            });

            const duration = Date.now() - startTime;
            const content = response.data?.choices?.[0]?.message?.content || '无回复内容';
            logger.info(this.name, `模型 ${model} 响应成功 (${duration}ms)`);
            return { success: true, model, content, duration, tokens: response.data?.usage };
        } catch (error) {
            const duration = Date.now() - startTime;
            const errorMsg = error.response?.data?.error?.message || error.message;
            logger.error(this.name, `模型 ${model} 请求失败: ${errorMsg}`);
            return { success: false, model, error: errorMsg, duration };
        }
    }
    
    async execute(args) {
        const { query, models = this.config.defaultModels, maxModels = 3 } = args;

        if (!query) throw new Error('缺少必需参数: query');
        if (!this.config.apiKey) throw new Error('API密钥未配置 (MULTI_AI_API_KEY 或 YUANPLUS_API_KEY)');

        const selectedModels = this.selectModels(models, maxModels);
        logger.info(this.name, `选择的模型: ${selectedModels.join(', ')}`);

        const promises = selectedModels.map(model => this.querySingleAI(query, model));
        const responses = await Promise.all(promises);

        const validResponses = responses.filter(r => r.success);
        if (validResponses.length === 0) {
            const errors = responses.map(r => `${r.model}: ${r.error}`).join('; ');
            throw new Error(`所有AI模型请求失败 - ${errors}`);
        }

        let responseText = `📊 **多AI协商结果**\n**问题：** ${query}\n\n---\n\n`;
        validResponses.forEach(res => {
            responseText += `🤖 **${res.model}** (用时: ${res.duration}ms)\n\`\`\`\n${res.content}\n\`\`\`\n\n`;
        });

        const failedResponses = responses.filter(r => !r.success);
        if (failedResponses.length > 0) {
            responseText += `---\n\n**❌ 失败的模型:**\n`;
            failedResponses.forEach(res => {
                responseText += `- **${res.model}:** ${res.error}\n`;
            });
        }
        
        return {
            status: "success",
            result: responseText,
            details: {
                query,
                selected_models: selectedModels,
                responses: responses.map(r => ({
                    model: r.model,
                    success: r.success,
                    duration: r.duration,
                    ...(r.success ? { content: r.content, tokens: r.tokens } : { error: r.error })
                }))
            }
        };
    }
}

async function main() {
    let inputData = '';
    process.stdin.setEncoding('utf8');
    for await (const chunk of process.stdin) {
        inputData += chunk;
    }

    try {
        if (!inputData.trim()) throw new Error("未从stdin接收到数据");
        
        const args = JSON.parse(inputData);
        const plugin = new MultiAIConsensusVCP();
        const result = await plugin.execute(args);
        
        // VCP插件通过stdout输出JSON结果
        console.log(JSON.stringify(result));

    } catch (e) {
        const errorMessage = e.message || "未知错误";
        // 按照标准格式返回错误信息
        console.log(JSON.stringify({
            status: "error",
            result: null,
            error: errorMessage,
            details: {
                error_type: e.name || 'Error',
                error_message: errorMessage,
                timestamp: new Date().toISOString()
            }
        }));
        process.exit(1);
    }
}

main(); 
 