# NapCat API 封装使用指南

## 概述

本文档介绍如何使用封装的NapCat API功能，让插件开发更加简便。基于NapCat官方文档 (https://napneko.github.io/develop/api/doc) 进行封装。

## 快速开始

### 1. 在插件中引入API

```javascript
const { helper } = require('./plugin_helper');

class MyPlugin {
    async init(adapter, config) {
        // 创建NapCat API实例
        this.napCatAPI = helper.createNapCatAPI(adapter);
    }
}
```

### 2. 基础使用示例

```javascript
// 戳一戳用户
await this.napCatAPI.poke(userId, groupId); // groupId可选

// 群签到
await this.napCatAPI.group.sign(groupId);

// 翻译英文
const result = await this.napCatAPI.tools.translateEn2Zh(['hello', 'world']);
```

## API 分类详解

### 群聊相关 API (group)

#### 群签到
```javascript
const result = await this.napCatAPI.group.sign(groupId);
```

#### 群聊戳一戳
```javascript
const result = await this.napCatAPI.group.poke(groupId, userId);
```

#### 设置群聊已读
```javascript
const result = await this.napCatAPI.group.markAsRead(groupId);
```

#### 发送AI语音
```javascript
const result = await this.napCatAPI.group.sendAiRecord(groupId, text, character);
```

#### 获取AI角色列表
```javascript
const result = await this.napCatAPI.group.getAiCharacters(groupId, chatType);
```

#### 获取推荐群聊卡片
```javascript
const result = await this.napCatAPI.group.getShareCard(groupId);
```

### 私聊相关 API (friend)

#### 私聊戳一戳
```javascript
const result = await this.napCatAPI.friend.poke(userId);
```

#### 设置私聊已读
```javascript
const result = await this.napCatAPI.friend.markAsRead(userId);
```

#### 获取私聊历史记录
```javascript
const result = await this.napCatAPI.friend.getHistory(userId, messageSeq, count, reverseOrder);
```

#### 获取推荐好友卡片
```javascript
const result = await this.napCatAPI.friend.getShareCard(userId, phoneNumber);
```

### 消息相关 API (message)

#### 转发消息到私聊
```javascript
const result = await this.napCatAPI.message.forwardToFriend(messageId, userId);
```

#### 转发消息到群聊
```javascript
const result = await this.napCatAPI.message.forwardToGroup(messageId, groupId);
```

#### 设置表情回复
```javascript
const result = await this.napCatAPI.message.setEmojiLike(messageId, emojiId);
```

#### 发送合并转发
```javascript
const result = await this.napCatAPI.message.sendForward(messageType, targetId, messages);
```

### 文件相关 API (file)

#### 获取文件信息
```javascript
const result = await this.napCatAPI.file.getInfo(fileId);
// 返回: { file, url, file_size, file_name, base64 }
```

### 用户相关 API (user)

#### 设置在线状态
```javascript
const result = await this.napCatAPI.user.setOnlineStatus(status, extStatus, batteryStatus);
```

#### 设置QQ头像
```javascript
const result = await this.napCatAPI.user.setAvatar(file);
```

#### 设置签名
```javascript
const result = await this.napCatAPI.user.setSignature(longNick);
```

#### 获取分类的好友列表
```javascript
const result = await this.napCatAPI.user.getFriendsWithCategory();
```

#### 获取自身点赞列表
```javascript
const result = await this.napCatAPI.user.getProfileLike();
```

### 工具相关 API (tools)

#### 英译中
```javascript
const result = await this.napCatAPI.tools.translateEn2Zh(['hello', 'world']);
// 返回: ['你好', '世界']
```

#### AI文字转语音
```javascript
const result = await this.napCatAPI.tools.getAiRecord(character, groupId, text);
```

#### 获取机器人账号范围
```javascript
const result = await this.napCatAPI.tools.getRobotUinRange();
```

#### 获取自定义表情
```javascript
const result = await this.napCatAPI.tools.getCustomFace(count);
```

#### 获取最近联系人
```javascript
const result = await this.napCatAPI.tools.getRecentContact(count);
```

#### 标记所有已读
```javascript
const result = await this.napCatAPI.tools.markAllAsRead();
```

### 收藏相关 API (collection)

#### 创建收藏
```javascript
const result = await this.napCatAPI.collection.create(data);
```

#### 获取收藏列表
```javascript
const result = await this.napCatAPI.collection.getList();
```

## 快速调用方法 (quick)

为常用功能提供的简化调用：

```javascript
// 快速戳一戳
await this.napCatAPI.quick.poke(userId, groupId);

// 快速群签到
await this.napCatAPI.quick.groupSign(groupId);

// 快速设置已读
await this.napCatAPI.quick.markRead(target, isGroup);

// 快速获取文件信息
await this.napCatAPI.quick.getFile(fileId);

// 快速翻译
await this.napCatAPI.quick.translate(['hello']);

// 快速AI语音
await this.napCatAPI.quick.aiSpeak(character, groupId, text);
```

## 错误处理

所有API调用都包含错误处理，返回格式：

```javascript
// 成功时
{ success: true, data: ... }

// 失败时
{ success: false, error: "错误信息" }
```

使用示例：
```javascript
const result = await this.napCatAPI.group.sign(groupId);
if (result.success === false) {
    console.error('群签到失败:', result.error);
    return;
}
console.log('群签到成功');
```

## 完整插件示例

```javascript
const { helper } = require('./plugin_helper');
const MessageBuilder = require('./message_builder');

class MyNapCatPlugin {
    constructor() {
        this.name = 'my_napcat_plugin';
        this.priority = 50;
        this.permission = 'all';
    }

    async init(adapter, config) {
        this.adapter = adapter;
        this.napCatAPI = helper.createNapCatAPI(adapter);
    }

    async handleMessage(context) {
        const text = helper.extractText(context.message);
        
        if (text === '签到') {
            const result = await this.napCatAPI.group.sign(context.group_id);
            const reply = result.success !== false ? '✅ 签到成功' : `❌ 签到失败: ${result.error}`;
            await this.reply(context, reply);
            return { handled: true };
        }
        
        if (text.startsWith('戳 ')) {
            const userId = text.replace('戳 ', '').trim();
            await this.napCatAPI.poke(userId, context.group_id);
            return { handled: true };
        }
        
        return { handled: false };
    }

    async reply(context, message) {
        if (context.message_type === 'group') {
            await this.adapter.sendGroupMessage(context.group_id, message);
        } else {
            await this.adapter.sendPrivateMessage(context.user_id, message);
        }
    }
}

module.exports = MyNapCatPlugin;
```

## 演示插件

系统包含 `napcat_demo_plugin.js` 演示插件，可以通过以下命令体验：

- `napcat演示` - 显示功能菜单
- `戳一戳演示` - 演示戳一戳功能
- `群功能演示` - 演示群聊相关功能
- `ai语音演示` - 演示AI语音功能
- `文件演示` - 演示文件处理功能
- `翻译:英文内容` - 演示翻译功能

## 注意事项

1. **权限要求**: 某些API需要特定权限，请确保机器人有足够权限
2. **参数类型**: API会自动进行类型转换，但建议传入正确类型
3. **错误处理**: 始终检查返回结果的 `success` 字段
4. **频率限制**: 注意API调用频率，避免被限制
5. **群聊限制**: 某些功能仅在群聊中可用

## 更多信息

- NapCat官方文档: https://napneko.github.io/develop/api/doc
- OneBot11标准: https://github.com/botuniverse/onebot-11
- 插件开发指南: README_插件开发辅助工具.md
