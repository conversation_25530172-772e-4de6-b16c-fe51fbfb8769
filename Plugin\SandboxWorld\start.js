/**
 * 沙盒世界启动脚本
 * 独立启动沙盒世界系统
 */

const path = require('path');
const fs = require('fs');

// 加载环境配置
function loadEnvConfig() {
    const envPath = path.join(__dirname, 'config.env');
    if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf-8');
        const lines = envContent.split('\n');
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed && !trimmed.startsWith('#')) {
                const [key, value] = trimmed.split('=');
                if (key && value) {
                    process.env[key.trim()] = value.trim();
                }
            }
        }
    }
}

// 加载配置
loadEnvConfig();

const SandboxWorld = require('./SandboxWorld');

class SandboxWorldStarter {
    constructor() {
        this.sandboxWorld = null;
        this.isRunning = false;
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[沙盒世界] [${timestamp}]`;
        
        switch (type) {
            case 'error':
                console.error(`❌ ${prefix} ${message}`);
                break;
            case 'warning':
                console.warn(`⚠️ ${prefix} ${message}`);
                break;
            case 'success':
                console.log(`✅ ${prefix} ${message}`);
                break;
            default:
                console.log(`ℹ️ ${prefix} ${message}`);
        }
    }

    async checkDependencies() {
        this.log('检查依赖...');
        
        try {
            require('express');
            require('socket.io');
            this.log('依赖检查通过', 'success');
        } catch (error) {
            throw new Error(`缺少依赖: ${error.message}. 请运行: npm install`);
        }
    }

    async checkDataDirectories() {
        this.log('检查数据目录...');
        
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
            this.log('创建数据目录', 'success');
        }

        const agentsDir = path.join(dataDir, 'agents');
        if (!fs.existsSync(agentsDir)) {
            fs.mkdirSync(agentsDir, { recursive: true });
            this.log('创建Agent数据目录', 'success');
        }

        const eventsDir = path.join(dataDir, 'events');
        if (!fs.existsSync(eventsDir)) {
            fs.mkdirSync(eventsDir, { recursive: true });
            this.log('创建事件数据目录', 'success');
        }
    }

    async initializeSystem() {
        this.log('初始化沙盒世界系统...');
        
        try {
            // 创建沙盒世界实例
            this.sandboxWorld = SandboxWorld;
            
            // 初始化系统
            const initResult = await this.sandboxWorld.execute(JSON.stringify({
                action: 'init'
            }));
            
            if (initResult.status !== 'success') {
                throw new Error(initResult.message || '初始化失败');
            }
            
            this.log('系统初始化完成', 'success');
            return initResult;
            
        } catch (error) {
            throw new Error(`初始化失败: ${error.message}`);
        }
    }

    async startSystem() {
        this.log('启动沙盒世界...');
        
        try {
            const startResult = await this.sandboxWorld.execute(JSON.stringify({
                action: 'start'
            }));
            
            if (startResult.status !== 'success') {
                throw new Error(startResult.message || '启动失败');
            }
            
            this.isRunning = true;
            this.log('沙盒世界启动成功！', 'success');
            
            // 显示访问信息
            const webPort = process.env.WEB_INTERFACE_PORT || 8080;
            this.log(`Web管理界面: http://localhost:${webPort}`, 'success');
            
            return startResult;
            
        } catch (error) {
            throw new Error(`启动失败: ${error.message}`);
        }
    }

    async addSampleAgents() {
        this.log('添加示例Agent...');
        
        const sampleAgents = [
            { name: '小明', age: 25, gender: '男' },
            { name: '小红', age: 23, gender: '女' },
            { name: '小李', age: 28, gender: '男' }
        ];

        for (const agentConfig of sampleAgents) {
            try {
                const result = await this.sandboxWorld.execute(JSON.stringify({
                    action: 'addAgent',
                    agentConfig
                }));
                
                if (result.status === 'success') {
                    this.log(`添加Agent: ${agentConfig.name}`, 'success');
                } else {
                    this.log(`添加Agent失败: ${agentConfig.name} - ${result.message}`, 'warning');
                }
            } catch (error) {
                this.log(`添加Agent错误: ${agentConfig.name} - ${error.message}`, 'warning');
            }
        }
    }

    setupSignalHandlers() {
        // 优雅关闭处理
        const gracefulShutdown = async (signal) => {
            this.log(`接收到 ${signal} 信号，正在关闭系统...`);
            
            if (this.sandboxWorld && this.isRunning) {
                try {
                    await this.sandboxWorld.execute(JSON.stringify({
                        action: 'stop'
                    }));
                    this.log('系统已安全关闭', 'success');
                } catch (error) {
                    this.log(`关闭时发生错误: ${error.message}`, 'error');
                }
            }
            
            process.exit(0);
        };

        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        
        // 错误处理
        process.on('uncaughtException', (error) => {
            this.log(`未捕获的异常: ${error.message}`, 'error');
            console.error(error.stack);
        });

        process.on('unhandledRejection', (reason, promise) => {
            this.log(`未处理的Promise拒绝: ${reason}`, 'error');
            console.error('Promise:', promise);
        });
    }

    async start() {
        try {
            this.log('🌍 启动VCPToolBox沙盒世界系统...');
            this.log('');
            
            // 设置信号处理
            this.setupSignalHandlers();
            
            // 检查环境
            await this.checkDependencies();
            await this.checkDataDirectories();
            
            // 初始化和启动
            await this.initializeSystem();
            await this.startSystem();
            
            // 添加示例Agent（可选）
            const addSamples = process.env.ADD_SAMPLE_AGENTS !== 'false';
            if (addSamples) {
                setTimeout(() => {
                    this.addSampleAgents();
                }, 3000); // 3秒后添加示例Agent
            }
            
            this.log('');
            this.log('🎉 沙盒世界系统运行中！');
            this.log('按 Ctrl+C 安全关闭系统');
            this.log('');
            
            // 定期显示状态
            setInterval(async () => {
                try {
                    const statusResult = await this.sandboxWorld.execute(JSON.stringify({
                        action: 'getStatus'
                    }));
                    
                    if (statusResult.status === 'success' && statusResult.data) {
                        const stats = statusResult.data.statistics;
                        if (stats) {
                            this.log(`状态 - Agent: ${stats.population || 0}, 事件: ${stats.totalEvents || 0}, 运行时间: ${Math.floor((statusResult.data.worldState.uptime || 0) / 60000)}分钟`);
                        }
                    }
                } catch (error) {
                    // 静默处理状态检查错误
                }
            }, 60000); // 每分钟显示一次状态
            
        } catch (error) {
            this.log(`启动失败: ${error.message}`, 'error');
            console.error(error.stack);
            process.exit(1);
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const starter = new SandboxWorldStarter();
    starter.start();
}

module.exports = SandboxWorldStarter;
