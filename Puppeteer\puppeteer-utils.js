/**
 * Puppeteer兼容性工具
 * 处理不同版本之间的API差异，自动检测浏览器路径
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../utils/logger.cjs' 
        : '../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

/**
 * 检测系统中的Chrome/Chromium浏览器路径
 */
function detectChromePath() {
    const platform = os.platform();
    const possiblePaths = [];

    if (platform === 'win32') {
        // Windows 平台的可能路径
        possiblePaths.push(
            // Chrome 标准安装路径
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
            // Edge 浏览器
            'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
            'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
            // Chromium
            'C:\\Program Files\\Chromium\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Chromium\\Application\\chrome.exe',
            // 用户目录安装
            path.join(os.homedir(), 'AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'),
            path.join(os.homedir(), 'AppData\\Local\\Microsoft\\Edge\\Application\\msedge.exe'),
            // 便携版
            path.join(process.cwd(), 'chrome\\chrome.exe'),
            path.join(process.cwd(), 'chromium\\chrome.exe')
        );
    } else if (platform === 'darwin') {
        // macOS 平台的可能路径
        possiblePaths.push(
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
            '/Applications/Chromium.app/Contents/MacOS/Chromium',
            '/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge'
        );
    } else {
        // Linux 平台的可能路径
        possiblePaths.push(
            '/usr/bin/google-chrome',
            '/usr/bin/google-chrome-stable',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium',
            '/snap/bin/chromium',
            '/usr/bin/microsoft-edge',
            '/opt/google/chrome/chrome',
            '/opt/chromium.org/chromium/chromium'
        );
    }

    // 检查路径是否存在
    for (const chromePath of possiblePaths) {
        try {
            if (fs.existsSync(chromePath)) {
                logger.info('调试日志', `找到浏览器: ${chromePath}`);
                return chromePath;
            }
        } catch (error) {
            continue;
        }
    }

    return null;
}

/**
 * 尝试通过命令行查找浏览器
 */
function findBrowserByCommand() {
    const platform = os.platform();
    const commands = [];

    if (platform === 'win32') {
        commands.push(
            'where chrome',
            'where msedge',
            'where chromium'
        );
    } else if (platform === 'darwin') {
        commands.push(
            'which google-chrome',
            'which chromium',
            'which microsoft-edge'
        );
    } else {
        commands.push(
            'which google-chrome',
            'which google-chrome-stable', 
            'which chromium-browser',
            'which chromium',
            'which microsoft-edge'
        );
    }

    for (const command of commands) {
        try {
            const result = execSync(command, { encoding: 'utf8', timeout: 5000 }).trim();
            if (result && fs.existsSync(result)) {
                logger.info('调试日志', `通过命令找到浏览器: ${result}`);
                return result;
            }
        } catch (error) {
            continue;
        }
    }

    return null;
}

/**
 * 获取优化的Puppeteer启动配置
 */
function getOptimizedLaunchOptions(customOptions = {}) {
    const detectedPath = detectChromePath() || findBrowserByCommand();
    
    const baseOptions = {
        headless: 'new',
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--disable-gpu-sandbox',
            '--disable-web-security',
            '--allow-file-access-from-files',
            '--font-render-hinting=none',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-ipc-flooding-protection',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-default-apps',
            '--disable-component-extensions-with-background-pages',
            '--disable-background-networking',
            '--no-first-run',
            '--no-default-browser-check',
            '--single-process'
        ],
        ignoreHTTPSErrors: true,
        defaultViewport: null,
        pipe: true // 使用pipe而不是websocket
    };

    // 如果找到了浏览器路径，使用它
    if (detectedPath) {
        baseOptions.executablePath = detectedPath;
    } else {
        logger.warning('未找到系统浏览器，尝试使用Puppeteer自带的Chromium');
        // 可以尝试安装Chromium
        try {
            const puppeteer = require('puppeteer');
            // 某些情况下可能需要手动下载
            baseOptions.downloadPath = path.join(__dirname, '.local-chromium');
        } catch (error) {
            logger.warning('Puppeteer初始化失败:', error.message);
        }
    }

    return { ...baseOptions, ...customOptions };
}

/**
 * 尝试安装Chromium浏览器
 */
async function ensureChromiumInstalled() {
    try {
        logger.info('调试日志', '正在检查Chromium安装状态...');
        
        // 尝试使用puppeteer-core和@puppeteer/browsers
        try {
            const { install } = require('@puppeteer/browsers');
            const browserFetcher = require('puppeteer-core/lib/cjs/puppeteer/node/BrowserFetcher');
            
            logger.info('调试日志', '开始下载Chromium浏览器...');
            const revision = await install({
                browser: 'chrome',
                buildId: 'latest'
            });
            
            logger.info('调试日志', 'Chromium安装完成:', revision);
            return revision;
        } catch (installError) {
            logger.warning('自动安装失败:', installError.message);
        }

        // 降级方案：使用旧版API
        try {
            const puppeteer = require('puppeteer');
            const browserFetcher = puppeteer.createBrowserFetcher();
            
            const revisions = await browserFetcher.localRevisions();
            if (revisions.length > 0) {
                logger.info('调试日志', '找到本地Chromium版本:', revisions);
                return revisions[0];
            }
            
            logger.info('调试日志', '开始下载Chromium浏览器...');
            const revisionInfo = await browserFetcher.download('1069273'); // 使用稳定版本
            logger.info('调试日志', 'Chromium下载完成:', revisionInfo.executablePath);
            return revisionInfo.executablePath;
        } catch (fallbackError) {
            logger.warning('降级安装方案也失败:', fallbackError.message);
        }

    } catch (error) {
        logger.warning('无法安装Chromium:', error.message);
        logger.info('调试日志', '请手动安装Chrome或Chromium浏览器');
        return null;
    }
}

/**
 * 智能初始化Puppeteer浏览器
 */
async function smartLaunchBrowser(customOptions = {}) {
    let lastError = null;
    const puppeteer = require('puppeteer');
    
    // 策略1：使用检测到的浏览器路径
    try {
        const optimizedOptions = getOptimizedLaunchOptions(customOptions);
        logger.info('调试日志', '尝试启动浏览器，配置:', {
            executablePath: optimizedOptions.executablePath || '默认',
            headless: optimizedOptions.headless,
            args: optimizedOptions.args?.slice(0, 5).join(', ') + '...'
        });
        
        return await puppeteer.launch(optimizedOptions);
    } catch (error) {
        lastError = error;
        logger.warning('策略1失败:', error.message);
    }

    // 策略2：使用Puppeteer默认配置
    try {
        logger.info('调试日志', '尝试使用Puppeteer默认配置...');
        const defaultOptions = {
            ...customOptions,
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        };
        return await puppeteer.launch(defaultOptions);
    } catch (error) {
        lastError = error;
        logger.warning('策略2失败:', error.message);
    }

    // 策略3：尝试安装Chromium
    try {
        logger.info('调试日志', '尝试安装Chromium浏览器...');
        const chromiumPath = await ensureChromiumInstalled();
        
        if (chromiumPath) {
            const installOptions = {
                ...customOptions,
                executablePath: chromiumPath,
                headless: 'new',
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            };
            return await puppeteer.launch(installOptions);
        }
    } catch (error) {
        lastError = error;
        logger.warning('策略3失败:', error.message);
    }

    // 策略4：最简配置
    try {
        logger.info('调试日志', '尝试最简配置...');
        const minimalOptions = {
            headless: true,
            args: ['--no-sandbox']
        };
        return await puppeteer.launch(minimalOptions);
    } catch (error) {
        lastError = error;
        logger.warning('策略4失败:', error.message);
    }

    // 所有策略都失败了
    logger.warning('所有浏览器启动策略都失败了');
    logger.warning('最后一个错误:', lastError?.message);
    logger.info('调试日志', '\n解决方案:');
    logger.info('调试日志', '1. 安装Google Chrome: https://www.google.com/chrome/');
    logger.info('调试日志', '2. 安装Microsoft Edge: https://www.microsoft.com/edge');
    logger.info('调试日志', '3. 运行: npm install puppeteer (重新安装Puppeteer)');
    logger.info('调试日志', '4. 设置环境变量PUPPETEER_EXECUTABLE_PATH指向浏览器路径');
    
    throw new Error(`无法启动浏览器: ${lastError?.message}`);
}

/**
 * 获取 Puppeteer 版本信息
 */
function getPuppeteerVersion() {
    try {
        const puppeteer = require('puppeteer');
        return {
            version: puppeteer.version || '未知',
            isNew: puppeteer.version >= '10.0.0'
        };
    } catch (error) {
        return {
            version: '未知',
            isNew: false,
            error: error.message
        };
    }
}

/**
 * 检查页面API兼容性
 * @param {Page} page Puppeteer页面实例
 */
function checkPageAPICompatibility(page) {
    const compatibility = {
        waitForTimeout: typeof page.waitForTimeout === 'function',
        waitForFunction: typeof page.waitForFunction === 'function',
        screenshot: typeof page.screenshot === 'function',
        evaluate: typeof page.evaluate === 'function',
        setViewport: typeof page.setViewport === 'function'
    };

    return {
        ...compatibility,
        isFullyCompatible: Object.values(compatibility).every(v => v === true)
    };
}

/**
 * 兼容性等待超时方法
 * @param {Page} page Puppeteer页面实例
 * @param {number} timeout 等待时间（毫秒）
 */
async function compatibleWaitForTimeout(page, timeout) {
    if (typeof page.waitForTimeout === 'function') {
        await page.waitForTimeout(timeout);
    } else {
        await new Promise(resolve => setTimeout(resolve, timeout));
    }
}

/**
 * 兼容性等待函数执行方法
 * @param {Page} page Puppeteer页面实例
 * @param {Function} pageFunction 要执行的函数
 * @param {Object} options 选项
 */
async function compatibleWaitForFunction(page, pageFunction, options = {}) {
    if (typeof page.waitForFunction === 'function') {
        await page.waitForFunction(pageFunction, options);
    } else {
        let startTime = Date.now();
        const timeout = options.timeout || 30000;
        
        while (Date.now() - startTime < timeout) {
            const result = await page.evaluate(pageFunction);
            if (result) return;
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error('等待函数执行超时');
    }
}

/**
 * 兼容性截图方法
 * @param {Page} page Puppeteer页面实例
 * @param {Object} options 截图选项
 */
async function compatibleScreenshot(page, options = {}) {
    if (typeof page.screenshot !== 'function') {
        throw new Error('页面不支持截图功能');
    }

    try {
        return await page.screenshot(options);
    } catch (error) {
        // 处理特定的错误情况
        if (error.message.includes('Target closed')) {
            throw new Error('页面已关闭，无法截图');
        }
        if (error.message.includes('Protocol error')) {
            throw new Error('协议错误，截图失败');
        }
        throw error;
    }
}

/**
 * 安全的页面设置方法
 * @param {Page} page Puppeteer页面实例
 * @param {Object} options 配置选项
 */
async function safePageSetup(page, options = {}) {
    const {
        timeout = 30000,
        preloadScript = null
    } = options;

    try {
        // 设置默认超时
        page.setDefaultTimeout(timeout);

        // 禁用JavaScript对话框
        page.on('dialog', async dialog => {
            await dialog.dismiss();
        });

        // 忽略页面错误
        page.on('error', error => {
            logger.warning('页面错误:', error.message);
        });

        // 忽略控制台错误
        page.on('pageerror', error => {
            logger.warning('页面JavaScript错误:', error.message);
        });

        // 设置请求拦截
        await page.setRequestInterception(true);
        page.on('request', request => {
            if (request.resourceType() === 'image' || request.resourceType() === 'media') {
                request.continue();
            } else {
                request.continue();
            }
        });

        // 注入预加载脚本
        if (preloadScript && typeof preloadScript === 'function') {
            await page.evaluateOnNewDocument(preloadScript);
        }

    } catch (error) {
        logger.warning('页面设置失败:', error);
        throw error;
    }
}

module.exports = {
    // 浏览器检测和启动
    detectChromePath,
    findBrowserByCommand,
    getOptimizedLaunchOptions,
    ensureChromiumInstalled,
    smartLaunchBrowser,
    // 原有功能
    getPuppeteerVersion,
    checkPageAPICompatibility,
    compatibleWaitForTimeout,
    compatibleWaitForFunction,
    compatibleScreenshot,
    safePageSetup
}; 