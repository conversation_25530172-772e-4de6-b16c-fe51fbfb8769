{"name": "AdvancedMemorySystem", "version": "2.0.0", "description": "智能情感记忆系统 - 基于OpenAI Tools的高级记忆和情感分析插件", "author": "VCPToolBox Team", "main": "PluginInterface.js", "type": "memory", "capabilities": ["processConversation", "generateContext", "getUserAffinity", "getMemoryStats"], "dependencies": {"sqlite3": "^5.1.6", "openai": "^4.0.0"}, "config": {"EMOTION_MEMORY_ENABLED": true, "AFFINITY_TRACKING_ENABLED": true, "CONCEPT_LEARNING_ENABLED": true, "BACKGROUND_THREADS_ENABLED": true, "ENABLE_DETAILED_LOGGING": true, "LOG_LEVEL": "info", "TRANSACTION_TIMEOUT": 30, "ENABLE_ROLLBACK_ON_ERROR": true, "INTELLIGENT_ANALYSIS_ENABLED": true, "INTELLIGENT_ANALYSIS": {"tools_timeout": 15000, "fallback_enabled": true, "cache_enabled": true, "tools_weight": 0.7, "local_weight": 0.3, "emotion_threshold": 0.1, "stress_threshold": 0.05, "affinity_threshold": 0.02, "analysis_mode": "comprehensive", "require_detailed_reasoning": true, "nlp_config": {"enable_jieba": true, "enable_sentiment": true, "enable_complexity_analysis": true, "enable_semantic_similarity": true}}}, "database": {"type": "sqlite3", "file": "data/emotion_memory.db"}, "api": {"processConversation": {"description": "处理用户对话并进行情感分析和记忆存储", "parameters": ["userId", "userMessage", "aiResponse", "timestamp", "<PERSON><PERSON><PERSON>"]}, "generateContext": {"description": "生成智能上下文信息", "parameters": ["userId", "currentMessage", "maxContextSize", "<PERSON><PERSON><PERSON>"]}, "getUserAffinity": {"description": "获取用户好感度信息", "parameters": ["userId"]}, "getMemoryStats": {"description": "获取记忆系统统计信息", "parameters": []}}}