#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
import os
import time
import requests
import logging
import re

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from utils.token_utils import process_content_with_tokens
except ImportError:
    # 如果导入失败，使用简单的回退函数
    def process_content_with_tokens(content, plugin_name=''):
        return {
            'content': content,
            'original_tokens': len(content) // 4,
            'final_tokens': len(content) // 4,
            'truncated': False,
            'config': {'enabled': False}
        }

# --- Logging Setup ---
# Log to stderr to avoid interfering with stdout communication
logging.basicConfig(level=logging.INFO, stream=sys.stderr, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Constants ---
BILIBILI_VIDEO_BASE_URL = "https://www.bilibili.com/video/"
PAGELIST_API_URL = "https://api.bilibili.com/x/player/pagelist"
PLAYER_WBI_API_URL = "https://api.bilibili.com/x/player/wbi/v2"

# --- Helper Functions ---

def extract_bvid(video_input: str) -> str | None:
    """Extracts BV ID from URL or direct input."""
    match = re.search(r'bilibili\.com/video/(BV[a-zA-Z0-9]+)', video_input, re.IGNORECASE)
    if match:
        return match.group(1)
    match = re.match(r'^(BV[a-zA-Z0-9]+)$', video_input, re.IGNORECASE)
    if match:
        return match.group(1)
    return None

def get_subtitle_json_string(bvid: str, user_cookie: str | None) -> str:
    """
    Fetches the first available subtitle JSON for a given BVID.
    Returns the subtitle content as a JSON string or '{"body":[]}' if none found or error.
    Uses user_cookie if provided.
    """
    logging.info(f"Attempting to fetch subtitles for BVID: {bvid}")
    # --- Headers ---
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': f'{BILIBILI_VIDEO_BASE_URL}{bvid}/',
        'Origin': 'https://www.bilibili.com',
        'Connection': 'keep-alive',
    }

    # --- Cookie Handling ---
    if user_cookie:
        logging.info("Using user-provided cookie.")
        headers['Cookie'] = user_cookie
    else:
        logging.warning("User cookie not provided. Access may be limited or fail.")

    # --- Step 1: Get AID (Attempt from video page) ---
    aid = None
    try:
        logging.info(f"Step 1: Fetching video page for AID: {BILIBILI_VIDEO_BASE_URL}{bvid}/")
        resp = requests.get(f'{BILIBILI_VIDEO_BASE_URL}{bvid}/', headers=headers, timeout=10)
        resp.raise_for_status()
        text = resp.text
        aid_match = re.search(r'"aid"\s*:\s*(\d+)', text)
        if aid_match:
            aid = aid_match.group(1)
            logging.info(f"Step 1: Found AID via regex: {aid}")
        else:
            state_match = re.search(r'window\.__INITIAL_STATE__\s*=\s*(\{.*?\});?', text)
            if state_match:
                try:
                    initial_state = json.loads(state_match.group(1))
                    aid = initial_state.get('videoData', {}).get('aid')
                    if aid:
                        aid = str(aid)
                        logging.info(f"Step 1: Found AID in __INITIAL_STATE__: {aid}")
                    else:
                        logging.warning("Step 1: Could not find AID in __INITIAL_STATE__.")
                except json.JSONDecodeError:
                    logging.warning("Step 1: Failed to parse __INITIAL_STATE__ for AID.")
            else:
                 logging.warning("Step 1: Could not find AID in page HTML using regex or __INITIAL_STATE__.")
    except requests.exceptions.RequestException as e:
        logging.warning(f"Step 1: Error fetching video page for AID: {e}. Proceeding without AID.")
    except Exception as e:
         logging.warning(f"Step 1: Unexpected error fetching AID: {e}. Proceeding without AID.")


    # --- Step 2: Get CID (from pagelist API) ---
    cid = None
    try:
        logging.info(f"Step 2: Fetching CID from pagelist API: {PAGELIST_API_URL}?bvid={bvid}")
        pagelist_headers = headers.copy()
        cid_back = requests.get(PAGELIST_API_URL, params={'bvid': bvid}, headers=pagelist_headers, timeout=10)
        cid_back.raise_for_status()
        cid_json = cid_back.json()
        if cid_json.get('code') == 0 and cid_json.get('data') and len(cid_json['data']) > 0:
            cid = cid_json['data'][0]['cid']
            part_title = cid_json['data'][0]['part']
            logging.info(f"Step 2: Found CID: {cid} for part: {part_title}")
        else:
            logging.error(f"Step 2: Failed to get CID from pagelist. Code: {cid_json.get('code')}, Message: {cid_json.get('message')}")
            return json.dumps({"body":[]}) # Cannot proceed without CID
    except requests.exceptions.RequestException as e:
        logging.error(f"Step 2: Error fetching pagelist: {e}")
        return json.dumps({"body":[]})
    except (json.JSONDecodeError, KeyError, IndexError) as e:
         logging.error(f"Step 2: Error parsing pagelist response: {e}")
         return json.dumps({"body":[]})


    # --- Step 3: Get Subtitle List (using WBI API) ---
    subtitle_url = None
    try:
        logging.info("Step 3: Fetching subtitle list using WBI Player API...")
        wbi_params = {
            'cid': cid,
            'bvid': bvid,
            'isGaiaAvoided': 'false',
            'web_location': '1315873',
            'w_rid': '364cdf378b75ef6a0cee77484ce29dbb', # Hardcoded - might break
            'wts': int(time.time()),
        }
        if aid:
             wbi_params['aid'] = aid

        wbi_resp = requests.get(PLAYER_WBI_API_URL, params=wbi_params, headers=headers, timeout=15)
        logging.info(f"Step 3: WBI API Status Code: {wbi_resp.status_code}")

        wbi_data = wbi_resp.json()
        logging.debug(f"Step 3: WBI API Response Data: {json.dumps(wbi_data)}")

        if wbi_data.get('code') == 0:
            subtitles = wbi_data.get('data', {}).get('subtitle', {}).get('subtitles', [])
            if subtitles:
                first_subtitle = subtitles[0]
                subtitle_url = first_subtitle.get('subtitle_url')
                lan_doc = first_subtitle.get('lan_doc', 'Unknown Language')
                if subtitle_url:
                    if subtitle_url.startswith('//'):
                        subtitle_url = "https:" + subtitle_url
                    logging.info(f"Step 3: Found subtitle URL ({lan_doc}): {subtitle_url}")
                else:
                    logging.warning("Step 3: First subtitle entry found but is missing 'subtitle_url'.")
            else:
                logging.warning("Step 3: WBI API successful but no subtitles listed in response.")
        else:
            logging.warning(f"Step 3: WBI API returned error code {wbi_data.get('code')}: {wbi_data.get('message', 'Unknown error')}")
            if not wbi_resp.ok:
                 wbi_resp.raise_for_status()


    except requests.exceptions.RequestException as e:
        logging.error(f"Step 3: Error fetching subtitle list from WBI API: {e}")
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        logging.error(f"Step 3: Error parsing WBI API response: {e}")
    except Exception as e:
        logging.error(f"Step 3: Unexpected error during WBI API call: {e}")

    # --- Step 4: Fetch Subtitle Content ---
    if subtitle_url:
        try:
            logging.info(f"Step 4: Fetching subtitle content from: {subtitle_url}")
            subtitle_resp = requests.get(subtitle_url, headers=headers, timeout=15)
            subtitle_resp.raise_for_status()
            subtitle_text = subtitle_resp.text
            try:
                parsed_subtitle = json.loads(subtitle_text)
                if isinstance(parsed_subtitle, dict) and 'body' in parsed_subtitle:
                    logging.info(f"Step 4: Successfully fetched and validated subtitle content (Length: {len(subtitle_text)}).")
                    return subtitle_text # Return the raw JSON string
                else:
                    logging.error("Step 4: Fetched content is valid JSON but missing 'body' key.")
                    return json.dumps({"body":[]})
            except json.JSONDecodeError:
                 logging.error("Step 4: Fetched content is not valid JSON.")
                 return json.dumps({"body":[]})
        except requests.exceptions.RequestException as e:
            logging.error(f"Step 4: Error fetching subtitle content: {e}")
    else:
        logging.warning("Step 4: No subtitle URL found in Step 3.")

    # --- Fallback: Return empty if no subtitle found/fetched ---
    logging.info("Returning empty subtitle list.")
    return json.dumps({"body":[]})


# --- Main execution for VCP Synchronous Plugin ---

def process_bilibili_url(video_input: str) -> str:
    """
    Processes a Bilibili URL or BV ID to fetch and return subtitle text.
    Reads cookie from BILIBILI_COOKIE environment variable.
    Returns plain text subtitle content or an empty string on failure.
    """
    user_cookie = os.environ.get('BILIBILI_COOKIE')
    if user_cookie:
        logging.info("Using cookie from BILIBILI_COOKIE environment variable.")
    else:
        logging.info("No BILIBILI_COOKIE environment variable found. Proceeding without cookie.")

    bvid = extract_bvid(video_input)
    if not bvid:
        logging.error(f"Could not extract BV ID from input: {video_input}")
        return ""

    logging.info(f"Extracted BV ID: {bvid}")

    subtitle_json_str = get_subtitle_json_string(bvid, user_cookie)
    
    if not subtitle_json_str:
        logging.warning("No subtitle content retrieved.")
        return ""

    try:
        subtitle_data = json.loads(subtitle_json_str)
        if 'body' not in subtitle_data or not subtitle_data['body']:
            logging.warning("Subtitle data is empty or missing 'body' key.")
            return ""

        # Extract text content from subtitle body
        subtitle_lines = []
        for item in subtitle_data['body']:
            if 'content' in item:
                subtitle_lines.append(item['content'])

        subtitle_text = '\n'.join(subtitle_lines)
        
        logging.info(f"Successfully extracted {len(subtitle_lines)} subtitle lines.")
        logging.info(f"Original subtitle text length: {len(subtitle_text)} characters")
        
        # 使用token工具处理内容截断
        token_result = process_content_with_tokens(subtitle_text, 'BILIBILIFETCH')
        
        logging.info(f"Token processing result: original {token_result['original_tokens']} tokens, final {token_result['final_tokens']} tokens, truncated: {token_result['truncated']}")
        
        if token_result['truncated']:
            logging.warning(f"Content was truncated due to token limit ({token_result['original_tokens']} > {token_result['config'].get('max_tokens', 'unknown')})")
        
        return token_result['content']

    except json.JSONDecodeError as e:
        logging.error(f"Failed to parse subtitle JSON: {e}")
        return ""
    except Exception as e:
        logging.error(f"Unexpected error processing subtitle data: {e}")
        return ""


def main():
    """Main function for VCP plugin execution."""
    try:
        # Read input from stdin
        input_data = sys.stdin.read().strip()
        
        if not input_data:
            raise ValueError("No input data received from stdin.")

        # Parse JSON input
        try:
            data = json.loads(input_data)
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON input.")

        # Extract URL parameter
        url = data.get('url')
        if not url:
            raise ValueError("Missing required parameter: url")

        logging.info(f"Processing Bilibili URL: {url}")

        # Process the URL and get subtitle content
        result_text = process_bilibili_url(url)
        
        # Prepare output
        if result_text:
            # 获取token信息
            token_result = process_content_with_tokens(result_text, 'BILIBILIFETCH')
            
            output = {
                "status": "success",
                "result": token_result['content'],
                "token_info": {
                    "original_tokens": token_result['original_tokens'],
                    "final_tokens": token_result['final_tokens'],
                    "truncated": token_result['truncated'],
                    "max_tokens": token_result['config'].get('max_tokens', 16000),
                    "truncate_enabled": token_result['config'].get('enabled', True)
                }
            }
        else:
            output = {
                "status": "success",
                "result": "未能获取到字幕内容，可能是该视频没有字幕或需要登录权限。",
                "token_info": {
                    "original_tokens": 0,
                    "final_tokens": 0,
                    "truncated": False
                }
            }

    except Exception as e:
        error_message = str(e)
        logging.error(f"Plugin execution failed: {error_message}")
        output = {
            "status": "error",
            "error": f"BilibiliFetch Error: {error_message}"
        }

    # Output JSON result to stdout
    print(json.dumps(output, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()
