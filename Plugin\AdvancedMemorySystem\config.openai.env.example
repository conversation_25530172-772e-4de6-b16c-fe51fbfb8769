# ================================================================
# 智能情感记忆系统 - OpenAI Tools 配置文件
# Intelligent Emotion Memory System - OpenAI Tools Configuration
# 
# 基于OpenAI Tools的全新架构，模拟人类情绪和记忆系统
# New architecture based on OpenAI Tools, simulating human emotion and memory systems
# 
# 配置说明：将此文件重命名为 config.env 并根据需要修改配置值
# Instructions: Rename this file to config.env and modify values as needed
# ================================================================

# ----------------------------------------------------------------
# 🤖 OpenAI API 配置 (必须配置)
# OpenAI API Configuration (Required)
# ----------------------------------------------------------------

# OpenAI API密钥 (必填)
# OpenAI API Key (Required)
OPENAI_API_KEY=sk-your-openai-api-key-here

# OpenAI API地址 (默认官方地址，可使用代理)
# OpenAI API URL (Default official URL, can use proxy)
OPENAI_API_URL=https://api.openai.com

# 对话模型 (推荐gpt-4o-mini或gpt-4o)
# Chat Model (Recommended: gpt-4o-mini or gpt-4o)
OPENAI_MODEL=gpt-4o-mini

# 嵌入模型 (用于记忆检索)
# Embedding Model (For memory retrieval)
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# ----------------------------------------------------------------
# 🧠 核心功能开关
# Core Feature Switches
# ----------------------------------------------------------------

# 启用情感分析功能
# Enable emotion analysis
enable_emotion_analysis=true

# 启用记忆嵌入功能
# Enable memory embedding
enable_memory_embedding=true

# 启用概念学习功能
# Enable concept learning
enable_concept_learning=true

# 启用用户好感度系统
# Enable user affinity system
enable_user_affinity=true

# 启用后台智能线程
# Enable background intelligent threads
enable_background_threads=true

# 启用增强语义搜索
# Enable enhanced semantic search
enable_enhanced_semantic_search=true

# 启用记忆聚类分析
# Enable memory clustering
enable_memory_clustering=false

# 启用情感趋势分析
# Enable emotion trend analysis
enable_emotion_trend_analysis=true

# 启用概念关联网络
# Enable concept association network
enable_concept_network=true

# ----------------------------------------------------------------
# 📊 智能算法参数
# Intelligent Algorithm Parameters
# ----------------------------------------------------------------

# 最大上下文token数量
# Maximum context tokens
max_context_tokens=4000

# 情感分析敏感度 (0.0-1.0)
# Emotion analysis sensitivity
emotion_sensitivity=0.7

# 记忆相关性阈值 (0.0-1.0)
# Memory relevance threshold
memory_relevance_threshold=0.75

# 概念激活阈值 (0.0-1.0)
# Concept activation threshold
concept_activation_threshold=0.6

# 好感度更新敏感度 (0.0-1.0)
# Affinity update sensitivity
affinity_update_sensitivity=0.3

# VAD情感模型权重配置
# VAD emotion model weight configuration
emotion_valence_weight=0.4
emotion_arousal_weight=0.3
emotion_dominance_weight=0.3

# ----------------------------------------------------------------
# ⏰ 后台智能线程配置 (秒)
# Background Intelligence Thread Configuration (seconds)
# ----------------------------------------------------------------

# 自我反思处理间隔
# Self-reflection interval
reflection_interval=300

# 记忆演进处理间隔
# Memory evolution interval
evolution_interval=600

# 概念意识建构间隔
# Concept consciousness interval
consciousness_interval=450

# 情感状态监控间隔
# Emotion state monitoring interval
emotion_monitoring_interval=180

# ----------------------------------------------------------------
# 💾 数据存储配置
# Data Storage Configuration
# ----------------------------------------------------------------

# 数据库文件路径
# Database file path
database_path=data/emotion_memory.db

# 对话历史保留天数
# Conversation history retention days
conversation_retention_days=90

# 记忆片段最大数量
# Maximum memory fragments
max_memory_fragments=10000

# 用户好感度历史长度
# User affinity history length
affinity_history_length=50

# 概念神经元最大数量
# Maximum concept neurons
max_concept_neurons=5000

# ----------------------------------------------------------------
# 🚀 性能优化配置
# Performance Optimization Configuration
# ----------------------------------------------------------------

# 嵌入向量缓存大小
# Embedding vector cache size
embedding_cache_size=1000

# 用户状态缓存超时时间 (分钟)
# User state cache timeout (minutes)
user_cache_timeout=5

# 概念缓存超时时间 (分钟)
# Concept cache timeout (minutes)
concept_cache_timeout=15

# 对话历史缓存超时时间 (分钟)
# Conversation history cache timeout (minutes)
history_cache_timeout=10

# 最大并发OpenAI请求数
# Maximum concurrent OpenAI requests
max_concurrent_requests=3

# OpenAI请求超时时间 (秒)
# OpenAI request timeout (seconds)
openai_request_timeout=30

# ----------------------------------------------------------------
# 🎭 情感分析高级配置
# Advanced Emotion Analysis Configuration
# ----------------------------------------------------------------

# 情感标签识别精度
# Emotion label recognition precision
emotion_label_precision=0.8

# 情感强度计算方式 (weighted/average/max)
# Emotion intensity calculation method
emotion_intensity_method=weighted

# 情感稳定性检测窗口
# Emotion stability detection window
emotion_stability_window=5

# 用户个性化情感模型
# User personalized emotion model
enable_personalized_emotion=true

# ----------------------------------------------------------------
# 👥 用户好感度系统配置
# User Affinity System Configuration
# ----------------------------------------------------------------

# 好感度初始值
# Initial affinity value
initial_affinity=0.0

# 关系类型自动升级
# Automatic relationship type upgrade
enable_auto_relationship_upgrade=true

# 好感度历史权重衰减
# Affinity history weight decay
affinity_history_decay=0.95

# 交互质量评估模式 (ai/rule/hybrid)
# Interaction quality assessment mode
interaction_quality_mode=ai

# ----------------------------------------------------------------
# 🔍 记忆检索优化配置
# Memory Retrieval Optimization Configuration
# ----------------------------------------------------------------

# 语义搜索查询扩展
# Semantic search query expansion
enable_query_expansion=true

# 记忆重要性动态调整
# Dynamic memory importance adjustment
enable_dynamic_importance=true

# 时间衰减因子
# Time decay factor
time_decay_factor=0.05

# 情感上下文匹配权重
# Emotion context matching weight
emotion_context_weight=0.2

# ----------------------------------------------------------------
# 🧪 实验性功能配置
# Experimental Features Configuration
# ----------------------------------------------------------------

# 启用多模态分析 (实验性)
# Enable multimodal analysis (experimental)
enable_multimodal_analysis=false

# 启用跨用户概念学习 (实验性)
# Enable cross-user concept learning (experimental)
enable_cross_user_learning=false

# 启用预测性情感建模 (实验性)
# Enable predictive emotion modeling (experimental)
enable_predictive_emotion=false

# ----------------------------------------------------------------
# 📝 日志和调试配置
# Logging and Debug Configuration
# ----------------------------------------------------------------

# 调试模式
# Debug mode
debug_mode=false

# 记录详细分析过程
# Log detailed analysis process
log_detailed_analysis=false

# 记录OpenAI API调用
# Log OpenAI API calls
log_openai_calls=false

# 记录用户交互统计
# Log user interaction statistics
log_user_interactions=true

# 记录概念学习过程
# Log concept learning process
log_concept_learning=false

# 日志文件路径
# Log file path
log_file_path=logs/emotion_memory_system.log

# 日志级别 (error/warn/info/debug)
# Log level
log_level=info

# ----------------------------------------------------------------
# 🔒 安全和隐私配置
# Security and Privacy Configuration
# ----------------------------------------------------------------

# 启用用户数据加密
# Enable user data encryption
enable_data_encryption=false

# 数据匿名化处理
# Data anonymization processing
enable_data_anonymization=false

# 用户隔离模式
# User isolation mode
enable_user_isolation=true

# 敏感信息过滤
# Sensitive information filtering
enable_sensitive_filter=true

# ----------------------------------------------------------------
# 🌐 集成和API配置
# Integration and API Configuration
# ----------------------------------------------------------------

# API响应格式 (json/xml)
# API response format
api_response_format=json

# 启用WebSocket推送
# Enable WebSocket push
enable_websocket_push=false

# 状态报告间隔 (秒)
# Status report interval (seconds)
status_report_interval=3600

# 健康检查端点
# Health check endpoint
enable_health_check=true

# ----------------------------------------------------------------
# 🎛️ 高级调优参数 (慎重修改)
# Advanced Tuning Parameters (Modify with caution)
# ----------------------------------------------------------------

# OpenAI温度参数 (创造性)
# OpenAI temperature parameter (creativity)
openai_temperature=0.3

# OpenAI top_p参数 (多样性)
# OpenAI top_p parameter (diversity)
openai_top_p=0.9

# 函数调用重试次数
# Function call retry attempts
function_call_retries=2

# 批处理大小
# Batch processing size
batch_processing_size=10

# 内存清理阈值
# Memory cleanup threshold
memory_cleanup_threshold=0.8 