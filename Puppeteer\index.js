const HtmlToImageRenderer = require('./HtmlToImageRenderer');

// 创建全局渲染器实例
let globalRenderer = null;

/**
 * 获取或创建渲染器实例
 * @param {Object} config 配置选项
 * @returns {HtmlToImageRenderer} 渲染器实例
 */
function getRenderer(config = {}) {
    if (!globalRenderer) {
        globalRenderer = new HtmlToImageRenderer(config);
    }
    return globalRenderer;
}

/**
 * 渲染AI对话为图片
 * @param {Object} data 对话数据
 * @param {string} data.model AI模型名称
 * @param {string} data.id1 用户QQ号
 * @param {string} data.name 用户昵称
 * @param {string} data.MSG 用户消息
 * @param {string} data.id2 AI的QQ号
 * @param {string} data.name1 AI昵称
 * @param {string} data.CONTENT AI回复内容
 * @param {string} data.theme 主题 (dark/light)
 * @param {Object} options 渲染选项
 * @param {string} options.format 图片格式 (png|jpeg|webp)
 * @param {number} options.quality 图片质量 (1-100)
 * @param {number} options.width 视口宽度
 * @param {number} options.height 视口高度
 * @param {boolean} options.fullPage 是否截取整个页面
 * @param {string} options.filename 自定义文件名
 * @returns {Promise<string>} 图片的本地URL
 */
async function renderAiChat(data, options = {}) {
    try {
        const renderer = getRenderer();
        const imageUrl = await renderer.renderToImage(data, options);
        console.log('AI对话图片渲染成功:', imageUrl);
        return imageUrl;
    } catch (error) {
        console.error('AI对话图片渲染失败:', error);
        throw error;
    }
}

/**
 * 快速渲染函数 - 使用默认配置
 * @param {string} userMsg 用户消息
 * @param {string} aiReply AI回复
 * @param {Object} extraData 额外数据
 * @returns {Promise<string>} 图片的本地URL
 */
async function quickRender(userMsg, aiReply, extraData = {}) {
    const defaultData = {
        model: extraData.model || 'AI助手',
        id1: extraData.userId || '123456789',
        name: extraData.userName || '用户',
        MSG: userMsg,
        id2: extraData.aiId || '987654321',
        name1: extraData.aiName || 'AI助手',
        CONTENT: aiReply,
        theme: extraData.theme || 'dark'
    };

    const defaultOptions = {
        format: 'png',
        quality: 90,
        width: 800,
        height: 600,
        fullPage: true
    };

    return await renderAiChat(defaultData, defaultOptions);
}

/**
 * 关闭渲染器
 */
async function closeRenderer() {
    if (globalRenderer) {
        await globalRenderer.close();
        globalRenderer = null;
        console.log('渲染器已关闭');
    }
}

/**
 * 清理旧图片文件
 * @param {number} maxAge 最大保存时间（毫秒），默认7天
 */
async function cleanupImages(maxAge) {
    try {
        const renderer = getRenderer();
        await renderer.cleanupOldImages(maxAge);
        console.log('旧图片文件清理完成');
    } catch (error) {
        console.error('清理旧图片文件失败:', error);
    }
}

// 导出函数
module.exports = {
    // 主要渲染函数
    renderAiChat,
    quickRender,
    
    // 管理函数
    getRenderer,
    closeRenderer,
    cleanupImages,
    
    // 类导出（用于高级用法）
    HtmlToImageRenderer
};

// 为了向后兼容，也可以直接调用
module.exports.default = renderAiChat;
