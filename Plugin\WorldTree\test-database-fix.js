/**
 * 测试数据库修复效果
 */

const WorldTreeVCP = require('./WorldTreeVCP');

async function testDatabaseFix() {
    console.log('🧪 测试数据库修复效果...\n');
    
    const mockLogger = {
        info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
        warning: (tag, ...args) => console.log(`[WARN] [${tag}]`, ...args),
        error: (tag, ...args) => console.log(`[ERROR] [${tag}]`, ...args)
    };
    
    try {
        // 1. 初始化WorldTreeVCP
        console.log('1. 初始化WorldTreeVCP...');
        const worldTreeVCP = new WorldTreeVCP();
        const initResult = await worldTreeVCP.initialize(mockLogger);
        
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        console.log('✅ 插件初始化成功\n');
        
        // 2. 测试数据库表创建
        console.log('2. 测试数据库表创建...');
        // 表应该在初始化时自动创建
        console.log('✅ 数据库表创建成功\n');
        
        // 3. 测试心理状态缓存获取
        console.log('3. 测试心理状态缓存获取...');
        const testUserId = 'test_user_db_fix';
        const testAgentName = '雨安安';
        
        try {
            const cachedState = await worldTreeVCP.getCachedPsychologyActivity(testUserId, testAgentName);
            console.log('缓存状态结果:', cachedState ? '找到缓存' : '无缓存数据');
            console.log('✅ 心理状态缓存获取成功\n');
        } catch (error) {
            console.log('❌ 心理状态缓存获取失败:', error.message);
        }
        
        // 4. 测试集成分析器
        console.log('4. 测试集成分析器...');
        try {
            const testPsychologyState = {
                focus: 70,
                energy: 80,
                stress: 30,
                mood: 60
            };
            
            const guidance = worldTreeVCP.generateConversationalGuidance(testPsychologyState);
            console.log('对话指导生成结果:', guidance ? '成功' : '失败');
            console.log('✅ 集成分析器测试成功\n');
        } catch (error) {
            console.log('❌ 集成分析器测试失败:', error.message);
        }
        
        // 5. 测试世界树心理状态获取
        console.log('5. 测试世界树心理状态获取...');
        try {
            const worldTreeState = await worldTreeVCP.getWorldTreePsychologyState(testUserId, testAgentName);
            console.log('世界树状态结果:', worldTreeState ? '找到状态' : '无状态数据');
            console.log('✅ 世界树心理状态获取成功\n');
        } catch (error) {
            console.log('❌ 世界树心理状态获取失败:', error.message);
        }
        
        // 6. 测试完整的心理状态计算
        console.log('6. 测试完整的心理状态计算...');
        try {
            const contextFactors = {
                isRequestTriggered: true,
                cognitiveLoad: 0.5
            };
            
            const psychologyState = await worldTreeVCP.calculatePsychologyState(
                testUserId, 
                testAgentName, 
                contextFactors
            );
            
            console.log('心理状态计算结果:');
            console.log('- 专注度:', psychologyState.focus);
            console.log('- 精力:', psychologyState.energy);
            console.log('- 压力:', psychologyState.stress);
            console.log('- 心情:', psychologyState.mood);
            console.log('✅ 完整心理状态计算成功\n');
        } catch (error) {
            console.log('❌ 完整心理状态计算失败:', error.message);
        }
        
        console.log('🎉 数据库修复测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 运行测试
testDatabaseFix().catch(console.error);
