@font-face {
  font-family: "HYWenHei-55W";
  src: url("./ttf/HYWenHei-55W.ttf");
  font-weight: normal;
  font-style: normal;
}

img{
	margin-top:15px;
	border-radius:5px;
	width: 350px;
	height: 292px;
	box-shadow:3px 3px 3px rgb(0 0 0 / 30%);

	
}

.wenzi{
	padding-top:15px;
	padding-left:5px;
	padding-bottom:15px;
overflow: hidden; /*自动隐藏文字*/
text-overflow: ellipsis;/*文字隐藏后添加省略号*/
white-space: nowrap;/*强制不换行*/
width: 15em;/*不允许出现半汉字截断*/
text-align: center;
border-bottom:1px solid #ccc
}

.tu{
	padding-top:60px;
	text-align:center;
	display:block
	
}
.zhu{
	width:1000px;
	height:1000px;
	display:inline;
	
}
body {
  
  width: 100%;
  height: 130%;

 

   
    background-size: 100% ;
	background-image:url('99.jpg')
}

.bt{
	font-family:"NZBZ";
	padding-top:2px;
	text-align: center;
	font-size:70px;
	
	padding-bottom:20px;
background-color:rgb(0 0 0 / 0%);	
color:#000;
border-bottom:2px solid #ccc
	
}
.nr{
	border-radius:5px;	
	font-family:"HYWenHei-55W";
	font-size:25px;
	font-weight:510;
	padding-left:10px;
	padding-bottom:40px;
	padding-top:10px;
	padding-right:10px;
	margin-left:40px;
	margin-right:40px;
	color:#000;
	text-align:center;
	border-bottom:2px solid #ccc
	
	
}
.bg1{
	width:100%;
	text-align:left;
	height:60px;
	
	font-size:25px;
	color:#333333;
	
    font-family: "jty";
	
}

.nr2{
	position: absolute;
	border-radius:130px;
	left:50px;
	margin-top:20px;
	font-size:70px;
	text-align:center;
	padding-left:30px;
	padding-right:30px;
	background-color:#000;
	color:#fff;
	box-shadow:3px 2px 3px #ccc
}
.jw{
	font-size:20px;
	text-align:center
}