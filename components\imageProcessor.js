// components/imageProcessor.js
// 图片处理相关函数

const path = require('path');

/**
 * 新增：验证图片文件的辅助函数
 */
function validateImageFile(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.includes(ext);
}

/**
 * 新增：获取MIME类型的辅助函数
 */
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.bmp': 'image/bmp',
        '.webp': 'image/webp'
    };
    return mimeTypes[ext] || 'image/jpeg';
}





module.exports = {
    validateImageFile,
    getMimeType
};
