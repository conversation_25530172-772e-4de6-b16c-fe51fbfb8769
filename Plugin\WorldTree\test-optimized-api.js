/**
 * 测试优化后的世界树VCP API调用机制
 */

const WorldTreeVCP = require('./WorldTreeVCP');
const fs = require('fs').promises;
const path = require('path');

async function testOptimizedAPI() {
    console.log('🚀 测试优化后的世界树VCP API调用机制...\n');
    
    const mockLogger = {
        info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
        warning: (tag, ...args) => console.log(`[WARN] [${tag}]`, ...args),
        error: (tag, ...args) => console.log(`[ERROR] [${tag}]`, ...args),
        debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args)
    };
    
    try {
        // 1. 初始化WorldTreeVCP
        console.log('1. 初始化WorldTreeVCP...');
        const worldTreeVCP = new WorldTreeVCP();
        await worldTreeVCP.initialize(mockLogger);
        console.log('✅ 插件初始化成功\n');
        
        // 2. 模拟真实数据
        const testUserId = 'test_user_optimized';
        const testAgentName = '雨安安';
        
        const mockPsychologyState = {
            focus: 73.6,
            energy: 15.2,
            fatigue: 46.8,
            alertness: 57.5,
            hunger: 8.5,
            stress: 80.0,
            mood: 35.0,
            timePeriod: 'afternoon'
        };
        
        const mockWorldTreeConfig = {
            worldBackground: '你是雨安安，一位专注于AI研究的技术专家，对大语言模型和智能体技术有深入理解。',
            timeArchitecture: {
                afternoon: '下午时分，思维活跃，适合进行复杂的技术讨论和问题解决'
            },
            characterSchedules: {
                enabled: true,
                schedules: [
                    { time: '14:00-17:00', activity: '代码重构和系统优化' }
                ]
            }
        };
        
        const mockFullContext = {
            timestamp: '2025/7/20 14:30:45',
            timePeriod: 'afternoon',
            recentConversations: [
                {
                    speaker: '用户',
                    content: '为什么报错啊[2025/7/20 14:08:34] [!] [世界树VCP] 获取缓存心理活动失败: SQLITE_ERROR: no such column: psychology_state',
                    timestamp: '2025/7/20 14:08:34'
                },
                {
                    speaker: testAgentName,
                    content: '我看到问题了！有两个错误：1. **数据库错误**：`no such column: psychology_state` - 数据库表结构缺少心理状态列',
                    timestamp: '2025/7/20 14:09:15'
                }
            ],
            emotionalState: {
                emotion_valence: -0.2,
                emotion_arousal: 0.6,
                emotion_dominance: 0.7,
                current_affinity: 75,
                relationship_type: 'professional_friend'
            }
        };
        
        // 3. 测试构建优化的Messages数组
        console.log('2. 测试构建优化的Messages数组...');
        const messages = await worldTreeVCP.buildOptimizedMessages(
            testAgentName,
            mockPsychologyState,
            mockWorldTreeConfig,
            mockFullContext
        );
        
        console.log('📋 生成的Messages数组结构:');
        console.log('=' .repeat(60));
        messages.forEach((msg, index) => {
            console.log(`Message ${index + 1} (${msg.role}):`);
            console.log(`长度: ${msg.content.length} 字符`);
            console.log(`内容预览: ${msg.content.substring(0, 100)}...`);
            console.log('-' .repeat(40));
        });
        
        // 4. 显示完整的User消息内容
        console.log('\n📝 完整的User消息内容:');
        console.log('=' .repeat(60));
        const userMessage = messages.find(m => m.role === 'user');
        if (userMessage) {
            console.log(userMessage.content);
        }
        console.log('=' .repeat(60));
        
        // 5. 测试保存心理独白功能
        console.log('\n3. 测试保存心理独白功能...');
        const testMonologue = '下午的阳光透过窗户洒在键盘上，但我的精力已经明显下降了。虽然专注度还能维持在73%，但这种高压状态下的工作让我感到有些疲惫。数据库错误的解决过程让我想起了系统架构的复杂性，每一个模块都需要精心设计和维护。或许这就是技术工作的魅力所在，在挑战中不断成长和优化。';
        
        await worldTreeVCP.saveMonologueToWorldTree(
            testUserId,
            testAgentName,
            testMonologue,
            mockPsychologyState,
            mockFullContext
        );
        console.log('✅ 心理独白保存成功');
        
        // 6. 测试获取历史心理独白
        console.log('\n4. 测试获取历史心理独白...');
        const recentMonologues = await worldTreeVCP.getRecentMonologueFromWorldTree(testUserId, testAgentName, 2);
        console.log(`📚 获取到 ${recentMonologues.length} 条历史心理独白:`);
        recentMonologues.forEach((monologue, index) => {
            console.log(`${index + 1}. [${monologue.createdTime}] ${monologue.content.substring(0, 50)}...`);
        });
        
        // 7. 测试缓存获取功能
        console.log('\n5. 测试缓存获取功能...');
        const cachedActivity = await worldTreeVCP.getCachedPsychologyActivity(testUserId, testAgentName);
        if (cachedActivity) {
            console.log('✅ 缓存获取成功:');
            console.log(`来源: ${cachedActivity.source}`);
            console.log(`时间: ${cachedActivity.timestamp}`);
            console.log(`内容: ${cachedActivity.content?.substring(0, 80)}...`);
        } else {
            console.log('❌ 未找到缓存数据');
        }
        
        // 8. 对比新旧结构
        console.log('\n6. 新旧结构对比分析:');
        console.log('=' .repeat(60));
        console.log('🔴 旧结构问题:');
        console.log('- 单一巨大字符串 (1200+ 字符)');
        console.log('- 只有一个user角色');
        console.log('- 信息混杂，难以理解');
        console.log('- 使用旧的记忆片段表');
        console.log('');
        console.log('🟢 新结构优势:');
        console.log(`- 分离的messages数组 (${messages.length} 个消息)`);
        console.log('- system角色明确身份');
        console.log('- user角色结构化信息');
        console.log('- 专用的世界树表存储');
        console.log('- 更好的token效率');
        
        // 9. Token使用分析
        const totalTokens = messages.reduce((sum, msg) => sum + Math.ceil(msg.content.length / 4), 0);
        console.log('\n📊 Token使用分析:');
        console.log(`总字符数: ${messages.reduce((sum, msg) => sum + msg.content.length, 0)}`);
        console.log(`预估Token数: ${totalTokens}`);
        console.log(`平均每消息: ${Math.ceil(totalTokens / messages.length)} tokens`);
        
        console.log('\n🎉 优化后的API调用机制测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 运行测试
testOptimizedAPI().catch(console.error);
