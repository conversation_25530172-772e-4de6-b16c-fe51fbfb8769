{"name": "google-image-editor", "version": "1.0.0", "description": "谷歌图像编辑插件，支持Gemini模型的图像编辑功能", "main": "GoogleImageEditor.js", "scripts": {"test": "node test.js", "test-mcp": "node test-mcp.js", "test-markdown": "node test-markdown.js"}, "dependencies": {"axios": "^1.6.0", "form-data": "^4.0.0", "dotenv": "^16.0.0", "mime-types": "^2.1.35"}, "keywords": ["google", "gemini", "image-editing", "ai-art", "text-to-image", "markdown"], "author": "VCPToolBox Team", "license": "MIT"}