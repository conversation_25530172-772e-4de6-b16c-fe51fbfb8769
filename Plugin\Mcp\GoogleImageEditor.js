// Plugin/Mcp/GoogleImageEditor.js - 谷歌图像编辑MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class GoogleImageEditorMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'GoogleImageEditor';
        this.description = '谷歌图像编辑工具，使用Gemini模型进行图像编辑。支持处理markdown格式的图片链接，可以添加元素、修改风格、改变场景等。所有生成的图像都包含SynthID水印，输出使用本地绝对路径格式。';
        this.vcpName = 'GoogleImageEditor';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                prompt: {
                    type: 'string',
                    description: '图像编辑的详细描述文本，描述要如何修改图像。可以包含markdown格式的图片链接，如：![描述](图片路径) 请将背景改为夕阳'
                },
                image_path: {
                    type: 'string',
                    description: '图片文件路径 (可选，如果prompt中包含markdown图片链接则不需要)'
                },
                image_url: {
                    type: 'string',
                    description: '图片URL地址 (可选，如果prompt中包含markdown图片链接则不需要)'
                },
                image_data: {
                    type: 'string',
                    description: 'base64编码的图像数据 (可选，直接提供时优先使用)'
                },
                image_mime_type: {
                    type: 'string',
                    description: '图像MIME类型 (可选)',
                    default: 'image/png'
                }
            },
            required: ['prompt']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);

        // 验证参数 - 需要有图片来源
        if (!args.image_data && !args.image_path && !args.image_url && !args.prompt.includes('![')) {
            throw new Error('需要提供图片来源：在prompt中包含markdown图片链接，或提供image_data/image_path/image_url参数');
        }

        this.log('info', '开始编辑图像', {
            prompt: args.prompt.substring(0, 100),
            has_image_data: !!args.image_data,
            has_image_path: !!args.image_path,
            has_image_url: !!args.image_url,
            has_markdown_image: args.prompt.includes('![')
        });

        try {
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);

            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }

            // 构建标准响应格式
            const response = {
                type: 'google_image_processing',
                status: 'success',
                message: '图像编辑完成',
                data: {
                    prompt: args.prompt,
                    image_url: parsedResult?.data?.image_url,
                    image_path: parsedResult?.data?.image_path,
                    filename: parsedResult?.data?.filename,
                    text_response: parsedResult?.data?.text_response || '',
                    markdown_display: parsedResult?.data?.markdown_display || `![${args.prompt.substring(0, 80)}](${parsedResult?.data?.image_url})`,
                    generation_info: {
                        model_used: 'gemini',
                        generation_time: parsedResult?.data?.generation_time
                    },
                    processing_info: {
                        timestamp: parsedResult?.timestamp || new Date().toISOString(),
                        plugin_version: '1.0.0',
                        api_provider: 'Google AI'
                    }
                }
            };

            this.log('success', '图像编辑完成', {
                prompt: args.prompt.substring(0, 50),
                image_path: response.data.image_path,
                has_text_response: !!response.data.text_response
            });

            return response;

        } catch (error) {
            const errorResponse = {
                type: 'google_image_processing',
                status: 'error',
                message: error.message,
                data: {
                    prompt: args.prompt,
                    error: error.message,
                    error_type: this.getErrorType(error.message),
                    suggestions: this.getErrorSuggestions(error.message)
                }
            };

            this.log('error', '图像编辑失败', errorResponse);
            throw errorResponse;
        }
    }

    /**
     * 获取错误类型
     */
    getErrorType(errorMessage) {
        if (errorMessage.includes('API密钥') || errorMessage.includes('API key')) {
            return 'authentication_error';
        } else if (errorMessage.includes('配额') || errorMessage.includes('quota')) {
            return 'quota_exceeded';
        } else if (errorMessage.includes('网络') || errorMessage.includes('timeout')) {
            return 'network_error';
        } else if (errorMessage.includes('参数') || errorMessage.includes('parameter')) {
            return 'parameter_error';
        } else if (errorMessage.includes('模型') || errorMessage.includes('model')) {
            return 'model_error';
        } else {
            return 'unknown_error';
        }
    }

    /**
     * 获取错误建议
     */
    getErrorSuggestions(errorMessage) {
        const suggestions = [];

        if (errorMessage.includes('API密钥')) {
            suggestions.push('请检查GOOGLE_API_KEY或GEMINI_API_KEY是否正确配置');
            suggestions.push('确认API密钥是否有效且未过期');
        }

        if (errorMessage.includes('配额')) {
            suggestions.push('请检查API配额是否已用完');
            suggestions.push('考虑升级到付费计划或等待配额重置');
        }

        if (errorMessage.includes('网络') || errorMessage.includes('timeout')) {
            suggestions.push('检查网络连接是否正常');
            suggestions.push('如果在中国大陆，考虑配置GOOGLE_PROXY_URL');
        }



        if (errorMessage.includes('图片来源') || errorMessage.includes('image_data')) {
            suggestions.push('请在prompt中包含markdown格式的图片链接，如：![描述](图片路径)');
            suggestions.push('或者提供image_data/image_path/image_url参数');
        }

        if (suggestions.length === 0) {
            suggestions.push('请检查输入参数是否正确');
            suggestions.push('查看插件文档了解详细使用方法');
        }

        return suggestions;
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();

        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }

            // 检查必要的环境变量
            const apiKey = process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY;
            if (!apiKey) {
                throw new Error('未配置Google API密钥 (GOOGLE_API_KEY 或 GEMINI_API_KEY)');
            }

            // 检查模型配置
            const geminiModel = process.env.GEMINI_MODEL;

            if (!geminiModel) {
                this.log('warning', '未配置Gemini模型，将使用默认模型');
            }

        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }

        return true;
    }
}

module.exports = GoogleImageEditorMcp;
