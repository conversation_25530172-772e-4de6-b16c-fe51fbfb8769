const express = require('express');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

module.exports = function(debugMode, pluginManager) {
    const router = express.Router();

    // 数据库路径 - 使用智能记忆系统的实际数据库
    const DB_PATH = path.join(__dirname, '..', 'Plugin', 'AdvancedMemorySystem', 'data', 'emotion_memory.db');

    // 获取数据库连接
    function getDatabase() {
        if (debugMode) console.log('🔍 [DEBUG] 使用数据库路径:', DB_PATH);
        return new sqlite3.Database(DB_PATH);
    }

    // 通用错误处理
    function handleError(res, error, message = '操作失败') {
        console.error(`[Memory API Error] ${message}:`, error);
        res.status(500).json({
            success: false,
            error: message,
            details: debugMode ? error.message : undefined
        });
    }

    // 通用成功响应
    function sendSuccess(res, data = null, message = '操作成功') {
        res.json({
            success: true,
            message,
            data
        });
    }

    // 获取数据库信息和表列表
    router.get('/database/info', (req, res) => {
        const db = getDatabase();

        // 获取所有表的信息
        db.all(`
            SELECT name, sql
            FROM sqlite_master
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        `, (err, tables) => {
            if (err) {
                db.close();
                return handleError(res, err, '获取数据库信息失败');
            }

            // 获取每个表的记录数
            const tablePromises = tables.map(table => {
                return new Promise((resolve) => {
                    db.get(`SELECT COUNT(*) as count FROM ${table.name}`, (err, result) => {
                        resolve({
                            name: table.name,
                            sql: table.sql,
                            count: err ? 0 : result.count
                        });
                    });
                });
            });

            Promise.all(tablePromises).then(tablesWithCount => {
                db.close();
                sendSuccess(res, {
                    database: DB_PATH,
                    tables: tablesWithCount
                });
            });
        });
    });

    // 获取系统概览统计信息
    router.get('/overview', (req, res) => {
        const db = getDatabase();

        // 首先获取所有表名
        db.all("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'", (err, tables) => {
            if (err) {
                db.close();
                return handleError(res, err, '获取表列表失败');
            }

            const tableNames = tables.map(t => t.name);
            if (debugMode) console.log('🔍 [DEBUG] 数据库中的表:', tableNames);

            // 根据实际存在的表来构建查询
            const queries = [];

            // 概念数量 - 优先使用concepts表
            if (tableNames.includes('concepts')) {
                queries.push({ key: 'totalConcepts', sql: 'SELECT COUNT(*) as count FROM concepts' });
            } else if (tableNames.includes('concept_neurons')) {
                queries.push({ key: 'totalConcepts', sql: 'SELECT COUNT(*) as count FROM concept_neurons' });
            } else {
                queries.push({ key: 'totalConcepts', sql: 'SELECT 0 as count' });
            }

            // 对话数量
            if (tableNames.includes('conversation_history')) {
                queries.push({ key: 'totalConversations', sql: 'SELECT COUNT(*) as count FROM conversation_history' });
            } else {
                queries.push({ key: 'totalConversations', sql: 'SELECT 0 as count' });
            }

            // 用户数量 - 从多个表统计并合并
            let userQuery = 'SELECT 0 as count';
            if (tableNames.includes('conversation_history') && tableNames.includes('memory_fragments')) {
                userQuery = `SELECT COUNT(DISTINCT user_id) as count FROM (
                    SELECT user_id FROM conversation_history WHERE user_id IS NOT NULL
                    UNION
                    SELECT user_id FROM memory_fragments WHERE user_id IS NOT NULL
                ) combined_users`;
            } else if (tableNames.includes('conversation_history')) {
                userQuery = 'SELECT COUNT(DISTINCT user_id) as count FROM conversation_history WHERE user_id IS NOT NULL';
            } else if (tableNames.includes('memory_fragments')) {
                userQuery = 'SELECT COUNT(DISTINCT user_id) as count FROM memory_fragments WHERE user_id IS NOT NULL';
            }
            queries.push({ key: 'totalUsers', sql: userQuery });

            // 记忆片段数量
            if (tableNames.includes('memory_fragments')) {
                queries.push({ key: 'totalMemories', sql: 'SELECT COUNT(*) as count FROM memory_fragments' });
            } else {
                queries.push({ key: 'totalMemories', sql: 'SELECT 0 as count' });
            }

            // AI情绪状态数量
            if (tableNames.includes('ai_emotion_states')) {
                queries.push({ key: 'totalAIEmotions', sql: 'SELECT COUNT(*) as count FROM ai_emotion_states' });
            } else {
                queries.push({ key: 'totalAIEmotions', sql: 'SELECT 0 as count' });
            }

            // 用户好感度状态数量
            if (tableNames.includes('user_affinity_states')) {
                queries.push({ key: 'totalUserAffinities', sql: 'SELECT COUNT(*) as count FROM user_affinity_states' });
            } else {
                queries.push({ key: 'totalUserAffinities', sql: 'SELECT 0 as count' });
            }

            const stats = {};
            let completed = 0;

            if (queries.length === 0) {
                db.close();
                return sendSuccess(res, {
                    stats: {
                        totalConcepts: 0,
                        totalConversations: 0,
                        totalUsers: 0,
                        totalMemories: 0,
                        totalAIEmotions: 0,
                        totalUserAffinities: 0
                    }
                });
            }

            queries.forEach(query => {
                db.get(query.sql, (err, result) => {
                    if (err) {
                        console.error(`❌ [ERROR] 查询 ${query.key} 失败:`, err);
                        stats[query.key] = 0;
                    } else {
                        stats[query.key] = result?.count || 0;
                        if (debugMode) console.log(`📊 [DEBUG] ${query.key}: ${stats[query.key]}`);
                    }
                    completed++;

                    if (completed === queries.length) {
                        db.close();
                        if (debugMode) console.log('📊 [DEBUG] 最终统计结果:', stats);
                        sendSuccess(res, { stats });
                    }
                });
            });
        });
    });

    // 获取统计数据（为前端实时监控提供数据）
    router.get('/stats', (req, res) => {
        const db = getDatabase();

        // 获取今天的日期范围
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
        const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1).toISOString();

        // 定义统计查询（使用正确的表名和列名）
        const queries = [
            { key: 'totalRecords', sql: 'SELECT COUNT(*) as count FROM memory_fragments' },
            { key: 'todayRecords', sql: 'SELECT COUNT(*) as count FROM memory_fragments WHERE creation_time >= ? AND creation_time < ?', params: [todayStart, todayEnd] },
            { key: 'totalConcepts', sql: 'SELECT COUNT(*) as count FROM concepts' },
            { key: 'totalUsers', sql: 'SELECT COUNT(DISTINCT user_id) as count FROM memory_fragments' },
            { key: 'totalEmotions', sql: 'SELECT COUNT(*) as count FROM ai_emotion_states' },
            { key: 'totalAffinity', sql: 'SELECT COUNT(*) as count FROM user_affinity_states' }
        ];

        const stats = {};
        let completed = 0;

        queries.forEach(query => {
            const params = query.params || [];
            db.get(query.sql, params, (err, result) => {
                if (err) {
                    console.error(`❌ [ERROR] 统计查询 ${query.key} 失败:`, err);
                    stats[query.key] = 0;
                } else {
                    stats[query.key] = result?.count || 0;
                    if (debugMode) console.log(`📊 [DEBUG] ${query.key}: ${stats[query.key]}`);
                }
                completed++;

                if (completed === queries.length) {
                    db.close();
                    if (debugMode) console.log('📊 [DEBUG] 统计结果:', stats);
                    sendSuccess(res, stats);
                }
            });
        });
    });

    // 健康检查接口
    router.get('/health', (req, res) => {
        const db = getDatabase();

        // 简单的数据库连接测试
        db.get('SELECT 1 as test', (err, result) => {
            db.close();

            if (err) {
                console.error('❌ [ERROR] 数据库健康检查失败:', err);
                res.status(500).json({
                    success: false,
                    status: 'unhealthy',
                    error: '数据库连接失败',
                    timestamp: new Date().toISOString()
                });
            } else {
                res.json({
                    success: true,
                    status: 'healthy',
                    message: '数据库连接正常',
                    timestamp: new Date().toISOString()
                });
            }
        });
    });

    // 获取用户好感度信息
    router.get('/user-affinity', (req, res) => {
        const { userId, personaName } = req.query;
        const db = getDatabase();

        let query = 'SELECT * FROM user_affinity_states ORDER BY timestamp DESC';
        let params = [];

        if (userId && personaName) {
            query = 'SELECT * FROM user_affinity_states WHERE user_id = ? AND persona_name = ? ORDER BY timestamp DESC LIMIT 1';
            params = [userId, personaName];
        } else if (userId) {
            query = 'SELECT * FROM user_affinity_states WHERE user_id = ? ORDER BY timestamp DESC';
            params = [userId];
        }

        db.all(query, params, (err, rows) => {
            if (err) {
                db.close();
                return handleError(res, err, '获取用户好感度失败');
            }

            db.close();
            sendSuccess(res, { data: rows });
        });
    });

    // 获取AI情绪状态信息
    router.get('/ai-emotions', (req, res) => {
        const { userId, personaName } = req.query;
        const db = getDatabase();

        let query = 'SELECT * FROM ai_emotion_states ORDER BY timestamp DESC';
        let params = [];

        if (userId && personaName) {
            query = 'SELECT * FROM ai_emotion_states WHERE user_id = ? AND persona_name = ? ORDER BY timestamp DESC LIMIT 1';
            params = [userId, personaName];
        } else if (userId) {
            query = 'SELECT * FROM ai_emotion_states WHERE user_id = ? ORDER BY timestamp DESC';
            params = [userId];
        }

        db.all(query, params, (err, rows) => {
            if (err) {
                db.close();
                return handleError(res, err, '获取AI情绪状态失败');
            }

            db.close();
            sendSuccess(res, { data: rows });
        });
    });

    // 获取当前系统状态（包括最新的AI心情和用户好感度）
    router.get('/current-status', (req, res) => {
        const { userId = '雨安', personaName = '失语症' } = req.query;
        const db = getDatabase();

        // 获取最新的AI情绪状态
        const getLatestEmotion = new Promise((resolve, reject) => {
            db.get(
                'SELECT * FROM ai_emotion_states WHERE user_id = ? AND persona_name = ? ORDER BY timestamp DESC LIMIT 1',
                [userId, personaName],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        // 获取最新的用户好感度
        const getLatestAffinity = new Promise((resolve, reject) => {
            db.get(
                'SELECT * FROM user_affinity_states WHERE user_id = ? AND persona_name = ? ORDER BY timestamp DESC LIMIT 1',
                [userId, personaName],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        Promise.all([getLatestEmotion, getLatestAffinity])
            .then(([emotion, affinity]) => {
                db.close();
                sendSuccess(res, {
                    currentEmotion: emotion,
                    currentAffinity: affinity,
                    userId,
                    personaName,
                    timestamp: new Date().toISOString()
                });
            })
            .catch(err => {
                db.close();
                handleError(res, err, '获取当前状态失败');
            });
    });

    // 初始化用户好感度数据
    router.post('/init-affinity-data', (req, res) => {
        const db = getDatabase();

        // 示例用户数据
        const sampleUsers = [
            { user_id: '雨安', persona_name: '失语症', affinity_value: 75, trend: 'positive', interaction_quality: 'excellent' },
            { user_id: '静', persona_name: '失语症', affinity_value: 60, trend: 'stable', interaction_quality: 'good' },
            { user_id: '测试用户1', persona_name: '失语症', affinity_value: 45, trend: 'negative', interaction_quality: 'fair' },
            { user_id: '测试用户2', persona_name: '失语症', affinity_value: 85, trend: 'positive', interaction_quality: 'excellent' },
            { user_id: '测试用户3', persona_name: '失语症', affinity_value: 30, trend: 'declining', interaction_quality: 'poor' }
        ];

        const sampleEmotions = [
            { user_id: '雨安', persona_name: '失语症', emotion_type: 'happy', intensity: 0.8, trigger_event: '用户提出有趣问题' },
            { user_id: '静', persona_name: '失语症', emotion_type: 'curious', intensity: 0.6, trigger_event: '探讨技术话题' },
            { user_id: '测试用户1', persona_name: '失语症', emotion_type: 'neutral', intensity: 0.4, trigger_event: '日常对话' },
            { user_id: '测试用户2', persona_name: '失语症', emotion_type: 'excited', intensity: 0.9, trigger_event: '解决复杂问题' },
            { user_id: '测试用户3', persona_name: '失语症', emotion_type: 'concerned', intensity: 0.3, trigger_event: '用户表达困惑' }
        ];

        let completed = 0;
        const total = sampleUsers.length * 2 + sampleEmotions.length;

        function checkComplete() {
            completed++;
            if (completed >= total) {
                db.close();
                sendSuccess(res, {
                    message: '用户好感度数据初始化完成',
                    affinityRecords: sampleUsers.length,
                    emotionRecords: sampleEmotions.length
                });
            }
        }

        // 首先确保user_affinity_states表存在
        db.run(`CREATE TABLE IF NOT EXISTS user_affinity_states (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            persona_name TEXT NOT NULL,
            affinity_value REAL NOT NULL,
            trend TEXT,
            interaction_quality TEXT,
            timestamp TEXT NOT NULL
        )`, function(err) {
            if (err) {
                console.error('创建user_affinity_states表失败:', err);
                return;
            }

            // 插入用户好感度状态数据
            sampleUsers.forEach(user => {
                const timestamp = new Date().toISOString();

                // 插入到 user_affinity_states 表
                db.run(`INSERT OR REPLACE INTO user_affinity_states
                    (user_id, persona_name, affinity_value, trend, interaction_quality, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)`,
                    [user.user_id, user.persona_name, user.affinity_value, user.trend, user.interaction_quality, timestamp],
                    function(err) {
                        if (err) console.error('插入用户好感度状态失败:', err);
                        checkComplete();
                    });

                // 插入到 user_affinity 表
                db.run(`INSERT OR REPLACE INTO user_affinity
                    (user_id, persona_name, current_affinity, relationship_type, total_interactions, last_interaction)
                    VALUES (?, ?, ?, ?, ?, ?)`,
                    [user.user_id, user.persona_name, user.affinity_value,
                     user.affinity_value > 70 ? 'close_friend' : user.affinity_value > 40 ? 'friend' : 'acquaintance',
                     Math.floor(Math.random() * 50) + 10, timestamp],
                    function(err) {
                        if (err) console.error('插入用户好感度失败:', err);
                        checkComplete();
                    });
            });
        });

        // 首先确保ai_emotion_states表有正确的结构
        db.run(`CREATE TABLE IF NOT EXISTS ai_emotion_states (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            persona_name TEXT NOT NULL,
            current_emotion TEXT NOT NULL,
            emotion_value INTEGER NOT NULL,
            emotion_type TEXT,
            intensity REAL,
            trigger_event TEXT,
            trend TEXT,
            stability REAL,
            timestamp TEXT NOT NULL
        )`, function(err) {
            if (err) {
                console.error('创建ai_emotion_states表失败:', err);
                return;
            }

            // 插入AI情绪状态数据
            sampleEmotions.forEach(emotion => {
                const timestamp = new Date().toISOString();

                // 将emotion_type映射为current_emotion，并设置emotion_value
                const emotionValueMap = {
                    'happy': 80,
                    'curious': 60,
                    'neutral': 50,
                    'excited': 90,
                    'concerned': 30
                };

                db.run(`INSERT OR REPLACE INTO ai_emotion_states
                    (user_id, persona_name, current_emotion, emotion_value, emotion_type, intensity, trigger_event, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        emotion.user_id,
                        emotion.persona_name,
                        emotion.emotion_type,
                        emotionValueMap[emotion.emotion_type] || 50,
                        emotion.emotion_type,
                        emotion.intensity,
                        emotion.trigger_event,
                        timestamp
                    ],
                    function(err) {
                        if (err) console.error('插入AI情绪状态失败:', err);
                        checkComplete();
                    });
            });
        });
    });

    // 获取表格数据（支持分页、搜索、排序、筛选）
    router.get('/table/:tableName', (req, res) => {
        const { tableName } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const search = req.query.search || '';
        const sortBy = req.query.sortBy || '';
        const sortOrder = req.query.sortOrder || 'desc';
        const filterUserId = req.query.filterUserId || '';
        const filterPersonaName = req.query.filterPersonaName || '';
        const filterDateStart = req.query.filterDateStart || '';
        const filterDateEnd = req.query.filterDateEnd || '';
        const offset = (page - 1) * limit;

        console.log(`📊 [${tableName}] 查询参数:`, {
            page, limit, search, sortBy, sortOrder,
            filterUserId, filterPersonaName, filterDateStart, filterDateEnd
        });

        const db = getDatabase();

        // 首先获取表结构
        db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
            if (err) {
                db.close();
                return handleError(res, err, '获取表结构失败');
            }

            if (!columns || columns.length === 0) {
                db.close();
                return res.status(404).json({
                    success: false,
                    error: '表不存在'
                });
            }

            const columnNames = columns.map(col => col.name);

            // 构建WHERE条件
            let whereConditions = [];
            let queryParams = [];

            // 搜索条件
            if (search) {
                const searchColumns = columns
                    .filter(col => col.type.toLowerCase().includes('text') || col.type.toLowerCase().includes('varchar'))
                    .map(col => `${col.name} LIKE ?`);

                if (searchColumns.length > 0) {
                    whereConditions.push(`(${searchColumns.join(' OR ')})`);
                    queryParams.push(...new Array(searchColumns.length).fill(`%${search}%`));
                }
            }

            // 用户ID筛选
            if (filterUserId && columnNames.includes('user_id')) {
                whereConditions.push('user_id = ?');
                queryParams.push(filterUserId);
            }

            // 角色名称筛选
            if (filterPersonaName && columnNames.includes('persona_name')) {
                whereConditions.push('persona_name = ?');
                queryParams.push(filterPersonaName);
            }

            // 日期范围筛选
            const dateColumns = ['timestamp', 'created_at', 'creation_time', 'last_updated', 'updated_at'];
            const dateColumn = dateColumns.find(col => columnNames.includes(col));

            if (dateColumn) {
                if (filterDateStart) {
                    whereConditions.push(`${dateColumn} >= ?`);
                    queryParams.push(filterDateStart);
                }
                if (filterDateEnd) {
                    whereConditions.push(`${dateColumn} <= ?`);
                    queryParams.push(filterDateEnd);
                }
            }

            const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

            // 构建ORDER BY子句
            let orderClause = '';
            if (sortBy && columnNames.includes(sortBy)) {
                const validSortOrder = ['asc', 'desc'].includes(sortOrder.toLowerCase()) ? sortOrder.toLowerCase() : 'desc';
                orderClause = `ORDER BY ${sortBy} ${validSortOrder}`;
            } else {
                // 默认排序：优先按时间戳降序，其次按ID降序
                if (dateColumn) {
                    orderClause = `ORDER BY ${dateColumn} DESC`;
                } else if (columnNames.includes('id')) {
                    orderClause = `ORDER BY id DESC`;
                }
            }

            // 获取总记录数
            db.get(`SELECT COUNT(*) as total FROM ${tableName} ${whereClause}`, queryParams, (err, countResult) => {
                if (err) {
                    db.close();
                    return handleError(res, err, '获取记录数失败');
                }

                const total = countResult.total;

                // 获取分页数据
                const dataQuery = `SELECT * FROM ${tableName} ${whereClause} ${orderClause} LIMIT ? OFFSET ?`;
                const dataParams = [...queryParams, limit, offset];

                console.log(`📊 [${tableName}] SQL查询:`, dataQuery);
                console.log(`📊 [${tableName}] 查询参数:`, dataParams);

                db.all(dataQuery, dataParams, (err, rows) => {
                    db.close();

                    if (err) {
                        return handleError(res, err, '获取数据失败');
                    }

                    sendSuccess(res, {
                        data: rows,
                        columns: columns.map(col => ({
                            name: col.name,
                            type: col.type,
                            nullable: !col.notnull,
                            primaryKey: col.pk === 1
                        })),
                        pagination: {
                            page,
                            limit,
                            total,
                            totalPages: Math.ceil(total / limit)
                        }
                    });
                });
            });
        });
    });

    // 创建新记录
    router.post('/table/:tableName', (req, res) => {
        const { tableName } = req.params;
        const data = req.body;

        const db = getDatabase();

        // 获取表结构以验证字段
        db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
            if (err) {
                db.close();
                return handleError(res, err, '获取表结构失败');
            }

            // 过滤有效字段（排除主键自增字段）
            const validColumns = columns.filter(col =>
                data.hasOwnProperty(col.name) &&
                !(col.pk === 1 && col.type.toLowerCase().includes('integer'))
            );

            if (validColumns.length === 0) {
                db.close();
                return res.status(400).json({
                    success: false,
                    error: '没有有效的字段数据'
                });
            }

            const columnNames = validColumns.map(col => col.name);
            const placeholders = new Array(columnNames.length).fill('?');
            const values = columnNames.map(name => data[name]);

            const insertQuery = `
                INSERT INTO ${tableName} (${columnNames.join(', ')})
                VALUES (${placeholders.join(', ')})
            `;

            db.run(insertQuery, values, function(err) {
                db.close();

                if (err) {
                    return handleError(res, err, '创建记录失败');
                }

                sendSuccess(res, {
                    id: this.lastID,
                    affectedRows: this.changes
                }, '记录创建成功');
            });
        });
    });

    // 更新记录
    router.put('/table/:tableName/:id', (req, res) => {
        const { tableName, id } = req.params;
        const data = req.body;

        const db = getDatabase();

        // 获取表结构
        db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
            if (err) {
                db.close();
                return handleError(res, err, '获取表结构失败');
            }

            // 过滤有效字段（排除主键）
            const validColumns = columns.filter(col =>
                data.hasOwnProperty(col.name) && col.pk !== 1
            );

            if (validColumns.length === 0) {
                db.close();
                return res.status(400).json({
                    success: false,
                    error: '没有有效的字段数据'
                });
            }

            const setClause = validColumns.map(col => `${col.name} = ?`).join(', ');
            const values = validColumns.map(col => data[col.name]);
            values.push(id);

            const updateQuery = `UPDATE ${tableName} SET ${setClause} WHERE id = ? OR rowid = ?`;
            values.push(id); // 添加第二个id参数用于rowid匹配

            db.run(updateQuery, values, function(err) {
                db.close();

                if (err) {
                    return handleError(res, err, '更新记录失败');
                }

                if (this.changes === 0) {
                    return res.status(404).json({
                        success: false,
                        error: '记录不存在'
                    });
                }

                sendSuccess(res, {
                    affectedRows: this.changes
                }, '记录更新成功');
            });
        });
    });

    // 删除记录
    router.delete('/table/:tableName/:id', (req, res) => {
        const { tableName, id } = req.params;
        const db = getDatabase();

        const deleteQuery = `DELETE FROM ${tableName} WHERE id = ? OR rowid = ?`;

        db.run(deleteQuery, [id, id], function(err) {
            db.close();

            if (err) {
                return handleError(res, err, '删除记录失败');
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    error: '记录不存在'
                });
            }

            sendSuccess(res, {
                affectedRows: this.changes
            }, '记录删除成功');
        });
    });

    // 清空表格 - 修复路由冲突，使用不同的路径
    router.delete('/clear-table/:tableName', (req, res) => {
        const { tableName } = req.params;
        const db = getDatabase();

        console.log(`🗑️ [清空表格] 请求清空表格: ${tableName}`);

        // 先获取记录数量
        db.get(`SELECT COUNT(*) as count FROM ${tableName}`, (err, row) => {
            if (err) {
                db.close();
                console.error(`❌ [清空表格] 获取记录数量失败:`, err);
                return handleError(res, err, '获取记录数量失败');
            }

            const recordCount = row.count;
            console.log(`📊 [清空表格] 表格 ${tableName} 当前有 ${recordCount} 条记录`);

            // 清空表格
            const clearQuery = `DELETE FROM ${tableName}`;

            db.run(clearQuery, function(err) {
                db.close();

                if (err) {
                    console.error(`❌ [清空表格] 清空失败:`, err);
                    return handleError(res, err, '清空表格失败');
                }

                console.log(`✅ [清空表格] 成功清空表格 ${tableName}，删除了 ${this.changes} 条记录`);
                sendSuccess(res, {
                    affectedRows: this.changes,
                    originalCount: recordCount
                }, `表格已清空，共删除 ${this.changes} 条记录`);
            });
        });
    });

    // 导出表格数据为CSV
    router.get('/export/:tableName', (req, res) => {
        const { tableName } = req.params;
        const db = getDatabase();

        // 获取所有数据
        db.all(`SELECT * FROM ${tableName}`, (err, rows) => {
            if (err) {
                db.close();
                return handleError(res, err, '导出数据失败');
            }

            if (!rows || rows.length === 0) {
                db.close();
                return res.status(404).json({
                    success: false,
                    error: '没有数据可导出'
                });
            }

            // 生成CSV内容
            const headers = Object.keys(rows[0]);
            const csvContent = [
                headers.join(','), // CSV头部
                ...rows.map(row =>
                    headers.map(header => {
                        const value = row[header];
                        // 处理包含逗号或引号的值
                        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                            return `"${value.replace(/"/g, '""')}"`;
                        }
                        return value || '';
                    }).join(',')
                )
            ].join('\n');

            db.close();

            // 设置响应头
            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader('Content-Disposition', `attachment; filename="${tableName}_${new Date().toISOString().slice(0, 10)}.csv"`);

            // 添加BOM以支持中文
            res.send('\ufeff' + csvContent);
        });
    });

    // 批量删除记录
    router.delete('/table/:tableName/batch', (req, res) => {
        const { tableName } = req.params;
        const { ids } = req.body;

        if (!Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                success: false,
                error: '请提供要删除的记录ID数组'
            });
        }

        const db = getDatabase();
        const placeholders = ids.map(() => '?').join(',');
        const deleteQuery = `DELETE FROM ${tableName} WHERE id IN (${placeholders}) OR rowid IN (${placeholders})`;
        const params = [...ids, ...ids];

        db.run(deleteQuery, params, function(err) {
            db.close();

            if (err) {
                return handleError(res, err, '批量删除失败');
            }

            sendSuccess(res, {
                affectedRows: this.changes,
                deletedCount: this.changes
            }, `成功删除 ${this.changes} 条记录`);
        });
    });

    // 获取表格结构信息
    router.get('/table/:tableName/schema', (req, res) => {
        const { tableName } = req.params;
        const db = getDatabase();

        db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
            if (err) {
                db.close();
                return handleError(res, err, '获取表结构失败');
            }

            if (!columns || columns.length === 0) {
                db.close();
                return res.status(404).json({
                    success: false,
                    error: '表不存在'
                });
            }

            // 获取外键信息
            db.all(`PRAGMA foreign_key_list(${tableName})`, (err, foreignKeys) => {
                db.close();

                if (err) {
                    return handleError(res, err, '获取外键信息失败');
                }

                sendSuccess(res, {
                    tableName,
                    columns: columns.map(col => ({
                        name: col.name,
                        type: col.type,
                        nullable: !col.notnull,
                        primaryKey: col.pk === 1,
                        defaultValue: col.dflt_value
                    })),
                    foreignKeys: foreignKeys || []
                });
            });
        });
    });

    // 初始化系统状态数据
    router.post('/system/init-state', (req, res) => {
        const db = getDatabase();

        const defaultStates = [
            { key: 'system_version', value: '2.0.0' },
            { key: 'last_cleanup_time', value: new Date().toISOString() },
            { key: 'total_conversations_processed', value: '0' },
            { key: 'memory_optimization_enabled', value: 'true' },
            { key: 'emotion_analysis_enabled', value: 'true' },
            { key: 'concept_learning_enabled', value: 'true' },
            { key: 'max_memory_fragments', value: '10000' },
            { key: 'cleanup_interval_hours', value: '24' }
        ];

        let completed = 0;
        const total = defaultStates.length;

        defaultStates.forEach(state => {
            db.run(
                `INSERT OR IGNORE INTO system_state (state_key, state_value, last_updated) VALUES (?, ?, ?)`,
                [state.key, state.value, new Date().toISOString()],
                function(err) {
                    if (err) {
                        console.error('插入系统状态失败:', err);
                    }
                    completed++;

                    if (completed === total) {
                        db.close();
                        sendSuccess(res, {
                            message: '系统状态初始化完成',
                            initialized: total
                        });
                    }
                }
            );
        });
    });

    // 执行自定义SQL查询（仅限SELECT，安全考虑）
    router.post('/query', (req, res) => {
        const { sql } = req.body;

        if (!sql || typeof sql !== 'string') {
            return res.status(400).json({
                success: false,
                error: '请提供有效的SQL查询'
            });
        }

        // 安全检查：只允许SELECT查询
        const trimmedSql = sql.trim().toLowerCase();
        if (!trimmedSql.startsWith('select')) {
            return res.status(403).json({
                success: false,
                error: '出于安全考虑，只允许执行SELECT查询'
            });
        }

        const db = getDatabase();

        db.all(sql, (err, rows) => {
            db.close();

            if (err) {
                return handleError(res, err, 'SQL查询执行失败');
            }

            sendSuccess(res, {
                rows,
                count: rows ? rows.length : 0
            });
        });
    });

    // 保存对话记录到数据库 - 统一接口（优化版：支持智能合并）
    router.post('/conversation/save', async (req, res) => {
        const { userId, personaName, userMessage, aiResponse, conversationId, chatType, chatName, emotionState, importanceScore, timestamp } = req.body;

        // 参数验证
        if (!userId || !personaName || !userMessage || !aiResponse) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数',
                required: ['userId', 'personaName', 'userMessage', 'aiResponse']
            });
        }

        try {
            const db = getDatabase();
            
            // 如果有智能情感记忆系统插件，使用插件的智能对话管理
            if (global.advancedMemoryPlugin && global.advancedMemoryPlugin.conversationManager) {
                const conversationManager = global.advancedMemoryPlugin.conversationManager;
                
                // 构建本地时间戳
                const localTimestamp = timestamp || conversationManager.getLocalTimestamp();
                
                // 使用智能对话记录
                const result = await conversationManager.addConversation(
                    userId,
                    userMessage,
                    aiResponse,
                    localTimestamp,
                    {
                        personaName: personaName,
                        conversationId: conversationId,
                        chatType: chatType || 'private',
                        chatName: chatName || '',
                        emotionData: emotionState,
                        userEmotion: emotionState,
                        aiEmotion: emotionState
                    }
                );

                if (result.success) {
                    if (debugMode) {
                        console.log(`🔍 [DEBUG] 智能对话记录成功: 会话=${result.session_id}, 动作=${result.action}, 消息数=${result.message_count}`);
                    }

                    sendSuccess(res, {
                        sessionId: result.session_id,
                        conversationId: result.id,
                        action: result.action,
                        messageCount: result.message_count,
                        userMessageId: result.userMessageId,
                        assistantMessageId: result.assistantMessageId,
                        timestamp: result.timestamp,
                        importanceScore: result.userImportanceScore
                    }, `智能对话记录成功 (${result.action})`);
                } else {
                    throw new Error(result.error || '智能对话记录失败');
                }
            } else {
                // 回退到传统数据库操作
        const finalTimestamp = timestamp || new Date().toISOString();
        const finalConversationId = conversationId || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const finalEmotionState = emotionState || { valence: 0.5, arousal: 0.5, dominance: 0.5 };
        const finalImportanceScore = importanceScore || 0.5;
                const finalChatType = chatType || 'private';

                // 插入对话记录到传统表结构
        db.run(`
            INSERT INTO conversation_history (
                user_id, persona_name, conversation_id, speaker, content,
                        embedding_vector, emotion_state, local_timestamp, tokens, importance_score, chat_type, chat_name
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
                    userId, personaName, finalConversationId, 'user', userMessage,
                    null, JSON.stringify(finalEmotionState), finalTimestamp,
                    userMessage.length, finalImportanceScore, finalChatType, chatName || null
                ], function(userErr) {
                    if (userErr) {
            db.close();
                        return handleError(res, userErr, '保存用户消息失败');
                    }

                    const userMessageId = this.lastID;

                    db.run(`
                        INSERT INTO conversation_history (
                            user_id, persona_name, conversation_id, speaker, content,
                            embedding_vector, emotion_state, local_timestamp, tokens, importance_score, chat_type, chat_name
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        userId, personaName, finalConversationId, 'assistant', aiResponse,
                        null, JSON.stringify(finalEmotionState), finalTimestamp,
                        aiResponse.length, finalImportanceScore, finalChatType, chatName || null
                    ], function(assistantErr) {
                        db.close();

                        if (assistantErr) {
                            return handleError(res, assistantErr, '保存助手回复失败');
            }

            if (debugMode) {
                            console.log(`🔍 [DEBUG] 传统对话记录已保存: 用户消息ID=${userMessageId}, 助手消息ID=${this.lastID}`);
            }

            sendSuccess(res, {
                conversationId: finalConversationId,
                            userMessageId: userMessageId,
                            assistantMessageId: this.lastID,
                timestamp: finalTimestamp,
                importanceScore: finalImportanceScore,
                chatType: finalChatType
                        }, '对话记录保存成功（传统模式）');
        });
                });
            }
        } catch (error) {
            console.error('[Conversation Save Error]:', error);
            return handleError(res, error, '保存对话记录失败');
        }
    });

    // 获取对话会话列表（新增）
    router.get('/conversation/sessions', (req, res) => {
        const { userId, personaName, chatType, limit = 10 } = req.query;

        if (!userId || !personaName) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数：userId 和 personaName'
            });
        }

        try {
            // 如果有智能情感记忆系统插件，使用插件获取会话
            if (global.advancedMemoryPlugin && global.advancedMemoryPlugin.conversationManager) {
                global.advancedMemoryPlugin.conversationManager.getRecentSessions(userId, personaName, parseInt(limit), chatType)
                    .then(sessions => {
                        if (debugMode) {
                            console.log(`🔍 [DEBUG] 获取对话会话: 用户=${userId}, 助手=${personaName}, 会话数=${sessions.length}`);
                        }

                        sendSuccess(res, {
                            sessions: sessions,
                            total: sessions.length,
                            userId: userId,
                            personaName: personaName,
                            chatType: chatType
                        }, '获取对话会话成功');
                    })
                    .catch(error => {
                        console.error('[Sessions Get Error]:', error);
                        handleError(res, error, '获取对话会话失败');
                    });
            } else {
                // 回退到传统查询
                const db = getDatabase();
                let query = `
                    SELECT conversation_id, 
                           MAX(local_timestamp) as last_update_time,
                           COUNT(*) as message_count,
                           chat_type, chat_name
                    FROM conversation_history 
                    WHERE user_id = ? AND persona_name = ?
                `;
                let params = [userId, personaName];

                if (chatType) {
                    query += ` AND chat_type = ?`;
                    params.push(chatType);
                }

                query += ` GROUP BY conversation_id ORDER BY last_update_time DESC LIMIT ?`;
                params.push(parseInt(limit));

                db.all(query, params, (err, rows) => {
                    db.close();

                    if (err) {
                        return handleError(res, err, '获取对话会话失败');
                    }

                    if (debugMode) {
                        console.log(`🔍 [DEBUG] 传统模式获取对话会话: ${rows.length}个会话`);
                    }

                    sendSuccess(res, {
                        sessions: rows || [],
                        total: rows ? rows.length : 0,
                        userId: userId,
                        personaName: personaName,
                        chatType: chatType,
                        mode: 'traditional'
                    }, '获取对话会话成功（传统模式）');
                });
            }
        } catch (error) {
            console.error('[Sessions Get Error]:', error);
            return handleError(res, error, '获取对话会话失败');
        }
    });

    // 获取对话统计信息（优化版）
    router.get('/conversation/stats', (req, res) => {
        const { userId, personaName } = req.query;

        try {
            // 如果有智能情感记忆系统插件，使用插件获取统计
            if (global.advancedMemoryPlugin && global.advancedMemoryPlugin.conversationManager) {
                global.advancedMemoryPlugin.conversationManager.getConversationStats(userId, personaName)
                    .then(stats => {
                        if (debugMode) {
                            console.log(`🔍 [DEBUG] 获取对话统计: 会话=${stats.sessions}, 总消息=${stats.total_messages}`);
                        }

                        sendSuccess(res, {
                            ...stats,
                            mode: 'intelligent',
                            query: { userId, personaName }
                        }, '获取对话统计成功');
                    })
                    .catch(error => {
                        console.error('[Stats Get Error]:', error);
                        handleError(res, error, '获取对话统计失败');
                    });
            } else {
                // 回退到传统查询
                const db = getDatabase();
                let query = `
                    SELECT 
                        COUNT(*) as total_messages,
                        COUNT(DISTINCT conversation_id) as conversations,
                        MIN(local_timestamp) as first_message,
                        MAX(local_timestamp) as last_message
                    FROM conversation_history WHERE 1=1
                `;
                let params = [];

                if (userId && personaName) {
                    query += ` AND user_id = ? AND persona_name = ?`;
                    params = [userId, personaName];
                } else if (userId) {
                    query += ` AND user_id = ?`;
                    params = [userId];
                }

                db.get(query, params, (err, row) => {
                    db.close();

                    if (err) {
                        return handleError(res, err, '获取对话统计失败');
                    }

                    const stats = {
                        sessions: row?.conversations || 0,
                        total_messages: row?.total_messages || 0,
                        detailed_history: row?.total_messages || 0,
                        first_message: row?.first_message,
                        last_message: row?.last_message,
                        mode: 'traditional'
                    };

                    if (debugMode) {
                        console.log(`🔍 [DEBUG] 传统模式对话统计:`, stats);
                    }

                    sendSuccess(res, {
                        ...stats,
                        query: { userId, personaName }
                    }, '获取对话统计成功（传统模式）');
                });
            }
        } catch (error) {
            console.error('[Stats Get Error]:', error);
            return handleError(res, error, '获取对话统计失败');
        }
    });

    // 清理旧对话数据（新增）
    router.delete('/conversation/cleanup', (req, res) => {
        const { retentionDays = 90 } = req.body;

        if (!Number.isInteger(retentionDays) || retentionDays < 1) {
            return res.status(400).json({
                success: false,
                error: '保留天数必须是正整数'
            });
        }

        try {
            // 如果有智能情感记忆系统插件，使用插件清理
            if (global.advancedMemoryPlugin && global.advancedMemoryPlugin.conversationManager) {
                global.advancedMemoryPlugin.conversationManager.cleanupOldConversations(retentionDays)
                    .then(result => {
                        if (result.success) {
                            logger.info('对话清理', `清理完成 - 会话: ${result.cleaned_sessions}条, 历史: ${result.cleaned_history}条`);
                            
                            sendSuccess(res, {
                                cleaned_sessions: result.cleaned_sessions,
                                cleaned_history: result.cleaned_history,
                                retention_days: retentionDays,
                                mode: 'intelligent'
                            }, `成功清理${retentionDays}天前的对话数据`);
                        } else {
                            throw new Error(result.error);
                        }
                    })
                    .catch(error => {
                        console.error('[Cleanup Error]:', error);
                        handleError(res, error, '清理对话数据失败');
                    });
            } else {
                // 回退到传统清理
                const db = getDatabase();
                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
                const cutoffTimestamp = cutoffDate.toISOString();

                db.run(`DELETE FROM conversation_history WHERE local_timestamp < ?`, [cutoffTimestamp], function(err) {
                    db.close();

                    if (err) {
                        return handleError(res, err, '清理对话数据失败');
                    }

                    const cleanedCount = this.changes || 0;
                    logger.info('对话清理', `传统模式清理完成，删除${cleanedCount}条记录`);

                    sendSuccess(res, {
                        cleaned_history: cleanedCount,
                        retention_days: retentionDays,
                        cutoff_timestamp: cutoffTimestamp,
                        mode: 'traditional'
                    }, `成功清理${retentionDays}天前的对话数据（传统模式）`);
                });
            }
        } catch (error) {
            console.error('[Cleanup Error]:', error);
            return handleError(res, error, '清理对话数据失败');
        }
    });

    // 查询群聊历史记录
    router.get('/conversation/group/:groupName', (req, res) => {
        const { groupName } = req.params;
        const { limit = 10, date } = req.query;

        if (!groupName) {
            return res.status(400).json({
                success: false,
                error: '缺少群聊名称参数'
            });
        }

        const db = getDatabase();

        // 构建查询条件
        let whereClause = `conversation_id LIKE ?`;
        let params = [`group_${groupName}_%`];

        if (date) {
            const dateStr = date.replace(/-/g, '');
            whereClause += ` AND conversation_id LIKE ?`;
            params = [`group_${groupName}_${dateStr}%`];
        }

        // 查询群聊历史
        db.all(`
            SELECT id, user_id, speaker, content, timestamp, importance_score, conversation_id
            FROM conversation_history
            WHERE ${whereClause}
            AND speaker IN ('user', 'assistant')
            ORDER BY timestamp DESC
            LIMIT ?
        `, [...params, parseInt(limit)], (err, rows) => {
            db.close();

            if (err) {
                return handleError(res, err, '查询群聊历史失败');
            }

            // 格式化结果
            const formattedHistory = rows.reverse().map(row => ({
                id: row.id,
                userId: row.user_id,
                speaker: row.speaker,
                content: row.content,
                timestamp: row.timestamp,
                importanceScore: row.importance_score,
                conversationId: row.conversation_id
            }));

            sendSuccess(res, {
                groupName,
                history: formattedHistory,
                count: formattedHistory.length,
                limit: parseInt(limit)
            }, '群聊历史查询成功');
        });
    });

    // 查询私聊历史记录
    router.get('/conversation/private/:userId/:personaName', (req, res) => {
        const { userId, personaName } = req.params;
        const { limit = 10 } = req.query;

        if (!userId || !personaName) {
            return res.status(400).json({
                success: false,
                error: '缺少用户ID或助手名称参数'
            });
        }

        const db = getDatabase();

        // 查询私聊历史
        db.all(`
            SELECT id, conversation_id, speaker, content, timestamp, importance_score
            FROM conversation_history
            WHERE user_id = ? AND persona_name = ?
            AND speaker IN ('user', 'assistant')
            ORDER BY timestamp DESC
            LIMIT ?
        `, [userId, personaName, parseInt(limit)], (err, rows) => {
            db.close();

            if (err) {
                return handleError(res, err, '查询私聊历史失败');
            }

            // 格式化结果
            const formattedHistory = rows.reverse().map(row => ({
                id: row.id,
                conversationId: row.conversation_id,
                speaker: row.speaker,
                content: row.content,
                timestamp: row.timestamp,
                importanceScore: row.importance_score
            }));

            sendSuccess(res, {
                userId,
                personaName,
                history: formattedHistory,
                count: formattedHistory.length,
                limit: parseInt(limit)
            }, '私聊历史查询成功');
        });
    });

    // 添加测试数据API
    router.post('/add-test-data', (req, res) => {
        const db = getDatabase();

        try {
            // 添加测试用户数据
            const testUsers = [
                { user_id: 'user_001', persona_name: 'Assistant', content: '你好，我是新用户' },
                { user_id: 'user_002', persona_name: 'Assistant', content: '我想学习编程' },
                { user_id: 'user_003', persona_name: 'Assistant', content: '今天天气真好' }
            ];

            let completed = 0;
            const total = testUsers.length * 3; // 每个用户添加3条记录

            testUsers.forEach((user, index) => {
                // 添加对话历史
                db.run(`INSERT OR IGNORE INTO conversation_history
                    (user_id, persona_name, speaker, content, timestamp)
                    VALUES (?, ?, 'user', ?, datetime('now'))`,
                    [user.user_id, user.persona_name, user.content],
                    function(err) {
                        if (err) console.error('添加对话历史失败:', err);
                        completed++;
                        checkComplete();
                    });

                // 添加用户好感度
                db.run(`INSERT OR IGNORE INTO user_affinity
                    (user_id, persona_name, current_affinity, relationship_type, total_interactions)
                    VALUES (?, ?, ?, 'friend', 1)`,
                    [user.user_id, user.persona_name, Math.random() * 100 - 50],
                    function(err) {
                        if (err) console.error('添加用户好感度失败:', err);
                        completed++;
                        checkComplete();
                    });

                // 添加记忆片段
                db.run(`INSERT OR IGNORE INTO memory_fragments
                    (user_id, persona_name, memory_type, content, importance_score)
                    VALUES (?, ?, 'short_term', ?, ?)`,
                    [user.user_id, user.persona_name, `记忆: ${user.content}`, Math.random()],
                    function(err) {
                        if (err) console.error('添加记忆片段失败:', err);
                        completed++;
                        checkComplete();
                    });
            });

            function checkComplete() {
                if (completed === total) {
                    db.close();
                    sendSuccess(res, { message: '测试数据添加成功', count: testUsers.length });
                }
            }

        } catch (error) {
            db.close();
            handleError(res, error, '添加测试数据失败');
        }
    });

    return router;
};
