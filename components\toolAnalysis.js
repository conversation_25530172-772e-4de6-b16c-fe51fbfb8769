// components/toolAnalysis.js
// 工具分析相关函数

/**
 * 格式化工具分析结果为system消息格式
 */
function formatToolAnalysisForSystem(analysisData, toolSummary, totalToolCount, userInfo = null) {
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });

    // 使用传入的用户信息，如果没有则使用默认值
    const userName = userInfo?.userName || '用户';
    const assistantDisplayName = userInfo?.assistantDisplayName || 'AI助手';

    let formattedInfo = `\n\n=== 🔧 ${assistantDisplayName}的工具调用分析 ===\n`;
    formattedInfo += `📅 分析时间: ${timestamp}\n`;
    formattedInfo += `🎯 ${userName}的需求: ${analysisData.needs_tools ? '需要工具支持' : '普通对话交流'}\n`;

    if (analysisData.needs_tools && analysisData.required_tools && analysisData.required_tools.length > 0) {
        formattedInfo += `🛠️ ${assistantDisplayName}的执行计划: 共${totalToolCount}个工具 (${toolSummary})\n`;
        formattedInfo += `📋 执行顺序: ${analysisData.required_tools.map((tool, index) => `${index + 1}.${tool}`).join(' → ')}\n`;
        formattedInfo += `🧠 分析推理: ${analysisData.reasoning}\n`;
        formattedInfo += `⚡ 执行模式: ${assistantDisplayName}将逐个执行工具，每个工具完成后生成回复再执行下一个\n`;

        // 添加工具执行指导
        formattedInfo += `\n📌 ${assistantDisplayName}的行为指导:\n`;
        formattedInfo += `• 系统将按顺序强制执行上述工具来帮助${userName}\n`;
        formattedInfo += `• 每个工具执行完成后，${assistantDisplayName}需要基于工具结果生成有意义的回复给${userName}\n`;
        formattedInfo += `• 如果是多次执行同一工具，${assistantDisplayName}每次都要有不同的处理方式\n`;
        formattedInfo += `• 工具执行结果会自动添加到对话上下文中\n`;
        formattedInfo += `• ${assistantDisplayName}请根据工具执行的实际结果来调整回复内容，确保满足${userName}的需求\n`;

    } else {
        formattedInfo += `💬 对话模式: ${userName}只是想和${assistantDisplayName}聊天，无需工具支持\n`;
        formattedInfo += `🧠 分析推理: ${analysisData.reasoning}\n`;
        formattedInfo += `\n📌 ${assistantDisplayName}的行为指导:\n`;
        formattedInfo += `• 这是${userName}和${assistantDisplayName}之间的普通对话交流，不涉及特殊功能\n`;
        formattedInfo += `• ${assistantDisplayName}请以自然、友好的方式回应${userName}\n`;
        formattedInfo += `• 专注于理解${userName}的意图并提供有帮助的回复\n`;
    }

    formattedInfo += `=== 🔧 工具分析结束 ===\n\n`;

    return formattedInfo;
}

module.exports = {
    formatToolAnalysisForSystem
};
