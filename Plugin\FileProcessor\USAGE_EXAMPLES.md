# FileProcessor 使用示例

## 🎯 插件功能验证

✅ **MCP插件加载**: FileProcessor已成功加载到MCP系统中
✅ **参数验证**: 支持必需参数`links`、`prompts`和可选参数`models`
✅ **OpenAI Tools**: 已生成标准的OpenAI Tools格式接口
✅ **文件类型检测**: 使用mime-types库准确识别各种文件类型
✅ **数组标准化**: 智能处理不同长度的参数数组
✅ **错误处理**: 完善的错误处理和异常捕获机制
✅ **依赖优化**: 移除不必要的依赖，使用mime-types替代硬编码扩展名
✅ **日志清理**: 移除所有console.log输出，避免日志污染

## 📝 使用示例

### 1. 基本用法 - 单个图片分析

```json
{
  "tool_calls": [
    {
      "function": {
        "name": "FileProcessor",
        "arguments": {
          "links": ["http://localhost:6005/pw=vcptools123/images/novelaigen/453fd497-e7ac-4392-98c1-26818ecee77a.webp"],
          "prompts": ["请分析这张图片的艺术风格、色彩搭配和情感表达"]
        }
      }
    }
  ]
}
```

### 2. 多文件处理

```json
{
  "tool_calls": [
    {
      "function": {
        "name": "FileProcessor",
        "arguments": {
          "links": [
            "http://example.com/architecture.png",
            "http://example.com/project.pdf",
            "http://example.com/report.xlsx",
            "http://example.com/code.py"
          ],
          "prompts": [
            "分析这张技术架构图的设计思路",
            "总结项目文档的主要内容",
            "分析数据报告的关键指标",
            "分析代码的功能和结构"
          ],
          "models": ["gpt-4o", "gpt-4-turbo", "gpt-4-turbo", "gpt-4-turbo"]
        }
      }
    }
  ]
}
```

### 3. 单一提示词应用到多个文件

```json
{
  "tool_calls": [
    {
      "function": {
        "name": "FileProcessor",
        "arguments": {
          "links": [
            "http://example.com/image1.jpg",
            "http://example.com/image2.png",
            "http://example.com/image3.gif"
          ],
          "prompts": ["请详细分析图片内容"],
          "models": ["gpt-4o"]
        }
      }
    }
  ]
}
```

## 📊 返回格式示例

### 成功处理图片
```json
{
  "type": "file_processing",
  "status": "success",
  "message": "文件处理完成",
  "data": {
    "processed_count": 1,
    "results": [
      {
        "url": "http://localhost:6005/images/cat.webp",
        "alt": "一只可爱的动漫风格猫猫",
        "type": "image",
        "status": "success",
        "model": "gpt-4o",
        "base64_data": "iVBORw0KGgoAAAANSUhEUgAA...",
        "analysis": "这是一张精美的动漫风格插画，展现了一只毛色柔和的猫咪...",
        "tokens_used": {
          "prompt_tokens": 1200,
          "completion_tokens": 150,
          "total_tokens": 1350
        }
      }
    ],
    "original_content": "![一只可爱的动漫风格猫猫...](http://...)",
    "prompt_used": "请分析这张图片的艺术风格、色彩搭配和情感表达",
    "model_used": "gpt-4o",
    "processing_info": {
      "timestamp": "2024-01-15T10:30:15.000Z",
      "plugin_version": "1.0.0"
    }
  }
}
```

### 成功处理文档
```json
{
  "type": "file_processing",
  "status": "success",
  "message": "文件处理完成",
  "data": {
    "processed_count": 1,
    "results": [
      {
        "url": "http://example.com/document.pdf",
        "alt": "技术文档",
        "type": "document",
        "status": "success",
        "model": "gpt-4-turbo",
        "text_content": "第一章 系统架构\n本文档描述了...",
        "analysis": "这是一份详细的技术文档，主要内容包括...",
        "tokens_used": {
          "prompt_tokens": 2500,
          "completion_tokens": 300,
          "total_tokens": 2800
        }
      }
    ]
  }
}
```

### 错误处理示例
```json
{
  "type": "file_processing",
  "status": "error",
  "message": "未找到有效的markdown格式链接",
  "data": {
    "content": "这里没有markdown链接",
    "error": "未找到有效的markdown格式链接",
    "processing_info": {
      "timestamp": "2024-01-15T10:30:15.000Z",
      "plugin_version": "1.0.0"
    }
  }
}
```

## 🔧 配置要求

### 必需配置
```env
# API密钥（必需）
FILEPROCESSOR_API_KEY=your_openai_api_key
# 或使用全局配置
API_Key=your_global_api_key
```

### 可选配置
```env
# API地址
FILEPROCESSOR_API_URL=https://api.openai.com/v1/chat/completions

# 模型配置
FILEPROCESSOR_IMAGE_MODELS=gpt-4o,gpt-4-vision-preview,claude-3-sonnet
FILEPROCESSOR_FILE_MODELS=gpt-4o,gpt-4-turbo,claude-3-sonnet

# 文件限制
FILEPROCESSOR_MAX_FILE_SIZE=52428800  # 50MB
FILEPROCESSOR_DOWNLOAD_TIMEOUT=30000  # 30秒
```

## 🚀 集成说明

### VCP插件调用
```javascript
const pluginManager = global.pluginManager;
const result = await pluginManager.executePlugin('FileProcessor', JSON.stringify({
    content: "![图片](http://example.com/image.jpg)",
    prompt: "请分析图片内容"
}));
```

### MCP插件调用
```javascript
const mcpManager = global.mcpManager;
const result = await mcpManager.executeMcpPlugin('FileProcessor', {
    content: "![图片](http://example.com/image.jpg)",
    prompt: "请分析图片内容"
});
```

## ⚠️ 注意事项

1. **链接格式**: 必须使用标准markdown语法 `![alt](url)`
2. **文件后缀**: URL必须包含有效的文件扩展名
3. **数量限制**: 最多处理4个链接，最少1个链接
4. **文件大小**: 默认限制50MB，可通过配置调整
5. **API配额**: 注意模型调用的费用和限制
6. **网络访问**: 确保服务器能访问目标文件URL
