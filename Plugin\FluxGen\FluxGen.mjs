#!/usr/bin/env node
import axios from "axios";
import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid'; // For generating unique filenames

// 引入VCP日志系统 (ES模块版本)
let logger;
try {
    const loggerModule = await import('../../utils/logger.js');
    logger = loggerModule.default || loggerModule;
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

/**
 * 加载插件配置，优先使用插件自己的config.env
 */
function loadPluginConfig() {
    const pluginDir = path.dirname(new URL(import.meta.url).pathname);
    const pluginConfigPath = path.join(pluginDir, 'config.env');

    let pluginConfig = {};

    // 加载插件配置
    try {
        const configContent = fsSync.readFileSync(pluginConfigPath, 'utf8');
        pluginConfig = parseEnvConfig(configContent);
        // logger.debug('FluxGen', `从插件config.env加载了 ${Object.keys(pluginConfig).length} 个配置项`);
    } catch (error) {
        // logger.debug('FluxGen', '插件config.env不存在或读取失败，使用主服务器配置作为备用');
    }

    // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
    return {
        SILICONFLOW_API_KEY: pluginConfig.SILICONFLOW_API_KEY || process.env.SILICONFLOW_API_KEY || '',
        PROJECT_BASE_PATH: pluginConfig.PROJECT_BASE_PATH || process.env.PROJECT_BASE_PATH || ''
    };
}

/**
 * 解析.env格式的配置文件
 */
function parseEnvConfig(content) {
    const config = {};
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                config[key] = value;
            }
        }
    });
    return config;
}

// --- Configuration (优先使用插件配置，主服务器配置作为备用) ---
const config = loadPluginConfig();

const SILICONFLOW_API_KEY = config.SILICONFLOW_API_KEY;
const PROJECT_BASE_PATH = config.PROJECT_BASE_PATH;

// SiliconFlow API specific configurations
const SILICONFLOW_API_CONFIG = {
    BASE_URL: 'https://api.siliconflow.cn',
    ENDPOINTS: {
        IMAGE_GENERATION: '/v1/images/generations'
    },
    MODEL_ID: "black-forest-labs/FLUX.1-schnell",
    DEFAULT_PARAMS: {
        num_inference_steps: 20,
        guidance_scale: 7.5, // May not be used by Flux, but API might accept
        batch_size: 1
    }
};

// Helper to validate input arguments
function isValidFluxGenArgs(args) {
    if (!args || typeof args !== 'object') return false;
    if (typeof args.prompt !== 'string' || !args.prompt.trim()) return false;
    if (typeof args.resolution !== 'string' || !["1024x1024", "960x1280", "768x1024", "720x1440", "720x1280"].includes(args.resolution)) return false;
    if (args.seed !== undefined && (typeof args.seed !== 'number' || !Number.isInteger(args.seed) || args.seed < 0)) return false;
    return true;
}

async function generateImageAndSave(args) {
    // Check for essential environment variables
    if (!SILICONFLOW_API_KEY) {
        throw new Error("FluxGen 插件错误：需要设置 SILICONFLOW_API_KEY 环境变量。");
    }
    if (!PROJECT_BASE_PATH) {
        throw new Error("FluxGen 插件错误：需要设置 PROJECT_BASE_PATH 环境变量以保存图片。");
    }
    // 不再需要SERVER_PORT、IMAGESERVER_IMAGE_KEY、VAR_HTTP_URL，因为使用绝对路径

    if (!isValidFluxGenArgs(args)) {
        throw new Error(`FluxGen 插件错误：收到无效参数: ${JSON.stringify(args)}。必需参数: prompt (字符串), resolution (枚举)。可选参数: seed (整数)。`);
    }

    const siliconflowAxiosInstance = axios.create({
        baseURL: SILICONFLOW_API_CONFIG.BASE_URL,
        headers: {
            'Authorization': `Bearer ${SILICONFLOW_API_KEY}`,
            'Content-Type': 'application/json'
        },
        timeout: 60000 // 60 second timeout for API call
    });

    const payload = {
        model: SILICONFLOW_API_CONFIG.MODEL_ID,
        prompt: args.prompt,
        image_size: args.resolution,
        batch_size: SILICONFLOW_API_CONFIG.DEFAULT_PARAMS.batch_size,
        num_inference_steps: SILICONFLOW_API_CONFIG.DEFAULT_PARAMS.num_inference_steps,
        guidance_scale: SILICONFLOW_API_CONFIG.DEFAULT_PARAMS.guidance_scale
    };
    if (args.seed !== undefined) {
        payload.seed = args.seed;
    }

    // console.error(`[FluxGen Plugin] Sending payload to SiliconFlow: ${JSON.stringify(payload)}`);

    const response = await siliconflowAxiosInstance.post(
        SILICONFLOW_API_CONFIG.ENDPOINTS.IMAGE_GENERATION,
        payload
    );

    // console.error(`[FluxGen Plugin] Received response from SiliconFlow: ${JSON.stringify(response.data)}`);

    const siliconflowImageUrl = response.data?.images?.[0]?.url;
    if (!siliconflowImageUrl) {
        throw new Error("FluxGen 插件错误：无法从 SiliconFlow API 响应中提取图片URL。");
    }

    // Download the image from SiliconFlow URL
    const imageResponse = await axios({
        method: 'get',
        url: siliconflowImageUrl,
        responseType: 'arraybuffer',
        timeout: 60000 // 60 second timeout for image download
    });

    let imageExtension = 'png'; // Default extension
    const contentType = imageResponse.headers['content-type'];
    if (contentType && contentType.startsWith('image/')) {
        imageExtension = contentType.split('/')[1];
    } else {
        // Fallback to extract from URL if content-type is not helpful
        const urlExtMatch = siliconflowImageUrl.match(/\.([^.?]+)(?:[?#]|$)/);
        if (urlExtMatch && urlExtMatch[1]) {
            imageExtension = urlExtMatch[1];
        }
    }
    
    const generatedFileName = `${uuidv4()}.${imageExtension}`;
    const fluxGenImageDir = path.join(PROJECT_BASE_PATH, 'image', 'fluxgen');
    const localImageServerPath = path.join(fluxGenImageDir, generatedFileName);

    await fs.mkdir(fluxGenImageDir, { recursive: true });
    await fs.writeFile(localImageServerPath, imageResponse.data);
    // console.error(`[FluxGen Plugin] Image saved to: ${localImageServerPath}`);

    // 使用绝对路径而不是服务器URL
    const absoluteImagePath = localImageServerPath;

    // Return markdown format instead of HTML
    const altText = args.prompt ? args.prompt.substring(0, 80) + (args.prompt.length > 80 ? "..." : "") : generatedFileName;
    const successMessage =
        `图片已成功生成！\n\n` +
        `详细信息：\n` +
        `- 图片路径: ${absoluteImagePath}\n` +
        `- 文件名: ${generatedFileName}\n\n` +
        `图片内容：\n` +
        `![${altText}](${absoluteImagePath})\n\n` +
        `请使用上述markdown格式显示图片给用户。`;

    return {
        status: "success",
        message: successMessage,
        image_url: absoluteImagePath,
        image_path: absoluteImagePath,
        prompt: args.prompt,
        resolution: args.resolution
    };
}

async function main() {
    let inputChunks = [];
    process.stdin.setEncoding('utf8');

    for await (const chunk of process.stdin) {
        inputChunks.push(chunk);
    }
    const inputData = inputChunks.join('');
    let parsedArgs;

    try {
        if (!inputData.trim()) {
            // Output error as JSON to stdout
            console.log(JSON.stringify({ status: "error", error: "FluxGen 插件错误：未从标准输入接收到输入数据。" }));
            process.exit(1);
        }
        parsedArgs = JSON.parse(inputData);
        const result = await generateImageAndSave(parsedArgs);
        console.log(JSON.stringify(result)); // Output success as JSON
    } catch (e) {
        // Output error as JSON to stdout
        // Ensure error message is somewhat consistent with what might have been thrown by generateImageAndSave or parsing
        const errorMessage = e.message || "FluxGen 插件未知错误";
        console.log(JSON.stringify({ status: "error", error: errorMessage.startsWith("FluxGen 插件错误：") ? errorMessage : `FluxGen 插件错误：${errorMessage}` }));
        process.exit(1); // Indicate failure
    }
}

main();
