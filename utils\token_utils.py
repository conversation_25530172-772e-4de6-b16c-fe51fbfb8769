#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
utils/token_utils.py - Python版本的Token计算和截断工具
"""

import os
import re
import sys
import json
import logging

try:
    import tiktoken
except ImportError:
    # 如果没有安装tiktoken，使用简单的估算方法
    tiktoken = None

class TokenUtils:
    def __init__(self):
        # 默认配置
        self.default_config = {
            'enabled': True,
            'max_tokens': 16000,
            'truncate_marker': '...\n\n[内容已截断，超过最大token限制]'
        }
        
        # 初始化编码器
        self.encoder = None
        if tiktoken:
            try:
                self.encoder = tiktoken.encoding_for_model('gpt-4')
            except Exception as e:
                logging.warning(f"初始化tiktoken编码器失败: {e}")
    
    def count_tokens(self, text):
        """计算文本的token数量"""
        if not isinstance(text, str):
            return 0
        
        if self.encoder:
            try:
                tokens = self.encoder.encode(text)
                return len(tokens)
            except Exception as e:
                logging.warning(f"Token计算失败: {e}")
        
        # 回退到字符数除以4的估算方法
        return len(text) // 4
    
    def find_word_boundary(self, text, position):
        """在单词边界截断文本"""
        if position >= len(text):
            return text
        
        # 向前查找最近的空格、换行符或标点符号
        boundaries = [' ', '\n', '\t', '.', '!', '?', ',', ';', ':', '。', '！', '？', '，', '；', '：']
        
        for i in range(position, max(0, position - 100), -1):
            if i < len(text) and text[i] in boundaries:
                return text[:i]
        
        return text[:position]
    
    def truncate_text(self, text, max_tokens=None, truncate_marker=None):
        """截断文本到指定的token数量"""
        if not isinstance(text, str):
            return {
                'text': '',
                'original_tokens': 0,
                'final_tokens': 0,
                'truncated': False
            }
        
        if max_tokens is None:
            max_tokens = self.default_config['max_tokens']
        if truncate_marker is None:
            truncate_marker = self.default_config['truncate_marker']
        
        original_tokens = self.count_tokens(text)
        
        # 如果原始文本已经在限制内，直接返回
        if original_tokens <= max_tokens:
            return {
                'text': text,
                'original_tokens': original_tokens,
                'final_tokens': original_tokens,
                'truncated': False
            }
        
        # 预留截断标记的token数量
        marker_tokens = self.count_tokens(truncate_marker)
        available_tokens = max_tokens - marker_tokens
        
        if available_tokens <= 0:
            return {
                'text': truncate_marker,
                'original_tokens': original_tokens,
                'final_tokens': marker_tokens,
                'truncated': True
            }
        
        # 二分查找最佳截断位置
        left = 0
        right = len(text)
        best_position = 0
        
        while left <= right:
            mid = (left + right) // 2
            substring = text[:mid]
            tokens = self.count_tokens(substring)
            
            if tokens <= available_tokens:
                best_position = mid
                left = mid + 1
            else:
                right = mid - 1
        
        # 尝试在单词边界截断
        truncated_text = self.find_word_boundary(text, best_position)
        final_text = truncated_text + truncate_marker
        final_tokens = self.count_tokens(final_text)
        
        return {
            'text': final_text,
            'original_tokens': original_tokens,
            'final_tokens': final_tokens,
            'truncated': True
        }
    
    def load_config(self, prefix=''):
        """从环境变量加载配置"""
        config = self.default_config.copy()
        
        # 读取环境变量
        enabled_var = f"{prefix}TOKEN_TRUNCATE_ENABLED"
        max_tokens_var = f"{prefix}TOKEN_MAX_TOKENS"
        marker_var = f"{prefix}TOKEN_TRUNCATE_MARKER"
        
        if os.environ.get(enabled_var) is not None:
            config['enabled'] = os.environ.get(enabled_var).lower() == 'true'
        
        if os.environ.get(max_tokens_var) is not None:
            try:
                max_tokens = int(os.environ.get(max_tokens_var))
                if max_tokens > 0:
                    config['max_tokens'] = max_tokens
            except ValueError:
                pass
        
        if os.environ.get(marker_var) is not None:
            config['truncate_marker'] = os.environ.get(marker_var)
        
        return config
    
    def process_plugin_content(self, content, plugin_name=''):
        """处理插件内容的token截断"""
        prefix = f"{plugin_name.upper()}_" if plugin_name else ''
        config = self.load_config(prefix)
        
        if not config['enabled']:
            return {
                'content': content,
                'original_tokens': self.count_tokens(content),
                'final_tokens': self.count_tokens(content),
                'truncated': False,
                'config': config
            }
        
        result = self.truncate_text(content, config['max_tokens'], config['truncate_marker'])
        
        return {
            'content': result['text'],
            'original_tokens': result['original_tokens'],
            'final_tokens': result['final_tokens'],
            'truncated': result['truncated'],
            'config': config
        }

# 创建单例实例
token_utils = TokenUtils()

def process_content_with_tokens(content, plugin_name=''):
    """便捷函数：处理内容的token截断"""
    return token_utils.process_plugin_content(content, plugin_name)

if __name__ == "__main__":
    # 测试代码
    test_text = "这是一个测试文本。" * 1000
    result = process_content_with_tokens(test_text, 'TEST')
    print(json.dumps(result, ensure_ascii=False, indent=2)) 