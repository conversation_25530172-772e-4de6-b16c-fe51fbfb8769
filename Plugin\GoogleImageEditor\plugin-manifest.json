{"manifestVersion": "1.0.0", "name": "GoogleImageEditor", "displayName": "谷歌图像编辑器", "version": "1.0.0", "description": "使用谷歌Gemini模型进行图像编辑的专业工具。支持处理markdown格式的图片链接，可以添加元素、修改风格、改变场景等。输出使用本地绝对路径格式。", "author": "VCPToolBox Team", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node GoogleImageEditor.js"}, "communication": {"protocol": "stdio"}, "configSchema": {"GOOGLE_API_KEY": {"type": "string", "required": true, "description": "谷歌API密钥"}, "GOOGLE_PROXY_URL": {"type": "string", "description": "谷歌API代理地址 (可选)"}, "GEMINI_MODEL": {"type": "string", "default": "gemini-2.0-flash-preview-image-generation", "description": "Gemini模型名称"}, "DEBUG_MODE": {"type": "boolean", "default": false, "description": "调试模式"}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "GoogleImageGenerate", "description": "使用谷歌Gemini模型编辑图像。支持处理markdown格式的图片链接进行图像编辑。请使用以下格式调用：\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」GoogleImageEditor「末」,\nprompt:「始」(必需) 包含图片链接和编辑指令的文本，如：![图片描述](图片路径) 请将背景改为夕阳「末」\n<<<[END_TOOL_REQUEST]>>>\n\n或者单独提供图片路径：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」GoogleImageEditor「末」,\nprompt:「始」(必需) 图像编辑指令「末」,\nimage_path:「始」(可选) 图片文件路径「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示：\n1. 专注于图像编辑功能，输出包含文本和图像\n2. 支持markdown格式的图片链接自动提取\n3. 生成的图像使用本地绝对路径格式\n4. 所有图像都包含SynthID水印"}, {"commandIdentifier": "GoogleImageEdit", "description": "使用谷歌Gemini模型编辑现有图像。可以基于文本指令对图像进行修改、添加元素或改变风格。支持markdown格式图片链接自动提取。请使用以下格式调用：\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」GoogleImageEditor「末」,\nprompt:「始」(必需) 包含图片和编辑指令，如：![原图](D:\\path\\to\\image.png) 添加彩虹效果「末」\n<<<[END_TOOL_REQUEST]>>>\n\n注意事项：\n1. 自动从markdown链接中提取图片路径\n2. 支持本地文件和网络图片\n3. 支持的图像格式：PNG、JPEG、GIF、WebP等\n4. 编辑结果使用本地绝对路径格式"}]}, "dependencies": {"axios": "^1.6.0", "form-data": "^4.0.0", "dotenv": "^16.0.0"}, "tags": ["图像生成", "图像编辑", "AI绘画", "谷歌AI", "Gemini", "Imagen", "文本到图像", "图像处理"], "license": "MIT", "homepage": "https://github.com/your-repo/GoogleImageEditor", "repository": {"type": "git", "url": "https://github.com/your-repo/GoogleImageEditor.git"}, "keywords": ["google", "gemini", "image-generation", "image-editing", "ai-art", "text-to-image"]}