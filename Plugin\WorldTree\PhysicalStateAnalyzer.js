/**
 * 物理状态分析器
 * 保持原有物理层面元素，参考情感记忆算法的格式写法
 * 
 * 物理状态元素：
 * - 专注程度 (Focus)
 * - 精力水平 (Energy) 
 * - 饥饿感 (Hunger)
 * - 疲劳度 (Fatigue)
 * - 警觉性 (Alertness)
 * - 压力水平 (Stress)
 * - 心情 (Mood)
 */

const moment = require('moment');

class PhysicalStateAnalyzer {
    constructor(config = {}) {
        this.config = {
            // 从env配置读取显示选项
            showHunger: config.showHunger !== false,
            showFatigue: config.showFatigue !== false,
            showStress: config.showStress !== false,
            showMood: config.showMood !== false,
            showFocus: config.showFocus !== false,
            showEnergy: config.showEnergy !== false,
            showAlertness: config.showAlertness !== false,
            
            // 分析格式配置
            formatStyle: config.formatStyle || 'scientific',
            showAlgorithmDetails: config.showAlgorithmDetails !== false,
            includeTheoryReferences: config.includeTheoryReferences !== false,
            
            ...config
        };
        
        // 物理状态理论基础
        this.stateTheories = {
            focus: {
                theory: 'Cognitive Load Theory',
                optimal: [70, 90],
                description: '认知负荷理论 - 注意力资源分配'
            },
            energy: {
                theory: 'Circadian Energy Model',
                optimal: [60, 85],
                description: '生理节律能量模型 - 基于昼夜节律'
            },
            fatigue: {
                theory: 'Fatigue Accumulation Model',
                optimal: [0, 30],
                description: '疲劳累积模型 - 基于睡眠压力理论'
            },
            alertness: {
                theory: 'Attention Network Model',
                optimal: [65, 90],
                description: '注意力网络模型 - 警觉性维持机制'
            },
            hunger: {
                theory: 'Metabolic Regulation Theory',
                optimal: [0, 40],
                description: '代谢调节理论 - 基于血糖稳态'
            },
            stress: {
                theory: 'Yerkes-Dodson Law',
                optimal: [20, 50],
                description: '耶克斯-多德森定律 - 压力与表现关系'
            },
            mood: {
                theory: 'Affective Circumplex Model',
                optimal: [60, 80],
                description: '情感环形模型 - 情绪效价维度'
            }
        };
        
        // 状态区间定义
        this.stateRanges = {
            excellent: [85, 100],
            good: [70, 84],
            moderate: [50, 69],
            poor: [30, 49],
            critical: [0, 29]
        };
    }

    /**
     * 分析物理状态（参考情感记忆格式）
     */
    analyzePhysicalState(physicalState, contextFactors = {}) {
        const analysis = {
            timestamp: moment().format('YYYY/MM/DD HH:mm:ss'),
            states: {},
            summary: {},
            conversationalImpact: {}
        };

        // 分析各个物理状态
        if (this.config.showFocus && physicalState.focus !== undefined) {
            analysis.states.focus = this.analyzeStateComponent('focus', physicalState.focus, contextFactors);
        }
        
        if (this.config.showEnergy && physicalState.energy !== undefined) {
            analysis.states.energy = this.analyzeStateComponent('energy', physicalState.energy, contextFactors);
        }
        
        if (this.config.showFatigue && physicalState.fatigue !== undefined) {
            analysis.states.fatigue = this.analyzeStateComponent('fatigue', physicalState.fatigue, contextFactors);
        }
        
        if (this.config.showAlertness && physicalState.alertness !== undefined) {
            analysis.states.alertness = this.analyzeStateComponent('alertness', physicalState.alertness, contextFactors);
        }
        
        if (this.config.showHunger && physicalState.hunger !== undefined) {
            analysis.states.hunger = this.analyzeStateComponent('hunger', physicalState.hunger, contextFactors);
        }
        
        if (this.config.showStress && physicalState.stress !== undefined) {
            analysis.states.stress = this.analyzeStateComponent('stress', physicalState.stress, contextFactors);
        }
        
        if (this.config.showMood && physicalState.mood !== undefined) {
            analysis.states.mood = this.analyzeStateComponent('mood', physicalState.mood, contextFactors);
        }

        // 计算综合指标
        analysis.summary = this.calculateSummaryMetrics(analysis.states);
        
        // 分析对话影响
        analysis.conversationalImpact = this.analyzeConversationalImpact(analysis.states, analysis.summary);

        return analysis;
    }

    /**
     * 分析单个状态组件
     */
    analyzeStateComponent(stateName, value, contextFactors) {
        const theory = this.stateTheories[stateName];
        const level = this.getStateLevel(value, stateName);
        const trend = this.calculateTrend(stateName, value, contextFactors);
        const impact = this.calculateStateImpact(stateName, value);
        
        return {
            value: parseFloat(value.toFixed(1)),
            level: level.name,
            levelDescription: level.description,
            theory: theory.theory,
            theoryDescription: theory.description,
            optimal: theory.optimal,
            isOptimal: value >= theory.optimal[0] && value <= theory.optimal[1],
            trend: trend,
            impact: impact,
            recommendations: this.getStateRecommendations(stateName, value, level.name)
        };
    }

    /**
     * 获取状态水平
     */
    getStateLevel(value, stateName) {
        // 对于疲劳度，数值越高越差
        const isReverse = ['fatigue', 'hunger', 'stress'].includes(stateName);
        const adjustedValue = isReverse ? 100 - value : value;
        
        for (const [levelName, range] of Object.entries(this.stateRanges)) {
            if (adjustedValue >= range[0] && adjustedValue <= range[1]) {
                return {
                    name: levelName,
                    description: this.getLevelDescription(levelName, stateName, isReverse)
                };
            }
        }
        
        return { name: 'unknown', description: '状态未知' };
    }

    /**
     * 获取水平描述
     */
    getLevelDescription(level, stateName, isReverse) {
        const descriptions = {
            excellent: isReverse ? '极低水平，状态优秀' : '极高水平，状态优秀',
            good: isReverse ? '低水平，状态良好' : '高水平，状态良好',
            moderate: isReverse ? '中等水平，状态一般' : '中等水平，状态一般',
            poor: isReverse ? '较高水平，状态较差' : '较低水平，状态较差',
            critical: isReverse ? '极高水平，状态危险' : '极低水平，状态危险'
        };
        
        return descriptions[level] || '状态未知';
    }

    /**
     * 计算趋势
     */
    calculateTrend(stateName, value, contextFactors) {
        // 基于上下文因素预测趋势
        const timeFactors = contextFactors.timeSinceLastMeal || 3;
        const sleepFactors = contextFactors.timeSinceLastSleep || 8;
        
        let trend = 'stable';
        let confidence = 0.5;
        
        if (stateName === 'hunger' && timeFactors > 4) {
            trend = 'increasing';
            confidence = 0.8;
        } else if (stateName === 'fatigue' && sleepFactors > 12) {
            trend = 'increasing';
            confidence = 0.9;
        } else if (stateName === 'energy' && sleepFactors < 2) {
            trend = 'increasing';
            confidence = 0.7;
        }
        
        return { direction: trend, confidence: confidence.toFixed(2) };
    }

    /**
     * 计算状态影响
     */
    calculateStateImpact(stateName, value) {
        const impacts = {
            focus: value > 80 ? 'enhanced_concentration' : value < 40 ? 'attention_deficit' : 'normal_focus',
            energy: value > 70 ? 'high_vitality' : value < 30 ? 'energy_depletion' : 'moderate_energy',
            fatigue: value > 80 ? 'severe_exhaustion' : value < 30 ? 'well_rested' : 'mild_fatigue',
            alertness: value > 75 ? 'hyper_vigilant' : value < 40 ? 'reduced_awareness' : 'normal_alertness',
            hunger: value > 70 ? 'strong_appetite' : value < 30 ? 'satiated' : 'mild_hunger',
            stress: value > 70 ? 'high_tension' : value < 30 ? 'relaxed_state' : 'moderate_stress',
            mood: value > 75 ? 'positive_affect' : value < 40 ? 'negative_affect' : 'neutral_mood'
        };
        
        return impacts[stateName] || 'unknown_impact';
    }

    /**
     * 获取状态建议
     */
    getStateRecommendations(stateName, value, level) {
        const recommendations = {
            focus: {
                critical: ['立即休息', '减少认知负荷', '避免复杂任务'],
                poor: ['短暂休息', '简化任务', '减少干扰'],
                moderate: ['保持当前状态', '适度挑战'],
                good: ['可进行复杂任务', '保持专注'],
                excellent: ['最佳表现窗口', '处理重要任务']
            },
            energy: {
                critical: ['立即休息', '补充营养', '避免消耗'],
                poor: ['适度休息', '轻度活动'],
                moderate: ['平衡活动与休息'],
                good: ['可进行中等强度活动'],
                excellent: ['最佳活动时机']
            },
            fatigue: {
                excellent: ['保持良好状态'],
                good: ['继续当前节奏'],
                moderate: ['考虑适度休息'],
                poor: ['需要休息恢复'],
                critical: ['立即休息，避免过度消耗']
            }
        };
        
        return recommendations[stateName]?.[level] || ['保持当前状态'];
    }

    /**
     * 计算综合指标
     */
    calculateSummaryMetrics(states) {
        const values = Object.values(states).map(s => s.value);
        const positiveStates = ['focus', 'energy', 'alertness', 'mood'];
        const negativeStates = ['fatigue', 'hunger', 'stress'];
        
        let overallWellbeing = 0;
        let count = 0;
        
        Object.entries(states).forEach(([name, state]) => {
            if (positiveStates.includes(name)) {
                overallWellbeing += state.value;
            } else if (negativeStates.includes(name)) {
                overallWellbeing += (100 - state.value);
            }
            count++;
        });
        
        overallWellbeing = count > 0 ? overallWellbeing / count : 50;
        
        return {
            overallWellbeing: overallWellbeing.toFixed(1),
            activeStatesCount: Object.keys(states).length,
            optimalStatesCount: Object.values(states).filter(s => s.isOptimal).length,
            criticalStatesCount: Object.values(states).filter(s => s.level === 'critical').length
        };
    }

    /**
     * 分析对话影响
     */
    analyzeConversationalImpact(states, summary) {
        const focusImpact = states.focus ? states.focus.value / 100 : 0.5;
        const energyImpact = states.energy ? states.energy.value / 100 : 0.5;
        const fatigueImpact = states.fatigue ? (100 - states.fatigue.value) / 100 : 0.5;
        const alertnessImpact = states.alertness ? states.alertness.value / 100 : 0.5;
        
        const conversationCapacity = (focusImpact + energyImpact + fatigueImpact + alertnessImpact) / 4;
        
        let strategy = 'standard_conversation';
        if (conversationCapacity > 0.8) strategy = 'deep_technical_discussion';
        else if (conversationCapacity > 0.6) strategy = 'moderate_engagement';
        else if (conversationCapacity > 0.4) strategy = 'light_conversation';
        else strategy = 'minimal_interaction';
        
        return {
            capacity: (conversationCapacity * 100).toFixed(1),
            strategy: strategy,
            recommendations: this.generateConversationRecommendations(states, conversationCapacity)
        };
    }

    /**
     * 生成对话建议
     */
    generateConversationRecommendations(states, capacity) {
        const recommendations = [];
        
        if (capacity > 0.8) {
            recommendations.push('可进行深度技术讨论');
            recommendations.push('适合处理复杂问题');
        } else if (capacity > 0.6) {
            recommendations.push('适合中等深度交流');
            recommendations.push('可讨论一般性话题');
        } else if (capacity > 0.4) {
            recommendations.push('建议轻松对话');
            recommendations.push('避免复杂话题');
        } else {
            recommendations.push('建议简短交流');
            recommendations.push('重点关注核心信息');
        }
        
        // 基于具体状态添加建议
        if (states.fatigue && states.fatigue.value > 80) {
            recommendations.push('考虑疲劳状态，简化表达');
        }
        if (states.focus && states.focus.value > 80) {
            recommendations.push('专注度高，可深入分析');
        }
        if (states.energy && states.energy.value < 20) {
            recommendations.push('精力不足，保存体力');
        }
        
        return recommendations;
    }

    /**
     * 格式化输出（参考情感记忆格式）
     */
    formatAnalysisOutput(analysis) {
        let output = '';
        
        // 物理状态指标
        output += '[你的物理状态指标]\n';
        Object.entries(analysis.states).forEach(([name, state]) => {
            const displayName = this.getDisplayName(name);
            output += `${displayName}: ${state.value}/100\n`;
        });
        output += '---\n\n';
        
        if (this.config.showAlgorithmDetails) {
            // 深度物理状态分析
            output += '[深度物理状态分析]\n';
            Object.entries(analysis.states).forEach(([name, state]) => {
                const displayName = this.getDisplayName(name);
                output += `• ${displayName}: ${state.levelDescription}(${state.value}) [${state.theory}] [趋势:${state.trend.direction}]\n`;
                if (this.config.includeTheoryReferences) {
                    output += `  ├ 理论基础: ${state.theoryDescription}\n`;
                    output += `  ├ 最优区间: ${state.optimal[0]}-${state.optimal[1]} (当前${state.isOptimal ? '达标' : '未达标'})\n`;
                    output += `  ├ 状态影响: ${state.impact}\n`;
                    output += `  └ 调节建议: ${state.recommendations.join('、')}\n`;
                }
            });
            
            // 综合评估
            output += `• 综合健康度: ${analysis.summary.overallWellbeing}/100 [活跃状态:${analysis.summary.activeStatesCount}项] [最优状态:${analysis.summary.optimalStatesCount}项]\n`;
            if (analysis.summary.criticalStatesCount > 0) {
                output += `  └ 警告: ${analysis.summary.criticalStatesCount}项状态处于危险水平\n`;
            }
            output += '---\n\n';
        }
        
        // 对话影响分析
        output += '[当前状态对对话的影响分析]\n';
        output += `• 对话能力: ${analysis.conversationalImpact.capacity}% - 推荐策略: ${analysis.conversationalImpact.strategy}\n`;
        output += `• 状态建议:\n`;
        analysis.conversationalImpact.recommendations.forEach(rec => {
            output += `  - ${rec}\n`;
        });
        output += '---\n';
        
        return output;
    }

    /**
     * 获取显示名称
     */
    getDisplayName(stateName) {
        const names = {
            focus: '专注程度',
            energy: '精力水平',
            fatigue: '疲劳度',
            alertness: '警觉性',
            hunger: '饥饿感',
            stress: '压力水平',
            mood: '心情'
        };
        return names[stateName] || stateName;
    }
}

module.exports = PhysicalStateAnalyzer;
