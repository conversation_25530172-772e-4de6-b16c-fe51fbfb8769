/**
 * 世界树VCP插件测试脚本
 * 用于验证插件功能的正确性
 */

const WorldTreeVCP = require('./WorldTreeVCP.js');
const path = require('path');

// 模拟日志记录器
const mockLogger = {
    info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
    error: (tag, ...args) => console.error(`[ERROR] [${tag}]`, ...args),
    warning: (tag, ...args) => console.warn(`[WARNING] [${tag}]`, ...args),
    debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args),
    success: (tag, ...args) => console.log(`[SUCCESS] [${tag}]`, ...args),
    system: (...args) => console.log(`[SYSTEM]`, ...args)
};

async function testWorldTreeVCP() {
    console.log('🌳 开始测试世界树VCP插件...\n');
    
    try {
        // 1. 创建插件实例
        console.log('1. 创建插件实例...');
        const worldTreeVCP = new WorldTreeVCP();
        
        // 2. 初始化插件
        console.log('2. 初始化插件...');
        const initResult = await worldTreeVCP.initialize(mockLogger);
        
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        
        console.log('✅ 插件初始化成功\n');
        
        // 3. 测试插件状态
        console.log('3. 测试插件状态...');
        const status = worldTreeVCP.getStatus();
        console.log('插件状态:', JSON.stringify(status, null, 2));
        console.log('✅ 状态获取成功\n');
        
        // 4. 测试Agent列表获取
        console.log('4. 测试Agent列表获取...');
        const agents = await worldTreeVCP.getAgentList();
        console.log('Agent列表:', agents);
        console.log('✅ Agent列表获取成功\n');
        
        // 5. 测试世界树配置创建
        console.log('5. 测试世界树配置创建...');
        const testConfig = {
            worldBackground: '这是一个测试的魔法世界，充满了神秘和冒险。',
            timeArchitecture: {
                morning: '清晨的魔法能量最为纯净',
                afternoon: '下午是学习魔法的最佳时间',
                evening: '傍晚时分，魔法生物开始活跃',
                night: '夜晚是进行高级魔法研究的时候'
            },
            characterSchedules: {
                '09:00-12:00': '整理魔法典籍',
                '14:00-17:00': '教授魔法理论',
                '19:00-22:00': '研究古代魔法'
            },
            narrativeRules: {
                '知识渊博': '对各种魔法知识都有深入了解',
                '谨慎细致': '处理魔法时格外小心',
                '乐于助人': '愿意帮助真诚的学习者'
            }
        };
        
        const configResult = await worldTreeVCP.createOrUpdateWorldTreeConfig('TestAgent', testConfig);
        if (configResult) {
            console.log('✅ 世界树配置创建成功\n');
        } else {
            throw new Error('世界树配置创建失败');
        }
        
        // 6. 测试配置获取
        console.log('6. 测试配置获取...');
        const retrievedConfig = await worldTreeVCP.getWorldTreeConfig('TestAgent');
        if (retrievedConfig) {
            console.log('获取的配置:', JSON.stringify(retrievedConfig, null, 2));
            console.log('✅ 配置获取成功\n');
        } else {
            throw new Error('配置获取失败');
        }
        
        // 7. 测试心理状态计算
        console.log('7. 测试心理状态计算...');
        const psychologyState = await worldTreeVCP.calculatePsychologyState('testUser', 'TestAgent', {
            taskType: 'creative',
            taskLoad: 0.3,
            restQuality: 0.8
        });
        console.log('心理状态:', JSON.stringify(psychologyState, null, 2));
        console.log('✅ 心理状态计算成功\n');
        
        // 8. 测试心理活动生成
        console.log('8. 测试心理活动生成...');
        const psychologyActivity = await worldTreeVCP.generatePsychologyActivity('testUser', 'TestAgent', {
            hasRecentConversation: true,
            conversationLength: 100
        });
        if (psychologyActivity) {
            console.log('心理活动:', JSON.stringify(psychologyActivity, null, 2));
            console.log('✅ 心理活动生成成功\n');
        } else {
            throw new Error('心理活动生成失败');
        }
        
        // 9. 测试系统消息生成
        console.log('9. 测试系统消息生成...');
        const systemMessage = await worldTreeVCP.generateSystemMessage('testUser', 'TestAgent', '你好，我想了解一些魔法知识。');
        console.log('系统消息长度:', systemMessage.length);
        console.log('系统消息内容预览:', systemMessage.substring(0, 200) + '...');
        console.log('✅ 系统消息生成成功\n');
        
        // 10. 测试时间相关功能
        console.log('10. 测试时间相关功能...');
        const currentTime = worldTreeVCP.getCurrentLocalTime();
        const timePeriod = worldTreeVCP.getTimePeriod();
        console.log('当前本地时间:', currentTime);
        console.log('当前时间段:', timePeriod);
        console.log('✅ 时间功能测试成功\n');
        
        // 11. 清理测试
        console.log('11. 清理测试...');
        await worldTreeVCP.cleanup();
        console.log('✅ 插件清理成功\n');
        
        console.log('🎉 所有测试通过！世界树VCP插件功能正常。');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testWorldTreeVCP().then(() => {
        console.log('\n测试完成，退出程序。');
        process.exit(0);
    }).catch(error => {
        console.error('\n测试过程中发生未捕获的错误:', error);
        process.exit(1);
    });
}

module.exports = { testWorldTreeVCP };
