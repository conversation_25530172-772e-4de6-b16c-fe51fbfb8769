/**
 * 事件驱动系统
 * 管理随机事件、定期事件、用户触发事件等
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class EventSystem extends EventEmitter {
    constructor(worldCore) {
        super();
        this.worldCore = worldCore;
        this.logger = worldCore.logger;
        
        // 事件类型定义
        this.eventTypes = {
            random: {
                frequency: 0.1, // 每次检查10%概率
                events: [
                    'unexpected_meeting',
                    'lost_item',
                    'found_money',
                    'sudden_rain',
                    'power_outage',
                    'festival_announcement',
                    'new_shop_opening',
                    'celebrity_sighting'
                ]
            },
            scheduled: {
                events: [
                    'daily_sunrise',
                    'daily_sunset',
                    'weekly_market',
                    'monthly_festival',
                    'seasonal_change',
                    'birthday_celebration'
                ]
            },
            triggered: {
                events: [
                    'agent_birthday',
                    'relationship_milestone',
                    'skill_achievement',
                    'location_overcrowding',
                    'resource_shortage'
                ]
            }
        };

        // 事件模板
        this.eventTemplates = new Map();
        this.initializeEventTemplates();
        
        // 活跃事件
        this.activeEvents = new Map();
        
        // 事件历史
        this.eventHistory = [];
        
        // 定时器
        this.updateInterval = null;
        this.updateFrequency = 120000; // 2分钟检查一次事件
    }

    /**
     * 初始化事件系统
     */
    async init() {
        this.logger.info('🎭 初始化事件系统...');
        
        // 加载事件历史
        await this.loadEventHistory();
        
        this.logger.info('✅ 事件系统初始化完成');
    }

    /**
     * 初始化事件模板
     */
    initializeEventTemplates() {
        // 随机事件模板
        this.eventTemplates.set('unexpected_meeting', {
            name: '意外相遇',
            description: '两个很久没见的朋友意外相遇',
            duration: 300000, // 5分钟
            effects: {
                participants: { happiness: 15, social: 10 },
                relationship: { strength: 8 }
            },
            requirements: {
                minAgents: 2,
                relationshipHistory: true
            }
        });

        this.eventTemplates.set('lost_item', {
            name: '丢失物品',
            description: '某个Agent丢失了重要物品',
            duration: 600000, // 10分钟
            effects: {
                target: { happiness: -10, stress: 15 }
            },
            requirements: {
                minAgents: 1
            }
        });

        this.eventTemplates.set('found_money', {
            name: '意外之财',
            description: '某个Agent在路上发现了钱',
            duration: 60000, // 1分钟
            effects: {
                target: { happiness: 20, achievement: 5 }
            },
            requirements: {
                minAgents: 1
            }
        });

        this.eventTemplates.set('sudden_rain', {
            name: '突然下雨',
            description: '天空突然下起了雨',
            duration: 1800000, // 30分钟
            effects: {
                global: { mood: -5 },
                environment: { weather: 'rainy' }
            },
            requirements: {}
        });

        this.eventTemplates.set('festival_announcement', {
            name: '节日公告',
            description: '宣布即将举办的节日活动',
            duration: 86400000, // 24小时
            effects: {
                global: { happiness: 10, anticipation: 15 }
            },
            requirements: {}
        });

        this.eventTemplates.set('new_shop_opening', {
            name: '新店开业',
            description: '附近开了一家新店',
            duration: 604800000, // 7天
            effects: {
                location: { attractiveness: 10 },
                global: { curiosity: 5 }
            },
            requirements: {}
        });

        // 定期事件模板
        this.eventTemplates.set('daily_sunrise', {
            name: '日出',
            description: '新的一天开始了',
            duration: 3600000, // 1小时
            effects: {
                global: { energy: 5, optimism: 3 }
            },
            schedule: { type: 'daily', time: '06:00' }
        });

        this.eventTemplates.set('daily_sunset', {
            name: '日落',
            description: '一天即将结束',
            duration: 3600000, // 1小时
            effects: {
                global: { relaxation: 5, reflection: 3 }
            },
            schedule: { type: 'daily', time: '18:00' }
        });

        this.eventTemplates.set('weekly_market', {
            name: '周末市集',
            description: '热闹的周末市集开始了',
            duration: 14400000, // 4小时
            effects: {
                location: { activity: 20, social: 15 },
                participants: { happiness: 8, social: 12 }
            },
            schedule: { type: 'weekly', day: 'saturday', time: '10:00' }
        });
    }

    /**
     * 加载事件历史
     */
    async loadEventHistory() {
        try {
            const historyPath = path.join(this.worldCore.dataPath.events, 'history.json');
            const data = await fs.readFile(historyPath, 'utf-8');
            this.eventHistory = JSON.parse(data);
            
            this.logger.info(`📖 加载了 ${this.eventHistory.length} 条事件历史`);
            
        } catch (error) {
            this.logger.info('🆕 创建新的事件历史');
            this.eventHistory = [];
        }
    }

    /**
     * 保存事件历史
     */
    async saveEventHistory() {
        try {
            const historyPath = path.join(this.worldCore.dataPath.events, 'history.json');
            await fs.writeFile(historyPath, JSON.stringify(this.eventHistory, null, 2));
            
        } catch (error) {
            this.logger.error('❌ 保存事件历史失败:', error);
        }
    }

    /**
     * 启动事件系统
     */
    async start() {
        if (this.updateInterval) {
            return;
        }

        this.updateInterval = setInterval(() => {
            this.processEvents();
        }, this.updateFrequency);

        this.logger.info('🎭 事件系统已启动');
    }

    /**
     * 暂停事件系统
     */
    async pause() {
        this.logger.info('⏸️ 事件系统已暂停');
    }

    /**
     * 恢复事件系统
     */
    async resume() {
        this.logger.info('▶️ 事件系统已恢复');
    }

    /**
     * 停止事件系统
     */
    async stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        await this.saveEventHistory();
        this.logger.info('🛑 事件系统已停止');
    }

    /**
     * 处理事件
     */
    processEvents() {
        if (this.worldCore.worldState.isPaused) {
            return;
        }

        // 检查随机事件
        this.checkRandomEvents();
        
        // 检查定期事件
        this.checkScheduledEvents();
        
        // 检查触发事件
        this.checkTriggeredEvents();
        
        // 更新活跃事件
        this.updateActiveEvents();
    }

    /**
     * 检查随机事件
     */
    checkRandomEvents() {
        const randomEvents = this.eventTypes.random.events;
        const frequency = this.eventTypes.random.frequency;
        
        if (Math.random() < frequency) {
            const eventType = randomEvents[Math.floor(Math.random() * randomEvents.length)];
            this.attemptTriggerEvent(eventType);
        }
    }

    /**
     * 检查定期事件
     */
    checkScheduledEvents() {
        const currentTime = this.worldCore.getCurrentWorldTime();
        const currentHour = currentTime.getHours();
        const currentDay = currentTime.getDay(); // 0 = Sunday
        
        for (const [eventType, template] of this.eventTemplates.entries()) {
            if (!template.schedule) continue;
            
            const schedule = template.schedule;
            let shouldTrigger = false;
            
            switch (schedule.type) {
                case 'daily':
                    const scheduleHour = parseInt(schedule.time.split(':')[0]);
                    shouldTrigger = currentHour === scheduleHour && !this.hasEventTriggeredToday(eventType);
                    break;
                    
                case 'weekly':
                    const scheduleDayMap = {
                        'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
                        'thursday': 4, 'friday': 5, 'saturday': 6
                    };
                    const scheduleDay = scheduleDayMap[schedule.day];
                    const scheduleWeekHour = parseInt(schedule.time.split(':')[0]);
                    shouldTrigger = currentDay === scheduleDay && 
                                  currentHour === scheduleWeekHour && 
                                  !this.hasEventTriggeredThisWeek(eventType);
                    break;
            }
            
            if (shouldTrigger) {
                this.triggerEvent(eventType);
            }
        }
    }

    /**
     * 检查触发事件
     */
    checkTriggeredEvents() {
        // 检查Agent生日
        this.checkBirthdayEvents();
        
        // 检查关系里程碑
        this.checkRelationshipMilestones();
        
        // 检查地点过度拥挤
        this.checkLocationOvercrowding();
    }

    /**
     * 检查生日事件
     */
    checkBirthdayEvents() {
        const agents = this.worldCore.agentEcosystem.getAllAgents();
        const today = this.worldCore.getCurrentWorldTime();
        
        for (const agent of agents) {
            // 简化的生日检查（假设每个Agent都有生日）
            if (Math.random() < 0.001) { // 0.1%概率模拟生日
                this.triggerEvent('agent_birthday', { agentId: agent.id });
            }
        }
    }

    /**
     * 检查关系里程碑
     */
    checkRelationshipMilestones() {
        // 监听关系更新事件
        // 这里可以检查关系强度达到特定阈值的情况
    }

    /**
     * 检查地点过度拥挤
     */
    checkLocationOvercrowding() {
        const locations = this.worldCore.worldEnvironment.getAllLocations();
        
        for (const location of locations) {
            if (location.currentOccupants.length > location.capacity * 0.9) {
                this.triggerEvent('location_overcrowding', { 
                    locationId: location.id,
                    occupancy: location.currentOccupants.length,
                    capacity: location.capacity
                });
            }
        }
    }

    /**
     * 尝试触发事件
     */
    attemptTriggerEvent(eventType, context = {}) {
        const template = this.eventTemplates.get(eventType);
        if (!template) {
            this.logger.warn(`⚠️ 未知事件类型: ${eventType}`);
            return false;
        }

        // 检查事件要求
        if (!this.checkEventRequirements(template, context)) {
            return false;
        }

        return this.triggerEvent(eventType, context);
    }

    /**
     * 检查事件要求
     */
    checkEventRequirements(template, context) {
        const requirements = template.requirements || {};
        
        // 检查最小Agent数量
        if (requirements.minAgents) {
            const activeAgents = this.worldCore.agentEcosystem.getActiveAgents();
            if (activeAgents.length < requirements.minAgents) {
                return false;
            }
        }

        // 检查关系历史要求
        if (requirements.relationshipHistory) {
            const socialNetwork = this.worldCore.socialNetwork;
            const hasRelationships = socialNetwork.relationships.size > 0;
            if (!hasRelationships) {
                return false;
            }
        }

        return true;
    }

    /**
     * 触发事件
     */
    triggerEvent(eventType, context = {}) {
        const template = this.eventTemplates.get(eventType);
        if (!template) {
            this.logger.warn(`⚠️ 未知事件类型: ${eventType}`);
            return false;
        }

        const eventId = `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const event = {
            id: eventId,
            type: eventType,
            name: template.name,
            description: template.description,
            context,
            startTime: new Date(),
            duration: template.duration,
            endTime: new Date(Date.now() + template.duration),
            effects: template.effects,
            participants: this.selectEventParticipants(template, context),
            status: 'active'
        };

        // 应用事件效果
        this.applyEventEffects(event);
        
        // 保存到活跃事件
        this.activeEvents.set(eventId, event);
        
        // 记录到历史
        this.eventHistory.push({
            ...event,
            triggeredAt: new Date()
        });

        this.emit('eventTriggered', event);
        this.logger.info(`🎭 触发事件: ${event.name} (${eventType})`);
        
        return true;
    }

    /**
     * 选择事件参与者
     */
    selectEventParticipants(template, context) {
        const participants = [];
        const requirements = template.requirements || {};
        
        if (context.agentId) {
            participants.push(context.agentId);
        } else if (requirements.minAgents) {
            const activeAgents = this.worldCore.agentEcosystem.getActiveAgents();
            const count = Math.min(requirements.minAgents, activeAgents.length);
            
            for (let i = 0; i < count; i++) {
                const randomAgent = activeAgents[Math.floor(Math.random() * activeAgents.length)];
                if (!participants.includes(randomAgent.id)) {
                    participants.push(randomAgent.id);
                }
            }
        }
        
        return participants;
    }

    /**
     * 应用事件效果
     */
    applyEventEffects(event) {
        const effects = event.effects;
        
        // 应用参与者效果
        if (effects.participants && event.participants.length > 0) {
            for (const agentId of event.participants) {
                const agent = this.worldCore.agentEcosystem.getAgent(agentId);
                if (agent) {
                    this.applyAgentEffects(agent, effects.participants);
                }
            }
        }
        
        // 应用目标效果
        if (effects.target && event.context.agentId) {
            const agent = this.worldCore.agentEcosystem.getAgent(event.context.agentId);
            if (agent) {
                this.applyAgentEffects(agent, effects.target);
            }
        }
        
        // 应用全局效果
        if (effects.global) {
            this.applyGlobalEffects(effects.global);
        }
        
        // 应用环境效果
        if (effects.environment) {
            this.applyEnvironmentEffects(effects.environment);
        }
        
        // 应用关系效果
        if (effects.relationship && event.participants.length >= 2) {
            this.applyRelationshipEffects(event.participants, effects.relationship);
        }
    }

    /**
     * 应用Agent效果
     */
    applyAgentEffects(agent, effects) {
        for (const [effect, value] of Object.entries(effects)) {
            switch (effect) {
                case 'happiness':
                case 'stress':
                case 'energy':
                case 'social':
                case 'achievement':
                    if (agent.needs[effect] !== undefined) {
                        agent.needs[effect] = Math.max(0, Math.min(100, agent.needs[effect] + value));
                    }
                    break;
                    
                default:
                    // 其他自定义效果
                    if (!agent.eventEffects) agent.eventEffects = {};
                    agent.eventEffects[effect] = (agent.eventEffects[effect] || 0) + value;
                    break;
            }
        }
        
        // 重新计算心情
        agent.mood = this.worldCore.agentEcosystem.calculateMood(agent);
    }

    /**
     * 应用全局效果
     */
    applyGlobalEffects(effects) {
        const agents = this.worldCore.agentEcosystem.getAllAgents();
        
        for (const agent of agents) {
            this.applyAgentEffects(agent, effects);
        }
    }

    /**
     * 应用环境效果
     */
    applyEnvironmentEffects(effects) {
        if (effects.weather) {
            this.worldCore.worldEnvironment.weatherSystem.current = effects.weather;
        }
    }

    /**
     * 应用关系效果
     */
    applyRelationshipEffects(participants, effects) {
        for (let i = 0; i < participants.length; i++) {
            for (let j = i + 1; j < participants.length; j++) {
                this.worldCore.socialNetwork.updateRelationship(
                    participants[i],
                    participants[j],
                    'event_interaction',
                    effects.strength || 5
                );
            }
        }
    }

    /**
     * 更新活跃事件
     */
    updateActiveEvents() {
        const now = Date.now();
        const expiredEvents = [];
        
        for (const [eventId, event] of this.activeEvents.entries()) {
            if (now >= new Date(event.endTime).getTime()) {
                expiredEvents.push(eventId);
            }
        }
        
        // 移除过期事件
        for (const eventId of expiredEvents) {
            const event = this.activeEvents.get(eventId);
            event.status = 'completed';
            
            this.activeEvents.delete(eventId);
            this.emit('eventCompleted', event);
            this.logger.info(`🎭 事件完成: ${event.name}`);
        }
    }

    /**
     * 检查今天是否已触发事件
     */
    hasEventTriggeredToday(eventType) {
        const today = this.worldCore.getCurrentWorldTime();
        const todayStr = today.toDateString();
        
        return this.eventHistory.some(event => 
            event.type === eventType && 
            new Date(event.triggeredAt).toDateString() === todayStr
        );
    }

    /**
     * 检查本周是否已触发事件
     */
    hasEventTriggeredThisWeek(eventType) {
        const now = this.worldCore.getCurrentWorldTime();
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - now.getDay());
        weekStart.setHours(0, 0, 0, 0);
        
        return this.eventHistory.some(event => 
            event.type === eventType && 
            new Date(event.triggeredAt) >= weekStart
        );
    }

    /**
     * 获取活跃事件
     */
    getActiveEvents() {
        return Array.from(this.activeEvents.values());
    }

    /**
     * 获取事件历史
     */
    getEventHistory(limit = 50) {
        return this.eventHistory.slice(-limit);
    }

    /**
     * 销毁事件系统
     */
    async destroy() {
        await this.stop();
        this.removeAllListeners();
        this.logger.info('🗑️ 事件系统已销毁');
    }
}

module.exports = { EventSystem };
