# 聊天上下文参数使用指南

## 概述

新的聊天上下文参数系统支持私聊和群聊的不同历史记录处理方式：
- **私聊**：使用 role-based 消息格式，效果更好
- **群聊**：继续使用 system 消息格式，保持兼容性

## 新参数格式

### 基本结构
```json
{
  "chat_context": {
    "type": "private" | "group",
    "name": "聊天名称",
    "history_count": 10,
    "user_names": {
      "user_id": "显示名称",
      "assistant_name": "AI显示名称"
    }
  }
}
```

### 私聊示例
```json
{
  "model": "gemini-2.5-pro-free",
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "chat_context": {
    "type": "private",
    "name": "雨安和静的私聊",
    "history_count": 5,
    "user_names": {
      "2166683295": "静",
      "assistant": "雨安"
    }
  },
  "userId": "2166683295",
  "assistantName": "雨安"
}
```

### 群聊示例
```json
{
  "model": "gemini-2.5-pro-free",
  "messages": [
    {
      "role": "user",
      "content": "大家好"
    }
  ],
  "chat_context": {
    "type": "group",
    "name": "Sentra-Wechat 测试",
    "history_count": 10
  }
}
```

## 微信适配器集成

微信适配器会根据当前监听的对象自动构建聊天上下文参数：

### 私聊自动处理
```python
# 私聊模式：自动构建私聊上下文
chat_name = f"{assistantName}和{user_name}的私聊"
merged_config['chat_context'] = {
    'type': 'private',
    'name': chat_name,
    'history_count': merged_config.get('private_history_count', 5),
    'user_names': {
        user_name: user_name,
        'assistant': assistantName
    }
}
```

### 群聊自动处理
```python
# 群聊模式：自动构建群聊上下文
merged_config['chat_context'] = {
    'type': 'group',
    'name': user_name,  # 群名
    'history_count': merged_config.get('group_history_count', 10),
    'user_names': {
        sender_name: sender_name,
        'assistant': assistantName
    }
}
```

## 处理效果

### 私聊历史记录格式
私聊历史记录会被转换为 role-based 消息对象，添加时间和聊天类型前缀：

```json
[
  {
    "role": "system",
    "content": "你是雨安，一个友善的AI助手。"
  },
  {
    "role": "user",
    "content": "[雨安和静的私聊][07-13 18:30:00] 你好，我是静"
  },
  {
    "role": "assistant",
    "content": "你好静！很高兴认识你。有什么我可以帮助你的吗？"
  },
  {
    "role": "user",
    "content": "[雨安和静的私聊][07-13 18:31:00] 今天天气怎么样？"
  },
  {
    "role": "user",
    "content": "现在几点了？"
  }
]
```

### 群聊历史记录格式
群聊历史记录继续添加到 system 消息中：

```
你是雨安安，一个活泼的AI助手。

=== 群聊【Sentra-Wechat 测试】最近5条历史记录 ===
[07-13 02:12:50] 高中物理王老师说: 晚安晚安！
[07-13 02:13:04] 高中物理王老师说: 梦里见！
[07-13 02:16:33] 高中物理王老师说: 破案了
[07-13 02:16:53] 高中物理王老师说: 可以安心睡觉了
[07-13 02:17:07] 高中物理王老师发送了emotion消息: [动画表情]
```

## 时间格式

历史记录中的时间格式为：`MM-DD HH:mm:ss`
例如：`[07-13 18:30:00]`

## 数据来源

- **私聊历史**：从数据库 `conversation_history` 表获取
- **群聊历史**：从文件系统的 JSON 格式聊天记录获取

## MCP 模式支持

新的聊天上下文参数系统完全支持 MCP 模式，使用相同的处理逻辑。

## 注意事项

1. **私聊历史记录**：只有 user 消息会添加时间和聊天类型前缀
2. **assistant 消息**：保持原样，不添加前缀
3. **插入位置**：历史记录会插入到最后一个 user 消息之前
4. **群聊历史记录**：继续使用原有的 system 消息方式
5. **微信适配器**：根据消息类型自动构建相应的聊天上下文
6. **配置要求**：需要在配置中设置 `private_history_count` 和 `group_history_count`
7. **旧格式支持**：已删除旧的 `group` 参数兼容性，必须使用新的 `chat_context` 格式
