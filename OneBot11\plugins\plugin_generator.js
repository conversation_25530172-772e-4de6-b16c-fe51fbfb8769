/**
 * OneBot11 Plugin Generator
 * 插件生成器 - 用于快速创建新插件
 */

const fs = require('fs');
const path = require('path');

class PluginGenerator {
    constructor() {
        this.templatePath = path.join(__dirname, 'plugin_template.js');
        this.pluginsDir = __dirname;
    }

    /**
     * 生成新插件
     * @param {Object} options 插件配置选项
     * @param {string} options.name 插件名称
     * @param {string} options.className 插件类名
     * @param {number} options.priority 优先级
     * @param {string} options.permission 权限级别
     * @param {Array} options.keywords 关键词触发
     * @param {Array} options.commands 命令触发
     * @param {string} options.description 插件描述
     * @param {string} options.author 作者
     * @param {string} options.version 版本
     */
    async generatePlugin(options) {
        try {
            // 验证参数
            this.validateOptions(options);

            // 读取模板文件
            const template = await fs.promises.readFile(this.templatePath, 'utf8');

            // 替换模板内容
            const pluginContent = this.replaceTemplate(template, options);

            // 生成文件名
            const fileName = `${options.name.toLowerCase().replace(/\s+/g, '_')}_plugin.js`;
            const filePath = path.join(this.pluginsDir, fileName);

            // 检查文件是否已存在
            if (fs.existsSync(filePath)) {
                throw new Error(`插件文件已存在: ${fileName}`);
            }

            // 写入文件
            await fs.promises.writeFile(filePath, pluginContent, 'utf8');

            // 更新插件加载顺序
            await this.updatePluginOrder(fileName.replace('.js', ''));

            return {
                success: true,
                fileName: fileName,
                filePath: filePath,
                message: `插件 ${options.name} 创建成功`
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 验证选项参数
     */
    validateOptions(options) {
        if (!options.name || typeof options.name !== 'string') {
            throw new Error('插件名称不能为空');
        }

        if (!options.className || typeof options.className !== 'string') {
            throw new Error('插件类名不能为空');
        }

        if (!/^[A-Z][a-zA-Z0-9]*$/.test(options.className)) {
            throw new Error('插件类名必须以大写字母开头，只能包含字母和数字');
        }

        if (options.priority && (typeof options.priority !== 'number' || options.priority < 1)) {
            throw new Error('优先级必须是大于0的数字');
        }

        const validPermissions = ['all', 'admin', 'super_admin', 'master'];
        if (options.permission && !validPermissions.includes(options.permission)) {
            throw new Error(`权限级别必须是: ${validPermissions.join(', ')}`);
        }
    }

    /**
     * 替换模板内容
     */
    replaceTemplate(template, options) {
        let content = template;

        // 基本信息替换
        content = content.replace(/PluginTemplate/g, options.className);
        content = content.replace(/'PluginTemplate'/g, `'${options.name}'`);
        content = content.replace(/this\.priority = 100;/, `this.priority = ${options.priority || 100};`);
        content = content.replace(/this\.permission = 'all';/, `this.permission = '${options.permission || 'all'}';`);

        // 触发条件替换
        if (options.keywords && options.keywords.length > 0) {
            const keywordsStr = options.keywords.map(k => `'${k}'`).join(', ');
            content = content.replace(/keywords: \['模板'\]/, `keywords: [${keywordsStr}]`);
        }

        if (options.commands && options.commands.length > 0) {
            const commandsStr = options.commands.map(c => `'${c}'`).join(', ');
            content = content.replace(/commands: \['template'\]/, `commands: [${commandsStr}]`);
        }

        // 元信息替换
        if (options.version) {
            content = content.replace(/version: '1\.0\.0'/, `version: '${options.version}'`);
        }

        if (options.author) {
            content = content.replace(/author: 'VCPToolBox'/, `author: '${options.author}'`);
        }

        if (options.description) {
            content = content.replace(/description: '这是一个插件模板'/, `description: '${options.description}'`);
        }

        // 更新使用说明
        const firstKeyword = options.keywords && options.keywords.length > 0 ? options.keywords[0] : '模板';
        const firstCommand = options.commands && options.commands.length > 0 ? options.commands[0] : 'template';
        content = content.replace(/usage: '发送"模板"或"\/template"来测试插件'/, 
                                 `usage: '发送"${firstKeyword}"或"/${firstCommand}"来测试插件'`);
        content = content.replace(/example: '模板'/, `example: '${firstKeyword}'`);

        // 更新日志信息
        content = content.replace(/Plugin template initialized/, `${options.name} plugin initialized`);

        return content;
    }

    /**
     * 更新插件加载顺序
     */
    async updatePluginOrder(pluginName) {
        const orderFile = path.join(this.pluginsDir, 'plugin_order.txt');
        
        try {
            let orderContent = '';
            if (fs.existsSync(orderFile)) {
                orderContent = await fs.promises.readFile(orderFile, 'utf8');
            }

            // 如果插件不在列表中，添加到末尾
            if (!orderContent.includes(pluginName)) {
                orderContent += `\n# Generated plugin\n${pluginName}\n`;
                await fs.promises.writeFile(orderFile, orderContent, 'utf8');
            }
        } catch (error) {
            console.warn('更新插件加载顺序失败:', error.message);
        }
    }

    /**
     * 获取可用的权限级别
     */
    getAvailablePermissions() {
        return [
            { value: 'all', label: '所有用户', description: '任何用户都可以使用' },
            { value: 'admin', label: '管理员', description: '群管理员和机器人管理员可用' },
            { value: 'super_admin', label: '超级管理员', description: '超级管理员可用' },
            { value: 'master', label: '主人', description: '仅主人可用' }
        ];
    }

    /**
     * 获取插件模板示例
     */
    getTemplateExamples() {
        return [
            {
                name: '天气查询',
                className: 'WeatherPlugin',
                priority: 50,
                permission: 'all',
                keywords: ['天气', '气温'],
                commands: ['weather', 'tq'],
                description: '查询天气信息',
                author: 'VCPToolBox',
                version: '1.0.0'
            },
            {
                name: '群管理',
                className: 'GroupManagePlugin',
                priority: 10,
                permission: 'admin',
                keywords: ['踢人', '禁言'],
                commands: ['kick', 'mute'],
                description: '群组管理功能',
                author: 'VCPToolBox',
                version: '1.0.0'
            },
            {
                name: '系统管理',
                className: 'SystemManagePlugin',
                priority: 1,
                permission: 'master',
                keywords: ['重启', '关机'],
                commands: ['restart', 'shutdown'],
                description: '系统管理功能',
                author: 'VCPToolBox',
                version: '1.0.0'
            }
        ];
    }

    /**
     * 生成插件使用说明
     */
    generateUsageGuide() {
        return `
# OneBot11 插件生成器使用指南

## 快速生成插件

\`\`\`javascript
const PluginGenerator = require('./plugin_generator');
const generator = new PluginGenerator();

// 生成插件
const result = await generator.generatePlugin({
    name: '我的插件',
    className: 'MyPlugin',
    priority: 50,
    permission: 'all',
    keywords: ['测试', '帮助'],
    commands: ['test', 'help'],
    description: '这是我的第一个插件',
    author: '我的名字',
    version: '1.0.0'
});

console.log(result);
\`\`\`

## 权限级别说明

- **all**: 所有用户可用
- **admin**: 管理员可用 (群管理员、机器人管理员)
- **super_admin**: 超级管理员可用
- **master**: 主人可用

## 优先级说明

数值越小优先级越高，建议范围：
- 1-10: 系统级插件
- 11-50: 管理类插件
- 51-100: 功能类插件
- 101+: 娱乐类插件
        `;
    }
}

module.exports = PluginGenerator;
