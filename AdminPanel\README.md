# VCP 管理面板

## 📁 文件结构

```
AdminPanel/
├── index.html                  # 主管理面板首页
├── memory_system.html          # 智能记忆系统管理界面
├── image_cache_editor.html     # 图片缓存编辑器
├── script.js                   # 主页面脚本
├── style.css                   # 主页面样式
├── js/
│   └── memory_system.js        # 智能记忆系统管理脚本
├── css/                        # 样式文件目录
└── libs/                       # 第三方库目录
    └── threejs/                # Three.js 库
```

## 🚀 功能模块

### 智能记忆系统管理
- **访问地址**: `/AdminPanel/memory_system.html`
- **功能特性**:
  - 📊 数据库管理：表结构查看、数据编辑、导出功能
  - 🧠 概念管理：概念列表、激活强度可视化、概念分布图表
  - ❤️ 情感分析：AI情感状态、用户好感度、情感趋势图表
  - 📈 数据分析：系统使用统计、性能指标、趋势分析
  - ⚙️ 系统配置：参数设置、数据管理、系统日志

### 图片缓存编辑器
- **访问地址**: `/AdminPanel/image_cache_editor.html`
- **功能**: 管理和编辑图片缓存

## 🔧 技术栈

- **前端框架**: Tailwind CSS + Vanilla JavaScript
- **图表库**: Chart.js 4.4.0（轻量级版本）
- **HTTP客户端**: Axios 1.6.0
- **图标库**: Font Awesome 6.5.0
- **字体**: Google Fonts Inter

## 🌐 访问方式

1. **主管理面板**: http://localhost:6005/AdminPanel/
2. **智能记忆系统**: http://localhost:6005/AdminPanel/memory_system.html
3. **图片缓存编辑器**: http://localhost:6005/AdminPanel/image_cache_editor.html

## 🔒 认证信息

- **用户名**: admin
- **密码**: 123456

## 📝 更新日志

### v3.0 (最新版本)
- ✅ 清理重复文件，优化文件结构
- ✅ 统一命名规范，移除版本号后缀
- ✅ 现代化界面设计，玻璃拟态效果
- ✅ 防卡死图表渲染，性能优化
- ✅ 完整的数据编辑功能
- ✅ 真实数据库数据集成
- ✅ 响应式设计，移动端适配

## 🛠️ 开发说明

所有重复的历史版本文件已被清理，当前只保留最新优化版本：
- `memory_system.html` - 主界面文件
- `js/memory_system.js` - 核心逻辑文件

界面采用模块化设计，支持多标签页管理，具备完整的CRUD功能和数据可视化能力。
