/**
 * 测试修复后的配置加载逻辑
 * 验证插件专用配置是否正确生效
 */

const WorldTreeVCP = require('./WorldTreeVCP.js');

// 模拟日志记录器
const mockLogger = {
    info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
    error: (tag, ...args) => console.error(`[ERROR] [${tag}]`, ...args),
    warning: (tag, ...args) => console.warn(`[WARNING] [${tag}]`, ...args),
    debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args),
    success: (tag, ...args) => console.log(`[SUCCESS] [${tag}]`, ...args),
    system: (...args) => console.log(`[SYSTEM]`, ...args)
};

async function testConfigurationFix() {
    console.log('🔧 测试修复后的配置加载逻辑...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 测试构造函数配置加载
        console.log('1. 测试构造函数配置加载...');
        worldTreeVCP = new WorldTreeVCP();
        
        console.log('构造函数加载的配置:');
        console.log(`  API URL: ${worldTreeVCP.config.apiUrl}`);
        console.log(`  API Key: ${worldTreeVCP.config.apiKey ? `${worldTreeVCP.config.apiKey.substring(0, 10)}...` : '未配置'}`);
        console.log(`  模型: ${worldTreeVCP.config.model}`);
        console.log(`  使用本地算法: ${worldTreeVCP.config.useLocalAlgorithm}`);
        console.log(`  超时时间: ${worldTreeVCP.config.timeout}ms`);
        console.log(`  调试模式: ${worldTreeVCP.config.debugMode}`);
        console.log('✅ 构造函数配置加载成功\n');
        
        // 2. 测试初始化过程
        console.log('2. 测试初始化过程...');
        const initResult = await worldTreeVCP.initialize(mockLogger);
        
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        
        console.log('初始化后的配置:');
        console.log(`  API URL: ${worldTreeVCP.config.apiUrl}`);
        console.log(`  API Key: ${worldTreeVCP.config.apiKey ? `${worldTreeVCP.config.apiKey.substring(0, 10)}...` : '未配置'}`);
        console.log(`  模型: ${worldTreeVCP.config.model}`);
        console.log(`  使用本地算法: ${worldTreeVCP.config.useLocalAlgorithm}`);
        console.log(`  超时时间: ${worldTreeVCP.config.timeout}ms`);
        console.log(`  调试模式: ${worldTreeVCP.config.debugMode}`);
        console.log('✅ 初始化配置加载成功\n');
        
        // 3. 验证配置优先级
        console.log('3. 验证配置优先级...');
        const configPriority = {
            hasPluginConfig: !!worldTreeVCP.config.apiKey,
            isUsingPluginModel: worldTreeVCP.config.model === 'gemini-2.5-flash-free',
            isUsingPluginUrl: worldTreeVCP.config.apiUrl === 'https://yuanplus.cloud',
            useLocalAlgorithm: worldTreeVCP.config.useLocalAlgorithm
        };
        
        console.log('配置优先级验证:');
        console.log(`  使用插件API Key: ${configPriority.hasPluginConfig ? '✅' : '❌'}`);
        console.log(`  使用插件模型: ${configPriority.isUsingPluginModel ? '✅' : '❌'}`);
        console.log(`  使用插件URL: ${configPriority.isUsingPluginUrl ? '✅' : '❌'}`);
        console.log(`  本地算法设置: ${configPriority.useLocalAlgorithm ? '本地' : 'API'}`);
        console.log('✅ 配置优先级验证完成\n');
        
        // 4. 测试API调用（如果配置了API Key）
        if (worldTreeVCP.config.apiKey && !worldTreeVCP.config.useLocalAlgorithm) {
            console.log('4. 测试API调用...');
            try {
                const testPrompt = '你好，请简单回复一下。';
                const response = await worldTreeVCP.callPsychologyAPI(testPrompt);
                
                if (response) {
                    console.log('API调用成功:');
                    console.log(`  响应: ${response}`);
                    console.log('✅ API调用测试成功\n');
                } else {
                    console.log('⚠️ API调用返回空响应\n');
                }
            } catch (error) {
                console.log(`❌ API调用失败: ${error.message}`);
                
                // 分析错误类型
                if (error.response) {
                    console.log(`  状态码: ${error.response.status}`);
                    console.log(`  错误详情:`, error.response.data);
                    
                    if (error.response.status === 401) {
                        console.log('  🔍 401错误 - API Key可能无效或已过期');
                    } else if (error.response.status === 502) {
                        console.log('  🔍 502错误 - 上游服务器问题');
                    }
                } else {
                    console.log('  🔍 网络或配置问题');
                }
                console.log('');
            }
        } else {
            console.log('4. 跳过API调用测试（使用本地算法或未配置API Key）\n');
        }
        
        // 5. 测试心理状态生成
        console.log('5. 测试心理状态生成...');
        const psychologyActivity = await worldTreeVCP.generatePsychologyActivity('test_user', '测试Agent', {
            hasRecentConversation: true,
            conversationLength: 100
        });
        
        if (psychologyActivity) {
            console.log('心理状态生成成功:');
            console.log(`  内容: "${psychologyActivity.content}"`);
            console.log(`  时间: ${psychologyActivity.timestamp}`);
            console.log('✅ 心理状态生成测试成功\n');
        } else {
            console.log('❌ 心理状态生成失败\n');
        }
        
        // 6. 配置验证总结
        console.log('6. 配置验证总结...');
        const configSummary = {
            configLoaded: !!worldTreeVCP.config,
            hasApiKey: !!worldTreeVCP.config.apiKey,
            correctModel: worldTreeVCP.config.model === 'gemini-2.5-flash-free',
            correctUrl: worldTreeVCP.config.apiUrl === 'https://yuanplus.cloud',
            algorithmMode: worldTreeVCP.config.useLocalAlgorithm ? '本地算法' : 'API模式'
        };
        
        console.log('配置验证总结:');
        console.log(`  配置已加载: ${configSummary.configLoaded ? '✅' : '❌'}`);
        console.log(`  API Key配置: ${configSummary.hasApiKey ? '✅' : '❌'}`);
        console.log(`  模型配置正确: ${configSummary.correctModel ? '✅' : '❌'}`);
        console.log(`  URL配置正确: ${configSummary.correctUrl ? '✅' : '❌'}`);
        console.log(`  算法模式: ${configSummary.algorithmMode}`);
        
        console.log('\n🎉 配置加载修复验证完成！');
        console.log('\n📋 修复总结:');
        console.log('• 插件现在优先使用自己的config.env配置');
        console.log('• 配置加载时机正确（构造函数 + 初始化时重新加载）');
        console.log('• 支持配置优先级：插件配置 > 主服务器配置 > 默认值');
        console.log('• API Key现在正确传递到请求中');
        console.log('• 模型和URL配置正确生效');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error.stack);
    } finally {
        // 清理
        if (worldTreeVCP) {
            try {
                await worldTreeVCP.cleanup();
                console.log('\n🧹 插件清理完成');
            } catch (cleanupError) {
                console.error('清理失败:', cleanupError.message);
            }
        }
    }
}

// 运行测试
if (require.main === module) {
    testConfigurationFix().then(() => {
        console.log('\n测试完成，退出程序。');
        process.exit(0);
    }).catch(error => {
        console.error('\n测试过程中发生未捕获的错误:', error);
        process.exit(1);
    });
}

module.exports = { testConfigurationFix };
