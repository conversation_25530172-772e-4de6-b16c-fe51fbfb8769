# OneBot11 戳一戳功能全面优化说明

## 问题分析

原始错误信息：
```
[ERROR] 获取用户昵称失败: TypeError: this.napCatAPI.user.getInfo is not a function
```

### 根本原因
1. **API封装不完整**：`napcat_api.js`中缺少用户信息获取的API方法
2. **戳一戳事件处理逻辑不完善**：缺少专门的戳一戳事件处理器
3. **API调用方式错误**：使用了不存在的API方法

## 优化内容

### 1. NapCat API 封装优化

#### 新增用户相关API
```javascript
// 在 napcat_api.js 的 user 部分新增：
get user() {
    return {
        // 获取用户信息（陌生人信息）
        getInfo: async (bot, userId, noCache = false) => {
            return await this.callApi(bot, 'get_stranger_info', {
                user_id: Number(userId),
                no_cache: Boolean(noCache)
            });
        },

        // 获取群成员信息
        getGroupMemberInfo: async (bot, groupId, userId, noCache = false) => {
            return await this.callApi(bot, 'get_group_member_info', {
                group_id: Number(groupId),
                user_id: Number(userId),
                no_cache: Boolean(noCache)
            });
        },

        // 获取群成员列表
        getGroupMemberList: async (bot, groupId, noCache = false) => {
            return await this.callApi(bot, 'get_group_member_list', {
                group_id: Number(groupId),
                no_cache: Boolean(noCache)
            });
        },

        // 获取好友列表
        getFriendList: async (bot) => {
            return await this.callApi(bot, 'get_friend_list', {});
        }
    };
}
```

#### 新增群组相关API
```javascript
// 在 napcat_api.js 的 group 部分新增：
get group() {
    return {
        // 获取群信息
        getInfo: async (bot, groupId, noCache = false) => {
            return await this.callApi(bot, 'get_group_info', {
                group_id: Number(groupId),
                no_cache: Boolean(noCache)
            });
        },

        // 获取群列表
        getList: async (bot, noCache = false) => {
            return await this.callApi(bot, 'get_group_list', {
                no_cache: Boolean(noCache)
            });
        }
    };
}
```

#### 优化戳一戳API
```javascript
// 通用戳一戳（使用NapCat的send_poke API）
async poke(bot, userId, groupId = null) {
    const params = { user_id: Number(userId) };
    if (groupId) {
        params.group_id = Number(groupId);
    }
    return await this.callApi(bot, 'send_poke', params);
}
```

### 2. 消息处理器优化

#### 新增戳一戳事件处理器
在 `message_handler.js` 中新增：
```javascript
// 戳一戳事件
this.handlers.set('notice_notify', this.handleNotify.bind(this));

// 处理通知事件（戳一戳等）
async handleNotify(event) {
    const { sub_type, user_id, target_id, group_id, operator_id } = event;
    this.stats.notices++;
    
    if (sub_type === 'poke') {
        const location = group_id ? `群聊[${group_id}]` : '私聊';
        const operator = operator_id || user_id;
        const target = target_id;
        this.logger.info('MessageHandler', `戳一戳事件 ${location}: ${operator} 戳了戳 ${target}`);
    } else {
        this.logger.info('MessageHandler', `通知事件: ${sub_type}`);
    }
}
```

### 3. 聊天记录插件优化

#### 修复用户昵称获取
```javascript
async getUserNickname(userId, groupId = null) {
    try {
        const bot = { ws: this.adapter.ws, selfId: this.adapter.selfId };

        if (groupId) {
            // 群聊中获取群名片
            const memberInfo = await this.napCatAPI.user.getGroupMemberInfo(bot, groupId, userId);
            if (memberInfo.retcode === 0 && memberInfo.data) {
                return memberInfo.data.card || memberInfo.data.nickname || `用户${userId}`;
            }
        }

        // 获取用户信息
        const userInfo = await this.napCatAPI.user.getInfo(bot, userId);
        if (userInfo.retcode === 0 && userInfo.data) {
            return userInfo.data.nickname || `用户${userId}`;
        }

        return `用户${userId}`;
    } catch (error) {
        console.error(`[ERROR] 获取用户昵称失败:`, error);
        return `用户${userId}`;
    }
}
```

#### 优化戳一戳事件记录
```javascript
async saveNoticeRecord(context) {
    try {
        const event = context.event;
        console.log(`[DEBUG] 处理notice事件:`, JSON.stringify(event, null, 2));

        // 处理戳一戳事件 - 根据NapCat文档，戳一戳事件的结构
        if (event.notice_type === 'notify' && event.sub_type === 'poke') {
            const logEntry = await this.formatNoticeLog(context);
            if (logEntry) {
                await this.saveToFile(context, logEntry);
            }
        }
        // 处理其他notice事件类型
        else if (event.notice_type === 'group_recall') {
            // 群消息撤回
            console.log(`[DEBUG] 群消息撤回事件: 群${event.group_id}, 操作者${event.operator_id}, 消息ID${event.message_id}`);
        }
        else if (event.notice_type === 'friend_recall') {
            // 好友消息撤回
            console.log(`[DEBUG] 好友消息撤回事件: 用户${event.user_id}, 消息ID${event.message_id}`);
        }
    } catch (error) {
        console.error(`[ERROR] 保存notice记录失败:`, error);
    }
}
```

## 支持的戳一戳API

### 1. 群聊戳一戳
```javascript
// 使用群聊专用API
await napCatAPI.group.poke(bot, groupId, userId);

// 使用NapCat原生API
await napCatAPI.callApi(bot, 'group_poke', {
    group_id: Number(groupId),
    user_id: Number(userId)
});
```

### 2. 私聊戳一戳
```javascript
// 使用私聊专用API
await napCatAPI.friend.poke(bot, userId);

// 使用NapCat原生API
await napCatAPI.callApi(bot, 'friend_poke', {
    user_id: Number(userId)
});
```

### 3. 通用戳一戳
```javascript
// 自动判断群聊/私聊
await napCatAPI.poke(bot, userId, groupId); // 群聊
await napCatAPI.poke(bot, userId);           // 私聊

// 使用NapCat统一API
await napCatAPI.callApi(bot, 'send_poke', {
    user_id: Number(userId),
    group_id: Number(groupId) // 可选，不传则为私聊
});
```

## 戳一戳事件结构

### 群聊戳一戳事件
```json
{
    "post_type": "notice",
    "notice_type": "notify",
    "sub_type": "poke",
    "time": 1751789416,
    "self_id": 123456789,
    "group_id": 987654321,
    "user_id": 111222333,
    "operator_id": 111222333,
    "target_id": 123456789
}
```

### 私聊戳一戳事件
```json
{
    "post_type": "notice",
    "notice_type": "notify",
    "sub_type": "poke",
    "time": 1751789416,
    "self_id": 123456789,
    "user_id": 111222333,
    "operator_id": 111222333,
    "target_id": 123456789
}
```

## 测试验证

运行测试脚本验证功能：
```bash
node test_poke.js
```

测试内容包括：
1. 戳一戳API调用测试
2. 戳一戳事件结构验证
3. 用户信息获取API测试

## 参考文档

- [NapCat API 文档](https://napneko.github.io/develop/api/doc)
- [OneBot11 标准](https://github.com/botuniverse/onebot-11)
- [NapCat 戳一戳API](https://napneko.github.io/develop/api/doc#send-poke-群聊-私聊戳一戳)

## 注意事项

1. **API调用格式**：确保使用正确的参数类型（Number/String）
2. **事件处理**：戳一戳事件属于notice类型，sub_type为poke
3. **错误处理**：所有API调用都包含完整的错误处理机制
4. **兼容性**：支持NapCat的所有戳一戳相关API
