/**
 * OneBot11消息处理器
 * 处理不同类型的OneBot11消息和事件
 * 类似于Wechat目录中的消息处理逻辑
 */

const OneBotv11 = require('./OneBotv11');

class MessageHandler {
    constructor(logger, bot) {
        this.logger = logger;
        this.bot = bot;
        this.protocol = new OneBotv11(logger, bot.config);
        
        // 消息过滤器
        this.messageFilters = [];
        
        // 消息处理器映射
        this.handlers = new Map();
        this.setupHandlers();
        
        // 统计信息
        this.stats = {
            messagesProcessed: 0,
            privateMessages: 0,
            groupMessages: 0,
            notices: 0,
            requests: 0,
            metaEvents: 0,
            filteredMessages: 0
        };
        
        // 加载插件处理器
        this.pluginHandlers = [];
    }

    /**
     * 设置消息处理器
     */
    setupHandlers() {
        // 私聊消息处理器
        this.handlers.set('message_private', this.handlePrivateMessage.bind(this));
        
        // 群聊消息处理器
        this.handlers.set('message_group', this.handleGroupMessage.bind(this));
        
        // 群文件上传通知
        this.handlers.set('notice_group_upload', this.handleGroupUpload.bind(this));
        
        // 群管理员变动
        this.handlers.set('notice_group_admin', this.handleGroupAdmin.bind(this));
        
        // 群成员减少
        this.handlers.set('notice_group_decrease', this.handleGroupDecrease.bind(this));
        
        // 群成员增加
        this.handlers.set('notice_group_increase', this.handleGroupIncrease.bind(this));
        
        // 群禁言
        this.handlers.set('notice_group_ban', this.handleGroupBan.bind(this));
        
        // 好友添加
        this.handlers.set('notice_friend_add', this.handleFriendAdd.bind(this));
        
        // 群消息撤回
        this.handlers.set('notice_group_recall', this.handleGroupRecall.bind(this));
        
        // 好友消息撤回
        this.handlers.set('notice_friend_recall', this.handleFriendRecall.bind(this));

        // 戳一戳事件
        this.handlers.set('notice_notify', this.handleNotify.bind(this));

        // 好友请求
        this.handlers.set('request_friend', this.handleFriendRequest.bind(this));

        // 群请求/邀请
        this.handlers.set('request_group', this.handleGroupRequest.bind(this));
        
        // 生命周期元事件
        this.handlers.set('meta_event_lifecycle', this.handleLifecycle.bind(this));
        
        // 心跳元事件
        this.handlers.set('meta_event_heartbeat', this.handleHeartbeat.bind(this));
    }

    /**
     * 处理消息事件
     */
    async handleEvent(event) {
        try {
            // 检查是否是有效的事件数据
            if (!event || typeof event !== 'object') {
                this.logger.debug('MessageHandler', '无效的事件数据');
                return;
            }

            // 添加时间戳
            if (!event.timestamp) {
                event.timestamp = new Date().toISOString();
            }

            this.logger.debug('MessageHandler', `收到事件: ${event.post_type || 'unknown'}`);

            // 简化验证 - 只检查基本字段
            if (!event.post_type) {
                this.logger.debug('MessageHandler', '事件缺少post_type字段');
                return;
            }

            // 直接处理不同类型的事件
            if (event.post_type === 'message') {
                await this.handleMessage(event);
                await this.processWithPlugins(event);
                this.stats.messagesProcessed++;
            } else if (event.post_type === 'notice') {
                await this.handleNotice(event);
            } else if (event.post_type === 'request') {
                await this.handleRequest(event);
            } else if (event.post_type === 'meta_event') {
                await this.handleMetaEventSimple(event);
            } else {
                this.logger.debug('MessageHandler', `未知的事件类型: ${event.post_type}`);
            }

            // 发送VCP日志
            this.sendVCPLog(event);

        } catch (error) {
            this.logger.error('MessageHandler', `事件处理失败: ${error.message}`);
            this.logger.debug('MessageHandler', `错误详情: ${error.stack}`);
            this.logger.debug('MessageHandler', `事件数据: ${JSON.stringify(event, null, 2)}`);
        }
    }

    /**
     * 构建处理器键名
     */
    buildHandlerKey(event) {
        const { post_type } = event;
        
        switch (post_type) {
            case 'message':
                return `${post_type}_${event.message_type}`;
            case 'notice':
                return `${post_type}_${event.notice_type}`;
            case 'request':
                return `${post_type}_${event.request_type}`;
            case 'meta_event':
                return `${post_type}_${event.meta_event_type}`;
            default:
                return post_type;
        }
    }

    /**
     * 提取消息文本内容
     */
    extractMessageText(event) {
        // 优先使用 raw_message
        if (event.raw_message && typeof event.raw_message === 'string') {
            return event.raw_message;
        }

        // 如果 message 是字符串，直接返回
        if (typeof event.message === 'string') {
            return event.message;
        }

        // 如果 message 是数组，提取文本内容
        if (Array.isArray(event.message)) {
            const textSegments = event.message
                .filter(segment => segment.type === 'text')
                .map(segment => segment.data?.text || '')
                .join('')
                .trim();

            // 如果没有文本内容，返回消息类型描述
            if (!textSegments) {
                const nonTextTypes = event.message
                    .filter(segment => segment.type !== 'text')
                    .map(segment => `[${segment.type}]`)
                    .join('');
                return nonTextTypes || '[非文本消息]';
            }

            return textSegments;
        }

        // 如果 message 是对象，尝试提取文本
        if (typeof event.message === 'object' && event.message !== null) {
            if (event.message.text) {
                return event.message.text;
            }
            if (event.message.data && event.message.data.text) {
                return event.message.data.text;
            }
        }

        // 最后尝试转换为字符串，如果是空的则返回占位符
        const result = String(event.message || '').replace('[object Object]', '');
        return result || '[空消息]';
    }

    /**
     * 应用消息过滤器
     */
    async applyFilters(event) {
        // 检查配置中的过滤设置
        const config = this.bot.config;
        
        if (config.MESSAGE_FILTER && config.MESSAGE_FILTER.ENABLE_FILTER) {
            // 过滤自己的消息
            if (config.MESSAGE_FILTER.FILTER_SELF_MESSAGES && event.user_id === event.self_id) {
                return true;
            }
            
            // 过滤指定用户
            if (config.MESSAGE_FILTER.FILTER_USER_IDS && 
                config.MESSAGE_FILTER.FILTER_USER_IDS.includes(event.user_id)) {
                return true;
            }
            
            // 过滤指定群组
            if (event.group_id && config.MESSAGE_FILTER.FILTER_GROUP_IDS && 
                config.MESSAGE_FILTER.FILTER_GROUP_IDS.includes(event.group_id)) {
                return true;
            }
            
            // 过滤关键词
            if (event.message && config.MESSAGE_FILTER.FILTER_KEYWORDS) {
                for (const keyword of config.MESSAGE_FILTER.FILTER_KEYWORDS) {
                    if (event.message.includes(keyword)) {
                        return true;
                    }
                }
            }
        }
        
        // 应用自定义过滤器
        for (const filter of this.messageFilters) {
            try {
                if (await filter(event)) {
                    return true; // 被过滤
                }
            } catch (error) {
                this.logger.error('MessageHandler', '过滤器执行失败:', error.message);
            }
        }
        
        return false;
    }

    /**
     * 处理私聊消息
     */
    async handlePrivateMessage(event) {
        const { user_id, sender } = event;
        this.stats.privateMessages++;

        // 解析消息内容
        const messageText = this.extractMessageText(event);
        const segments = this.protocol.parseMessageSegments(event.message);

        this.logger.info('MessageHandler', `私聊消息 [${user_id}] ${sender.nickname}: ${messageText}`);

        // 构建处理上下文
        const context = {
            platform: 'onebot11',
            type: 'private',
            userId: user_id.toString(),
            message: messageText,
            segments: segments,
            sender: sender,
            timestamp: new Date(event.time * 1000),
            messageId: event.message_id,
            event: event
        };

        // 处理自动回复
        await this.handleAutoReply(context);
    }

    /**
     * 处理群聊消息
     */
    async handleGroupMessage(event) {
        const { group_id, user_id, sender } = event;
        this.stats.groupMessages++;

        // 解析消息内容
        const messageText = this.extractMessageText(event);
        const segments = this.protocol.parseMessageSegments(event.message);

        this.logger.info('MessageHandler', `群聊消息 [${group_id}][${user_id}] ${sender.nickname || sender.card}: ${messageText}`);

        // 构建处理上下文
        const context = {
            platform: 'onebot11',
            type: 'group',
            userId: user_id.toString(),
            groupId: group_id.toString(),
            message: messageText,
            segments: segments,
            sender: sender,
            timestamp: new Date(event.time * 1000),
            messageId: event.message_id,
            event: event
        };

        // 处理自动回复
        await this.handleAutoReply(context);
    }

    /**
     * 处理自动回复
     */
    async handleAutoReply(context) {
        const config = this.bot.config;

        // 检查关键词问答
        const keywordReply = await this.checkKeywordReply(context);
        if (keywordReply) {
            await this.sendReply(context, keywordReply);
            return;
        }

        // 检查命令处理
        const commandReply = await this.checkCommands(context);
        if (commandReply) {
            await this.sendReply(context, commandReply);
            return;
        }

        // 默认自动回复
        if (config.AUTO_REPLY && config.AUTO_REPLY.ENABLE_AUTO_REPLY) {
            // 延迟回复
            setTimeout(async () => {
                try {
                    const reply = config.AUTO_REPLY.DEFAULT_REPLY || '收到消息';
                    await this.sendReply(context, reply);
                } catch (error) {
                    this.logger.error('MessageHandler', `自动回复失败: ${error.message}`);
                }
            }, config.AUTO_REPLY.AUTO_REPLY_DELAY || 1000);
        }
    }

    /**
     * 检查关键词问答
     */
    async checkKeywordReply(context) {
        const config = this.bot.config;
        const message = context.message.toLowerCase();

        // 内置关键词问答
        const builtinKeywords = {
            '你好': ['你好！', '您好！', '嗨！'],
            'hello': ['Hello!', 'Hi there!', 'Hey!'],
            // '时间': [`现在时间是：${new Date().toLocaleString('zh-CN')}`], // 禁用时间回复，避免意外触发
            '帮助': ['可用命令：\n/help - 显示帮助\n/time - 显示时间\n/ping - 测试连接'],
            '功能': ['我是OneBot11机器人，可以：\n1. 关键词问答\n2. 群管理\n3. 消息转发\n4. 自动回复'],
            '什么的': ['什么的什么的～', '嗯嗯，什么的呢？', '什么什么？']
        };

        // 检查内置关键词
        for (const [keyword, replies] of Object.entries(builtinKeywords)) {
            if (message.includes(keyword)) {
                const randomReply = replies[Math.floor(Math.random() * replies.length)];
                this.logger.info('MessageHandler', `关键词匹配: ${keyword} -> ${randomReply}`);
                return randomReply;
            }
        }

        // 检查配置中的自定义关键词
        if (config.KEYWORD_REPLY && config.KEYWORD_REPLY.ENABLE_KEYWORD_REPLY) {
            const keywords = config.KEYWORD_REPLY.KEYWORDS || {};

            for (const [keyword, replies] of Object.entries(keywords)) {
                if (message.includes(keyword.toLowerCase())) {
                    const replyList = Array.isArray(replies) ? replies : [replies];
                    const randomReply = replyList[Math.floor(Math.random() * replyList.length)];
                    this.logger.info('MessageHandler', `自定义关键词匹配: ${keyword} -> ${randomReply}`);
                    return randomReply;
                }
            }
        }

        return null;
    }

    /**
     * 检查命令处理
     */
    async checkCommands(context) {
        const message = context.message.trim();

        if (!message.startsWith('/')) {
            return null;
        }

        const [command, ...args] = message.substring(1).split(' ');

        switch (command.toLowerCase()) {
            case 'help':
                return '🤖 OneBot11机器人帮助\n\n' +
                       '📝 基础命令：\n' +
                       '/help - 显示此帮助信息\n' +
                       '/time - 显示当前时间\n' +
                       '/ping - 测试机器人响应\n' +
                       '/status - 显示机器人状态\n\n' +
                       '🔧 管理命令：\n' +
                       '/info - 显示群/用户信息\n' +
                       '/echo <消息> - 复读消息';

            case 'time':
                return `🕐 当前时间：${new Date().toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                })}`;

            case 'ping':
                return '🏓 Pong! 机器人运行正常';

            case 'status':
                const stats = this.getStats();
                return `📊 机器人状态：\n` +
                       `✅ 运行中\n` +
                       `📨 已处理消息：${stats.messagesProcessed}\n` +
                       `👥 群聊消息：${stats.groupMessages}\n` +
                       `💬 私聊消息：${stats.privateMessages}\n` +
                       `🔔 通知事件：${stats.notices}`;

            case 'info':
                if (context.type === 'group') {
                    return `📋 群聊信息：\n` +
                           `🆔 群号：${context.groupId}\n` +
                           `👤 发送者：${context.sender.nickname || context.sender.card}\n` +
                           `🆔 用户ID：${context.userId}`;
                } else {
                    return `📋 用户信息：\n` +
                           `👤 昵称：${context.sender.nickname}\n` +
                           `🆔 用户ID：${context.userId}`;
                }

            case 'echo':
                if (args.length === 0) {
                    return '❌ 请提供要复读的消息内容\n用法：/echo <消息>';
                }
                return `🔄 ${args.join(' ')}`;

            default:
                return `❌ 未知命令：${command}\n输入 /help 查看可用命令`;
        }
    }

    /**
     * 发送回复消息
     */
    async sendReply(context, message) {
        try {
            if (context.type === 'private') {
                await this.sendPrivateMessage(context.userId, message);
            } else if (context.type === 'group') {
                await this.sendGroupMessage(context.groupId, message);
            }
        } catch (error) {
            this.logger.error('MessageHandler', `发送回复失败: ${error.message}`);
        }
    }

    /**
     * 处理群文件上传
     */
    async handleGroupUpload(event) {
        const { group_id, user_id, file } = event;
        this.stats.notices++;
        
        this.logger.info('MessageHandler', `群文件上传 [${group_id}][${user_id}]: ${file.name}`);
    }

    /**
     * 处理群管理员变动
     */
    async handleGroupAdmin(event) {
        const { group_id, user_id, sub_type } = event;
        this.stats.notices++;
        
        const action = sub_type === 'set' ? '设置' : '取消';
        this.logger.info('MessageHandler', `群管理员变动 [${group_id}]: ${action}管理员 ${user_id}`);
    }

    /**
     * 处理群成员减少
     */
    async handleGroupDecrease(event) {
        const { group_id, user_id, operator_id, sub_type } = event;
        this.stats.notices++;
        
        let action;
        switch (sub_type) {
            case 'leave':
                action = '主动退群';
                break;
            case 'kick':
                action = `被踢出群 (操作者: ${operator_id})`;
                break;
            case 'kick_me':
                action = '机器人被踢出群';
                break;
            default:
                action = '离开群聊';
        }
        
        this.logger.info('MessageHandler', `群成员减少 [${group_id}]: ${user_id} ${action}`);
    }

    /**
     * 处理群成员增加
     */
    async handleGroupIncrease(event) {
        const { group_id, user_id, operator_id, sub_type } = event;
        this.stats.notices++;
        
        let action;
        switch (sub_type) {
            case 'approve':
                action = `加入群聊 (审批者: ${operator_id})`;
                break;
            case 'invite':
                action = `被邀请入群 (邀请者: ${operator_id})`;
                break;
            default:
                action = '加入群聊';
        }
        
        this.logger.info('MessageHandler', `群成员增加 [${group_id}]: ${user_id} ${action}`);
    }

    /**
     * 处理群禁言
     */
    async handleGroupBan(event) {
        const { group_id, user_id, operator_id, duration, sub_type } = event;
        this.stats.notices++;
        
        const action = sub_type === 'ban' ? '禁言' : '解除禁言';
        const durationText = duration > 0 ? ` ${duration}秒` : '';
        
        this.logger.info('MessageHandler', `群禁言 [${group_id}]: ${operator_id} ${action} ${user_id}${durationText}`);
    }

    /**
     * 处理好友添加
     */
    async handleFriendAdd(event) {
        const { user_id } = event;
        this.stats.notices++;
        
        this.logger.info('MessageHandler', `好友添加: ${user_id}`);
    }

    /**
     * 处理群消息撤回
     */
    async handleGroupRecall(event) {
        const { group_id, user_id, operator_id, message_id } = event;
        this.stats.notices++;
        
        this.logger.info('MessageHandler', `群消息撤回 [${group_id}]: ${operator_id} 撤回了 ${user_id} 的消息 ${message_id}`);
    }

    /**
     * 处理好友消息撤回
     */
    async handleFriendRecall(event) {
        const { user_id, message_id } = event;
        this.stats.notices++;

        this.logger.info('MessageHandler', `好友消息撤回: ${user_id} 撤回了消息 ${message_id}`);
    }

    /**
     * 处理通知事件（戳一戳等）
     */
    async handleNotify(event) {
        const { sub_type, user_id, target_id, group_id, operator_id } = event;
        this.stats.notices++;

        if (sub_type === 'poke') {
            const location = group_id ? `群聊[${group_id}]` : '私聊';
            const operator = operator_id || user_id;
            const target = target_id;
            this.logger.info('MessageHandler', `戳一戳事件 ${location}: ${operator} 戳了戳 ${target}`);
        } else {
            this.logger.info('MessageHandler', `通知事件: ${sub_type}`);
        }
    }

    /**
     * 处理好友请求
     */
    async handleFriendRequest(event) {
        const { user_id, comment, flag } = event;
        this.stats.requests++;
        
        this.logger.info('MessageHandler', `好友请求: ${user_id} - ${comment} (flag: ${flag})`);
        
        // 自动处理好友请求
        const config = this.bot.config;
        if (config.GROUP_MANAGEMENT && config.GROUP_MANAGEMENT.AUTO_ACCEPT_FRIEND_REQUESTS) {
            try {
                await this.setFriendAddRequest(flag, true);
                this.logger.success('MessageHandler', `自动同意好友请求: ${user_id}`);
            } catch (error) {
                this.logger.error('MessageHandler', `自动处理好友请求失败: ${error.message}`);
            }
        }
    }

    /**
     * 处理群请求/邀请
     */
    async handleGroupRequest(event) {
        const { group_id, user_id, comment, flag, sub_type } = event;
        this.stats.requests++;
        
        const type = sub_type === 'add' ? '加群请求' : '群邀请';
        this.logger.info('MessageHandler', `${type} [${group_id}]: ${user_id} - ${comment} (flag: ${flag})`);
        
        // 自动处理群邀请
        const config = this.bot.config;
        if (config.GROUP_MANAGEMENT && config.GROUP_MANAGEMENT.AUTO_ACCEPT_GROUP_INVITES && sub_type === 'invite') {
            try {
                await this.setGroupAddRequest(flag, sub_type, true);
                this.logger.success('MessageHandler', `自动同意群邀请: ${group_id}`);
            } catch (error) {
                this.logger.error('MessageHandler', `自动处理群邀请失败: ${error.message}`);
            }
        }
    }

    /**
     * 处理生命周期事件
     */
    async handleLifecycle(event) {
        const { sub_type } = event;
        this.stats.metaEvents++;
        
        this.logger.info('MessageHandler', `生命周期事件: ${sub_type}`);
        
        if (sub_type === 'connect') {
            this.logger.success('MessageHandler', 'OneBot11连接已建立');
        } else if (sub_type === 'enable') {
            this.logger.success('MessageHandler', 'OneBot11插件已启用');
        }
    }

    /**
     * 处理心跳事件
     */
    async handleHeartbeat(event) {
        this.stats.metaEvents++;
        this.logger.debug('MessageHandler', '收到心跳事件');
    }

    /**
     * 简化的消息处理
     */
    async handleMessage(event) {
        if (!event.message_type || !event.user_id) {
            this.logger.debug('MessageHandler', '消息事件缺少必要字段');
            return;
        }

        const messageText = this.extractMessageText(event);
        const shortText = messageText.length > 30 ? messageText.substring(0, 30) + '...' : messageText;
        this.logger.info('MessageHandler', `收到${event.message_type}消息: ${shortText}`);
    }

    /**
     * 简化的通知处理
     */
    async handleNotice(event) {
        this.logger.info('MessageHandler', `收到通知事件: ${event.notice_type}`);

        // 将notice事件也传递给插件处理
        await this.processWithPlugins(event);
    }

    /**
     * 简化的请求处理
     */
    async handleRequest(event) {
        this.logger.info('MessageHandler', `收到请求事件: ${event.request_type}`);
    }

    /**
     * 简化的元事件处理
     */
    async handleMetaEventSimple(event) {
        if (event.meta_event_type === 'lifecycle') {
            this.logger.info('MessageHandler', `生命周期事件: ${event.sub_type}`);
            if (event.sub_type === 'connect') {
                this.logger.success('MessageHandler', 'OneBot11连接已建立');
            }
        } else if (event.meta_event_type === 'heartbeat') {
            this.logger.debug('MessageHandler', '收到心跳事件');
        }
    }

    /**
     * 使用插件处理消息
     */
    async processWithPlugins(event) {
        // 构建插件上下文
        const context = this.buildPluginContext(event);

        // 获取所有插件并按优先级排序
        const plugins = this.bot.getPlugins().sort((a, b) => a.priority - b.priority);

        for (const plugin of plugins) {
            try {
                // 检查插件是否应该处理此消息
                if (await plugin.shouldHandle(context)) {
                    const result = await plugin.execute(context);

                    // 如果插件处理了消息且要求停止传播，则跳出循环
                    if (result && result.handled && result.stopPropagation) {
                        this.logger.debug('MessageHandler', `插件 ${plugin.name} 停止了消息传播`);
                        break;
                    }
                }
            } catch (error) {
                this.logger.error('MessageHandler', `插件 ${plugin.name} 处理失败: ${error.message}`);
            }
        }

        // 兼容旧的插件处理器
        for (const pluginHandler of this.pluginHandlers) {
            try {
                await pluginHandler(event);
            } catch (error) {
                this.logger.error('MessageHandler', `旧插件处理失败: ${error.message}`);
            }
        }
    }

    /**
     * 构建插件上下文
     */
    buildPluginContext(event) {
        const context = {
            platform: 'onebot11',
            event: event,
            timestamp: new Date(event.time * 1000),
            messageId: event.message_id,
            selfId: event.self_id,
            ws: event.ws // WebSocket连接已经在bot.js中正确添加到event中
        };

        // 根据事件类型添加特定字段
        if (event.post_type === 'message') {
            context.type = event.message_type;
            context.userId = event.user_id.toString();
            context.message = this.extractMessageText(event);
            context.segments = this.protocol.parseMessageSegments(event.message);
            context.sender = event.sender;

            if (event.message_type === 'group') {
                context.groupId = event.group_id.toString();
            }
        } else if (event.post_type === 'notice') {
            context.type = 'notice';
            context.noticeType = event.notice_type;
            context.subType = event.sub_type;

            // 戳一戳事件
            if (event.notice_type === 'notify' && event.sub_type === 'poke') {
                context.userId = event.user_id?.toString();
                context.targetId = event.target_id?.toString();
                context.groupId = event.group_id?.toString();
                context.operatorId = event.operator_id?.toString();
            }

            // 其他notice事件的字段
            if (event.user_id) context.userId = event.user_id.toString();
            if (event.group_id) context.groupId = event.group_id.toString();
            if (event.operator_id) context.operatorId = event.operator_id.toString();
        }

        return context;
    }

    /**
     * 发送VCP日志
     */
    sendVCPLog(event) {
        if (this.bot.webSocketClient && this.bot.config.ENABLE_VCP_INTEGRATION) {
            let logContent = '';
            
            switch (event.post_type) {
                case 'message':
                    const msgText = this.extractMessageText(event);
                    const shortMsg = msgText.length > 20 ? msgText.substring(0, 20) + '...' : msgText;
                    logContent = `收到${event.message_type === 'private' ? '私聊' : '群聊'}消息: ${shortMsg}`;
                    break;
                case 'notice':
                    logContent = `收到通知事件: ${event.notice_type}`;
                    break;
                case 'request':
                    logContent = `收到请求事件: ${event.request_type}`;
                    break;
                case 'meta_event':
                    logContent = `收到元事件: ${event.meta_event_type}`;
                    break;
                default:
                    logContent = `收到未知事件: ${event.post_type}`;
            }
            
            this.bot.webSocketClient.sendVCPLog('OneBot11', 'info', logContent);
        }
    }

    /**
     * 发送私聊消息
     */
    async sendPrivateMessage(userId, message) {
        try {
            const apiData = {
                action: 'send_private_msg',
                params: {
                    user_id: parseInt(userId),
                    message: message
                }
            };

            await this.callOneBot11API(apiData);
            this.logger.success('MessageHandler', `✅ 私聊消息已发送到 ${userId}: ${message}`);

        } catch (error) {
            this.logger.error('MessageHandler', `发送私聊消息失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 发送群聊消息
     */
    async sendGroupMessage(groupId, message) {
        try {
            const apiData = {
                action: 'send_group_msg',
                params: {
                    group_id: parseInt(groupId),
                    message: message
                }
            };

            await this.callOneBot11API(apiData);
            this.logger.success('MessageHandler', `✅ 群聊消息已发送到 ${groupId}: ${message}`);

        } catch (error) {
            this.logger.error('MessageHandler', `发送群聊消息失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 调用OneBot11 API
     */
    async callOneBot11API(apiData) {
        return new Promise((resolve, reject) => {
            try {
                // 生成唯一的echo标识
                const echo = `api_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
                apiData.echo = echo;

                // 设置超时处理
                const timeout = setTimeout(() => {
                    reject(new Error('API调用超时'));
                }, 10000);

                // 监听API响应
                const responseHandler = (response) => {
                    if (response.echo === echo) {
                        clearTimeout(timeout);
                        this.bot.removeListener('api_response', responseHandler);

                        if (response.status === 'ok') {
                            resolve(response.data);
                        } else {
                            reject(new Error(`API调用失败: ${response.msg || '未知错误'}`));
                        }
                    }
                };

                this.bot.on('api_response', responseHandler);

                // 发送API请求到NapCat
                this.bot.sendToNapCat(apiData);

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 处理好友请求
     */
    async setFriendAddRequest(flag, approve, remark = '') {
        // 这里需要实现实际的API调用
        this.logger.info('MessageHandler', `处理好友请求 ${flag}: ${approve ? '同意' : '拒绝'}`);
    }

    /**
     * 处理群请求
     */
    async setGroupAddRequest(flag, subType, approve, reason = '') {
        // 这里需要实现实际的API调用
        this.logger.info('MessageHandler', `处理群请求 ${flag}: ${approve ? '同意' : '拒绝'}`);
    }

    /**
     * 添加消息过滤器
     */
    addFilter(filter) {
        if (typeof filter === 'function') {
            this.messageFilters.push(filter);
            this.logger.debug('MessageHandler', '已添加消息过滤器');
        }
    }

    /**
     * 添加插件处理器
     */
    addPluginHandler(handler) {
        if (typeof handler === 'function') {
            this.pluginHandlers.push(handler);
            this.logger.debug('MessageHandler', '已添加插件处理器');
        }
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            messagesProcessed: 0,
            privateMessages: 0,
            groupMessages: 0,
            notices: 0,
            requests: 0,
            metaEvents: 0,
            filteredMessages: 0
        };
    }
}

module.exports = MessageHandler;
