/**
 * OneBot11适配器
 * 基于VCPToolBox编码风格的OneBot11协议实现
 * 支持NapCat反向WebSocket连接
 */

const path = require('path');
const EventEmitter = require('events');
const fs = require('fs');

class OneBotv11Adapter extends EventEmitter {
    constructor(logger, config) {
        super();

        this.id = "QQ";
        this.name = "OneBotv11";
        this.logger = logger;
        this.config = config || {};

        // API请求管理
        this.echo = new Map();
        this.timeout = 120000; // 增加到120秒，适应图片发送等耗时操作

        // 消息类型定义
        this.messageTypes = {
            PRIVATE: 'private',
            GROUP: 'group',
            GUILD: 'guild'
        };

        // 事件类型定义
        this.postTypes = {
            MESSAGE: 'message',
            NOTICE: 'notice',
            REQUEST: 'request',
            META_EVENT: 'meta_event'
        };

        // 统计信息
        this.stats = {
            messagesReceived: 0,
            messagesSent: 0,
            apiCallsSuccess: 0,
            apiCallsError: 0,
            connectTime: null
        };
    }

    /**
     * 生成简化的日志消息
     */
    makeLog(msg) {
        if (typeof msg === 'string') {
            // 限制字符串长度
            const cleaned = msg.replace(/base64:\/\/.*?(,|]|")/g, "base64://...$1");
            return cleaned.length > 50 ? cleaned.substring(0, 50) + '...' : cleaned;
        }

        if (Array.isArray(msg)) {
            return `[${msg.length}段消息]`;
        }

        if (typeof msg === 'object' && msg !== null) {
            // 只显示关键信息
            const keys = Object.keys(msg);
            if (keys.length > 3) {
                return `{${keys.slice(0, 3).join(', ')}...}`;
            }
            return JSON.stringify(msg).substring(0, 100);
        }

        return String(msg);
    }

    /**
     * 发送API请求
     */
    async sendApi(ws, action, params = {}) {
        const echo = this.generateEcho();
        const request = { action, params, echo };

        return new Promise((resolve, reject) => {
            // 根据操作类型设置不同的超时时间
            const timeoutDuration = this.getTimeoutForAction(action, params);

            // 设置超时
            const timeout = setTimeout(() => {
                this.echo.delete(echo);
                this.stats.apiCallsError++;
                const error = new Error(`API请求超时: ${action}`);
                this.logger.error('OneBotv11Adapter', `API请求超时: ${action}`, this.makeLog(params));
                reject(error);
            }, timeoutDuration);

            // 保存回调
            this.echo.set(echo, { resolve, reject, timeout });
            this.logger.debug('OneBotv11Adapter', `保存API请求回调: echo=${echo}, action=${action}, 当前等待数量=${this.echo.size}`);

            // 发送请求
            try {
                if (ws && typeof ws.send === 'function' && ws.readyState === 1) { // 1 = OPEN
                    ws.send(JSON.stringify(request));
                    this.logger.debug('OneBotv11Adapter', `发送API请求: ${action}, echo=${echo}`, this.makeLog(params));
                } else {
                    clearTimeout(timeout);
                    this.echo.delete(echo);
                    const wsState = ws ? `readyState=${ws.readyState}` : 'ws=null';
                    reject(new Error(`WebSocket连接无效 (${wsState})`));
                }
            } catch (error) {
                clearTimeout(timeout);
                this.echo.delete(echo);
                this.stats.apiCallsError++;
                reject(error);
            }
        });
    }

    /**
     * 处理API响应
     */
    handleApiResponse(data) {
        this.logger.debug('OneBotv11Adapter', `收到API响应: echo=${data.echo}, retcode=${data.retcode}`, data);

        if (data.echo && this.echo.has(data.echo)) {
            const { resolve, reject, timeout } = this.echo.get(data.echo);
            clearTimeout(timeout);
            this.echo.delete(data.echo);

            this.logger.debug('OneBotv11Adapter', `处理API响应: echo=${data.echo}, retcode=${data.retcode}`);

            if (data.retcode === 0 || data.retcode === 1) {
                this.stats.apiCallsSuccess++;
                this.logger.debug('OneBotv11Adapter', `API请求成功: echo=${data.echo}`);
                resolve(data);
            } else {
                this.stats.apiCallsError++;
                this.logger.error('OneBotv11Adapter', `API请求失败: echo=${data.echo}, retcode=${data.retcode}`);
                reject(new Error(data.msg || data.wording || '未知错误'));
            }
            return true;
        } else {
            this.logger.warning('OneBotv11Adapter', `收到未匹配的API响应: echo=${data.echo}, 当前等待中的echo数量=${this.echo.size}`);
            return false;
        }
    }

    /**
     * 根据操作类型获取超时时间
     */
    getTimeoutForAction(action, params = {}) {
        // 图片发送相关操作需要更长时间
        if (action === 'send_msg' && params.message) {
            // 检查是否包含图片
            const hasImage = Array.isArray(params.message) &&
                params.message.some(msg => msg.type === 'image');
            if (hasImage) {
                return 180000; // 图片发送3分钟超时
            }
        }

        // 文件上传相关操作
        if (action.includes('upload') || action.includes('file')) {
            return 180000; // 文件操作3分钟超时
        }

        // 其他操作使用默认超时
        return this.timeout;
    }

    /**
     * 生成唯一echo标识
     */
    generateEcho() {
        return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    /**
     * 处理文件（转换为base64或保持原样）
     */
    async makeFile(file, opts = {}) {
        // 如果是Buffer，转换为base64
        if (Buffer.isBuffer(file)) {
            return `base64://${file.toString('base64')}`;
        }

        // 如果是文件路径，保持原样
        if (typeof file === 'string') {
            if (file.startsWith('http://') || file.startsWith('https://')) {
                return file; // 网络文件
            }
            if (opts.file) {
                return `file://${file}`; // 本地文件
            }
            return file;
        }

        return file;
    }

    /**
     * 构建消息数组
     */
    async makeMsg(msg) {
        if (!Array.isArray(msg)) {
            msg = [msg];
        }

        const msgs = [];
        const forward = [];

        for (let i of msg) {
            if (typeof i !== 'object') {
                i = { type: 'text', data: { text: String(i) } };
            } else if (!i.data) {
                i = { type: i.type, data: { ...i, type: undefined } };
            }

            switch (i.type) {
                case 'at':
                    i.data.qq = String(i.data.qq);
                    break;
                case 'reply':
                    i.data.id = String(i.data.id);
                    break;
                case 'button':
                    continue; // 跳过按钮
                case 'node':
                    forward.push(...i.data);
                    continue;
                case 'raw':
                    i = i.data;
                    break;
            }

            if (i.data && i.data.file) {
                i.data.file = await this.makeFile(i.data.file);
            }

            msgs.push(i);
        }

        return [msgs, forward];
    }

    /**
     * 发送消息的通用方法
     */
    async sendMsg(msg, send, sendForwardMsg) {
        const [message, forward] = await this.makeMsg(msg);
        const ret = [];

        if (forward.length) {
            const data = await sendForwardMsg(forward);
            if (Array.isArray(data)) {
                ret.push(...data);
            } else {
                ret.push(data);
            }
        }

        if (message.length) {
            ret.push(await send(message));
        }

        if (ret.length === 1) {
            return ret[0];
        }

        const message_id = [];
        for (const i of ret) {
            if (i?.message_id) {
                message_id.push(i.message_id);
            }
        }
        return { data: ret, message_id };
    }

    /**
     * 发送好友消息
     */
    async sendFriendMsg(bot, userId, msg) {
        return this.sendMsg(
            msg,
            async (message) => {
                this.logger.info('OneBotv11Adapter', `发送好友消息: ${this.makeLog(message)}`, `${bot.selfId} => ${userId}`);
                this.stats.messagesSent++;
                return await this.sendApi(bot.ws, 'send_msg', {
                    user_id: userId,
                    message: message
                });
            },
            (msg) => this.sendFriendForwardMsg(bot, userId, msg)
        );
    }

    /**
     * 发送私聊消息 (sendFriendMsg的别名，用于插件兼容性)
     */
    async sendPrivateMsg(bot, userId, msg) {
        return this.sendFriendMsg(bot, userId, msg);
    }

    /**
     * 发送群消息
     */
    async sendGroupMsg(bot, groupId, msg) {
        return this.sendMsg(
            msg,
            async (message) => {
                this.logger.info('OneBotv11Adapter', `发送群消息: ${this.makeLog(message)}`, `${bot.selfId} => ${groupId}`);
                this.stats.messagesSent++;
                return await this.sendApi(bot.ws, 'send_msg', {
                    group_id: groupId,
                    message: message
                });
            },
            (msg) => this.sendGroupForwardMsg(bot, groupId, msg)
        );
    }

    /**
     * 发送频道消息
     */
    async sendGuildMsg(bot, guildId, channelId, msg) {
        return this.sendMsg(
            msg,
            async (message) => {
                this.logger.info('OneBotv11Adapter', `发送频道消息: ${this.makeLog(message)}`, `${bot.selfId} => ${guildId}-${channelId}`);
                this.stats.messagesSent++;
                return await this.sendApi(bot.ws, 'send_guild_channel_msg', {
                    guild_id: guildId,
                    channel_id: channelId,
                    message: message
                });
            },
            (msg) => this.sendGuildForwardMsg(bot, guildId, channelId, msg)
        );
    }

    /**
     * 撤回消息
     */
    async recallMsg(bot, messageId) {
        this.logger.info('OneBotv11Adapter', `撤回消息: ${messageId}`, bot.selfId);

        if (!Array.isArray(messageId)) {
            messageId = [messageId];
        }

        const results = [];
        for (const id of messageId) {
            try {
                const result = await this.sendApi(bot.ws, 'delete_msg', { message_id: id });
                results.push(result);
            } catch (error) {
                this.logger.error('OneBotv11Adapter', `撤回消息失败: ${error.message}`, id);
                results.push(error);
            }
        }
        return results;
    }

    /**
     * 解析消息数组
     */
    parseMsg(msg) {
        const array = [];
        for (const i of Array.isArray(msg) ? msg : [msg]) {
            if (typeof i === 'object') {
                array.push({ ...i.data, type: i.type });
            } else {
                array.push({ type: 'text', text: String(i) });
            }
        }
        return array;
    }

    /**
     * 获取消息
     */
    async getMsg(bot, messageId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_msg', { message_id: messageId });
            const msg = response.data;
            if (msg?.message) {
                msg.message = this.parseMsg(msg.message);
            }
            return msg;
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取消息失败: ${error.message}`, messageId);
            throw error;
        }
    }

    /**
     * 处理OneBot11事件
     */
    handleEvent(data, ws) {
        try {
            // 验证事件格式
            if (!data || typeof data !== 'object') {
                this.logger.warning('OneBotv11Adapter', '收到无效事件数据');
                return;
            }

            // 处理API响应
            if (data.echo && this.handleApiResponse(data)) {
                return;
            }

            // 处理事件
            if (data.post_type) {
                this.stats.messagesReceived++;
                this.processEvent(data, ws);
            } else {
                this.logger.debug('OneBotv11Adapter', '收到未知消息类型', data);
            }

        } catch (error) {
            this.logger.error('OneBotv11Adapter', `处理事件失败: ${error.message}`, data);
        }
    }

    /**
     * 处理具体事件
     */
    processEvent(data, ws) {
        switch (data.post_type) {
            case this.postTypes.MESSAGE:
                this.handleMessage(data, ws);
                break;
            case this.postTypes.NOTICE:
                this.handleNotice(data, ws);
                break;
            case this.postTypes.REQUEST:
                this.handleRequest(data, ws);
                break;
            case this.postTypes.META_EVENT:
                this.handleMetaEvent(data, ws);
                break;
            default:
                this.logger.warning('OneBotv11Adapter', `未知事件类型: ${data.post_type}`, data);
        }
    }

    /**
     * 处理消息事件
     */
    handleMessage(data, ws) {
        // 解析消息内容
        if (data.message) {
            data.message = this.parseMsg(data.message);
        }

        // 记录日志
        const logMsg = this.makeLog(data.raw_message || JSON.stringify(data.message));
        switch (data.message_type) {
            case this.messageTypes.PRIVATE:
                this.logger.info('OneBotv11Adapter', `收到私聊消息: ${logMsg}`, `${data.self_id} <= ${data.user_id}`);
                break;
            case this.messageTypes.GROUP:
                this.logger.info('OneBotv11Adapter', `收到群消息: ${logMsg}`, `${data.self_id} <= ${data.group_id}, ${data.user_id}`);
                break;
            case this.messageTypes.GUILD:
                this.logger.info('OneBotv11Adapter', `收到频道消息: ${logMsg}`, `${data.self_id} <= ${data.guild_id}-${data.channel_id}, ${data.user_id}`);
                break;
        }

        // 发射消息事件
        this.emit('message', data, ws);
    }

    /**
     * 处理通知事件
     */
    handleNotice(data, ws) {
        this.logger.info('OneBotv11Adapter', `收到通知事件: ${data.notice_type}`, data);
        this.emit('notice', data, ws);
    }

    /**
     * 处理请求事件
     */
    handleRequest(data, ws) {
        this.logger.info('OneBotv11Adapter', `收到请求事件: ${data.request_type}`, data);
        this.emit('request', data, ws);
    }

    /**
     * 处理元事件
     */
    handleMetaEvent(data, ws) {
        if (data.meta_event_type === 'heartbeat') {
            this.logger.debug('OneBotv11Adapter', '收到心跳事件', data);
        } else if (data.meta_event_type === 'lifecycle') {
            this.logger.info('OneBotv11Adapter', `生命周期事件: ${data.sub_type}`, data);
            if (data.sub_type === 'connect') {
                this.stats.connectTime = new Date();
            }
        }
        this.emit('meta_event', data, ws);
    }

    /**
     * 获取转发消息
     */
    async getForwardMsg(bot, messageId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_forward_msg', { message_id: messageId });
            const msgs = response.data?.messages;

            if (Array.isArray(msgs)) {
                for (const msg of msgs) {
                    if (msg?.message) {
                        msg.message = this.parseMsg(msg.message || msg.content);
                    }
                }
            }
            return msgs;
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取转发消息失败: ${error.message}`, messageId);
            throw error;
        }
    }

    /**
     * 构建转发消息
     */
    async makeForwardMsg(msg) {
        const msgs = [];
        for (const i of msg) {
            const [content, forward] = await this.makeMsg(i.message);
            if (forward.length) {
                msgs.push(...(await this.makeForwardMsg(forward)));
            }
            if (content.length) {
                msgs.push({
                    type: 'node',
                    data: {
                        name: i.nickname || '匿名消息',
                        uin: String(Number(i.user_id) || 80000000),
                        content,
                        time: i.time
                    }
                });
            }
        }
        return msgs;
    }

    /**
     * 发送好友转发消息
     */
    async sendFriendForwardMsg(bot, userId, msg) {
        this.logger.info('OneBotv11Adapter', `发送好友转发消息: ${this.makeLog(msg)}`, `${bot.selfId} => ${userId}`);
        return await this.sendApi(bot.ws, 'send_private_forward_msg', {
            user_id: userId,
            messages: await this.makeForwardMsg(msg)
        });
    }

    /**
     * 发送群转发消息
     */
    async sendGroupForwardMsg(bot, groupId, msg) {
        this.logger.info('OneBotv11Adapter', `发送群转发消息: ${this.makeLog(msg)}`, `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'send_group_forward_msg', {
            group_id: groupId,
            messages: await this.makeForwardMsg(msg)
        });
    }

    /**
     * 发送频道转发消息
     */
    async sendGuildForwardMsg(bot, guildId, channelId, msg) {
        this.logger.info('OneBotv11Adapter', `发送频道转发消息: ${this.makeLog(msg)}`, `${bot.selfId} => ${guildId}-${channelId}`);
        // 频道转发消息通常使用普通消息方式发送
        return await this.sendGuildMsg(bot, guildId, channelId, msg);
    }

    /**
     * 获取好友列表
     */
    async getFriendArray(bot) {
        try {
            const response = await this.sendApi(bot.ws, 'get_friend_list');
            return response.data || [];
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取好友列表失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 获取好友ID列表
     */
    async getFriendList(bot) {
        const array = [];
        const friends = await this.getFriendArray(bot);
        for (const friend of friends) {
            array.push(friend.user_id);
        }
        return array;
    }

    /**
     * 获取好友信息映射
     */
    async getFriendMap(bot) {
        const map = new Map();
        const friends = await this.getFriendArray(bot);
        for (const friend of friends) {
            map.set(friend.user_id, friend);
        }
        return map;
    }

    /**
     * 获取好友信息
     */
    async getFriendInfo(bot, userId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_stranger_info', { user_id: userId });
            return response.data;
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取好友信息失败: ${error.message}`, userId);
            throw error;
        }
    }

    /**
     * 获取群组列表
     */
    async getGroupArray(bot) {
        try {
            const response = await this.sendApi(bot.ws, 'get_group_list');
            const array = response.data || [];

            // 尝试获取频道列表并合并
            try {
                const guilds = await this.getGuildArray(bot);
                for (const guild of guilds) {
                    const channels = await this.getGuildChannelArray(bot, guild.guild_id);
                    for (const channel of channels) {
                        array.push({
                            guild,
                            channel,
                            group_id: `${guild.guild_id}-${channel.channel_id}`,
                            group_name: `${guild.guild_name}-${channel.channel_name}`
                        });
                    }
                }
            } catch (err) {
                this.logger.debug('OneBotv11Adapter', '获取频道列表失败，跳过', err.message);
            }

            return array;
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取群组列表失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 获取群组ID列表
     */
    async getGroupList(bot) {
        const array = [];
        const groups = await this.getGroupArray(bot);
        for (const group of groups) {
            array.push(group.group_id);
        }
        return array;
    }

    /**
     * 获取群组信息映射
     */
    async getGroupMap(bot) {
        const map = new Map();
        const groups = await this.getGroupArray(bot);
        for (const group of groups) {
            map.set(group.group_id, group);
        }
        return map;
    }

    /**
     * 获取群组信息
     */
    async getGroupInfo(bot, groupId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_group_info', { group_id: groupId });
            return response.data;
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取群组信息失败: ${error.message}`, groupId);
            throw error;
        }
    }

    /**
     * 获取群成员列表
     */
    async getMemberArray(bot, groupId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_group_member_list', { group_id: groupId });
            return response.data || [];
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取群成员列表失败: ${error.message}`, groupId);
            return [];
        }
    }

    /**
     * 获取群成员ID列表
     */
    async getMemberList(bot, groupId) {
        const array = [];
        const members = await this.getMemberArray(bot, groupId);
        for (const member of members) {
            array.push(member.user_id);
        }
        return array;
    }

    /**
     * 获取群成员信息映射
     */
    async getMemberMap(bot, groupId) {
        const map = new Map();
        const members = await this.getMemberArray(bot, groupId);
        for (const member of members) {
            map.set(member.user_id, member);
        }
        return map;
    }

    /**
     * 获取所有群的成员映射
     */
    async getGroupMemberMap(bot, cacheEnabled = true) {
        if (!cacheEnabled) {
            return this.getGroupMap(bot);
        }

        const groupMap = await this.getGroupMap(bot);
        const memberMaps = new Map();

        for (const [groupId, group] of groupMap) {
            if (group.guild) continue; // 跳过频道
            const memberMap = await this.getMemberMap(bot, groupId);
            memberMaps.set(groupId, memberMap);
        }

        return memberMaps;
    }

    /**
     * 获取群成员信息
     */
    async getMemberInfo(bot, groupId, userId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_group_member_info', {
                group_id: groupId,
                user_id: userId
            });
            return response.data;
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取群成员信息失败: ${error.message}`, `${groupId}-${userId}`);
            throw error;
        }
    }

    /**
     * 获取频道列表
     */
    async getGuildArray(bot) {
        try {
            const response = await this.sendApi(bot.ws, 'get_guild_list');
            return response.data || [];
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取频道列表失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 获取频道信息
     */
    async getGuildInfo(bot, guildId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_guild_meta_by_guest', { guild_id: guildId });
            return response.data;
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取频道信息失败: ${error.message}`, guildId);
            throw error;
        }
    }

    /**
     * 获取频道子频道列表
     */
    async getGuildChannelArray(bot, guildId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_guild_channel_list', { guild_id: guildId });
            return response.data || [];
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取频道子频道列表失败: ${error.message}`, guildId);
            return [];
        }
    }

    /**
     * 获取频道子频道映射
     */
    async getGuildChannelMap(bot, guildId) {
        const map = new Map();
        const channels = await this.getGuildChannelArray(bot, guildId);
        for (const channel of channels) {
            map.set(channel.channel_id, channel);
        }
        return map;
    }

    /**
     * 获取频道成员列表
     */
    async getGuildMemberArray(bot, guildId) {
        const array = [];
        let nextToken = '';

        try {
            while (true) {
                const response = await this.sendApi(bot.ws, 'get_guild_member_list', {
                    guild_id: guildId,
                    next_token: nextToken
                });

                const list = response.data;
                if (!list) break;

                for (const member of list.members) {
                    array.push({
                        ...member,
                        user_id: member.tiny_id
                    });
                }

                if (list.finished) break;
                nextToken = list.next_token;
            }
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取频道成员列表失败: ${error.message}`, guildId);
        }

        return array;
    }

    /**
     * 获取频道成员ID列表
     */
    async getGuildMemberList(bot, guildId) {
        const array = [];
        const members = await this.getGuildMemberArray(bot, guildId);
        for (const member of members) {
            array.push(member.user_id);
        }
        return array;
    }

    /**
     * 获取频道成员映射
     */
    async getGuildMemberMap(bot, guildId) {
        const map = new Map();
        const members = await this.getGuildMemberArray(bot, guildId);
        for (const member of members) {
            map.set(member.user_id, member);
        }
        return map;
    }

    /**
     * 获取频道成员信息
     */
    async getGuildMemberInfo(bot, guildId, userId) {
        try {
            const response = await this.sendApi(bot.ws, 'get_guild_member_profile', {
                guild_id: guildId,
                user_id: userId
            });
            return response.data;
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `获取频道成员信息失败: ${error.message}`, `${guildId}-${userId}`);
            throw error;
        }
    }

    /**
     * 设置QQ资料
     */
    async setProfile(bot, profile) {
        this.logger.info('OneBotv11Adapter', `设置资料: ${JSON.stringify(profile)}`, bot.selfId);
        return await this.sendApi(bot.ws, 'set_qq_profile', profile);
    }

    /**
     * 设置QQ头像
     */
    async setAvatar(bot, file) {
        this.logger.info('OneBotv11Adapter', `设置头像: ${file}`, bot.selfId);
        return await this.sendApi(bot.ws, 'set_qq_avatar', {
            file: await this.makeFile(file)
        });
    }

    /**
     * 发送点赞
     */
    async sendLike(bot, userId, times) {
        this.logger.info('OneBotv11Adapter', `点赞: ${times}次`, `${bot.selfId} => ${userId}`);
        return await this.sendApi(bot.ws, 'send_like', {
            user_id: userId,
            times: times
        });
    }

    /**
     * 设置群名称
     */
    async setGroupName(bot, groupId, groupName) {
        this.logger.info('OneBotv11Adapter', `设置群名: ${groupName}`, `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'set_group_name', {
            group_id: groupId,
            group_name: groupName
        });
    }

    /**
     * 设置群头像
     */
    async setGroupAvatar(bot, groupId, file) {
        this.logger.info('OneBotv11Adapter', `设置群头像: ${file}`, `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'set_group_portrait', {
            group_id: groupId,
            file: await this.makeFile(file)
        });
    }

    /**
     * 设置群管理员
     */
    async setGroupAdmin(bot, groupId, userId, enable) {
        this.logger.info('OneBotv11Adapter', `${enable ? '设置' : '取消'}群管理员: ${userId}`, `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'set_group_admin', {
            group_id: groupId,
            user_id: userId,
            enable: enable
        });
    }

    /**
     * 设置群名片
     */
    async setGroupCard(bot, groupId, userId, card) {
        this.logger.info('OneBotv11Adapter', `设置群名片: ${card}`, `${bot.selfId} => ${groupId}, ${userId}`);
        return await this.sendApi(bot.ws, 'set_group_card', {
            group_id: groupId,
            user_id: userId,
            card: card
        });
    }

    /**
     * 设置群头衔
     */
    async setGroupTitle(bot, groupId, userId, specialTitle, duration) {
        this.logger.info('OneBotv11Adapter', `设置群头衔: ${specialTitle} ${duration}`, `${bot.selfId} => ${groupId}, ${userId}`);
        return await this.sendApi(bot.ws, 'set_group_special_title', {
            group_id: groupId,
            user_id: userId,
            special_title: specialTitle,
            duration: duration
        });
    }

    /**
     * 群打卡
     */
    async sendGroupSign(bot, groupId) {
        this.logger.info('OneBotv11Adapter', '群打卡', `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'send_group_sign', {
            group_id: groupId
        });
    }

    /**
     * 禁言群成员
     */
    async setGroupBan(bot, groupId, userId, duration) {
        this.logger.info('OneBotv11Adapter', `禁言群成员: ${duration}秒`, `${bot.selfId} => ${groupId}, ${userId}`);
        return await this.sendApi(bot.ws, 'set_group_ban', {
            group_id: groupId,
            user_id: userId,
            duration: duration
        });
    }

    /**
     * 全员禁言
     */
    async setGroupWholeBan(bot, groupId, enable) {
        this.logger.info('OneBotv11Adapter', `${enable ? '开启' : '关闭'}全员禁言`, `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'set_group_whole_ban', {
            group_id: groupId,
            enable: enable
        });
    }

    /**
     * 踢出群成员
     */
    async setGroupKick(bot, groupId, userId, rejectAddRequest = false) {
        this.logger.info('OneBotv11Adapter', `踢出群成员${rejectAddRequest ? '拒绝再次加群' : ''}`, `${bot.selfId} => ${groupId}, ${userId}`);
        return await this.sendApi(bot.ws, 'set_group_kick', {
            group_id: groupId,
            user_id: userId,
            reject_add_request: rejectAddRequest
        });
    }

    /**
     * 退出群聊
     */
    async setGroupLeave(bot, groupId, isDismiss = false) {
        this.logger.info('OneBotv11Adapter', isDismiss ? '解散群' : '退群', `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'set_group_leave', {
            group_id: groupId,
            is_dismiss: isDismiss
        });
    }

    /**
     * 下载文件
     */
    async downloadFile(bot, url, threadCount, headers) {
        return await this.sendApi(bot.ws, 'download_file', {
            url: url,
            thread_count: threadCount,
            headers: headers
        });
    }

    /**
     * 发送好友文件
     */
    async sendFriendFile(bot, userId, file, name) {
        const fileName = name || require('path').basename(file);
        this.logger.info('OneBotv11Adapter', `发送好友文件: ${fileName}(${file})`, `${bot.selfId} => ${userId}`);
        return await this.sendApi(bot.ws, 'upload_private_file', {
            user_id: userId,
            file: (await this.makeFile(file, { file: true })).replace("file://", ""),
            name: fileName
        });
    }

    /**
     * 发送群文件
     */
    async sendGroupFile(bot, groupId, file, folder, name) {
        const fileName = name || require('path').basename(file);
        this.logger.info('OneBotv11Adapter', `发送群文件: ${folder || ''}/${fileName}(${file})`, `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'upload_group_file', {
            group_id: groupId,
            folder: folder,
            file: (await this.makeFile(file, { file: true })).replace("file://", ""),
            name: fileName
        });
    }

    /**
     * 删除群文件
     */
    async deleteGroupFile(bot, groupId, fileId, busid) {
        this.logger.info('OneBotv11Adapter', `删除群文件: ${fileId}(${busid})`, `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'delete_group_file', {
            group_id: groupId,
            file_id: fileId,
            busid: busid
        });
    }

    /**
     * 创建群文件夹
     */
    async createGroupFileFolder(bot, groupId, name) {
        this.logger.info('OneBotv11Adapter', `创建群文件夹: ${name}`, `${bot.selfId} => ${groupId}`);
        return await this.sendApi(bot.ws, 'create_group_file_folder', {
            group_id: groupId,
            name: name
        });
    }

    /**
     * 获取群文件系统信息
     */
    async getGroupFileSystemInfo(bot, groupId) {
        return await this.sendApi(bot.ws, 'get_group_file_system_info', {
            group_id: groupId
        });
    }

    /**
     * 获取群文件列表
     */
    async getGroupFiles(bot, groupId, folderId) {
        if (folderId) {
            return await this.sendApi(bot.ws, 'get_group_files_by_folder', {
                group_id: groupId,
                folder_id: folderId
            });
        }
        return await this.sendApi(bot.ws, 'get_group_root_files', {
            group_id: groupId
        });
    }

    /**
     * 获取群文件下载链接
     */
    async getGroupFileUrl(bot, groupId, fileId, busid) {
        return await this.sendApi(bot.ws, 'get_group_file_url', {
            group_id: groupId,
            file_id: fileId,
            busid: busid
        });
    }

    /**
     * 删除好友
     */
    async deleteFriend(bot, userId) {
        this.logger.info('OneBotv11Adapter', '删除好友', `${bot.selfId} => ${userId}`);
        return await this.sendApi(bot.ws, 'delete_friend', { user_id: userId });
    }

    /**
     * 处理好友添加请求
     */
    async setFriendAddRequest(bot, flag, approve, remark) {
        return await this.sendApi(bot.ws, 'set_friend_add_request', {
            flag: flag,
            approve: approve,
            remark: remark
        });
    }

    /**
     * 处理群添加请求
     */
    async setGroupAddRequest(bot, flag, approve, reason, subType = 'add') {
        return await this.sendApi(bot.ws, 'set_group_add_request', {
            flag: flag,
            sub_type: subType,
            approve: approve,
            reason: reason
        });
    }

    /**
     * 获取群荣誉信息
     */
    async getGroupHonorInfo(bot, groupId) {
        return await this.sendApi(bot.ws, 'get_group_honor_info', { group_id: groupId });
    }

    /**
     * 获取精华消息列表
     */
    async getEssenceMsg(bot, groupId) {
        return await this.sendApi(bot.ws, 'get_essence_msg_list', { group_id: groupId });
    }

    /**
     * 设置精华消息
     */
    async setEssenceMsg(bot, messageId) {
        return await this.sendApi(bot.ws, 'set_essence_msg', { message_id: messageId });
    }

    /**
     * 删除精华消息
     */
    async deleteEssenceMsg(bot, messageId) {
        return await this.sendApi(bot.ws, 'delete_essence_msg', { message_id: messageId });
    }

    // pickFriend方法已移除 - VCPToolBox风格不使用对象绑定模式

    // pickMember方法已移除 - VCPToolBox风格不使用对象绑定模式

    // pickGroup方法已移除 - VCPToolBox风格不使用对象绑定模式

    /**
     * 处理连接事件
     */
    async connect(data, ws) {
        this.logger.debug('OneBotv11Adapter', '处理连接事件', data);

        // 保存selfId到适配器实例
        this.selfId = data.self_id;

        // 创建简化的bot对象
        const bot = {
            selfId: data.self_id,
            ws: ws,
            adapter: this,
            startTime: data.time || Date.now()
        };

        // 获取基本信息
        try {
            const loginInfo = await this.sendApi(ws, 'get_login_info');
            bot.info = loginInfo.data || {};
            bot.uin = bot.info.user_id;
            bot.nickname = bot.info.nickname;
        } catch (error) {
            this.logger.warning('OneBotv11Adapter', `获取登录信息失败: ${error.message}`);
        }

        // 获取版本信息
        try {
            const versionInfo = await this.sendApi(ws, 'get_version_info');
            bot.version = {
                ...versionInfo.data,
                id: this.id,
                name: this.name
            };
        } catch (error) {
            this.logger.warning('OneBotv11Adapter', `获取版本信息失败: ${error.message}`);
        }

        // 获取CSRF令牌
        try {
            const csrfResponse = await this.sendApi(ws, 'get_csrf_token');
            bot.bkn = csrfResponse.token;
        } catch (error) {
            this.logger.warning('OneBotv11Adapter', `获取CSRF令牌失败: ${error.message}`);
        }

        this.logger.info('OneBotv11Adapter', `${this.name}(${this.id}) 已连接`, data.self_id);
        this.emit('connect', { ...data, bot });
    }

    /**
     * 处理消息事件
     */
    makeMessage(data) {
        data.message = this.parseMsg(data.message);

        switch (data.message_type) {
            case "private": {
                const name = data.sender.card || data.sender.nickname;
                this.logger.info('OneBotv11Adapter', `好友消息: ${name ? `[${name}] ` : ''}${data.raw_message}`, `${data.self_id} <= ${data.user_id}`);
                break;
            }
            case "group": {
                const groupName = data.group_name;
                const userName = data.sender.card || data.sender.nickname;
                this.logger.info('OneBotv11Adapter', `群消息: ${userName ? `[${groupName ? `${groupName}, ` : ''}${userName}] ` : ''}${data.raw_message}`, `${data.self_id} <= ${data.group_id}, ${data.user_id}`);
                break;
            }
            case "guild":
                data.message_type = "group";
                data.group_id = `${data.guild_id}-${data.channel_id}`;
                this.logger.info('OneBotv11Adapter', `频道消息: [${data.sender.nickname}] ${data.raw_message}`, `${data.self_id} <= ${data.group_id}, ${data.user_id}`);
                Object.defineProperty(data, "friend", {
                    get() {
                        return this.member || {};
                    },
                });
                break;
            default:
                this.logger.warning('OneBotv11Adapter', `未知消息类型: ${data.message_type}`, data.self_id);
        }

        // 发送消息事件
        this.emit('message', data);
    }

    /**
     * 处理通知事件
     */
    async makeNotice(data) {
        this.logger.debug('OneBotv11Adapter', `处理通知事件: ${data.notice_type}`, data);

        // 发送通知事件
        this.emit('notice', data);

        // 记录常见通知类型
        switch (data.notice_type) {
            case "friend_recall":
                this.logger.info('OneBotv11Adapter', `好友消息撤回: ${data.message_id}`, `${data.self_id} <= ${data.user_id}`);
                break;
            case "group_recall":
                this.logger.info('OneBotv11Adapter', `群消息撤回: ${data.operator_id} => ${data.user_id} ${data.message_id}`, `${data.self_id} <= ${data.group_id}`);
                break;
            case "group_increase":
                this.logger.info('OneBotv11Adapter', `群成员增加: ${data.operator_id} => ${data.user_id} ${data.sub_type}`, `${data.self_id} <= ${data.group_id}`);
                break;
            case "group_decrease":
                this.logger.info('OneBotv11Adapter', `群成员减少: ${data.operator_id} => ${data.user_id} ${data.sub_type}`, `${data.self_id} <= ${data.group_id}`);
                break;
            case "group_admin":
                this.logger.info('OneBotv11Adapter', `群管理员变动: ${data.sub_type}`, `${data.self_id} <= ${data.group_id}, ${data.user_id}`);
                break;
            case "group_upload":
                this.logger.info('OneBotv11Adapter', `群文件上传`, `${data.self_id} <= ${data.group_id}, ${data.user_id}`);
                break;
            case "group_ban":
                this.logger.info('OneBotv11Adapter', `群禁言: ${data.operator_id} => ${data.user_id} ${data.sub_type} ${data.duration}秒`, `${data.self_id} <= ${data.group_id}`);
                break;
            case "group_msg_emoji_like":
                this.logger.info('OneBotv11Adapter', `群消息回应: ${data.message_id}`, `${data.self_id} <= ${data.group_id}, ${data.user_id}`);
                break;
            case "friend_add":
                this.logger.info('OneBotv11Adapter', `好友添加`, `${data.self_id} <= ${data.user_id}`);
                break;
            case "notify":
                this.logger.info('OneBotv11Adapter', `通知事件: ${data.sub_type}`, `${data.self_id} <= ${data.group_id || data.user_id}`);
                break;
            case "group_card":
                this.logger.info('OneBotv11Adapter', `群名片更新: ${data.card_old} => ${data.card_new}`, `${data.self_id} <= ${data.group_id}, ${data.user_id}`);
                break;
            case "offline_file":
                this.logger.info('OneBotv11Adapter', `离线文件`, `${data.self_id} <= ${data.user_id}`);
                break;
            case "client_status":
                this.logger.info('OneBotv11Adapter', `客户端${data.online ? '上线' : '下线'}`, data.self_id);
                break;
            case "essence":
                this.logger.info('OneBotv11Adapter', `群精华消息: ${data.operator_id} => ${data.sender_id} ${data.sub_type} ${data.message_id}`, `${data.self_id} <= ${data.group_id}`);
                break;
            case "guild_channel_recall":
                this.logger.info('OneBotv11Adapter', `频道消息撤回: ${data.operator_id} => ${data.user_id} ${data.message_id}`, `${data.self_id} <= ${data.guild_id}-${data.channel_id}`);
                break;
            case "message_reactions_updated":
                this.logger.info('OneBotv11Adapter', `频道消息表情贴: ${data.message_id}`, `${data.self_id} <= ${data.guild_id}-${data.channel_id}, ${data.user_id}`);
                break;
            case "channel_updated":
                this.logger.info('OneBotv11Adapter', `子频道更新通知`, `${data.self_id} <= ${data.guild_id}-${data.channel_id}, ${data.user_id}`);
                break;
            case "channel_created":
                this.logger.info('OneBotv11Adapter', `频道创建通知`, `${data.self_id} <= ${data.guild_id}-${data.channel_id}, ${data.user_id}`);
                break;
            case "channel_destroyed":
                this.logger.info('OneBotv11Adapter', `频道删除通知`, `${data.self_id} <= ${data.guild_id}-${data.channel_id}, ${data.user_id}`);
                break;
            case "bot_offline":
                this.logger.info('OneBotv11Adapter', `系统通知: ${data.tag || '账号下线'}: ${data.message}`, data.self_id);
                break;
            default:
                this.logger.warning('OneBotv11Adapter', `未知通知类型: ${data.notice_type}`, data);
                break;
        }
    }

    /**
     * 处理WebSocket消息
     */
    message(data, ws) {
        try {
            if (typeof data === 'string') {
                data = JSON.parse(data);
            }
            this.handleEvent(data, ws);
        } catch (error) {
            this.logger.error('OneBotv11Adapter', `解析消息失败: ${error.message}`, data);
        }
    }

    /**
     * 解析消息段
     * 根据NapCat文档格式解析消息数组
     */
    parseMessageSegments(message) {
        if (!message) {
            return [];
        }

        // 如果是字符串，转换为文本段
        if (typeof message === 'string') {
            return [{
                type: 'text',
                data: { text: message }
            }];
        }

        // 如果已经是数组格式，直接返回
        if (Array.isArray(message)) {
            return message.map(segment => {
                // 确保每个段都有正确的格式
                return {
                    type: segment.type || 'text',
                    data: segment.data || {}
                };
            });
        }

        // 其他情况，尝试转换为文本
        return [{
            type: 'text',
            data: { text: String(message) }
        }];
    }

    /**
     * 获取适配器统计信息
     */
    getStats() {
        return {
            ...this.stats,
            echoCount: this.echo.size,
            uptime: this.stats.connectTime ? Date.now() - this.stats.connectTime.getTime() : 0
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 清理所有待处理的API请求
        for (const [, { reject, timeout }] of this.echo) {
            clearTimeout(timeout);
            reject(new Error('适配器正在关闭'));
        }
        this.echo.clear();

        // 移除所有事件监听器
        this.removeAllListeners();

        this.logger.info('OneBotv11Adapter', '适配器已清理');
    }
}

module.exports = OneBotv11Adapter;
