// Plugin/Mcp/BilibiliFetch.js - Bilibili内容获取MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class BilibiliFetchMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'BilibiliFetch';
        this.description = '获取Bilibili视频或直播信息，包括标题、描述、字幕等内容，支持token截断';
        this.vcpName = 'BilibiliFetch';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: 'Bilibili视频或直播的URL，支持完整URL或BV号'
                }
            },
            required: ['url']
        };
    }

    async execute(args) {
        try {
            this.validateArgs(args);
            
            const { url } = args;
            
            this.log('info', `开始获取Bilibili内容: ${url}`);
            
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin({ url });
            
            // 解析VCP插件返回的结果
            let parsedResult;
            if (typeof result === 'string') {
                try {
                    parsedResult = JSON.parse(result);
                } catch (e) {
                    // 如果不是JSON格式，作为纯文本处理
                    parsedResult = { 
                        content: result,
                        token_info: {
                            original_tokens: Math.ceil(result.length / 4),
                            final_tokens: Math.ceil(result.length / 4),
                            truncated: false
                        }
                    };
                }
            } else {
                parsedResult = result;
            }
            
            // 记录token信息
            if (parsedResult.token_info) {
                this.log('info', `Token信息: 原始${parsedResult.token_info.original_tokens}个token, 最终${parsedResult.token_info.final_tokens}个token, 截断: ${parsedResult.token_info.truncated}`);
                
                if (parsedResult.token_info.truncated) {
                    this.log('warning', `内容已截断，原始token数量超过限制`);
                }
            }
            
            // 格式化返回结果
            const response = {
                type: 'bilibili_fetch',
                status: 'success',
                message: 'Bilibili内容获取完成',
                data: {
                    url: url,
                    content: parsedResult.result || parsedResult.content || parsedResult,
                    token_info: parsedResult.token_info || {
                        original_tokens: 0,
                        final_tokens: 0,
                        truncated: false
                    },
                    fetch_time: new Date().toISOString()
                }
            };
            
            this.log('success', `Bilibili内容获取成功: ${url}`);
            return response;
            
        } catch (error) {
            this.log('error', `Bilibili内容获取失败: ${error.message}`);
            return {
                type: 'bilibili_fetch',
                status: 'error',
                message: `获取Bilibili内容失败: ${error.message}`,
                data: null
            };
        }
    }
}

module.exports = BilibiliFetchMcp; 