// components/memorySystem.js
// 情感记忆系统相关函数 - 优化版
// 支持智能对话合并、本地时间戳、群聊私聊统一处理

const logger = require('../utils/logger.cjs');

/**
 * 获取本地时间戳 (YYYY-MM-DD HH:mm:ss 格式)
 */
function getLocalTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 获取北京时间戳（保持兼容性）
 */
function formatBeijingTime() {
    return getLocalTimestamp();
}

/**
 * 智能情感记忆记录函数
 * 优化版：支持对话合并、本地时间戳、统一群聊私聊处理
 */
async function recordEmotionMemory(userId, userMessage, aiResponse, assistantName = null, metadata = {}) {
    if (!global.advancedMemoryPlugin) {
        logger.warn('情感记忆插件', '智能情感记忆系统插件未初始化，跳过记录');
        return { success: false, reason: 'plugin_not_initialized' };
    }
    
    const startTime = Date.now();
    const localTimestamp = getLocalTimestamp();
    
    // 提取聊天相关信息
    const chatType = metadata.chat_type || metadata.chatType || 'private';
    const chatName = metadata.chat_name || metadata.chatName || '';
    const finalAssistantName = assistantName || metadata.assistantName || metadata.assistant_name || 'Assistant';
    
    logger.info('智能记忆记录', `开始智能情感记忆记录 [${userId}]`, {
        messageLength: userMessage?.length || 0,
        responseLength: aiResponse?.length || 0,
        timestamp: localTimestamp,
        assistantName: finalAssistantName,
        chatType: chatType,
        chatName: chatName,
        metadata: {
            type: metadata.type,
            model: metadata.model,
            stream: metadata.stream,
            toolCalls: metadata.toolCalls
        }
    });
    
    try {
        // 构建优化的参数结构
        const optimizedParams = {
            userId: userId,
            userMessage: userMessage,
            aiResponse: aiResponse,
            timestamp: localTimestamp,
            personaName: finalAssistantName,
            metadata: {
                ...metadata,
                localTimestamp: localTimestamp,
                serverTimestamp: new Date().toISOString(),
                assistantName: finalAssistantName,
                chatType: chatType,
                chatName: chatName,
                recordingVersion: '3.0.0' // 标记优化版本
            }
        };

        // 调用插件进行事务性全量处理
        const result = await global.advancedMemoryPlugin.processConversation(optimizedParams);
        
        const duration = Date.now() - startTime;
        
        if (result.success) {
            logger.success('智能记忆完成', `成功智能情感记忆记录 [${userId}] 耗时${duration}ms`, {
                transactionId: result.transactionId,
                sessionInfo: result.sessionInfo || {},
                processedModules: Object.keys(result.results || {}),
                localTimestamp: localTimestamp,
                userMessagePreview: userMessage.substring(0, 50) + '...',
                aiResponsePreview: aiResponse.substring(0, 50) + '...',
                chatType: chatType,
                chatName: chatName
            });

            // 异步执行对话后处理（情感分析、用户关系分析、概念学习）
            setImmediate(async () => {
                try {
                    logger.info('对话后处理', `开始异步智能分析 [${userId}] (${chatType})`);
                    await global.advancedMemoryPlugin.processPostConversationAnalysis({
                        userId: userId,
                        userMessage: userMessage,
                        aiResponse: aiResponse,
                        personaName: finalAssistantName,
                        conversationContext: `对话时间: ${localTimestamp}, 类型: ${chatType}, 群组: ${chatName || '私聊'}`,
                        chatType: chatType,
                        chatName: chatName
                    });
                    logger.success('对话后处理', `异步智能分析完成 [${userId}] (${chatType})`);
                } catch (error) {
                    logger.error('对话后处理', `异步智能分析失败 [${userId}]: ${error.message}`);
                }
            });

            return {
                success: true,
                transactionId: result.transactionId,
                sessionInfo: result.sessionInfo || {},
                duration: duration,
                processedModules: Object.keys(result.results || {}),
                message: '智能情感记忆处理成功',
                recordingMode: 'intelligent_merge', // 标记为智能合并模式
                chatType: chatType,
                chatName: chatName
            };
            
        } else {
            logger.error('智能记忆失败', `失败智能情感记忆记录 [${userId}] 耗时${duration}ms`, {
                transactionId: result.transactionId,
                error: result.error,
                localTimestamp: localTimestamp,
                chatType: chatType,
                chatName: chatName
            });
            
            return {
                success: false,
                transactionId: result.transactionId,
                duration: duration,
                error: result.error,
                message: '智能情感记忆处理失败，数据已回滚',
                chatType: chatType,
                chatName: chatName
            };
        }
        
    } catch (error) {
        const duration = Date.now() - startTime;
        logger.error('智能记忆异常', `智能情感记忆记录异常 [${userId}] 耗时${duration}ms`, {
            error: error.message,
            stack: error.stack,
            localTimestamp: localTimestamp,
            chatType: chatType,
            chatName: chatName
        });
        
        return {
            success: false,
            duration: duration,
            error: error.message,
            message: '智能情感记忆处理异常',
            chatType: chatType,
            chatName: chatName
        };
    }
}

/**
 * 生成智能上下文（优化版）
 * 基于新的会话系统获取相关记忆和历史对话
 */
async function generateIntelligentContext(userId, userMessage, maxContextSize = 15, assistantName = 'Assistant') {
    if (!global.advancedMemoryPlugin) {
        logger.debug('智能上下文', '智能情感记忆系统插件未初始化，跳过上下文生成');
        return '';
    }
    
    try {
        logger.info('智能上下文', `开始生成智能上下文 [${userId}] (${assistantName})`);
        
        const startTime = Date.now();
        
        // 调用插件的智能上下文生成
        const contextResult = await global.advancedMemoryPlugin.generateIntelligentContext({
            userId: userId,
            userMessage: userMessage,
            personaName: assistantName,
            maxContextSize: maxContextSize,
            includeEmotionalContext: true,
            includeConceptualLinks: true,
            includeRecentSessions: true, // 新增：包含最近会话
            timestamp: getLocalTimestamp()
        });
        
        const duration = Date.now() - startTime;
        
        if (contextResult.success) {
            const contextLength = contextResult.context ? contextResult.context.length : 0;
            if (contextLength > 0) {
                logger.success('智能上下文', 
                    `生成智能上下文成功 [${userId}] 耗时${duration}ms，长度: ${contextLength}`
                );
            } else {
                logger.info('智能上下文', 
                    `生成智能上下文成功但内容为空 [${userId}] 耗时${duration}ms（未找到相关记忆或历史）`
                );
            }

            return contextResult.context || '';
        } else {
            logger.warning('智能上下文', 
                `生成智能上下文失败 [${userId}]: ${contextResult.error || '未知错误'}`
            );
            return '';
        }
        
    } catch (error) {
        logger.error('智能上下文', 
            `生成智能上下文异常 [${userId}]: ${error.message}`
        );
        return '';
    }
}

/**
 * 获取用户情感状态（优化版）
 */
async function getUserEmotionState(userId, assistantName = 'Assistant') {
    if (!global.advancedMemoryPlugin) {
        return null;
    }

    try {
        const emotionState = await global.advancedMemoryPlugin.getCurrentEmotionalState(userId, assistantName);
        return emotionState?.success ? emotionState.data : null;
    } catch (error) {
        logger.error('情感状态', `获取用户情感状态失败 [${userId}]: ${error.message}`);
        return null;
    }
}

/**
 * 获取用户关系状态（优化版）
 */
async function getUserAffinityState(userId, assistantName = 'Assistant') {
    if (!global.advancedMemoryPlugin) {
        return null;
    }

    try {
        const affinityState = await global.advancedMemoryPlugin.getCurrentAffinityState(userId, assistantName);
        return affinityState?.success ? affinityState.data : null;
    } catch (error) {
        logger.error('关系状态', `获取用户关系状态失败 [${userId}]: ${error.message}`);
        return null;
    }
}

/**
 * 获取记忆统计信息（新增）
 */
async function getMemoryStats(userId = null, assistantName = null) {
    if (!global.advancedMemoryPlugin) {
        return { error: 'plugin_not_initialized' };
    }

    try {
        const stats = await global.advancedMemoryPlugin.getMemoryStatistics(userId, assistantName);
        return stats?.success ? stats.data : { error: 'stats_unavailable' };
    } catch (error) {
        logger.error('记忆统计', `获取记忆统计失败: ${error.message}`);
        return { error: error.message };
    }
}

/**
 * 清理旧记忆数据（新增）
 */
async function cleanupOldMemories(retentionDays = 90) {
    if (!global.advancedMemoryPlugin) {
        return { success: false, error: 'plugin_not_initialized' };
    }

    try {
        logger.info('记忆清理', `开始清理${retentionDays}天前的记忆数据`);
        
        const cleanupResult = await global.advancedMemoryPlugin.cleanupOldData(retentionDays);
        
        if (cleanupResult.success) {
            logger.success('记忆清理', 
                `记忆数据清理完成 - 清理项目: ${Object.keys(cleanupResult.cleaned || {}).join(', ')}`
            );
        }
        
        return cleanupResult;
    } catch (error) {
        logger.error('记忆清理', `清理记忆数据失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 保持向后兼容的时间工具函数
function getTimeAgo(timestamp) {
    const now = Date.now();
    const past = new Date(timestamp).getTime();
    const diffMs = now - past;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 1) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}小时前`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 30) return `${diffDays}天前`;
    
    const diffMonths = Math.floor(diffDays / 30);
    if (diffMonths < 12) return `${diffMonths}个月前`;
    
    const diffYears = Math.floor(diffMonths / 12);
    return `${diffYears}年前`;
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatUptime(seconds) {
    const days = Math.floor(seconds / (24 * 60 * 60));
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((seconds % (60 * 60)) / 60);
    const secs = Math.floor(seconds % 60);
    
    let result = '';
    if (days > 0) result += `${days}天 `;
    if (hours > 0) result += `${hours}小时 `;
    if (minutes > 0) result += `${minutes}分钟 `;
    result += `${secs}秒`;
    
    return result;
}

// 导出所有函数
module.exports = {
    // 主要功能函数
    recordEmotionMemory,
    generateIntelligentContext,
    
    // 状态查询函数
    getUserEmotionState,
    getUserAffinityState,
    getMemoryStats,
    
    // 管理函数
    cleanupOldMemories,
    
    // 时间工具函数
    getLocalTimestamp,
    formatBeijingTime, // 保持兼容性
    getTimeAgo,
    formatBytes,
    formatUptime
};
