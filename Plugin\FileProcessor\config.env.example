# FileProcessor 插件配置文件示例

# OpenAI API配置
FILEPROCESSOR_API_URL=https://api.openai.com/v1/chat/completions
FILEPROCESSOR_API_KEY=your_api_key_here

# 图片处理模型列表（用逗号分隔）
# 支持视觉功能的模型用于处理图片文件
FILEPROCESSOR_IMAGE_MODELS=gpt-4o,gpt-4-vision-preview,claude-3-sonnet

# 文件处理模型列表（用逗号分隔）
# 用于处理文档、代码、表格等文本文件
FILEPROCESSOR_FILE_MODELS=gpt-4o,gpt-4-turbo,claude-3-sonnet

# 文件下载配置
FILEPROCESSOR_MAX_FILE_SIZE=52428800  # 50MB (字节)
FILEPROCESSOR_DOWNLOAD_TIMEOUT=30  # 30秒

# API调用超时配置
FILEPROCESSOR_API_TIMEOUT=120  # 120秒

# 支持的文件类型（自动检测，无需配置）
# 图片: .jpg, .jpeg, .png, .gif, .bmp, .webp, .svg, .tiff, .tif, .ico
# 文档: .txt, .md, .pdf, .doc, .docx, .rtf, .csv, .json, .xml, .html, .htm
# 代码: .js, .ts, .py, .java, .cpp, .c, .h, .cs, .php, .rb, .go, .rs, .swift, .kt, .scala, .r, .sql, .sh, .bat, .ps1, .yaml, .yml, .toml, .ini, .cfg, .conf
# 表格: .xlsx, .xls, .ods, .csv, .tsv
# 其他: .log, .properties, .env, .gitignore, .dockerfile, .makefile, .cmake, .gradle

# 如果没有设置专用配置，将使用全局配置
# API_URL=https://api.openai.com/v1/chat/completions
# API_Key=your_global_api_key_here
