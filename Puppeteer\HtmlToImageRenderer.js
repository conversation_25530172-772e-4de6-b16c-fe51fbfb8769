const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const config = require('./puppeteer.config');
const {
    compatibleWaitForTimeout,
    compatibleWaitForFunction,
    compatibleScreenshot,
    getPuppeteerVersion,
    checkPageAPICompatibility,
    safePageSetup,
    smartLaunchBrowser,
    getOptimizedLaunchOptions
} = require('./puppeteer-utils');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../utils/logger.cjs' 
        : '../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

class HtmlToImageRenderer {
    constructor(options = {}) {
        this.browser = null;
        this.resourcesPath = path.join(__dirname, '../resources');
        this.imagePath = path.join(__dirname, '../image');
        this.tempPath = path.join(__dirname, '../temp');
        // 合并配置
        this.config = {
            ...config.launchOptions,
            ...options
        };
        
        // 页面配置
        this.pageConfig = config.pageOptions;
        this.waitConfig = config.waitOptions;
        this.retryConfig = config.retryOptions;
        
        // 确保目录存在
        this.ensureDirectoryExists(this.imagePath);
        this.ensureDirectoryExists(this.tempPath);
    }

    /**
     * 确保目录存在
     * @param {string} dirPath 目录路径
     */
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }

    /**
     * 智能初始化浏览器 - 使用多策略启动
     */
    async initBrowser() {
        if (this.browser) {
            try {
                // 检查浏览器是否仍然可用
                await this.browser.version();
                return this.browser;
            } catch (error) {
                console.warn('现有浏览器实例不可用，重新启动');
                this.browser = null;
            }
        }

        //console.log('正在启动浏览器...');
        
        try {
            // 使用智能启动策略
            this.browser = await smartLaunchBrowser(this.config);
            
            // 显示版本信息
            const versionInfo = getPuppeteerVersion();
            //console.log(`Puppeteer 浏览器启动成功 (版本: ${versionInfo.version})`);
            
            // 监听浏览器事件
            this.browser.on('disconnected', () => {
                //console.log('Puppeteer 浏览器连接断开');
                this.browser = null;
            });

            this.browser.on('targetcreated', (target) => {
                //console.log('新页面创建:', target.type());
            });

            this.browser.on('targetdestroyed', (target) => {
                //console.log('页面销毁:', target.type());
            });

            // 测试浏览器功能
            try {
                const pages = await this.browser.pages();
                //console.log(`浏览器启动完成，当前页面数: ${pages.length}`);
            } catch (testError) {
                //console.warn('浏览器功能测试失败:', testError.message);
            }

            return this.browser;
        } catch (error) {
            //logger.warning('智能浏览器启动失败:', error.message);
            
            // 提供详细的错误信息和解决方案
            this.provideErrorSolution(error);
            
            throw new Error(`浏览器初始化失败: ${error.message}`);
        }
    }

    /**
     * 提供错误解决方案
     * @param {Error} error 错误对象
     */
    provideErrorSolution(error) {
        logger.info('调试日志', '\n=== 浏览器启动问题解决方案 ===');
        
        if (error.message.includes('Could not find Chrome')) {
            logger.info('调试日志', '问题：找不到Chrome浏览器');
            logger.info('调试日志', '解决方案：');
            logger.info('调试日志', '1. 安装Google Chrome: https://www.google.com/chrome/');
            logger.info('调试日志', '2. 或安装Microsoft Edge: https://www.microsoft.com/edge');
            logger.info('调试日志', '3. 重启程序让系统重新检测浏览器路径');
            
        } else if (error.message.includes('net::ERR_')) {
            logger.info('调试日志', '问题：网络连接错误');
            logger.info('调试日志', '解决方案：');
            logger.info('调试日志', '1. 检查网络连接');
            logger.info('调试日志', '2. 关闭防火墙或杀毒软件');
            logger.info('调试日志', '3. 重试操作');
            
        } else if (error.message.includes('Protocol error')) {
            logger.info('调试日志', '问题：浏览器协议错误');
            logger.info('调试日志', '解决方案：');
            logger.info('调试日志', '1. 重启应用程序');
            logger.info('调试日志', '2. 检查浏览器是否被其他程序占用');
            logger.info('调试日志', '3. 尝试以管理员权限运行');
            
        } else if (error.message.includes('Target closed')) {
            logger.info('调试日志', '问题：浏览器页面意外关闭');
            logger.info('调试日志', '解决方案：');
            logger.info('调试日志', '1. 增加等待时间');
            logger.info('调试日志', '2. 检查系统资源占用');
            logger.info('调试日志', '3. 重启程序');
            
        } else {
            logger.info('调试日志', '问题：未知错误');
            logger.info('调试日志', '通用解决方案：');
            logger.info('调试日志', '1. 重新安装puppeteer: npm install puppeteer');
            logger.info('调试日志', '2. 清除npm缓存: npm cache clean --force');
            logger.info('调试日志', '3. 更新Node.js到最新版本');
            logger.info('调试日志', '4. 检查系统权限设置');
        }
        
        logger.info('调试日志', '\n系统信息检查：');
        logger.info('调试日志', `操作系统: ${process.platform}`);
        logger.info('调试日志', `Node.js版本: ${process.version}`);
        logger.info('调试日志', `工作目录: ${process.cwd()}`);
        logger.info('调试日志', '===============================\n');
    }

    /**
     * HTML转义
     * @param {string} text 要转义的文本
     */
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, (m) => map[m]);
    }

    /**
     * 关闭浏览器
     */
    async close() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
            logger.info('调试日志', 'Puppeteer 浏览器已关闭');
        }
    }

    /**
     * 清理旧的图片文件
     * @param {number} maxAge 最大保存时间（毫秒）
     */
    async cleanupOldImages(maxAge = 7 * 24 * 60 * 60 * 1000) { // 默认7天
        try {
            const files = fs.readdirSync(this.imagePath);
            const now = Date.now();

            for (const file of files) {
                const filePath = path.join(this.imagePath, file);
                
                try {
                    const stats = fs.statSync(filePath);
                    
                    // 只处理文件，跳过目录
                    if (stats.isFile() && now - stats.mtime.getTime() > maxAge) {
                        fs.unlinkSync(filePath);
                        logger.info('调试日志', `清理旧图片文件: ${file}`);
                    } else if (stats.isDirectory()) {
                        logger.info('调试日志', `跳过目录: ${file}`);
                    }
                } catch (fileError) {
                    console.warn(`无法处理文件 ${file}:`, fileError.message);
                }
            }
        } catch (error) {
            logger.warning('清理旧图片文件失败:', error);
        }
    }

    /**
     * 生成HTML模板
     * @param {Object} data 模板数据
     * @param {string} data.model AI模型名称
     * @param {string} data.id1 用户QQ号
     * @param {string} data.name 用户昵称
     * @param {string} data.MSG 用户消息
     * @param {string} data.id2 AI的QQ号
     * @param {string} data.name1 AI昵称
     * @param {string} data.CONTENT AI回复内容
     * @param {string} data.theme 主题 (dark/light)
     */
    generateHtml(data) {
        const theme = data.theme || 'light';
        const cssTheme = theme === 'light' ? 'gptx1.css' : 'gptx2.css';
        
        // 定义资源路径
        const resources = {
            dz: `file://${this.resourcesPath}/css/${cssTheme}`,
            atom_one_dark_min_css: `file://${this.resourcesPath}/css/atom-one-dark.min.css`,
            katex_css: `file://${this.resourcesPath}/css/katex.min.css`,
            marked_min_js: `file://${this.resourcesPath}/js/marked.min.js`,
            highlight_min_js: `file://${this.resourcesPath}/js/highlight.min.js`,
            katex_js: `file://${this.resourcesPath}/js/katex.min.js`,
            auto_render_js: `file://${this.resourcesPath}/js/auto-render.min.js`,
            purify_min_js: `file://${this.resourcesPath}/js/purify.min.js`
        };

        const template = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>gptx</title>
    <link rel="stylesheet" href="${resources.dz}">
    <link rel="stylesheet" href="${resources.atom_one_dark_min_css}">
    <link rel="stylesheet" href="${resources.katex_css}">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="model-container">
                <h1>当前模型：<span>${data.model || 'AI助手'}</span></h1>
            </div>            
            <div class="header-bar"></div>
        </div>
        <!-- 用户消息区域 -->
        <div class="user2">
            <div id="title">
                <img src="http://q.qlogo.cn/headimg_dl?dst_uin=${data.id1}&spec=640&img_type=jpg" alt="User 2 Avatar" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDUiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA0NSA0NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjIuNSIgY3k9IjIyLjUiIHI9IjIyLjUiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeD0iNy41IiB5PSI3LjUiPgo8cGF0aCBkPSJNMTUgMTVDMTcuNzYxNCAxNSAyMCAxMi43NjE0IDIwIDEwQzIwIDcuMjM4NTggMTcuNzYxNCA1IDE1IDVDMTIuMjM4NiA1IDEwIDcuMjM4NTggMTAgMTBDMTAgMTIuNzYxNCAxMi4yMzg2IDE1IDE1IDE1WiIgZmlsbD0id2hpdGUiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAyNCAxMCIgZmlsbD0ibm9uZSIgeD0iMyIgeT0iMjAiPgo8cGF0aCBkPSJNMTIgMjBDMTguNjI3NCAyMCAyNCAyMC44OTU0IDI0IDIyVjI1SDAgVjIyQzAgMjAuODk1NCA1LjM3MjU4IDIwIDEyIDIwWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo='">
            </div>
            <div class="name">${data.name || '用户'}</div>
            <div class="message">
                <div id="renderedMarkdown" class="markdown-content">${this.escapeHtml(data.MSG || '用户消息为空')}</div>
            </div>
        </div>

        <!-- AI回复区域 -->
        <div class="user">
            <div id="title">
                <img src="http://q.qlogo.cn/headimg_dl?dst_uin=${data.id2}&spec=640&img_type=jpg" alt="User 1 Avatar" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDUiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA0NSA0NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjIuNSIgY3k9IjIyLjUiIHI9IjIyLjUiIGZpbGw9IiM3NjRiYTIiLz4KPHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeD0iNy41IiB5PSI3LjUiPgo8cGF0aCBkPSJNMTUgMTVDMTcuNzYxNCAxNSAyMCAxMi43NjE0IDIwIDEwQzIwIDcuMjM4NTggMTcuNzYxNCA1IDE1IDVDMTIuMjM4NiA1IDEwIDcuMjM4NTggMTAgMTBDMTAgMTIuNzYxNCAxMi4yMzg2IDE1IDE1IDE1WiIgZmlsbD0id2hpdGUiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAyNCAxMCIgZmlsbD0ibm9uZSIgeD0iMyIgeT0iMjAiPgo8cGF0aCBkPSJNMTIgMjBDMTguNjI3NCAyMCAyNCAyMC44OTU0IDI0IDIyVjI1SDAgVjIyQzAgMjAuODk1NCA1LjM3MjU4IDIwIDEyIDIwWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo='">
            </div>
            <div class="name">${data.name1 || 'AI助手'}</div>
            <div class="message">
                <div id="renderedMarkdown2" class="markdown-content">${this.escapeHtml(data.CONTENT || 'AI回复为空')}</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本库 -->
    <script src="${resources.marked_min_js}" defer></script>
    <script src="${resources.highlight_min_js}" defer></script>
    <script src="${resources.katex_js}" defer></script>
    <script src="${resources.auto_render_js}" defer></script>
    <script src="${resources.purify_min_js}" defer></script>

    <script defer>
        /**
         * 预处理LaTeX公式，仅处理非代码块内容
         * @param {string} content - 原始Markdown内容
         * @returns {string} - 处理后的内容
         */
        const preHandleLaTeX = (content) => {
            if (!content || typeof content !== 'string') {
                return content;
            }

            // 使用更严格的正则匹配代码块
            const codeBlockRegex = /\`\`\`(?:\\w+)?\\n[\\s\\S]*?\\n\`\`\`/g;
            const codeBlocks = [];
            const placeholderPrefix = '__CODE_BLOCK_PLACEHOLDER_';
            let processedContent = content.replace(codeBlockRegex, (match) => {
                codeBlocks.push(match);
                return \`\\n\${placeholderPrefix}\${codeBlocks.length - 1}__\\n\`;
            });

            // 处理 LaTeX，仅针对非代码块部分
            processedContent = processedContent.replace(
                /(^|\\n)\\s*\\$\\$([\\s\\S]*?)\\$\\$\\s*($|\\n)/g,
                (match, start, equation, end) => {
                    return \`\${start}§§\${equation}§§\${end}\`;
                }
            );

            // 处理不同的LaTeX语法
            processedContent = processedContent.replace(
                /\\\\\\[([\\s\\S]*?)\\\\\\]/g,
                (match, equation) => {
                    equation = equation.trim();
                    return '\\n$$' + equation + '$$\\n';
                }
            );

            processedContent = processedContent.replace(
                /\\\\\\(([\\s\\S]*?)\\\\\\)/g,
                (match, equation) => {
                    equation = equation.trim();
                    return '$' + equation + '$';
                }
            );

            // 恢复 $$...$$ 的内容
            processedContent = processedContent.replace(
                /\\$\\$([\\s\\S]*?)\\$\\$/g,
                (match, equation) => {
                    if (!match.includes('§§')) {
                        equation = equation.trim();
                        return '\\n$$' + equation + '$$\\n';
                    }
                    return match;
                }
            );

            // 替换 §§...§§ 为 $$...$$
            processedContent = processedContent.replace(
                /§§([\\s\\S]*?)§§/g,
                (match, equation) => {
                    return '$$' + equation + '$$';
                }
            );

            // 仅处理 \\,，其他 \\ 保持不变
            processedContent = processedContent.replace(
                /\\\\,/g,
                '\\\\,'
            );

            // 合并多个换行符
            processedContent = processedContent.replace(/\\n{3,}/g, '\\n\\n');

            // 恢复代码块
            const finalContent = processedContent.replace(new RegExp(\`\${placeholderPrefix}(\\\\d+)__\`, 'g'), (match, index) => {
                return codeBlocks[index];
            });

            return finalContent;
        };

        /**
         * 为每个代码块添加 data-lang 属性，用于指示代码的语言类型
         */
        const addDataLangAttributes = () => {
            document.querySelectorAll('.markdown-content pre code').forEach(code => {
                const classes = Array.from(code.classList);
                const languageClass = classes.find(cls => cls.startsWith('language-'));
                if (languageClass) {
                    const language = languageClass.replace('language-', '').toUpperCase();
                    const pre = code.parentElement;
                    if (pre) {
                        pre.setAttribute('data-lang', language);
                    }
                }
            });
        };

        /**
         * 处理并渲染Markdown内容，包括高亮代码和数学公式
         */
        const renderMarkdownContent = () => {
            const markdownElements = document.querySelectorAll('.markdown-content');
            markdownElements.forEach(element => {
                try {
                    // 获取原始Markdown文本
                    let markdownText = element.textContent;
                    // 预处理LaTeX公式
                    let processedContent = preHandleLaTeX(markdownText);

                    // 将处理后的内容传递给marked解析器
                    let htmlContent = marked.parse(processedContent);

                    // 使用DOMPurify清理HTML，防止XSS攻击
                    htmlContent = DOMPurify.sanitize(htmlContent);

                    // 创建一个临时容器来操作HTML
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = htmlContent;

                    // 仅对非代码块内的元素进行替换，避免影响代码块
                    // 替换双星号 **text** -> <span class="double-star">text</span>
                    tempDiv.querySelectorAll('strong').forEach(elem => {
                        const span = document.createElement('span');
                        span.className = 'double-star';
                        span.innerHTML = elem.innerHTML;
                        elem.parentNode.replaceChild(span, elem);
                    });

                    // 替换单下划线 _text_ -> <span class="single-underline">text</span>
                    tempDiv.querySelectorAll('em').forEach(elem => {
                        const span = document.createElement('span');
                        span.className = 'single-underline';
                        span.innerHTML = elem.innerHTML;
                        elem.parentNode.replaceChild(span, elem);
                    });

                    // 替换尖括号 <<text>> -> <span class="angle-bracket">text</span>
                    tempDiv.innerHTML = tempDiv.innerHTML.replace(/&lt;&lt;(.*?)&gt;&gt;/g, (match, p1) => {
                        return \`<span class="angle-bracket">\${p1}</span>\`;
                    });

                    // 替换高亮文本 ==text== -> <span class="highlight">text</span>
                    tempDiv.innerHTML = tempDiv.innerHTML.replace(/==(.+?)==/g, (match, p1) => {
                        return \`<span class="highlight">\${p1}</span>\`;
                    });

                    // 替换图片描述 <<ImageDisplayed>>text<< -> <span class="image-text">text</span>
                    tempDiv.innerHTML = tempDiv.innerHTML.replace(/&lt;&lt;ImageDisplayed&gt;&gt;(.*?)&lt;&lt;/g, (match, p1) => {
                        return \`<span class="image-text">\${p1}</span>\`;
                    });

                    // 将处理后的HTML插入元素
                    element.innerHTML = \`<div class="markdown-content-inner">\${tempDiv.innerHTML}</div>\`;
                } catch (error) {
                    console.error('Markdown渲染错误:', error);
                }
            });
        };

        /**
         * 初始化所有功能，包括渲染Markdown、添加data-lang属性、代码高亮和数学公式渲染
         */
        const initialize = () => {
            renderMarkdownContent();
            addDataLangAttributes();

            // 使用 Highlight.js 高亮代码块
            document.querySelectorAll('.markdown-content pre code').forEach((block) => {
                try {
                    hljs.highlightElement(block);
                } catch (error) {
                    console.error('代码高亮错误:', error);
                }
            });

            // 使用 KaTeX 渲染数学公式
            try {
                renderMathInElement(document.body, {
                    delimiters: [
                        { left: "$$", right: "$$", display: true },
                        { left: "$", right: "$", display: false }
                    ],
                    throwOnError: false,
                    strict: false,
                    trust: true,
                    macros: {
                        "\\\\eqref": "\\\\href{#1}{}",   // 处理 \\eqref 命令
                        "\\\\label": "\\\\href{#1}{}"    // 处理 \\label 命令
                    },
                    displayMode: true,
                    output: 'html',
                    maxSize: 500,
                    maxExpand: 1000,
                    fleqn: false
                });
            } catch (error) {
                console.error('数学公式渲染错误:', error);
            }
        };

        // 确保所有资源加载完毕后初始化
        window.addEventListener('DOMContentLoaded', function() {
            var requiredLibs = {
                'marked': 'marked库',
                'hljs': 'highlight.js库',
                'katex': 'KaTeX库',
                'renderMathInElement': 'auto_render库',
                'DOMPurify': 'DOMPurify库'
            };

            var entries = Object.entries(requiredLibs);
            for (var i = 0; i < entries.length; i++) {
                var lib = entries[i][0];
                var name = entries[i][1];
                if (typeof window[lib] === 'undefined') {
                    console.error(name + '未加载');
                    return;
                }
            }

            initialize();
        });
    </script>
</body>
</html>`;

        return template;
    }

    /**
     * 渲染HTML为图片
     * @param {Object} renderData 渲染数据
     * @param {Object} options 渲染选项
     * @param {string} options.format 图片格式 (png|jpeg|webp)
     * @param {number} options.quality 图片质量 (1-100)
     * @param {number} options.width 视口宽度
     * @param {number} options.height 视口高度
     * @param {boolean} options.fullPage 是否截取整个页面
     * @param {string} options.filename 自定义文件名
     * @returns {Promise<string>} 返回图片的本地URL
     */
    async renderToImage(renderData, options = {}) {
        const {
            format = 'png',
            quality = 90,
            width = 800,
            height = 600,
            fullPage = true,
            filename = null
        } = options;

        try {


            // 初始化浏览器
            await this.initBrowser();

            // 生成HTML内容
            const htmlContent = this.generateHtml(renderData);

            // 生成唯一的文件名
            const timestamp = Date.now();
            const hash = crypto.createHash('md5').update(htmlContent).digest('hex').substring(0, 8);
            const imageFilename = filename || `ai_chat_${timestamp}_${hash}.${format}`;
            const imagePath = path.join(this.imagePath, imageFilename);

            // 创建临时HTML文件
            const tempHtmlPath = path.join(this.tempPath, `temp_${timestamp}_${hash}.html`);
            fs.writeFileSync(tempHtmlPath, htmlContent, 'utf8');

            // 创建新页面
            const page = await this.browser.newPage();

            try {
                // 检查页面API兼容性
                const apiCompat = checkPageAPICompatibility(page);
                logger.info('调试日志', '页面API兼容性检查:', apiCompat);

                // 设置视口大小（使用配置）
                const viewport = {
                    ...this.pageConfig.viewport,
                    width,
                    height
                };
                await page.setViewport(viewport);

                // 安全设置页面配置
                const preloadScript = () => {
                    // 禁用图片懒加载
                    const originalCreateElement = document.createElement;
                    document.createElement = function(tagName, options) {
                        const element = originalCreateElement.call(this, tagName, options);
                        if (tagName.toLowerCase() === 'img') {
                            element.loading = 'eager';
                        }
                        return element;
                    };
                    
                    // 字体预加载
                    const link = document.createElement('link');
                    link.rel = 'preload';
                    link.as = 'font';
                    link.type = 'font/woff2';
                    link.crossOrigin = 'anonymous';
                    document.head.appendChild(link);
                };

                await safePageSetup(page, {
                    timeout: this.pageConfig.timeout,
                    preloadScript
                });

                // 加载HTML文件（使用配置）
                await page.goto(`file://${tempHtmlPath}`, this.pageConfig.gotoOptions);

                // 等待内容渲染完成（兼容性方法）
                await compatibleWaitForTimeout(page, this.waitConfig.baseWaitTime);

                // 等待所有脚本执行完成（兼容性方法）
                await compatibleWaitForFunction(page, () => {
                    return typeof marked !== 'undefined' && 
                           typeof hljs !== 'undefined' && 
                           typeof katex !== 'undefined' && 
                           typeof renderMathInElement !== 'undefined' &&
                           typeof DOMPurify !== 'undefined';
                }, { timeout: this.waitConfig.scriptLoadTimeout });

                // 等待DOM更新完成（兼容性方法）
                await compatibleWaitForFunction(page, () => {
                    const markdownElements = document.querySelectorAll('.markdown-content');
                    return markdownElements.length > 0 && 
                           markdownElements[0].querySelector('.markdown-content-inner') !== null;
                }, { timeout: this.waitConfig.domUpdateTimeout });

                // 等待字体加载完成
                try {
                    await page.evaluate(() => {
                        return document.fonts.ready;
                    });
                } catch (fontError) {
                    console.warn('字体加载检查失败，继续渲染:', fontError.message);
                }

                // 额外等待确保所有内容完全渲染
                await compatibleWaitForTimeout(page, 2000);

                // 动态计算页面实际高度
                const pageMetrics = await page.evaluate(() => {
                    const body = document.body;
                    const html = document.documentElement;

                    // 获取页面的实际高度
                    const height = Math.max(
                        body.scrollHeight,
                        body.offsetHeight,
                        html.clientHeight,
                        html.scrollHeight,
                        html.offsetHeight
                    );

                    // 获取页面宽度
                    const width = Math.max(
                        body.scrollWidth,
                        body.offsetWidth,
                        html.clientWidth,
                        html.scrollWidth,
                        html.offsetWidth
                    );

                    return { width, height };
                });

                console.log(`页面实际尺寸: ${pageMetrics.width}x${pageMetrics.height}`);

                // 如果启用fullPage，确保视口高度足够
                if (fullPage && pageMetrics.height > height) {
                    console.log(`调整视口高度: ${height} -> ${pageMetrics.height}`);
                    await page.setViewport({
                        ...viewport,
                        height: Math.max(pageMetrics.height, 800) // 最小高度800px
                    });

                    // 重新等待一下确保调整生效
                    await compatibleWaitForTimeout(page, 1000);
                }

                // 截图选项（使用配置）
                const screenshotOptions = {
                    ...config.screenshotOptions,
                    path: imagePath,
                    type: format,
                    fullPage
                };

                // 如果使用fullPage，添加额外的选项确保完整截图
                if (fullPage) {
                    screenshotOptions.captureBeyondViewport = true;
                    // 注意：不能同时设置clip和fullPage，它们是互斥的
                }

                // 只有jpeg格式才需要quality参数
                if (format === 'jpeg') {
                    screenshotOptions.quality = quality;
                } else {
                    delete screenshotOptions.quality;
                }

                // 截图（带重试机制和兼容性处理）
                let screenshot = null;
                let retryCount = 0;
                let screenshotSuccess = false;

                while (retryCount < this.retryConfig.maxRetries && !screenshotSuccess) {
                    try {
                        screenshot = await compatibleScreenshot(page, screenshotOptions);
                        screenshotSuccess = true;
                        console.log('截图成功');
                        break;
                    } catch (error) {
                        retryCount++;
                        console.log(`截图尝试 ${retryCount} 失败:`, error.message);

                        const shouldRetry = this.retryConfig.retryConditions.some(condition =>
                            error.message.includes(condition));

                        if (shouldRetry && retryCount < this.retryConfig.maxRetries) {
                            logger.info('调试日志', `截图重试 ${retryCount}/${this.retryConfig.maxRetries}: ${error.message}`);
                            await new Promise(resolve => setTimeout(resolve, this.retryConfig.retryDelay));
                        } else if (fullPage && retryCount === this.retryConfig.maxRetries) {
                            // 如果fullPage截图失败，尝试使用clip方式
                            console.log('fullPage截图失败，尝试使用clip方式');
                            const clipOptions = {
                                path: imagePath,
                                type: format,
                                fullPage: false, // 关闭fullPage
                                captureBeyondViewport: true,
                                clip: {
                                    x: 0,
                                    y: 0,
                                    width: Math.min(pageMetrics.width, 1920), // 限制最大宽度
                                    height: Math.min(pageMetrics.height + 100, 4000) // 限制最大高度，添加一些缓冲
                                }
                            };

                            // 添加质量参数（如果需要）
                            if (format === 'jpeg') {
                                clipOptions.quality = quality;
                            }

                            try {
                                screenshot = await compatibleScreenshot(page, clipOptions);
                                screenshotSuccess = true;
                                console.log('clip方式截图成功');
                                break;
                            } catch (clipError) {
                                console.log('clip方式也失败:', clipError.message);
                                throw error; // 抛出原始错误
                            }
                        } else {
                            throw error;
                        }
                    }
                }

                logger.info('调试日志', `AI对话图片生成成功: ${imageFilename}`);

                // 清理临时文件
                if (fs.existsSync(tempHtmlPath)) {
                    fs.unlinkSync(tempHtmlPath);
                }

                // 返回本地可访问的URL
                return `/image/${imageFilename}`;

            } finally {
                // 关闭页面
                await page.close();
            }

        } catch (error) {
            logger.warning('渲染图片失败:', error);
            throw error;
        }
    }
}

module.exports = HtmlToImageRenderer; 