{"manifestVersion": "1.0.0", "name": "DailyNoteEditor", "version": "1.0.0", "displayName": "日记内容编辑器", "description": "允许AI通过VCP调用编辑日记文件中的特定内容。", "author": "<PERSON><PERSON>", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node daily-note-editor.js"}, "communication": {"protocol": "stdio", "timeout": 30000}, "capabilities": {"invocationCommands": [{"commandIdentifier": "EditDailyNote", "tool_name": "DailyNoteEditor", "description": "用于编辑指定角色的日记内容。AI需要提供要查找并替换的旧内容（target）和新的内容（replace）。\n\n安全性检查：\n1. target字段长度不能少于15字符。\n2. 一次调用只能修改一个日记文件中的匹配内容。\n\n请在您的回复中，使用以下精确格式来请求日记编辑，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」DailyNoteEditor「末」,\ntarget:「始」(必需) 日记中需要被查找和替换的旧内容。必须至少包含15个字符。「末」,\nreplace:「始」(必需) 用于替换target的新内容。「末」\n<<<[END_TOOL_REQUEST]>>>", "parameters": [{"name": "target", "description": "日记中需要被查找和替换的旧内容。必须至少包含15个字符。", "required": true, "type": "string"}, {"name": "replace", "description": "用于替换target的新内容。", "required": true, "type": "string"}], "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」DailyNoteEditor「末」,\ntarget:「始」这是日记中要被替换掉的旧内容，至少15个字符长。「末」,\nreplace:「始」这是将要写入日记的新内容。「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}, "configSchema": {"DebugMode": {"type": "boolean", "description": "DebugMode 配置项", "required": false}}}