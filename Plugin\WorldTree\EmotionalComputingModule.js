/**
 * 情感计算模块
 * 
 * 基于情感心理学和计算情感学研究：
 * 1. <PERSON>'s Circumplex Model (罗素环形模型)
 * 2. <PERSON><PERSON><PERSON><PERSON>'s Wheel of Emotions (普拉奇克情绪轮)
 * 3. Affective Computing Theory (情感计算理论)
 * 4. Mood Congruence Effect (心境一致性效应)
 * 5. Emotional Contagion (情绪传染)
 * 6. Cognitive Appraisal Theory (认知评价理论)
 */

const natural = require('natural');
const { Matrix } = require('ml-matrix');

class EmotionalComputingModule {
    constructor(config = {}) {
        this.config = {
            // 情绪衰减参数
            emotionDecayRate: config.emotionDecayRate || 0.95,
            moodStability: config.moodStability || 0.8,
            emotionalSensitivity: config.emotionalSensitivity || 0.7,
            
            // 个性特征 (Big Five)
            openness: config.openness || 0.5,
            conscientiousness: config.conscientiousness || 0.5,
            extraversion: config.extraversion || 0.5,
            agreeableness: config.agreeableness || 0.5,
            neuroticism: config.neuroticism || 0.5,
            
            ...config
        };
        
        // 情绪词典 (基于中文情感词典)
        this.emotionLexicon = {
            positive: ['开心', '快乐', '兴奋', '满意', '愉快', '高兴', '欣喜', '舒适', '放松', '安心'],
            negative: ['难过', '伤心', '愤怒', '焦虑', '担心', '害怕', '沮丧', '失望', '烦躁', '痛苦'],
            neutral: ['平静', '普通', '一般', '正常', '稳定', '平和', '淡然', '冷静', '理性', '客观']
        };
        
        // Plutchik基础情绪
        this.basicEmotions = {
            joy: { valence: 0.8, arousal: 0.6, dominance: 0.7 },
            sadness: { valence: -0.8, arousal: -0.4, dominance: -0.5 },
            anger: { valence: -0.6, arousal: 0.8, dominance: 0.6 },
            fear: { valence: -0.7, arousal: 0.7, dominance: -0.8 },
            surprise: { valence: 0.2, arousal: 0.8, dominance: 0.0 },
            disgust: { valence: -0.7, arousal: 0.3, dominance: 0.2 },
            trust: { valence: 0.6, arousal: -0.2, dominance: 0.3 },
            anticipation: { valence: 0.4, arousal: 0.5, dominance: 0.4 }
        };
        
        // 情绪状态历史
        this.emotionHistory = [];
        this.currentMood = { valence: 0, arousal: 0, dominance: 0 };
    }

    /**
     * 分析文本情感
     * 使用自然语言处理和情感词典
     */
    analyzeTextEmotion(text) {
        if (!text || typeof text !== 'string') {
            return { valence: 0, arousal: 0, dominance: 0, confidence: 0 };
        }
        
        // 分词处理
        const tokens = this.tokenizeText(text);
        
        // 情感词匹配
        const emotionScores = this.calculateEmotionScores(tokens);
        
        // 语义分析
        const semanticEmotion = this.analyzeSemanticEmotion(text);
        
        // 综合情感评估
        const combinedEmotion = this.combineEmotionScores(emotionScores, semanticEmotion);
        
        return combinedEmotion;
    }

    /**
     * 文本分词
     */
    tokenizeText(text) {
        // 简单的中文分词（实际应用中可以使用更高级的分词器）
        return text.split(/[\s，。！？；：、]+/).filter(token => token.length > 0);
    }

    /**
     * 计算情感分数
     */
    calculateEmotionScores(tokens) {
        let positiveScore = 0;
        let negativeScore = 0;
        let neutralScore = 0;
        let totalMatches = 0;
        
        tokens.forEach(token => {
            if (this.emotionLexicon.positive.some(word => token.includes(word))) {
                positiveScore++;
                totalMatches++;
            }
            if (this.emotionLexicon.negative.some(word => token.includes(word))) {
                negativeScore++;
                totalMatches++;
            }
            if (this.emotionLexicon.neutral.some(word => token.includes(word))) {
                neutralScore++;
                totalMatches++;
            }
        });
        
        if (totalMatches === 0) {
            return { valence: 0, arousal: 0, dominance: 0, confidence: 0 };
        }
        
        // 计算情感维度
        const valence = (positiveScore - negativeScore) / totalMatches;
        const arousal = (positiveScore + negativeScore) / totalMatches;
        const dominance = positiveScore / totalMatches;
        const confidence = totalMatches / tokens.length;
        
        return { valence, arousal, dominance, confidence };
    }

    /**
     * 语义情感分析
     */
    analyzeSemanticEmotion(text) {
        // 基于句子结构和语义的情感分析
        const sentenceLength = text.length;
        const questionMarks = (text.match(/[？?]/g) || []).length;
        const exclamationMarks = (text.match(/[！!]/g) || []).length;
        const periods = (text.match(/[。.]/g) || []).length;
        
        // 标点符号影响情感强度
        let arousal = 0;
        if (exclamationMarks > 0) arousal += 0.3;
        if (questionMarks > 0) arousal += 0.2;
        
        // 句子长度影响情感复杂度
        const complexity = Math.min(1, sentenceLength / 100);
        
        return {
            valence: 0,
            arousal: arousal,
            dominance: complexity * 0.2,
            confidence: 0.3
        };
    }

    /**
     * 综合情感分数
     */
    combineEmotionScores(lexiconEmotion, semanticEmotion) {
        const lexiconWeight = lexiconEmotion.confidence;
        const semanticWeight = semanticEmotion.confidence;
        const totalWeight = lexiconWeight + semanticWeight;
        
        if (totalWeight === 0) {
            return { valence: 0, arousal: 0, dominance: 0, confidence: 0 };
        }
        
        return {
            valence: (lexiconEmotion.valence * lexiconWeight + semanticEmotion.valence * semanticWeight) / totalWeight,
            arousal: (lexiconEmotion.arousal * lexiconWeight + semanticEmotion.arousal * semanticWeight) / totalWeight,
            dominance: (lexiconEmotion.dominance * lexiconWeight + semanticEmotion.dominance * semanticWeight) / totalWeight,
            confidence: Math.min(1, totalWeight)
        };
    }

    /**
     * 更新情绪状态
     * 基于情绪传染和认知评价理论
     */
    updateEmotionalState(newEmotion, contextFactors = {}) {
        const timestamp = Date.now();
        
        // 情绪传染效应
        const contagionEffect = this.calculateEmotionContagion(newEmotion, contextFactors);
        
        // 个性调节
        const personalityModulation = this.applyPersonalityModulation(contagionEffect);
        
        // 情绪衰减
        const decayedCurrentMood = this.applyEmotionDecay(this.currentMood);
        
        // 情绪融合
        const updatedMood = this.blendEmotions(decayedCurrentMood, personalityModulation);
        
        // 更新历史记录
        this.emotionHistory.push({
            timestamp,
            emotion: newEmotion,
            mood: { ...updatedMood },
            contextFactors
        });
        
        // 保持历史记录在合理范围内
        if (this.emotionHistory.length > 100) {
            this.emotionHistory = this.emotionHistory.slice(-50);
        }
        
        this.currentMood = updatedMood;
        
        return this.currentMood;
    }

    /**
     * 计算情绪传染效应
     */
    calculateEmotionContagion(newEmotion, contextFactors) {
        const socialContext = contextFactors.socialInteraction || 0.5;
        const empathy = this.config.agreeableness; // 宜人性影响共情能力
        const emotionalSensitivity = this.config.emotionalSensitivity;
        
        // 情绪传染强度
        const contagionStrength = socialContext * empathy * emotionalSensitivity;
        
        return {
            valence: newEmotion.valence * contagionStrength,
            arousal: newEmotion.arousal * contagionStrength,
            dominance: newEmotion.dominance * contagionStrength
        };
    }

    /**
     * 应用个性调节
     * 基于大五人格模型
     */
    applyPersonalityModulation(emotion) {
        const { openness, conscientiousness, extraversion, agreeableness, neuroticism } = this.config;
        
        // 外向性影响正面情绪的强度
        const extraversionEffect = extraversion * 0.3;
        
        // 神经质影响负面情绪的强度
        const neuroticismEffect = neuroticism * 0.4;
        
        // 开放性影响情绪的复杂性
        const opennessEffect = openness * 0.2;
        
        return {
            valence: emotion.valence * (1 + (emotion.valence > 0 ? extraversionEffect : neuroticismEffect)),
            arousal: emotion.arousal * (1 + opennessEffect),
            dominance: emotion.dominance * (1 + conscientiousness * 0.2)
        };
    }

    /**
     * 应用情绪衰减
     */
    applyEmotionDecay(currentMood) {
        const decayRate = this.config.emotionDecayRate;
        const stability = this.config.moodStability;
        
        return {
            valence: currentMood.valence * decayRate * stability,
            arousal: currentMood.arousal * decayRate,
            dominance: currentMood.dominance * decayRate * stability
        };
    }

    /**
     * 情绪融合
     */
    blendEmotions(currentMood, newEmotion) {
        const currentWeight = 0.7; // 当前情绪的权重
        const newWeight = 0.3; // 新情绪的权重
        
        return {
            valence: currentMood.valence * currentWeight + newEmotion.valence * newWeight,
            arousal: currentMood.arousal * currentWeight + newEmotion.arousal * newWeight,
            dominance: currentMood.dominance * currentWeight + newEmotion.dominance * newWeight
        };
    }

    /**
     * 获取当前情绪状态
     */
    getCurrentEmotionalState() {
        const mood = this.currentMood;
        
        // 将VAD空间映射到具体情绪
        const primaryEmotion = this.mapVADToEmotion(mood);
        
        // 计算情绪强度
        const intensity = Math.sqrt(mood.valence ** 2 + mood.arousal ** 2 + mood.dominance ** 2) / Math.sqrt(3);
        
        // 情绪稳定性
        const stability = this.calculateEmotionalStability();
        
        return {
            mood,
            primaryEmotion,
            intensity,
            stability,
            history: this.getRecentEmotionTrend()
        };
    }

    /**
     * 将VAD空间映射到具体情绪
     */
    mapVADToEmotion(vad) {
        let closestEmotion = 'neutral';
        let minDistance = Infinity;
        
        for (const [emotion, emotionVAD] of Object.entries(this.basicEmotions)) {
            const distance = Math.sqrt(
                (vad.valence - emotionVAD.valence) ** 2 +
                (vad.arousal - emotionVAD.arousal) ** 2 +
                (vad.dominance - emotionVAD.dominance) ** 2
            );
            
            if (distance < minDistance) {
                minDistance = distance;
                closestEmotion = emotion;
            }
        }
        
        return {
            name: closestEmotion,
            confidence: Math.max(0, 1 - minDistance / 2),
            vad: this.basicEmotions[closestEmotion]
        };
    }

    /**
     * 计算情绪稳定性
     */
    calculateEmotionalStability() {
        if (this.emotionHistory.length < 5) {
            return 0.5; // 默认稳定性
        }
        
        const recentEmotions = this.emotionHistory.slice(-10);
        const valenceVariance = this.calculateVariance(recentEmotions.map(e => e.mood.valence));
        const arousalVariance = this.calculateVariance(recentEmotions.map(e => e.mood.arousal));
        
        // 方差越小，稳定性越高
        const stability = 1 - Math.min(1, (valenceVariance + arousalVariance) / 2);
        return stability;
    }

    /**
     * 计算方差
     */
    calculateVariance(values) {
        if (values.length === 0) return 0;
        
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const variance = values.reduce((sum, val) => sum + (val - mean) ** 2, 0) / values.length;
        return variance;
    }

    /**
     * 获取最近情绪趋势
     */
    getRecentEmotionTrend() {
        if (this.emotionHistory.length < 3) {
            return { trend: 'stable', direction: 0 };
        }
        
        const recent = this.emotionHistory.slice(-5);
        const valences = recent.map(e => e.mood.valence);
        
        // 简单线性趋势分析
        let trend = 0;
        for (let i = 1; i < valences.length; i++) {
            trend += valences[i] - valences[i - 1];
        }
        
        trend /= (valences.length - 1);
        
        let trendLabel = 'stable';
        if (trend > 0.1) trendLabel = 'improving';
        else if (trend < -0.1) trendLabel = 'declining';
        
        return { trend: trendLabel, direction: trend };
    }

    /**
     * 预测情绪变化
     */
    predictEmotionalChange(futureContextFactors = {}) {
        const currentState = this.getCurrentEmotionalState();
        const trend = currentState.history.direction;
        
        // 基于当前趋势和未来情境预测
        const predictedValence = this.currentMood.valence + trend * 0.5;
        const contextInfluence = futureContextFactors.expectedPositivity || 0;
        
        return {
            predictedMood: {
                valence: Math.max(-1, Math.min(1, predictedValence + contextInfluence)),
                arousal: this.currentMood.arousal * 0.9, // 唤醒度逐渐衰减
                dominance: this.currentMood.dominance * 0.95
            },
            confidence: Math.max(0.3, currentState.stability)
        };
    }

    /**
     * 获取情绪对认知的影响
     * 基于心境一致性效应
     */
    getEmotionalCognitiveEffects() {
        const mood = this.currentMood;
        const intensity = Math.abs(mood.valence) + Math.abs(mood.arousal);
        
        return {
            // 注意力影响
            attentionFocus: mood.arousal > 0 ? 1 + mood.arousal * 0.2 : 1 + mood.arousal * 0.1,
            
            // 记忆影响
            memoryBias: mood.valence, // 正面情绪促进正面记忆
            
            // 决策影响
            riskTolerance: mood.dominance + mood.valence * 0.3,
            
            // 创造力影响
            creativity: mood.valence > 0 ? 1 + mood.valence * 0.3 : 1 + mood.valence * 0.1,
            
            // 社交倾向
            socialMotivation: mood.valence * 0.5 + this.config.extraversion * 0.5,
            
            // 整体认知效率
            cognitiveEfficiency: 1 - intensity * 0.2 // 强烈情绪可能干扰认知
        };
    }

    /**
     * 重置情绪状态
     */
    resetEmotionalState() {
        this.currentMood = { valence: 0, arousal: 0, dominance: 0 };
        this.emotionHistory = [];
    }
}

module.exports = EmotionalComputingModule;
