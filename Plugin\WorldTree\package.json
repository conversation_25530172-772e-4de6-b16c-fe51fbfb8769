{"name": "worldtree-vcp-plugin", "version": "1.0.0", "description": "世界树VCP插件 - 时间架构与角色心理活动系统", "main": "WorldTreeVCP.js", "scripts": {"test": "node test.js", "quick-start": "node quick-start.js", "validate": "node -c WorldTreeVCP.js && echo '✅ 语法检查通过'", "example": "node -e \"console.log('示例配置:'); console.log(JSON.stringify(require('./examples.json').examples['魔法图书管理员'], null, 2));\""}, "keywords": ["vcp", "worldtree", "psychology", "character", "narrative", "ai", "chatbot", "plugin"], "author": "VCPToolBox", "license": "MIT", "dependencies": {"sqlite3": "^5.1.7"}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/VCPToolBox/VCPToolBox.git", "directory": "Plugin/WorldTree"}, "bugs": {"url": "https://github.com/VCPToolBox/VCPToolBox/issues"}, "homepage": "https://github.com/VCPToolBox/VCPToolBox#readme", "plugin": {"type": "vcp", "category": "psychology", "version": "1.0.0", "compatibility": {"vcptoolbox": ">=1.0.0"}, "features": ["时间架构管理", "角色日程表配置", "心理状态算法计算", "心理活动内容生成", "系统消息注入", "本地算法优先", "数据库集成"], "requirements": {"database": "AdvancedMemorySystem", "permissions": ["database_read", "database_write", "agent_access", "system_message_injection"]}}, "files": ["WorldTreeVCP.js", "plugin-manifest.json", "config.env.example", "README.md", "examples.json", "test.js", "quick-start.js"]}