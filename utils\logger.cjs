// utils/logger.cjs - 现代化中文日志系统 (CommonJS 版本)
const chalk = require('chalk');

class VCPLogger {
    constructor() {
        this.isDevelopment = process.env.NODE_ENV === 'development';
        this.debugMode = process.env.DebugMode === 'true' || process.env.DebugMode === true;
        
        // 配置样式
        this.styles = {
            info: chalk.cyan,
            success: chalk.green,
            warning: chalk.yellow,
            error: chalk.red,
            debug: chalk.magenta,
            system: chalk.blue,
            plugin: chalk.cyan,
            server: chalk.green,
            websocket: chalk.yellow,
            admin: chalk.magenta,
            image: chalk.blue,
            tool: chalk.cyan,
            memory: chalk.green,
            request: chalk.gray,
            response: chalk.white
        };
        
// 配置图标 (兼容 CMD 显示)
this.icons = {
    info: '[i]',
    success: '[+]',
    warning: '[!]',
    error: '[x]',
    debug: '[*]',
    system: '[#]',
    plugin: '[P]',
    server: '[S]',
    websocket: '[~]',
    admin: '[A]',
    image: '[IMG]',
    tool: '[T]',
    memory: '[M]',
    request: '[>>]',
    response: '[<<]'
};

    }

    formatTimestamp() {
        const now = new Date();
        return chalk.gray(`[${now.toLocaleString('zh-CN')}]`);
    }

    formatComponent(component) {
        return chalk.bold(`[${component}]`);
    }

    log(level, component, message, data = null) {
        if (level === 'debug' && !this.debugMode) return;
        
        const timestamp = this.formatTimestamp();
        const icon = this.icons[level] || '💬';
        const styledComponent = this.styles[level] ? 
            this.styles[level](this.formatComponent(component)) : 
            this.formatComponent(component);
        const styledMessage = this.styles[level] ? 
            this.styles[level](message) : 
            message;

        let output = `${timestamp} ${icon} ${styledComponent} ${styledMessage}`;
        
        if (data) {
            output += '\n' + chalk.gray(typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
        }
        
        console.log(output);
    }

    // 便捷方法
    info(component, message, data) {
        this.log('info', component, message, data);
    }

    success(component, message, data) {
        this.log('success', component, message, data);
    }

    warning(component, message, data) {
        this.log('warning', component, message, data);
    }

    error(component, message, data) {
        this.log('error', component, message, data);
    }

    debug(component, message, data) {
        this.log('debug', component, message, data);
    }

    system(message, data) {
        this.log('system', '系统', message, data);
    }

    plugin(pluginName, message, data) {
        this.log('plugin', `插件-${pluginName}`, message, data);
    }

    server(message, data) {
        this.log('server', 'VCP服务器', message, data);
    }

    websocket(message, data) {
        this.log('websocket', 'WebSocket', message, data);
    }

    admin(message, data) {
        this.log('admin', '管理面板', message, data);
    }

    image(message, data) {
        this.log('image', '图片服务', message, data);
    }

    tool(toolName, message, data) {
        this.log('tool', `工具-${toolName}`, message, data);
    }

    memory(message, data) {
        this.log('memory', '记忆系统', message, data);
    }

    request(method, path, message, data) {
        this.log('request', `请求-${method.toUpperCase()}`, `${path} ${message}`, data);
    }

    response(status, message, data) {
        const level = status >= 400 ? 'error' : status >= 300 ? 'warning' : 'success';
        this.log(level, `响应-${status}`, message, data);
    }

    // 启动横幅
    banner() {
        const banner = chalk.cyan.bold(`
╔══════════════════════════════════════════════════════════════╗
║                        MCP-VCP 后端工具箱                    ║                  ║
║                              测试版                          ║
╚══════════════════════════════════════════════════════════════╝`);
        console.log(banner);
        this.server('VCP 工具箱已成功启动!');
    }

    // 分隔线
    separator(title = '') {
        const line = '─'.repeat(60);
        if (title) {
            console.log(chalk.gray(`\n${line} ${chalk.white.bold(title)} ${line}\n`));
        } else {
            console.log(chalk.gray(`\n${line}\n`));
        }
    }
}

// 创建全局日志实例
const logger = new VCPLogger();

module.exports = logger; 