const fs = require('fs').promises;
const path = require('path');


// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

// --- Configuration ---
const DEBUG_MODE = (process.env.DebugMode || "false").toLowerCase() === "true";
const projectBasePath = process.env.PROJECT_BASE_PATH;
const dailyNoteRootPath = projectBasePath ? path.join(projectBasePath, 'dailynote') : path.join(__dirname, '..', '..', 'dailynote'); // Fallback

// --- Debug Logging (to stderr) ---
function debugLog(message, ...args) {
    if (DEBUG_MODE) {
        logger.debug('日记写入', message, ...args);
    }
}

// --- Output Function (to stdout) ---
function sendOutput(data) {
    try {
        const jsonString = JSON.stringify(data);
        process.stdout.write(jsonString);
        debugLog('Sent output to stdout:', jsonString);
    } catch (e) {
        // Fallback for stringification errors
        logger.error('日记写入', '字符串化输出时出错', e);
        process.stdout.write(JSON.stringify({ status: "error", message: "Internal error: 失败： stringify output." }));
    }
}

// --- Core Diary Writing Logic ---
async function writeDiary(maidName, dateString, contentText) {
    debugLog(`Processing diary write for Maid: ${maidName}, Date: ${dateString}`);
    if (!maidName || !dateString || !contentText) {
        throw new Error('无效的 input: Missing Maid, Date, or Content.');
    }

    let folderName = maidName;
    let actualMaidName = maidName;
    // Use regex to find [tag]name format
    const tagMatch = maidName.match(/^\[(.*?)\](.*)$/);

    if (tagMatch) {
        folderName = tagMatch[1].trim(); // Use the captured tag as folder name
        actualMaidName = tagMatch[2].trim(); // Use the captured name as actual maid name
        debugLog(`Tagged note detected. Tag: ${folderName}, Actual Maid: ${actualMaidName}`);
    } else {
        debugLog(`No tag detected. Folder: ${folderName}, Actual Maid: ${actualMaidName}`);
    }

    const datePart = dateString.replace(/[.\\\/\s-]/g, '-').replace(/-+/g, '-');
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    const timeStringForFile = `${hours}_${minutes}_${seconds}`;

    const dirPath = path.join(dailyNoteRootPath, folderName);
    const baseFileNameWithoutExt = `${datePart}-${timeStringForFile}`;
    const fileExtension = '.txt';
    const finalFileName = `${baseFileNameWithoutExt}${fileExtension}`;
    const filePath = path.join(dirPath, finalFileName);

    debugLog(`Target file path: ${filePath}`);

    await fs.mkdir(dirPath, { recursive: true });
    const fileContent = `[${datePart}] - ${actualMaidName}\n${contentText}`;
    await fs.writeFile(filePath, fileContent);
    debugLog(`成功写入 file (length: ${fileContent.length})`);
    return filePath; // Return the path on success
}

// --- Main Execution ---
async function main() {
    let inputData = '';
    process.stdin.setEncoding('utf8');

    process.stdin.on('readable', () => {
        let chunk;
        while ((chunk = process.stdin.read()) !== null) {
            inputData += chunk;
        }
    });

    process.stdin.on('end', async () => {
        debugLog('Received stdin data:', inputData);
        try {
            if (!inputData) {
                throw new Error("No input data received via stdin.");
            }
            const diaryData = JSON.parse(inputData);
            const { maidName, dateString, contentText } = diaryData;

            const savedFilePath = await writeDiary(maidName, dateString, contentText);
            sendOutput({ status: "success", message: `Diary saved to ${savedFilePath}` });

        } catch (error) {
            logger.error('日记写入', '处理请求时出错', error.message);
            sendOutput({ status: "error", message: error.message || "An unknown error occurred." });
            process.exitCode = 1; // Indicate failure
        }
    });

     process.stdin.on('error', (err) => {
         logger.error('日记写入', 'Stdin错误', err);
         sendOutput({ status: "error", message: "读取时出错 input." });
         process.exitCode = 1; // Indicate failure
     });
}

main();
