/**
 * Puppeteer配置文件 - 适配最新版本
 */

module.exports = {
    // 浏览器启动选项
    launchOptions: {
        headless: 'new', // 使用新的无头模式
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--font-render-hinting=none', // 改善字体渲染
            '--disable-web-security', // 允许加载本地文件
            '--allow-file-access-from-files' // 允许访问本地文件
        ],
        ignoreHTTPSErrors: true,
        defaultViewport: null // 使用页面级别的视口设置
    },

    // 页面配置
    pageOptions: {
        viewport: {
            width: 800,
            height: 600,
            deviceScaleFactor: 2, // 提高渲染质量
            isMobile: false
        },
        timeout: 30000, // 页面操作超时时间
        gotoOptions: {
            waitUntil: 'networkidle0',
            timeout: 30000
        }
    },

    // 等待配置
    waitOptions: {
        baseWaitTime: 1000, // 基础等待时间
        scriptLoadTimeout: 10000, // 脚本加载超时时间
        domUpdateTimeout: 5000 // DOM更新超时时间
    },

    // 重试配置
    retryOptions: {
        maxRetries: 3, // 最大重试次数
        retryDelay: 1000, // 重试间隔时间
        retryConditions: [ // 需要重试的错误条件
            'net::ERR_CONNECTION_REFUSED',
            'net::ERR_CONNECTION_RESET',
            'net::ERR_NETWORK_CHANGED',
            'Target closed',
            'Session closed',
            'Protocol error'
        ]
    },

    // 截图配置
    screenshotOptions: {
        type: 'png',
        quality: 90, // 仅对 jpeg 格式有效
        fullPage: true,
        captureBeyondViewport: true,
        encoding: 'binary',
        omitBackground: false
    },

    // 清理配置
    cleanupOptions: {
        // 清理间隔（毫秒）
        cleanupInterval: 24 * 60 * 60 * 1000, // 24小时
        
        // 文件保留时间（毫秒）
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
        
        // 自动清理旧图片
        autoCleanup: true
    },

    // 调试配置
    debugOptions: {
        // 是否启用调试
        enabled: false,
        
        // 调试截图
        debugScreenshots: false,
        
        // 保存HTML文件用于调试
        saveHtml: false,
        
        // 控制台日志
        consoleLog: false
    }
}; 