/**
 * 沙盒世界Web管理界面
 * 提供实时监控和控制功能
 */

const express = require('express');
const http = require('http');
const path = require('path');

// 尝试加载socket.io，如果失败则提供降级方案
let socketIo;
try {
    socketIo = require('socket.io');
} catch (error) {
    console.warn('⚠️ socket.io未安装，WebSocket功能将被禁用');
    socketIo = null;
}

class WebInterface {
    constructor(worldCore, config = {}) {
        this.worldCore = worldCore;
        this.config = {
            port: config.port || 8080,
            updateInterval: config.updateInterval || 5000,
            theme: config.theme || 'dark',
            ...config
        };

        this.app = express();
        this.server = http.createServer(this.app);

        // 只有在socket.io可用时才初始化
        if (socketIo) {
            this.io = socketIo(this.server);
            this.connectedClients = new Set();
        } else {
            this.io = null;
            this.connectedClients = null;
        }

        this.updateInterval = null;

        this.setupRoutes();
        if (this.io) {
            this.setupSocketHandlers();
            this.setupEventListeners();
        }
    }

    /**
     * 设置路由
     */
    setupRoutes() {
        // 静态文件服务
        this.app.use(express.static(path.join(__dirname, 'public')));
        this.app.use(express.json());

        // 主页
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });

        // API路由
        this.app.get('/api/world/status', (req, res) => {
            res.json(this.worldCore.getWorldState());
        });

        this.app.get('/api/agents', (req, res) => {
            const agents = this.worldCore.agentEcosystem.getAllAgents();
            res.json(agents);
        });

        this.app.get('/api/agents/:id', (req, res) => {
            const agent = this.worldCore.agentEcosystem.getAgent(req.params.id);
            if (agent) {
                res.json(agent);
            } else {
                res.status(404).json({ error: 'Agent not found' });
            }
        });

        this.app.get('/api/agent-templates', (req, res) => {
            const templates = Array.from(this.worldCore.agentEcosystem.agentTemplates.values());
            res.json(templates);
        });

        this.app.get('/api/relationships', (req, res) => {
            const relationships = [];
            for (const [agentId, agentRelationships] of this.worldCore.socialNetwork.relationships.entries()) {
                for (const [targetId, relationship] of agentRelationships.entries()) {
                    relationships.push({
                        from: agentId,
                        to: targetId,
                        ...relationship
                    });
                }
            }
            res.json(relationships);
        });

        this.app.get('/api/locations', (req, res) => {
            const locations = this.worldCore.worldEnvironment.getAllLocations();
            res.json(locations);
        });

        this.app.get('/api/events/active', (req, res) => {
            const events = this.worldCore.eventSystem.getActiveEvents();
            res.json(events);
        });

        this.app.get('/api/events/history', (req, res) => {
            const limit = parseInt(req.query.limit) || 50;
            const events = this.worldCore.eventSystem.getEventHistory(limit);
            res.json(events);
        });

        this.app.get('/api/conversations', (req, res) => {
            const conversations = this.worldCore.autonomousDialogue.getActiveConversations();
            res.json(conversations);
        });

        this.app.get('/api/statistics', async (req, res) => {
            const stats = await this.worldCore.getStatistics();
            res.json(stats);
        });

        // 控制API
        this.app.post('/api/world/start', async (req, res) => {
            try {
                await this.worldCore.startWorld();
                res.json({ success: true, message: '世界已启动' });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        this.app.post('/api/world/pause', async (req, res) => {
            try {
                await this.worldCore.pauseWorld();
                res.json({ success: true, message: '世界已暂停' });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        this.app.post('/api/world/resume', async (req, res) => {
            try {
                await this.worldCore.resumeWorld();
                res.json({ success: true, message: '世界已恢复' });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        this.app.post('/api/world/stop', async (req, res) => {
            try {
                await this.worldCore.stopWorld();
                res.json({ success: true, message: '世界已停止' });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        this.app.post('/api/world/timespeed', (req, res) => {
            try {
                const { speed } = req.body;
                this.worldCore.setTimeSpeed(speed);
                res.json({ success: true, message: `时间流速设置为 ${speed}x` });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        this.app.post('/api/agents', async (req, res) => {
            try {
                const agent = await this.worldCore.addAgent(req.body);
                res.json({ success: true, agent });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        this.app.delete('/api/agents/:id', async (req, res) => {
            try {
                await this.worldCore.removeAgent(req.params.id);
                res.json({ success: true, message: 'Agent已移除' });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        this.app.post('/api/events/trigger', async (req, res) => {
            try {
                const { eventType, context } = req.body;
                const success = await this.worldCore.triggerEvent(eventType, context);
                res.json({ success, message: success ? '事件已触发' : '事件触发失败' });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
    }

    /**
     * 设置Socket处理器
     */
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            console.log('🌐 客户端连接:', socket.id);
            this.connectedClients.add(socket);

            // 发送初始数据
            this.sendInitialData(socket);

            socket.on('disconnect', () => {
                console.log('🌐 客户端断开:', socket.id);
                this.connectedClients.delete(socket);
            });

            // 处理客户端请求
            socket.on('requestUpdate', () => {
                this.sendUpdateData(socket);
            });

            socket.on('worldControl', async (action) => {
                try {
                    switch (action.type) {
                        case 'start':
                            await this.worldCore.startWorld();
                            break;
                        case 'pause':
                            await this.worldCore.pauseWorld();
                            break;
                        case 'resume':
                            await this.worldCore.resumeWorld();
                            break;
                        case 'stop':
                            await this.worldCore.stopWorld();
                            break;
                        case 'setTimeSpeed':
                            this.worldCore.setTimeSpeed(action.speed);
                            break;
                    }
                    socket.emit('actionResult', { success: true, action });
                } catch (error) {
                    socket.emit('actionResult', { success: false, error: error.message, action });
                }
            });
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听世界事件
        this.worldCore.on('worldStarted', () => {
            this.broadcast('worldEvent', { type: 'started', timestamp: new Date() });
        });

        this.worldCore.on('worldPaused', () => {
            this.broadcast('worldEvent', { type: 'paused', timestamp: new Date() });
        });

        this.worldCore.on('worldResumed', () => {
            this.broadcast('worldEvent', { type: 'resumed', timestamp: new Date() });
        });

        this.worldCore.on('worldStopped', () => {
            this.broadcast('worldEvent', { type: 'stopped', timestamp: new Date() });
        });

        // 监听Agent事件
        this.worldCore.agentEcosystem.on('agentCreated', (agent) => {
            this.broadcast('agentEvent', { type: 'created', agent });
        });

        this.worldCore.agentEcosystem.on('agentRemoved', (agentId) => {
            this.broadcast('agentEvent', { type: 'removed', agentId });
        });

        // 监听对话事件
        this.worldCore.autonomousDialogue.on('dialogueInitiated', (conversation) => {
            this.broadcast('dialogueEvent', { type: 'initiated', conversation });
        });

        this.worldCore.autonomousDialogue.on('dialogueEnded', (conversation) => {
            this.broadcast('dialogueEvent', { type: 'ended', conversation });
        });

        // 监听事件系统
        this.worldCore.eventSystem.on('eventTriggered', (event) => {
            this.broadcast('worldEvent', { type: 'eventTriggered', event });
        });

        // 监听环境变化
        this.worldCore.worldEnvironment.on('weatherChanged', (data) => {
            this.broadcast('environmentEvent', { type: 'weatherChanged', data });
        });
    }

    /**
     * 发送初始数据
     */
    async sendInitialData(socket) {
        try {
            const data = {
                worldState: this.worldCore.getWorldState(),
                agents: this.worldCore.agentEcosystem.getAllAgents(),
                locations: this.worldCore.worldEnvironment.getAllLocations(),
                activeEvents: this.worldCore.eventSystem.getActiveEvents(),
                conversations: this.worldCore.autonomousDialogue.getActiveConversations(),
                statistics: await this.worldCore.getStatistics()
            };

            socket.emit('initialData', data);
        } catch (error) {
            console.error('发送初始数据失败:', error);
        }
    }

    /**
     * 发送更新数据
     */
    async sendUpdateData(socket) {
        try {
            const data = {
                worldState: this.worldCore.getWorldState(),
                agents: this.worldCore.agentEcosystem.getAllAgents(),
                activeEvents: this.worldCore.eventSystem.getActiveEvents(),
                conversations: this.worldCore.autonomousDialogue.getActiveConversations(),
                statistics: await this.worldCore.getStatistics(),
                timestamp: new Date()
            };

            socket.emit('updateData', data);
        } catch (error) {
            console.error('发送更新数据失败:', error);
        }
    }

    /**
     * 广播消息
     */
    broadcast(event, data) {
        if (this.io) {
            this.io.emit(event, data);
        }
    }

    /**
     * 启动Web界面
     */
    async start() {
        return new Promise((resolve, reject) => {
            this.server.listen(this.config.port, (error) => {
                if (error) {
                    reject(error);
                } else {
                    console.log(`🌐 沙盒世界Web界面启动在端口 ${this.config.port}`);
                    
                    // 启动定期更新
                    this.updateInterval = setInterval(() => {
                        this.broadcastUpdates();
                    }, this.config.updateInterval);
                    
                    resolve();
                }
            });
        });
    }

    /**
     * 广播更新
     */
    async broadcastUpdates() {
        if (this.connectedClients.size === 0) return;

        try {
            const updateData = {
                worldState: this.worldCore.getWorldState(),
                agentCount: this.worldCore.agentEcosystem.getAllAgents().length,
                activeEventCount: this.worldCore.eventSystem.getActiveEvents().length,
                activeConversationCount: this.worldCore.autonomousDialogue.getActiveConversations().length,
                timestamp: new Date()
            };

            this.broadcast('periodicUpdate', updateData);
        } catch (error) {
            console.error('广播更新失败:', error);
        }
    }

    /**
     * 停止Web界面
     */
    async stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        return new Promise((resolve) => {
            this.server.close(() => {
                console.log('🌐 沙盒世界Web界面已停止');
                resolve();
            });
        });
    }
}

module.exports = { WebInterface };
