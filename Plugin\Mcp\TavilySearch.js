// Plugin/Mcp/TavilySearch.js - 网络搜索MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}


class TavilySearchMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'TavilySearch';
        this.description = '使用Tavily搜索引擎进行网络搜索';
        this.vcpName = 'TavilySearch';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: '搜索查询词'
                },
                max_results: {
                    type: 'number',
                    description: '最大搜索结果数量',
                    default: 5,
                    minimum: 1,
                    maximum: 20
                },
                search_depth: {
                    type: 'string',
                    description: '搜索深度，可选值：basic或comprehensive',
                    enum: ['basic', 'comprehensive'],
                    default: 'basic'
                },
                include_domains: {
                    type: 'array',
                    description: '限制搜索的域名列表',
                    items: {
                        type: 'string'
                    }
                },
                exclude_domains: {
                    type: 'array',
                    description: '排除的域名列表',
                    items: {
                        type: 'string'
                    }
                }
            },
            required: ['query']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);
        
        this.log('info', `开始网络搜索`, {
            query: args.query,
            max_results: args.max_results || 5,
            search_depth: args.search_depth || 'basic'
        });
        
        try {
        // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);
            
            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }
            
            // 构建标准响应格式
            const response = {
                type: 'web_search',
                status: 'success',
                message: '搜索完成',
                data: {
                    query: args.query,
                    results: parsedResult?.results || [],
                    results_count: parsedResult?.results?.length || 0,
                    search_depth: args.search_depth || 'basic',
                    search_id: parsedResult?.search_id,
                    total_tokens: parsedResult?.total_tokens,
                    total_cost: parsedResult?.total_cost
                }
            };
            
            this.log('success', `搜索完成`, {
                query: args.query,
                results_count: response.data.results_count
            });
            
            return response;
            
        } catch (error) {
            const errorResponse = {
                type: 'web_search',
                status: 'error',
                message: error.message,
                data: {
            query: args.query,
                    error: error.message
                }
            };
            
            this.log('error', `搜索失败`, errorResponse);
            throw errorResponse;
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查API密钥是否配置
            if (!process.env.TAVILY_API_KEY) {
                throw new Error('未配置TAVILY_API_KEY环境变量');
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = TavilySearchMcp; 