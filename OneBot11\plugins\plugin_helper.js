/**
 * Plugin Helper Library
 * 插件开发辅助库 - 提供常用的插件开发工具函数和类
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * 插件开发辅助类
 * 提供常用的工具函数，简化插件开发
 */
class PluginHelper {
    constructor() {
        this.downloadDir = path.join(__dirname, '..', 'downloads');
        this.configDir = path.join(__dirname, '..');
        this.tempDir = path.join(__dirname, '..', 'temp');
        
        // 确保目录存在
        this.ensureDir(this.downloadDir);
        this.ensureDir(this.tempDir);
    }

    /**
     * 确保目录存在
     * @param {string} dirPath 目录路径
     */
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }

    /**
     * 生成唯一ID
     * @param {number} length ID长度
     * @returns {string} 唯一ID
     */
    generateId(length = 8) {
        return crypto.randomBytes(length).toString('hex').substring(0, length);
    }

    /**
     * 获取当前时间戳
     * @returns {number} 时间戳
     */
    getTimestamp() {
        return Date.now();
    }

    /**
     * 格式化时间
     * @param {Date|number} date 日期对象或时间戳
     * @param {string} format 格式字符串
     * @returns {string} 格式化后的时间
     */
    formatTime(date = new Date(), format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hour = String(d.getHours()).padStart(2, '0');
        const minute = String(d.getMinutes()).padStart(2, '0');
        const second = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hour)
            .replace('mm', minute)
            .replace('ss', second);
    }

    /**
     * 延迟执行
     * @param {number} ms 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 安全的JSON解析
     * @param {string} jsonString JSON字符串
     * @param {*} defaultValue 默认值
     * @returns {*} 解析结果或默认值
     */
    safeJsonParse(jsonString, defaultValue = null) {
        try {
            return JSON.parse(jsonString);
        } catch (error) {
            return defaultValue;
        }
    }

    /**
     * 读取文件内容
     * @param {string} filePath 文件路径
     * @param {string} encoding 编码格式
     * @returns {string|null} 文件内容或null
     */
    readFile(filePath, encoding = 'utf8') {
        try {
            return fs.readFileSync(filePath, encoding);
        } catch (error) {
            return null;
        }
    }

    /**
     * 写入文件内容
     * @param {string} filePath 文件路径
     * @param {string} content 文件内容
     * @param {string} encoding 编码格式
     * @returns {boolean} 是否成功
     */
    writeFile(filePath, content, encoding = 'utf8') {
        try {
            fs.writeFileSync(filePath, content, encoding);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 追加文件内容
     * @param {string} filePath 文件路径
     * @param {string} content 追加内容
     * @param {string} encoding 编码格式
     * @returns {boolean} 是否成功
     */
    appendFile(filePath, content, encoding = 'utf8') {
        try {
            fs.appendFileSync(filePath, content, encoding);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 检查文件是否存在
     * @param {string} filePath 文件路径
     * @returns {boolean} 是否存在
     */
    fileExists(filePath) {
        return fs.existsSync(filePath);
    }

    /**
     * 获取文件大小
     * @param {string} filePath 文件路径
     * @returns {number|null} 文件大小（字节）或null
     */
    getFileSize(filePath) {
        try {
            const stats = fs.statSync(filePath);
            return stats.size;
        } catch (error) {
            return null;
        }
    }

    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 提取消息中的文本内容
     * @param {*} message 消息对象
     * @returns {string} 文本内容
     */
    extractText(message) {
        if (typeof message === 'string') {
            return message;
        }
        
        if (Array.isArray(message)) {
            return message
                .filter(seg => seg.type === 'text')
                .map(seg => seg.data?.text || '')
                .join('');
        }
        
        return '';
    }

    /**
     * 提取消息中的回复/引用信息
     * @param {*} message 消息对象
     * @returns {Array} 回复信息数组
     */
    extractReplies(message) {
        if (!Array.isArray(message)) {
            return [];
        }

        return message
            .filter(seg => seg.type === 'reply')
            .map(seg => ({
                id: seg.data?.id || '',           // 被回复消息的ID
                message_id: seg.data?.id || ''    // 兼容性字段
            }));
    }

    /**
     * 提取消息中的文本内容
     * @param {*} message 消息对象
     * @returns {Array} 文本信息数组
     */
    extractTexts(message) {
        if (!Array.isArray(message)) {
            return [];
        }

        return message
            .filter(seg => seg.type === 'text')
            .map(seg => ({
                text: seg.data?.text || '',
                length: (seg.data?.text || '').length
            }));
    }

    /**
     * 提取消息中的At信息
     * @param {*} message 消息对象
     * @returns {Array} At信息数组
     */
    extractAts(message) {
        if (!Array.isArray(message)) {
            return [];
        }

        return message
            .filter(seg => seg.type === 'at')
            .map(seg => ({
                qq: seg.data?.qq || '',
                name: seg.data?.name || '',
                type: seg.data?.qq === 'all' ? '全体成员' : '个人'
            }));
    }

    /**
     * 提取消息中的表情信息
     * @param {*} message 消息对象
     * @returns {Array} 表情信息数组
     */
    extractFaces(message) {
        if (!Array.isArray(message)) {
            return [];
        }

        return message
            .filter(seg => seg.type === 'face')
            .map(seg => ({
                id: seg.data?.id || '',
                name: seg.data?.name || '',
                type: 'QQ表情'
            }));
    }

    /**
     * 提取消息中的其他类型消息段
     * @param {*} message 消息对象
     * @returns {Array} 其他类型信息数组
     */
    extractOthers(message) {
        if (!Array.isArray(message)) {
            return [];
        }

        const knownTypes = ['text', 'image', 'file', 'video', 'record', 'at', 'face', 'reply'];

        return message
            .filter(seg => !knownTypes.includes(seg.type))
            .map(seg => ({
                type: seg.type,
                data: seg.data || {},
                description: this.getSegmentDescription(seg.type)
            }));
    }

    /**
     * 获取消息段类型的描述
     * @param {string} type 消息段类型
     * @returns {string} 类型描述
     */
    getSegmentDescription(type) {
        const descriptions = {
            'share': '分享链接',
            'contact': '推荐好友/群',
            'location': '位置信息',
            'music': '音乐分享',
            'xml': 'XML消息',
            'json': 'JSON消息',
            'poke': '戳一戳',
            'gift': '礼物',
            'forward': '转发消息',
            'node': '转发节点',
            'dice': '骰子',
            'rps': '猜拳',
            'shake': '窗口抖动',
            'anonymous': '匿名消息',
            'cardimage': '装逼大图',
            'tts': '文本转语音'
        };

        return descriptions[type] || `未知类型(${type})`;
    }

    /**
     * 格式化时间戳为可读格式
     * @param {number} timestamp 时间戳（秒）
     * @returns {string} 格式化的时间字符串
     */
    formatTimestamp(timestamp) {
        const date = new Date(timestamp * 1000);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    /**
     * 获取群成员身份描述
     * @param {string} role 群成员角色
     * @returns {string} 身份描述
     */
    getGroupRole(role) {
        const roles = {
            'owner': '群主',
            'admin': '管理员',
            'member': '群员'
        };
        return roles[role] || '未知身份';
    }

    /**
     * 确保目录存在
     * @param {string} dirPath 目录路径
     */
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }

    /**
     * 获取聊天记录保存路径
     * @param {string} type 聊天类型 (group/private)
     * @param {string} id 群号或QQ号
     * @returns {string} 文件路径
     */
    getChatLogPath(type, id) {
        const baseDir = path.join(__dirname, '..', 'chat_logs');
        this.ensureDirectoryExists(baseDir);

        const typeDir = path.join(baseDir, type);
        this.ensureDirectoryExists(typeDir);

        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        const fileName = `${id}_${today}.log`;

        return path.join(typeDir, fileName);
    }

    /**
     * 提取消息中的图片信息
     * @param {*} message 消息对象
     * @returns {Array} 图片信息数组
     */
    extractImages(message) {
        if (!Array.isArray(message)) {
            return [];
        }

        return message
            .filter(seg => seg.type === 'image')
            .map(seg => ({
                file: seg.data?.file || '',
                url: seg.data?.url || '',
                summary: seg.data?.summary || '',
                fileSize: seg.data?.file_size || 0,  // 保持fileSize命名一致性
                sub_type: seg.data?.sub_type || 0,
                // 商城表情额外字段
                key: seg.data?.key || '',
                emoji_id: seg.data?.emoji_id || '',
                emoji_package_id: seg.data?.emoji_package_id || ''
            }));
    }

    /**
     * 提取消息中的文件信息
     * @param {*} message 消息对象
     * @returns {Array} 文件信息数组
     */
    extractFiles(message) {
        if (!Array.isArray(message)) {
            return [];
        }

        return message
            .filter(seg => seg.type === 'file')
            .map(seg => ({
                file: seg.data?.file || '',           // 文件名（接收时字段）
                file_id: seg.data?.file_id || '',     // 文件ID（接收时字段）
                url: seg.data?.url || '',             // 文件URL（如果有）
                name: seg.data?.file || seg.data?.name || '',  // 文件名（兼容性）
                size: seg.data?.file_size || 0        // 文件大小（接收时字段）
            }));
    }

    /**
     * 检查消息是否包含关键词
     * @param {*} message 消息对象
     * @param {Array|string} keywords 关键词数组或单个关键词
     * @returns {boolean} 是否包含关键词
     */
    containsKeywords(message, keywords) {
        const text = this.extractText(message).toLowerCase();
        const keywordArray = Array.isArray(keywords) ? keywords : [keywords];
        
        return keywordArray.some(keyword => 
            text.includes(keyword.toLowerCase())
        );
    }

    /**
     * 检查消息是否匹配正则表达式
     * @param {*} message 消息对象
     * @param {RegExp|string} pattern 正则表达式
     * @returns {boolean} 是否匹配
     */
    matchesPattern(message, pattern) {
        const text = this.extractText(message);
        const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
        
        return regex.test(text);
    }

    /**
     * 构建回复消息
     * @param {string} text 文本内容
     * @param {Object} options 选项
     * @returns {Array} 消息段数组
     */
    buildMessage(text, options = {}) {
        const segments = [];
        
        // 添加文本
        if (text) {
            segments.push({
                type: 'text',
                data: { text }
            });
        }
        
        // 添加图片
        if (options.image) {
            segments.push({
                type: 'image',
                data: { file: options.image }
            });
        }
        
        // 添加@用户
        if (options.at) {
            segments.unshift({
                type: 'at',
                data: { qq: options.at }
            });
        }
        
        return segments;
    }

    /**
     * 创建简单的键值存储
     * @param {string} filename 存储文件名
     * @returns {Object} 存储对象
     */
    createStorage(filename) {
        const filePath = path.join(this.configDir, `${filename}.json`);
        
        return {
            get: (key, defaultValue = null) => {
                try {
                    const data = this.readFile(filePath);
                    if (!data) return defaultValue;
                    
                    const json = this.safeJsonParse(data, {});
                    return json[key] !== undefined ? json[key] : defaultValue;
                } catch (error) {
                    return defaultValue;
                }
            },
            
            set: (key, value) => {
                try {
                    const data = this.readFile(filePath);
                    const json = this.safeJsonParse(data, {});
                    json[key] = value;
                    
                    return this.writeFile(filePath, JSON.stringify(json, null, 2));
                } catch (error) {
                    return false;
                }
            },
            
            delete: (key) => {
                try {
                    const data = this.readFile(filePath);
                    const json = this.safeJsonParse(data, {});
                    delete json[key];
                    
                    return this.writeFile(filePath, JSON.stringify(json, null, 2));
                } catch (error) {
                    return false;
                }
            },
            
            clear: () => {
                return this.writeFile(filePath, '{}');
            },
            
            getAll: () => {
                const data = this.readFile(filePath);
                return this.safeJsonParse(data, {});
            }
        };
    }

    /**
     * 创建NapCat API调用器
     * @param {Object} adapter - OneBot适配器实例
     * @returns {Object} NapCat API实例
     */
    createNapCatAPI(adapter) {
        const { createNapCatAPI } = require('./napcat_api');
        return createNapCatAPI(adapter);
    }
}

// 创建全局实例
const pluginHelper = new PluginHelper();

module.exports = {
    PluginHelper,
    helper: pluginHelper
};
