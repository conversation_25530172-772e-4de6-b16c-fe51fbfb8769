<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentra 沙盒世界管理界面</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 添加动态背景效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .header {
            background: rgba(15, 15, 35, 0.8);
            backdrop-filter: blur(20px);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid rgba(100, 255, 218, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            color: #64ffda;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px rgba(100, 255, 218, 0.4);
            background: linear-gradient(135deg, #64ffda 0%, #00bcd4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff5252;
            animation: pulse 2s infinite;
        }

        .status-dot.running {
            background: #4caf50;
        }

        .status-dot.paused {
            background: #ff9800;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
            padding: 1rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .panel {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(100, 255, 218, 0.1);
            backdrop-filter: blur(20px);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #64ffda, transparent);
            opacity: 0.5;
        }

        .panel:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            border-color: rgba(100, 255, 218, 0.2);
        }

        .panel h2 {
            color: #64ffda;
            margin-bottom: 1.5rem;
            font-size: 1.4rem;
            font-weight: 600;
            border-bottom: 2px solid rgba(100, 255, 218, 0.3);
            padding-bottom: 0.8rem;
            position: relative;
        }

        .panel h2::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30%;
            height: 2px;
            background: linear-gradient(90deg, #64ffda, #00bcd4);
            border-radius: 1px;
        }

        .controls {
            grid-column: span 3;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #64ffda 0%, #00bcd4 100%);
            color: #0f0f23;
            border: none;
            padding: 0.9rem 1.8rem;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-transform: uppercase;
            letter-spacing: 0.8px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(100, 255, 218, 0.2);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(100, 255, 218, 0.4);
            filter: brightness(1.1);
        }

        .btn:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(100, 255, 218, 0.3);
        }

        .btn:disabled {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            color: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.6;
        }

        .btn.danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
        }

        .btn.danger:hover {
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
        }

        .btn.warning:hover {
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(100, 255, 218, 0.2);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #64ffda;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #b0b0b0;
            margin-top: 0.5rem;
        }

        .agent-controls {
            display: flex;
            gap: 0.8rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .agent-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 0.8rem;
            background: rgba(100, 255, 218, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(100, 255, 218, 0.1);
        }

        .stat-mini {
            font-size: 0.85rem;
            color: #94a3b8;
        }

        .stat-mini span {
            color: #64ffda;
            font-weight: 600;
        }

        .agent-list, .event-list, .conversation-list {
            max-height: 350px;
            overflow-y: auto;
            margin-top: 1rem;
            scrollbar-width: thin;
            scrollbar-color: rgba(100, 255, 218, 0.3) transparent;
        }

        .agent-list::-webkit-scrollbar {
            width: 6px;
        }

        .agent-list::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .agent-list::-webkit-scrollbar-thumb {
            background: rgba(100, 255, 218, 0.3);
            border-radius: 3px;
        }

        .agent-item, .event-item, .conversation-item {
            background: rgba(255, 255, 255, 0.03);
            padding: 1rem;
            margin-bottom: 0.8rem;
            border-radius: 10px;
            border: 1px solid rgba(100, 255, 218, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .agent-item:hover {
            background: rgba(255, 255, 255, 0.06);
            border-color: rgba(100, 255, 218, 0.2);
            transform: translateX(4px);
        }

        .agent-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(180deg, #64ffda, #00bcd4);
            border-radius: 0 2px 2px 0;
        }

        .agent-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .agent-name, .event-name, .conversation-participants {
            font-weight: 600;
            color: #64ffda;
            font-size: 1rem;
        }

        .agent-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-mini {
            padding: 0.3rem 0.6rem;
            font-size: 0.7rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .btn-mini.edit {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .btn-mini.delete {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .btn-mini:hover {
            transform: scale(1.05);
            filter: brightness(1.1);
        }

        .agent-status, .event-status, .conversation-status {
            font-size: 0.85rem;
            color: #94a3b8;
            margin-top: 0.5rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
        }

        .status-value {
            color: #64ffda;
            font-weight: 500;
        }

        .time-control {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        .time-speed {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #e0e0e0;
            padding: 0.5rem;
            border-radius: 4px;
            width: 80px;
        }

        .log-panel {
            grid-column: span 3;
            max-height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            border-radius: 3px;
        }

        .log-info { background: rgba(100, 255, 218, 0.1); }
        .log-warning { background: rgba(255, 152, 0, 0.1); }
        .log-error { background: rgba(255, 82, 82, 0.1); }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(100, 255, 218, 0.3);
            border-radius: 50%;
            border-top-color: #64ffda;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .connection-status {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .connection-status.connected {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }

        .connection-status.disconnected {
            background: rgba(255, 82, 82, 0.2);
            color: #ff5252;
            border: 1px solid #ff5252;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            margin: 5% auto;
            padding: 2rem;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            position: relative;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(100, 255, 218, 0.2);
        }

        .modal-title {
            color: #64ffda;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .close {
            color: #94a3b8;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #ef4444;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #e2e8f0;
            font-weight: 500;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid rgba(100, 255, 218, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: #e2e8f0;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #64ffda;
            box-shadow: 0 0 0 3px rgba(100, 255, 218, 0.1);
            background: rgba(255, 255, 255, 0.08);
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                padding: 0.5rem;
            }

            .controls {
                grid-column: span 1;
            }

            .log-panel {
                grid-column: span 1;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 1.5rem;
            }

            .agent-controls {
                flex-direction: column;
            }

            .agent-controls .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌍 Sentra 沙盒世界</h1>
        <div class="status-indicator">
            <div class="status-dot" id="worldStatus"></div>
            <span id="worldStatusText">未启动</span>
        </div>
    </div>

    <div class="connection-status disconnected" id="connectionStatus">
        🔴 未连接
    </div>

    <div class="container">
        <!-- 控制面板 -->
        <div class="panel controls">
            <h2>🎮 世界控制</h2>
            <button class="btn" id="startBtn" onclick="startWorld()">启动世界</button>
            <button class="btn warning" id="pauseBtn" onclick="pauseWorld()" disabled>暂停</button>
            <button class="btn" id="resumeBtn" onclick="resumeWorld()" disabled>恢复</button>
            <button class="btn danger" id="stopBtn" onclick="stopWorld()" disabled>停止</button>
            
            <div class="time-control">
                <label>时间流速:</label>
                <input type="number" class="time-speed" id="timeSpeed" value="1" min="0.1" max="10" step="0.1">
                <button class="btn" onclick="setTimeSpeed()">设置</button>
            </div>
        </div>

        <!-- 世界统计 -->
        <div class="panel">
            <h2>📊 世界统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-value" id="agentCount">0</span>
                    <div class="stat-label">Agent数量</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="activeEvents">0</span>
                    <div class="stat-label">活跃事件</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="conversations">0</span>
                    <div class="stat-label">进行中对话</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="uptime">0</span>
                    <div class="stat-label">运行时间(分)</div>
                </div>
            </div>
        </div>

        <!-- 环境信息 -->
        <div class="panel">
            <h2>🌤️ 环境信息</h2>
            <div class="stat-item">
                <span class="stat-value" id="worldTime">--:--</span>
                <div class="stat-label">世界时间</div>
            </div>
            <div class="stat-item">
                <span class="stat-value" id="weather">未知</span>
                <div class="stat-label">天气</div>
            </div>
            <div class="stat-item">
                <span class="stat-value" id="temperature">--°C</span>
                <div class="stat-label">温度</div>
            </div>
        </div>

        <!-- Agent管理 -->
        <div class="panel">
            <h2>👥 Agent管理</h2>
            <div class="agent-controls">
                <button class="btn" onclick="showAddAgentModal()">📝 添加Agent</button>
                <button class="btn" onclick="loadAgentTemplates()">📋 加载模板</button>
                <button class="btn warning" onclick="refreshAgentList()">🔄 刷新列表</button>
            </div>
            <div class="agent-stats">
                <span class="stat-mini">总数: <span id="totalAgents">0</span></span>
                <span class="stat-mini">在线: <span id="activeAgents">0</span></span>
                <span class="stat-mini">平均心情: <span id="avgMood">0</span></span>
            </div>
            <div class="agent-list" id="agentList">
                <div class="loading"></div>
            </div>
        </div>

        <!-- 活跃事件 -->
        <div class="panel">
            <h2>🎭 事件管理</h2>
            <div class="agent-controls">
                <button class="btn" onclick="triggerRandomEvent()">🎲 随机事件</button>
                <button class="btn" onclick="triggerSpecificEvent('festival_announcement', '节日公告')">🎉 节日</button>
                <button class="btn" onclick="triggerSpecificEvent('sudden_rain', '突然下雨')">🌧️ 下雨</button>
                <button class="btn warning" onclick="triggerSpecificEvent('weekly_market', '周末市集')">🛒 市集</button>
            </div>
            <div class="event-list" id="eventList">
                <div class="loading"></div>
            </div>
        </div>

        <!-- 对话列表 -->
        <div class="panel">
            <h2>💬 活跃对话</h2>
            <div class="conversation-list" id="conversationList">
                <div class="loading"></div>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="panel log-panel">
            <h2>📝 系统日志</h2>
            <div id="logContainer"></div>
        </div>
    </div>

    <!-- Agent添加模态框 -->
    <div id="addAgentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">📝 添加新Agent</h3>
                <span class="close" onclick="closeAddAgentModal()">&times;</span>
            </div>
            <form id="addAgentForm">
                <div class="form-group">
                    <label class="form-label">选择Agent模板</label>
                    <select id="agentTemplate" class="form-select">
                        <option value="">创建全新Agent</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Agent名称</label>
                    <input type="text" id="agentName" class="form-input" placeholder="输入Agent名称" required>
                </div>
                <div class="form-group">
                    <label class="form-label">年龄</label>
                    <input type="number" id="agentAge" class="form-input" placeholder="18" min="1" max="100" value="25">
                </div>
                <div class="form-group">
                    <label class="form-label">性别</label>
                    <select id="agentGender" class="form-select">
                        <option value="未知">未知</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">初始位置</label>
                    <select id="agentLocation" class="form-select">
                        <option value="home">家</option>
                        <option value="school">学校</option>
                        <option value="park">公园</option>
                        <option value="library">图书馆</option>
                        <option value="cafe">咖啡厅</option>
                        <option value="gym">健身房</option>
                        <option value="mall">商场</option>
                        <option value="workplace">工作场所</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" onclick="closeAddAgentModal()">取消</button>
                    <button type="submit" class="btn">添加Agent</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // WebSocket连接
        const socket = io();
        let worldData = {};
        let agentTemplates = [];

        // 连接状态管理
        socket.on('connect', () => {
            updateConnectionStatus(true);
            addLog('WebSocket连接已建立', 'info');
            // 连接后立即加载Agent模板
            loadAgentTemplates();
        });

        socket.on('disconnect', () => {
            updateConnectionStatus(false);
            addLog('WebSocket连接已断开', 'warning');
        });

        // 接收初始数据
        socket.on('initialData', (data) => {
            worldData = data;
            updateUI();
            addLog('接收到初始数据', 'info');
        });

        // 接收更新数据
        socket.on('updateData', (data) => {
            worldData = { ...worldData, ...data };
            updateUI();
        });

        // 接收定期更新
        socket.on('periodicUpdate', (data) => {
            updateBasicStats(data);
        });

        // 监听世界事件
        socket.on('worldEvent', (event) => {
            addLog(`世界事件: ${event.type}`, 'info');
            updateWorldStatus(event);
        });

        // 监听Agent事件
        socket.on('agentEvent', (event) => {
            addLog(`Agent事件: ${event.type}`, 'info');
        });

        // 监听对话事件
        socket.on('dialogueEvent', (event) => {
            addLog(`对话事件: ${event.type}`, 'info');
        });

        // 更新连接状态
        function updateConnectionStatus(connected) {
            const status = document.getElementById('connectionStatus');
            if (connected) {
                status.className = 'connection-status connected';
                status.textContent = '🟢 已连接';
            } else {
                status.className = 'connection-status disconnected';
                status.textContent = '🔴 未连接';
            }
        }

        // 更新UI
        function updateUI() {
            if (!worldData.worldState) return;

            updateWorldStatus();
            updateStats();
            updateEnvironment();
            updateAgentList();
            updateEventList();
            updateConversationList();
        }

        // 更新世界状态
        function updateWorldStatus(event = null) {
            const statusDot = document.getElementById('worldStatus');
            const statusText = document.getElementById('worldStatusText');
            const startBtn = document.getElementById('startBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const resumeBtn = document.getElementById('resumeBtn');
            const stopBtn = document.getElementById('stopBtn');

            if (worldData.worldState) {
                const { isRunning, isPaused } = worldData.worldState;
                
                if (isRunning && !isPaused) {
                    statusDot.className = 'status-dot running';
                    statusText.textContent = '运行中';
                    startBtn.disabled = true;
                    pauseBtn.disabled = false;
                    resumeBtn.disabled = true;
                    stopBtn.disabled = false;
                } else if (isRunning && isPaused) {
                    statusDot.className = 'status-dot paused';
                    statusText.textContent = '已暂停';
                    startBtn.disabled = true;
                    pauseBtn.disabled = true;
                    resumeBtn.disabled = false;
                    stopBtn.disabled = false;
                } else {
                    statusDot.className = 'status-dot';
                    statusText.textContent = '已停止';
                    startBtn.disabled = false;
                    pauseBtn.disabled = true;
                    resumeBtn.disabled = true;
                    stopBtn.disabled = true;
                }
            }
        }

        // 更新统计信息
        function updateStats() {
            if (worldData.statistics) {
                document.getElementById('agentCount').textContent = worldData.statistics.population || 0;
            }
            if (worldData.activeEvents) {
                document.getElementById('activeEvents').textContent = worldData.activeEvents.length || 0;
            }
            if (worldData.conversations) {
                document.getElementById('conversations').textContent = worldData.conversations.length || 0;
            }
            if (worldData.worldState && worldData.worldState.uptime) {
                const minutes = Math.floor(worldData.worldState.uptime / 60000);
                document.getElementById('uptime').textContent = minutes;
            }
        }

        // 更新基础统计
        function updateBasicStats(data) {
            if (data.agentCount !== undefined) {
                document.getElementById('agentCount').textContent = data.agentCount;
            }
            if (data.activeEventCount !== undefined) {
                document.getElementById('activeEvents').textContent = data.activeEventCount;
            }
            if (data.activeConversationCount !== undefined) {
                document.getElementById('conversations').textContent = data.activeConversationCount;
            }
        }

        // 更新环境信息
        function updateEnvironment() {
            if (worldData.worldState && worldData.worldState.currentTime) {
                const time = new Date(worldData.worldState.currentTime);
                document.getElementById('worldTime').textContent = 
                    time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            }
            
            // 这里需要从环境数据中获取天气信息
            // 暂时使用占位符
            document.getElementById('weather').textContent = '晴朗';
            document.getElementById('temperature').textContent = '20°C';
        }

        // 更新Agent列表
        function updateAgentList() {
            const container = document.getElementById('agentList');
            if (!worldData.agents) {
                container.innerHTML = '<div class="loading"></div>';
                return;
            }

            if (worldData.agents.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">暂无Agent<br><small>点击"添加Agent"开始创建</small></div>';
                return;
            }

            // 更新统计信息
            updateAgentStats();

            container.innerHTML = worldData.agents.map(agent => `
                <div class="agent-item">
                    <div class="agent-header">
                        <div class="agent-name">${agent.name}</div>
                        <div class="agent-actions">
                            <button class="btn-mini edit" onclick="editAgent('${agent.id}')" title="编辑">✏️</button>
                            <button class="btn-mini delete" onclick="deleteAgent('${agent.id}')" title="删除">🗑️</button>
                        </div>
                    </div>
                    <div class="agent-status">
                        <div class="status-item">
                            <span>年龄:</span>
                            <span class="status-value">${agent.age}</span>
                        </div>
                        <div class="status-item">
                            <span>性别:</span>
                            <span class="status-value">${agent.gender || '未知'}</span>
                        </div>
                        <div class="status-item">
                            <span>心情:</span>
                            <span class="status-value">${Math.round(agent.mood || 0)}%</span>
                        </div>
                        <div class="status-item">
                            <span>位置:</span>
                            <span class="status-value">${getLocationName(agent.currentLocation)}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 更新Agent统计信息
        function updateAgentStats() {
            if (!worldData.agents) return;

            const totalAgents = worldData.agents.length;
            const activeAgents = worldData.agents.filter(agent => {
                const lastActive = new Date(agent.lastActive);
                const now = new Date();
                return (now - lastActive) < 5 * 60 * 1000; // 5分钟内活跃
            }).length;

            const avgMood = totalAgents > 0 ?
                Math.round(worldData.agents.reduce((sum, agent) => sum + (agent.mood || 0), 0) / totalAgents) : 0;

            document.getElementById('totalAgents').textContent = totalAgents;
            document.getElementById('activeAgents').textContent = activeAgents;
            document.getElementById('avgMood').textContent = avgMood + '%';
        }

        // 获取位置中文名称
        function getLocationName(locationId) {
            const locationNames = {
                'home': '家',
                'school': '学校',
                'park': '公园',
                'library': '图书馆',
                'cafe': '咖啡厅',
                'gym': '健身房',
                'mall': '商场',
                'workplace': '工作场所'
            };
            return locationNames[locationId] || locationId;
        }

        // 更新事件列表
        function updateEventList() {
            const container = document.getElementById('eventList');
            if (!worldData.activeEvents) {
                container.innerHTML = '<div class="loading"></div>';
                return;
            }

            if (worldData.activeEvents.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">暂无活跃事件<br><small>点击"触发随机事件"创建事件</small></div>';
                return;
            }

            container.innerHTML = worldData.activeEvents.map(event => {
                const startTime = new Date(event.startTime);
                const endTime = new Date(event.endTime);
                const now = new Date();
                const progress = Math.min(100, Math.max(0, (now - startTime) / (endTime - startTime) * 100));

                return `
                    <div class="event-item">
                        <div class="agent-header">
                            <div class="event-name">${event.name}</div>
                            <div class="event-progress" style="width: 60px; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px; overflow: hidden;">
                                <div style="width: ${progress}%; height: 100%; background: linear-gradient(90deg, #64ffda, #00bcd4); transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                        <div class="event-status">
                            <div class="status-item">
                                <span>类型:</span>
                                <span class="status-value">${getEventTypeName(event.type)}</span>
                            </div>
                            <div class="status-item">
                                <span>参与者:</span>
                                <span class="status-value">${event.participants.length}人</span>
                            </div>
                            <div class="status-item">
                                <span>进度:</span>
                                <span class="status-value">${Math.round(progress)}%</span>
                            </div>
                            <div class="status-item">
                                <span>剩余:</span>
                                <span class="status-value">${getTimeRemaining(endTime)}</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 获取事件类型中文名
        function getEventTypeName(type) {
            const typeNames = {
                'unexpected_meeting': '意外相遇',
                'lost_item': '丢失物品',
                'found_money': '意外之财',
                'sudden_rain': '突然下雨',
                'festival_announcement': '节日公告',
                'new_shop_opening': '新店开业',
                'daily_sunrise': '日出',
                'daily_sunset': '日落',
                'weekly_market': '周末市集'
            };
            return typeNames[type] || type;
        }

        // 获取剩余时间
        function getTimeRemaining(endTime) {
            const now = new Date();
            const remaining = endTime - now;

            if (remaining <= 0) return '已结束';

            const minutes = Math.floor(remaining / 60000);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (days > 0) return `${days}天`;
            if (hours > 0) return `${hours}小时`;
            return `${minutes}分钟`;
        }

        // 更新对话列表
        function updateConversationList() {
            const container = document.getElementById('conversationList');
            if (!worldData.conversations) {
                container.innerHTML = '<div class="loading"></div>';
                return;
            }

            if (worldData.conversations.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">暂无活跃对话<br><small>Agent会自动开始对话</small></div>';
                return;
            }

            container.innerHTML = worldData.conversations.map(conv => {
                const participantNames = conv.participants.map(id => {
                    const agent = worldData.agents?.find(a => a.id === id);
                    return agent ? agent.name : id;
                }).join(' & ');

                const startTime = new Date(conv.startTime);
                const duration = Math.floor((new Date() - startTime) / 1000 / 60); // 分钟
                const lastMessage = conv.messages[conv.messages.length - 1];

                return `
                    <div class="conversation-item">
                        <div class="agent-header">
                            <div class="conversation-participants">${participantNames}</div>
                            <div class="conversation-type" style="font-size: 0.7rem; color: #94a3b8; background: rgba(100,255,218,0.1); padding: 0.2rem 0.5rem; border-radius: 4px;">
                                ${getConversationTypeName(conv.type)}
                            </div>
                        </div>
                        <div class="conversation-status">
                            <div class="status-item">
                                <span>消息数:</span>
                                <span class="status-value">${conv.messages.length}</span>
                            </div>
                            <div class="status-item">
                                <span>持续:</span>
                                <span class="status-value">${duration}分钟</span>
                            </div>
                        </div>
                        ${lastMessage ? `
                            <div style="margin-top: 0.5rem; padding: 0.5rem; background: rgba(255,255,255,0.03); border-radius: 6px; font-size: 0.8rem; color: #94a3b8;">
                                <strong style="color: #64ffda;">${getAgentName(lastMessage.speaker)}:</strong>
                                ${lastMessage.content.length > 50 ? lastMessage.content.substring(0, 50) + '...' : lastMessage.content}
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        // 获取对话类型中文名
        function getConversationTypeName(type) {
            const typeNames = {
                'need_help': '寻求帮助',
                'seek_comfort': '寻求安慰',
                'share_happiness': '分享快乐',
                'reconnect': '重新联系',
                'casual_encounter': '偶遇',
                'weather_comment': '谈论天气',
                'location_greeting': '地点问候',
                'critical_need': '紧急需求'
            };
            return typeNames[type] || type;
        }

        // 获取Agent名称
        function getAgentName(agentId) {
            const agent = worldData.agents?.find(a => a.id === agentId);
            return agent ? agent.name : agentId;
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${time}] ${message}`;
            
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
            
            // 限制日志数量
            while (container.children.length > 100) {
                container.removeChild(container.firstChild);
            }
        }

        // 控制函数
        async function startWorld() {
            try {
                const response = await fetch('/api/world/start', { method: 'POST' });
                const result = await response.json();
                addLog(result.message, response.ok ? 'info' : 'error');
            } catch (error) {
                addLog(`启动失败: ${error.message}`, 'error');
            }
        }

        async function pauseWorld() {
            try {
                const response = await fetch('/api/world/pause', { method: 'POST' });
                const result = await response.json();
                addLog(result.message, response.ok ? 'info' : 'error');
            } catch (error) {
                addLog(`暂停失败: ${error.message}`, 'error');
            }
        }

        async function resumeWorld() {
            try {
                const response = await fetch('/api/world/resume', { method: 'POST' });
                const result = await response.json();
                addLog(result.message, response.ok ? 'info' : 'error');
            } catch (error) {
                addLog(`恢复失败: ${error.message}`, 'error');
            }
        }

        async function stopWorld() {
            try {
                const response = await fetch('/api/world/stop', { method: 'POST' });
                const result = await response.json();
                addLog(result.message, response.ok ? 'info' : 'error');
            } catch (error) {
                addLog(`停止失败: ${error.message}`, 'error');
            }
        }

        async function setTimeSpeed() {
            const speed = parseFloat(document.getElementById('timeSpeed').value);
            try {
                const response = await fetch('/api/world/timespeed', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ speed })
                });
                const result = await response.json();
                addLog(result.message, response.ok ? 'info' : 'error');
            } catch (error) {
                addLog(`设置时间流速失败: ${error.message}`, 'error');
            }
        }

        // 加载Agent模板
        async function loadAgentTemplates() {
            try {
                const response = await fetch('/api/agent-templates');
                if (response.ok) {
                    agentTemplates = await response.json();
                    updateAgentTemplateSelect();
                    addLog(`加载了 ${agentTemplates.length} 个Agent模板`, 'info');
                } else {
                    addLog('加载Agent模板失败', 'warning');
                }
            } catch (error) {
                addLog(`加载Agent模板错误: ${error.message}`, 'error');
            }
        }

        // 更新Agent模板选择框
        function updateAgentTemplateSelect() {
            const select = document.getElementById('agentTemplate');
            select.innerHTML = '<option value="">创建全新Agent</option>';

            agentTemplates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.id;

                // 构建更详细的显示文本
                let displayText = template.name || template.id;
                const details = [];

                if (template.age) details.push(`${template.age}岁`);
                if (template.gender && template.gender !== '未知') details.push(template.gender);
                if (template.qqNumber) details.push(`QQ:${template.qqNumber}`);

                if (details.length > 0) {
                    displayText += ` (${details.join(', ')})`;
                }

                option.textContent = displayText;
                select.appendChild(option);
            });
        }

        // 显示添加Agent模态框
        function showAddAgentModal() {
            document.getElementById('addAgentModal').style.display = 'block';
            // 重置表单
            document.getElementById('addAgentForm').reset();
            document.getElementById('agentAge').value = 25;
        }

        // 关闭添加Agent模态框
        function closeAddAgentModal() {
            document.getElementById('addAgentModal').style.display = 'none';
        }

        // 处理Agent模板选择
        document.getElementById('agentTemplate').addEventListener('change', function() {
            const templateId = this.value;
            if (templateId) {
                const template = agentTemplates.find(t => t.id === templateId);
                if (template) {
                    document.getElementById('agentName').value = template.name;
                    document.getElementById('agentAge').value = template.age || 25;
                    document.getElementById('agentGender').value = template.gender || '未知';
                }
            }
        });

        // 处理添加Agent表单提交
        document.getElementById('addAgentForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('agentName').value,
                age: parseInt(document.getElementById('agentAge').value),
                gender: document.getElementById('agentGender').value,
                currentLocation: document.getElementById('agentLocation').value,
                templateId: document.getElementById('agentTemplate').value || undefined
            };

            try {
                const response = await fetch('/api/agents', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok) {
                    addLog(`成功添加Agent: ${formData.name}`, 'success');
                    closeAddAgentModal();
                    // 刷新Agent列表
                    setTimeout(() => {
                        socket.emit('requestUpdate');
                    }, 1000);
                } else {
                    addLog(`添加Agent失败: ${result.error || result.message}`, 'error');
                }
            } catch (error) {
                addLog(`添加Agent错误: ${error.message}`, 'error');
            }
        });

        // 删除Agent
        async function deleteAgent(agentId) {
            if (!confirm('确定要删除这个Agent吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch(`/api/agents/${agentId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    addLog(`成功删除Agent: ${agentId}`, 'success');
                    // 刷新Agent列表
                    setTimeout(() => {
                        socket.emit('requestUpdate');
                    }, 1000);
                } else {
                    addLog(`删除Agent失败: ${result.error || result.message}`, 'error');
                }
            } catch (error) {
                addLog(`删除Agent错误: ${error.message}`, 'error');
            }
        }

        // 编辑Agent (暂时显示信息)
        function editAgent(agentId) {
            const agent = worldData.agents.find(a => a.id === agentId);
            if (agent) {
                alert(`Agent信息:\n名称: ${agent.name}\n年龄: ${agent.age}\n性别: ${agent.gender}\n心情: ${Math.round(agent.mood || 0)}%\n位置: ${getLocationName(agent.currentLocation)}`);
            }
        }

        // 刷新Agent列表
        function refreshAgentList() {
            addLog('刷新Agent列表...', 'info');
            socket.emit('requestUpdate');
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('addAgentModal');
            if (event.target === modal) {
                closeAddAgentModal();
            }
        }

        async function triggerRandomEvent() {
            const events = [
                { type: 'unexpected_meeting', name: '意外相遇' },
                { type: 'found_money', name: '意外之财' },
                { type: 'festival_announcement', name: '节日公告' },
                { type: 'new_shop_opening', name: '新店开业' },
                { type: 'sudden_rain', name: '突然下雨' },
                { type: 'weekly_market', name: '周末市集' }
            ];

            const randomEvent = events[Math.floor(Math.random() * events.length)];

            try {
                const response = await fetch('/api/events/trigger', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ eventType: randomEvent.type })
                });
                const result = await response.json();
                addLog(`触发事件: ${randomEvent.name} (${randomEvent.type})`, response.ok ? 'success' : 'error');

                if (response.ok) {
                    // 刷新事件列表
                    setTimeout(() => {
                        socket.emit('requestUpdate');
                    }, 1000);
                }
            } catch (error) {
                addLog(`触发事件失败: ${error.message}`, 'error');
            }
        }

        // 触发特定事件
        async function triggerSpecificEvent(eventType, eventName) {
            try {
                const response = await fetch('/api/events/trigger', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ eventType })
                });
                const result = await response.json();
                addLog(`触发事件: ${eventName}`, response.ok ? 'success' : 'error');

                if (response.ok) {
                    setTimeout(() => {
                        socket.emit('requestUpdate');
                    }, 1000);
                }
            } catch (error) {
                addLog(`触发事件失败: ${error.message}`, 'error');
            }
        }

        // 定期请求更新
        setInterval(() => {
            if (socket.connected) {
                socket.emit('requestUpdate');
            }
        }, 10000); // 每10秒请求一次更新

        // 初始化
        addLog('沙盒世界管理界面已加载', 'info');
    </script>
</body>
</html>
