# 🌍 VCPToolBox 沙盒世界生态系统

一个充满活力的虚拟社会生态系统，让AI Agent在其中自由互动、成长和发展。

## ✨ 核心特性

### 🌍 世界环境系统
- **时间系统**: 可调节的时间流速，支持暂停、加速、倒退
- **天气系统**: 动态天气变化，影响Agent心情和行为
- **地点系统**: 多个虚拟场景（家、学校、公园、商店等）
- **资源系统**: 虚拟货币、物品、能量等资源管理

### 👥 Agent生态管理
- **生存需求**: 饥饿、疲劳、社交需求、娱乐需求等
- **技能系统**: 学习、工作、艺术、运动等技能发展
- **性格演化**: 根据经历和互动逐渐改变性格特征
- **记忆继承**: 基于现有记忆系统的长期记忆和经验积累

### 🤝 社交网络系统
- **关系类型**: 朋友、恋人、家人、同事、陌生人等
- **关系指标**: 好感度、信任度、影响力、依赖度
- **群体动态**: 小团体形成、社交圈子、社会地位
- **影响力计算**: 基于关系网络的智能影响力评估

### 💬 自主对话引擎
- **智能触发**: 基于需求、情绪、环境、关系的自主对话
- **话题生成**: 根据共同经历、兴趣、当前事件生成话题
- **情感表达**: 结合心理状态系统的真实情感反应
- **对话演化**: 关系深度影响对话内容和方式

### 🎭 事件驱动系统
- **随机事件**: 意外、机遇、挑战等随机发生的事件
- **定期事件**: 节日、生日、季节变化等周期性事件
- **用户事件**: 用户可以手动触发的特殊事件
- **连锁反应**: 事件之间的相互影响和连锁效应

## 🚀 快速开始

### 1. 初始化系统
```javascript
// 通过VCP插件系统调用
const result = await pluginManager.executePlugin('SandboxWorld', JSON.stringify({
    action: 'init'
}));
```

### 2. 启动世界
```javascript
const result = await pluginManager.executePlugin('SandboxWorld', JSON.stringify({
    action: 'start'
}));
```

### 3. 添加Agent
```javascript
const result = await pluginManager.executePlugin('SandboxWorld', JSON.stringify({
    action: 'addAgent',
    agentConfig: {
        name: '小明',
        age: 25,
        templateId: '雨安_2166683295' // 可选：基于现有Agent模板
    }
}));
```

### 4. 访问Web界面
启动后访问 `http://localhost:8080` 查看实时管理界面

## 🎮 Web管理界面

### 功能特性
- **实时监控**: WebSocket实时更新世界状态
- **可视化控制**: 直观的按钮控制世界运行
- **数据展示**: 实时显示Agent、事件、对话等信息
- **日志系统**: 详细的系统运行日志
- **响应式设计**: 支持桌面和移动设备

### 主要面板
1. **世界控制**: 启动、暂停、恢复、停止世界
2. **统计信息**: Agent数量、活跃事件、对话数等
3. **环境信息**: 世界时间、天气、温度等
4. **Agent列表**: 所有Agent的状态和信息
5. **活跃事件**: 当前正在进行的事件
6. **对话监控**: 实时对话内容和状态

## 📊 API接口

### RESTful API
- `GET /api/world/status` - 获取世界状态
- `POST /api/world/start` - 启动世界
- `POST /api/world/pause` - 暂停世界
- `POST /api/world/resume` - 恢复世界
- `POST /api/world/stop` - 停止世界
- `GET /api/agents` - 获取所有Agent
- `POST /api/agents` - 添加新Agent
- `DELETE /api/agents/:id` - 移除Agent
- `GET /api/events/active` - 获取活跃事件
- `POST /api/events/trigger` - 触发事件
- `GET /api/conversations` - 获取活跃对话
- `GET /api/statistics` - 获取统计信息

### WebSocket事件
- `worldEvent` - 世界状态变化
- `agentEvent` - Agent相关事件
- `dialogueEvent` - 对话事件
- `environmentEvent` - 环境变化事件

## ⚙️ 配置说明

### 基础配置 (config.json)
```json
{
  "world": {
    "name": "我的沙盒世界",
    "maxAgents": 50,
    "timeSpeed": 1,
    "autoSave": true
  },
  "ui": {
    "webInterface": {
      "enabled": true,
      "port": 8080
    }
  }
}
```

### 高级配置
- **更新频率**: 各系统的更新间隔
- **触发器设置**: 对话和事件的触发条件
- **性格影响**: 性格对行为的影响程度
- **关系衰减**: 关系随时间的自然衰减
- **事件概率**: 各类事件的发生概率

## 🔗 系统集成

### 与现有系统的集成
1. **Agent系统**: 自动加载现有Agent文件作为模板
2. **心理状态系统**: 同步情绪、压力、好感度等数据
3. **记忆系统**: 集成对话历史和重要事件记录
4. **OneBot11**: 可选的消息推送和用户交互

### 数据同步
- 实时同步Agent的心理状态变化
- 自动保存重要对话到记忆系统
- 定期备份世界状态和关系数据

## 📈 性能优化

### 系统要求
- **内存**: 建议512MB以上
- **存储**: 约100MB初始空间
- **网络**: WebSocket + HTTP API

### 优化策略
- **批量处理**: 减少频繁的数据库操作
- **缓存机制**: 缓存常用数据和计算结果
- **异步处理**: 非阻塞的事件处理
- **数据压缩**: WebSocket数据传输压缩

## 🎯 使用场景

### 1. AI社交实验
研究AI Agent之间的社交行为和群体动态

### 2. 虚拟社区
创建一个持续运行的虚拟社区环境

### 3. 行为模拟
模拟真实社会中的各种社交现象

### 4. 教育演示
展示人工智能和社会网络的概念

### 5. 娱乐观察
观察AI Agent的有趣互动和突发事件

## 🔧 开发扩展

### 自定义事件
```javascript
// 添加自定义事件类型
eventSystem.eventTemplates.set('custom_event', {
    name: '自定义事件',
    description: '这是一个自定义事件',
    duration: 300000,
    effects: {
        participants: { happiness: 10 }
    }
});
```

### 自定义地点
```javascript
// 添加新地点
worldEnvironment.locations.set('custom_location', {
    id: 'custom_location',
    name: '自定义地点',
    type: 'special',
    capacity: 20,
    activities: ['custom_activity']
});
```

## 🐛 故障排除

### 常见问题
1. **世界无法启动**: 检查端口占用和依赖安装
2. **Agent无法加载**: 确认Agent文件格式正确
3. **Web界面无法访问**: 检查防火墙和端口设置
4. **数据丢失**: 确认自动保存功能已启用

### 日志查看
- Web界面实时日志
- 控制台输出
- 文件日志（如果启用）

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 完整的沙盒世界生态系统
- Web管理界面
- RESTful API和WebSocket支持
- 与现有系统的集成

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境
1. Node.js >= 14.0.0
2. 安装依赖: `npm install`
3. 启动开发服务器
4. 访问测试界面

## 📄 许可证

MIT License - 详见LICENSE文件

## 🙏 致谢

感谢VCPToolBox团队和所有贡献者的支持！

---

**🌟 开始你的沙盒世界之旅吧！** 

创建一个充满活力的虚拟社会，观察AI Agent们如何在其中生活、成长和互动。每一次运行都会带来不同的故事和惊喜！
