/**
 * 用户信息查看插件
 * 提供用户信息查询和管理功能
 */

const BasePlugin = require('./base_plugin');
const UserInfoManager = require('./user_info_manager');

class UserInfoPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super(adapter, logger, config);
        this.name = 'UserInfoPlugin';
        this.description = '用户信息查看和管理插件';
        this.version = '1.0.0';
        this.priority = 80;
        this.permissions = ['all'];
        
        this.userInfoManager = new UserInfoManager();
        
        // 支持的命令
        this.commands = [
            '查看用户信息',
            '用户统计',
            '用户列表',
            '清理用户数据'
        ];
    }

    /**
     * 检查是否应该处理此消息
     */
    shouldHandle(context) {
        if (!context.message) return false;
        
        const message = context.message.trim();
        return this.commands.some(cmd => 
            message.startsWith(cmd) || 
            message.includes(cmd)
        );
    }

    /**
     * 处理消息
     */
    async handle(context) {
        try {
            const message = context.message.trim();
            
            if (message.startsWith('查看用户信息')) {
                await this.handleUserInfoQuery(context, message);
            } else if (message.includes('用户统计')) {
                await this.handleUserStats(context);
            } else if (message.includes('用户列表')) {
                await this.handleUserList(context);
            } else if (message.includes('清理用户数据')) {
                await this.handleCleanupData(context, message);
            }
            
        } catch (error) {
            this.logger.error('UserInfoPlugin', `处理消息失败: ${error.message}`);
            await this.reply(context, `处理失败: ${error.message}`);
        }
    }

    /**
     * 处理用户信息查询
     */
    async handleUserInfoQuery(context, message) {
        try {
            // 提取用户ID
            const match = message.match(/查看用户信息\s*(\d+)?/);
            let userId = match && match[1] ? match[1] : context.userId;
            
            if (!userId) {
                await this.reply(context, '请指定用户ID，例如：查看用户信息 123456789');
                return;
            }

            const userInfo = this.userInfoManager.getUserInfo(userId);
            if (!userInfo) {
                await this.reply(context, `未找到用户 ${userId} 的信息`);
                return;
            }

            const infoText = this.formatUserInfo(userInfo);
            await this.reply(context, infoText);
            
        } catch (error) {
            this.logger.error('UserInfoPlugin', `查询用户信息失败: ${error.message}`);
            await this.reply(context, '查询失败，请稍后重试');
        }
    }

    /**
     * 处理用户统计查询
     */
    async handleUserStats(context) {
        try {
            const stats = this.userInfoManager.getStats();
            
            const statsText = [
                '📊 用户信息统计',
                `总用户数: ${stats.total_users}`,
                `今日更新: ${stats.updated_today}`,
                `本周更新: ${stats.updated_this_week}`,
                `数据目录: ${stats.data_directory}`
            ].join('\n');

            await this.reply(context, statsText);
            
        } catch (error) {
            this.logger.error('UserInfoPlugin', `获取统计信息失败: ${error.message}`);
            await this.reply(context, '获取统计信息失败');
        }
    }

    /**
     * 处理用户列表查询
     */
    async handleUserList(context) {
        try {
            const users = this.userInfoManager.getAllUsers();
            
            if (users.length === 0) {
                await this.reply(context, '暂无用户信息');
                return;
            }

            const listText = [
                '👥 用户列表 (最近更新)',
                ...users.slice(0, 10).map((user, index) => {
                    const updateTime = user.last_update ? 
                        new Date(user.last_update * 1000).toLocaleString('zh-CN') : '未知';
                    return `${index + 1}. ${user.nickname || '未知'} (${user.user_id}) - ${updateTime}`;
                })
            ];

            if (users.length > 10) {
                listText.push(`... 还有 ${users.length - 10} 个用户`);
            }

            await this.reply(context, listText.join('\n'));
            
        } catch (error) {
            this.logger.error('UserInfoPlugin', `获取用户列表失败: ${error.message}`);
            await this.reply(context, '获取用户列表失败');
        }
    }

    /**
     * 处理数据清理
     */
    async handleCleanupData(context, message) {
        try {
            // 提取天数
            const match = message.match(/清理用户数据\s*(\d+)?/);
            const days = match && match[1] ? parseInt(match[1]) : 30;
            
            this.userInfoManager.cleanupOldData(days);
            await this.reply(context, `已清理 ${days} 天前的用户数据`);
            
        } catch (error) {
            this.logger.error('UserInfoPlugin', `清理数据失败: ${error.message}`);
            await this.reply(context, '清理数据失败');
        }
    }

    /**
     * 格式化用户信息显示
     */
    formatUserInfo(userInfo) {
        const info = [];
        
        info.push('👤 用户信息');
        
        // 基本信息
        if (userInfo.nickname) info.push(`昵称: ${userInfo.nickname}`);
        if (userInfo.user_id) info.push(`QQ号: ${userInfo.user_id}`);
        if (userInfo.long_nick) info.push(`个性签名: ${userInfo.long_nick}`);
        
        // 个人信息
        if (userInfo.sex) {
            const sexMap = { 'male': '男', 'female': '女', 'unknown': '未知' };
            info.push(`性别: ${sexMap[userInfo.sex] || userInfo.sex}`);
        }
        if (userInfo.age) info.push(`年龄: ${userInfo.age}`);
        
        // 生日信息
        if (userInfo.birthday_year && userInfo.birthday_month && userInfo.birthday_day) {
            info.push(`生日: ${userInfo.birthday_year}-${userInfo.birthday_month}-${userInfo.birthday_day}`);
        }
        
        // 地理信息
        const location = [userInfo.country, userInfo.province, userInfo.city].filter(Boolean).join(' ');
        if (location) info.push(`地区: ${location}`);
        
        // 教育信息
        if (userInfo.college) info.push(`学校: ${userInfo.college}`);
        
        // QQ相关
        if (userInfo.qq_level) info.push(`QQ等级: ${userInfo.qq_level}`);
        if (userInfo.is_vip !== undefined) {
            info.push(`VIP: ${userInfo.is_vip ? '是' : '否'}`);
        }
        if (userInfo.vip_level) info.push(`VIP等级: ${userInfo.vip_level}`);
        
        // 联系信息
        if (userInfo.email && userInfo.email !== '-') info.push(`邮箱: ${userInfo.email}`);
        
        // 时间信息
        if (userInfo.reg_time) {
            const regDate = new Date(userInfo.reg_time * 1000).toLocaleDateString('zh-CN');
            info.push(`注册时间: ${regDate}`);
        }
        if (userInfo.last_update) {
            const updateDate = new Date(userInfo.last_update * 1000).toLocaleString('zh-CN');
            info.push(`最后更新: ${updateDate}`);
        }
        
        // 更新历史
        if (userInfo.update_history && userInfo.update_history.length > 0) {
            info.push(`更新次数: ${userInfo.update_history.length}`);
        }
        
        return info.join('\n');
    }

    /**
     * 获取插件帮助信息
     */
    getHelp() {
        return [
            '用户信息插件帮助：',
            '• 查看用户信息 [QQ号] - 查看指定用户信息',
            '• 用户统计 - 查看用户数据统计',
            '• 用户列表 - 查看最近更新的用户列表',
            '• 清理用户数据 [天数] - 清理指定天数前的数据'
        ].join('\n');
    }
}

module.exports = UserInfoPlugin;
