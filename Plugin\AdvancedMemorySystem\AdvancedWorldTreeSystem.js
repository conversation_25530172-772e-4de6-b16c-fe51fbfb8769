/**
 * 高级世界树背景系统
 * 实现更丰富的叙事环境和角色定位系统
 * 基于叙事学和世界构建理论
 */

class AdvancedWorldTreeSystem {
    constructor(config, logger, openaiService) {
        this.config = config;
        this.logger = logger;
        this.openaiService = openaiService;
        
        // 世界树分支定义
        this.worldBranches = {
            // 现实世界分支
            reality: {
                name: '现实世界',
                themes: ['日常生活', '工作学习', '人际关系', '社会现象'],
                toneRange: ['严肃', '轻松', '幽默', '温馨'],
                complexityLevel: 0.6,
                fantasyElements: 0.1
            },
            
            // 科幻世界分支
            scifi: {
                name: '科幻世界',
                themes: ['未来科技', '太空探索', '人工智能', '生物工程'],
                toneRange: ['严肃', '紧张', '神秘', '宏大'],
                complexityLevel: 0.8,
                fantasyElements: 0.7
            },
            
            // 奇幻世界分支
            fantasy: {
                name: '奇幻世界',
                themes: ['魔法', '冒险', '神话', '传说'],
                toneRange: ['史诗', '神秘', '浪漫', '冒险'],
                complexityLevel: 0.9,
                fantasyElements: 0.9
            },
            
            // 历史世界分支
            historical: {
                name: '历史世界',
                themes: ['古代文明', '历史事件', '文化传统', '时代变迁'],
                toneRange: ['庄重', '怀旧', '教育', '反思'],
                complexityLevel: 0.7,
                fantasyElements: 0.2
            },
            
            // 心理世界分支
            psychological: {
                name: '心理世界',
                themes: ['内心探索', '情感体验', '意识流', '梦境'],
                toneRange: ['深沉', '内省', '抽象', '象征'],
                complexityLevel: 0.8,
                fantasyElements: 0.5
            },
            
            // 哲学世界分支
            philosophical: {
                name: '哲学世界',
                themes: ['存在意义', '道德伦理', '认知思辨', '价值探讨'],
                toneRange: ['深刻', '思辨', '启发', '质疑'],
                complexityLevel: 0.9,
                fantasyElements: 0.3
            }
        };
        
        // 角色原型定义
        this.characterArchetypes = {
            hero: { name: '英雄', traits: ['勇敢', '正义', '领导力'], motivation: '拯救他人' },
            mentor: { name: '导师', traits: ['智慧', '经验', '指导'], motivation: '传授知识' },
            explorer: { name: '探索者', traits: ['好奇', '冒险', '发现'], motivation: '探索未知' },
            creator: { name: '创造者', traits: ['创新', '想象', '表达'], motivation: '创造新事物' },
            caregiver: { name: '照顾者', traits: ['关爱', '保护', '奉献'], motivation: '帮助他人' },
            rebel: { name: '反叛者', traits: ['独立', '挑战', '变革'], motivation: '打破常规' },
            lover: { name: '恋人', traits: ['激情', '浪漫', '连接'], motivation: '寻找爱情' },
            jester: { name: '小丑', traits: ['幽默', '轻松', '娱乐'], motivation: '带来快乐' },
            sage: { name: '智者', traits: ['理性', '分析', '真理'], motivation: '理解世界' },
            innocent: { name: '天真者', traits: ['纯真', '乐观', '信任'], motivation: '保持纯真' },
            ruler: { name: '统治者', traits: ['权威', '控制', '责任'], motivation: '建立秩序' },
            magician: { name: '魔法师', traits: ['神秘', '转化', '力量'], motivation: '改变现实' }
        };
        
        // 叙事结构模式
        this.narrativeStructures = {
            heroJourney: {
                name: '英雄之旅',
                stages: ['平凡世界', '冒险召唤', '拒绝召唤', '遇见导师', '跨越门槛', '试炼', '启示', '回归'],
                emotionalArc: 'U型',
                complexity: 0.8
            },
            threeAct: {
                name: '三幕结构',
                stages: ['建立', '对抗', '解决'],
                emotionalArc: '上升',
                complexity: 0.6
            },
            cyclical: {
                name: '循环结构',
                stages: ['开始', '发展', '高潮', '回归'],
                emotionalArc: '圆形',
                complexity: 0.7
            },
            episodic: {
                name: '情节式',
                stages: ['事件1', '事件2', '事件3', '连接'],
                emotionalArc: '波浪',
                complexity: 0.5
            }
        };
        
        // 当前世界状态
        this.currentWorldState = {
            activeBranch: 'reality',
            narrativeContext: {},
            characterRole: 'sage',
            storyProgression: 0.0,
            thematicElements: [],
            emotionalTone: 'neutral'
        };
    }

    /**
     * 分析和更新世界树背景
     */
    async analyzeWorldTreeBackground(userMessage, aiResponse, currentStates, context = {}) {
        try {
            this.logger.info('世界树系统', '开始分析世界树背景');

            // 1. 分析对话的世界特征
            const worldCharacteristics = await this.analyzeWorldCharacteristics(userMessage, aiResponse);
            
            // 2. 确定最适合的世界分支
            const optimalBranch = await this.determineOptimalBranch(worldCharacteristics, currentStates);
            
            // 3. 分析角色定位
            const characterAnalysis = await this.analyzeCharacterRole(userMessage, aiResponse, optimalBranch);
            
            // 4. 计算叙事进展
            const narrativeProgression = await this.calculateNarrativeProgression(worldCharacteristics, currentStates);
            
            // 5. 生成叙事背景
            const narrativeContext = await this.generateNarrativeContext(optimalBranch, characterAnalysis, narrativeProgression);
            
            // 6. 计算世界状态影响
            const worldStateImpact = this.calculateWorldStateImpact(narrativeContext, currentStates);
            
            const result = {
                current_branch: optimalBranch.name,
                character_role: characterAnalysis.primaryRole,
                story_progression: narrativeProgression.progressValue,
                narrative_context: narrativeContext,
                world_state: {
                    environment: narrativeContext.environment,
                    social_dynamics: narrativeContext.socialDynamics,
                    technological_level: narrativeContext.technologyLevel,
                    magical_elements: narrativeContext.magicalElements
                },
                background_influence: worldStateImpact,
                thematic_resonance: this.calculateThematicResonance(worldCharacteristics, optimalBranch),
                immersion_level: this.calculateImmersionLevel(narrativeContext, currentStates)
            };

            this.logger.success('世界树系统', '世界树背景分析完成');
            return result;

        } catch (error) {
            this.logger.error('世界树系统', '世界树分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 分析世界特征
     */
    async analyzeWorldCharacteristics(userMessage, aiResponse) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "analyze_world_characteristics",
                    description: "分析对话内容的世界特征和叙事元素",
                    parameters: {
                        type: "object",
                        properties: {
                            genre_indicators: {
                                type: "object",
                                properties: {
                                    reality_score: { type: "number", minimum: 0, maximum: 1, description: "现实性评分" },
                                    fantasy_score: { type: "number", minimum: 0, maximum: 1, description: "奇幻性评分" },
                                    scifi_score: { type: "number", minimum: 0, maximum: 1, description: "科幻性评分" },
                                    historical_score: { type: "number", minimum: 0, maximum: 1, description: "历史性评分" },
                                    psychological_score: { type: "number", minimum: 0, maximum: 1, description: "心理性评分" },
                                    philosophical_score: { type: "number", minimum: 0, maximum: 1, description: "哲学性评分" }
                                },
                                required: ["reality_score", "fantasy_score", "scifi_score"]
                            },
                            narrative_elements: {
                                type: "object",
                                properties: {
                                    conflict_level: { type: "number", minimum: 0, maximum: 1, description: "冲突强度" },
                                    character_development: { type: "number", minimum: 0, maximum: 1, description: "角色发展度" },
                                    world_building: { type: "number", minimum: 0, maximum: 1, description: "世界构建度" },
                                    emotional_depth: { type: "number", minimum: 0, maximum: 1, description: "情感深度" },
                                    thematic_complexity: { type: "number", minimum: 0, maximum: 1, description: "主题复杂度" }
                                },
                                required: ["conflict_level", "character_development", "emotional_depth"]
                            },
                            linguistic_features: {
                                type: "object",
                                properties: {
                                    formality_level: { type: "number", minimum: 0, maximum: 1, description: "正式程度" },
                                    metaphor_usage: { type: "number", minimum: 0, maximum: 1, description: "隐喻使用" },
                                    technical_language: { type: "number", minimum: 0, maximum: 1, description: "技术语言" },
                                    emotional_language: { type: "number", minimum: 0, maximum: 1, description: "情感语言" },
                                    abstract_concepts: { type: "number", minimum: 0, maximum: 1, description: "抽象概念" }
                                },
                                required: ["formality_level", "metaphor_usage", "emotional_language"]
                            },
                            contextual_cues: {
                                type: "object",
                                properties: {
                                    time_period: { type: "string", description: "时间背景" },
                                    setting_type: { type: "string", description: "场景类型" },
                                    social_context: { type: "string", description: "社会背景" },
                                    cultural_elements: { type: "array", items: { type: "string" }, description: "文化元素" }
                                }
                            }
                        },
                        required: ["genre_indicators", "narrative_elements", "linguistic_features"]
                    }
                }
            }];

            const prompt = `请分析以下对话的世界特征和叙事元素：

用户消息：${userMessage}
AI回复：${aiResponse}

分析要求：
1. 类型指标：评估对话内容属于哪种世界类型（现实、奇幻、科幻、历史、心理、哲学）
2. 叙事元素：分析对话中的叙事结构和发展特征
3. 语言特征：分析语言风格和表达方式
4. 背景线索：识别时间、地点、社会文化背景

请基于叙事学和世界构建理论提供详细分析。`;

            const response = await this.openaiService.makeToolCall(prompt, tools, "analyze_world_characteristics");
            return response;

        } catch (error) {
            this.logger.error('世界树系统', '世界特征分析失败:', error.message);
            return {
                genre_indicators: { reality_score: 0.7, fantasy_score: 0.2, scifi_score: 0.1 },
                narrative_elements: { conflict_level: 0.3, character_development: 0.5, emotional_depth: 0.4 },
                linguistic_features: { formality_level: 0.5, metaphor_usage: 0.3, emotional_language: 0.4 }
            };
        }
    }

    /**
     * 确定最适合的世界分支
     */
    async determineOptimalBranch(worldCharacteristics, currentStates) {
        try {
            const genreScores = worldCharacteristics.genre_indicators;
            const narrativeElements = worldCharacteristics.narrative_elements;
            
            // 计算每个分支的适配度
            const branchScores = {};
            
            for (const [branchKey, branchConfig] of Object.entries(this.worldBranches)) {
                let score = 0;
                
                // 基于类型评分计算适配度
                switch (branchKey) {
                    case 'reality':
                        score = genreScores.reality_score * 0.8 + (1 - branchConfig.fantasyElements) * 0.2;
                        break;
                    case 'scifi':
                        score = genreScores.scifi_score * 0.7 + branchConfig.fantasyElements * genreScores.fantasy_score * 0.3;
                        break;
                    case 'fantasy':
                        score = genreScores.fantasy_score * 0.8 + branchConfig.fantasyElements * 0.2;
                        break;
                    case 'historical':
                        score = genreScores.historical_score * 0.7 + genreScores.reality_score * 0.3;
                        break;
                    case 'psychological':
                        score = genreScores.psychological_score * 0.6 + narrativeElements.emotional_depth * 0.4;
                        break;
                    case 'philosophical':
                        score = genreScores.philosophical_score * 0.7 + narrativeElements.thematic_complexity * 0.3;
                        break;
                }
                
                // 考虑复杂度匹配
                const complexityMatch = 1 - Math.abs(branchConfig.complexityLevel - narrativeElements.thematic_complexity);
                score = score * 0.8 + complexityMatch * 0.2;
                
                branchScores[branchKey] = score;
            }
            
            // 找到最高分的分支
            const optimalBranchKey = Object.entries(branchScores)
                .reduce((max, [key, score]) => score > max.score ? { key, score } : max, { key: 'reality', score: 0 }).key;
            
            return {
                ...this.worldBranches[optimalBranchKey],
                key: optimalBranchKey,
                adaptationScore: branchScores[optimalBranchKey],
                alternativeBranches: this.getAlternativeBranches(branchScores, optimalBranchKey)
            };

        } catch (error) {
            this.logger.error('世界树系统', '分支确定失败:', error.message);
            return { ...this.worldBranches.reality, key: 'reality', adaptationScore: 0.5 };
        }
    }

    /**
     * 分析角色定位
     */
    async analyzeCharacterRole(userMessage, aiResponse, optimalBranch) {
        try {
            // 基于对话内容和世界分支分析角色原型
            const roleIndicators = this.extractRoleIndicators(userMessage, aiResponse);
            const branchCompatibility = this.calculateBranchCompatibility(roleIndicators, optimalBranch);
            
            // 计算每个原型的匹配度
            const archetypeScores = {};
            
            for (const [archetypeKey, archetype] of Object.entries(this.characterArchetypes)) {
                let score = 0;
                
                // 基于特征匹配
                for (const trait of archetype.traits) {
                    if (roleIndicators.traits.includes(trait.toLowerCase())) {
                        score += 0.3;
                    }
                }
                
                // 基于动机匹配
                if (roleIndicators.motivations.some(m => m.includes(archetype.motivation.toLowerCase()))) {
                    score += 0.4;
                }
                
                // 基于分支兼容性
                score += branchCompatibility[archetypeKey] || 0;
                
                archetypeScores[archetypeKey] = Math.min(1.0, score);
            }
            
            // 选择最匹配的角色
            const primaryRole = Object.entries(archetypeScores)
                .reduce((max, [key, score]) => score > max.score ? { key, score } : max, { key: 'sage', score: 0 }).key;
            
            return {
                primaryRole: this.characterArchetypes[primaryRole].name,
                roleKey: primaryRole,
                confidence: archetypeScores[primaryRole],
                secondaryRoles: this.getSecondaryRoles(archetypeScores, primaryRole),
                roleEvolution: this.calculateRoleEvolution(archetypeScores, optimalBranch)
            };

        } catch (error) {
            this.logger.error('世界树系统', '角色分析失败:', error.message);
            return { primaryRole: '智者', roleKey: 'sage', confidence: 0.5 };
        }
    }

    /**
     * 提取角色指标
     */
    extractRoleIndicators(userMessage, aiResponse) {
        const combinedText = (userMessage + ' ' + aiResponse).toLowerCase();

        // 简化的特征识别
        const traitKeywords = {
            '勇敢': ['勇敢', '勇气', '无畏', '挑战'],
            '智慧': ['智慧', '聪明', '理解', '分析'],
            '关爱': ['关心', '照顾', '帮助', '支持'],
            '创新': ['创新', '创造', '想象', '新颖'],
            '幽默': ['幽默', '搞笑', '有趣', '轻松'],
            '探索': ['探索', '发现', '寻找', '冒险'],
            '领导': ['领导', '指导', '管理', '带领'],
            '反叛': ['反对', '挑战', '质疑', '改变']
        };

        const detectedTraits = [];
        for (const [trait, keywords] of Object.entries(traitKeywords)) {
            if (keywords.some(keyword => combinedText.includes(keyword))) {
                detectedTraits.push(trait.toLowerCase());
            }
        }

        // 简化的动机识别
        const motivationKeywords = [
            '帮助', '拯救', '保护', '创造', '探索', '理解', '改变', '娱乐'
        ];

        const detectedMotivations = motivationKeywords.filter(motivation =>
            combinedText.includes(motivation)
        );

        return {
            traits: detectedTraits,
            motivations: detectedMotivations
        };
    }

    /**
     * 计算分支兼容性
     */
    calculateBranchCompatibility(roleIndicators, optimalBranch) {
        const compatibility = {};

        // 基于世界分支特性计算角色兼容性
        for (const [archetypeKey, archetype] of Object.entries(this.characterArchetypes)) {
            let score = 0.1; // 基础分数

            // 根据分支类型调整兼容性
            switch (optimalBranch.key) {
                case 'fantasy':
                    if (['hero', 'magician', 'explorer'].includes(archetypeKey)) score += 0.3;
                    break;
                case 'scifi':
                    if (['explorer', 'creator', 'sage'].includes(archetypeKey)) score += 0.3;
                    break;
                case 'reality':
                    if (['caregiver', 'sage', 'ruler'].includes(archetypeKey)) score += 0.3;
                    break;
                case 'psychological':
                    if (['sage', 'caregiver', 'innocent'].includes(archetypeKey)) score += 0.3;
                    break;
                case 'philosophical':
                    if (['sage', 'rebel', 'magician'].includes(archetypeKey)) score += 0.3;
                    break;
            }

            compatibility[archetypeKey] = score;
        }

        return compatibility;
    }

    /**
     * 计算叙事进展
     */
    async calculateNarrativeProgression(worldCharacteristics, currentStates) {
        try {
            const narrativeElements = worldCharacteristics.narrative_elements;
            const currentProgression = this.currentWorldState.storyProgression;

            // 基于冲突水平和角色发展计算进展
            const conflictProgression = narrativeElements.conflict_level * 0.4;
            const developmentProgression = narrativeElements.character_development * 0.3;
            const emotionalProgression = narrativeElements.emotional_depth * 0.3;

            const baseProgression = conflictProgression + developmentProgression + emotionalProgression;

            // 应用渐进式增长
            const progressionDelta = (baseProgression - currentProgression) * 0.1;
            const newProgression = Math.max(0, Math.min(1, currentProgression + progressionDelta));

            // 确定叙事阶段
            const narrativeStage = this.determineNarrativeStage(newProgression);

            return {
                progressValue: newProgression,
                progressionDelta: progressionDelta,
                narrativeStage: narrativeStage,
                storyArc: this.calculateStoryArc(newProgression, narrativeElements),
                tensionLevel: this.calculateTensionLevel(newProgression, narrativeElements)
            };

        } catch (error) {
            this.logger.error('世界树系统', '叙事进展计算失败:', error.message);
            return { progressValue: 0.5, narrativeStage: '发展' };
        }
    }

    /**
     * 生成叙事背景
     */
    async generateNarrativeContext(optimalBranch, characterAnalysis, narrativeProgression) {
        try {
            const context = {
                worldType: optimalBranch.name,
                primaryTheme: this.selectPrimaryTheme(optimalBranch, narrativeProgression),
                narrativeTone: this.selectNarrativeTone(optimalBranch, characterAnalysis),
                environment: this.generateEnvironmentDescription(optimalBranch, narrativeProgression),
                socialDynamics: this.generateSocialDynamics(optimalBranch, characterAnalysis),
                technologyLevel: this.determineTechnologyLevel(optimalBranch),
                magicalElements: this.determineMagicalElements(optimalBranch),
                culturalContext: this.generateCulturalContext(optimalBranch),
                timeframe: this.determineTimeframe(optimalBranch, narrativeProgression),
                conflictSources: this.identifyConflictSources(optimalBranch, narrativeProgression),
                characterMotivations: this.generateCharacterMotivations(characterAnalysis, optimalBranch)
            };

            return context;

        } catch (error) {
            this.logger.error('世界树系统', '叙事背景生成失败:', error.message);
            return {
                worldType: '现实世界',
                primaryTheme: '日常生活',
                narrativeTone: '轻松',
                environment: '现代都市'
            };
        }
    }

    /**
     * 选择主要主题
     */
    selectPrimaryTheme(optimalBranch, narrativeProgression) {
        const themes = optimalBranch.themes;
        const progressionStage = narrativeProgression.narrativeStage;

        // 根据叙事阶段选择合适的主题
        const stageThemeMap = {
            '开始': themes[0] || themes[0],
            '发展': themes[1] || themes[0],
            '高潮': themes[2] || themes[1],
            '结局': themes[3] || themes[2]
        };

        return stageThemeMap[progressionStage] || themes[0];
    }

    /**
     * 计算世界状态影响
     */
    calculateWorldStateImpact(narrativeContext, currentStates) {
        try {
            const impact = {
                emotional_influence: 0,
                cognitive_influence: 0,
                behavioral_influence: 0,
                motivational_influence: 0
            };

            // 基于世界类型计算影响
            switch (narrativeContext.worldType) {
                case '奇幻世界':
                    impact.emotional_influence = 0.3;
                    impact.cognitive_influence = 0.4;
                    break;
                case '科幻世界':
                    impact.cognitive_influence = 0.5;
                    impact.behavioral_influence = 0.2;
                    break;
                case '心理世界':
                    impact.emotional_influence = 0.6;
                    impact.motivational_influence = 0.3;
                    break;
                case '哲学世界':
                    impact.cognitive_influence = 0.7;
                    impact.motivational_influence = 0.4;
                    break;
                default:
                    impact.emotional_influence = 0.1;
                    impact.cognitive_influence = 0.2;
            }

            // 基于叙事语调调整
            const toneMultipliers = {
                '严肃': 1.2,
                '轻松': 0.8,
                '神秘': 1.1,
                '幽默': 0.7,
                '史诗': 1.3,
                '深沉': 1.4
            };

            const multiplier = toneMultipliers[narrativeContext.narrativeTone] || 1.0;

            for (const key in impact) {
                impact[key] *= multiplier;
            }

            return impact;

        } catch (error) {
            this.logger.error('世界树系统', '世界状态影响计算失败:', error.message);
            return {
                emotional_influence: 0.1,
                cognitive_influence: 0.1,
                behavioral_influence: 0.1,
                motivational_influence: 0.1
            };
        }
    }

    /**
     * 计算主题共鸣
     */
    calculateThematicResonance(worldCharacteristics, optimalBranch) {
        const narrativeElements = worldCharacteristics.narrative_elements;
        const linguisticFeatures = worldCharacteristics.linguistic_features;

        // 基于复杂度匹配计算共鸣
        const complexityResonance = 1 - Math.abs(
            optimalBranch.complexityLevel - narrativeElements.thematic_complexity
        );

        // 基于语言特征计算共鸣
        const languageResonance = (
            linguisticFeatures.metaphor_usage * 0.4 +
            linguisticFeatures.abstract_concepts * 0.3 +
            linguisticFeatures.emotional_language * 0.3
        );

        return (complexityResonance + languageResonance) / 2;
    }

    /**
     * 计算沉浸水平
     */
    calculateImmersionLevel(narrativeContext, currentStates) {
        let immersion = 0.5; // 基础沉浸水平

        // 基于世界一致性
        if (narrativeContext.worldType && narrativeContext.primaryTheme) {
            immersion += 0.2;
        }

        // 基于角色一致性
        if (narrativeContext.characterMotivations) {
            immersion += 0.1;
        }

        // 基于情感状态
        if (currentStates.emotion) {
            const emotionalEngagement = Math.abs(currentStates.emotion.emotion_value) / 100;
            immersion += emotionalEngagement * 0.2;
        }

        // 基于模因活跃度
        if (currentStates.meme && currentStates.meme.memetic_influence > 0.5) {
            immersion += 0.1;
        }

        return Math.min(1.0, immersion);
    }

    /**
     * 获取备选分支
     */
    getAlternativeBranches(branchScores, optimalBranchKey) {
        return Object.entries(branchScores)
            .filter(([key, score]) => key !== optimalBranchKey)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 2)
            .map(([key, score]) => ({ key, name: this.worldBranches[key].name, score }));
    }

    /**
     * 获取次要角色
     */
    getSecondaryRoles(archetypeScores, primaryRole) {
        return Object.entries(archetypeScores)
            .filter(([key, score]) => key !== primaryRole)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 2)
            .map(([key, score]) => ({
                role: this.characterArchetypes[key].name,
                key,
                score
            }));
    }

    /**
     * 确定叙事阶段
     */
    determineNarrativeStage(progression) {
        if (progression < 0.25) return '开始';
        if (progression < 0.5) return '发展';
        if (progression < 0.75) return '高潮';
        return '结局';
    }

    /**
     * 生成环境描述
     */
    generateEnvironmentDescription(optimalBranch, narrativeProgression) {
        const baseEnvironments = {
            'reality': ['现代都市', '乡村小镇', '办公环境', '家庭空间'],
            'scifi': ['太空站', '未来城市', '外星世界', '虚拟现实'],
            'fantasy': ['魔法森林', '古老城堡', '神秘洞穴', '天空之城'],
            'historical': ['古代宫殿', '中世纪村庄', '古老图书馆', '历史战场'],
            'psychological': ['内心世界', '梦境空间', '记忆迷宫', '潜意识深处'],
            'philosophical': ['思辨空间', '概念世界', '抽象维度', '智慧殿堂']
        };

        const environments = baseEnvironments[optimalBranch.key] || baseEnvironments['reality'];
        const stageIndex = Math.floor(narrativeProgression.progressValue * environments.length);

        return environments[Math.min(stageIndex, environments.length - 1)];
    }
}

module.exports = AdvancedWorldTreeSystem;
