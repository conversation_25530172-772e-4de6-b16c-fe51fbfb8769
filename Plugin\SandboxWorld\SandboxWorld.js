/**
 * 沙盒世界主插件
 * VCPToolBox 沙盒世界生态系统
 */

const fs = require('fs').promises;
const path = require('path');
const { SandboxWorldCore } = require('./SandboxWorldCore');
const { WebInterface } = require('./WebInterface');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production'
        ? '../../utils/logger.cjs'
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

class SandboxWorld {
    constructor() {
        this.name = 'SandboxWorld';
        this.version = '1.0.0';
        this.description = '沙盒世界生态系统 - 创建一个充满活力的虚拟社会';
        
        this.worldCore = null;
        this.webInterface = null;
        this.config = null;
        
        this.isInitialized = false;
        this.isRunning = false;
    }

    /**
     * 初始化插件
     */
    async init() {
        try {
            // logger.plugin('SandboxWorld', '🌍 初始化沙盒世界插件...');
            
            // 加载配置
            await this.loadConfig();
            
            // 初始化核心系统
            this.worldCore = new SandboxWorldCore({
                worldName: this.config.world.name,
                timeSpeed: this.config.world.timeSpeed,
                maxAgents: this.config.world.maxAgents,
                autoSave: this.config.world.autoSave,
                saveInterval: this.config.world.saveInterval,
                dataDir: path.join(__dirname, 'data')
            });

            await this.worldCore.init();
            
            // 初始化Web界面
            if (this.config.ui.webInterface.enabled) {
                this.webInterface = new WebInterface(this.worldCore, this.config.ui.webInterface);
            }
            
            // 设置事件监听
            this.setupEventListeners();
            
            this.isInitialized = true;
            // logger.plugin('SandboxWorld', '✅ 沙盒世界插件初始化完成');
            
            return {
                status: 'success',
                message: '沙盒世界插件初始化成功',
                data: {
                    worldName: this.config.world.name,
                    maxAgents: this.config.world.maxAgents,
                    webInterfaceEnabled: this.config.ui.webInterface.enabled,
                    webInterfacePort: this.config.ui.webInterface.port
                }
            };
            
        } catch (error) {
            // logger.error('SandboxWorld', '❌ 沙盒世界插件初始化失败', error);
            throw error;
        }
    }

    /**
     * 加载配置：优先使用插件config.env，然后是config.json，最后是默认配置
     */
    async loadConfig() {
        try {
            // 1. 尝试加载插件的config.env文件
            const envConfig = await this.loadEnvConfig();

            // 2. 尝试加载config.json文件
            let jsonConfig = {};
            try {
                const configPath = path.join(__dirname, 'config.json');
                const configData = await fs.readFile(configPath, 'utf-8');
                jsonConfig = JSON.parse(configData);
                // logger.debug('SandboxWorld', '📋 config.json加载成功');
            } catch (error) {
                // logger.debug('SandboxWorld', '📋 config.json不存在或读取失败');
            }

            // 3. 获取默认配置
            const defaultConfig = this.getDefaultConfig();

            // 4. 合并配置：默认配置 < JSON配置 < ENV配置
            this.config = this.mergeConfigs(defaultConfig, jsonConfig, envConfig);

            // logger.debug('SandboxWorld', '📋 配置加载完成，优先级：config.env > config.json > 默认配置');

        } catch (error) {
            console.warn('⚠️ 配置加载失败，使用默认配置');
            this.config = this.getDefaultConfig();
        }
    }

    /**
     * 加载.env配置文件
     */
    async loadEnvConfig() {
        try {
            const envPath = path.join(__dirname, 'config.env');
            const envContent = await fs.readFile(envPath, 'utf-8');
            const envConfig = this.parseEnvConfig(envContent);

            // logger.debug('SandboxWorld', `📋 从config.env加载了 ${Object.keys(envConfig).length} 个配置项`);
            return this.convertEnvToConfig(envConfig);

        } catch (error) {
            // logger.debug('SandboxWorld', '📋 config.env不存在或读取失败');
            return {};
        }
    }

    /**
     * 解析.env格式的配置文件
     */
    parseEnvConfig(content) {
        const config = {};
        content.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const equalIndex = line.indexOf('=');
                if (equalIndex > 0) {
                    const key = line.substring(0, equalIndex).trim();
                    const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');

                    // 处理布尔值
                    if (value.toLowerCase() === 'true') {
                        config[key] = true;
                    } else if (value.toLowerCase() === 'false') {
                        config[key] = false;
                    } else if (!isNaN(value) && value !== '') {
                        // 处理数字
                        config[key] = parseFloat(value);
                    } else {
                        // 字符串值
                        config[key] = value;
                    }
                }
            }
        });
        return config;
    }

    /**
     * 将env配置转换为标准配置格式
     */
    convertEnvToConfig(envConfig) {
        const config = {};

        // 世界配置
        if (envConfig.WORLD_NAME) config.world = { ...config.world, name: envConfig.WORLD_NAME };
        if (envConfig.MAX_AGENTS) config.world = { ...config.world, maxAgents: envConfig.MAX_AGENTS };
        if (envConfig.TIME_SPEED) config.world = { ...config.world, timeSpeed: envConfig.TIME_SPEED };
        if (envConfig.AUTO_SAVE !== undefined) config.world = { ...config.world, autoSave: envConfig.AUTO_SAVE };
        if (envConfig.SAVE_INTERVAL) config.world = { ...config.world, saveInterval: envConfig.SAVE_INTERVAL };

        // Web界面配置
        if (envConfig.WEB_INTERFACE_ENABLED !== undefined || envConfig.WEB_INTERFACE_PORT || envConfig.WEB_INTERFACE_THEME) {
            config.ui = {
                webInterface: {
                    enabled: envConfig.WEB_INTERFACE_ENABLED !== undefined ? envConfig.WEB_INTERFACE_ENABLED : true,
                    port: envConfig.WEB_INTERFACE_PORT || 8080,
                    theme: envConfig.WEB_INTERFACE_THEME || 'dark'
                }
            };
        }

        return config;
    }

    /**
     * 合并多个配置对象
     */
    mergeConfigs(defaultConfig, jsonConfig, envConfig) {
        // 深度合并配置
        const merged = JSON.parse(JSON.stringify(defaultConfig));

        // 合并JSON配置
        this.deepMerge(merged, jsonConfig);

        // 合并ENV配置（优先级最高）
        this.deepMerge(merged, envConfig);

        return merged;
    }

    /**
     * 深度合并对象
     */
    deepMerge(target, source) {
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                if (!target[key]) target[key] = {};
                this.deepMerge(target[key], source[key]);
            } else {
                target[key] = source[key];
            }
        }
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            world: {
                name: 'VCPToolBox沙盒世界',
                maxAgents: 50,
                timeSpeed: 1,
                autoSave: true,
                saveInterval: 300000
            },
            ui: {
                webInterface: {
                    enabled: true,
                    port: 8080,
                    updateInterval: 5000
                }
            },
            integration: {
                memorySystem: { enabled: true },
                psychologySystem: { enabled: true },
                oneBot11: { enabled: true }
            }
        };
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听世界核心事件
        this.worldCore.on('worldStarted', () => {
            this.isRunning = true;
            // logger.info('SandboxWorld', '🚀 沙盒世界已启动');
        });

        this.worldCore.on('worldStopped', () => {
            this.isRunning = false;
            // logger.info('SandboxWorld', '🛑 沙盒世界已停止');
        });

        this.worldCore.on('agentAdded', (agent) => {
            // logger.info('SandboxWorld', `👤 新Agent加入: ${agent.name}`);
        });

        this.worldCore.on('agentRemoved', (agentId) => {
            // logger.info('SandboxWorld', `👤 Agent离开: ${agentId}`);
        });

        // 监听对话事件
        this.worldCore.autonomousDialogue.on('dialogueInitiated', (conversation) => {
            const initiatorName = this.getAgentName(conversation.initiator);
            const targetName = this.getAgentName(conversation.participants.find(id => id !== conversation.initiator));
            // logger.info('SandboxWorld', `💬 对话开始: ${initiatorName} -> ${targetName} (${conversation.type})`);
        });

        // 监听事件系统
        this.worldCore.eventSystem.on('eventTriggered', (event) => {
            // logger.info('SandboxWorld', `🎭 事件触发: ${event.name}`);
        });
    }

    /**
     * 获取Agent名称
     */
    getAgentName(agentId) {
        const agent = this.worldCore.agentEcosystem.getAgent(agentId);
        return agent ? agent.name : agentId;
    }

    /**
     * 启动沙盒世界
     */
    async start() {
        if (!this.isInitialized) {
            throw new Error('插件未初始化');
        }

        try {
            // 启动Web界面
            if (this.webInterface) {
                await this.webInterface.start();
            }
            
            // 启动世界核心
            await this.worldCore.startWorld();
            
            return {
                status: 'success',
                message: '沙盒世界已启动',
                data: {
                    worldState: this.worldCore.getWorldState(),
                    webInterfaceUrl: this.webInterface ? `http://localhost:${this.config.ui.webInterface.port}` : null
                }
            };
            
        } catch (error) {
            // logger.error('SandboxWorld', '❌ 启动沙盒世界失败', error);
            throw error;
        }
    }

    /**
     * 停止沙盒世界
     */
    async stop() {
        try {
            // 停止世界核心
            if (this.worldCore) {
                await this.worldCore.stopWorld();
            }
            
            // 停止Web界面
            if (this.webInterface) {
                await this.webInterface.stop();
            }
            
            return {
                status: 'success',
                message: '沙盒世界已停止'
            };
            
        } catch (error) {
            // logger.error('SandboxWorld', '❌ 停止沙盒世界失败', error);
            throw error;
        }
    }

    /**
     * 暂停沙盒世界
     */
    async pause() {
        if (!this.worldCore) {
            throw new Error('世界核心未初始化');
        }

        await this.worldCore.pauseWorld();
        
        return {
            status: 'success',
            message: '沙盒世界已暂停'
        };
    }

    /**
     * 恢复沙盒世界
     */
    async resume() {
        if (!this.worldCore) {
            throw new Error('世界核心未初始化');
        }

        await this.worldCore.resumeWorld();
        
        return {
            status: 'success',
            message: '沙盒世界已恢复'
        };
    }

    /**
     * 添加Agent
     */
    async addAgent(agentConfig) {
        if (!this.worldCore) {
            throw new Error('世界核心未初始化');
        }

        const agent = await this.worldCore.addAgent(agentConfig);
        
        return {
            status: 'success',
            message: `Agent ${agent.name} 已添加到世界`,
            data: { agent }
        };
    }

    /**
     * 移除Agent
     */
    async removeAgent(agentId) {
        if (!this.worldCore) {
            throw new Error('世界核心未初始化');
        }

        await this.worldCore.removeAgent(agentId);
        
        return {
            status: 'success',
            message: `Agent ${agentId} 已从世界移除`
        };
    }

    /**
     * 触发事件
     */
    async triggerEvent(eventType, context = {}) {
        if (!this.worldCore) {
            throw new Error('世界核心未初始化');
        }

        const success = await this.worldCore.triggerEvent(eventType, context);
        
        return {
            status: success ? 'success' : 'failed',
            message: success ? `事件 ${eventType} 已触发` : `事件 ${eventType} 触发失败`
        };
    }

    /**
     * 获取世界状态
     */
    getWorldState() {
        if (!this.worldCore) {
            return { error: '世界核心未初始化' };
        }

        return this.worldCore.getWorldState();
    }

    /**
     * 获取统计信息
     */
    async getStatistics() {
        if (!this.worldCore) {
            return { error: '世界核心未初始化' };
        }

        return await this.worldCore.getStatistics();
    }

    /**
     * 设置时间流速
     */
    setTimeSpeed(speed) {
        if (!this.worldCore) {
            throw new Error('世界核心未初始化');
        }

        this.worldCore.setTimeSpeed(speed);
        
        return {
            status: 'success',
            message: `时间流速已设置为 ${speed}x`
        };
    }

    /**
     * 获取所有Agent
     */
    getAllAgents() {
        if (!this.worldCore) {
            return [];
        }

        return this.worldCore.agentEcosystem.getAllAgents();
    }

    /**
     * 获取活跃对话
     */
    getActiveConversations() {
        if (!this.worldCore) {
            return [];
        }

        return this.worldCore.autonomousDialogue.getActiveConversations();
    }

    /**
     * 获取活跃事件
     */
    getActiveEvents() {
        if (!this.worldCore) {
            return [];
        }

        return this.worldCore.eventSystem.getActiveEvents();
    }

    /**
     * 销毁插件
     */
    async destroy() {
        try {
            await this.stop();
            
            if (this.worldCore) {
                await this.worldCore.destroy();
                this.worldCore = null;
            }
            
            this.webInterface = null;
            this.isInitialized = false;
            this.isRunning = false;
            
            // logger.plugin('SandboxWorld', '🗑️ 沙盒世界插件已销毁');
            
        } catch (error) {
            // logger.error('SandboxWorld', '❌ 销毁沙盒世界插件失败', error);
            throw error;
        }
    }

    /**
     * VCP插件接口 - 执行命令
     */
    async execute(args) {
        try {
            const command = JSON.parse(args);
            
            switch (command.action) {
                case 'init':
                    return await this.init();
                    
                case 'start':
                    return await this.start();
                    
                case 'stop':
                    return await this.stop();
                    
                case 'pause':
                    return await this.pause();
                    
                case 'resume':
                    return await this.resume();
                    
                case 'addAgent':
                    return await this.addAgent(command.agentConfig);
                    
                case 'removeAgent':
                    return await this.removeAgent(command.agentId);
                    
                case 'triggerEvent':
                    return await this.triggerEvent(command.eventType, command.context);
                    
                case 'setTimeSpeed':
                    return this.setTimeSpeed(command.speed);
                    
                case 'getStatus':
                    return {
                        status: 'success',
                        data: {
                            isInitialized: this.isInitialized,
                            isRunning: this.isRunning,
                            worldState: this.getWorldState(),
                            statistics: await this.getStatistics()
                        }
                    };
                    
                default:
                    return {
                        status: 'error',
                        message: `未知命令: ${command.action}`
                    };
            }
            
        } catch (error) {
            return {
                status: 'error',
                message: error.message,
                error: error.stack
            };
        }
    }
}

// VCP插件导出
module.exports = new SandboxWorld();
