/**
 * NapCat API 封装工具
 * 将NapCat API请求封装成更简便的调用方法
 * 参考文档: https://napneko.github.io/develop/api/doc
 */

class NapCatAPI {
    constructor(adapter) {
        this.adapter = adapter;
    }

    /**
     * 调用API的通用方法
     * @param {Object} bot - bot对象，包含ws和selfId
     * @param {string} action - API动作名称
     * @param {Object} params - API参数
     */
    async callApi(bot, action, params = {}) {
        try {
            return await this.adapter.sendApi(bot.ws, action, params);
        } catch (error) {
            console.error(`API调用失败 [${action}]: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 群聊相关API
     */
    get group() {
        return {
            // 获取群信息
            getInfo: async (bot, groupId, noCache = false) => {
                try {
                    return await this.callApi(bot, 'get_group_info', {
                        group_id: Number(groupId),
                        no_cache: Boolean(noCache)
                    });
                } catch (error) {
                    console.error(`获取群信息失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取群列表
            getList: async (bot, noCache = false) => {
                try {
                    return await this.callApi(bot, 'get_group_list', {
                        no_cache: Boolean(noCache)
                    });
                } catch (error) {
                    console.error(`获取群列表失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 群签到
            sign: async (bot, groupId) => {
                try {
                    return await this.callApi(bot, 'set_group_sign', {
                        group_id: String(groupId)
                    });
                } catch (error) {
                    console.error(`群签到失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 群聊戳一戳
            poke: async (bot, groupId, userId) => {
                try {
                    return await this.callApi(bot, 'group_poke', {
                        group_id: Number(groupId),
                        user_id: Number(userId)
                    });
                } catch (error) {
                    console.error(`群聊戳一戳失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 设置群聊已读
            markAsRead: async (bot, groupId) => {
                try {
                    return await this.callApi(bot, 'mark_group_msg_as_read', {
                        group_id: Number(groupId)
                    });
                } catch (error) {
                    console.error(`设置群聊已读失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 发送AI语音
            sendAiRecord: async (bot, groupId, character, text) => {
                try {
                    return await this.callApi(bot, 'send_group_ai_record', {
                        group_id: Number(groupId),
                        character: String(character),
                        text: String(text)
                    });
                } catch (error) {
                    console.error(`发送AI语音失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取AI角色列表
            getAiCharacters: async (bot, groupId, chatType = 1) => {
                try {
                    return await this.callApi(bot, 'get_ai_characters', {
                        group_id: Number(groupId),
                        chat_type: Number(chatType)
                    });
                } catch (error) {
                    console.error(`获取AI角色列表失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取推荐群聊卡片
            getShareCard: async (bot, groupId) => {
                try {
                    return await this.callApi(bot, 'ArkShareGroup', {
                        group_id: String(groupId)
                    });
                } catch (error) {
                    console.error(`获取推荐群聊卡片失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            }
        };
    }

    /**
     * 私聊相关API
     */
    get friend() {
        return {
            // 私聊戳一戳
            poke: async (bot, userId) => {
                try {
                    return await this.callApi(bot, 'friend_poke', {
                        user_id: Number(userId)
                    });
                } catch (error) {
                    console.error(`私聊戳一戳失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 设置私聊已读
            markAsRead: async (bot, userId) => {
                try {
                    return await this.callApi(bot, 'mark_private_msg_as_read', {
                        user_id: Number(userId)
                    });
                } catch (error) {
                    console.error(`设置私聊已读失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取私聊历史记录
            getHistory: async (userId, messageSeq = '0', count = 20, reverseOrder = false) => {
                try {
                    return await this.adapter.callApi('get_friend_msg_history', {
                        user_id: String(userId),
                        message_seq: String(messageSeq),
                        count: Number(count),
                        reverseOrder: Boolean(reverseOrder)
                    });
                } catch (error) {
                    console.error(`获取私聊历史记录失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取推荐好友卡片
            getShareCard: async (bot, userId, phoneNumber = '') => {
                try {
                    return await this.callApi(bot, 'ArkSharePeer', {
                        user_id: String(userId),
                        phoneNumber: String(phoneNumber)
                    });
                } catch (error) {
                    console.error(`获取推荐好友卡片失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            }
        };
    }

    /**
     * 消息相关API
     */
    get message() {
        return {
            // 获取消息
            get: async (bot, messageId) => {
                try {
                    return await this.callApi(bot, 'get_msg', {
                        message_id: Number(messageId)
                    });
                } catch (error) {
                    console.error(`获取消息失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 转发消息到私聊
            forwardToFriend: async (messageId, userId) => {
                try {
                    return await this.adapter.callApi('forward_friend_single_msg', {
                        message_id: Number(messageId),
                        user_id: Number(userId)
                    });
                } catch (error) {
                    console.error(`转发消息到私聊失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 转发消息到群聊
            forwardToGroup: async (messageId, groupId) => {
                try {
                    return await this.adapter.callApi('forward_group_single_msg', {
                        message_id: Number(messageId),
                        group_id: Number(groupId)
                    });
                } catch (error) {
                    console.error(`转发消息到群聊失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 设置表情回复
            setEmojiLike: async (messageId, emojiId) => {
                try {
                    return await this.adapter.callApi('set_msg_emoji_like', {
                        message_id: Number(messageId),
                        emoji_id: String(emojiId)
                    });
                } catch (error) {
                    console.error(`设置表情回复失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 发送合并转发
            sendForward: async (messageType, targetId, messages) => {
                try {
                    const params = {
                        message_type: messageType,
                        messages: messages
                    };
                    
                    if (messageType === 'private') {
                        params.user_id = Number(targetId);
                    } else if (messageType === 'group') {
                        params.group_id = Number(targetId);
                    }
                    
                    return await this.adapter.callApi('send_forward_msg', params);
                } catch (error) {
                    console.error(`发送合并转发失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            }
        };
    }

    /**
     * 文件相关API
     */
    get file() {
        return {
            // 获取文件信息
            getInfo: async (bot, fileId) => {
                try {
                    return await this.callApi(bot, 'get_file', {
                        file_id: String(fileId)
                    });
                } catch (error) {
                    console.error(`获取文件信息失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            }
        };
    }

    /**
     * 用户相关API
     */
    get user() {
        return {
            // 获取用户信息（陌生人信息）
            getInfo: async (bot, userId, noCache = false) => {
                try {
                    return await this.callApi(bot, 'get_stranger_info', {
                        user_id: Number(userId),
                        no_cache: Boolean(noCache)
                    });
                } catch (error) {
                    console.error(`获取用户信息失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取群成员信息
            getGroupMemberInfo: async (bot, groupId, userId, noCache = false) => {
                try {
                    return await this.callApi(bot, 'get_group_member_info', {
                        group_id: Number(groupId),
                        user_id: Number(userId),
                        no_cache: Boolean(noCache)
                    });
                } catch (error) {
                    console.error(`获取群成员信息失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },



            // 获取群成员列表
            getGroupMemberList: async (bot, groupId, noCache = false) => {
                try {
                    return await this.callApi(bot, 'get_group_member_list', {
                        group_id: Number(groupId),
                        no_cache: Boolean(noCache)
                    });
                } catch (error) {
                    console.error(`获取群成员列表失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取好友列表
            getFriendList: async (bot) => {
                try {
                    return await this.callApi(bot, 'get_friend_list', {});
                } catch (error) {
                    console.error(`获取好友列表失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 设置在线状态
            setOnlineStatus: async (bot, status, extStatus = 0, batteryStatus = 100) => {
                try {
                    return await this.callApi(bot, 'set_online_status', {
                        status: Number(status),
                        ext_status: Number(extStatus),
                        battery_status: Number(batteryStatus)
                    });
                } catch (error) {
                    console.error(`设置在线状态失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 设置QQ头像
            setAvatar: async (bot, file) => {
                try {
                    return await this.callApi(bot, 'set_qq_avatar', {
                        file: String(file)
                    });
                } catch (error) {
                    console.error(`设置QQ头像失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 设置签名
            setSignature: async (bot, longNick) => {
                try {
                    return await this.callApi(bot, 'set_self_longnick', {
                        longNick: String(longNick)
                    });
                } catch (error) {
                    console.error(`设置签名失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取分类的好友列表
            getFriendsWithCategory: async (bot) => {
                try {
                    return await this.callApi(bot, 'get_friends_with_category', {});
                } catch (error) {
                    console.error(`获取分类的好友列表失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取自身点赞列表
            getProfileLike: async (bot) => {
                try {
                    return await this.callApi(bot, 'get_profile_like', {});
                } catch (error) {
                    console.error(`获取自身点赞列表失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            }
        };
    }

    /**
     * 工具相关API
     */
    get tools() {
        return {
            // 英译中
            translateEn2Zh: async (bot, words) => {
                try {
                    return await this.callApi(bot, 'translate_en2zh', {
                        words: Array.isArray(words) ? words : [String(words)]
                    });
                } catch (error) {
                    console.error(`英译中失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // AI文字转语音
            getAiRecord: async (bot, character, groupId, text) => {
                try {
                    return await this.callApi(bot, 'get_ai_record', {
                        character: String(character),
                        group_id: Number(groupId),
                        text: String(text)
                    });
                } catch (error) {
                    console.error(`AI文字转语音失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取机器人账号范围
            getRobotUinRange: async (bot) => {
                try {
                    return await this.callApi(bot, 'get_robot_uin_range');
                } catch (error) {
                    console.error(`获取机器人账号范围失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取自定义表情
            getCustomFace: async (bot, count = 48) => {
                try {
                    return await this.callApi(bot, 'fetch_custom_face', {
                        count: Number(count)
                    });
                } catch (error) {
                    console.error(`获取自定义表情失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取最近联系人
            getRecentContact: async (bot, count = 10) => {
                try {
                    return await this.callApi(bot, 'get_recent_contact', {
                        count: Number(count)
                    });
                } catch (error) {
                    console.error(`获取最近联系人失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 标记所有已读
            markAllAsRead: async (bot) => {
                try {
                    return await this.callApi(bot, '_mark_all_as_read');
                } catch (error) {
                    console.error(`标记所有已读失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            }
        };
    }

    /**
     * 收藏相关API
     */
    get collection() {
        return {
            // 创建收藏
            create: async (data) => {
                try {
                    return await this.adapter.callApi('create_collection', data);
                } catch (error) {
                    console.error(`创建收藏失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            },

            // 获取收藏列表
            getList: async () => {
                try {
                    return await this.adapter.callApi('get_collection_list');
                } catch (error) {
                    console.error(`获取收藏列表失败: ${error.message}`);
                    return { success: false, error: error.message };
                }
            }
        };
    }

    /**
     * 通用戳一戳（自动判断群聊/私聊）
     * 使用NapCat的send_poke API
     */
    async poke(bot, userId, groupId = null) {
        try {
            const params = { user_id: Number(userId) };
            if (groupId) {
                params.group_id = Number(groupId);
            }
            return await this.callApi(bot, 'send_poke', params);
        } catch (error) {
            console.error(`戳一戳失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取登录信息（机器人自己的信息）
     */
    async getLoginInfo(bot) {
        try {
            return await this.callApi(bot, 'get_login_info', {});
        } catch (error) {
            console.error(`获取登录信息失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 快速调用方法 - 常用功能的简化调用
     */
    get quick() {
        return {
            // 快速戳一戳
            poke: this.poke.bind(this),

            // 快速群签到
            groupSign: this.group.sign,

            // 快速设置已读
            markRead: (target, isGroup = false) => {
                return isGroup ? this.group.markAsRead(target) : this.friend.markAsRead(target);
            },

            // 快速获取文件信息
            getFile: this.file.getInfo,

            // 快速翻译
            translate: this.tools.translateEn2Zh,

            // 快速AI语音
            aiSpeak: this.tools.getAiRecord
        };
    }
}

// 导出类和创建实例的工厂函数
module.exports = {
    NapCatAPI,

    /**
     * 创建NapCat API实例
     * @param {Object} adapter - OneBot适配器实例
     * @returns {NapCatAPI} API实例
     */
    createNapCatAPI: (adapter) => {
        return new NapCatAPI(adapter);
    }
};
