/**
 * Puppeteer安装脚本
 * 自动安装最新版本的Puppeteer并进行环境检查
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始安装最新版本的Puppeteer...');

async function installPuppeteer() {
    try {
        // 检查是否已安装
        try {
            require('puppeteer');
            console.log('✅ Puppeteer已安装，检查版本...');
            
            const puppeteer = require('puppeteer');
            console.log(`📦 当前版本: ${puppeteer.version || '未知'}`);
            
        } catch (error) {
            console.log('📦 Puppeteer未安装，开始安装...');
        }

        // 安装最新版本
        console.log('⬇️  正在安装Puppeteer最新版本...');
        execSync('npm install puppeteer@latest', { 
            stdio: 'inherit',
            cwd: path.join(__dirname, '..')
        });

        // 验证安装
        const puppeteer = require('puppeteer');
        console.log('✅ Puppeteer安装成功！');
        console.log(`📦 版本: ${puppeteer.version || '最新版'}`);

        // 环境检查
        console.log('🔍 开始环境检查...');
        await environmentCheck();

        console.log('🎉 安装完成！您可以开始使用渲染器了。');
        
    } catch (error) {
        console.error('❌ 安装失败:', error.message);
        console.log('💡 建议解决方案:');
        console.log('1. 检查网络连接');
        console.log('2. 尝试使用国内镜像:');
        console.log('   npm config set puppeteer_download_host=https://npm.taobao.org/mirrors');
        console.log('3. 手动安装: npm install puppeteer@latest');
        process.exit(1);
    }
}

async function environmentCheck() {
    try {
        const puppeteer = require('puppeteer');
        
        console.log('🌐 启动浏览器测试...');
        const browser = await puppeteer.launch({ 
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        console.log('📄 创建测试页面...');
        const page = await browser.newPage();
        
        await page.setContent(`
            <html>
                <body>
                    <h1>Puppeteer环境测试</h1>
                    <p>如果您能看到这条消息，说明环境配置正确！</p>
                </body>
            </html>
        `);
        
        console.log('📸 测试截图功能...');
        const testImagePath = path.join(__dirname, '../image/test.png');
        
        // 确保目录存在
        const imageDir = path.dirname(testImagePath);
        if (!fs.existsSync(imageDir)) {
            fs.mkdirSync(imageDir, { recursive: true });
        }
        
        await page.screenshot({ 
            path: testImagePath,
            type: 'png'
        });
        
        await browser.close();
        
        // 检查文件是否生成
        if (fs.existsSync(testImagePath)) {
            console.log('✅ 截图测试成功！');
            console.log(`📸 测试图片: ${testImagePath}`);
            
            // 删除测试文件
            fs.unlinkSync(testImagePath);
            console.log('🗑️  测试文件已清理');
        } else {
            throw new Error('截图文件未生成');
        }
        
        console.log('✅ 环境检查通过！');
        
    } catch (error) {
        console.error('❌ 环境检查失败:', error.message);
        console.log('💡 可能的问题:');
        console.log('1. 缺少必要的系统依赖');
        console.log('2. 权限问题');
        console.log('3. 内存不足');
        throw error;
    }
}

// 运行安装
if (require.main === module) {
    installPuppeteer().catch(console.error);
}

module.exports = { installPuppeteer, environmentCheck }; 