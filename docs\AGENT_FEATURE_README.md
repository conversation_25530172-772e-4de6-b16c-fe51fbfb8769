# Agent参数功能说明

## 功能概述

agent参数允许您在API请求中指定一个预定义的Agent配置文件，系统会自动加载对应的Agent配置文件并替换所有用户提供的system消息。

## 使用方法

### 基本语法

在API请求中添加 `agent` 参数：

```json
{
    "model": "your-model",
    "agent": "AgentName",
    "messages": [
        {
            "role": "system",
            "content": "这个system消息会被agent文件内容替换"
        },
        {
            "role": "user", 
            "content": "你的用户消息"
        }
    ]
}
```

### 支持的模式

- **VCP模式**: `"type": "vcp"`
- **MCP模式**: `"type": "mcp"`

两种模式都完全支持agent参数功能。

## Agent文件格式

Agent文件存储在 `Agent/` 目录下，文件名格式为 `{AgentName}.txt`。

### 示例Agent文件

#### Nova.txt
```
Nova的日记本:{{Nova日记本}}。你是一个测试AI,Nova。我是你的主人——{{VarUser}}。{{TarSysPrompt}}系统信息是{{VarSystemInfo}}。系统工具列表：{{VarToolList}}。{{VarDailyNoteGuide}}额外指令:{{SarThink}} 表情包系统:{{TarEmojiPrompt}} VCP工具系统:{{VarVCPGuide}}
```

#### TestAgent.txt
```
测试Agent的个人信息：{{TestAgent日记本}}。你是一个专门用于测试的AI助手，名字叫TestAgent。你的主人是{{VarUser}}。

系统配置：{{TarSysPrompt}}
当前系统信息：{{VarSystemInfo}}
可用工具列表：{{VarToolList}}

日记系统指南：{{VarDailyNoteGuide}}

特殊思考模式：{{SarThink}}

表情包系统：{{TarEmojiPrompt}}
你可以使用各种表情包来增强交流效果。

VCP工具系统：{{VarVCPGuide}}
你可以调用各种VCP工具来帮助用户完成任务。

作为测试Agent，你应该：
1. 详细展示各种功能的使用方法
2. 对用户的测试请求给出明确的反馈
3. 展示工具调用的完整流程
4. 提供清晰的操作说明和结果解释

请始终保持友好、专业且富有帮助性的态度。
```

## 完整请求示例

### VCP模式示例

```javascript
const requestBody = {
    model: 'gemini-2.5-flash-preview-05-20',
    type: "vcp",
    agent: "Nova",
    memory_tracking: true,
    enable_context: true,
    userId: '雨安',
    stream: false,
    messages: [
        { 
            role: "system", 
            content: "这个消息会被Nova.txt内容替换" 
        },
        { 
            role: "user", 
            content: "你好，请介绍一下你自己" 
        }
    ]
};
```

### MCP模式示例

```javascript
const requestBody = {
    model: 'gemini-2.5-flash-preview-05-20',
    type: "mcp",
    agent: "TestAgent",
    memory_tracking: true,
    render_as_image: true,
    userId: 'test_user',
    stream: false,
    messages: [
        { 
            role: "system", 
            content: "这个消息会被TestAgent.txt内容替换" 
        },
        { 
            role: "user", 
            content: "请展示你的测试功能" 
        }
    ]
};
```

## 工作原理

1. **参数检测**: 系统检测到 `agent` 参数后，会尝试从 `Agent/` 目录加载对应的文件
2. **文件验证**: 检查 `Agent/{AgentName}.txt` 文件是否存在
3. **消息替换**: 如果文件存在，系统会：
   - 移除所有用户提供的 `system` 角色消息
   - 将Agent文件内容作为新的system消息添加到消息列表的第一位
4. **变量处理**: 对Agent文件内容进行变量替换处理（如 `{{VarUser}}`、`{{TarSysPrompt}}` 等）
5. **继续处理**: 继续执行后续的图片处理、工具调用等流程

## 错误处理

- 如果指定的Agent文件不存在，系统会记录警告日志但不会中断请求处理
- 如果Agent文件读取失败，系统会记录错误日志但继续使用原始消息
- 在错误情况下，用户原始的system消息会被保留

## 日志输出

当使用agent参数时，系统会输出相关日志：

```
[Agent系统] 尝试加载Agent: Nova
[Agent系统] 成功加载Agent Nova，已替换system消息
```

或者在MCP模式下：

```
[Agent系统] [MCP模式] 尝试加载Agent: TestAgent
[Agent系统] [MCP模式] 成功加载Agent TestAgent，已替换system消息
```

## 测试脚本

项目中包含了测试脚本 `test_agent_mode.js`，可以用来测试agent功能：

```bash
node test_agent_mode.js
```

该脚本会测试：
- Nova Agent在VCP模式下的工作
- TestAgent在MCP模式下的工作
- 不存在的Agent的错误处理

## 注意事项

1. Agent文件必须是UTF-8编码的文本文件
2. Agent参数会被从最终发送给AI API的请求中移除
3. 变量替换会在Agent文件加载后进行
4. 支持所有现有的功能，如图片渲染、工具调用、对话记录等
5. 在VCP和MCP模式下行为完全一致

## 最新更新（v2.0）

### 新增功能
- ✅ **MCP模式日记处理**: MCP模式现在完全支持日记功能，包括：
  - 直接对话的日记处理
  - 工具调用后的日记处理
  - 与VCP模式完全一致的`<<<DailyNoteStart>>>`格式支持
- ✅ **图片渲染参数修复**: 修复了`userId`、`assistantName`参数失效的问题：
  - 支持`user_name`和`assistant_name`参数
  - 向后兼容原有的`userId`和`assistantName`参数
  - 在所有渲染场景中（VCP流式、VCP非流式、MCP直接、MCP工具）统一参数处理
- ✅ **功能统一**: VCP和MCP模式现在拥有完全一致的功能特性

### 修复的问题
- 🔧 修复了MCP模式缺少日记处理功能的问题
- 🔧 修复了图片渲染中`userId`和`assistantName`参数被忽略的问题
- 🔧 统一了所有模式的参数命名规范

### 测试文件
- 新增：`test_mcp_diary.js` - 专门测试MCP模式的日记功能
- 更新：`test_agent_mode.js` - 包含修复后的参数测试

### 使用示例

现在您可以在任何模式下使用以下参数组合：

```javascript
{
    type: "mcp", // 或 "vcp"
    agent: "Nova", // 使用Agent文件
    memory_tracking: true,
    userId: '雨安',
    user_name: '雨安', // 用于图片渲染
    assistant_name: '失语症', // 用于图片渲染
    render_as_image: true,
    // ... 其他参数
}
``` 