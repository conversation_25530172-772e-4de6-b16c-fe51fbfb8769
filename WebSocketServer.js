// WebSocketServer.js
const WebSocket = require('ws');
const url = require('url');

// 引入VCP日志系统
let logger;
try {
    logger = require('./utils/logger.js').default;
} catch (e) {
    // 回退到传统日志
    logger = {
        websocket: (msg) => console.log(`[WebSocket] ${msg}`),
        error: (component, msg) => console.error(`[${component}] ${msg}`),
        warning: (component, msg) => console.warn(`[${component}] ${msg}`),
        debug: (component, msg) => console.log(`[${component}] ${msg}`)
    };
}

let wssInstance;
let serverConfig = {
    debugMode: false,
    vcpKey: null
};

// 用于存储已连接并认证的客户端
const clients = new Map(); // 使用 Map 存储，key 可以是 clientId, value 是 ws 连接实例

function generateClientId() {
    return Math.random().toString(36).substring(2, 15);
}

async function writeLog(message) {
    // 使用VCP日志系统
    if (serverConfig.debugMode) {
        logger.websocket(message);
    }
}

function initialize(httpServer, config) {
    if (!httpServer) {
        logger.error('WebSocket服务器', '无法在没有HTTP服务器实例的情况下初始化');
        return;
    }
    serverConfig = { ...serverConfig, ...config };

    if (!serverConfig.vcpKey && serverConfig.debugMode) {
        logger.warning('WebSocket服务器', 'VCP_Key 未设置。如果使用默认路径，WebSocket连接将不会被认证');
    }

    wssInstance = new WebSocket.Server({ noServer: true });

    httpServer.on('upgrade', (request, socket, head) => {
        const parsedUrl = url.parse(request.url, true);
        const pathname = parsedUrl.pathname;
        
        // 示例：VCPLog 的路径和认证，可以扩展为更通用的路径处理和认证机制
        // 例如，路径可以指示消息的目标插件或频道
        const vcpLogPathRegex = /^\/VCPlog\/VCP_Key=(.+)$/;
        const vcpMatch = pathname.match(vcpLogPathRegex);

        let isAuthenticated = false;
        let clientId = null;
        let clientType = null; // 可以用来区分不同类型的客户端或插件订阅

        if (vcpMatch && vcpMatch[1]) {
            const providedKey = vcpMatch[1];
            if (serverConfig.vcpKey && providedKey === serverConfig.vcpKey) {
                isAuthenticated = true;
                clientType = 'VCPLog'; // 标记这个连接是为 VCPLog 服务的
                writeLog(`VCPLog客户端尝试连接，密钥: ${providedKey}`);
            } else {
                writeLog(`VCPLog客户端连接被拒绝。无效或缺失VCP_Key。提供的密钥: ${providedKey}`);
                socket.destroy();
                return;
            }
        } else if (pathname === '/wechat-adapter') {
            // WeChat适配器WebSocket连接（无需特殊认证）
            isAuthenticated = true;
            clientType = 'WeChat';
            writeLog('WeChat适配器客户端尝试连接');
        } else {
            // 未来可以为其他插件或通用 WebSocket 连接定义不同的路径和认证
            // 例如: /ws/pluginName?token=xxx
            // 对于未知路径，可以选择拒绝或允许（如果不需要认证）
            writeLog(`WebSocket升级请求的未处理路径: ${pathname}。忽略中`);
            // socket.destroy(); // 如果只允许特定路径，则销毁
            return; // 当前只处理 VCPLog 和 WeChat 路径
        }

        if (isAuthenticated) {
            wssInstance.handleUpgrade(request, socket, head, (ws) => {
                clientId = generateClientId();
                ws.clientId = clientId;
                ws.clientType = clientType; // 存储客户端类型
                clients.set(clientId, ws);
                wssInstance.emit('connection', ws, request);
                writeLog(`客户端 ${clientId} (类型: ${clientType}) 已认证并连接`);
            });
        } else {
            // 此处理论上不会到达，因为上面已经 destroy 或 return
            writeLog(`WebSocket路径认证失败: ${pathname}`);
            socket.destroy();
        }
    });

    wssInstance.on('connection', (ws, request) => {
        if (serverConfig.debugMode) {
            logger.websocket(`客户端 ${ws.clientId} 已连接`);
        }

        // 发送连接确认消息给特定类型的客户端
        if (ws.clientType === 'VCPLog') {
            ws.send(JSON.stringify({ type: 'connection_ack', message: 'VCPLog WebSocket连接成功' }));
        } else if (ws.clientType === 'WeChat') {
            ws.send(JSON.stringify({ type: 'connection_ack', message: 'WeChat适配器WebSocket连接成功' }));
        }
        // 可以根据 ws.clientType 或其他标识符发送不同的欢迎消息

        ws.on('message', (message) => {
            if (serverConfig.debugMode) {
                logger.websocket(`从客户端 ${ws.clientId} 接收到消息: ${message}`);
            }
            // TODO: 处理来自客户端的消息
            // 例如，根据消息内容或 ws.clientType 将消息路由到特定插件
            // let parsedMessage;
            // try {
            //     parsedMessage = JSON.parse(message);
            //     if (parsedMessage.targetPlugin && parsedMessage.payload) {
            //         // Route to pluginManager.callPluginMethod(parsedMessage.targetPlugin, parsedMessage.payload)
            //     }
            // } catch (e) {
            //     logger.error('WebSocket服务器', `解析客户端消息失败: ${message}`);
            // }
        });

        ws.on('close', () => {
            clients.delete(ws.clientId);
            if (serverConfig.debugMode) {
                logger.websocket(`客户端 ${ws.clientId} 已断开连接`);
            }
            writeLog(`客户端 ${ws.clientId} 已断开连接`);
        });

        ws.on('error', (error) => {
            logger.error('WebSocket服务器', `客户端 ${ws.clientId} 发生错误: ${error.message}`);
            writeLog(`客户端 ${ws.clientId} WebSocket错误: ${error.message}`);
            // 确保在出错时也从 clients Map 中移除
            if(ws.clientId) clients.delete(ws.clientId);
        });
    });

    if (serverConfig.debugMode) {
        logger.websocket('WebSocket服务器已初始化。等待HTTP服务器升级');
    }
}

// 广播给所有已连接且认证的客户端，或者根据 clientType 筛选
function broadcast(data, targetClientType = null) {
    if (wssInstance && wssInstance.clients) {
        const messageString = JSON.stringify(data);
        clients.forEach(clientWs => {
            if (clientWs.readyState === WebSocket.OPEN) {
                if (targetClientType === null || clientWs.clientType === targetClientType) {
                    clientWs.send(messageString);
                }
            }
        });
        writeLog(`已广播 (目标: ${targetClientType || '全部'}): ${JSON.stringify(data)}`);
    }
}

// 发送给特定客户端
function sendMessageToClient(clientId, data) {
    const clientWs = clients.get(clientId);
    if (clientWs && clientWs.readyState === WebSocket.OPEN) {
        clientWs.send(JSON.stringify(data));
        writeLog(`已发送消息给客户端 ${clientId}: ${JSON.stringify(data)}`);
        return true;
    }
    writeLog(`发送消息给客户端 ${clientId} 失败: 未找到或连接未打开`);
    return false;
}

function shutdown() {
    if (serverConfig.debugMode) {
        logger.websocket('正在关闭WebSocket服务器...');
    }
    if (wssInstance) {
        wssInstance.clients.forEach(client => {
            client.close();
        });
        wssInstance.close(() => {
            if (serverConfig.debugMode) {
                logger.websocket('WebSocket服务器已关闭');
            }
        });
    }
    writeLog('WebSocket服务器已关闭');
}

module.exports = {
    initialize,
    broadcast,
    sendMessageToClient,
    shutdown
};