/**
 * 沙盒世界测试脚本
 * 用于测试系统功能
 */

const SandboxWorld = require('./SandboxWorld');

async function testSandboxWorld() {
    console.log('🧪 开始测试沙盒世界系统...\n');
    
    try {
        // 1. 测试初始化
        console.log('1️⃣ 测试系统初始化...');
        const initResult = await SandboxWorld.execute(JSON.stringify({
            action: 'init'
        }));
        console.log('初始化结果:', initResult);
        
        if (initResult.status !== 'success') {
            throw new Error('初始化失败');
        }
        console.log('✅ 初始化测试通过\n');
        
        // 2. 测试启动
        console.log('2️⃣ 测试系统启动...');
        const startResult = await SandboxWorld.execute(JSON.stringify({
            action: 'start'
        }));
        console.log('启动结果:', startResult);
        
        if (startResult.status !== 'success') {
            throw new Error('启动失败');
        }
        console.log('✅ 启动测试通过\n');
        
        // 3. 测试添加Agent
        console.log('3️⃣ 测试添加Agent...');
        const addAgentResult = await SandboxWorld.execute(JSON.stringify({
            action: 'addAgent',
            agentConfig: {
                name: '测试Agent',
                age: 25,
                gender: '未知'
            }
        }));
        console.log('添加Agent结果:', addAgentResult);
        
        if (addAgentResult.status !== 'success') {
            console.log('⚠️ 添加Agent失败，但继续测试');
        } else {
            console.log('✅ 添加Agent测试通过\n');
        }
        
        // 4. 测试获取状态
        console.log('4️⃣ 测试获取状态...');
        const statusResult = await SandboxWorld.execute(JSON.stringify({
            action: 'getStatus'
        }));
        console.log('状态结果:', JSON.stringify(statusResult, null, 2));
        
        if (statusResult.status !== 'success') {
            throw new Error('获取状态失败');
        }
        console.log('✅ 状态测试通过\n');
        
        // 5. 测试触发事件
        console.log('5️⃣ 测试触发事件...');
        const eventResult = await SandboxWorld.execute(JSON.stringify({
            action: 'triggerEvent',
            eventType: 'festival_announcement'
        }));
        console.log('事件结果:', eventResult);
        
        if (eventResult.status !== 'success') {
            console.log('⚠️ 触发事件失败，但继续测试');
        } else {
            console.log('✅ 事件测试通过\n');
        }
        
        // 6. 等待一段时间观察系统运行
        console.log('6️⃣ 观察系统运行 (10秒)...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // 7. 再次获取状态
        const finalStatusResult = await SandboxWorld.execute(JSON.stringify({
            action: 'getStatus'
        }));
        console.log('最终状态:', JSON.stringify(finalStatusResult.data?.statistics, null, 2));
        
        // 8. 测试停止
        console.log('7️⃣ 测试系统停止...');
        const stopResult = await SandboxWorld.execute(JSON.stringify({
            action: 'stop'
        }));
        console.log('停止结果:', stopResult);
        
        if (stopResult.status !== 'success') {
            throw new Error('停止失败');
        }
        console.log('✅ 停止测试通过\n');
        
        console.log('🎉 所有测试完成！系统运行正常。');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
        
        // 尝试停止系统
        try {
            await SandboxWorld.execute(JSON.stringify({
                action: 'stop'
            }));
        } catch (stopError) {
            console.error('停止系统时也发生错误:', stopError.message);
        }
        
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testSandboxWorld();
}

module.exports = testSandboxWorld;
