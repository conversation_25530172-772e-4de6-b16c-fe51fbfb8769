/**
 * 浏览器安装和配置助手
 * 帮助用户自动检测、安装和配置浏览器环境
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync, spawn } = require('child_process');
const { detectChromePath, findBrowserByCommand } = require('./puppeteer-utils');

class BrowserSetup {
    constructor() {
        this.platform = os.platform();
        this.arch = os.arch();
        this.userDataDir = path.join(os.homedir(), '.puppeteer-browser');
    }

    /**
     * 检查浏览器环境
     */
    async checkBrowserEnvironment() {
        console.log('🔍 检查浏览器环境...\n');
        
        const report = {
            system: {
                platform: this.platform,
                arch: this.arch,
                nodeVersion: process.version
            },
            browsers: [],
            puppeteer: null,
            recommendations: []
        };

        // 检测系统浏览器
        const detectedPath = detectChromePath();
        const commandPath = findBrowserByCommand();
        
        if (detectedPath) {
            try {
                const version = await this.getBrowserVersion(detectedPath);
                report.browsers.push({
                    name: 'Chrome/Chromium (检测到)',
                    path: detectedPath,
                    version: version,
                    status: 'available'
                });
                console.log('✅ 找到系统浏览器:', detectedPath);
            } catch (error) {
                report.browsers.push({
                    name: 'Chrome/Chromium (检测到)',
                    path: detectedPath,
                    version: 'unknown',
                    status: 'error',
                    error: error.message
                });
                console.log('⚠️  系统浏览器存在但无法获取版本:', error.message);
            }
        }

        // 检查Puppeteer状态
        try {
            const puppeteer = require('puppeteer');
            report.puppeteer = {
                version: puppeteer.version || 'unknown',
                status: 'installed'
            };
            console.log('✅ Puppeteer已安装:', puppeteer.version);
        } catch (error) {
            report.puppeteer = {
                status: 'not_installed',
                error: error.message
            };
            console.log('❌ Puppeteer未安装或有问题');
            report.recommendations.push('安装Puppeteer: npm install puppeteer');
        }

        // 检查环境变量
        if (process.env.PUPPETEER_EXECUTABLE_PATH) {
            console.log('📋 发现环境变量 PUPPETEER_EXECUTABLE_PATH:', process.env.PUPPETEER_EXECUTABLE_PATH);
            report.browsers.push({
                name: '环境变量指定',
                path: process.env.PUPPETEER_EXECUTABLE_PATH,
                version: 'unknown',
                status: fs.existsSync(process.env.PUPPETEER_EXECUTABLE_PATH) ? 'available' : 'missing'
            });
        }

        // 生成建议
        if (report.browsers.length === 0) {
            report.recommendations.push('安装Chrome浏览器: https://www.google.com/chrome/');
            report.recommendations.push('或安装Microsoft Edge: https://www.microsoft.com/edge');
        }

        return report;
    }

    /**
     * 获取浏览器版本
     */
    async getBrowserVersion(browserPath) {
        try {
            const command = `"${browserPath}" --version`;
            const result = execSync(command, { encoding: 'utf8', timeout: 5000 });
            return result.trim();
        } catch (error) {
            return 'unknown';
        }
    }

    /**
     * 自动安装Chromium
     */
    async autoInstallChromium() {
        console.log('🚀 开始自动安装Chromium...\n');
        
        try {
            // 方案1：使用@puppeteer/browsers
            try {
                console.log('尝试使用 @puppeteer/browsers 安装...');
                const { install, computeExecutablePath } = require('@puppeteer/browsers');
                
                const installed = await install({
                    browser: 'chrome',
                    buildId: 'latest',
                    cacheDir: this.userDataDir
                });
                
                const executablePath = computeExecutablePath({
                    browser: 'chrome',
                    buildId: installed.buildId,
                    cacheDir: this.userDataDir
                });
                
                console.log('✅ Chromium安装成功:', executablePath);
                return executablePath;
                
            } catch (modernError) {
                console.log('现代安装方案失败，尝试传统方案...');
                
                // 方案2：使用传统puppeteer API
                const puppeteer = require('puppeteer');
                const browserFetcher = puppeteer.createBrowserFetcher({
                    path: this.userDataDir
                });
                
                console.log('下载Chromium中，请稍候...');
                const revisionInfo = await browserFetcher.download('1069273');
                
                console.log('✅ Chromium安装成功:', revisionInfo.executablePath);
                return revisionInfo.executablePath;
            }
            
        } catch (error) {
            console.error('❌ Chromium自动安装失败:', error.message);
            throw error;
        }
    }

    /**
     * 创建浏览器启动脚本
     */
    createLaunchScript(browserPath) {
        const scriptDir = path.join(__dirname, 'scripts');
        if (!fs.existsSync(scriptDir)) {
            fs.mkdirSync(scriptDir, { recursive: true });
        }

        const scriptContent = this.platform === 'win32' 
            ? this.generateWindowsScript(browserPath)
            : this.generateUnixScript(browserPath);

        const scriptName = this.platform === 'win32' ? 'launch-browser.bat' : 'launch-browser.sh';
        const scriptPath = path.join(scriptDir, scriptName);
        
        fs.writeFileSync(scriptPath, scriptContent);
        
        if (this.platform !== 'win32') {
            fs.chmodSync(scriptPath, '755');
        }
        
        console.log('📝 创建启动脚本:', scriptPath);
        return scriptPath;
    }

    generateWindowsScript(browserPath) {
        return `@echo off
echo 启动浏览器测试...
"${browserPath}" --version
if %ERRORLEVEL% NEQ 0 (
    echo 浏览器启动失败
    pause
    exit /b 1
)
echo 浏览器正常工作
pause
`;
    }

    generateUnixScript(browserPath) {
        return `#!/bin/bash
echo "启动浏览器测试..."
"${browserPath}" --version
if [ $? -ne 0 ]; then
    echo "浏览器启动失败"
    exit 1
fi
echo "浏览器正常工作"
`;
    }

    /**
     * 运行环境诊断
     */
    async runDiagnostics() {
        console.log('🔧 运行完整环境诊断...\n');
        
        const report = await this.checkBrowserEnvironment();
        
        console.log('\n📊 诊断报告:');
        console.log('='.repeat(50));
        
        console.log('\n🖥️  系统信息:');
        console.log(`   平台: ${report.system.platform}`);
        console.log(`   架构: ${report.system.arch}`);
        console.log(`   Node.js: ${report.system.nodeVersion}`);
        
        console.log('\n🌐 浏览器状态:');
        if (report.browsers.length > 0) {
            report.browsers.forEach(browser => {
                const status = browser.status === 'available' ? '✅' : '❌';
                console.log(`   ${status} ${browser.name}`);
                console.log(`      路径: ${browser.path}`);
                console.log(`      版本: ${browser.version}`);
                if (browser.error) {
                    console.log(`      错误: ${browser.error}`);
                }
            });
        } else {
            console.log('   ❌ 未找到可用浏览器');
        }
        
        console.log('\n🔧 Puppeteer状态:');
        if (report.puppeteer.status === 'installed') {
            console.log(`   ✅ 已安装 (版本: ${report.puppeteer.version})`);
        } else {
            console.log('   ❌ 未安装或有问题');
            console.log(`      错误: ${report.puppeteer.error}`);
        }
        
        if (report.recommendations.length > 0) {
            console.log('\n💡 建议:');
            report.recommendations.forEach(rec => {
                console.log(`   • ${rec}`);
            });
        }
        
        console.log('\n='.repeat(50));
        
        return report;
    }

    /**
     * 一键修复环境
     */
    async quickFix() {
        console.log('🚑 开始一键修复环境...\n');
        
        const report = await this.checkBrowserEnvironment();
        
        // 如果没有浏览器，尝试安装
        if (report.browsers.filter(b => b.status === 'available').length === 0) {
            console.log('📥 未找到可用浏览器，尝试自动安装Chromium...');
            try {
                const chromiumPath = await this.autoInstallChromium();
                console.log('✅ Chromium安装完成:', chromiumPath);
                
                // 创建启动脚本
                this.createLaunchScript(chromiumPath);
                
            } catch (error) {
                console.error('❌ 自动安装失败:', error.message);
                console.log('\n请手动安装浏览器:');
                console.log('• Chrome: https://www.google.com/chrome/');
                console.log('• Edge: https://www.microsoft.com/edge');
            }
        }
        
        // 如果Puppeteer有问题，给出建议
        if (report.puppeteer.status !== 'installed') {
            console.log('\n📦 建议重新安装Puppeteer:');
            console.log('npm uninstall puppeteer');
            console.log('npm install puppeteer');
        }
        
        console.log('\n✅ 修复完成！');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const setup = new BrowserSetup();
    
    const command = process.argv[2] || 'diagnose';
    
    switch (command) {
        case 'diagnose':
            setup.runDiagnostics();
            break;
        case 'fix':
            setup.quickFix();
            break;
        case 'install':
            setup.autoInstallChromium();
            break;
        default:
            console.log('用法:');
            console.log('  node browser-setup.js diagnose  # 运行诊断');
            console.log('  node browser-setup.js fix       # 一键修复');
            console.log('  node browser-setup.js install   # 安装Chromium');
    }
}

module.exports = BrowserSetup; 