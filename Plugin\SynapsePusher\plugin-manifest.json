{"name": "Synapse<PERSON><PERSON>er", "displayName": "VCP 日志 Synapse 推送器", "version": "1.0.0", "description": "将 VCP 工具调用日志实时推送到指定的 Synapse (Matrix) 房间。", "pluginType": "service", "entryPoint": {"script": "SynapsePusher.js"}, "communication": {"protocol": "direct"}, "configSchema": {"DebugMode": {"type": "boolean", "description": "DebugMode 配置项", "required": false}, "VCP_Key": {"type": "string", "description": "VCP_Key 配置项", "required": false}, "SynapseHomeserver": {"type": "string", "description": "SynapseHomeserver 配置项", "required": false}, "SynapseRoomID": {"type": "string", "description": "SynapseRoomID 配置项", "required": false}, "MaidAccessTokensJSON": {"type": "string", "description": "MaidAccessTokensJSON 配置项", "required": false}, "MaidToolWhitelistJSON": {"type": "string", "description": "MaidToolWhitelistJSON 配置项", "required": false}, "BypassWhitelistForTesting": {"type": "boolean", "description": "BypassWhitelistForTesting 配置项", "required": false}, "SynapseAccessTokenForTestingOnly": {"type": "string", "description": "SynapseAccessTokenForTestingOnly 配置项", "required": false}}, "capabilities": {"systemPromptPlaceholders": [], "invocationCommands": []}}