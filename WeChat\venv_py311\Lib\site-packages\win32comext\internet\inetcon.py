INET_E_USE_DEFAULT_PROTOCOLHANDLER = -2146697199  # _HRESULT_TYPEDEF_(0x800C0011L)
INET_E_USE_DEFAULT_SETTING = -2146697198  # _HRESULT_TYPEDEF_(0x800C0012L)
INET_E_DEFAULT_ACTION = INET_E_USE_DEFAULT_PROTOCOLHANDLER
INET_E_QUERYOPTION_UNKNOWN = -2146697197  # _HRESULT_TYPEDEF_(0x800C0013L)
INET_E_REDIRECTING = -2146697196  # _HRESULT_TYPEDEF_(0x800C0014L)

INET_E_INVALID_URL = -2146697214  # _HRESULT_TYPEDEF_(0x800C0002L)
INET_E_NO_SESSION = -2146697213  # _HRESULT_TYPEDEF_(0x800C0003L)
INET_E_CANNOT_CONNECT = -2146697212  # _HRESULT_TYPEDEF_(0x800C0004L)
INET_E_RESOURCE_NOT_FOUND = -2146697211  # _HRESULT_TYPEDEF_(0x800C0005L)
INET_E_OBJECT_NOT_FOUND = -2146697210  # _HRESULT_TYPEDEF_(0x800C0006L)
INET_E_DATA_NOT_AVAILABLE = -2146697209  # _HRESULT_TYPEDEF_(0x800C0007L)
INET_E_DOWNLOAD_FAILURE = -2146697208  # _HRESULT_TYPEDEF_(0x800C0008L)
INET_E_AUTHENTICATION_REQUIRED = -2146697207  # _HRESULT_TYPEDEF_(0x800C0009L)
INET_E_NO_VALID_MEDIA = -2146697206  # _HRESULT_TYPEDEF_(0x800C000AL)
INET_E_CONNECTION_TIMEOUT = -2146697205  # _HRESULT_TYPEDEF_(0x800C000BL)
INET_E_INVALID_REQUEST = -2146697204  # _HRESULT_TYPEDEF_(0x800C000CL)
INET_E_UNKNOWN_PROTOCOL = -2146697203  # _HRESULT_TYPEDEF_(0x800C000DL)
INET_E_SECURITY_PROBLEM = -2146697202  # _HRESULT_TYPEDEF_(0x800C000EL)
INET_E_CANNOT_LOAD_DATA = -2146697201  # _HRESULT_TYPEDEF_(0x800C000FL)
INET_E_CANNOT_INSTANTIATE_OBJECT = -2146697200  # _HRESULT_TYPEDEF_(0x800C0010L)
INET_E_INVALID_CERTIFICATE = -2146697191  # _HRESULT_TYPEDEF_(0x800C0019L)
INET_E_REDIRECT_FAILED = -2146697196  # _HRESULT_TYPEDEF_(0x800C0014L)
INET_E_REDIRECT_TO_DIR = -2146697195  # _HRESULT_TYPEDEF_(0x800C0015L)
INET_E_CANNOT_LOCK_REQUEST = -2146697194  # _HRESULT_TYPEDEF_(0x800C0016L)
INET_E_USE_EXTEND_BINDING = -2146697193  # _HRESULT_TYPEDEF_(0x800C0017L)
INET_E_TERMINATED_BIND = -2146697192  # _HRESULT_TYPEDEF_(0x800C0018L)
INET_E_CODE_DOWNLOAD_DECLINED = -2146696960  # _HRESULT_TYPEDEF_(0x800C0100L)
INET_E_RESULT_DISPATCHED = -2146696704  # _HRESULT_TYPEDEF_(0x800C0200L)
INET_E_CANNOT_REPLACE_SFP_FILE = -2146696448  # _HRESULT_TYPEDEF_(0x800C0300L)
INET_E_CODE_INSTALL_SUPPRESSED = -2146696192  # _HRESULT_TYPEDEF_(0x800C0400L)
INET_E_CODE_INSTALL_BLOCKED_BY_HASH_POLICY = (
    -2146695936
)  # _HRESULT_TYPEDEF_(0x800C0500L)

# Generated by h2py from UrlMon.h
MKSYS_URLMONIKER = 6
URL_MK_LEGACY = 0
URL_MK_UNIFORM = 1
URL_MK_NO_CANONICALIZE = 2
FIEF_FLAG_FORCE_JITUI = 0x1
FIEF_FLAG_PEEK = 0x2
FIEF_FLAG_SKIP_INSTALLED_VERSION_CHECK = 0x4
FMFD_DEFAULT = 0x00000000
FMFD_URLASFILENAME = 0x00000001
FMFD_ENABLEMIMESNIFFING = 0x00000002
FMFD_IGNOREMIMETEXTPLAIN = 0x00000004
URLMON_OPTION_USERAGENT = 0x10000001
URLMON_OPTION_USERAGENT_REFRESH = 0x10000002
URLMON_OPTION_URL_ENCODING = 0x10000004
URLMON_OPTION_USE_BINDSTRINGCREDS = 0x10000008
URLMON_OPTION_USE_BROWSERAPPSDOCUMENTS = 0x10000010
CF_NULL = 0
Uri_CREATE_ALLOW_RELATIVE = 0x00000001
Uri_CREATE_ALLOW_IMPLICIT_WILDCARD_SCHEME = 0x00000002
Uri_CREATE_ALLOW_IMPLICIT_FILE_SCHEME = 0x00000004
Uri_CREATE_NOFRAG = 0x00000008
Uri_CREATE_NO_CANONICALIZE = 0x00000010
Uri_CREATE_CANONICALIZE = 0x00000100
Uri_CREATE_FILE_USE_DOS_PATH = 0x00000020
Uri_CREATE_DECODE_EXTRA_INFO = 0x00000040
Uri_CREATE_NO_DECODE_EXTRA_INFO = 0x00000080
Uri_CREATE_CRACK_UNKNOWN_SCHEMES = 0x00000200
Uri_CREATE_NO_CRACK_UNKNOWN_SCHEMES = 0x00000400
Uri_CREATE_PRE_PROCESS_HTML_URI = 0x00000800
Uri_CREATE_NO_PRE_PROCESS_HTML_URI = 0x00001000
Uri_CREATE_IE_SETTINGS = 0x00002000
Uri_CREATE_NO_IE_SETTINGS = 0x00004000
Uri_CREATE_NO_ENCODE_FORBIDDEN_CHARACTERS = 0x00008000
Uri_DISPLAY_NO_FRAGMENT = 0x00000001
Uri_PUNYCODE_IDN_HOST = 0x00000002
Uri_DISPLAY_IDN_HOST = 0x00000004
Uri_ENCODING_USER_INFO_AND_PATH_IS_PERCENT_ENCODED_UTF8 = 0x00000001
Uri_ENCODING_USER_INFO_AND_PATH_IS_CP = 0x00000002
Uri_ENCODING_HOST_IS_IDN = 0x00000004
Uri_ENCODING_HOST_IS_PERCENT_ENCODED_UTF8 = 0x00000008
Uri_ENCODING_HOST_IS_PERCENT_ENCODED_CP = 0x00000010
Uri_ENCODING_QUERY_AND_FRAGMENT_IS_PERCENT_ENCODED_UTF8 = 0x00000020
Uri_ENCODING_QUERY_AND_FRAGMENT_IS_CP = 0x00000040
Uri_ENCODING_RFC = (
    Uri_ENCODING_USER_INFO_AND_PATH_IS_PERCENT_ENCODED_UTF8
    | Uri_ENCODING_HOST_IS_PERCENT_ENCODED_UTF8
    | Uri_ENCODING_QUERY_AND_FRAGMENT_IS_PERCENT_ENCODED_UTF8
)
UriBuilder_USE_ORIGINAL_FLAGS = 0x00000001
WININETINFO_OPTION_LOCK_HANDLE = 65534
URLOSTRM_USECACHEDCOPY_ONLY = 0x1
URLOSTRM_USECACHEDCOPY = 0x2
URLOSTRM_GETNEWESTVERSION = 0x3
SET_FEATURE_ON_THREAD = 0x00000001
SET_FEATURE_ON_PROCESS = 0x00000002
SET_FEATURE_IN_REGISTRY = 0x00000004
SET_FEATURE_ON_THREAD_LOCALMACHINE = 0x00000008
SET_FEATURE_ON_THREAD_INTRANET = 0x00000010
SET_FEATURE_ON_THREAD_TRUSTED = 0x00000020
SET_FEATURE_ON_THREAD_INTERNET = 0x00000040
SET_FEATURE_ON_THREAD_RESTRICTED = 0x00000080
GET_FEATURE_FROM_THREAD = 0x00000001
GET_FEATURE_FROM_PROCESS = 0x00000002
GET_FEATURE_FROM_REGISTRY = 0x00000004
GET_FEATURE_FROM_THREAD_LOCALMACHINE = 0x00000008
GET_FEATURE_FROM_THREAD_INTRANET = 0x00000010
GET_FEATURE_FROM_THREAD_TRUSTED = 0x00000020
GET_FEATURE_FROM_THREAD_INTERNET = 0x00000040
GET_FEATURE_FROM_THREAD_RESTRICTED = 0x00000080
PROTOCOLFLAG_NO_PICS_CHECK = 0x00000001
MUTZ_NOSAVEDFILECHECK = 0x00000001
MUTZ_ISFILE = 0x00000002
MUTZ_ACCEPT_WILDCARD_SCHEME = 0x00000080
MUTZ_ENFORCERESTRICTED = 0x00000100
MUTZ_RESERVED = 0x00000200
MUTZ_REQUIRESAVEDFILECHECK = 0x00000400
MUTZ_DONT_UNESCAPE = 0x00000800
MUTZ_DONT_USE_CACHE = 0x00001000
MUTZ_FORCE_INTRANET_FLAGS = 0x00002000
MUTZ_IGNORE_ZONE_MAPPINGS = 0x00004000
MAX_SIZE_SECURITY_ID = 512
URLACTION_MIN = 0x00001000
URLACTION_DOWNLOAD_MIN = 0x00001000
URLACTION_DOWNLOAD_SIGNED_ACTIVEX = 0x00001001
URLACTION_DOWNLOAD_UNSIGNED_ACTIVEX = 0x00001004
URLACTION_DOWNLOAD_CURR_MAX = 0x00001004
URLACTION_DOWNLOAD_MAX = 0x000011FF
URLACTION_ACTIVEX_MIN = 0x00001200
URLACTION_ACTIVEX_RUN = 0x00001200
URLPOLICY_ACTIVEX_CHECK_LIST = 0x00010000
URLACTION_ACTIVEX_OVERRIDE_OBJECT_SAFETY = 0x00001201
URLACTION_ACTIVEX_OVERRIDE_DATA_SAFETY = 0x00001202
URLACTION_ACTIVEX_OVERRIDE_SCRIPT_SAFETY = 0x00001203
URLACTION_SCRIPT_OVERRIDE_SAFETY = 0x00001401
URLACTION_ACTIVEX_CONFIRM_NOOBJECTSAFETY = 0x00001204
URLACTION_ACTIVEX_TREATASUNTRUSTED = 0x00001205
URLACTION_ACTIVEX_NO_WEBOC_SCRIPT = 0x00001206
URLACTION_ACTIVEX_OVERRIDE_REPURPOSEDETECTION = 0x00001207
URLACTION_ACTIVEX_OVERRIDE_OPTIN = 0x00001208
URLACTION_ACTIVEX_SCRIPTLET_RUN = 0x00001209
URLACTION_ACTIVEX_DYNSRC_VIDEO_AND_ANIMATION = 0x0000120A
URLACTION_ACTIVEX_CURR_MAX = 0x0000120A
URLACTION_ACTIVEX_MAX = 0x000013FF
URLACTION_SCRIPT_MIN = 0x00001400
URLACTION_SCRIPT_RUN = 0x00001400
URLACTION_SCRIPT_JAVA_USE = 0x00001402
URLACTION_SCRIPT_SAFE_ACTIVEX = 0x00001405
URLACTION_CROSS_DOMAIN_DATA = 0x00001406
URLACTION_SCRIPT_PASTE = 0x00001407
URLACTION_ALLOW_XDOMAIN_SUBFRAME_RESIZE = 0x00001408
URLACTION_SCRIPT_CURR_MAX = 0x00001408
URLACTION_SCRIPT_MAX = 0x000015FF
URLACTION_HTML_MIN = 0x00001600
URLACTION_HTML_SUBMIT_FORMS = 0x00001601
URLACTION_HTML_SUBMIT_FORMS_FROM = 0x00001602
URLACTION_HTML_SUBMIT_FORMS_TO = 0x00001603
URLACTION_HTML_FONT_DOWNLOAD = 0x00001604
URLACTION_HTML_JAVA_RUN = 0x00001605
URLACTION_HTML_USERDATA_SAVE = 0x00001606
URLACTION_HTML_SUBFRAME_NAVIGATE = 0x00001607
URLACTION_HTML_META_REFRESH = 0x00001608
URLACTION_HTML_MIXED_CONTENT = 0x00001609
URLACTION_HTML_INCLUDE_FILE_PATH = 0x0000160A
URLACTION_HTML_MAX = 0x000017FF
URLACTION_SHELL_MIN = 0x00001800
URLACTION_SHELL_INSTALL_DTITEMS = 0x00001800
URLACTION_SHELL_MOVE_OR_COPY = 0x00001802
URLACTION_SHELL_FILE_DOWNLOAD = 0x00001803
URLACTION_SHELL_VERB = 0x00001804
URLACTION_SHELL_WEBVIEW_VERB = 0x00001805
URLACTION_SHELL_SHELLEXECUTE = 0x00001806
URLACTION_SHELL_EXECUTE_HIGHRISK = 0x00001806
URLACTION_SHELL_EXECUTE_MODRISK = 0x00001807
URLACTION_SHELL_EXECUTE_LOWRISK = 0x00001808
URLACTION_SHELL_POPUPMGR = 0x00001809
URLACTION_SHELL_RTF_OBJECTS_LOAD = 0x0000180A
URLACTION_SHELL_ENHANCED_DRAGDROP_SECURITY = 0x0000180B
URLACTION_SHELL_EXTENSIONSECURITY = 0x0000180C
URLACTION_SHELL_SECURE_DRAGSOURCE = 0x0000180D
URLACTION_SHELL_CURR_MAX = 0x0000180D
URLACTION_SHELL_MAX = 0x000019FF
URLACTION_NETWORK_MIN = 0x00001A00
URLACTION_CREDENTIALS_USE = 0x00001A00
URLPOLICY_CREDENTIALS_SILENT_LOGON_OK = 0x00000000
URLPOLICY_CREDENTIALS_MUST_PROMPT_USER = 0x00010000
URLPOLICY_CREDENTIALS_CONDITIONAL_PROMPT = 0x00020000
URLPOLICY_CREDENTIALS_ANONYMOUS_ONLY = 0x00030000
URLACTION_AUTHENTICATE_CLIENT = 0x00001A01
URLPOLICY_AUTHENTICATE_CLEARTEXT_OK = 0x00000000
URLPOLICY_AUTHENTICATE_CHALLENGE_RESPONSE = 0x00010000
URLPOLICY_AUTHENTICATE_MUTUAL_ONLY = 0x00030000
URLACTION_COOKIES = 0x00001A02
URLACTION_COOKIES_SESSION = 0x00001A03
URLACTION_CLIENT_CERT_PROMPT = 0x00001A04
URLACTION_COOKIES_THIRD_PARTY = 0x00001A05
URLACTION_COOKIES_SESSION_THIRD_PARTY = 0x00001A06
URLACTION_COOKIES_ENABLED = 0x00001A10
URLACTION_NETWORK_CURR_MAX = 0x00001A10
URLACTION_NETWORK_MAX = 0x00001BFF
URLACTION_JAVA_MIN = 0x00001C00
URLACTION_JAVA_PERMISSIONS = 0x00001C00
URLPOLICY_JAVA_PROHIBIT = 0x00000000
URLPOLICY_JAVA_HIGH = 0x00010000
URLPOLICY_JAVA_MEDIUM = 0x00020000
URLPOLICY_JAVA_LOW = 0x00030000
URLPOLICY_JAVA_CUSTOM = 0x00800000
URLACTION_JAVA_CURR_MAX = 0x00001C00
URLACTION_JAVA_MAX = 0x00001CFF
URLACTION_INFODELIVERY_MIN = 0x00001D00
URLACTION_INFODELIVERY_NO_ADDING_CHANNELS = 0x00001D00
URLACTION_INFODELIVERY_NO_EDITING_CHANNELS = 0x00001D01
URLACTION_INFODELIVERY_NO_REMOVING_CHANNELS = 0x00001D02
URLACTION_INFODELIVERY_NO_ADDING_SUBSCRIPTIONS = 0x00001D03
URLACTION_INFODELIVERY_NO_EDITING_SUBSCRIPTIONS = 0x00001D04
URLACTION_INFODELIVERY_NO_REMOVING_SUBSCRIPTIONS = 0x00001D05
URLACTION_INFODELIVERY_NO_CHANNEL_LOGGING = 0x00001D06
URLACTION_INFODELIVERY_CURR_MAX = 0x00001D06
URLACTION_INFODELIVERY_MAX = 0x00001DFF
URLACTION_CHANNEL_SOFTDIST_MIN = 0x00001E00
URLACTION_CHANNEL_SOFTDIST_PERMISSIONS = 0x00001E05
URLPOLICY_CHANNEL_SOFTDIST_PROHIBIT = 0x00010000
URLPOLICY_CHANNEL_SOFTDIST_PRECACHE = 0x00020000
URLPOLICY_CHANNEL_SOFTDIST_AUTOINSTALL = 0x00030000
URLACTION_CHANNEL_SOFTDIST_MAX = 0x00001EFF
URLACTION_BEHAVIOR_MIN = 0x00002000
URLACTION_BEHAVIOR_RUN = 0x00002000
URLPOLICY_BEHAVIOR_CHECK_LIST = 0x00010000
URLACTION_FEATURE_MIN = 0x00002100
URLACTION_FEATURE_MIME_SNIFFING = 0x00002100
URLACTION_FEATURE_ZONE_ELEVATION = 0x00002101
URLACTION_FEATURE_WINDOW_RESTRICTIONS = 0x00002102
URLACTION_FEATURE_SCRIPT_STATUS_BAR = 0x00002103
URLACTION_FEATURE_FORCE_ADDR_AND_STATUS = 0x00002104
URLACTION_FEATURE_BLOCK_INPUT_PROMPTS = 0x00002105
URLACTION_AUTOMATIC_DOWNLOAD_UI_MIN = 0x00002200
URLACTION_AUTOMATIC_DOWNLOAD_UI = 0x00002200
URLACTION_AUTOMATIC_ACTIVEX_UI = 0x00002201
URLACTION_ALLOW_RESTRICTEDPROTOCOLS = 0x00002300
URLACTION_ALLOW_APEVALUATION = 0x00002301
URLACTION_WINDOWS_BROWSER_APPLICATIONS = 0x00002400
URLACTION_XPS_DOCUMENTS = 0x00002401
URLACTION_LOOSE_XAML = 0x00002402
URLACTION_LOWRIGHTS = 0x00002500
URLACTION_WINFX_SETUP = 0x00002600
URLPOLICY_ALLOW = 0x00
URLPOLICY_QUERY = 0x01
URLPOLICY_DISALLOW = 0x03
URLPOLICY_NOTIFY_ON_ALLOW = 0x10
URLPOLICY_NOTIFY_ON_DISALLOW = 0x20
URLPOLICY_LOG_ON_ALLOW = 0x40
URLPOLICY_LOG_ON_DISALLOW = 0x80
URLPOLICY_MASK_PERMISSIONS = 0x0F
URLPOLICY_DONTCHECKDLGBOX = 0x100
URLZONE_ESC_FLAG = 0x100
SECURITY_IE_STATE_GREEN = 0x00000000
SECURITY_IE_STATE_RED = 0x00000001
SOFTDIST_FLAG_USAGE_EMAIL = 0x00000001
SOFTDIST_FLAG_USAGE_PRECACHE = 0x00000002
SOFTDIST_FLAG_USAGE_AUTOINSTALL = 0x00000004
SOFTDIST_FLAG_DELETE_SUBSCRIPTION = 0x00000008
SOFTDIST_ADSTATE_NONE = 0x00000000
SOFTDIST_ADSTATE_AVAILABLE = 0x00000001
SOFTDIST_ADSTATE_DOWNLOADED = 0x00000002
SOFTDIST_ADSTATE_INSTALLED = 0x00000003
CONFIRMSAFETYACTION_LOADOBJECT = 0x00000001
