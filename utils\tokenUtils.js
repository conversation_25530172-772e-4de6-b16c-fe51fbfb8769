// utils/tokenUtils.js - Token计算和截断工具
let encoding_for_model;

try {
    // 尝试导入js-tiktoken
    const tiktoken = require('js-tiktoken');
    encoding_for_model = tiktoken.encoding_for_model;
} catch (error) {
    console.warn('js-tiktoken未安装或导入失败，将使用估算方法:', error.message);
    encoding_for_model = null;
}

class TokenUtils {
    constructor() {
        // 初始化编码器
        this.encoder = null;
        
        if (encoding_for_model) {
            try {
                this.encoder = encoding_for_model('gpt-4');
            } catch (error) {
                console.warn('初始化tiktoken编码器失败:', error.message);
            }
        }
        
        // 默认配置
        this.defaultConfig = {
            enabled: true,
            maxTokens: 16000,
            truncateMarker: '...\n\n[内容已截断，超过最大token限制]'
        };
    }

    /**
     * 计算文本的token数量
     * @param {string} text - 要计算的文本
     * @returns {number} token数量
     */
    countTokens(text) {
        if (typeof text !== 'string') {
            return 0;
        }
        
        if (this.encoder) {
            try {
                const tokens = this.encoder.encode(text);
                return tokens.length;
            } catch (error) {
                console.error('Token计算失败:', error);
            }
        }
        
        // 回退到字符数除以4的估算方法
        return Math.ceil(text.length / 4);
    }

    /**
     * 截断文本到指定的token数量
     * @param {string} text - 要截断的文本
     * @param {number} maxTokens - 最大token数量
     * @param {string} truncateMarker - 截断标记
     * @returns {object} 包含截断后的文本和相关信息
     */
    truncateText(text, maxTokens = this.defaultConfig.maxTokens, truncateMarker = this.defaultConfig.truncateMarker) {
        if (typeof text !== 'string') {
            return {
                text: '',
                originalTokens: 0,
                finalTokens: 0,
                truncated: false
            };
        }

        const originalTokens = this.countTokens(text);
        
        // 如果原始文本已经在限制内，直接返回
        if (originalTokens <= maxTokens) {
            return {
                text: text,
                originalTokens: originalTokens,
                finalTokens: originalTokens,
                truncated: false
            };
        }

        // 预留截断标记的token数量
        const markerTokens = this.countTokens(truncateMarker);
        const availableTokens = maxTokens - markerTokens;

        if (availableTokens <= 0) {
            return {
                text: truncateMarker,
                originalTokens: originalTokens,
                finalTokens: markerTokens,
                truncated: true
            };
        }

        // 二分查找最佳截断位置
        let left = 0;
        let right = text.length;
        let bestPosition = 0;

        while (left <= right) {
            const mid = Math.floor((left + right) / 2);
            const substring = text.substring(0, mid);
            const tokens = this.countTokens(substring);

            if (tokens <= availableTokens) {
                bestPosition = mid;
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        // 尝试在单词边界截断
        const truncatedText = this.findWordBoundary(text, bestPosition);
        const finalText = truncatedText + truncateMarker;
        const finalTokens = this.countTokens(finalText);

        return {
            text: finalText,
            originalTokens: originalTokens,
            finalTokens: finalTokens,
            truncated: true
        };
    }

    /**
     * 在单词边界截断文本
     * @param {string} text - 原始文本
     * @param {number} position - 截断位置
     * @returns {string} 在单词边界截断的文本
     */
    findWordBoundary(text, position) {
        if (position >= text.length) {
            return text;
        }

        // 向前查找最近的空格、换行符或标点符号
        let boundaryPosition = position;
        const boundaries = [' ', '\n', '\t', '.', '!', '?', ',', ';', ':', '。', '！', '？', '，', '；', '：'];

        for (let i = position; i >= Math.max(0, position - 100); i--) {
            if (boundaries.includes(text[i])) {
                boundaryPosition = i;
                break;
            }
        }

        return text.substring(0, boundaryPosition);
    }

    /**
     * 从环境变量加载配置
     * @param {string} prefix - 环境变量前缀
     * @returns {object} 配置对象
     */
    loadConfig(prefix = '') {
        const config = { ...this.defaultConfig };

        // 读取环境变量
        const enabledVar = `${prefix}TOKEN_TRUNCATE_ENABLED`;
        const maxTokensVar = `${prefix}TOKEN_MAX_TOKENS`;
        const markerVar = `${prefix}TOKEN_TRUNCATE_MARKER`;

        if (process.env[enabledVar] !== undefined) {
            config.enabled = process.env[enabledVar].toLowerCase() === 'true';
        }

        if (process.env[maxTokensVar] !== undefined) {
            const maxTokens = parseInt(process.env[maxTokensVar], 10);
            if (!isNaN(maxTokens) && maxTokens > 0) {
                config.maxTokens = maxTokens;
            }
        }

        if (process.env[markerVar] !== undefined) {
            config.truncateMarker = process.env[markerVar];
        }

        return config;
    }

    /**
     * 处理插件内容的token截断
     * @param {string} content - 内容
     * @param {string} pluginName - 插件名称（用于环境变量前缀）
     * @returns {object} 处理结果
     */
    processPluginContent(content, pluginName = '') {
        const prefix = pluginName ? `${pluginName.toUpperCase()}_` : '';
        const config = this.loadConfig(prefix);

        if (!config.enabled) {
            return {
                content: content,
                originalTokens: this.countTokens(content),
                finalTokens: this.countTokens(content),
                truncated: false,
                config: config
            };
        }

        const result = this.truncateText(content, config.maxTokens, config.truncateMarker);
        result.config = config;

        return {
            content: result.text,
            originalTokens: result.originalTokens,
            finalTokens: result.finalTokens,
            truncated: result.truncated,
            config: config
        };
    }

    /**
     * 释放编码器资源
     */
    dispose() {
        if (this.encoder && typeof this.encoder.free === 'function') {
            this.encoder.free();
        }
    }
}

// 创建单例实例
const tokenUtils = new TokenUtils();

module.exports = {
    TokenUtils,
    tokenUtils
}; 