/**
 * 测试科学算法优化后的心理状态计算
 * 验证基于论文研究的算法准确性和时间关联性
 */

const WorldTreeVCP = require('./WorldTreeVCP.js');
const ScientificPsychologyAlgorithms = require('./ScientificPsychologyAlgorithms.js');
const ChronobiologyModule = require('./ChronobiologyModule.js');
const EmotionalComputingModule = require('./EmotionalComputingModule.js');

// 模拟日志记录器
const mockLogger = {
    info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
    error: (tag, ...args) => console.error(`[ERROR] [${tag}]`, ...args),
    warning: (tag, ...args) => console.warn(`[WARNING] [${tag}]`, ...args),
    debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args),
    success: (tag, ...args) => console.log(`[SUCCESS] [${tag}]`, ...args),
    system: (...args) => console.log(`[SYSTEM]`, ...args)
};

async function testScientificAlgorithms() {
    console.log('🧬 测试基于科学研究的心理状态算法...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 测试独立的科学算法模块
        console.log('1. 测试独立的科学算法模块...');
        
        // 测试科学心理学算法
        const scientificAlgorithms = new ScientificPsychologyAlgorithms({
            circadianPeriod: 24,
            ultradianPeriod: 1.5,
            maxCognitiveCapacity: 100,
            hungerCycle: 4,
            metabolicRate: 1.0
        });
        
        const psychState = scientificAlgorithms.calculatePsychologicalState({
            timeSinceLastMeal: 3,
            timeSinceLastSleep: 8,
            physicalActivity: 0.3,
            recentInteractions: 5,
            taskComplexity: 0.7,
            multitasking: 0.2,
            socialInteraction: 0.8,
            achievement: 0.6,
            novelty: 0.4
        });
        
        console.log('科学心理学算法结果:');
        console.log(`  专注度: ${psychState.focus.toFixed(1)}% (基于认知负荷理论)`);
        console.log(`  精力水平: ${psychState.energy.toFixed(1)}% (基于能量分配理论)`);
        console.log(`  疲劳度: ${psychState.fatigue.toFixed(1)}% (基于疲劳累积模型)`);
        console.log(`  警觉性: ${psychState.alertness.toFixed(1)}% (基于警觉性网络模型)`);
        console.log(`  饥饿感: ${psychState.hunger.toFixed(1)}% (基于代谢调节理论)`);
        console.log(`  压力水平: ${psychState.stress.toFixed(1)}% (基于压力反应模型)`);
        console.log(`  心情: ${psychState.mood.toFixed(1)}% (基于情绪维度模型)`);
        console.log(`  时间段: ${psychState.timePeriod}`);
        console.log(`  生理节律相位: ${psychState.circadianPhase.toFixed(3)}`);
        console.log(`  超日节律相位: ${psychState.ultradianPhase.toFixed(3)}`);
        console.log('✅ 科学心理学算法测试成功\n');
        
        // 测试时间生物学模块
        const chronobiology = new ChronobiologyModule({
            chronotype: 'intermediate',
            sleepDuration: 8,
            bedtime: '23:00',
            wakeTime: '07:00'
        });
        
        const circadianState = chronobiology.calculateCircadianState();
        console.log('时间生物学模块结果:');
        console.log(`  生理节律相位: ${circadianState.circadianPhase.toFixed(3)}`);
        console.log(`  皮质醇水平: ${circadianState.cortisolLevel.toFixed(3)}`);
        console.log(`  褪黑素水平: ${circadianState.melatoninLevel.toFixed(3)}`);
        console.log(`  体温节律: ${circadianState.bodyTemperature.toFixed(3)}`);
        console.log(`  警觉性水平: ${circadianState.alertnessLevel.toFixed(3)}`);
        console.log(`  认知表现: ${circadianState.cognitivePerformance.toFixed(3)}`);
        console.log(`  时型: ${circadianState.chronotype}`);
        console.log(`  最佳表现窗口: ${circadianState.optimalWindow ? '是' : '否'}`);
        console.log(`  详细时间段: ${circadianState.timeOfDay}`);
        console.log(`  社会时差: ${circadianState.socialJetlag.toFixed(3)}`);
        console.log('✅ 时间生物学模块测试成功\n');
        
        // 测试情感计算模块
        const emotionalComputing = new EmotionalComputingModule({
            emotionDecayRate: 0.95,
            moodStability: 0.8,
            emotionalSensitivity: 0.7,
            openness: 0.6,
            conscientiousness: 0.7,
            extraversion: 0.5,
            agreeableness: 0.8,
            neuroticism: 0.3
        });
        
        // 分析一些测试文本
        const testTexts = [
            '今天的研究进展很顺利，发现了一个有趣的算法优化方案！',
            '感觉有点累了，需要休息一下。',
            '这个问题比较复杂，需要仔细思考。'
        ];
        
        console.log('情感计算模块结果:');
        testTexts.forEach((text, index) => {
            const emotion = emotionalComputing.analyzeTextEmotion(text);
            emotionalComputing.updateEmotionalState(emotion, {
                socialInteraction: 0.5,
                achievement: index === 0 ? 0.8 : 0.4,
                novelty: 0.3
            });
            
            console.log(`  文本 ${index + 1}: "${text}"`);
            console.log(`    情感效价: ${emotion.valence.toFixed(3)} (正负情绪)`);
            console.log(`    唤醒度: ${emotion.arousal.toFixed(3)} (激活程度)`);
            console.log(`    支配感: ${emotion.dominance.toFixed(3)} (控制感)`);
            console.log(`    置信度: ${emotion.confidence.toFixed(3)}`);
        });
        
        const currentEmotion = emotionalComputing.getCurrentEmotionalState();
        console.log(`  当前情绪状态: ${currentEmotion.primaryEmotion.name} (置信度: ${currentEmotion.primaryEmotion.confidence.toFixed(3)})`);
        console.log(`  情绪强度: ${currentEmotion.intensity.toFixed(3)}`);
        console.log(`  情绪稳定性: ${currentEmotion.stability.toFixed(3)}`);
        console.log(`  情绪趋势: ${currentEmotion.history.trend} (方向: ${currentEmotion.history.direction.toFixed(3)})`);
        
        const cognitiveEffects = emotionalComputing.getEmotionalCognitiveEffects();
        console.log(`  对注意力的影响: ${cognitiveEffects.attentionFocus.toFixed(3)}`);
        console.log(`  对记忆的影响: ${cognitiveEffects.memoryBias.toFixed(3)}`);
        console.log(`  对决策的影响: ${cognitiveEffects.riskTolerance.toFixed(3)}`);
        console.log(`  对创造力的影响: ${cognitiveEffects.creativity.toFixed(3)}`);
        console.log('✅ 情感计算模块测试成功\n');
        
        // 2. 测试集成的WorldTreeVCP
        console.log('2. 测试集成的WorldTreeVCP科学算法...');
        worldTreeVCP = new WorldTreeVCP();
        const initResult = await worldTreeVCP.initialize(mockLogger);
        
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        
        console.log('WorldTreeVCP科学算法配置:');
        console.log(`  使用本地算法: ${worldTreeVCP.config.useLocalAlgorithm ? '是' : '否'}`);
        console.log(`  科学算法模块: ${worldTreeVCP.scientificAlgorithms ? '已加载' : '未加载'}`);
        console.log(`  时间生物学模块: ${worldTreeVCP.chronobiology ? '已加载' : '未加载'}`);
        console.log(`  情感计算模块: ${worldTreeVCP.emotionalComputing ? '已加载' : '未加载'}`);
        console.log('');
        
        // 3. 测试不同时间段的心理状态
        console.log('3. 测试不同时间段的心理状态变化...');
        const testTimes = [
            { hour: 8, period: '早晨' },
            { hour: 14, period: '下午' },
            { hour: 20, period: '傍晚' },
            { hour: 23, period: '夜晚' }
        ];
        
        for (const timeTest of testTimes) {
            // 模拟不同时间段
            const contextFactors = {
                timeSinceLastMeal: timeTest.hour === 8 ? 12 : (timeTest.hour === 14 ? 2 : 4),
                timeSinceLastSleep: timeTest.hour === 8 ? 1 : (timeTest.hour - 7),
                physicalActivity: timeTest.hour === 14 ? 0.6 : 0.3,
                achievement: timeTest.hour === 14 ? 0.7 : 0.5,
                novelty: 0.4,
                uncertainty: timeTest.hour === 23 ? 0.2 : 0.3
            };
            
            const psychState = await worldTreeVCP.calculatePsychologyState('test_user', '雨安安', contextFactors);
            
            console.log(`${timeTest.period} (${timeTest.hour}:00) 心理状态:`);
            console.log(`  专注度: ${psychState.focus?.toFixed(1) || 'N/A'}%`);
            console.log(`  精力水平: ${psychState.energy?.toFixed(1) || 'N/A'}%`);
            console.log(`  疲劳度: ${psychState.fatigue?.toFixed(1) || 'N/A'}%`);
            console.log(`  警觉性: ${psychState.alertness?.toFixed(1) || 'N/A'}%`);
            console.log(`  饥饿感: ${psychState.hunger?.toFixed(1) || 'N/A'}%`);
            console.log(`  压力水平: ${psychState.stress?.toFixed(1) || 'N/A'}%`);
            console.log(`  心情: ${psychState.mood?.toFixed(1) || 'N/A'}%`);
            console.log(`  算法版本: ${psychState.algorithm || 'unknown'}`);
            console.log('');
        }
        
        // 4. 测试饥饿感和时间的关联
        console.log('4. 测试饥饿感与用餐时间的关联...');
        const mealTests = [
            { timeSinceLastMeal: 1, description: '刚用餐1小时' },
            { timeSinceLastMeal: 3, description: '用餐后3小时' },
            { timeSinceLastMeal: 5, description: '用餐后5小时' },
            { timeSinceLastMeal: 8, description: '用餐后8小时' }
        ];
        
        for (const mealTest of mealTests) {
            const contextFactors = {
                timeSinceLastMeal: mealTest.timeSinceLastMeal,
                timeSinceLastSleep: 6,
                physicalActivity: 0.3
            };
            
            const psychState = await worldTreeVCP.calculatePsychologyState('test_user', '雨安安', contextFactors);
            console.log(`${mealTest.description}: 饥饿感 ${psychState.hunger?.toFixed(1) || 'N/A'}%, 精力 ${psychState.energy?.toFixed(1) || 'N/A'}%`);
        }
        console.log('✅ 饥饿感时间关联测试成功\n');
        
        // 5. 测试睡眠与精力的关联
        console.log('5. 测试睡眠时间与精力的关联...');
        const sleepTests = [
            { timeSinceLastSleep: 2, description: '睡醒2小时' },
            { timeSinceLastSleep: 6, description: '睡醒6小时' },
            { timeSinceLastSleep: 12, description: '睡醒12小时' },
            { timeSinceLastSleep: 18, description: '睡醒18小时' }
        ];
        
        for (const sleepTest of sleepTests) {
            const contextFactors = {
                timeSinceLastMeal: 3,
                timeSinceLastSleep: sleepTest.timeSinceLastSleep,
                physicalActivity: 0.3
            };
            
            const psychState = await worldTreeVCP.calculatePsychologyState('test_user', '雨安安', contextFactors);
            console.log(`${sleepTest.description}: 精力 ${psychState.energy?.toFixed(1) || 'N/A'}%, 疲劳度 ${psychState.fatigue?.toFixed(1) || 'N/A'}%, 警觉性 ${psychState.alertness?.toFixed(1) || 'N/A'}%`);
        }
        console.log('✅ 睡眠时间关联测试成功\n');
        
        // 6. 总结
        console.log('6. 科学算法优化总结...');
        console.log('🎉 基于科学研究的心理状态算法测试完成！');
        console.log('\n📋 算法优化成果:');
        console.log('• ✅ 集成了基于论文研究的科学算法');
        console.log('• ✅ 实现了精确的时间-生理状态关联');
        console.log('• ✅ 优化了饥饿感的代谢调节模型');
        console.log('• ✅ 改进了精力水平的能量分配理论');
        console.log('• ✅ 增强了专注度的认知负荷计算');
        console.log('• ✅ 完善了情感状态的多维度分析');
        console.log('• ✅ 应用了生物钟和超日节律研究');
        console.log('• ✅ 整合了个性特征对心理状态的影响');
        
        console.log('\n🧬 科学理论基础:');
        console.log('- Circadian Rhythm Research (生理节律研究)');
        console.log('- Yerkes-Dodson Law (耶克斯-多德森定律)');
        console.log('- Cognitive Load Theory (认知负荷理论)');
        console.log('- Attention Restoration Theory (注意力恢复理论)');
        console.log('- Homeostatic Sleep Drive (睡眠稳态驱动)');
        console.log('- Ultradian Rhythms (超日节律)');
        console.log('- Russell\'s Circumplex Model (罗素环形模型)');
        console.log('- Plutchik\'s Wheel of Emotions (普拉奇克情绪轮)');
        console.log('- Big Five Personality Model (大五人格模型)');
        console.log('- Flow Theory (心流理论)');
        
        console.log('\n🚀 现在的心理状态计算更加科学和准确！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error.stack);
    } finally {
        // 清理
        if (worldTreeVCP) {
            try {
                await worldTreeVCP.cleanup();
                console.log('\n🧹 插件清理完成');
            } catch (cleanupError) {
                console.error('清理失败:', cleanupError.message);
            }
        }
    }
}

// 运行测试
if (require.main === module) {
    testScientificAlgorithms().then(() => {
        console.log('\n测试完成，退出程序。');
        process.exit(0);
    }).catch(error => {
        console.error('\n测试过程中发生未捕获的错误:', error);
        process.exit(1);
    });
}

module.exports = { testScientificAlgorithms };
