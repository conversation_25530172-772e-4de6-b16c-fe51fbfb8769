/**
 * Admin Plugin
 * 管理员功能插件
 * 演示权限系统的使用
 */

const BasePlugin = require('./base_plugin');

class AdminPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('AdminPlugin', adapter, logger, config);
        
        // 设置优先级和权限
        this.priority = 20; // 高优先级
        this.permission = 'admin'; // 需要管理员权限
        this.supportedTypes = ['message'];

        // 插件元信息
        this.meta = {
            version: '1.0.0',
            author: 'VCPToolBox',
            description: '管理员功能插件',
            usage: '管理员可使用的高级功能',
            example: '插件管理'
        };

        // 管理员命令
        this.adminCommands = {
            '插件管理': this.handlePluginManagement.bind(this),
            '插件列表': this.showPluginList.bind(this),
            '插件状态': this.showPluginStatus.bind(this),
            '重载插件': this.reloadPlugin.bind(this),
            '禁用插件': this.disablePlugin.bind(this),
            '启用插件': this.enablePlugin.bind(this),
            '系统状态': this.showSystemStatus.bind(this),
            '清理缓存': this.clearCache.bind(this)
        };

        this.logger.info('Plugin', 'Admin plugin initialized');
    }

    /**
     * 检查是否应该处理此消息
     */
    shouldHandle(context) {
        const message = context.raw_message || context.message;
        const messageText = typeof message === 'string' ? message : 
                           Array.isArray(message) ? message.map(seg => seg.data?.text || '').join('') : '';

        // 检查是否包含管理员命令
        for (const command of Object.keys(this.adminCommands)) {
            if (messageText.includes(command)) {
                return super.shouldHandle(context);
            }
        }

        return false;
    }

    /**
     * 处理消息
     */
    async handle(context) {
        try {
            const message = context.raw_message || context.message;
            const messageText = typeof message === 'string' ? message : 
                               Array.isArray(message) ? message.map(seg => seg.data?.text || '').join('') : '';

            // 查找匹配的命令
            for (const [command, handler] of Object.entries(this.adminCommands)) {
                if (messageText.includes(command)) {
                    await handler(context, messageText);
                    return {
                        handled: true,
                        message: `管理员命令 ${command} 执行完成`
                    };
                }
            }

            return {
                handled: false,
                message: '未找到匹配的管理员命令'
            };

        } catch (error) {
            this.logger.error('Plugin', `管理员插件处理失败: ${error.message}`);
            await this.reply(context, `❌ 处理失败: ${error.message}`);
            return {
                handled: true,
                error: error.message
            };
        }
    }

    /**
     * 处理插件管理
     */
    async handlePluginManagement(context, messageText) {
        const helpText = `🔧 插件管理功能\n\n` +
                        `📋 可用命令:\n` +
                        `• 插件列表 - 查看所有插件\n` +
                        `• 插件状态 - 查看插件运行状态\n` +
                        `• 重载插件 [名称] - 重载指定插件\n` +
                        `• 禁用插件 [名称] - 禁用指定插件\n` +
                        `• 启用插件 [名称] - 启用指定插件\n` +
                        `• 系统状态 - 查看系统运行状态\n` +
                        `• 清理缓存 - 清理系统缓存\n\n` +
                        `💡 提示: 只有管理员才能使用这些功能`;

        await this.reply(context, helpText);
    }

    /**
     * 显示插件列表
     */
    async showPluginList(context, messageText) {
        try {
            // 获取机器人实例中的插件列表
            const bot = this.adapter.bot || this.adapter;
            const plugins = bot.pluginInstances || [];

            if (plugins.length === 0) {
                await this.reply(context, '❌ 未找到任何插件');
                return;
            }

            let response = `📋 插件列表 (共 ${plugins.length} 个)\n\n`;
            
            plugins.forEach((plugin, index) => {
                const status = plugin.enabled ? '✅' : '❌';
                const priority = plugin.priority || 100;
                const permission = plugin.permission || 'all';
                
                response += `${index + 1}. ${status} ${plugin.name}\n`;
                response += `   优先级: ${priority} | 权限: ${permission}\n`;
                response += `   触发次数: ${plugin.stats.triggered || 0}\n\n`;
            });

            await this.reply(context, response);

        } catch (error) {
            await this.reply(context, `❌ 获取插件列表失败: ${error.message}`);
        }
    }

    /**
     * 显示插件状态
     */
    async showPluginStatus(context, messageText) {
        try {
            const bot = this.adapter.bot || this.adapter;
            const plugins = bot.pluginInstances || [];

            let enabledCount = 0;
            let disabledCount = 0;
            let totalTriggered = 0;
            let totalErrors = 0;

            plugins.forEach(plugin => {
                if (plugin.enabled) {
                    enabledCount++;
                } else {
                    disabledCount++;
                }
                totalTriggered += plugin.stats.triggered || 0;
                totalErrors += plugin.stats.errors || 0;
            });

            const response = `📊 插件系统状态\n\n` +
                           `🔢 统计信息:\n` +
                           `• 总插件数: ${plugins.length}\n` +
                           `• 启用插件: ${enabledCount}\n` +
                           `• 禁用插件: ${disabledCount}\n` +
                           `• 总触发次数: ${totalTriggered}\n` +
                           `• 总错误次数: ${totalErrors}\n\n` +
                           `📈 成功率: ${totalTriggered > 0 ? 
                             ((totalTriggered - totalErrors) / totalTriggered * 100).toFixed(2) + '%' : 
                             '暂无数据'}\n` +
                           `🕒 系统运行时间: ${this.getUptime()}`;

            await this.reply(context, response);

        } catch (error) {
            await this.reply(context, `❌ 获取插件状态失败: ${error.message}`);
        }
    }

    /**
     * 重载插件
     */
    async reloadPlugin(context, messageText) {
        const pluginName = this.extractPluginName(messageText, '重载插件');
        
        if (!pluginName) {
            await this.reply(context, '❌ 请指定要重载的插件名称\n用法: 重载插件 [插件名称]');
            return;
        }

        await this.reply(context, `🔄 重载插件功能暂未实现\n指定插件: ${pluginName}`);
    }

    /**
     * 禁用插件
     */
    async disablePlugin(context, messageText) {
        const pluginName = this.extractPluginName(messageText, '禁用插件');
        
        if (!pluginName) {
            await this.reply(context, '❌ 请指定要禁用的插件名称\n用法: 禁用插件 [插件名称]');
            return;
        }

        try {
            const bot = this.adapter.bot || this.adapter;
            const plugins = bot.pluginInstances || [];
            const plugin = plugins.find(p => p.name === pluginName);

            if (!plugin) {
                await this.reply(context, `❌ 未找到插件: ${pluginName}`);
                return;
            }

            plugin.disable();
            await this.reply(context, `✅ 插件 ${pluginName} 已禁用`);

        } catch (error) {
            await this.reply(context, `❌ 禁用插件失败: ${error.message}`);
        }
    }

    /**
     * 启用插件
     */
    async enablePlugin(context, messageText) {
        const pluginName = this.extractPluginName(messageText, '启用插件');
        
        if (!pluginName) {
            await this.reply(context, '❌ 请指定要启用的插件名称\n用法: 启用插件 [插件名称]');
            return;
        }

        try {
            const bot = this.adapter.bot || this.adapter;
            const plugins = bot.pluginInstances || [];
            const plugin = plugins.find(p => p.name === pluginName);

            if (!plugin) {
                await this.reply(context, `❌ 未找到插件: ${pluginName}`);
                return;
            }

            plugin.enable();
            await this.reply(context, `✅ 插件 ${pluginName} 已启用`);

        } catch (error) {
            await this.reply(context, `❌ 启用插件失败: ${error.message}`);
        }
    }

    /**
     * 显示系统状态
     */
    async showSystemStatus(context, messageText) {
        const memUsage = process.memoryUsage();
        const uptime = process.uptime();

        const response = `🖥️ 系统状态\n\n` +
                        `💾 内存使用:\n` +
                        `• RSS: ${(memUsage.rss / 1024 / 1024).toFixed(2)} MB\n` +
                        `• Heap Used: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)} MB\n` +
                        `• Heap Total: ${(memUsage.heapTotal / 1024 / 1024).toFixed(2)} MB\n\n` +
                        `⏱️ 运行时间: ${this.formatUptime(uptime)}\n` +
                        `🔧 Node.js 版本: ${process.version}\n` +
                        `📊 CPU 架构: ${process.arch}\n` +
                        `🖥️ 操作系统: ${process.platform}`;

        await this.reply(context, response);
    }

    /**
     * 清理缓存
     */
    async clearCache(context, messageText) {
        try {
            // 执行垃圾回收
            if (global.gc) {
                global.gc();
                await this.reply(context, '✅ 缓存清理完成');
            } else {
                await this.reply(context, '⚠️ 垃圾回收功能未启用\n请使用 --expose-gc 参数启动');
            }
        } catch (error) {
            await this.reply(context, `❌ 清理缓存失败: ${error.message}`);
        }
    }

    /**
     * 提取插件名称
     */
    extractPluginName(messageText, command) {
        const parts = messageText.split(command);
        if (parts.length > 1) {
            return parts[1].trim();
        }
        return null;
    }

    /**
     * 获取运行时间
     */
    getUptime() {
        return this.formatUptime(process.uptime());
    }

    /**
     * 格式化运行时间
     */
    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (days > 0) {
            return `${days}天 ${hours}小时 ${minutes}分钟`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟 ${secs}秒`;
        } else {
            return `${secs}秒`;
        }
    }
}

module.exports = AdminPlugin;
