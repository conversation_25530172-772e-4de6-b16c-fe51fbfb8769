# 🚀 Sentra 沙盒世界 - 快速启动指南

## 📋 系统要求

- Node.js 14.0.0 或更高版本
- 至少 512MB 可用内存
- 100MB 可用存储空间

## 🎯 一键启动（推荐）

### 1. 安装依赖
双击 `安装依赖.bat` 文件，等待安装完成

### 2. 启动系统
双击 `启动沙盒世界.bat` 文件

### 3. 访问界面
打开浏览器访问：`http://localhost:8080`

## 🎮 界面功能说明

### 🌍 世界控制面板
- **启动世界**: 开始沙盒世界模拟
- **暂停/恢复**: 暂停或恢复世界运行
- **停止世界**: 完全停止并保存数据
- **时间流速**: 调整世界时间流速（0.1x - 10x）

### 👥 Agent管理
- **添加Agent**: 点击"📝 添加Agent"创建新Agent
- **选择模板**: 可以基于现有Agent文件创建，或创建全新Agent
- **Agent信息**: 显示每个Agent的详细状态
- **删除Agent**: 点击🗑️按钮删除Agent

### 🎭 事件管理
- **随机事件**: 触发随机事件
- **特定事件**: 触发节日、下雨、市集等特定事件
- **事件进度**: 实时显示事件进度和剩余时间

### 💬 对话监控
- **实时对话**: 查看Agent之间的实时对话
- **对话类型**: 显示不同类型的对话（寻求帮助、分享快乐等）
- **对话历史**: 查看最近的对话内容

## 🔧 Agent模板系统

### 自动加载现有Agent
系统会自动扫描以下目录中的Agent文件：
- `../Agent/` (主Agent目录)
- `./data/sample_agents/` (示例Agent目录)

### Agent文件格式
```
昵称: Agent名称
QQ号: 123456789
年龄: 25
性别: 男/女/其他

Agent的描述信息...
```

### 添加新Agent模板
1. 在Agent目录中创建 `.txt` 文件
2. 按照上述格式填写信息
3. 重启系统或点击"📋 加载模板"

## 📊 实时监控

### 统计信息
- **Agent数量**: 当前世界中的Agent总数
- **在线Agent**: 最近5分钟内活跃的Agent
- **平均心情**: 所有Agent的平均心情值
- **活跃事件**: 正在进行的事件数量
- **对话数量**: 当前活跃的对话数量

### 系统日志
右下角的日志面板显示：
- 系统运行状态
- Agent添加/删除记录
- 事件触发信息
- 错误和警告信息

## 🎨 界面特色

### 现代化设计
- 深色主题，护眼舒适
- 渐变背景和动态效果
- 响应式设计，支持移动设备

### 实时更新
- WebSocket实时数据同步
- 自动刷新Agent状态
- 实时事件和对话更新

### 交互体验
- 悬停动画效果
- 平滑过渡动画
- 直观的操作反馈

## 🔍 常见问题

### Q: 界面显示空白怎么办？
A: 
1. 确保已运行 `安装依赖.bat`
2. 检查控制台是否有错误信息
3. 尝试刷新浏览器页面

### Q: 无法添加Agent？
A: 
1. 检查Agent名称是否重复
2. 确保所有必填字段都已填写
3. 查看系统日志中的错误信息

### Q: Agent模板无法加载？
A: 
1. 确认Agent文件格式正确
2. 检查文件编码是否为UTF-8
3. 确保Agent目录路径正确

### Q: 事件无法触发？
A: 
1. 确保世界已启动
2. 检查是否有足够的Agent参与
3. 查看系统日志了解具体原因

## 🎯 使用技巧

### 创建有趣的社区
1. 添加3-5个不同性格的Agent
2. 让他们在不同地点活动
3. 触发一些社交事件促进互动

### 观察社会现象
1. 添加更多Agent（10+）
2. 观察群体关系的形成
3. 分析不同事件对社区的影响

### 实验不同场景
1. 调整时间流速观察长期变化
2. 触发不同类型的事件
3. 观察Agent的适应和反应

## 🎉 开始探索

现在您已经掌握了基本操作，开始创建您独特的沙盒世界吧！

每个世界都会产生不同的故事，Agent们会根据他们的性格和经历展现出意想不到的行为。享受观察和互动的乐趣！

---

**💡 提示**: 如果遇到问题，请查看系统日志或重启系统。大多数问题都可以通过重新启动解决。
