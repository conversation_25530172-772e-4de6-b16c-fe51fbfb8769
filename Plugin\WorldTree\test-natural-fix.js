/**
 * 测试natural库修复效果
 */

const WorldTreeVCP = require('./WorldTreeVCP');

async function testNaturalFix() {
    console.log('🔧 测试natural库修复效果...\n');
    
    const mockLogger = {
        info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
        warning: (tag, ...args) => console.log(`[WARN] [${tag}]`, ...args),
        error: (tag, ...args) => console.log(`[ERROR] [${tag}]`, ...args),
        debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args)
    };
    
    try {
        // 1. 测试natural库版本和API
        console.log('1. 检查natural库版本和API...');
        const natural = require('natural');
        console.log('Natural库版本信息:');
        console.log('- WordTokenizer存在:', !!natural.WordTokenizer);
        console.log('- SentenceTokenizer存在:', !!natural.SentenceTokenizer);
        
        if (natural.WordTokenizer) {
            console.log('- WordTokenizer.tokenize方法:', typeof natural.WordTokenizer.tokenize);
            console.log('- WordTokenizer构造函数:', typeof natural.WordTokenizer);
        }
        
        if (natural.SentenceTokenizer) {
            console.log('- SentenceTokenizer.tokenize方法:', typeof natural.SentenceTokenizer.tokenize);
            console.log('- SentenceTokenizer构造函数:', typeof natural.SentenceTokenizer);
        }
        
        // 2. 测试文本分析功能
        console.log('\n2. 测试文本分析功能...');
        const testText = '这是一个测试文本。它包含多个句子！用来测试分词和句子分割功能？';
        
        let tokens, sentences;
        
        try {
            // 测试修复后的分词逻辑
            if (natural.WordTokenizer && typeof natural.WordTokenizer.tokenize === 'function') {
                tokens = natural.WordTokenizer.tokenize(testText);
                console.log('✅ 使用静态方法分词成功');
            } else if (natural.WordTokenizer) {
                const tokenizer = new natural.WordTokenizer();
                tokens = tokenizer.tokenize(testText);
                console.log('✅ 使用实例方法分词成功');
            } else {
                tokens = testText.split(/\s+/).filter(token => token.length > 0);
                console.log('✅ 使用备用方案分词成功');
            }
            
            if (natural.SentenceTokenizer && typeof natural.SentenceTokenizer.tokenize === 'function') {
                sentences = natural.SentenceTokenizer.tokenize(testText);
                console.log('✅ 使用静态方法句子分割成功');
            } else if (natural.SentenceTokenizer) {
                const sentenceTokenizer = new natural.SentenceTokenizer();
                sentences = sentenceTokenizer.tokenize(testText);
                console.log('✅ 使用实例方法句子分割成功');
            } else {
                sentences = testText.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
                console.log('✅ 使用备用方案句子分割成功');
            }
            
            console.log(`分词结果 (${tokens.length}个): ${tokens.slice(0, 10).join(', ')}${tokens.length > 10 ? '...' : ''}`);
            console.log(`句子分割结果 (${sentences.length}个): ${sentences.map(s => `"${s.trim()}"`).join(', ')}`);
            
        } catch (error) {
            console.log('❌ 文本分析失败:', error.message);
        }
        
        // 3. 初始化WorldTreeVCP并测试
        console.log('\n3. 初始化WorldTreeVCP并测试...');
        const worldTreeVCP = new WorldTreeVCP();
        await worldTreeVCP.initialize(mockLogger);
        console.log('✅ 插件初始化成功');
        
        // 4. 测试心理状态计算（包含文本分析）
        console.log('\n4. 测试心理状态计算...');
        const testUserId = 'test_user_natural_fix';
        const testAgentName = '雨安安';
        
        const contextFactors = {
            isRequestTriggered: true,
            cognitiveLoad: 0.5
        };
        
        try {
            const psychologyState = await worldTreeVCP.calculatePsychologyState(
                testUserId, 
                testAgentName, 
                contextFactors
            );
            
            console.log('✅ 心理状态计算成功:');
            console.log(`  专注度: ${psychologyState.focus?.toFixed(1)}/100`);
            console.log(`  精力: ${psychologyState.energy?.toFixed(1)}/100`);
            console.log(`  疲劳度: ${psychologyState.fatigue?.toFixed(1)}/100`);
            console.log(`  警觉性: ${psychologyState.alertness?.toFixed(1)}/100`);
            
        } catch (error) {
            console.log('❌ 心理状态计算失败:', error.message);
            console.log('错误堆栈:', error.stack);
        }
        
        // 5. 测试完整的心理活动生成
        console.log('\n5. 测试完整的心理活动生成...');
        try {
            const mockPsychologyState = {
                focus: 75.0,
                energy: 60.0,
                fatigue: 40.0,
                alertness: 70.0,
                hunger: 20.0,
                stress: 50.0,
                mood: 65.0,
                timePeriod: 'afternoon'
            };
            
            const mockWorldTreeConfig = {
                worldBackground: '你是雨安安，一位AI研究专家',
                timeArchitecture: {
                    afternoon: '下午时分，思维活跃'
                }
            };
            
            const mockFullContext = {
                timestamp: '2025/7/20 16:02:01',
                timePeriod: 'afternoon',
                recentConversations: [
                    {
                        speaker: '用户',
                        content: '测试natural库的修复效果，看看文本分析是否正常工作。',
                        timestamp: '2025/7/20 16:01:30'
                    }
                ]
            };
            
            // 测试本地心理内容生成（包含文本分析）
            const localContent = await worldTreeVCP.generateLocalPsychologyContent(
                mockPsychologyState,
                mockWorldTreeConfig,
                {}
            );
            
            console.log('✅ 本地心理内容生成成功:');
            console.log(`  内容: "${localContent}"`);
            
        } catch (error) {
            console.log('❌ 心理活动生成失败:', error.message);
            console.log('错误堆栈:', error.stack);
        }
        
        console.log('\n🎉 natural库修复测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 运行测试
testNaturalFix().catch(console.error);
