@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap');
@import url('fontawesome-free-6.6.0-web/css/all.min.css');

/* 全局样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: "RuanMengKeAiShouXieZi", sans-serif;
    background-color: #f5f7fb;
    color: #000000;
    line-height: 1.6;
    font-size: 14px;
    height: auto;
    overflow: auto;
}

/* 聊天界面容器 */
.chat-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    height: auto;
    max-width: 100%;
    margin: 0 auto;
    overflow: visible;
}

/* 聊天头部 */
.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    background-color: #f2f3f5;
    border-bottom: 1px solid #e3e5e8;
    z-index: 10;
}

.header-left {
    display: flex;
    align-items: center;
}

.bot-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
}

.bot-name {
    font-weight: 700;
    font-size: 16px;
    color: #23272a;
}

.online-status {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #43b581;
    border-radius: 50%;
    margin-left: 8px;
}

.header-right {
    display: flex;
    gap: 16px;
}

.header-icon {
    color: #72767d;
    font-size: 20px;
    cursor: pointer;
}

.header-icon:hover {
    color: #5865f2;
}

/* 聊天内容区域 */
.chat-messages {
    padding: 16px;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    height: auto;
    overflow: visible;
}

/* 消息样式 */
.message {
    display: flex;
    margin-bottom: 16px;
    position: relative;
    width: 100%;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 16px;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 56px);
    /* 减去头像和间距的宽度 */
}

.message-author {
    font-size: 16px;
    font-weight: 500;
    color: #23272a;
    margin-bottom: 4px;
}

.message-text {
    background-color: #f2f3f5;
    padding: 10px 12px;
    border-radius: 0 8px 8px 8px;
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 自己发送的消息样式 */
.message.self {
    flex-direction: row-reverse;
}

.message.self .message-avatar {
    margin-right: 0;
    margin-left: 16px;
}

.message.self .message-content {
    align-items: flex-end;
}

.message.self .message-text {
    background-color: #5865f2;
    color: white;
    border-radius: 8px 0 8px 8px;
}

/* 聊天输入区域 */
.chat-input-container {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background-color: #f2f3f5;
    border-top: 1px solid #e3e5e8;
}

.chat-input {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 24px;
    background-color: #ffffff;
    font-size: 14px;
    outline: none;
}

.send-button {
    background-color: #5865f2;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin-left: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

/* 工具栏样式 */
.chat-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 8px 16px;
    background-color: #18191c;
    border-top: 1px solid #2c2f33;
}

.toolbar-icon {
    color: #ffffff;
    font-size: 22px;
    cursor: pointer;
}

/* Markdown内容容器 */
.markdown-content {
    font-family: "RuanMengKeAiShouXieZi", sans-serif;
    font-size: 14px;
    color: #23272a;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    line-height: 1.5;
    width: 100%;
}

.self .markdown-content {
    color: white;
}

/* 段落样式 */
.markdown-content p {
    margin-bottom: 8px;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
}

.self .markdown-content p {
    color: white;
}

/* 链接样式 */
.markdown-content a {
    color: #00b0f4;
    text-decoration: none;
}

.markdown-content a:hover {
    text-decoration: underline;
}

.self .markdown-content a {
    color: #99aab5;
}

/* 列表样式 */
.markdown-content ol,
.markdown-content ul {
    padding-left: 20px;
    margin-bottom: 8px;
}

.markdown-content ol li,
.markdown-content ul li {
    margin-bottom: 4px;
}

/* 内联代码样式 */
.markdown-content code {
    font-size: 14px;
    background-color: #f6f7f8;
    padding: 2px 4px;
    border-radius: 4px;
    color: #e74c3c;
    font-family: "SourceCodePro-Italic-VariableFont_wght", monospace;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
}

.self .markdown-content code {
    background-color: #4752c4;
    color: #ffffff;
}

/* 代码块样式 */
.markdown-content pre {
    position: relative;
    background-color: #2f3136;
    color: #dcddde;
    padding: 32px 16px 16px;
    border-radius: 8px;
    margin: 8px 0;
    font-size: 14px;
    font-family: "SourceCodePro-Italic-VariableFont_wght", monospace;
    max-width: 100%;
    overflow-x: auto;
}

/* 代码块内代码 */
.markdown-content pre code {
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap;
    background-color: transparent;
    color: inherit;
    padding: 0;
    border-radius: 0;
}

/* 代码块顶部语言标签 */
.markdown-content pre[data-lang]::after {
    content: attr(data-lang);
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #7289da;
    color: #ffffff;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    text-transform: uppercase;
}

/* 代码块顶部的红绿黄圆点 */
.markdown-content pre::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 12px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ff5f56;
    box-shadow: 20px 0 0 #ffbd2e, 40px 0 0 #27c93f;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: #c4c9d4;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #7a7f8c;
}

/* 引用块样式 */
.markdown-content blockquote {
    border-left: 4px solid #7289da;
    padding: 8px 12px;
    margin: 8px 0;
    background-color: #f6f7f8;
    border-radius: 4px;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
}

.self .markdown-content blockquote {
    border-left-color: #99aab5;
    background-color: #4752c4;
}

/* 图片样式 */
.markdown-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 8px 0;
}

/* 强调文本样式 */
.markdown-content .highlight {
    background-color: #ffeaa7;
    color: #e84393;
    padding: 2px 4px;
    border-radius: 4px;
}

.self .markdown-content .highlight {
    background-color: #4752c4;
    color: #ffd700;
}

/* 特定符号包裹的文本样式 */
.markdown-content .double-star {
    color: #7289da;
    font-weight: bold;
}

.self .markdown-content .double-star {
    color: #ffffff;
    font-weight: bold;
}

.markdown-content .single-underline {
    text-decoration: underline;
    color: #43b581;
}

.self .markdown-content .single-underline {
    color: #99aab5;
}

/* 表格样式 */
.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 8px 0;
    border-radius: 8px;
    overflow: hidden;
    table-layout: fixed;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #e3e5e8;
    text-align: left;
    padding: 8px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
}

.markdown-content th {
    background-color: #5865f2;
    color: white;
}

.self .markdown-content table {
    color: white;
}

.self .markdown-content th,
.self .markdown-content td {
    border-color: #4752c4;
}

/* KaTeX 样式 */
.katex {
    font-family: "KaTeX_Main", Arial !important;
    font-size: 1em !important;
    white-space: normal;
    display: inline-block !important;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
}

/* 字体引入 */
@font-face {
    font-family: "RuanMengKeAiShouXieZi";
    src: url("TengXiangJiaLiDaYuanJian-1.ttf");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "SourceCodePro-Italic-VariableFont_wght";
    src: url("SourceCodePro-Italic-VariableFont_wght.ttf");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "KaTeX_Main";
    src: url("KaTeX_Main-Regular.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .message-text {
        max-width: 100%;
    }

    .header-right {
        gap: 12px;
    }

    .header-icon {
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .chat-header {
        padding: 8px 12px;
    }

    .bot-avatar {
        width: 28px;
        height: 28px;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
    }

    .chat-messages {
        padding: 12px;
    }

    .chat-input-container {
        padding: 8px 12px;
    }

    .header-right {
        gap: 8px;
    }

    .header-icon {
        font-size: 16px;
    }
}