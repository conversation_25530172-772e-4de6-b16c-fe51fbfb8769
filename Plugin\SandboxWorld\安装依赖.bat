@echo off
chcp 65001 >nul
title VCPToolBox 沙盒世界 - 安装依赖

echo.
echo ========================================
echo    VCPToolBox 沙盒世界 - 安装依赖
echo ========================================
echo.

cd /d "%~dp0"

echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js
    echo.
    echo 请先安装Node.js 14.0.0或更高版本
    echo 下载地址: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过
node --version

echo.
echo 📦 开始安装依赖包...
call npm install

if errorlevel 1 (
    echo.
    echo ❌ 依赖包安装失败
    echo 请检查网络连接或尝试使用国内镜像:
    echo npm config set registry https://registry.npmmirror.com
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 依赖包安装完成！

echo.
echo 🔧 运行安装脚本...
node install.js

if errorlevel 1 (
    echo.
    echo ❌ 安装脚本执行失败
    pause
    exit /b 1
)

echo.
echo 🎉 安装完成！
echo.
echo 下一步:
echo 1. 双击 "启动沙盒世界.bat" 启动系统
echo 2. 或运行: node start.js
echo 3. 访问Web界面: http://localhost:8080
echo.

pause
