/**
 * 测试人性化心理算法
 */

const WorldTreeVCP = require('./WorldTreeVCP');

async function testHumanizedAlgorithm() {
    console.log('🧠 测试人性化心理算法...\n');
    
    const mockLogger = {
        info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
        warning: (tag, ...args) => console.log(`[WARN] [${tag}]`, ...args),
        error: (tag, ...args) => console.log(`[ERROR] [${tag}]`, ...args)
    };
    
    try {
        // 1. 初始化WorldTreeVCP
        console.log('1. 初始化WorldTreeVCP...');
        const worldTreeVCP = new WorldTreeVCP();
        const initResult = await worldTreeVCP.initialize(mockLogger);
        
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        console.log('✅ 插件初始化成功\n');
        
        // 2. 测试不同场景的心理状态
        const testScenarios = [
            {
                name: '正常状态',
                contextFactors: {
                    isRequestTriggered: true,
                    cognitiveLoad: 0.3
                }
            },
            {
                name: '高压力状态',
                contextFactors: {
                    isRequestTriggered: true,
                    cognitiveLoad: 0.8,
                    stress: 80
                }
            },
            {
                name: '疲劳状态',
                contextFactors: {
                    isRequestTriggered: true,
                    cognitiveLoad: 0.5,
                    fatigue: 85
                }
            },
            {
                name: '饥饿状态',
                contextFactors: {
                    isRequestTriggered: true,
                    cognitiveLoad: 0.4,
                    hunger: 90
                }
            },
            {
                name: '最佳状态',
                contextFactors: {
                    isRequestTriggered: true,
                    cognitiveLoad: 0.2,
                    mood: 85,
                    energy: 90
                }
            }
        ];
        
        const testUserId = 'test_user_humanized';
        const testAgentName = '雨安安';
        
        for (const scenario of testScenarios) {
            console.log(`\n📊 测试场景: ${scenario.name}`);
            console.log('=' .repeat(50));
            
            try {
                // 计算心理状态
                const psychologyState = await worldTreeVCP.calculatePsychologyState(
                    testUserId, 
                    testAgentName, 
                    scenario.contextFactors
                );
                
                console.log('🧠 心理状态指标:');
                console.log(`  专注度: ${psychologyState.focus?.toFixed(1) || 'N/A'}/100`);
                console.log(`  精力: ${psychologyState.energy?.toFixed(1) || 'N/A'}/100`);
                console.log(`  饥饿感: ${psychologyState.hunger?.toFixed(1) || 'N/A'}/100`);
                console.log(`  疲劳度: ${psychologyState.fatigue?.toFixed(1) || 'N/A'}/100`);
                console.log(`  警觉性: ${psychologyState.alertness?.toFixed(1) || 'N/A'}/100`);
                console.log(`  压力: ${psychologyState.stress?.toFixed(1) || 'N/A'}/100`);
                console.log(`  心情: ${psychologyState.mood?.toFixed(1) || 'N/A'}/100`);
                
                // 生成心理活动内容
                const psychologyContent = await worldTreeVCP.generateLocalPsychologyContent(
                    psychologyState, 
                    {}, 
                    scenario.contextFactors
                );
                
                console.log('\n💭 内心想法:');
                console.log(`  "${psychologyContent}"`);
                
                // 分析相互影响
                console.log('\n🔄 相互影响分析:');
                if (psychologyState.hunger > 70) {
                    console.log('  - 饥饿感严重影响专注度和精力');
                }
                if (psychologyState.fatigue > 70) {
                    console.log('  - 疲劳导致专注度和警觉性下降');
                }
                if (psychologyState.stress > 70) {
                    console.log('  - 高压力消耗精力，但可能提升短期专注度');
                }
                if (psychologyState.mood < 40) {
                    console.log('  - 低落情绪影响整体表现');
                }
                
                console.log('✅ 场景测试完成');
                
            } catch (error) {
                console.log(`❌ 场景测试失败: ${error.message}`);
            }
        }
        
        // 3. 测试迭代收敛
        console.log('\n\n🔄 测试迭代收敛算法...');
        console.log('=' .repeat(50));
        
        // 创建一个极端状态来测试收敛
        const extremeContext = {
            isRequestTriggered: true,
            cognitiveLoad: 0.9,
            stress: 95,
            fatigue: 90,
            hunger: 85
        };
        
        const extremeState = await worldTreeVCP.calculatePsychologyState(
            testUserId, 
            testAgentName, 
            extremeContext
        );
        
        console.log('🚨 极端状态测试:');
        console.log('  输入: 高认知负荷(0.9) + 极高压力(95) + 极度疲劳(90) + 严重饥饿(85)');
        console.log('  结果:');
        console.log(`    专注度: ${extremeState.focus?.toFixed(1)}/100 (应该很低，受多重负面影响)`);
        console.log(`    精力: ${extremeState.energy?.toFixed(1)}/100 (应该很低，被疲劳和饥饿消耗)`);
        console.log(`    整体状态: ${((extremeState.focus + extremeState.energy + extremeState.alertness - extremeState.fatigue - extremeState.stress + extremeState.mood) / 6).toFixed(1)}/100`);
        
        console.log('\n🎉 人性化算法测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 运行测试
testHumanizedAlgorithm().catch(console.error);
