{"manifestVersion": "1.0.0", "name": "FileProcessor", "displayName": "图文件处理器", "version": "1.0.0", "description": "支持markdown链接提取、图片base64转换、文件文本提取和AI分析的综合文件处理插件", "author": "VCPToolBox", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node FileProcessor.js"}, "communication": {"protocol": "stdio", "timeout": 300000}, "configSchema": {"FILEPROCESSOR_API_URL": {"type": "string", "description": "API服务地址", "required": false, "default": "https://api.openai.com/v1/chat/completions"}, "FILEPROCESSOR_API_KEY": {"type": "string", "description": "API密钥", "required": true}, "FILEPROCESSOR_IMAGE_MODELS": {"type": "string", "description": "图片处理模型列表（用逗号分隔）", "required": false, "default": "gpt-4o,gpt-4-vision-preview,claude-3-sonnet"}, "FILEPROCESSOR_FILE_MODELS": {"type": "string", "description": "文件处理模型列表（用逗号分隔）", "required": false, "default": "gpt-4o,gpt-4-turbo,claude-3-sonnet"}, "FILEPROCESSOR_MAX_FILE_SIZE": {"type": "integer", "description": "最大文件大小限制（字节）", "required": false, "default": 52428800, "minimum": 1048576, "maximum": 104857600}, "FILEPROCESSOR_DOWNLOAD_TIMEOUT": {"type": "integer", "description": "文件下载超时时间（秒）", "required": false, "default": 30, "minimum": 5, "maximum": 300}, "FILEPROCESSOR_API_TIMEOUT": {"type": "integer", "description": "API调用超时时间（秒）", "required": false, "default": 120, "minimum": 10, "maximum": 600}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "ProcessFiles", "description": "调用此工具处理多种类型的文件链接。请在您的回复中，使用以下精确格式来请求文件处理，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」FileProcessor「末」,\nlinks:「始」(必需) 文件链接数组，如：[\"http://example.com/image.jpg\", \"http://example.com/doc.pdf\"]「末」,\nprompts:「始」(必需) 对应每个文件的分析提示词数组，如：[\"分析图片内容\", \"总结文档要点\"]「末」,\nmodels:「始」(可选) 指定每个文件使用的模型数组，如：[\"gpt-4o\", \"gpt-4-turbo\"]「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n1. 支持的文件格式：图片(.jpg,.png,.gif,.webp等)、文档(.pdf,.txt,.doc,.docx等)、代码(.js,.py,.go,.cpp等)、表格(.xlsx,.csv等)\n2. 最多处理4个链接，最少1个链接\n3. links和prompts数组长度必须一致，或prompts只有1个元素（应用到所有文件）\n4. models数组可选，长度应与links一致，或只有1个元素，或不提供（自动选择）\n5. 图片文件会转换为base64格式并调用视觉模型分析\n6. 其他文件会提取文本内容并调用文本模型分析\n7. 链接必须包含有效的文件后缀名", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」FileProcessor「末」,\nlinks:「始」[\"http://localhost:6005/images/cat.jpg\", \"http://example.com/doc.pdf\", \"http://example.com/code.py\"]「末」,\nprompts:「始」[\"分析这张图片的内容和风格\", \"总结文档的主要内容\", \"分析代码的功能和结构\"]「末」,\nmodels:「始」[\"gpt-4o\", \"gpt-4-turbo\", \"gpt-4-turbo\"]「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}, "dependencies": {"required": ["axios", "mime-types"], "optional": ["pdf-parse", "textract", "xlsx"]}}