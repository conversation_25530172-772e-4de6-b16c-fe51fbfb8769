# Generated by h2py from d:\mssdk\include\winnetwk.h
WNNC_NET_MSNET = 0x00010000
WNNC_NET_LANMAN = 0x00020000
WNNC_NET_NETWARE = 0x00030000
WNNC_NET_VINES = 0x00040000
WNNC_NET_10NET = 0x00050000
WNNC_NET_LOCUS = 0x00060000
WNNC_NET_SUN_PC_NFS = 0x00070000
WNNC_NET_LANSTEP = 0x00080000
WNNC_NET_9TILES = 0x00090000
WNNC_NET_LANTASTIC = 0x000A0000
WNNC_NET_AS400 = 0x000B0000
WNNC_NET_FTP_NFS = 0x000C0000
WNNC_NET_PATHWORKS = 0x000D0000
WNNC_NET_LIFENET = 0x000E0000
WNNC_NET_POWERLAN = 0x000F0000
WNNC_NET_BWNFS = 0x00100000
WNNC_NET_COGENT = 0x00110000
WNNC_NET_FARALLON = 0x00120000
WNNC_NET_APPLETALK = 0x00130000
WNNC_NET_INTERGRAPH = 0x00140000
WNNC_NET_SYMFONET = 0x00150000
WNNC_NET_CLEARCASE = 0x00160000
WNNC_NET_FRONTIER = 0x00170000
WNNC_NET_BMC = 0x00180000
WNNC_NET_DCE = 0x00190000
WNNC_NET_DECORB = 0x00200000
WNNC_NET_PROTSTOR = 0x00210000
WNNC_NET_FJ_REDIR = 0x00220000
WNNC_NET_DISTINCT = 0x00230000
WNNC_NET_TWINS = 0x00240000
WNNC_NET_RDR2SAMPLE = 0x00250000
RESOURCE_CONNECTED = 0x00000001
RESOURCE_GLOBALNET = 0x00000002
RESOURCE_REMEMBERED = 0x00000003
RESOURCE_RECENT = 0x00000004
RESOURCE_CONTEXT = 0x00000005
RESOURCETYPE_ANY = 0x00000000
RESOURCETYPE_DISK = 0x00000001
RESOURCETYPE_PRINT = 0x00000002
RESOURCETYPE_RESERVED = 0x00000008
RESOURCETYPE_UNKNOWN = 0xFFFFFFFF
RESOURCEUSAGE_CONNECTABLE = 0x00000001
RESOURCEUSAGE_CONTAINER = 0x00000002
RESOURCEUSAGE_NOLOCALDEVICE = 0x00000004
RESOURCEUSAGE_SIBLING = 0x00000008
RESOURCEUSAGE_ATTACHED = 0x00000010
RESOURCEUSAGE_ALL = (
    RESOURCEUSAGE_CONNECTABLE | RESOURCEUSAGE_CONTAINER | RESOURCEUSAGE_ATTACHED
)
RESOURCEUSAGE_RESERVED = 0x80000000
RESOURCEDISPLAYTYPE_GENERIC = 0x00000000
RESOURCEDISPLAYTYPE_DOMAIN = 0x00000001
RESOURCEDISPLAYTYPE_SERVER = 0x00000002
RESOURCEDISPLAYTYPE_SHARE = 0x00000003
RESOURCEDISPLAYTYPE_FILE = 0x00000004
RESOURCEDISPLAYTYPE_GROUP = 0x00000005
RESOURCEDISPLAYTYPE_NETWORK = 0x00000006
RESOURCEDISPLAYTYPE_ROOT = 0x00000007
RESOURCEDISPLAYTYPE_SHAREADMIN = 0x00000008
RESOURCEDISPLAYTYPE_DIRECTORY = 0x00000009
RESOURCEDISPLAYTYPE_TREE = 0x0000000A
RESOURCEDISPLAYTYPE_NDSCONTAINER = 0x0000000B
NETPROPERTY_PERSISTENT = 1
CONNECT_UPDATE_PROFILE = 0x00000001
CONNECT_UPDATE_RECENT = 0x00000002
CONNECT_TEMPORARY = 0x00000004
CONNECT_INTERACTIVE = 0x00000008
CONNECT_PROMPT = 0x00000010
CONNECT_NEED_DRIVE = 0x00000020
CONNECT_REFCOUNT = 0x00000040
CONNECT_REDIRECT = 0x00000080
CONNECT_LOCALDRIVE = 0x00000100
CONNECT_CURRENT_MEDIA = 0x00000200
CONNECT_DEFERRED = 0x00000400
CONNECT_RESERVED = 0xFF000000
CONNDLG_RO_PATH = 0x00000001
CONNDLG_CONN_POINT = 0x00000002
CONNDLG_USE_MRU = 0x00000004
CONNDLG_HIDE_BOX = 0x00000008
CONNDLG_PERSIST = 0x00000010
CONNDLG_NOT_PERSIST = 0x00000020
DISC_UPDATE_PROFILE = 0x00000001
DISC_NO_FORCE = 0x00000040
UNIVERSAL_NAME_INFO_LEVEL = 0x00000001
REMOTE_NAME_INFO_LEVEL = 0x00000002
WNFMT_MULTILINE = 0x01
WNFMT_ABBREVIATED = 0x02
WNFMT_INENUM = 0x10
WNFMT_CONNECTION = 0x20
NETINFO_DLL16 = 0x00000001
NETINFO_DISKRED = 0x00000004
NETINFO_PRINTERRED = 0x00000008
RP_LOGON = 0x01
RP_INIFILE = 0x02
PP_DISPLAYERRORS = 0x01
WNCON_FORNETCARD = 0x00000001
WNCON_NOTROUTED = 0x00000002
WNCON_SLOWLINK = 0x00000004
WNCON_DYNAMIC = 0x00000008
