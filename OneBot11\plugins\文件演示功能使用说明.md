# 文件演示功能使用说明

## 🔧 问题修复状态
**最新修复 (2025-07-05):**
- ✅ 修复了context.message和context.segments的使用问题
- ✅ 文件演示现在使用正确的消息段数组来提取文件和图片
- ✅ 添加了详细的调试信息和错误处理
- ✅ 移除了所有emoji符号，改为纯文本显示
- ✅ 优化了extractFiles方法，正确映射OneBot11文件消息段字段
- ✅ 添加了对视频(video)和语音(record)类型的支持
- ✅ 新增"消息段调试"功能，可查看原始消息段结构

## 功能概述
文件演示功能用于测试OneBot11适配器的文件和图片处理能力，包括文件信息获取、链接提取等功能。

## 使用方法

### 方法一：先发送文件，再触发演示
1. 在群聊或私聊中发送文件或图片
2. 发送文本消息"文件演示"来触发功能演示

### 方法二：同时发送文件和触发词
1. 发送文件或图片的同时，在消息中包含"文件演示"文字

## 支持的文件类型
- **文件**: 任何类型的文件（文档、压缩包、音频、视频等）
- **图片**: JPG、PNG、GIF等图片格式

## 演示功能
当检测到文件或图片时，系统会：

### 对于文件：
- 显示文件名
- 显示文件大小（格式化显示）
- 获取文件下载链接
- 调用NapCat API获取详细文件信息

### 对于图片：
- 显示图片序号
- 显示图片URL或文件标识
- 提取图片基本信息

## 技术实现
- 使用`helper.extractFiles()`提取消息中的文件段
- 使用`helper.extractImages()`提取消息中的图片段
- 调用`napCatAPI.file.getInfo()`获取详细文件信息
- 支持错误处理和异常情况反馈

## 注意事项
1. 如果只发送"文件演示"文字而没有文件，会显示使用说明
2. 文件信息获取依赖于NapCat API的正常工作
3. 某些文件类型可能无法获取完整信息
4. 大文件的处理可能需要更长时间

## 调试信息
系统会在控制台输出调试信息，包括：
- 消息类型和内容
- 提取到的文件和图片数量
- API调用结果

## 示例使用场景
```
用户: [发送一个PDF文件]
用户: 文件演示
机器人: 文件功能演示

检测到文件:
• 文件名: 示例文档.pdf
• 大小: 2.5 MB
• 链接: https://example.com/file/xxx
```
