/**
 * 大脑皮层模拟系统
 * 模拟大脑皮层的神经网络连接、认知处理、情感调节等功能
 * 基于神经科学原理实现高级认知模拟
 */

class CorticalSimulationSystem {
    constructor(config, logger, openaiService) {
        this.config = config;
        this.logger = logger;
        this.openaiService = openaiService;
        
        // 大脑皮层区域定义
        this.corticalAreas = {
            // 前额叶皮层 - 执行功能
            prefrontal: {
                name: '前额叶皮层',
                functions: ['执行控制', '工作记忆', '决策制定', '计划', '抑制控制'],
                connections: ['temporal', 'parietal', 'cingulate', 'limbic'],
                baseActivation: 0.4,
                plasticityRate: 0.8,
                fatigueThreshold: 0.9
            },
            
            // 颞叶皮层 - 语言和记忆
            temporal: {
                name: '颞叶皮层',
                functions: ['语言理解', '语义记忆', '概念处理', '听觉处理'],
                connections: ['prefrontal', 'parietal', 'occipital', 'hippocampus'],
                baseActivation: 0.5,
                plasticityRate: 0.9,
                fatigueThreshold: 0.8
            },
            
            // 顶叶皮层 - 空间和注意力
            parietal: {
                name: '顶叶皮层',
                functions: ['空间认知', '注意力控制', '感觉整合', '数字处理'],
                connections: ['prefrontal', 'temporal', 'occipital', 'motor'],
                baseActivation: 0.3,
                plasticityRate: 0.7,
                fatigueThreshold: 0.85
            },
            
            // 枕叶皮层 - 视觉处理
            occipital: {
                name: '枕叶皮层',
                functions: ['视觉处理', '模式识别', '空间定位'],
                connections: ['temporal', 'parietal'],
                baseActivation: 0.2,
                plasticityRate: 0.6,
                fatigueThreshold: 0.7
            },
            
            // 扣带皮层 - 情感和认知控制
            cingulate: {
                name: '扣带皮层',
                functions: ['情感调节', '冲突监控', '疼痛处理', '共情'],
                connections: ['prefrontal', 'limbic', 'insula'],
                baseActivation: 0.6,
                plasticityRate: 1.0,
                fatigueThreshold: 0.95
            },
            
            // 岛叶皮层 - 内感受和情感意识
            insula: {
                name: '岛叶皮层',
                functions: ['内感受', '情感意识', '社会认知', '风险评估'],
                connections: ['cingulate', 'prefrontal', 'limbic'],
                baseActivation: 0.5,
                plasticityRate: 0.9,
                fatigueThreshold: 0.9
            }
        };
        
        // 神经网络参数
        this.networkParameters = {
            maxConnections: 1000,
            synapticStrength: 0.5,
            learningRate: 0.01,
            decayRate: 0.001,
            noiseLevel: 0.02,
            synchronyThreshold: 0.7
        };
        
        // 神经递质系统
        this.neurotransmitters = {
            dopamine: { level: 0.5, function: '奖励和动机', halfLife: 30 },
            serotonin: { level: 0.5, function: '情绪调节', halfLife: 60 },
            norepinephrine: { level: 0.5, function: '注意力和觉醒', halfLife: 20 },
            acetylcholine: { level: 0.5, function: '学习和记忆', halfLife: 10 },
            gaba: { level: 0.5, function: '抑制和平静', halfLife: 5 }
        };
        
        // 当前皮层状态
        this.currentState = {
            activations: {},
            connections: {},
            neurotransmitterLevels: { ...this.neurotransmitters },
            globalWorkspace: 0.5,
            consciousness: 0.5
        };
        
        this.initializeCorticalState();
    }

    /**
     * 初始化皮层状态
     */
    initializeCorticalState() {
        // 初始化各区域激活水平
        for (const [area, config] of Object.entries(this.corticalAreas)) {
            this.currentState.activations[area] = config.baseActivation;
        }
        
        // 初始化连接强度
        for (const [area, config] of Object.entries(this.corticalAreas)) {
            this.currentState.connections[area] = {};
            for (const connectedArea of config.connections) {
                this.currentState.connections[area][connectedArea] = this.networkParameters.synapticStrength;
            }
        }
    }

    /**
     * 模拟皮层处理过程
     */
    async simulateCorticalProcessing(inputStimuli, currentStates, context = {}) {
        try {
            this.logger.info('皮层模拟', '开始皮层处理模拟');

            // 1. 感觉输入处理
            const sensoryProcessing = await this.processSensoryInput(inputStimuli);
            
            // 2. 皮层区域激活计算
            const corticalActivation = await this.calculateCorticalActivation(sensoryProcessing, currentStates);
            
            // 3. 神经网络传播
            const networkPropagation = await this.simulateNetworkPropagation(corticalActivation);
            
            // 4. 全局工作空间整合
            const globalIntegration = await this.simulateGlobalWorkspace(networkPropagation);
            
            // 5. 意识状态计算
            const consciousnessState = await this.calculateConsciousnessState(globalIntegration);
            
            // 6. 神经递质调节
            const neurotransmitterAdjustment = await this.adjustNeurotransmitters(consciousnessState, currentStates);
            
            // 7. 更新皮层状态
            this.updateCorticalState(corticalActivation, networkPropagation, neurotransmitterAdjustment);

            const result = {
                cortical_activation: corticalActivation,
                network_state: networkPropagation,
                global_workspace: globalIntegration,
                consciousness: consciousnessState,
                neurotransmitters: neurotransmitterAdjustment,
                processing_efficiency: this.calculateProcessingEfficiency(),
                cognitive_load: this.calculateCognitiveLoad()
            };

            this.logger.success('皮层模拟', '皮层处理模拟完成');
            return result;

        } catch (error) {
            this.logger.error('皮层模拟', '皮层处理失败:', error.message);
            throw error;
        }
    }

    /**
     * 处理感觉输入
     */
    async processSensoryInput(inputStimuli) {
        try {
            const { userMessage, aiResponse, emotionalContext } = inputStimuli;
            
            // 语言输入处理（主要激活颞叶）
            const languageActivation = this.processLanguageInput(userMessage, aiResponse);
            
            // 情感输入处理（主要激活扣带皮层和岛叶）
            const emotionalActivation = this.processEmotionalInput(emotionalContext);
            
            // 认知负荷评估（影响前额叶）
            const cognitiveLoad = this.assessCognitiveLoad(userMessage, aiResponse);
            
            return {
                language: languageActivation,
                emotion: emotionalActivation,
                cognitive_load: cognitiveLoad,
                overall_intensity: (languageActivation + emotionalActivation + cognitiveLoad) / 3
            };

        } catch (error) {
            this.logger.error('皮层模拟', '感觉输入处理失败:', error.message);
            return { language: 0.5, emotion: 0.5, cognitive_load: 0.5, overall_intensity: 0.5 };
        }
    }

    /**
     * 处理语言输入
     */
    processLanguageInput(userMessage, aiResponse) {
        const totalLength = (userMessage?.length || 0) + (aiResponse?.length || 0);
        const wordCount = (userMessage?.split(/\s+/).length || 0) + (aiResponse?.split(/\s+/).length || 0);
        
        // 基于长度和词汇复杂度计算激活强度
        const lengthFactor = Math.min(1.0, totalLength / 1000);
        const complexityFactor = Math.min(1.0, wordCount / 200);
        
        return (lengthFactor + complexityFactor) / 2;
    }

    /**
     * 处理情感输入
     */
    processEmotionalInput(emotionalContext) {
        if (!emotionalContext) return 0.3;
        
        const { valence = 0, arousal = 0, intensity = 0.5 } = emotionalContext;
        
        // 情感强度基于效价、唤醒度和强度
        const emotionalIntensity = (Math.abs(valence) + arousal + intensity) / 3;
        
        return Math.min(1.0, emotionalIntensity);
    }

    /**
     * 评估认知负荷
     */
    assessCognitiveLoad(userMessage, aiResponse) {
        // 基于消息复杂度和处理需求评估认知负荷
        const messageComplexity = this.calculateMessageComplexity(userMessage);
        const responseComplexity = this.calculateMessageComplexity(aiResponse);
        
        return (messageComplexity + responseComplexity) / 2;
    }

    /**
     * 计算消息复杂度
     */
    calculateMessageComplexity(message) {
        if (!message) return 0;
        
        const length = message.length;
        const sentences = message.split(/[.!?。！？]/).length;
        const words = message.split(/\s+/).length;
        
        // 复杂度基于长度、句子数和词汇密度
        const lengthComplexity = Math.min(1.0, length / 500);
        const structureComplexity = Math.min(1.0, sentences / 10);
        const densityComplexity = words > 0 ? Math.min(1.0, length / words / 10) : 0;
        
        return (lengthComplexity + structureComplexity + densityComplexity) / 3;
    }

    /**
     * 计算皮层激活
     */
    async calculateCorticalActivation(sensoryProcessing, currentStates) {
        try {
            const activations = {};
            
            // 计算各皮层区域的激活水平
            for (const [area, config] of Object.entries(this.corticalAreas)) {
                activations[area] = this.calculateAreaActivation(area, config, sensoryProcessing, currentStates);
            }
            
            // 应用侧抑制（竞争性抑制）
            const inhibitedActivations = this.applyLateralInhibition(activations);
            
            // 应用疲劳效应
            const fatigueAdjusted = this.applyFatigueEffects(inhibitedActivations);
            
            return fatigueAdjusted;

        } catch (error) {
            this.logger.error('皮层模拟', '皮层激活计算失败:', error.message);
            return this.currentState.activations;
        }
    }

    /**
     * 计算区域激活
     */
    calculateAreaActivation(area, config, sensoryProcessing, currentStates) {
        let activation = config.baseActivation;
        
        // 基于功能特化调整激活
        if (config.functions.includes('语言理解') || config.functions.includes('语义记忆')) {
            activation += sensoryProcessing.language * 0.5;
        }
        
        if (config.functions.includes('情感调节') || config.functions.includes('情感意识')) {
            activation += sensoryProcessing.emotion * 0.6;
        }
        
        if (config.functions.includes('执行控制') || config.functions.includes('工作记忆')) {
            activation += sensoryProcessing.cognitive_load * 0.4;
        }
        
        // 基于当前心理状态调整
        if (currentStates.stress && currentStates.stress.stress_value > 5) {
            if (config.functions.includes('执行控制')) {
                activation *= 1.2; // 压力增加执行控制需求
            }
        }
        
        if (currentStates.emotion && Math.abs(currentStates.emotion.emotion_value) > 30) {
            if (config.functions.includes('情感调节')) {
                activation *= 1.3; // 强情绪增加调节需求
            }
        }
        
        // 添加神经噪声
        const noise = (Math.random() - 0.5) * this.networkParameters.noiseLevel;
        activation += noise;
        
        // 限制在合理范围内
        return Math.max(0.1, Math.min(1.0, activation));
    }

    /**
     * 应用侧抑制
     */
    applyLateralInhibition(activations) {
        const inhibited = { ...activations };
        const totalActivation = Object.values(activations).reduce((sum, val) => sum + val, 0);
        
        // 如果总激活过高，应用竞争性抑制
        if (totalActivation > 3.0) {
            const inhibitionFactor = 3.0 / totalActivation;
            for (const area in inhibited) {
                inhibited[area] *= inhibitionFactor;
            }
        }
        
        return inhibited;
    }

    /**
     * 应用疲劳效应
     */
    applyFatigueEffects(activations) {
        const fatigued = { ...activations };
        
        for (const [area, activation] of Object.entries(activations)) {
            const config = this.corticalAreas[area];
            if (activation > config.fatigueThreshold) {
                // 超过疲劳阈值时降低激活
                const fatigueReduction = (activation - config.fatigueThreshold) * 0.5;
                fatigued[area] = activation - fatigueReduction;
            }
        }
        
        return fatigued;
    }

    /**
     * 模拟网络传播
     */
    async simulateNetworkPropagation(corticalActivation) {
        try {
            const propagation = {};
            const connectionStrengths = {};
            
            // 计算各连接的传播强度
            for (const [sourceArea, activation] of Object.entries(corticalActivation)) {
                propagation[sourceArea] = {};
                connectionStrengths[sourceArea] = {};
                
                const connections = this.currentState.connections[sourceArea] || {};
                
                for (const [targetArea, strength] of Object.entries(connections)) {
                    // 传播强度 = 源激活 × 连接强度 × 目标接受性
                    const targetReceptivity = corticalActivation[targetArea] || 0.5;
                    const propagationStrength = activation * strength * targetReceptivity;
                    
                    propagation[sourceArea][targetArea] = propagationStrength;
                    connectionStrengths[sourceArea][targetArea] = strength;
                }
            }
            
            // 计算网络同步性
            const synchrony = this.calculateNetworkSynchrony(corticalActivation);
            
            // 计算信息流
            const informationFlow = this.calculateInformationFlow(propagation);
            
            return {
                propagation,
                connection_strengths: connectionStrengths,
                network_synchrony: synchrony,
                information_flow: informationFlow,
                global_connectivity: this.calculateGlobalConnectivity(propagation)
            };

        } catch (error) {
            this.logger.error('皮层模拟', '网络传播模拟失败:', error.message);
            return { propagation: {}, network_synchrony: 0.5 };
        }
    }

    /**
     * 计算网络同步性
     */
    calculateNetworkSynchrony(activations) {
        const activationValues = Object.values(activations);
        if (activationValues.length < 2) return 0.5;

        // 计算激活水平的标准差
        const mean = activationValues.reduce((sum, val) => sum + val, 0) / activationValues.length;
        const variance = activationValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / activationValues.length;
        const stdDev = Math.sqrt(variance);

        // 同步性与标准差成反比
        return Math.max(0, 1 - stdDev);
    }

    /**
     * 计算信息流
     */
    calculateInformationFlow(propagation) {
        let totalFlow = 0;
        let connectionCount = 0;

        for (const sourceConnections of Object.values(propagation)) {
            for (const flowStrength of Object.values(sourceConnections)) {
                totalFlow += flowStrength;
                connectionCount++;
            }
        }

        return connectionCount > 0 ? totalFlow / connectionCount : 0;
    }

    /**
     * 计算全局连接性
     */
    calculateGlobalConnectivity(propagation) {
        const areas = Object.keys(propagation);
        let totalConnections = 0;
        let activeConnections = 0;

        for (const sourceArea of areas) {
            for (const targetArea of areas) {
                if (sourceArea !== targetArea) {
                    totalConnections++;
                    if (propagation[sourceArea] && propagation[sourceArea][targetArea] > 0.1) {
                        activeConnections++;
                    }
                }
            }
        }

        return totalConnections > 0 ? activeConnections / totalConnections : 0;
    }

    /**
     * 模拟全局工作空间
     */
    async simulateGlobalWorkspace(networkState) {
        try {
            // 全局工作空间理论：意识信息需要在多个脑区间广播
            const { propagation, network_synchrony, information_flow } = networkState;

            // 计算广播强度
            const broadcastStrength = this.calculateBroadcastStrength(propagation);

            // 计算竞争胜出的信息
            const winningCoalition = this.calculateWinningCoalition(propagation);

            // 计算全局可用性
            const globalAvailability = network_synchrony * information_flow * broadcastStrength;

            // 计算注意力聚焦
            const attentionalFocus = this.calculateAttentionalFocus(winningCoalition);

            return {
                broadcast_strength: broadcastStrength,
                winning_coalition: winningCoalition,
                global_availability: globalAvailability,
                attentional_focus: attentionalFocus,
                workspace_capacity: this.calculateWorkspaceCapacity(globalAvailability),
                information_integration: this.calculateInformationIntegration(networkState)
            };

        } catch (error) {
            this.logger.error('皮层模拟', '全局工作空间模拟失败:', error.message);
            return { global_availability: 0.5, workspace_capacity: 0.5 };
        }
    }

    /**
     * 计算广播强度
     */
    calculateBroadcastStrength(propagation) {
        let maxBroadcast = 0;

        for (const [sourceArea, connections] of Object.entries(propagation)) {
            const outgoingStrength = Object.values(connections).reduce((sum, val) => sum + val, 0);
            maxBroadcast = Math.max(maxBroadcast, outgoingStrength);
        }

        return Math.min(1.0, maxBroadcast);
    }

    /**
     * 计算获胜联盟
     */
    calculateWinningCoalition(propagation) {
        const coalitionStrengths = {};

        // 计算每个区域的联盟强度
        for (const [area, connections] of Object.entries(propagation)) {
            const incomingStrength = this.calculateIncomingStrength(area, propagation);
            const outgoingStrength = Object.values(connections).reduce((sum, val) => sum + val, 0);

            coalitionStrengths[area] = (incomingStrength + outgoingStrength) / 2;
        }

        // 找到最强的联盟
        const maxStrength = Math.max(...Object.values(coalitionStrengths));
        const winningAreas = Object.entries(coalitionStrengths)
            .filter(([area, strength]) => strength > maxStrength * 0.8)
            .map(([area, strength]) => area);

        return {
            areas: winningAreas,
            strength: maxStrength,
            coherence: this.calculateCoalitionCoherence(winningAreas, propagation)
        };
    }

    /**
     * 计算传入强度
     */
    calculateIncomingStrength(targetArea, propagation) {
        let incomingStrength = 0;

        for (const [sourceArea, connections] of Object.entries(propagation)) {
            if (connections[targetArea]) {
                incomingStrength += connections[targetArea];
            }
        }

        return incomingStrength;
    }

    /**
     * 计算意识状态
     */
    async calculateConsciousnessState(globalIntegration) {
        try {
            const {
                global_availability,
                workspace_capacity,
                information_integration,
                attentional_focus
            } = globalIntegration;

            // 意识水平基于多个因素
            const consciousnessLevel = (
                global_availability * 0.4 +
                workspace_capacity * 0.3 +
                information_integration * 0.2 +
                attentional_focus * 0.1
            );

            // 计算意识内容的丰富度
            const contentRichness = this.calculateContentRichness(globalIntegration);

            // 计算意识的统一性
            const unityOfConsciousness = this.calculateUnityOfConsciousness(globalIntegration);

            // 计算自我意识水平
            const selfAwareness = this.calculateSelfAwareness(consciousnessLevel, contentRichness);

            return {
                consciousness_level: Math.min(1.0, Math.max(0.0, consciousnessLevel)),
                content_richness: contentRichness,
                unity: unityOfConsciousness,
                self_awareness: selfAwareness,
                phenomenal_consciousness: this.calculatePhenomenalConsciousness(consciousnessLevel, contentRichness),
                access_consciousness: this.calculateAccessConsciousness(global_availability, workspace_capacity)
            };

        } catch (error) {
            this.logger.error('皮层模拟', '意识状态计算失败:', error.message);
            return { consciousness_level: 0.5, content_richness: 0.5 };
        }
    }

    /**
     * 调整神经递质
     */
    async adjustNeurotransmitters(consciousnessState, currentStates) {
        try {
            const adjustments = {};

            // 多巴胺调节（奖励和动机）
            adjustments.dopamine = this.adjustDopamine(consciousnessState, currentStates);

            // 血清素调节（情绪和幸福感）
            adjustments.serotonin = this.adjustSerotonin(consciousnessState, currentStates);

            // 去甲肾上腺素调节（注意力和觉醒）
            adjustments.norepinephrine = this.adjustNorepinephrine(consciousnessState, currentStates);

            // 乙酰胆碱调节（学习和记忆）
            adjustments.acetylcholine = this.adjustAcetylcholine(consciousnessState, currentStates);

            // GABA调节（抑制和平静）
            adjustments.gaba = this.adjustGABA(consciousnessState, currentStates);

            return adjustments;

        } catch (error) {
            this.logger.error('皮层模拟', '神经递质调节失败:', error.message);
            return this.currentState.neurotransmitterLevels;
        }
    }

    /**
     * 调整多巴胺
     */
    adjustDopamine(consciousnessState, currentStates) {
        let adjustment = 0;

        // 基于意识水平调整
        if (consciousnessState.consciousness_level > 0.7) {
            adjustment += 0.1; // 高意识水平增加多巴胺
        }

        // 基于好感度调整
        if (currentStates.affinity && currentStates.affinity.affinity_value > 50) {
            adjustment += 0.05; // 高好感度增加多巴胺
        }

        // 基于压力调整
        if (currentStates.stress && currentStates.stress.stress_value > 10) {
            adjustment -= 0.08; // 高压力降低多巴胺
        }

        const currentLevel = this.currentState.neurotransmitterLevels.dopamine.level;
        const newLevel = Math.max(0.1, Math.min(1.0, currentLevel + adjustment));

        return { level: newLevel, change: adjustment };
    }

    /**
     * 更新皮层状态
     */
    updateCorticalState(corticalActivation, networkPropagation, neurotransmitterAdjustment) {
        // 更新激活水平
        this.currentState.activations = { ...corticalActivation };

        // 更新连接强度（基于Hebbian学习）
        this.updateConnectionStrengths(networkPropagation);

        // 更新神经递质水平
        for (const [neurotransmitter, adjustment] of Object.entries(neurotransmitterAdjustment)) {
            if (this.currentState.neurotransmitterLevels[neurotransmitter]) {
                this.currentState.neurotransmitterLevels[neurotransmitter].level = adjustment.level;
            }
        }

        // 更新全局工作空间
        this.currentState.globalWorkspace = networkPropagation.global_connectivity || 0.5;
    }

    /**
     * 更新连接强度
     */
    updateConnectionStrengths(networkPropagation) {
        const learningRate = this.networkParameters.learningRate;
        const decayRate = this.networkParameters.decayRate;

        for (const [sourceArea, connections] of Object.entries(networkPropagation.propagation || {})) {
            for (const [targetArea, activity] of Object.entries(connections)) {
                if (this.currentState.connections[sourceArea] && this.currentState.connections[sourceArea][targetArea] !== undefined) {
                    // Hebbian学习：同时激活的连接增强
                    const currentStrength = this.currentState.connections[sourceArea][targetArea];
                    const strengthChange = activity * learningRate - currentStrength * decayRate;

                    this.currentState.connections[sourceArea][targetArea] = Math.max(0.1, Math.min(1.0,
                        currentStrength + strengthChange
                    ));
                }
            }
        }
    }

    /**
     * 计算处理效率
     */
    calculateProcessingEfficiency() {
        const activations = Object.values(this.currentState.activations);
        const averageActivation = activations.reduce((sum, val) => sum + val, 0) / activations.length;

        // 效率基于激活水平的平衡性
        const variance = activations.reduce((sum, val) => sum + Math.pow(val - averageActivation, 2), 0) / activations.length;

        return Math.max(0.1, 1 - variance);
    }

    /**
     * 计算认知负荷
     */
    calculateCognitiveLoad() {
        const totalActivation = Object.values(this.currentState.activations).reduce((sum, val) => sum + val, 0);
        const maxPossibleActivation = Object.keys(this.currentState.activations).length;

        return Math.min(1.0, totalActivation / maxPossibleActivation);
    }
}

module.exports = CorticalSimulationSystem;
