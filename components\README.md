# Components 组件模块

这个文件夹包含了从 server.js 中分离出来的各种功能组件，用于提高代码的可维护性和清晰度。

## 文件结构

### timeUtils.js (77行)
时间相关工具函数
- `formatBeijingTime()` - 北京时间格式化
- `getTimeAgo()` - 计算时间差
- `formatBytes()` - 格式化字节数
- `formatUptime()` - 格式化运行时间

### toolAnalysis.js (44行)
工具分析相关函数
- `formatToolAnalysisForSystem()` - 格式化工具分析结果为system消息格式

### memorySystem.js (237行)
情感记忆系统相关函数
- `recordEmotionMemory()` - 事务性情感记忆记录
- `generateIntelligentContext()` - 生成智能上下文

### imageProcessor.js (38行)
图片处理相关函数（保留基础功能）
- `validateImageFile()` - 验证图片文件
- `getMimeType()` - 获取MIME类型

### variableReplacer.js (128行)
变量替换相关函数
- `replaceCommonVariables()` - 替换通用变量和占位符
- `processMessagesVariableReplacement()` - 处理消息数组中的变量替换

### messageProcessor.js (258行)
消息处理相关函数
- `processUserMessage()` - 处理用户消息，添加用户ID前缀和图片路径解析
- `processAgentParameter()` - 处理Agent参数
- `processUserAgentParameter()` - 处理UserAgent参数
- `processMcpAgentParameter()` - 处理MCP模式的Agent参数（保留上下文）
- `processMcpUserAgentParameter()` - 处理MCP模式的UserAgent参数
- `cleanRequestBody()` - 清理请求体中的自定义参数

## 优化效果

### 优化前
- server.js: 约4154行（单一大文件）

### 优化后
- server.js: 3728行（减少了426行）
- components/: 780行（6个模块文件）
- 总计: 4508行

### 优化收益
1. **代码结构清晰**: 功能按模块分离，便于维护和理解
2. **复用性提高**: 组件可以在不同地方重复使用
3. **测试友好**: 每个组件可以独立测试
4. **减少重复**: 消除了重复的函数定义和重复的变量替换逻辑
5. **修复问题**: 解决了用户ID重复显示的问题和logger调用问题
6. **删除冗余**: 完全删除了不需要的多模态图片处理功能和ImageProcessor插件
7. **优化导入**: 所有组件直接引入logger.cjs，无需参数传递

## 使用方式

在 server.js 中通过 require 导入需要的组件：

```javascript
const { formatBeijingTime, getTimeAgo } = require('./components/timeUtils');
const { processUserMessage, cleanRequestBody } = require('./components/messageProcessor');
// ... 其他组件
```

## 注意事项

1. 所有组件都使用 CommonJS 模块格式 (module.exports)
2. 组件之间保持低耦合，避免循环依赖
3. 每个组件都包含详细的函数注释
4. 删除了不需要的多模态图片处理功能，简化了代码逻辑
