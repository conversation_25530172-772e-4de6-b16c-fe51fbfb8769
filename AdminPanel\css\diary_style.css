/* 死亡笔记风格 - 简化字体避免加载问题 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow: auto;
    height: auto;
}

body {
    font-family: 'Orbitron', 'Noto Sans SC', 'Microsoft YaHei', monospace;
    background:
        radial-gradient(ellipse at top left, rgba(30, 30, 60, 0.3) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(60, 20, 80, 0.2) 0%, transparent 50%),
        radial-gradient(ellipse at center, rgba(20, 20, 40, 0.4) 0%, transparent 70%),
        linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 30%, #16213e 70%, #0f0f23 100%);
    height: 100vh; /* 固定为视口高度 */
    color: #d0d0d0;
    position: relative;
    overflow: hidden; /* 防止body产生滚动条 */
    margin: 0;
    padding: 0;
}

/* 诡异雾气效果 - 阴森蓝紫色调 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(30, 30, 80, 0.1) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(80, 30, 120, 0.08) 0%, transparent 60%),
        radial-gradient(circle at 50% 50%, rgba(40, 40, 90, 0.06) 0%, transparent 80%);
    pointer-events: none;
    z-index: -1;
    animation: mistDrift 50s linear infinite;
    will-change: opacity;
}

/* 诡异监视线 - 动物园怪谈风格 */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 0, 0, 0.8), transparent);
    animation: watchingScan 15s linear infinite;
    pointer-events: none;
    z-index: 1000;
    box-shadow: 0 0 8px rgba(255, 0, 0, 0.4);
}

/* 简化动画以优化性能 */
@keyframes horrorPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
}

/* 删除复杂血液动画 */

/* 移除扫描线动画 */

/* 主布局 - 高度同步优化 */
.app-layout {
    display: grid;
    grid-template-columns: 420px 1fr;
    gap: 25px;
    height: 100vh; /* 固定为视口高度 */
    padding: 25px;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden; /* 防止整体滚动 */
    align-items: stretch; /* 确保两列高度相同 */
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 22px;
    height: 100%; /* 占满父容器高度 */
    padding: 0;
    box-sizing: border-box;
    overflow-y: auto; /* 垂直滚动 */
    overflow-x: hidden; /* 隐藏水平滚动 */
}

/* 确保侧边栏内容完整显示 */
.sidebar > * {
    flex-shrink: 0;
    width: 100%;
    box-sizing: border-box;
}

/* 防止内容被截断 */
.death-note-panel {
    min-height: auto;
    height: auto;
}

.death-eye-panel,
.death-stats-grid,
.death-section-title {
    width: 100%;
    min-width: 0;
}

/* 自定义滚动条样式 */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(100, 100, 200, 0.7) 0%, rgba(60, 60, 150, 0.9) 100%);
    border-radius: 4px;
    box-shadow: 0 0 8px rgba(100, 100, 200, 0.3);
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(255, 0, 0, 0.8) 0%, rgba(139, 0, 0, 1) 100%);
}

/* 主内容区域滚动条样式 */
.main-content::-webkit-scrollbar {
    width: 6px;
}

.main-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(100, 100, 200, 0.7) 0%, rgba(60, 60, 150, 0.9) 100%);
    border-radius: 4px;
    box-shadow: 0 0 8px rgba(100, 100, 200, 0.3);
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(255, 0, 0, 0.8) 0%, rgba(139, 0, 0, 1) 100%);
}

/* 数据表格滚动条样式 */
.death-data-table::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.death-data-table::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 4px;
}

.death-data-table::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(139, 0, 0, 0.8) 0%, rgba(100, 0, 0, 1) 100%);
    border-radius: 4px;
    box-shadow: 0 0 6px rgba(139, 0, 0, 0.5);
}

.death-data-table::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(255, 0, 0, 0.9) 0%, rgba(139, 0, 0, 1) 100%);
}

.death-data-table::-webkit-scrollbar-corner {
    background: rgba(0, 0, 0, 0.4);
}

/* 确保内容区域的滚动性能 */
.sidebar,
.main-content {
    scroll-behavior: smooth;
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: rgba(100, 100, 200, 0.7) rgba(0, 0, 0, 0.3); /* Firefox */
}

/* 防止内容溢出 */
.death-note-panel {
    max-width: 100%;
    overflow: visible; /* 允许面板内容正常显示 */
    word-wrap: break-word;
}

.death-data-table {
    max-width: 100%;
    overflow-x: auto !important; /* 强制水平滚动 */
    overflow-y: auto !important; /* 强制垂直滚动 */
    word-wrap: break-word;
    max-height: 400px; /* 限制表格高度 */
}

.death-stats-grid {
    max-width: 100%;
    overflow-x: auto;
    word-wrap: break-word;
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 25px;
    min-width: 0;
    height: 100%; /* 占满父容器高度 */
    width: 100%;
    box-sizing: border-box;
    overflow-y: auto !important; /* 强制垂直滚动 */
    overflow-x: auto !important; /* 允许水平滚动 */
    padding-right: 5px; /* 为滚动条留出空间 */
}

/* 死亡笔记面板基础样式 - 优雅阴森风格 */
.death-note-panel {
    background:
        radial-gradient(circle at 15% 85%, rgba(40, 0, 0, 0.4) 0%, transparent 40%),
        radial-gradient(circle at 85% 15%, rgba(0, 0, 40, 0.3) 0%, transparent 40%),
        linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 20%, #0f0f0f 60%, #050505 100%);
    border: 1px solid #2a2a2a;
    border-radius: 0;
    padding: 28px;
    margin-bottom: 25px;
    box-shadow:
        inset 0 0 60px rgba(0, 0, 0, 0.9),
        inset 0 0 25px rgba(60, 0, 0, 0.2),
        0 0 40px rgba(0, 0, 0, 0.8),
        0 2px 8px rgba(0, 0, 0, 0.6);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    border-left: 4px solid #333;
    border-top: 1px solid #444;
}

/* 移除面板扫描线动画 */

.death-note-panel:hover {
    border-left-color: #555;
    border-color: #3a3a3a;
    box-shadow:
        inset 0 0 80px rgba(0, 0, 0, 0.95),
        inset 0 0 30px rgba(80, 0, 0, 0.3),
        0 0 50px rgba(0, 0, 0, 0.9),
        0 4px 15px rgba(0, 0, 0, 0.7);
    transform: translateY(-1px);
}

/* 死神之眼面板 - 阴森蓝紫色调 */
.death-eye-panel {
    text-align: center;
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
    border: 2px solid #5a5aaa;
}

.death-eye-container {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    position: relative;
}

.death-eye-container::before {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255, 0, 0, 0.2) 0%, rgba(139, 0, 0, 0.1) 50%, transparent 70%);
    border-radius: 50%;
    animation: eyeAura 8s ease-in-out infinite;
}

.death-eye {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background:
        radial-gradient(ellipse at 25% 25%, rgba(255, 255, 255, 0.3) 0%, transparent 30%),
        radial-gradient(circle, #ff0000 0%, #8b0000 40%, #440000 70%, #000000 100%);
    position: relative;
    border: 3px solid #6666aa;
    animation: eyePulse 4s ease-in-out infinite;
    box-shadow:
        0 0 40px rgba(255, 0, 0, 0.8),
        0 0 80px rgba(255, 0, 0, 0.4),
        inset 0 0 20px rgba(0, 0, 0, 0.6);
    z-index: 1;
}

.death-pupil {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    background:
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle, #000000 0%, #111111 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pupilMove 12s ease-in-out infinite;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.9);
}

.death-iris {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50px;
    height: 50px;
    background:
        radial-gradient(ellipse at 20% 20%, rgba(255, 255, 255, 0.4) 0%, transparent 40%),
        radial-gradient(circle, #ff6666 0%, #ff4444 30%, #cc0000 60%, #880000 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: irisGlow 6s ease-in-out infinite;
    box-shadow: inset 0 0 15px rgba(255, 0, 0, 0.6);
}

@keyframes eyeAura {
    0%, 100% {
        opacity: 0.3;
        transform: translateX(-50%) scale(1);
    }
    50% {
        opacity: 0.7;
        transform: translateX(-50%) scale(1.1);
    }
}

@keyframes eyePulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 0 40px rgba(255, 0, 0, 0.8),
            0 0 80px rgba(255, 0, 0, 0.4),
            inset 0 0 20px rgba(0, 0, 0, 0.6);
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 0 60px rgba(255, 0, 0, 1),
            0 0 120px rgba(255, 0, 0, 0.6),
            inset 0 0 30px rgba(0, 0, 0, 0.8);
    }
}

@keyframes pupilMove {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    20% { transform: translate(-45%, -55%) scale(0.9); }
    40% { transform: translate(-55%, -45%) scale(1.1); }
    60% { transform: translate(-45%, -45%) scale(0.95); }
    80% { transform: translate(-55%, -55%) scale(1.05); }
}

@keyframes irisGlow {
    0%, 100% {
        box-shadow: inset 0 0 15px rgba(255, 0, 0, 0.6);
        filter: brightness(1);
    }
    50% {
        box-shadow: inset 0 0 25px rgba(255, 0, 0, 0.9);
        filter: brightness(1.2);
    }
    100% { box-shadow: 0 0 20px rgba(255, 0, 0, 0.8); }
}

/* 死亡标题 */
.death-title {
    font-family: 'Orbitron', monospace;
    font-size: 28px;
    color: #9999ff;
    text-align: center;
    text-shadow:
        0 0 15px rgba(255, 0, 0, 1),
        0 0 30px rgba(255, 0, 0, 0.8),
        0 0 45px rgba(255, 0, 0, 0.6),
        0 0 60px rgba(255, 0, 0, 0.4),
        3px 3px 6px rgba(0, 0, 0, 0.9);
    margin-bottom: 10px;
    animation: titleFlicker 5s ease-in-out infinite;
    letter-spacing: 3px;
    position: relative;
}

.death-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff0000, transparent);
    animation: underlineGlow 3s ease-in-out infinite;
}

.death-subtitle {
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    color: #ccc;
    text-align: center;
    letter-spacing: 4px;
    text-transform: uppercase;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    animation: subtitlePulse 6s ease-in-out infinite;
}

@keyframes titleFlicker {
    0%, 100% {
        opacity: 1;
        text-shadow:
            0 0 15px rgba(255, 0, 0, 1),
            0 0 30px rgba(255, 0, 0, 0.8),
            0 0 45px rgba(255, 0, 0, 0.6),
            0 0 60px rgba(255, 0, 0, 0.4),
            3px 3px 6px rgba(0, 0, 0, 0.9);
    }
    25% {
        opacity: 0.9;
        text-shadow:
            0 0 20px rgba(255, 0, 0, 1),
            0 0 40px rgba(255, 0, 0, 0.9),
            0 0 60px rgba(255, 0, 0, 0.7),
            0 0 80px rgba(255, 0, 0, 0.5),
            3px 3px 6px rgba(0, 0, 0, 0.9);
    }
    50% {
        opacity: 0.85;
        text-shadow:
            0 0 10px rgba(255, 0, 0, 0.8),
            0 0 20px rgba(255, 0, 0, 0.6),
            0 0 30px rgba(255, 0, 0, 0.4),
            3px 3px 6px rgba(0, 0, 0, 0.9);
    }
    75% {
        opacity: 0.95;
        text-shadow:
            0 0 25px rgba(255, 0, 0, 1),
            0 0 50px rgba(255, 0, 0, 0.8),
            0 0 75px rgba(255, 0, 0, 0.6),
            3px 3px 6px rgba(0, 0, 0, 0.9);
    }
}

@keyframes underlineGlow {
    0%, 100% {
        opacity: 0.6;
        box-shadow: 0 0 10px rgba(255, 0, 0, 0.6);
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 20px rgba(255, 0, 0, 1);
    }
}

@keyframes subtitlePulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

@keyframes errorBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* ========== 动物园怪谈风格诡异元素 ========== */

/* 监视眼睛 */
.zoo-watching-eyes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh; /* 固定为视口高度 */
    pointer-events: none;
    z-index: 999;
}

.watching-eye {
    position: absolute;
    font-size: 20px;
    color: rgba(255, 0, 0, 0.7);
    animation: eyeWatch 8s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255, 0, 0, 0.8));
    cursor: none;
}

.watching-eye-1 {
    top: 15%;
    right: 10%;
    animation-delay: 0s;
}

.watching-eye-2 {
    top: 60%;
    left: 5%;
    animation-delay: 3s;
}

.watching-eye-3 {
    bottom: 20%;
    right: 15%;
    animation-delay: 6s;
}

/* 诡异警告文字 */
.zoo-warning-text {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
    text-align: center;
    pointer-events: none;
    opacity: 0;
    animation: warningFlash 20s ease-in-out infinite;
}

.warning-line {
    font-family: 'Orbitron', monospace;
    font-size: 14px;
    color: #8888ff;
    text-shadow:
        0 0 10px rgba(136, 136, 255, 0.8),
        0 0 20px rgba(136, 136, 255, 0.6);
    margin: 5px 0;
    letter-spacing: 2px;
    animation: textGlitch 0.5s ease-in-out infinite;
}

/* 诡异提示样式 */
.zoo-hint {
    text-align: center;
    margin-top: 5px;
    animation: hintFlicker 4s ease-in-out infinite;
}

.zoo-warning-small {
    text-align: center;
    margin: 8px 0;
    animation: warningBlink 3s ease-in-out infinite;
}

/* 阴森表格效果 */
.death-table {
    position: relative;
}

.death-table::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 40px,
            rgba(255, 0, 0, 0.02) 41px,
            rgba(255, 0, 0, 0.02) 42px
        );
    pointer-events: none;
    z-index: 1;
}

/* 诡异鼠标悬停效果 */
.death-table tbody tr:hover {
    background: rgba(139, 0, 0, 0.1) !important;
    box-shadow:
        0 0 15px rgba(255, 0, 0, 0.2),
        inset 0 0 10px rgba(139, 0, 0, 0.1);
    transform: scale(1.001);
    transition: all 0.3s ease;
}

/* 诡异滚动条 */
::-webkit-scrollbar {
    width: 8px;
    background: #0a0a0a;
}

::-webkit-scrollbar-track {
    background: linear-gradient(180deg, #1a0a0a, #0a0a0a, #1a0a0a);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #8b0000, #ff0000, #8b0000);
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #ff0000, #ff4444, #ff0000);
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.8);
}

/* 随机诡异文字 */
.zoo-random-text {
    position: fixed;
    top: 20%;
    right: 10%;
    z-index: 998;
    pointer-events: none;
    opacity: 0;
    animation: randomAppear 25s ease-in-out infinite;
}

.random-message {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: rgba(255, 0, 0, 0.8);
    text-shadow:
        0 0 8px rgba(255, 0, 0, 0.6),
        0 0 16px rgba(255, 0, 0, 0.4);
    letter-spacing: 1px;
    animation: messageGlitch 0.3s ease-in-out infinite;
    transform: rotate(-5deg);
}

/* 阴森红光闪烁效果 */
.sinister-flash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 0, 0, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 999;
    opacity: 0;
    animation: sinisterFlash 45s ease-in-out infinite;
}

/* 诡异数据行效果 */
.death-table tbody tr.anomaly {
    background: rgba(139, 0, 0, 0.05) !important;
    animation: anomalyPulse 3s ease-in-out infinite;
}

.death-table tbody tr.anomaly td {
    color: #ff6666 !important;
    text-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
}

/* 状态指示器 */
.death-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 12px 20px;
    background:
        linear-gradient(145deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 0, 0, 0.6) 100%);
    border-radius: 25px;
    border: 2px solid #333;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.death-status:hover {
    border-color: #8888ff;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.7),
        0 0 15px rgba(136, 136, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.death-status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #8888ff;
    animation: statusPulse 2s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(136, 136, 255, 0.6);
}

.status-text {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #8888ff;
    font-weight: 700;
    letter-spacing: 1px;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

/* 章节标题 - 阴森蓝紫色调 */
.death-section-title {
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    color: #8888ff;
    text-align: center;
    margin-bottom: 16px;
    animation: horrorGlow 4s ease-in-out infinite;
    letter-spacing: 3px;
    position: relative;
    padding: 10px 0;
    text-shadow:
        0 0 10px rgba(255, 0, 0, 0.8),
        0 0 20px rgba(255, 0, 0, 0.5);
    animation: sectionPulse 6s ease-in-out infinite;
}

/* 新增动画效果 */
@keyframes sectionPulse {
    0%, 100% {
        opacity: 0.9;
        text-shadow:
            0 0 10px rgba(136, 136, 255, 0.8),
            0 0 20px rgba(136, 136, 255, 0.5);
    }
    50% {
        opacity: 1;
        text-shadow:
            0 0 15px rgba(136, 136, 255, 1),
            0 0 30px rgba(136, 136, 255, 0.8),
            0 0 45px rgba(136, 136, 255, 0.6);
    }
}

@keyframes titleFlicker {
    0%, 90%, 100% { opacity: 1; }
    95% { opacity: 0.3; }
    97% { opacity: 1; }
    98% { opacity: 0.5; }
    99% { opacity: 1; }
}

/* 新增恐怖动画效果 - 阴森蓝紫色调 */
@keyframes horrorGlow {
    0%, 100% {
        text-shadow:
            0 0 5px rgba(136, 136, 255, 0.5),
            0 0 10px rgba(136, 136, 255, 0.3);
    }
    50% {
        text-shadow:
            0 0 10px rgba(136, 136, 255, 0.8),
            0 0 20px rgba(136, 136, 255, 0.6),
            0 0 30px rgba(136, 136, 255, 0.4);
    }
}

@keyframes shadowPulse {
    0%, 100% {
        box-shadow:
            0 0 10px rgba(100, 100, 200, 0.2),
            inset 0 0 10px rgba(100, 100, 200, 0.1);
    }
    50% {
        box-shadow:
            0 0 20px rgba(100, 100, 200, 0.4),
            inset 0 0 20px rgba(100, 100, 200, 0.2);
    }
}

.death-section-title::before,
.death-section-title::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30px;
    height: 1px;
    background: linear-gradient(90deg, transparent, #8888ff, transparent);
}

.death-section-title::before {
    left: 0;
}

.death-section-title::after {
    right: 0;
}

.death-skull, .death-flame, .death-control, .death-monitor, .death-pulse-icon {
    animation: iconFloat 3s ease-in-out infinite;
}

/* 优化图标浮动动画 */
@keyframes iconFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

@keyframes emotionPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

@keyframes backgroundShift {
    0%, 100% {
        background-position: 0% 0%, 20% 20%, 80% 80%, 60% 60%, 0% 0%;
        filter: hue-rotate(0deg);
    }
    25% {
        background-position: 25% 25%, 75% 25%, 25% 75%, 75% 75%, 25% 25%;
        filter: hue-rotate(5deg);
    }
    50% {
        background-position: 50% 50%, 30% 70%, 70% 30%, 40% 60%, 50% 50%;
        filter: hue-rotate(10deg);
    }
    75% {
        background-position: 75% 25%, 25% 75%, 75% 25%, 25% 75%, 75% 25%;
        filter: hue-rotate(5deg);
    }
}

@keyframes bloodPulse {
    0%, 100% {
        box-shadow: inset 0 0 50px rgba(139, 0, 0, 0.1);
    }
    50% {
        box-shadow: inset 0 0 80px rgba(139, 0, 0, 0.2);
    }
}

/* 恐怖统计卡片完整样式 - 阴森风格 */
.death-stat-card {
    background:
        radial-gradient(circle at 25% 75%, rgba(30, 0, 0, 0.3) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #0f0f0f 70%, #050505 100%);
    border: 1px solid #2a2a2a;
    border-radius: 0;
    padding: 18px 15px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow:
        inset 0 0 40px rgba(0, 0, 0, 0.9),
        inset 0 0 15px rgba(50, 0, 0, 0.2),
        0 0 25px rgba(0, 0, 0, 0.8);
    min-height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-left: 3px solid #333;
    border-top: 1px solid #444;
}

/* 移除统计卡片动画效果 */

.death-stat-card:hover {
    transform: translateY(-2px);
    border-left-color: #444;
    border-color: #3a3a3a;
    box-shadow:
        inset 0 0 50px rgba(0, 0, 0, 0.95),
        inset 0 0 20px rgba(60, 0, 0, 0.3),
        0 0 30px rgba(0, 0, 0, 0.9);
}

.death-stat-icon {
    font-size: 28px;
    margin-bottom: 10px;
    color: #666;
    position: relative;
    z-index: 2;
}

.death-stat-number {
    font-family: 'Orbitron', monospace;
    font-size: 20px;
    font-weight: 600;
    color: #aaa;
    margin-bottom: 6px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    position: relative;
    z-index: 2;
    line-height: 1;
}

.death-stat-label {
    font-family: 'Orbitron', monospace;
    font-size: 10px;
    color: #ccc;
    letter-spacing: 1px;
    text-transform: uppercase;
    line-height: 1;
    font-weight: 700;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
}

.death-stat-pulse {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff0000, transparent);
    animation: scanLine 3s linear infinite;
}

@keyframes cardPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.02);
    }
}

@keyframes numberGlow {
    0%, 100% {
        text-shadow:
            0 0 15px rgba(255, 0, 0, 0.8),
            0 0 30px rgba(255, 0, 0, 0.6),
            0 0 45px rgba(255, 0, 0, 0.4);
        transform: scale(1);
    }
    50% {
        text-shadow:
            0 0 25px rgba(255, 0, 0, 1),
            0 0 50px rgba(255, 0, 0, 0.8),
            0 0 75px rgba(255, 0, 0, 0.6),
            0 0 100px rgba(255, 0, 0, 0.4);
        transform: scale(1.05);
    }
}

/* 系统状态面板 */
.death-vitals-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.death-vital-card {
    background: linear-gradient(145deg, #1a0d0d 0%, #0d0606 100%);
    border: 1px solid #8b0000;
    border-radius: 8px;
    padding: 15px;
    position: relative;
}

.death-vital-icon {
    font-size: 20px;
    float: left;
    margin-right: 10px;
    animation: iconFloat 3s ease-in-out infinite;
}

.death-vital-value {
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    font-weight: 700;
    color: #ff0000;
    margin-bottom: 5px;
}

.death-vital-label {
    font-family: 'Orbitron', monospace;
    font-size: 10px;
    color: #999;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 10px;
}

.death-vital-bar {
    width: 100%;
    height: 6px;
    background: linear-gradient(90deg, #333 0%, #666 100%);
    border-radius: 3px;
    overflow: hidden;
    clear: both;
}

.death-vital-fill {
    height: 100%;
    background: linear-gradient(90deg, #8b0000 0%, #ff0000 50%, #8b0000 100%);
    width: 0%;
    transition: width 0.5s ease;
    animation: vitalPulse 2s ease-in-out infinite;
}

@keyframes vitalPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* 信息面板 */
.death-info-panel {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #333;
}

.death-info-line {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-family: 'Orbitron', monospace;
    font-size: 11px;
}

.death-info-line:last-child {
    margin-bottom: 0;
}

.death-info-icon {
    font-size: 14px;
    animation: iconFloat 3s ease-in-out infinite;
}

.death-info-text {
    color: #ccc;
    letter-spacing: 0.5px;
}

.death-info-text span {
    color: #ff0000;
    font-weight: 700;
}

/* 控制按钮 - 优化版 */
.death-control-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    max-width: 100%;
}

.death-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: 2px solid;
    border-radius: 10px;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        linear-gradient(145deg, #2d1a1a 0%, #1a0d0d 50%, #0d0606 100%);
    color: #ff0000;
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    font-weight: 700;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.5),
        0 0 10px rgba(139, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.death-button-primary {
    border-color: #00ff00;
    color: #00ff00;
}

.death-button-secondary {
    border-color: #0099ff;
    color: #0099ff;
}

.death-button-danger {
    border-color: #ff0000;
    color: #ff0000;
}

.death-button-special {
    border-color: #ff00ff;
    color: #ff00ff;
}

.death-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 0, 0, 0.1) 50%, transparent 70%);
    animation: buttonPulse 4s ease-in-out infinite;
    pointer-events: none;
}

.death-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.7),
        0 0 20px rgba(255, 0, 0, 0.4),
        0 0 40px rgba(255, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.death-button-primary:hover {
    box-shadow: 0 5px 15px rgba(0, 255, 0, 0.3);
}

.death-button-secondary:hover {
    box-shadow: 0 5px 15px rgba(0, 153, 255, 0.3);
}

.death-button-special:hover {
    box-shadow: 0 5px 15px rgba(255, 0, 255, 0.3);
}

.death-button-icon {
    font-size: 16px;
    /* animation: iconFloat 4s ease-in-out infinite; 临时禁用动画 */
    filter: drop-shadow(0 0 6px rgba(255, 0, 0, 0.6));
    position: relative;
    z-index: 2;
    pointer-events: none; /* 确保图标不阻塞点击 */
}

.death-button-text {
    font-size: 11px;
    font-weight: 700;
    position: relative;
    z-index: 2;
    pointer-events: none; /* 确保文本不阻塞点击 */
}

.death-button-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 0, 0, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.death-button:hover .death-button-glow {
    opacity: 1;
}

@keyframes buttonPulse {
    0%, 100% {
        opacity: 0.2;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.01);
    }
}

/* ========== 动物园怪谈动画效果 ========== */

/* 监视眼睛动画 */
@keyframes eyeWatch {
    0%, 100% {
        opacity: 0;
        transform: scale(0.8);
    }
    10%, 90% {
        opacity: 0.7;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
        color: rgba(255, 0, 0, 1);
        filter: drop-shadow(0 0 15px rgba(255, 0, 0, 1));
    }
}

/* 警告文字闪现 */
@keyframes warningFlash {
    0%, 85%, 100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    87%, 98% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    90%, 95% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

/* 文字故障效果 */
@keyframes textGlitch {
    0%, 90%, 100% {
        transform: translateX(0);
    }
    20% {
        transform: translateX(-2px);
    }
    40% {
        transform: translateX(2px);
    }
    60% {
        transform: translateX(-1px);
    }
    80% {
        transform: translateX(1px);
    }
}

/* 雾气漂移 */
@keyframes mistDrift {
    0% {
        opacity: 0.3;
        transform: translateX(-10%);
    }
    50% {
        opacity: 0.6;
        transform: translateX(10%);
    }
    100% {
        opacity: 0.3;
        transform: translateX(-10%);
    }
}

/* 监视扫描 */
@keyframes watchingScan {
    0% {
        left: -100%;
        opacity: 0;
    }
    10%, 90% {
        opacity: 1;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

/* 提示闪烁动画 */
@keyframes hintFlicker {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* 警告闪烁动画 */
@keyframes warningBlink {
    0%, 70%, 100% { opacity: 0.8; }
    75%, 85% { opacity: 1; }
    80% { opacity: 0.3; }
}

/* 随机文字出现动画 */
@keyframes randomAppear {
    0%, 85%, 100% {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    87%, 98% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    90%, 95% {
        opacity: 0.5;
        transform: translateY(-5px) scale(1.1);
    }
}

/* 消息故障动画 */
@keyframes messageGlitch {
    0%, 95%, 100% {
        transform: rotate(-5deg) translateX(0);
    }
    10% {
        transform: rotate(-4deg) translateX(-1px);
    }
    20% {
        transform: rotate(-6deg) translateX(1px);
    }
    30% {
        transform: rotate(-5deg) translateX(0);
    }
}

/* 阴森闪光动画 */
@keyframes sinisterFlash {
    0%, 95%, 100% {
        opacity: 0;
    }
    96%, 99% {
        opacity: 0.3;
    }
    97%, 98% {
        opacity: 0;
    }
}

/* 异常数据脉冲 */
@keyframes anomalyPulse {
    0%, 100% {
        background: rgba(139, 0, 0, 0.05);
        box-shadow: none;
    }
    50% {
        background: rgba(139, 0, 0, 0.15);
        box-shadow: 0 0 10px rgba(255, 0, 0, 0.2);
    }
}

/* 监控面板 */
.death-monitor-panel {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #333;
}

.death-monitor-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.death-monitor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333;
    font-family: 'Orbitron', monospace;
    font-size: 11px;
}

.death-monitor-item:last-child {
    border-bottom: none;
}

.death-monitor-label {
    color: #999;
    letter-spacing: 0.5px;
}

.death-monitor-value {
    color: #ff0000;
    font-weight: 700;
}

.death-monitor-status {
    display: flex;
    align-items: center;
    gap: 5px;
}

.death-monitor-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff0000;
    animation: statusPulse 2s ease-in-out infinite;
    box-shadow: 0 0 8px rgba(255, 0, 0, 0.6);
}

/* 表格按钮样式 - 阴森蓝紫色调 */
.death-table-btn {
    padding: 8px 15px;
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid #5a5aaa;
    border-radius: 6px;
    color: #8888ff;
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 2px;
}

.death-table-btn:hover {
    background: linear-gradient(145deg, #3d2a2a 0%, #2a1d1d 100%);
    border-color: #8888ff;
    box-shadow: 0 0 10px rgba(136, 136, 255, 0.3);
    transform: translateY(-1px);
}

.death-table-btn.active {
    background: linear-gradient(145deg, #4a2d2d 0%, #3d1a1a 100%);
    border-color: #8888ff;
    box-shadow: 0 0 15px rgba(136, 136, 255, 0.4);
    color: #fff;
}

/* 操作按钮样式 - 阴森风格 */
.death-action-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    border: 1px solid #333;
    border-radius: 0;
    color: #aaa;
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 3px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8);
    display: inline-flex;
    align-items: center;
    gap: 6px;
    position: relative;
    overflow: hidden;
    z-index: 1500; /* 确保按钮在最上层 */
}

.death-action-btn:hover {
    background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
    border-color: #444;
    color: #ccc;
    /* transform: translateY(-1px); 临时禁用transform */
}

.death-edit-btn {
    border-color: #00ff00;
    color: #00ff00;
}

.death-edit-btn:hover {
    box-shadow: 0 0 8px rgba(0, 255, 0, 0.3);
}

.death-delete-btn {
    border-color: #ff4444;
    color: #ff4444;
}

.death-delete-btn:hover {
    box-shadow: 0 0 8px rgba(255, 68, 68, 0.3);
}

/* 分页样式 */
.death-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid #333;
}

.death-page-btn {
    padding: 8px 12px;
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid #5a5aaa;
    border-radius: 6px;
    color: #8888ff;
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 35px;
    text-align: center;
}

.death-page-btn:hover {
    background: linear-gradient(145deg, #3d2a2a 0%, #2a1d1d 100%);
    border-color: #8888ff;
    box-shadow: 0 0 10px rgba(136, 136, 255, 0.3);
}

.death-page-btn.active {
    background: linear-gradient(145deg, #4a2d2d 0%, #3d1a1a 100%);
    border-color: #8888ff;
    box-shadow: 0 0 15px rgba(136, 136, 255, 0.4);
    color: #fff;
}

.death-page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.death-page-info {
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    color: #999;
    letter-spacing: 0.5px;
}

/* 恐怖发光效果 */
.horror-glow {
    position: relative;
}

.horror-glow::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 0, 0, 0.1) 50%, transparent 70%);
    animation: horrorGlow 4s ease-in-out infinite;
    pointer-events: none;
    border-radius: inherit;
}

@keyframes horrorGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

/* 数据显示样式 */
.death-data-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.death-data-cell.expandable {
    cursor: pointer;
    position: relative;
}

.death-data-cell.expandable:hover {
    background: rgba(255, 0, 0, 0.1);
    border-radius: 4px;
}

.death-json-data {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: #00ff00;
    background: rgba(0, 0, 0, 0.5);
    padding: 4px;
    border-radius: 3px;
    border: 1px solid #333;
}

.death-timestamp {
    color: #0099ff;
    font-size: 10px;
}

.death-emotion-value {
    color: #ff00ff;
    font-weight: 700;
}

.death-affinity-value {
    color: #ffff00;
    font-weight: 700;
}

/* 分页容器 */
.death-pagination-container {
    margin-top: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #333;
}

.death-pagination-info {
    text-align: center;
    margin-bottom: 15px;
}

.death-pagination-text {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #999;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.death-pagination-controls {
    display: flex;
    justify-content: center;
}

/* 实时监控面板 */
.death-monitor-main {
    background: linear-gradient(145deg, #0d0d0d 0%, #1a1a1a 50%, #0d0d0d 100%);
    border: 2px solid #8b0000;
}

.death-realtime-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.death-realtime-card {
    background: linear-gradient(145deg, #1a0d0d 0%, #0d0606 100%);
    border: 1px solid #8b0000;
    border-radius: 10px;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.death-realtime-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333;
}

.death-realtime-icon {
    font-size: 18px;
    animation: iconFloat 3s ease-in-out infinite;
}

.death-realtime-title {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #ff0000;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
}

/* 实时统计数据 */
.death-realtime-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.death-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    border: 1px solid #333;
}

.death-stat-label {
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    color: #aaa;
    text-transform: uppercase;
}

.death-stat-value {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #ff6666;
    font-weight: 700;
}

/* AI情感显示 */
.death-emotion-display {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.death-emotion-current {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid #333;
}

.death-emotion-icon {
    font-size: 24px;
    animation: emotionPulse 2s ease-in-out infinite;
}

.death-emotion-text {
    font-family: 'Orbitron', monospace;
    font-size: 14px;
    color: #ff6666;
    font-weight: 700;
    text-transform: uppercase;
}

.death-emotion-value {
    text-align: center;
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    color: #aaa;
    padding: 8px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

/* 系统状态 */
.death-system-status {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.death-status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    border: 1px solid #333;
}

.death-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666;
    animation: statusPulse 2s ease-in-out infinite;
}

.death-status-dot.online {
    background: #00ff00;
    box-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
}

.death-status-dot.offline {
    background: #ff0000;
    box-shadow: 0 0 8px rgba(255, 0, 0, 0.5);
}

.death-status-dot.warning {
    background: #ffaa00;
    box-shadow: 0 0 8px rgba(255, 170, 0, 0.5);
}

.death-status-text {
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    color: #aaa;
    text-transform: uppercase;
}

.death-chart-canvas {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        rgba(255, 0, 0, 0.1) 0%,
        transparent 25%,
        rgba(255, 0, 0, 0.05) 50%,
        transparent 75%,
        rgba(255, 0, 0, 0.1) 100%);
    animation: chartPulse 4s ease-in-out infinite;
}

@keyframes chartPulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* 情感温度计 */
.death-emotion-meter {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid #333;
    padding: 15px;
    margin-top: 10px;
}

.death-emotion-scale {
    height: 16px;
    background: linear-gradient(90deg,
        #ff4444 0%,
        #ffaa44 25%,
        #44aa44 50%,
        #44aaff 75%,
        #4444ff 100%);
    border-radius: 8px;
    position: relative;
    margin-bottom: 12px;
    border: 1px solid #333;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.death-emotion-marker {
    position: absolute;
    top: -4px;
    width: 24px;
    height: 24px;
    background: radial-gradient(circle, #fff 0%, #ff6666 100%);
    border-radius: 50%;
    border: 2px solid #333;
    transform: translateX(-50%);
    left: 50%;
    animation: markerPulse 2s ease-in-out infinite;
    box-shadow: 0 0 12px rgba(255, 102, 102, 0.8);
    transition: left 0.3s ease;
}

@keyframes markerPulse {
    0%, 100% { transform: translateX(-50%) scale(1); }
    50% { transform: translateX(-50%) scale(1.1); }
}

.death-emotion-labels {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.death-emotion-label {
    font-family: 'Orbitron', monospace;
    font-size: 10px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.death-cold {
    color: #0099ff;
}

.death-neutral {
    color: #ffff00;
}

.death-hot {
    color: #ff0000;
}

/* 威胁等级样式 */
.death-threat-level {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.death-threat-low {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
    border: 1px solid #00ff00;
}

.death-threat-medium {
    background: rgba(255, 255, 0, 0.2);
    color: #ffff00;
    border: 1px solid #ffff00;
}

.death-threat-high {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
    border: 1px solid #ff0000;
}

.death-threat-critical {
    background: rgba(255, 0, 255, 0.2);
    color: #ff00ff;
    border: 1px solid #ff00ff;
    animation: criticalBlink 1s ease-in-out infinite;
}

@keyframes criticalBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 监控脉冲效果 */
.death-monitor-pulse {
    width: 8px;
    height: 8px;
    background: #ff0000;
    border-radius: 50%;
    animation: statusPulse 2s ease-in-out infinite;
    box-shadow: 0 0 8px rgba(255, 0, 0, 0.6);
}

/* 添加按钮样式 - 阴森风格 */
.death-add-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    border: 1px solid #555;
    border-radius: 0;
    color: #aaa;
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 3px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8);
    display: inline-flex;
    align-items: center;
    gap: 6px;
    position: relative;
    overflow: hidden;
    z-index: 1500; /* 确保按钮在最上层 */
}

.death-add-btn:hover {
    border-color: #666;
    color: #ccc;
    background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
    /* transform: translateY(-1px); 临时禁用transform */
}

.death-filter-btn {
    border-color: #0099ff;
    color: #0099ff;
}

.death-filter-btn:hover {
    box-shadow: 0 0 15px rgba(0, 153, 255, 0.4);
}

/* 统计网格布局 - 优化版 */
.death-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 16px;
    max-width: 100%;
    overflow: hidden;
}

/* 主内容区域恐怖样式 */
.death-main-panel {
    background: linear-gradient(145deg, #0d0d0d 0%, #1a1a1a 50%, #0d0d0d 100%);
    border: 2px solid #8b0000;
    position: relative;
}

.death-main-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.death-main-title {
    font-family: 'Orbitron', monospace;
    font-size: 32px;
    color: #ff0000;
    text-shadow:
        0 0 20px rgba(255, 0, 0, 0.9),
        0 0 40px rgba(255, 0, 0, 0.7),
        0 0 60px rgba(255, 0, 0, 0.5),
        0 0 80px rgba(255, 0, 0, 0.3);
    margin-bottom: 15px;
    animation: titleFlicker 4s ease-in-out infinite;
    letter-spacing: 4px;
    text-align: center;
}

.death-skull-large {
    font-size: 36px;
    animation: skullRotate 6s ease-in-out infinite;
    display: inline-block;
    margin: 0 15px;
}

@keyframes skullRotate {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

.death-scan-line {
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff0000, transparent);
    animation: scanLine 2s linear infinite;
}

/* 表格选择器恐怖样式 */
.death-table-selector {
    margin-bottom: 25px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #333;
}

.death-selector-title {
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    color: #ff0000;
    text-align: center;
    margin-bottom: 15px;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.death-selector-icon {
    animation: iconFloat 3s ease-in-out infinite;
    margin: 0 10px;
}

.death-table-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

/* 搜索控制区域 */
.death-control-section {
    margin-bottom: 25px;
}

.death-search-container {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
}

.death-search-wrapper {
    flex: 1;
    position: relative;
}

.death-search-input {
    width: 100%;
    padding: 15px 20px 15px 50px;
    background: linear-gradient(145deg, #1a1a1a 0%, #0d0d0d 100%);
    border: 2px solid #333;
    border-radius: 8px;
    color: #e0e0e0;
    font-family: 'Orbitron', monospace;
    font-size: 14px;
    transition: all 0.3s ease;
    outline: none;
}

.death-search-input:focus {
    border-color: #ff0000;
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.3);
    background: linear-gradient(145deg, #2d1a1a 0%, #1a0d0d 100%);
}

.death-search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff0000;
    font-size: 18px;
    animation: iconFloat 3s ease-in-out infinite;
}

.death-search-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 0, 0, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.death-search-input:focus + .death-search-glow {
    opacity: 1;
}

.death-action-buttons {
    display: flex;
    gap: 10px;
}

/* 移除重复的操作按钮样式 */

.death-btn-icon {
    font-size: 12px;
    color: inherit;
    pointer-events: none;
}

.death-btn-text {
    pointer-events: none;
}

.death-btn-ripple {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 0, 0, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.death-action-btn:active .death-btn-ripple {
    opacity: 1;
}

/* 过滤器面板 */
.death-filter-panel {
    background: linear-gradient(145deg, #1a0d0d 0%, #0d0606 100%);
    border: 2px solid #8b0000;
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
    animation: filterSlideDown 0.3s ease-out;
}

@keyframes filterSlideDown {
    0% { opacity: 0; transform: translateY(-20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.death-filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.death-filter-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.death-filter-label {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #ff0000;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.death-filter-input, .death-filter-select {
    padding: 10px 15px;
    background: linear-gradient(145deg, #2d1a1a 0%, #1a0d0d 100%);
    border: 1px solid #8b0000;
    border-radius: 6px;
    color: #e0e0e0;
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    outline: none;
    transition: all 0.3s ease;
}

.death-filter-input:focus, .death-filter-select:focus {
    border-color: #ff0000;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

.death-filter-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.death-filter-separator {
    color: #ff0000;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 12px;
}

.death-filter-range {
    flex: 1;
    appearance: none;
    height: 6px;
    background: linear-gradient(90deg, #8b0000 0%, #ff0000 50%, #8b0000 100%);
    border-radius: 3px;
    outline: none;
}

.death-filter-range::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #ff0000 0%, #8b0000 100%);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
}

.death-filter-value {
    color: #ff0000;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 14px;
    min-width: 40px;
    text-align: center;
}

.death-filter-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.death-filter-btn {
    padding: 10px 20px;
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid #5a5aaa;
    border-radius: 6px;
    color: #8888ff;
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.death-filter-btn:hover {
    background: linear-gradient(145deg, #3d2a2a 0%, #2a1d1d 100%);
    border-color: #ff0000;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.4);
}

.death-apply-btn {
    background: linear-gradient(145deg, #2d4a2d 0%, #1a3d1a 100%);
    border-color: #00ff00;
    color: #00ff00;
}

.death-apply-btn:hover {
    border-color: #00ff00;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.4);
}

.death-clear-btn {
    background: linear-gradient(145deg, #4a2d2d 0%, #3d1a1a 100%);
    border-color: #ff4444;
    color: #ff4444;
}

.death-clear-btn:hover {
    border-color: #ff4444;
    box-shadow: 0 0 15px rgba(255, 68, 68, 0.4);
}

/* 表格信息区域 */
.death-table-info {
    margin-bottom: 20px;
    text-align: center;
}

.death-table-status {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #333;
    position: relative;
}

.death-table-title {
    font-family: 'Orbitron', monospace;
    font-size: 18px;
    color: #ff0000;
    margin-bottom: 8px;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.death-table-meta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* 移除重复的添加按钮样式 */

/* 移除添加按钮动画 */

.death-record-count {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #999;
    letter-spacing: 1px;
}

.death-table-pulse {
    width: 8px;
    height: 8px;
    background: #ff0000;
    border-radius: 50%;
    animation: statusPulse 2s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.6);
}

/* 恐怖表格样式 */
.death-table-container {
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
    border: 2px solid #5a5aaa;
    border-radius: 16px;
    overflow: visible;
    margin-bottom: 25px;
    box-shadow:
        0 12px 35px rgba(0, 0, 0, 0.7),
        0 0 25px rgba(90, 90, 170, 0.2);
    width: 100%;
    box-sizing: border-box;
}

.death-table-wrapper {
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
    border-radius: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(20, 0, 0, 0.2) 0%, transparent 50%),
        linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
    border: 1px solid #2a2a2a;
    border-left: 3px solid #333;
    box-shadow:
        inset 0 0 40px rgba(0, 0, 0, 0.8),
        0 0 20px rgba(0, 0, 0, 0.6);
}

.death-table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
    table-layout: fixed;
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #d0d0d0;
}

.death-table-head {
    background:
        linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 50%, #050505 100%);
    border-bottom: 2px solid #333;
    box-shadow: inset 0 -5px 15px rgba(0, 0, 0, 0.8);
}

.death-table-head th {
    padding: 15px 10px;
    color: #cccccc;
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-right: 1px solid #333;
    position: relative;
    background:
        linear-gradient(180deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* 智能列宽设置 - 确保内容完整显示 */
.death-table th:nth-child(1) { width: 6%; }   /* ID */
.death-table th:nth-child(2) { width: 10%; }  /* 用户名 */
.death-table th:nth-child(3) { width: 12%; }  /* 实体/类型 */
.death-table th:nth-child(4) { width: 12%; }  /* 工具/方法 */
.death-table th:nth-child(5) { width: 8%; }   /* 令牌/数值 */
.death-table th:nth-child(6) { width: 30%; }  /* 概括/内容 */
.death-table th:nth-child(7) { width: 15%; }  /* 时间 */
.death-table th:nth-child(8) { width: 7%; }   /* 操作 */

/* 特殊数据列的宽度调整 */
.death-table th[data-column*="vector"],
.death-table th[data-column*="embedding"] { width: 10%; }
.death-table th[data-column*="history"],
.death-table th[data-column*="context"] { width: 15%; }

.death-table-head th:last-child {
    border-right: none;
}

.death-table-head th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, #ff0000, transparent);
    animation: scanLine 3s linear infinite;
}

.death-table-body tr {
    border-bottom: 1px solid #333;
    transition: all 0.3s ease;
}

.death-table-body tr:hover {
    background:
        linear-gradient(90deg, rgba(30, 30, 30, 0.9) 0%, rgba(25, 25, 25, 0.7) 100%);
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.8),
        0 1px 5px rgba(0, 0, 0, 0.5);
}

.death-table-body td {
    padding: 14px 10px;
    color: #cccccc;
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    border-right: 1px solid #333;
    vertical-align: top;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 0;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* 概括列允许换行 */
.death-table-body td:nth-child(6) {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    max-height: 100px;
    overflow-y: auto;
}

/* 表格内操作按钮 - 阴森风格 */
.action-btn {
    padding: 3px 6px;
    margin: 1px;
    border: 1px solid #333;
    border-radius: 0;
    background:
        linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
    min-width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.8);
}

.action-btn.edit {
    color: #888;
    border-color: #444;
}

.action-btn.edit:hover {
    background:
        linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
    color: #aaa;
    border-color: #555;
}

.action-btn.delete {
    color: #666;
    border-color: #444;
}

.action-btn.delete:hover {
    background:
        linear-gradient(135deg, #2a1a1a 0%, #1f0f0f 100%);
    color: #888;
    border-color: #555;
}

.death-table-body td:last-child {
    border-right: none;
}

/* 空状态样式 */
.death-empty-state {
    background: linear-gradient(145deg, #1a0d0d 0%, #0d0606 100%);
}

.death-empty-cell {
    padding: 60px 20px !important;
    text-align: center;
    border: none !important;
}

.death-empty-content {
    position: relative;
    z-index: 2;
}

.death-empty-skull {
    font-size: 64px;
    margin-bottom: 15px;
    animation: skullFloat 4s ease-in-out infinite;
}

.death-empty-eyes {
    font-size: 24px;
    margin-bottom: 20px;
    animation: eyesBlink 3s ease-in-out infinite;
}

.death-empty-title {
    font-family: 'Orbitron', monospace;
    font-size: 24px;
    color: #ff0000;
    margin-bottom: 10px;
    text-shadow: 0 0 15px rgba(255, 0, 0, 0.6);
    letter-spacing: 2px;
}

.death-empty-text {
    font-family: 'Orbitron', monospace;
    font-size: 14px;
    color: #999;
    letter-spacing: 1px;
}

.death-empty-mist {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(255, 0, 0, 0.1) 0%, transparent 70%);
    animation: mistFloat 6s ease-in-out infinite;
    pointer-events: none;
}

@keyframes skullFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes eyesBlink {
    0%, 90%, 100% { opacity: 1; }
    95% { opacity: 0.3; }
}

@keyframes mistFloat {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
}

/* 恐怖模态框 */
.death-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.death-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        rgba(0, 0, 0, 0.85) 0%,
        rgba(26, 26, 46, 0.4) 50%,
        rgba(0, 0, 0, 0.85) 100%);
    backdrop-filter: blur(3px);
}

@keyframes backdropPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.9; }
}

.death-modal-container {
    position: relative;
    z-index: 2;
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
    border: 3px solid #5a5aaa;
    border-radius: 20px;
    width: 90%;
    max-width: 700px;
    max-height: 85vh;
    overflow: visible;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.8),
        0 0 40px rgba(255, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: modalSlideIn 0.4s ease-out;
}

@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.death-modal-header {
    padding: 28px;
    border-bottom: 2px solid #5a5aaa;
    background: linear-gradient(145deg, #1e1e3a 0%, #1a1a2e 100%);
    position: relative;
    border-radius: 20px 20px 0 0;
}

.death-modal-title {
    font-family: 'Orbitron', monospace;
    font-size: 24px;
    color: #ff0000;
    text-align: center;
    margin: 0;
    text-shadow:
        0 0 10px rgba(255, 0, 0, 0.8),
        0 0 20px rgba(255, 0, 0, 0.6);
    letter-spacing: 2px;
    animation: titleFlicker 4s ease-in-out infinite;
}

.death-modal-skull {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 32px;
    animation: skullFloat 4s ease-in-out infinite;
}

.death-modal-close {
    position: absolute;
    right: 15px;
    top: 15px;
    background: none;
    border: none;
    color: #ff0000;
    font-size: 24px;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.death-modal-close:hover {
    background: rgba(255, 0, 0, 0.2);
    transform: rotate(90deg);
}

.death-modal-body {
    padding: 30px;
    max-height: 500px;
    overflow-y: auto;
    background: linear-gradient(145deg, #16213e 0%, #1a1a2e 100%);
    border-radius: 0;
}

/* 恐怖表单样式 */
.death-form {
    display: flex;
    flex-direction: column;
    gap: 22px;
    width: 100%;
    box-sizing: border-box;
}

/* 通用表单组样式 */
.form-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 18px;
}

.form-group label {
    font-family: 'Orbitron', monospace;
    font-size: 13px;
    color: #8888ff;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 6px;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 14px 16px;
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid #5a5aaa;
    border-radius: 10px;
    color: #d0d0d0;
    font-family: 'Orbitron', monospace;
    font-size: 13px;
    outline: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: #7a7acc;
    box-shadow:
        0 0 20px rgba(122, 122, 204, 0.3),
        inset 0 0 10px rgba(90, 90, 170, 0.1);
    background: linear-gradient(145deg, #1e1e3a 0%, #1a1a2e 100%);
    transform: scale(1.02);
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
    font-family: 'Orbitron', monospace;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #666;
    font-style: italic;
}

/* JSON字段特殊样式 */
.json-field {
    font-family: 'Courier New', monospace !important;
    font-size: 12px !important;
    min-height: 150px !important;
    background: linear-gradient(145deg, #0f0f23 0%, #1a1a2e 100%) !important;
    border: 2px solid #4a4a8a !important;
    color: #b0b0ff !important;
}

.json-display {
    padding: 12px;
    background: linear-gradient(145deg, #0f0f23 0%, #1a1a2e 100%);
    border: 2px solid #4a4a8a;
    border-radius: 8px;
    color: #b0b0ff;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
}

/* 向量数据显示样式 - 阴森风格 */
.vector-data {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 3px 6px;
    background:
        linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    border: 1px solid #333;
    border-radius: 0;
    font-size: 9px;
    max-width: 100%;
    overflow: hidden;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8);
}

.vector-icon {
    font-size: 10px;
    color: #666;
}

.vector-info {
    color: #888;
    font-weight: 500;
    white-space: nowrap;
    font-family: 'Courier New', monospace;
}

.vector-preview {
    color: #999;
    font-family: 'Courier New', monospace;
    font-size: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    opacity: 0.7;
}

/* JSON数据显示样式 - 阴森风格 */
.json-data {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 3px 6px;
    background:
        linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    border: 1px solid #333;
    border-radius: 0;
    font-size: 9px;
    max-width: 100%;
    overflow: hidden;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8);
}

.json-icon {
    font-size: 10px;
    color: #666;
}

.json-info {
    color: #888;
    font-weight: 500;
    white-space: nowrap;
    font-family: 'Courier New', monospace;
}

.json-preview {
    color: #999;
    font-family: 'Courier New', monospace;
    font-size: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    opacity: 0.7;
}

/* 长文本显示优化 - 简洁风格 */
.long-text {
    max-width: 100%;
    overflow: hidden;
}

.text-preview {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #aaa;
    font-size: 10px;
}

/* 特殊数据类型样式 */
.null-value {
    color: #666;
    font-style: italic;
    font-size: 10px;
}

.id-value {
    color: #ffaa00;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.number-value {
    color: #00ffaa;
    font-weight: 600;
    text-align: right;
}

.boolean-value {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.boolean-value.true {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.boolean-value.false {
    background: rgba(244, 67, 54, 0.2);
    color: #F44336;
}

.datetime-value {
    font-size: 10px;
    text-align: center;
}

.date-main {
    color: #cccccc;
    font-weight: 600;
}

.date-relative {
    color: #888;
    font-style: italic;
    margin-top: 2px;
}

.progress-bar {
    position: relative;
    width: 100%;
    height: 16px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 8px;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.affinity-value {
    font-weight: 600;
    font-size: 11px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.death-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.death-form-label {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #ff0000;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.death-form-input, .death-form-textarea, .death-form-select {
    padding: 12px 15px;
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid #5a5aaa;
    border-radius: 8px;
    color: #d0d0d0;
    font-family: 'Orbitron', monospace;
    font-size: 13px;
    outline: none;
    transition: all 0.3s ease;
}

.death-form-input:focus, .death-form-textarea:focus, .death-form-select:focus {
    border-color: #ff0000;
    box-shadow:
        0 0 20px rgba(255, 0, 0, 0.3),
        inset 0 0 10px rgba(255, 0, 0, 0.1);
    background: linear-gradient(145deg, #3d2a2a 0%, #2a1d1d 100%);
}

.death-form-input::placeholder, .death-form-textarea::placeholder {
    color: #666;
    font-style: italic;
}

.death-form textarea {
    min-height: 100px;
    resize: vertical;
}

/* 模态框按钮和通知样式 */
.death-modal-footer {
    padding: 28px;
    border-top: 2px solid #5a5aaa;
    display: flex;
    gap: 18px;
    justify-content: center;
    background: linear-gradient(145deg, #16213e 0%, #1a1a2e 100%);
    border-radius: 0 0 20px 20px;
}

.death-modal-btn {
    padding: 12px 25px;
    border: 2px solid;
    border-radius: 8px;
    font-family: 'Orbitron', monospace;
    font-size: 13px;
    font-weight: 700;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.death-cancel-btn {
    background: linear-gradient(145deg, #2d2d2d 0%, #1a1a1a 100%);
    border-color: #666;
    color: #ccc;
}

.death-cancel-btn:hover {
    background: linear-gradient(145deg, #3d3d3d 0%, #2a2a2a 100%);
    border-color: #999;
    box-shadow: 0 0 15px rgba(153, 153, 153, 0.3);
}

.death-save-btn {
    background: linear-gradient(145deg, #2d2d4a 0%, #1a1a3d 100%);
    border-color: #8888ff;
    color: #8888ff;
}

.death-save-btn:hover {
    background: linear-gradient(145deg, #3d3d5a 0%, #2a2a4d 100%);
    border-color: #aaaaff;
    box-shadow: 0 0 15px rgba(136, 136, 255, 0.4);
    transform: scale(1.05);
}

.death-delete-btn {
    background: linear-gradient(145deg, #4a2d2d 0%, #3d1a1a 100%);
    border-color: #ff0000;
    color: #ff0000;
}

.death-delete-btn:hover {
    background: linear-gradient(145deg, #5a3d3d 0%, #4d2a2a 100%);
    border-color: #ff0000;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.4);
}

/* 恐怖加载指示器 */
.death-loading {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.death-loading-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(139, 0, 0, 0.5) 50%,
        rgba(0, 0, 0, 0.9) 100%);
    animation: loadingPulse 2s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 0.7; }
}

.death-loading-container {
    position: relative;
    z-index: 2;
    text-align: center;
    background: linear-gradient(145deg, #1a0d0d 0%, #0d0606 100%);
    border: 3px solid #8b0000;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 0 50px rgba(255, 0, 0, 0.5);
}

.death-loading-skull {
    position: relative;
    margin-bottom: 30px;
}

.death-skull-main {
    font-size: 64px;
    animation: loadingSkullSpin 3s linear infinite;
}

@keyframes loadingSkullSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 恐怖通知样式 */
.death-notification {
    display: none;
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(145deg, #1a0d0d 0%, #0d0606 100%);
    border: 2px solid #8b0000;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.8),
        0 0 20px rgba(255, 0, 0, 0.3);
    z-index: 3000;
    min-width: 300px;
    max-width: 400px;
    animation: notificationSlideIn 0.4s ease-out;
}

@keyframes notificationSlideIn {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 380px;
        min-width: 380px;
        max-width: 380px;
    }

    .death-stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .death-note-panel {
        padding: 14px;
        margin-bottom: 12px;
    }

    .death-stat-card {
        padding: 12px 10px;
        min-height: 75px;
    }
}

@media (max-width: 768px) {
    .app-layout {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr; /* 两行等高 */
        gap: 15px;
        padding: 15px;
        height: 100vh; /* 保持视口高度 */
    }

    .sidebar {
        order: 2;
        height: 100%; /* 占满分配的高度 */
    }

    .main-content {
        order: 1;
        height: 100%; /* 占满分配的高度 */
    }

    .death-note-panel {
        padding: 12px;
        margin-bottom: 10px;
    }

    .death-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .death-stat-card {
        padding: 10px 8px;
        min-height: 70px;
    }

    .death-stat-number {
        font-size: 18px;
        margin-bottom: 4px;
    }

    .death-stat-label {
        font-size: 9px;
    }

    .death-title {
        font-size: 24px;
        letter-spacing: 1px;
    }

    .death-section-title {
        font-size: 14px;
        margin-bottom: 12px;
    }

    .death-button {
        padding: 10px 14px;
        gap: 6px;
    }

    .death-control-grid {
        gap: 6px;
    }

    .death-table-container {
        overflow-x: auto;
        max-width: 100%;
        border-radius: 8px;
    }

    .death-table {
        min-width: 600px;
        font-size: 12px;
    }

    .death-table th,
    .death-table td {
        padding: 8px 6px;
        font-size: 11px;
    }

    .action-btn {
        padding: 3px 6px;
        font-size: 10px;
        min-width: 25px;
        height: 24px;
    }

    .death-add-btn {
        padding: 6px 12px;
        font-size: 11px;
    }

    .death-filter-grid {
        grid-template-columns: 1fr;
    }

    .death-search-container {
        flex-direction: column;
        gap: 10px;
    }

    .death-action-buttons {
        width: 100%;
        justify-content: center;
    }

    .death-modal-container {
        width: 95%;
    }

    .death-notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(145deg, #8b0000 0%, #ff0000 100%);
    border-radius: 4px;
    border: 1px solid #333;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(145deg, #ff0000 0%, #8b0000 100%);
}

/* 选择文本样式 */
::selection {
    background: rgba(255, 0, 0, 0.3);
    color: #fff;
}

::-moz-selection {
    background: rgba(255, 0, 0, 0.3);
    color: #fff;
}
