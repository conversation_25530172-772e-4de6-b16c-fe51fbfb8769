/**
 * Agent生态系统
 * 管理Agent的生存、成长、互动机制
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class AgentEcosystem extends EventEmitter {
    constructor(worldCore) {
        super();
        this.worldCore = worldCore;
        this.logger = worldCore.logger;
        
        // Agent存储
        this.agents = new Map();
        
        // Agent模板
        this.agentTemplates = new Map();
        
        // 生存需求系统
        this.needsSystem = {
            types: ['hunger', 'energy', 'social', 'entertainment', 'achievement'],
            decayRates: {
                hunger: 0.5, // 每小时减少0.5
                energy: 0.3,
                social: 0.2,
                entertainment: 0.4,
                achievement: 0.1
            },
            criticalThresholds: {
                hunger: 20,
                energy: 15,
                social: 25,
                entertainment: 30,
                achievement: 10
            }
        };

        // 技能系统
        this.skillSystem = {
            types: ['learning', 'social', 'creative', 'physical', 'technical'],
            maxLevel: 100,
            experienceRates: {
                learning: 1.2,
                social: 1.0,
                creative: 0.8,
                physical: 1.1,
                technical: 0.9
            }
        };

        // 性格系统
        this.personalitySystem = {
            traits: ['extroversion', 'agreeableness', 'conscientiousness', 'neuroticism', 'openness'],
            changeRates: {
                extroversion: 0.01,
                agreeableness: 0.015,
                conscientiousness: 0.008,
                neuroticism: 0.012,
                openness: 0.01
            }
        };

        // 更新间隔
        this.updateInterval = null;
        this.updateFrequency = 30000; // 30秒更新一次
    }

    /**
     * 初始化Agent生态系统
     */
    async init() {
        this.logger.info('👥 初始化Agent生态系统...');
        
        // 加载Agent模板
        await this.loadAgentTemplates();
        
        // 加载现有Agent
        await this.loadExistingAgents();
        
        this.logger.info('✅ Agent生态系统初始化完成');
    }

    /**
     * 加载Agent模板
     */
    async loadAgentTemplates() {
        // 尝试多个可能的Agent目录路径
        const possiblePaths = [
            path.join(this.worldCore.config.dataDir, '..', '..', 'Agent'),
            path.join(__dirname, '..', '..', 'Agent'),
            path.join(process.cwd(), 'Agent'),
            path.join(process.cwd(), '..', 'Agent'),
            path.join(this.worldCore.config.dataDir, 'sample_agents') // 示例Agent目录
        ];

        let agentDir = null;

        // 找到存在的Agent目录
        for (const testPath of possiblePaths) {
            try {
                await fs.access(testPath);
                agentDir = testPath;
                break;
            } catch {
                // 继续尝试下一个路径
            }
        }

        if (!agentDir) {
            this.logger.warn('⚠️ 未找到Agent目录，将跳过模板加载');
            return;
        }

        try {
            const files = await fs.readdir(agentDir);
            const agentFiles = files.filter(file => file.endsWith('.txt'));

            this.logger.info(`📁 在 ${agentDir} 找到 ${agentFiles.length} 个Agent文件`);

            for (const file of agentFiles) {
                const agentPath = path.join(agentDir, file);
                try {
                    const content = await fs.readFile(agentPath, 'utf-8');

                    // 解析Agent信息
                    const agentInfo = this.parseAgentFile(content, file);
                    if (agentInfo) {
                        this.agentTemplates.set(agentInfo.id, agentInfo);
                        this.logger.info(`📋 加载Agent模板: ${agentInfo.name} (${file})`);
                    }
                } catch (fileError) {
                    this.logger.warn(`⚠️ 读取Agent文件失败: ${file}`, fileError.message);
                }
            }

            this.logger.info(`✅ 成功加载了 ${this.agentTemplates.size} 个Agent模板`);

        } catch (error) {
            this.logger.warn('⚠️ 加载Agent模板失败:', error.message);
        }
    }

    /**
     * 解析Agent文件
     */
    parseAgentFile(content, filename) {
        try {
            const lines = content.split('\n');
            const agentInfo = {
                id: filename.replace('.txt', ''),
                name: '',
                qqNumber: '',
                age: 20,
                gender: '未知',
                description: '',
                personality: this.generateRandomPersonality(),
                skills: this.generateInitialSkills(),
                needs: this.generateInitialNeeds(),
                memories: [],
                relationships: new Map(),
                currentLocation: 'home',
                status: 'idle',
                lastActive: new Date(),
                originalContent: content // 保存原始内容
            };

            // 更全面的解析逻辑，参考server.js的方式
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;

                // 解析昵称/名称
                if (trimmedLine.includes('昵称') || trimmedLine.includes('名称') || trimmedLine.includes('姓名')) {
                    const patterns = [
                        /(?:昵称|名称|姓名).*?[:：]\s*(.+)/,
                        /(?:昵称|名称|姓名)\s*[=＝]\s*(.+)/,
                        /(?:昵称|名称|姓名)\s+(.+)/
                    ];
                    for (const pattern of patterns) {
                        const match = trimmedLine.match(pattern);
                        if (match && match[1]) {
                            agentInfo.name = match[1].trim();
                            break;
                        }
                    }
                }

                // 解析QQ号
                if (trimmedLine.includes('QQ') && /\d/.test(trimmedLine)) {
                    const patterns = [
                        /QQ.*?[:：]\s*(\d+)/,
                        /QQ.*?[=＝]\s*(\d+)/,
                        /QQ\s*(\d+)/
                    ];
                    for (const pattern of patterns) {
                        const match = trimmedLine.match(pattern);
                        if (match && match[1]) {
                            agentInfo.qqNumber = match[1].trim();
                            break;
                        }
                    }
                }

                // 解析年龄
                if (trimmedLine.includes('年龄') && /\d/.test(trimmedLine)) {
                    const patterns = [
                        /年龄.*?[:：]\s*(\d+)/,
                        /年龄.*?[=＝]\s*(\d+)/,
                        /年龄\s*(\d+)/
                    ];
                    for (const pattern of patterns) {
                        const match = trimmedLine.match(pattern);
                        if (match && match[1]) {
                            const age = parseInt(match[1]);
                            if (age > 0 && age < 150) {
                                agentInfo.age = age;
                            }
                            break;
                        }
                    }
                }

                // 解析性别
                if (trimmedLine.includes('性别')) {
                    const patterns = [
                        /性别.*?[:：]\s*(.+)/,
                        /性别.*?[=＝]\s*(.+)/,
                        /性别\s+(.+)/
                    ];
                    for (const pattern of patterns) {
                        const match = trimmedLine.match(pattern);
                        if (match && match[1]) {
                            const gender = match[1].trim();
                            // 标准化性别值
                            if (gender.includes('男') && !gender.includes('女')) {
                                agentInfo.gender = '男';
                            } else if (gender.includes('女') && !gender.includes('男')) {
                                agentInfo.gender = '女';
                            } else {
                                agentInfo.gender = gender;
                            }
                            break;
                        }
                    }
                }

                // 提取描述信息（取前几行作为描述）
                if (!agentInfo.description && trimmedLine.length > 10 &&
                    !trimmedLine.includes('昵称') && !trimmedLine.includes('QQ') &&
                    !trimmedLine.includes('年龄') && !trimmedLine.includes('性别')) {
                    agentInfo.description = trimmedLine.substring(0, 100) + (trimmedLine.length > 100 ? '...' : '');
                }
            }

            // 如果没有解析到名称，使用文件名
            if (!agentInfo.name) {
                agentInfo.name = filename.replace('.txt', '');
            }

            // 如果没有描述，生成一个简单的描述
            if (!agentInfo.description) {
                agentInfo.description = `这是一个${agentInfo.age}岁的${agentInfo.gender}性Agent`;
            }

            return agentInfo;

        } catch (error) {
            this.logger.warn(`⚠️ 解析Agent文件失败: ${filename}`, error.message);
            return null;
        }
    }

    /**
     * 生成随机性格
     */
    generateRandomPersonality() {
        const personality = {};
        this.personalitySystem.traits.forEach(trait => {
            personality[trait] = Math.random() * 100;
        });
        return personality;
    }

    /**
     * 生成初始技能
     */
    generateInitialSkills() {
        const skills = {};
        this.skillSystem.types.forEach(skill => {
            skills[skill] = Math.random() * 20 + 10; // 10-30的初始技能
        });
        return skills;
    }

    /**
     * 生成初始需求
     */
    generateInitialNeeds() {
        const needs = {};
        this.needsSystem.types.forEach(need => {
            needs[need] = Math.random() * 30 + 70; // 70-100的初始需求满足度
        });
        return needs;
    }

    /**
     * 加载现有Agent
     */
    async loadExistingAgents() {
        const agentsDir = this.worldCore.dataPath.agents;
        
        try {
            const files = await fs.readdir(agentsDir);
            const agentFiles = files.filter(file => file.endsWith('.json'));
            
            for (const file of agentFiles) {
                const agentPath = path.join(agentsDir, file);
                const content = await fs.readFile(agentPath, 'utf-8');
                const agentData = JSON.parse(content);
                
                // 恢复Agent状态
                this.agents.set(agentData.id, agentData);
            }
            
            this.logger.info(`👥 加载了 ${this.agents.size} 个现有Agent`);
            
        } catch (error) {
            this.logger.info('🆕 没有找到现有Agent数据');
        }
    }

    /**
     * 创建新Agent
     */
    async createAgent(config = {}) {
        let agentData;
        
        if (config.templateId && this.agentTemplates.has(config.templateId)) {
            // 基于模板创建
            agentData = { ...this.agentTemplates.get(config.templateId) };
        } else {
            // 创建全新Agent
            agentData = {
                id: config.id || `agent_${Date.now()}`,
                name: config.name || `Agent${Math.floor(Math.random() * 1000)}`,
                qqNumber: config.qqNumber || '',
                age: config.age || Math.floor(Math.random() * 50) + 18,
                gender: config.gender || 'unknown',
                personality: this.generateRandomPersonality(),
                skills: this.generateInitialSkills(),
                needs: this.generateInitialNeeds(),
                memories: [],
                relationships: new Map(),
                currentLocation: 'home',
                status: 'idle',
                lastActive: new Date()
            };
        }

        // 应用自定义配置
        Object.assign(agentData, config);
        
        // 确保必要字段
        agentData.createdAt = new Date();
        agentData.totalExperience = 0;
        agentData.level = 1;
        agentData.mood = this.calculateMood(agentData);
        
        // 保存Agent
        this.agents.set(agentData.id, agentData);
        await this.saveAgent(agentData);
        
        this.emit('agentCreated', agentData);
        this.logger.info(`👤 创建新Agent: ${agentData.name} (${agentData.id})`);
        
        return agentData;
    }

    /**
     * 移除Agent
     */
    async removeAgent(agentId) {
        const agent = this.agents.get(agentId);
        if (!agent) {
            throw new Error(`Agent不存在: ${agentId}`);
        }

        // 从当前位置移除
        if (agent.currentLocation) {
            this.worldCore.worldEnvironment.leaveLocation(agentId, agent.currentLocation);
        }

        // 清理关系
        this.worldCore.socialNetwork.removeAgentRelationships(agentId);

        // 删除数据文件
        const agentPath = path.join(this.worldCore.dataPath.agents, `${agentId}.json`);
        try {
            await fs.unlink(agentPath);
        } catch (error) {
            this.logger.warn(`⚠️ 删除Agent文件失败: ${agentPath}`);
        }

        // 从内存中移除
        this.agents.delete(agentId);
        
        this.emit('agentRemoved', agentId);
        this.logger.info(`👤 移除Agent: ${agentId}`);
    }

    /**
     * 保存Agent数据
     */
    async saveAgent(agent) {
        const agentPath = path.join(this.worldCore.dataPath.agents, `${agent.id}.json`);
        await fs.writeFile(agentPath, JSON.stringify(agent, null, 2));
    }

    /**
     * 启动生态系统
     */
    async start() {
        if (this.updateInterval) {
            return;
        }

        this.updateInterval = setInterval(() => {
            this.updateAgents();
        }, this.updateFrequency);

        this.logger.info('👥 Agent生态系统已启动');
    }

    /**
     * 暂停生态系统
     */
    async pause() {
        this.logger.info('⏸️ Agent生态系统已暂停');
    }

    /**
     * 恢复生态系统
     */
    async resume() {
        this.logger.info('▶️ Agent生态系统已恢复');
    }

    /**
     * 停止生态系统
     */
    async stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        // 保存所有Agent数据
        for (const agent of this.agents.values()) {
            await this.saveAgent(agent);
        }

        this.logger.info('🛑 Agent生态系统已停止');
    }

    /**
     * 更新所有Agent
     */
    updateAgents() {
        if (this.worldCore.worldState.isPaused) {
            return;
        }

        for (const agent of this.agents.values()) {
            this.updateAgent(agent);
        }
    }

    /**
     * 更新单个Agent
     */
    updateAgent(agent) {
        // 更新需求
        this.updateAgentNeeds(agent);
        
        // 更新心情
        agent.mood = this.calculateMood(agent);
        
        // 检查关键需求
        this.checkCriticalNeeds(agent);
        
        // 更新最后活跃时间
        agent.lastActive = new Date();
        
        this.emit('agentUpdated', agent);
    }

    /**
     * 更新Agent需求
     */
    updateAgentNeeds(agent) {
        const timeElapsed = (Date.now() - new Date(agent.lastActive).getTime()) / (1000 * 60 * 60); // 小时
        
        for (const need of this.needsSystem.types) {
            const decayRate = this.needsSystem.decayRates[need];
            agent.needs[need] = Math.max(0, agent.needs[need] - (decayRate * timeElapsed));
        }
    }

    /**
     * 计算心情
     */
    calculateMood(agent) {
        const needsAverage = this.needsSystem.types.reduce((sum, need) => {
            return sum + agent.needs[need];
        }, 0) / this.needsSystem.types.length;

        // 基于需求满足度和性格计算心情
        const personalityFactor = (agent.personality.neuroticism || 50) / 100;
        const mood = needsAverage * (1 - personalityFactor * 0.3);
        
        return Math.max(0, Math.min(100, mood));
    }

    /**
     * 检查关键需求
     */
    checkCriticalNeeds(agent) {
        for (const need of this.needsSystem.types) {
            const threshold = this.needsSystem.criticalThresholds[need];
            if (agent.needs[need] < threshold) {
                this.emit('criticalNeed', {
                    agent,
                    need,
                    value: agent.needs[need],
                    threshold
                });
            }
        }
    }

    /**
     * 获取Agent
     */
    getAgent(agentId) {
        return this.agents.get(agentId);
    }

    /**
     * 获取所有Agent
     */
    getAllAgents() {
        return Array.from(this.agents.values());
    }

    /**
     * 获取在线Agent
     */
    getActiveAgents() {
        const now = Date.now();
        const activeThreshold = 5 * 60 * 1000; // 5分钟内活跃
        
        return Array.from(this.agents.values()).filter(agent => {
            return (now - new Date(agent.lastActive).getTime()) < activeThreshold;
        });
    }

    /**
     * 获取统计信息
     */
    async getStatistics() {
        const agents = Array.from(this.agents.values());
        
        return {
            total: agents.length,
            active: this.getActiveAgents().length,
            averageAge: agents.reduce((sum, agent) => sum + agent.age, 0) / agents.length || 0,
            averageMood: agents.reduce((sum, agent) => sum + agent.mood, 0) / agents.length || 0,
            genderDistribution: this.getGenderDistribution(agents),
            locationDistribution: this.getLocationDistribution(agents),
            skillAverages: this.getSkillAverages(agents)
        };
    }

    /**
     * 获取性别分布
     */
    getGenderDistribution(agents) {
        const distribution = {};
        agents.forEach(agent => {
            distribution[agent.gender] = (distribution[agent.gender] || 0) + 1;
        });
        return distribution;
    }

    /**
     * 获取位置分布
     */
    getLocationDistribution(agents) {
        const distribution = {};
        agents.forEach(agent => {
            distribution[agent.currentLocation] = (distribution[agent.currentLocation] || 0) + 1;
        });
        return distribution;
    }

    /**
     * 获取技能平均值
     */
    getSkillAverages(agents) {
        const averages = {};
        this.skillSystem.types.forEach(skill => {
            const total = agents.reduce((sum, agent) => sum + (agent.skills[skill] || 0), 0);
            averages[skill] = total / agents.length || 0;
        });
        return averages;
    }

    /**
     * 销毁生态系统
     */
    async destroy() {
        await this.stop();
        this.removeAllListeners();
        this.logger.info('🗑️ Agent生态系统已销毁');
    }
}

module.exports = { AgentEcosystem };
