/**
 * 记忆深渊 - Memory Nexus
 * 现代化恐怖RPG风格的智能记忆管理系统
 */

class MemoryNexus {
    constructor() {
        // API配置 - 修复端口问题
        this.apiBase = '/admin_api/memory';
        this.authHeader = 'Basic ' + btoa('admin:123456');

        // 调试信息
        console.log('🔧 初始化API配置:', {
            apiBase: this.apiBase,
            authHeader: this.authHeader
        });

        // 状态管理
        this.currentTable = null;
        this.currentPage = 1;
        this.pageSize = 20;
        this.searchQuery = '';
        this.isLoading = false;
        this.tableData = [];
        this.totalRecords = 0;
        this.totalPages = 1;
        this.systemMonitorInterval = null;

        // 排序和筛选状态
        this.sortColumn = null;
        this.sortDirection = 'desc'; // 默认降序，最新的在前面
        this.filterUserId = '';
        this.filterPersonaName = '';
        this.filterDateRange = { start: '', end: '' };

        // 数据库字段中文映射
        this.fieldMappings = {
            // 通用字段
            'id': 'ID',
            'user_id': '用户ID',
            'persona_name': '角色名称',
            'timestamp': '时间戳',
            'creation_time': '创建时间',
            'last_update_time': '最后更新',
            'last_accessed': '最后访问',

            // conversation_sessions 表
            'session_id': '会话ID',
            'chat_type': '聊天类型',
            'chat_name': '聊天名称',
            'user_content': '用户消息',
            'assistant_content': 'AI回复',
            'message_count': '消息数量',
            'first_message_time': '首次消息时间',
            'importance_score': '重要性分数',
            'emotion_summary': '情感摘要',
            'topic_tags': '话题标签',

            // memory_fragments 表
            'memory_type': '记忆类型',
            'content': '内容',
            'ai_summary': 'AI摘要',
            'key_insights': '关键洞察',
            'conversation_theme': '对话主题',
            'embedding_vector': '嵌入向量',
            'access_count': '访问次数',
            'expires_at': '过期时间',
            'related_concepts': '相关概念',
            'emotional_context': '情感上下文',

            // recent_conversations 表
            'original_content': '原始内容',
            'summary_info': '摘要信息',

            // concept_neurons 表
            'concept_name': '概念名称',
            'concept_type': '概念类型',
            'activation_strength': '激活强度',
            'activation_count': '激活次数',
            'last_activation': '最后激活',
            'associated_emotions': '关联情绪',
            'detailed_meaning': '详细含义',

            // concept_associations 表
            'concept_a': '概念A',
            'concept_b': '概念B',
            'association_strength': '关联强度',
            'co_occurrence_count': '共现次数',
            'last_occurrence': '最后共现',
            'association_type': '关联类型',

            // ai_stress_states 表
            'stress_level': '压力等级',
            'stress_factors': '压力因素',
            'coping_strategies': '应对策略',
            'recovery_suggestions': '恢复建议',

            // meme_cognition_states 表
            'meme_content': '模因内容',
            'cognitive_load': '认知负荷',
            'processing_depth': '处理深度',
            'retention_probability': '保留概率',
            'influence_network': '影响网络',

            // world_tree_states 表
            'current_branch': '当前分支',
            'narrative_context': '叙事背景',
            'world_state': '世界状态',
            'character_role': '角色定位',
            'story_progression': '故事进展',
            'background_influence': '背景影响',

            // system_state 表
            'state_key': '状态键',
            'state_value': '状态值',
            'last_updated': '最后更新'
        };
        
        // 中文字段映射 - 完整的字段翻译
        this.fieldNameMap = {
            // 通用字段
            'id': 'ID',
            'user_id': '用户ID',
            'persona_name': '角色名称',
            'timestamp': '时间戳',
            'created_at': '创建时间',
            'updated_at': '更新时间',
            'last_updated': '最后更新',
            'rowid': '行ID',

            // 对话历史
            'speaker': '发言者',
            'content': '内容',
            'message_type': '消息类型',
            'conversation_id': '对话ID',
            'embedding_vector': '嵌入向量',
            'emotion_state': '情绪状态',
            'tokens': '令牌数',
            'importance_score': '重要性评分',

            // 概念相关
            'name': '名称',
            'meaning': '含义',
            'type': '类型',
            'confidence': '置信度',
            'concept_name': '概念名称',
            'concept1_id': '概念1 ID',
            'concept2_id': '概念2 ID',
            'association_type': '关联类型',
            'strength': '关联强度',
            'learned_from_context': '学习上下文',

            // 记忆片段
            'memory_type': '记忆类型',
            'creation_time': '创建时间',
            'importance': '重要性',
            'embedding': '向量嵌入',
            'expires_at': '过期时间',
            'related_concepts': '相关概念',
            'emotional_context': '情绪上下文',

            // 最近对话记录
            'original_content': '原始对话内容',
            'summary_info': '摘要信息',

            // 情感和好感度
            'affinity_value': '好感度值',
            'current_affinity': '当前好感度',
            'relationship_type': '关系类型',
            'emotion_valence': '情绪效价',
            'emotion_arousal': '情绪唤醒',
            'emotion_dominance': '情绪支配',
            'total_interactions': '总互动次数',
            'positive_interactions': '正面互动次数',
            'negative_interactions': '负面互动次数',
            'last_interaction': '最后互动时间',
            'affinity_history': '好感度历史',
            'trend': '趋势',
            'interaction_quality': '互动质量',
            'emotion_type': '情绪类型',
            'emotion_value': '情绪值',
            'intensity': '强度',
            'trigger_event': '触发事件',
            'stability': '稳定性',
            'current_emotion': '当前情绪',
            'previous_emotion': '之前情绪',
            'regulation_reason': '调节原因',

            // 系统状态
            'config_key': '配置键',
            'config_value': '配置值',
            'state_key': '状态键',
            'state_value': '状态值',
            'description': '描述',
            'last_modified': '最后修改',

            // 概念神经元
            'activation_level': '激活级别',
            'last_accessed': '最后访问',
            'access_count': '访问次数',
            'neural_weight': '神经权重',
            'connection_strength': '连接强度',

            // 世界树系统
            'current_branch': '当前分支',
            'narrative_context': '叙事背景',
            'world_state': '世界状态',
            'character_role': '角色定位',
            'story_progression': '故事进展',
            'background_influence': '背景影响',

            // 模因系统
            'meme_id': '模因ID',
            'meme_type': '模因类型',
            'propagation_strength': '传播强度',
            'mutation_rate': '变异率',
            'fitness_score': '适应性评分',

            // 性能监控
            'cpu_usage': 'CPU使用率',
            'memory_usage': '内存使用率',
            'gpu_usage': 'GPU使用率',
            'response_time': '响应时间',
            'error_count': '错误计数',

            // 其他常用字段
            'source': '来源',
            'original_content': '原始内容',
            'processed_content': '处理后内容',
            'metadata': '元数据',
            'tags': '标签',
            'category': '分类',
            'priority': '优先级',
            'status': '状态',
            'version': '版本',
            'hash': '哈希值'
        };

        // 表格配置映射 - 完整的中文映射（包含实际数据库表名）
        this.tableConfig = {
            // 核心记忆板块
            'conversation_history': {
                name: '对话历史',
                icon: '💬',
                category: 'core',
                description: '用户与AI的对话记录',
                columns: ['id', 'user_id', 'persona_name', 'speaker', 'content', 'timestamp']
            },
            'recent_conversations': {
                name: '最近对话记录',
                icon: '🕒',
                category: 'core',
                description: '按助手和用户分类的最近对话记录（替代本地文件存储）',
                columns: ['id', 'persona_name', 'user_id', 'content', 'creation_time', 'importance_score']
            },
            'concepts': {
                name: '概念库',
                icon: '🧠',
                category: 'core',
                description: '学习到的概念和知识',
                columns: ['id', 'name', 'meaning', 'type', 'confidence', 'created_at']
            },
            'concept_associations': {
                name: '概念关联',
                icon: '🔗',
                category: 'core',
                description: '概念之间的关联关系',
                columns: ['id', 'concept1_id', 'concept2_id', 'association_type', 'strength']
            },
            'memory_fragments': {
                name: '记忆片段',
                icon: '🧩',
                category: 'core',
                description: '短期和长期记忆片段',
                columns: ['id', 'user_id', 'persona_name', 'content', 'memory_type', 'creation_time']
            },

            // 情感系统板块
            'user_affinity': {
                name: '用户好感度',
                icon: '💖',
                category: 'emotion',
                description: 'AI对用户的当前好感度状态（主表）',
                columns: ['id', 'user_id', 'persona_name', 'current_affinity', 'relationship_type', 'total_interactions', 'last_interaction']
            },
            'user_affinity_states': {
                name: '好感度变化历史',
                icon: '📈',
                category: 'emotion',
                description: 'AI对用户的好感度变化历史记录',
                columns: ['id', 'user_id', 'persona_name', 'affinity_value', 'trend', 'interaction_quality', 'timestamp']
            },
            'ai_emotion_states': {
                name: 'AI情绪状态',
                icon: '😊',
                category: 'emotion',
                description: 'AI的情绪变化记录',
                columns: ['id', 'user_id', 'persona_name', 'emotion_type', 'intensity', 'trigger_event', 'timestamp']
            },
            'ai_stress_states': {
                name: 'AI压力状态',
                icon: '😰',
                category: 'emotion',
                description: 'AI的压力状态变化记录',
                columns: ['id', 'user_id', 'persona_name', 'stress_value', 'stress_level', 'stress_factors', 'timestamp']
            },

            'emotion_regulation_log': {
                name: '情绪调节日志',
                icon: '🎭',
                category: 'emotion',
                description: 'AI情绪自动调节记录'
            },
            'meme_cognition_states': {
                name: '模因认知状态',
                icon: '🧬',
                category: 'emotion',
                description: '模因认知系统状态记录'
            },

            // 神经网络板块
            'concept_neurons': {
                name: '概念神经元',
                icon: '🔬',
                category: 'neural',
                description: '概念的神经网络表示'
            },
            'memetic_networks': {
                name: '模因网络',
                icon: '🕸️',
                category: 'neural',
                description: '模因认知网络结构'
            },
            'neural_connections': {
                name: '神经连接',
                icon: '⚡',
                category: 'neural',
                description: '神经元之间的连接关系'
            },

            // 世界树系统板块
            'world_tree_states': {
                name: '世界树状态',
                icon: '🌳',
                category: 'worldtree',
                description: '世界树背景状态记录'
            },
            'narrative_contexts': {
                name: '叙事背景',
                icon: '📖',
                category: 'worldtree',
                description: '故事叙事背景信息'
            },
            'character_roles': {
                name: '角色定位',
                icon: '🎭',
                category: 'worldtree',
                description: '角色在世界树中的定位'
            },

            // 系统管理板块
            'system_state': {
                name: '系统状态',
                icon: '⚙️',
                category: 'system',
                description: '系统配置和状态信息'
            },
            'system_logs': {
                name: '系统日志',
                icon: '📋',
                category: 'system',
                description: '系统运行日志记录'
            },
            'performance_metrics': {
                name: '性能指标',
                icon: '📊',
                category: 'system',
                description: '系统性能监控数据'
            }
        };

        // 板块分类映射
        this.categoryConfig = {
            'core': {
                name: '核心记忆',
                icon: '🧠',
                color: '#00d4ff',
                description: '对话、概念、记忆等核心数据'
            },
            'emotion': {
                name: '情感系统',
                icon: '💖',
                color: '#8b5cf6',
                description: '情绪状态、好感度等情感数据'
            },
            'neural': {
                name: '神经网络',
                icon: '🔬',
                color: '#10b981',
                description: '神经元、连接等网络结构数据'
            },
            'worldtree': {
                name: '世界树',
                icon: '🌳',
                color: '#f59e0b',
                description: '世界背景、角色、叙事等数据'
            },
            'system': {
                name: '系统管理',
                icon: '⚙️',
                color: '#ef4444',
                description: '系统配置、日志、监控等管理数据'
            }
        };
        
        // 初始化
        this.init();
    }

    /**
     * 初始化系统
     */
    async init() {
        console.log('🚀 初始化记忆深渊系统...');

        // 初始化神经网络背景
        this.initNeuralBackground();

        // 绑定事件监听器
        this.bindEventListeners();

        // 初始化系统监控
        this.initSystemMonitor();

        // 测试API连接
        await this.testApiConnection();

        // 加载数据库信息
        await this.loadDatabaseInfo();

        // 显示欢迎界面
        this.showWelcomeScreen();

        console.log('✅ 记忆深渊系统初始化完成');
    }

    /**
     * 测试API连接
     */
    async testApiConnection() {
        console.log('🔍 测试API连接...');
        try {
            // 先测试一个简单的端点
            const response = await fetch('/admin_api/memory/database/info', {
                headers: {
                    'Authorization': this.authHeader
                }
            });

            console.log('🔍 API测试响应状态:', response.status, response.statusText);

            if (response.status === 401) {
                console.error('❌ 认证失败 - 检查用户名密码');
                this.showNotification('API认证失败，请检查用户名密码', 'error');
                return false;
            }

            if (!response.ok) {
                console.error('❌ API连接失败:', response.status, response.statusText);
                this.showNotification(`API连接失败: ${response.status}`, 'error');
                return false;
            }

            console.log('✅ API连接测试成功');
            return true;
        } catch (error) {
            console.error('❌ API连接测试失败:', error);
            this.showNotification('无法连接到API服务器', 'error');
            return false;
        }
    }

    /**
     * 初始化神经网络背景动画
     */
    initNeuralBackground() {
        const canvas = document.getElementById('neural-canvas');
        const ctx = canvas.getContext('2d');
        
        // 设置画布大小
        const resizeCanvas = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // 神经网络节点
        const nodes = [];
        const nodeCount = 50;
        
        // 创建节点
        for (let i = 0; i < nodeCount; i++) {
            nodes.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                radius: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2
            });
        }
        
        // 动画循环
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 更新和绘制节点
            nodes.forEach((node, i) => {
                // 更新位置
                node.x += node.vx;
                node.y += node.vy;
                
                // 边界检测
                if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                if (node.y < 0 || node.y > canvas.height) node.vy *= -1;
                
                // 绘制节点
                ctx.beginPath();
                ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(0, 212, 255, ${node.opacity})`;
                ctx.fill();
                
                // 绘制连接线
                nodes.forEach((otherNode, j) => {
                    if (i !== j) {
                        const distance = Math.sqrt(
                            Math.pow(node.x - otherNode.x, 2) + 
                            Math.pow(node.y - otherNode.y, 2)
                        );
                        
                        if (distance < 150) {
                            ctx.beginPath();
                            ctx.moveTo(node.x, node.y);
                            ctx.lineTo(otherNode.x, otherNode.y);
                            ctx.strokeStyle = `rgba(139, 92, 246, ${0.1 * (1 - distance / 150)})`;
                            ctx.lineWidth = 1;
                            ctx.stroke();
                        }
                    }
                });
            });
            
            requestAnimationFrame(animate);
        };
        
        animate();
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 同步数据按钮
        document.getElementById('sync-data').addEventListener('click', () => {
            this.loadDatabaseInfo();
        });


        
        // 搜索功能
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');
        
        searchBtn.addEventListener('click', () => this.performSearch());
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.performSearch();
        });
        
        // 分页控制
        document.getElementById('prev-page').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadTableData();
            }
        });
        
        document.getElementById('next-page').addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadTableData();
            }
        });
        
        // 模态框控制
        document.getElementById('modal-close').addEventListener('click', () => {
            this.hideModal();
        });
        
        document.getElementById('cancel-edit').addEventListener('click', () => {
            this.hideModal();
        });
        
        document.getElementById('save-edit').addEventListener('click', () => {
            this.saveRecord();
        });
        
        document.getElementById('delete-record').addEventListener('click', () => {
            this.deleteRecord();
        });

        // 新增记录
        document.getElementById('add-record').addEventListener('click', () => {
            this.showAddModal();
        });

        // 批量删除功能
        document.getElementById('batch-delete').addEventListener('click', () => {
            this.batchDeleteRecords();
        });

        // 清空板块功能
        document.getElementById('clear-table').addEventListener('click', () => {
            this.clearTable();
        });

        // 删除搜索结果功能
        document.getElementById('delete-search-results').addEventListener('click', () => {
            this.deleteSearchResults();
        });

        // 删除筛选结果功能
        document.getElementById('delete-filter-results').addEventListener('click', () => {
            this.deleteFilterResults();
        });

        // 应用筛选按钮
        document.getElementById('apply-filters').addEventListener('click', () => {
            this.applyFilters();
        });

        // 清除筛选按钮
        document.getElementById('clear-filters').addEventListener('click', () => {
            this.clearAllFilters();
        });

        // 筛选输入框回车键应用筛选
        ['filter-user-id', 'filter-persona-name'].forEach(id => {
            document.getElementById(id).addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });
        });

        // 筛选状态关闭按钮
        document.getElementById('filter-status-close').addEventListener('click', () => {
            this.hideFilterStatus();
        });

        // 表头排序事件委托
        document.getElementById('table-head').addEventListener('click', (e) => {
            const header = e.target.closest('.sortable-header');
            if (header) {
                const column = header.dataset.column;
                console.log('🔄 表头点击事件:', column);
                this.sortByColumn(column);
            }
        });

        // 点击遮罩关闭模态框
        document.getElementById('edit-modal').addEventListener('click', (e) => {
            if (e.target.id === 'edit-modal') {
                this.hideModal();
            }
        });
    }

    /**
     * 发送API请求
     */
    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const config = {
            headers: {
                'Authorization': this.authHeader,
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        console.log(`🌐 API请求: ${config.method || 'GET'} ${url}`);

        try {
            const response = await fetch(url, config);

            console.log(`📡 响应状态: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                // 尝试获取错误详情
                let errorDetail = '';
                try {
                    const errorData = await response.json();
                    errorDetail = errorData.error || errorData.message || '';
                } catch (e) {
                    errorDetail = await response.text();
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}${errorDetail ? ' - ' + errorDetail : ''}`);
            }

            const data = await response.json();
            console.log(`✅ API响应:`, data);
            return data;
        } catch (error) {
            console.error(`❌ API错误:`, error);
            this.showNotification('API请求失败: ' + error.message, 'error');
            throw error;
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(show = true) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.add('show');
            this.isLoading = true;
        } else {
            overlay.classList.remove('show');
            this.isLoading = false;
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);

        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => container.removeChild(notification), 300);
        }, duration);
    }

    /**
     * 加载数据库信息
     */
    async loadDatabaseInfo() {
        try {
            this.showLoading(true);

            const response = await this.apiRequest('/database/info');

            if (response.success) {
                const { tables } = response.data;

                // 更新导航菜单
                this.updateNavigation(tables);

                // 更新统计信息
                const totalRecords = tables.reduce((sum, table) => sum + table.count, 0);
                document.getElementById('total-records').textContent = totalRecords;
                document.getElementById('memory-count').textContent = `${totalRecords} 条记录`;
                document.getElementById('db-status').textContent = '正常运行';

                this.showNotification('数据库信息加载成功', 'success');
            }
        } catch (error) {
            document.getElementById('db-status').textContent = '连接失败';
            this.showNotification('数据库连接失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 更新导航菜单 - 按分类显示
     */
    updateNavigation(tables) {
        const navigationContainer = document.querySelector('.navigation-panel');

        // 清空现有导航
        navigationContainer.innerHTML = '';

        // 按分类组织表格
        const categorizedTables = {};
        tables.forEach(table => {
            const config = this.tableConfig[table.name] || {
                name: table.name,
                icon: '📄',
                category: 'system',
                description: '数据表'
            };

            const category = config.category;
            if (!categorizedTables[category]) {
                categorizedTables[category] = [];
            }
            categorizedTables[category].push({ ...table, config });
        });

        // 生成分类导航
        Object.keys(categorizedTables).forEach(categoryKey => {
            const categoryInfo = this.categoryConfig[categoryKey] || {
                name: categoryKey,
                icon: '📁',
                color: '#666666',
                description: '未分类'
            };

            const categorySection = document.createElement('div');
            categorySection.className = 'nav-category';
            categorySection.innerHTML = `
                <div class="category-header" style="border-left-color: ${categoryInfo.color};">
                    <span class="category-icon">${categoryInfo.icon}</span>
                    <span class="category-name">${categoryInfo.name}</span>
                    <span class="category-count">${categorizedTables[categoryKey].length}</span>
                </div>
                <div class="category-items" id="category-${categoryKey}">
                    ${categorizedTables[categoryKey].map(table => `
                        <div class="nav-item" data-table="${table.name}" title="${table.config.description}">
                            <span class="nav-icon">${table.config.icon}</span>
                            <span class="nav-text">${table.config.name}</span>
                            <span class="nav-count">${table.count}</span>
                        </div>
                    `).join('')}
                </div>
            `;

            navigationContainer.appendChild(categorySection);

            // 绑定点击事件
            categorySection.querySelectorAll('.nav-item').forEach(navItem => {
                navItem.addEventListener('click', () => {
                    const tableName = navItem.dataset.table;
                    const tableData = categorizedTables[categoryKey].find(t => t.name === tableName);
                    this.selectTable(tableName, tableData.config.name);
                });
            });

            // 分类折叠功能
            const categoryHeader = categorySection.querySelector('.category-header');
            const categoryItems = categorySection.querySelector('.category-items');

            categoryHeader.addEventListener('click', () => {
                categoryItems.classList.toggle('collapsed');
                categoryHeader.classList.toggle('collapsed');
            });
        });
    }

    /**
     * 选择表格
     */
    async selectTable(tableName, displayName) {
        console.log(`🎯 选择表格: ${tableName} (${displayName})`);

        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        const targetNavItem = document.querySelector(`[data-table="${tableName}"]`);
        if (targetNavItem) {
            targetNavItem.classList.add('active');
        } else {
            console.warn(`⚠️ 找不到导航项: [data-table="${tableName}"]`);
        }

        // 设置当前表格
        this.currentTable = tableName;
        this.currentPage = 1;
        this.searchQuery = '';

        // 更新界面
        document.getElementById('table-title').textContent = displayName;
        document.getElementById('search-input').value = '';

        // 显示表格界面
        this.showTableInterface();

        // 加载表格数据
        await this.loadTableData();
    }

    /**
     * 显示表格界面
     */
    showTableInterface() {
        document.getElementById('welcome-screen').style.display = 'none';
        document.getElementById('table-interface').style.display = 'flex';
    }

    /**
     * 显示欢迎界面
     */
    showWelcomeScreen() {
        document.getElementById('welcome-screen').style.display = 'flex';
        document.getElementById('table-interface').style.display = 'none';
    }

    /**
     * 加载表格数据
     */
    async loadTableData() {
        if (!this.currentTable) {
            console.warn('⚠️ 没有选择表格');
            return;
        }

        try {
            this.showLoading(true);

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search: this.searchQuery
            });

            // 添加排序参数
            if (this.sortColumn) {
                params.append('sortBy', this.sortColumn);
                params.append('sortOrder', this.sortDirection);
            }
            // 注意：默认排序逻辑移到后端处理

            // 添加筛选参数
            if (this.filterUserId) {
                params.append('filterUserId', this.filterUserId);
            }
            if (this.filterPersonaName) {
                params.append('filterPersonaName', this.filterPersonaName);
            }
            if (this.filterDateRange.start) {
                params.append('filterDateStart', this.filterDateRange.start);
            }
            if (this.filterDateRange.end) {
                params.append('filterDateEnd', this.filterDateRange.end);
            }

            console.log(`🔍 加载表格数据: ${this.currentTable}, 页码: ${this.currentPage}, 排序: ${this.sortColumn} ${this.sortDirection}`);
            console.log(`🔍 筛选参数: 用户ID=${this.filterUserId}, 角色=${this.filterPersonaName}, 日期=${this.filterDateRange.start}-${this.filterDateRange.end}`);
            console.log(`🔍 请求URL: /table/${this.currentTable}?${params.toString()}`);
            const response = await this.apiRequest(`/table/${this.currentTable}?${params}`);

            console.log('📊 API响应:', response);

            if (response && response.success) {
                // 修复数据结构解析 - API返回的是 data.data 而不是 data.records
                this.tableData = response.data.data || [];
                this.totalRecords = response.data.pagination?.total || 0;
                this.totalPages = Math.ceil(this.totalRecords / this.pageSize);

                console.log(`✅ 数据加载成功: ${this.tableData.length} 条记录, 总计: ${this.totalRecords}`);
                console.log('📋 表格数据:', this.tableData);

                // 更新表格
                this.updateTable();

                // 更新分页
                this.updatePagination();

                // 更新统计
                this.updateTableStats();
                this.updateDataStats();

                // 更新筛选状态显示
                this.updateFilterStatus();

                this.showNotification(`成功加载 ${this.tableData.length} 条记录`, 'success');
            } else {
                console.error('❌ API响应失败:', response);
                this.showNotification(`加载失败: ${response?.message || '未知错误'}`, 'error');
            }
        } catch (error) {
            console.error('❌ 加载表格数据异常:', error);
            this.showNotification('加载表格数据失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 更新表格
     */
    updateTable() {
        const tableHead = document.getElementById('table-head');
        const tableBody = document.getElementById('table-body');

        if (this.tableData.length === 0) {
            tableHead.innerHTML = '';
            tableBody.innerHTML = '<tr><td colspan="100%" style="text-align: center; padding: 2rem; color: var(--text-muted);">暂无数据</td></tr>';
            return;
        }

        // 生成表头
        const firstRecord = this.tableData[0];
        const columns = Object.keys(firstRecord);

        // 计算列宽 - 添加复选框列
        const checkboxWidth = 5; // 5%给复选框
        const dataColumnWidth = Math.floor(75 / columns.length); // 75%给数据列
        const actionColumnWidth = 20; // 20%给操作列

        tableHead.innerHTML = `
            <tr>
                <th style="width: ${checkboxWidth}%;">
                    <input type="checkbox" id="select-all" onchange="memoryNexus.toggleSelectAll(this)" title="全选/取消全选">
                </th>
                ${columns.map(col => `
                    <th style="width: ${dataColumnWidth}%; cursor: pointer;"
                        data-column="${col}"
                        class="sortable-header"
                        title="点击排序">
                        ${this.formatColumnName(col)}
                        ${this.getSortIcon(col)}
                    </th>
                `).join('')}
                <th style="width: ${actionColumnWidth}%;">操作</th>
            </tr>
        `;

        // 生成表格内容
        tableBody.innerHTML = this.tableData.map((record, index) => `
            <tr>
                <td style="width: ${checkboxWidth}%;">
                    <input type="checkbox" class="record-checkbox" data-id="${record.id}" onchange="memoryNexus.updateBatchButtons()" title="选择此记录">
                </td>
                ${columns.map(col => `<td style="width: ${dataColumnWidth}%;" title="${this.escapeHtml(String(record[col] || ''))}">${this.formatCellValue(record[col], col)}</td>`).join('')}
                <td class="table-actions" style="width: ${actionColumnWidth}%;">
                    <button class="action-btn edit-btn" onclick="memoryNexus.editRecord(${index})" title="编辑记录">编辑</button>
                    <button class="action-btn delete-btn" onclick="memoryNexus.confirmDelete(${index})" title="删除记录">删除</button>
                </td>
            </tr>
        `).join('');

        // 更新批量操作按钮状态
        this.updateBatchButtons();
    }

    /**
     * 切换全选状态
     */
    toggleSelectAll(checkbox) {
        const recordCheckboxes = document.querySelectorAll('.record-checkbox');
        recordCheckboxes.forEach(cb => {
            cb.checked = checkbox.checked;
        });
        this.updateBatchButtons();
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchButtons() {
        const checkedBoxes = document.querySelectorAll('.record-checkbox:checked');
        const batchDeleteBtn = document.getElementById('batch-delete');
        const deleteSearchBtn = document.getElementById('delete-search-results');
        const deleteFilterBtn = document.getElementById('delete-filter-results');

        // 更新批量删除按钮
        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = checkedBoxes.length === 0;
            batchDeleteBtn.textContent = checkedBoxes.length > 0 ?
                `删除选中 (${checkedBoxes.length})` : '批量删除';
        }

        // 更新删除搜索结果按钮
        if (deleteSearchBtn) {
            deleteSearchBtn.style.display = this.searchQuery && this.searchQuery.trim() !== '' ? 'inline-block' : 'none';
        }

        // 更新删除筛选结果按钮
        if (deleteFilterBtn) {
            const hasFilters = this.filterUserId || this.filterPersonaName ||
                              this.filterDateRange.start || this.filterDateRange.end;
            deleteFilterBtn.style.display = hasFilters && this.tableData.length > 0 ? 'inline-block' : 'none';
        }

        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('select-all');
        const allCheckboxes = document.querySelectorAll('.record-checkbox');
        if (selectAllCheckbox && allCheckboxes.length > 0) {
            selectAllCheckbox.checked = checkedBoxes.length === allCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < allCheckboxes.length;
        }
    }

    /**
     * 格式化列名 - 使用中文映射
     */
    formatColumnName(columnName) {
        // 使用中文字段映射
        return this.fieldMappings[columnName] || columnName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    /**
     * 转义HTML字符
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 根据列名获取最大显示长度
     */
    getMaxLengthForColumn(columnName) {
        // 根据不同列类型设置不同的最大长度 - 由于列宽增加，可以显示更多内容
        const lengthMap = {
            'id': 10,
            'user_id': 18,
            'persona_name': 25,
            'content': 50,
            'summary': 45,
            'concept_name': 30,
            'meaning': 45,
            'emotion_type': 18,
            'timestamp': 25,
            'created_at': 25,
            'updated_at': 25,
            'embedding_vector': 15,
            'confidence': 10,
            'intensity': 10,
            'trend': 10
        };

        return lengthMap[columnName] || 35; // 默认35个字符
    }

    /**
     * 格式化单元格值
     */
    formatCellValue(value, columnName) {
        if (value === null || value === undefined) {
            return '<span style="color: var(--text-muted);">空</span>';
        }

        // 时间戳格式化
        if (columnName.includes('time') || columnName === 'timestamp') {
            try {
                const date = new Date(value);
                return `<span style="color: var(--neural-blue);">${date.toLocaleString('zh-CN')}</span>`;
            } catch (e) {
                return value;
            }
        }

        // 好感度值格式化
        if (columnName === 'affinity_value') {
            const numValue = parseFloat(value);
            let color = 'var(--text-muted)';
            let emoji = '😐';
            if (numValue > 50) {
                color = 'var(--success-color)';
                emoji = '😊';
            } else if (numValue > 0) {
                color = 'var(--warning-color)';
                emoji = '🙂';
            } else if (numValue < -50) {
                color = 'var(--error-color)';
                emoji = '😠';
            } else if (numValue < 0) {
                color = 'var(--warning-color)';
                emoji = '😕';
            }
            return `<span style="color: ${color};">${emoji} ${numValue}</span>`;
        }

        // 置信度格式化
        if (columnName === 'confidence') {
            const numValue = parseFloat(value);
            const percentage = (numValue * 100).toFixed(1);
            let color = numValue > 0.8 ? 'var(--success-color)' : numValue > 0.5 ? 'var(--warning-color)' : 'var(--error-color)';
            return `<span style="color: ${color};">${percentage}%</span>`;
        }

        // 情绪强度格式化
        if (columnName === 'intensity') {
            const numValue = parseFloat(value);
            let color = numValue > 0.7 ? 'var(--error-color)' : numValue > 0.4 ? 'var(--warning-color)' : 'var(--success-color)';
            return `<span style="color: ${color};">${(numValue * 100).toFixed(0)}%</span>`;
        }

        // 趋势格式化
        if (columnName === 'trend') {
            const trendMap = {
                'increasing': '<span style="color: var(--success-color);">📈 上升</span>',
                'decreasing': '<span style="color: var(--error-color);">📉 下降</span>',
                'stable': '<span style="color: var(--neural-blue);">➡️ 稳定</span>'
            };
            return trendMap[value] || value;
        }

        // 向量嵌入数据格式化
        if (columnName === 'embedding_vector' || columnName.includes('vector')) {
            if (typeof value === 'string' && value.startsWith('[')) {
                try {
                    const vector = JSON.parse(value);
                    if (Array.isArray(vector) && vector.length > 0) {
                        return `<span style="color: var(--plasma-purple); cursor: pointer;" title="向量维度: ${vector.length}&#10;前3个值: ${vector.slice(0, 3).map(v => v.toFixed(4)).join(', ')}...">🔢 向量[${vector.length}维]</span>`;
                    }
                } catch (e) {
                    return `<span style="color: var(--warning-color);">⚠️ 向量数据</span>`;
                }
            }
        }

        // JSON数据格式化 - 优化展示效果
        if (typeof value === 'object' || (typeof value === 'string' && (value.startsWith('{') || value.startsWith('[')))) {
            try {
                const obj = typeof value === 'string' ? JSON.parse(value) : value;

                // 处理数组类型
                if (Array.isArray(obj)) {
                    if (obj.length === 0) {
                        return `<span style="color: var(--text-muted);">[]</span>`;
                    }

                    // 如果是字符串数组，显示前几个元素
                    if (obj.every(item => typeof item === 'string')) {
                        const preview = obj.slice(0, 2).map(item => `"${item}"`).join(', ');
                        const fullText = JSON.stringify(obj, null, 2);
                        return `<span style="color: var(--neural-blue); cursor: pointer;" title="${this.escapeHtml(fullText)}">[${this.escapeHtml(preview)}${obj.length > 2 ? `, +${obj.length - 2}项` : ''}]</span>`;
                    }

                    // 如果是对象数组，显示数量和类型
                    const firstItem = obj[0];
                    if (typeof firstItem === 'object' && firstItem !== null) {
                        const keys = Object.keys(firstItem);
                        const keyPreview = keys.slice(0, 2).join(', ');
                        const fullText = JSON.stringify(obj, null, 2);
                        return `<span style="color: var(--neural-blue); cursor: pointer;" title="${this.escapeHtml(fullText)}">[${obj.length}个对象] {${this.escapeHtml(keyPreview)}${keys.length > 2 ? '...' : ''}}</span>`;
                    }

                    // 数值数组
                    if (obj.every(item => typeof item === 'number')) {
                        const preview = obj.slice(0, 3).join(', ');
                        const fullText = JSON.stringify(obj, null, 2);
                        return `<span style="color: var(--neural-blue); cursor: pointer;" title="${this.escapeHtml(fullText)}">[${this.escapeHtml(preview)}${obj.length > 3 ? `, +${obj.length - 3}项` : ''}]</span>`;
                    }

                    // 其他类型数组
                    const preview = obj.slice(0, 2).map(item => String(item).substring(0, 10)).join(', ');
                    const fullText = JSON.stringify(obj, null, 2);
                    return `<span style="color: var(--neural-blue); cursor: pointer;" title="${this.escapeHtml(fullText)}">[${obj.length}项] ${this.escapeHtml(preview)}${obj.length > 2 ? '...' : ''}</span>`;
                }

                // 处理普通对象
                const keys = Object.keys(obj);
                if (keys.length === 0) {
                    return `<span style="color: var(--text-muted);">{}</span>`;
                }

                // 显示对象的主要键值对，更智能的预览
                const preview = keys.slice(0, 2).map(key => {
                    const val = obj[key];
                    let displayVal;
                    if (typeof val === 'string') {
                        displayVal = val.length > 20 ? `"${val.substring(0, 20)}..."` : `"${val}"`;
                    } else if (typeof val === 'number') {
                        displayVal = val;
                    } else if (typeof val === 'boolean') {
                        displayVal = val;
                    } else if (val === null) {
                        displayVal = 'null';
                    } else {
                        displayVal = typeof val === 'object' ? '{...}' : String(val).substring(0, 15);
                    }
                    return `${key}: ${displayVal}`;
                }).join(', ');

                const fullText = JSON.stringify(obj, null, 2);
                return `<span style="color: var(--neural-blue); cursor: pointer;" title="${this.escapeHtml(fullText)}">{${this.escapeHtml(preview)}${keys.length > 2 ? `, +${keys.length - 2}项` : ''}}</span>`;
            } catch (e) {
                // 继续处理为普通字符串
            }
        }

        // 处理特殊字符和括号
        if (typeof value === 'string') {
            // 转义HTML特殊字符
            value = this.escapeHtml(value);

            // 长文本截断 - 根据列宽动态调整
            const maxLength = this.getMaxLengthForColumn(columnName);
            if (value.length > maxLength) {
                return `<span title="${value}" style="cursor: help; display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${value.substring(0, maxLength)}...</span>`;
            }
        }

        return value;
    }

    /**
     * 转义HTML特殊字符
     */
    escapeHtml(text) {
        if (typeof text !== 'string') return text;

        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 更新分页控制
     */
    updatePagination() {
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        const pageNumbers = document.getElementById('page-numbers');

        // 更新按钮状态
        prevBtn.disabled = this.currentPage <= 1;
        nextBtn.disabled = this.currentPage >= this.totalPages;

        // 生成页码
        pageNumbers.innerHTML = '';

        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-number ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.currentPage = i;
                this.loadTableData();
            });
            pageNumbers.appendChild(pageBtn);
        }
    }

    /**
     * 更新表格统计信息
     */
    updateTableStats() {
        document.getElementById('current-page').textContent = this.currentPage;
        document.getElementById('total-pages').textContent = this.totalPages;
        document.getElementById('filtered-count').textContent = this.totalRecords;
    }

    /**
     * 更新数据统计显示
     */
    updateDataStats() {
        const currentCountElement = document.getElementById('current-count');
        const totalCountElement = document.getElementById('total-count');
        const filteredCountElement = document.getElementById('filtered-count');
        const searchCountElement = document.getElementById('search-count');
        const filterStatsElement = document.getElementById('filter-stats');
        const searchStatsElement = document.getElementById('search-stats');

        // 更新当前显示数量
        if (currentCountElement) {
            currentCountElement.textContent = this.tableData.length;
        }

        // 更新总数量
        if (totalCountElement) {
            totalCountElement.textContent = this.totalRecords;
        }

        // 检查是否有筛选条件
        const hasFilters = this.filterUserId || this.filterPersonaName ||
                          this.filterDateRange.start || this.filterDateRange.end;

        // 检查是否有搜索条件
        const hasSearch = this.searchQuery && this.searchQuery.trim() !== '';

        // 显示/隐藏筛选统计
        if (filterStatsElement && filteredCountElement) {
            if (hasFilters && !hasSearch) {
                filterStatsElement.style.display = 'flex';
                filteredCountElement.textContent = this.tableData.length;
            } else {
                filterStatsElement.style.display = 'none';
            }
        }

        // 显示/隐藏搜索统计
        if (searchStatsElement && searchCountElement) {
            if (hasSearch) {
                searchStatsElement.style.display = 'flex';
                searchCountElement.textContent = this.tableData.length;
            } else {
                searchStatsElement.style.display = 'none';
            }
        }
    }

    /**
     * 执行搜索
     */
    async performSearch() {
        this.searchQuery = document.getElementById('search-input').value.trim();
        this.currentPage = 1;
        await this.loadTableData();
    }

    /**
     * 编辑记录
     */
    editRecord(index) {
        const record = this.tableData[index];
        this.showEditModal(record);
    }

    /**
     * 确认删除
     */
    confirmDelete(index) {
        const record = this.tableData[index];
        if (confirm(`确定要删除这条记录吗？\n\nID: ${record.id}`)) {
            this.deleteRecordById(record.id);
        }
    }

    /**
     * 显示编辑模态框
     */
    showEditModal(record = null) {
        const modal = document.getElementById('edit-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('edit-form');
        const deleteBtn = document.getElementById('delete-record');

        // 设置标题
        modalTitle.textContent = record ? '编辑记录' : '新增记录';

        // 显示/隐藏删除按钮
        deleteBtn.style.display = record ? 'block' : 'none';

        // 生成表单
        this.generateForm(form, record);

        // 显示模态框
        modal.classList.add('show');
    }

    /**
     * 显示新增模态框
     */
    showAddModal() {
        this.showEditModal();
    }

    /**
     * 隐藏模态框
     */
    hideModal() {
        const modal = document.getElementById('edit-modal');
        modal.classList.remove('show');
    }

    /**
     * 生成表单
     */
    generateForm(form, record) {
        if (!this.currentTable || this.tableData.length === 0) {
            form.innerHTML = '<p>无法生成表单：没有可用的表结构信息</p>';
            return;
        }

        const firstRecord = this.tableData[0];
        const columns = Object.keys(firstRecord).filter(col => col !== 'id'); // 排除ID字段

        form.innerHTML = columns.map(col => {
            const value = record ? (record[col] || '') : '';
            const isTextarea = col.includes('content') || col.includes('vector') || col.includes('state');

            return `
                <div class="form-group">
                    <label class="form-label" for="field-${col}">${this.formatColumnName(col)}</label>
                    ${isTextarea ?
                        `<textarea class="form-input form-textarea" id="field-${col}" name="${col}">${value}</textarea>` :
                        `<input class="form-input" type="text" id="field-${col}" name="${col}" value="${value}">`
                    }
                </div>
            `;
        }).join('');

        // 存储当前编辑的记录
        this.currentEditRecord = record;
    }

    /**
     * 保存记录
     */
    async saveRecord() {
        const form = document.getElementById('edit-form');
        const formData = new FormData(form);
        const data = {};

        // 收集表单数据
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        try {
            this.showLoading(true);

            let response;
            if (this.currentEditRecord) {
                // 更新记录
                data.id = this.currentEditRecord.id;
                response = await this.apiRequest(`/table/${this.currentTable}/${data.id}`, {
                    method: 'PUT',
                    body: JSON.stringify(data)
                });
            } else {
                // 新增记录
                response = await this.apiRequest(`/table/${this.currentTable}`, {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
            }

            if (response.success) {
                this.showNotification(
                    this.currentEditRecord ? '记录更新成功' : '记录创建成功',
                    'success'
                );
                this.hideModal();
                await this.loadTableData();
            }
        } catch (error) {
            this.showNotification('保存失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 删除记录
     */
    async deleteRecord() {
        if (!this.currentEditRecord) return;

        if (!confirm('确定要删除这条记录吗？此操作不可撤销！')) {
            return;
        }

        await this.deleteRecordById(this.currentEditRecord.id);
        this.hideModal();
    }

    /**
     * 根据ID删除记录
     */
    async deleteRecordById(id) {
        try {
            this.showLoading(true);

            const response = await this.apiRequest(`/table/${this.currentTable}/${id}`, {
                method: 'DELETE'
            });

            if (response.success) {
                this.showNotification('记录删除成功', 'success');
                await this.loadTableData();
                // 重新加载数据库信息以更新总数
                await this.loadDatabaseInfo();
            }
        } catch (error) {
            this.showNotification('删除失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 批量删除选中的记录
     */
    async batchDeleteRecords() {
        const checkboxes = document.querySelectorAll('.record-checkbox:checked');
        if (checkboxes.length === 0) {
            this.showNotification('请先选择要删除的记录', 'warning');
            return;
        }

        const selectedIds = Array.from(checkboxes).map(cb => cb.dataset.id);
        const tableName = this.getTableDisplayName(this.currentTable);

        if (!confirm(`确定要删除选中的 ${selectedIds.length} 条记录吗？\n\n板块：${tableName}\n\n此操作不可撤销！`)) {
            return;
        }

        try {
            this.showLoading(true);
            let successCount = 0;
            let failCount = 0;

            for (const id of selectedIds) {
                try {
                    const response = await this.apiRequest(`/table/${this.currentTable}/${id}`, {
                        method: 'DELETE'
                    });
                    if (response.success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                    console.error(`删除记录 ${id} 失败:`, error);
                }
            }

            if (successCount > 0) {
                this.showNotification(`成功删除 ${successCount} 条记录${failCount > 0 ? `，失败 ${failCount} 条` : ''}`,
                    failCount > 0 ? 'warning' : 'success');
                await this.loadTableData();
                // 重新加载数据库信息以更新总数
                await this.loadDatabaseInfo();
            } else {
                this.showNotification('批量删除失败', 'error');
            }
        } catch (error) {
            this.showNotification('批量删除操作失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 清空整个表格
     */
    async clearTable() {
        if (!this.currentTable) {
            this.showNotification('请先选择一个表格', 'warning');
            return;
        }

        const tableName = this.getTableDisplayName(this.currentTable);
        const totalRecords = this.totalRecords;

        // 简化确认流程，只需要一次确认
        if (!confirm(`⚠️ 危险操作警告 ⚠️\n\n确定要清空整个「${tableName}」板块吗？\n\n这将删除该板块中的所有 ${totalRecords} 条记录！\n\n此操作不可撤销，请谨慎操作！`)) {
            return;
        }

        try {
            this.showLoading(true);

            // 修复API路径，使用新的清空路由
            const response = await this.apiRequest(`/clear-table/${this.currentTable}`, {
                method: 'DELETE'
            });

            if (response.success) {
                this.showNotification(`「${tableName}」板块已清空，共删除 ${response.data.affectedRows || totalRecords} 条记录`, 'success');
                await this.loadTableData();
                // 重新加载数据库信息以更新总数
                await this.loadDatabaseInfo();
            }
        } catch (error) {
            this.showNotification('清空表格失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 删除当前搜索结果
     */
    async deleteSearchResults() {
        if (!this.searchQuery || this.searchQuery.trim() === '') {
            this.showNotification('当前没有搜索结果', 'warning');
            return;
        }

        if (this.tableData.length === 0) {
            this.showNotification('当前搜索结果为空', 'warning');
            return;
        }

        const tableName = this.getTableDisplayName(this.currentTable);
        const resultCount = this.tableData.length;

        if (!confirm(`确定要删除当前搜索结果吗？\n\n搜索关键词：「${this.searchQuery}」\n板块：${tableName}\n匹配记录：${resultCount} 条\n\n此操作不可撤销！`)) {
            return;
        }

        try {
            this.showLoading(true);
            const recordIds = this.tableData.map(record => record.id);
            let successCount = 0;
            let failCount = 0;

            for (const id of recordIds) {
                try {
                    const response = await this.apiRequest(`/table/${this.currentTable}/${id}`, {
                        method: 'DELETE'
                    });
                    if (response.success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                    console.error(`删除搜索结果记录 ${id} 失败:`, error);
                }
            }

            if (successCount > 0) {
                this.showNotification(`搜索结果删除完成：成功 ${successCount} 条${failCount > 0 ? `，失败 ${failCount} 条` : ''}`,
                    failCount > 0 ? 'warning' : 'success');
                // 清空搜索并重新加载
                this.searchQuery = '';
                document.getElementById('search-input').value = '';
                await this.loadTableData();
                // 重新加载数据库信息以更新总数
                await this.loadDatabaseInfo();
            } else {
                this.showNotification('删除搜索结果失败', 'error');
            }
        } catch (error) {
            this.showNotification('删除搜索结果失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 删除当前筛选结果
     */
    async deleteFilterResults() {
        const hasFilters = this.filterUserId || this.filterPersonaName ||
                          this.filterDateRange.start || this.filterDateRange.end;

        if (!hasFilters) {
            this.showNotification('当前没有筛选条件', 'warning');
            return;
        }

        if (this.tableData.length === 0) {
            this.showNotification('当前筛选结果为空', 'warning');
            return;
        }

        const tableName = this.getTableDisplayName(this.currentTable);
        const resultCount = this.tableData.length;

        // 构建筛选条件描述
        let filterDescription = [];
        if (this.filterUserId) filterDescription.push(`用户ID: ${this.filterUserId}`);
        if (this.filterPersonaName) filterDescription.push(`角色: ${this.filterPersonaName}`);
        if (this.filterDateRange.start) filterDescription.push(`开始时间: ${this.filterDateRange.start}`);
        if (this.filterDateRange.end) filterDescription.push(`结束时间: ${this.filterDateRange.end}`);

        if (!confirm(`确定要删除当前筛选结果吗？\n\n筛选条件：\n${filterDescription.join('\n')}\n\n板块：${tableName}\n匹配记录：${resultCount} 条\n\n此操作不可撤销！`)) {
            return;
        }

        try {
            this.showLoading(true);
            const recordIds = this.tableData.map(record => record.id);
            let successCount = 0;
            let failCount = 0;

            for (const id of recordIds) {
                try {
                    const response = await this.apiRequest(`/table/${this.currentTable}/${id}`, {
                        method: 'DELETE'
                    });
                    if (response.success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                    console.error(`删除筛选结果记录 ${id} 失败:`, error);
                }
            }

            if (successCount > 0) {
                this.showNotification(`筛选结果删除完成：成功 ${successCount} 条${failCount > 0 ? `，失败 ${failCount} 条` : ''}`,
                    failCount > 0 ? 'warning' : 'success');
                // 重新加载数据库信息以更新总数
                await this.loadDatabaseInfo();
                // 清空筛选并重新加载
                await this.clearAllFilters();
            } else {
                this.showNotification('删除筛选结果失败', 'error');
            }
        } catch (error) {
            this.showNotification('删除筛选结果操作失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 获取表格的中文显示名称
     */
    getTableDisplayName(tableName) {
        return this.tableConfig[tableName]?.name || tableName;
    }

    /**
     * 按列排序
     */
    async sortByColumn(columnName) {
        console.log(`🔄 排序列: ${columnName}, 当前排序: ${this.sortColumn} ${this.sortDirection}`);

        if (this.sortColumn === columnName) {
            // 如果点击的是当前排序列，切换排序方向
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果点击的是新列，设置为降序（最新的在前面）
            this.sortColumn = columnName;
            this.sortDirection = 'desc';
        }

        console.log(`🔄 新排序设置: ${this.sortColumn} ${this.sortDirection}`);
        this.currentPage = 1; // 重置到第一页
        await this.loadTableData();
    }

    /**
     * 获取排序图标
     */
    getSortIcon(columnName) {
        if (this.sortColumn !== columnName) {
            return '<span class="sort-icon">⇅</span>';
        }
        return this.sortDirection === 'asc' ?
            '<span class="sort-icon active">↑</span>' :
            '<span class="sort-icon active">↓</span>';
    }

    /**
     * 应用筛选条件
     */
    async applyFilters() {
        const userIdInput = document.getElementById('filter-user-id');
        const personaNameInput = document.getElementById('filter-persona-name');
        const dateStartInput = document.getElementById('filter-date-start');
        const dateEndInput = document.getElementById('filter-date-end');

        this.filterUserId = userIdInput.value.trim();
        this.filterPersonaName = personaNameInput.value.trim();
        this.filterDateRange = {
            start: dateStartInput.value,
            end: dateEndInput.value
        };

        console.log(`🔍 应用筛选条件:`, {
            userId: this.filterUserId,
            personaName: this.filterPersonaName,
            dateRange: this.filterDateRange
        });

        this.currentPage = 1;
        await this.loadTableData();

        // 更新筛选状态显示
        this.updateFilterStatus();
    }

    /**
     * 清除单个筛选条件
     */
    async clearSingleFilter(filterType) {
        console.log(`🗑️ 清除单个筛选: ${filterType}`);

        // 获取筛选类型的中文名称
        const filterNames = {
            'user-id': '用户ID',
            'persona-name': '角色名称',
            'date-start': '开始时间',
            'date-end': '结束时间'
        };

        const filterName = filterNames[filterType] || filterType;

        switch (filterType) {
            case 'user-id':
                document.getElementById('filter-user-id').value = '';
                this.filterUserId = '';
                break;
            case 'persona-name':
                document.getElementById('filter-persona-name').value = '';
                this.filterPersonaName = '';
                break;
            case 'date-start':
                document.getElementById('filter-date-start').value = '';
                this.filterDateRange.start = '';
                break;
            case 'date-end':
                document.getElementById('filter-date-end').value = '';
                this.filterDateRange.end = '';
                break;
        }

        // 自动应用筛选
        await this.applyFilters();
        this.showNotification(`已清除「${filterName}」筛选条件`, 'success');
    }

    /**
     * 更新筛选状态显示
     */
    updateFilterStatus() {
        const hasFilters = this.filterUserId || this.filterPersonaName ||
                          this.filterDateRange.start || this.filterDateRange.end;

        const applyBtn = document.getElementById('apply-filters');
        const clearBtn = document.getElementById('clear-filters');

        if (hasFilters) {
            applyBtn.style.background = 'var(--success-green)';
            applyBtn.innerHTML = '✅ 筛选已应用';
            clearBtn.style.display = 'flex';
            this.showFilterStatus();
        } else {
            applyBtn.style.background = 'var(--neural-blue)';
            applyBtn.innerHTML = '🔍 应用筛选';
            clearBtn.style.display = 'flex';
            this.hideFilterStatus();
        }
    }

    /**
     * 显示筛选状态
     */
    showFilterStatus() {
        const filterStatusDiv = document.getElementById('filter-status');
        const filterStatusContent = document.getElementById('filter-status-content');

        if (!filterStatusDiv || !filterStatusContent) return;

        // 构建筛选标签
        let filterTags = [];

        if (this.filterUserId) {
            filterTags.push(this.createFilterTag('用户ID', this.filterUserId, 'user-id'));
        }

        if (this.filterPersonaName) {
            filterTags.push(this.createFilterTag('角色名称', this.filterPersonaName, 'persona-name'));
        }

        if (this.filterDateRange.start) {
            filterTags.push(this.createFilterTag('开始时间', this.filterDateRange.start, 'date-start'));
        }

        if (this.filterDateRange.end) {
            filterTags.push(this.createFilterTag('结束时间', this.filterDateRange.end, 'date-end'));
        }

        if (filterTags.length > 0) {
            filterStatusContent.innerHTML = filterTags.join('');
            filterStatusDiv.style.display = 'block';
        } else {
            this.hideFilterStatus();
        }
    }

    /**
     * 创建筛选标签
     */
    createFilterTag(label, value, type) {
        return `
            <div class="filter-tag">
                <span class="filter-tag-label">${label}:</span>
                <span class="filter-tag-value">${value}</span>
                <button class="filter-tag-remove" onclick="memoryNexus.clearSingleFilter('${type}')" title="移除此筛选条件">×</button>
            </div>
        `;
    }

    /**
     * 隐藏筛选状态
     */
    hideFilterStatus() {
        const filterStatusDiv = document.getElementById('filter-status');
        if (filterStatusDiv) {
            filterStatusDiv.style.display = 'none';
        }
    }

    /**
     * 清除所有筛选
     */
    async clearAllFilters() {
        console.log('🗑️ 清除所有筛选条件');

        // 检查是否有筛选条件
        const hasFilters = this.filterUserId || this.filterPersonaName ||
                          this.filterDateRange.start || this.filterDateRange.end ||
                          this.sortColumn;

        if (!hasFilters) {
            this.showNotification('当前没有筛选条件需要清除', 'info');
            return;
        }

        this.filterUserId = '';
        this.filterPersonaName = '';
        this.filterDateRange = { start: '', end: '' };
        this.sortColumn = null;
        this.sortDirection = 'desc';
        this.currentPage = 1;

        // 清空筛选控件
        const userIdFilter = document.getElementById('filter-user-id');
        const personaFilter = document.getElementById('filter-persona-name');
        const dateStartFilter = document.getElementById('filter-date-start');
        const dateEndFilter = document.getElementById('filter-date-end');

        if (userIdFilter) userIdFilter.value = '';
        if (personaFilter) personaFilter.value = '';
        if (dateStartFilter) dateStartFilter.value = '';
        if (dateEndFilter) dateEndFilter.value = '';

        await this.loadTableData();
        this.updateFilterStatus();
        this.hideFilterStatus();

        this.showNotification('已清除所有筛选条件', 'success');
    }

    /**
     * 初始化系统监控
     */
    initSystemMonitor() {
        console.log('🖥️ 初始化系统监控...');

        // 立即获取一次数据
        this.updateSystemMonitor();

        // 设置定时更新（每5分钟）
        this.systemMonitorInterval = setInterval(() => {
            this.updateSystemMonitor();
        }, 300000); // 5分钟 = 300000毫秒
    }

    /**
     * 更新系统监控数据
     */
    async updateSystemMonitor() {
        try {
            const response = await fetch('/admin_api/monitor/realtime', {
                headers: {
                    'Authorization': this.authHeader
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.displaySystemMonitor(result.data);
            }
        } catch (error) {
            console.error('❌ 获取系统监控数据失败:', error);
            // 显示错误状态
            this.displaySystemMonitorError();
        }
    }

    /**
     * 显示系统监控数据
     */
    displaySystemMonitor(data) {
        // CPU使用率
        const cpuUsage = parseFloat(data.cpu.usage) || 0;
        document.getElementById('cpu-usage').textContent = `${cpuUsage.toFixed(1)}%`;
        document.getElementById('cpu-bar').style.width = `${Math.min(cpuUsage, 100)}%`;

        // GPU使用率
        const gpuUsage = parseFloat(data.gpu?.usage) || 0;
        document.getElementById('gpu-usage').textContent = `${gpuUsage.toFixed(1)}%`;
        document.getElementById('gpu-bar').style.width = `${Math.min(gpuUsage, 100)}%`;

        // 系统内存使用率
        const memoryUsage = parseFloat(data.memory.system.usagePercent) || 0;
        document.getElementById('memory-usage').textContent = `${memoryUsage}%`;
        document.getElementById('memory-bar').style.width = `${Math.min(memoryUsage, 100)}%`;

        // GPU显存使用率
        const gpuMemoryUsage = parseFloat(data.gpu?.memory?.usagePercent) || 0;
        const gpuMemoryText = data.gpu?.memory?.usedFormatted || '0MB';
        document.getElementById('gpu-memory').textContent = gpuMemoryText;
        document.getElementById('gpu-memory-bar').style.width = `${Math.min(gpuMemoryUsage, 100)}%`;

        // 运行时间
        const uptime = data.system.processUptimeFormatted || '0s';
        document.getElementById('uptime').textContent = uptime;

        // GPU型号
        const gpuName = data.gpu?.name || '未检测到显卡';
        const displayName = gpuName.length > 20 ? gpuName.substring(0, 20) + '...' : gpuName;
        document.getElementById('gpu-name').textContent = displayName;
        document.getElementById('gpu-name').title = gpuName;

        // 根据使用率调整颜色
        this.updateMonitorColors('cpu-bar', cpuUsage);
        this.updateMonitorColors('gpu-bar', gpuUsage);
        this.updateMonitorColors('memory-bar', memoryUsage);
        this.updateMonitorColors('gpu-memory-bar', gpuMemoryUsage);
    }

    /**
     * 更新监控条颜色
     */
    updateMonitorColors(elementId, percentage) {
        const element = document.getElementById(elementId);
        if (!element) return;

        // 移除现有的颜色类
        element.classList.remove('monitor-normal', 'monitor-warning', 'monitor-danger');

        // 根据百分比添加颜色类
        if (percentage < 60) {
            element.classList.add('monitor-normal');
        } else if (percentage < 80) {
            element.classList.add('monitor-warning');
        } else {
            element.classList.add('monitor-danger');
        }
    }

    /**
     * 显示系统监控错误状态
     */
    displaySystemMonitorError() {
        document.getElementById('cpu-usage').textContent = 'N/A';
        document.getElementById('gpu-usage').textContent = 'N/A';
        document.getElementById('memory-usage').textContent = 'N/A';
        document.getElementById('gpu-memory').textContent = 'N/A';
        document.getElementById('uptime').textContent = 'N/A';
        document.getElementById('gpu-name').textContent = '检测失败';

        // 重置进度条
        ['cpu-bar', 'gpu-bar', 'memory-bar', 'gpu-memory-bar'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.width = '0%';
                element.classList.remove('monitor-normal', 'monitor-warning', 'monitor-danger');
            }
        });
    }

    /**
     * 销毁系统监控
     */
    destroySystemMonitor() {
        if (this.systemMonitorInterval) {
            clearInterval(this.systemMonitorInterval);
            this.systemMonitorInterval = null;
        }
    }



    /**
     * 显示通知消息
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                <span class="notification-message">${message}</span>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.add('notification-show');
        }, 100);

        // 自动移除
        setTimeout(() => {
            notification.classList.remove('notification-show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// 全局实例
let memoryNexus;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    memoryNexus = new MemoryNexus();

    // 添加全局调试函数
    window.testSort = function(column) {
        console.log('🧪 测试排序功能:', column);
        if (memoryNexus) {
            memoryNexus.sortByColumn(column);
        } else {
            console.error('❌ memoryNexus 未初始化');
        }
    };

    window.testFilter = function(userId, personaName) {
        console.log('🧪 测试筛选功能:', userId, personaName);
        if (memoryNexus) {
            if (userId) memoryNexus.filterByUserId(userId);
            if (personaName) memoryNexus.filterByPersonaName(personaName);
        } else {
            console.error('❌ memoryNexus 未初始化');
        }
    };
});
