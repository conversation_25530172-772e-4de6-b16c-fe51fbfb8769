<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的博客</title>
    <style>
        /* 极简样式，保证结构可读 */
        body { font-family: sans-serif; line-height: 1.6; margin: 0; background-color: #fdfdfd; }
        .container { max-width: 800px; margin: 20px auto; padding: 0 20px; }
        header { border-bottom: 1px solid #eee; padding-bottom: 10px; }
        article { margin-bottom: 40px; }
        footer { text-align: center; font-size: 0.8em; color: #aaa; margin-top: 40px; }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>雨安安的角落</h1>
        </header>

        <main>
            <article>
                <h2>第一篇文章</h2>
                <p>这是文章的摘要内容。逻辑应该保持干净，不应有过多的样式干扰。安静不是屏蔽世界，而是给世界一个干净的入口。</p>
            </article>

            <article>
                <h2>关于代码和人</h2>
                <p>人和代码一样，不能总运行在高负载上。需要 sleep()。</p>
            </article>
        </main>

        <footer>
            <p>&copy; 2024. All rights reserved.</p>
        </footer>
    </div>
</body>
</html>