{"world": {"name": "VCPToolBox沙盒世界", "description": "一个充满活力的虚拟社会生态系统", "maxAgents": 50, "timeSpeed": 1, "autoSave": true, "saveInterval": 300000}, "environment": {"updateFrequency": 60000, "weatherChangeChance": 0.1, "seasonalEffects": true, "resourceGeneration": {"energy": 10, "food": 8, "materials": 5, "knowledge": 3}}, "agents": {"updateFrequency": 30000, "maxLevel": 100, "needDecayRates": {"hunger": 0.5, "energy": 0.3, "social": 0.2, "entertainment": 0.4, "achievement": 0.1}, "criticalThresholds": {"hunger": 20, "energy": 15, "social": 25, "entertainment": 30, "achievement": 10}, "personalityChangeRates": {"extroversion": 0.01, "agreeableness": 0.015, "conscientiousness": 0.008, "neuroticism": 0.012, "openness": 0.01}}, "socialNetwork": {"updateFrequency": 60000, "relationshipDecayRate": 0.1, "maxRelationshipHistory": 50, "influenceCalculation": {"enabled": true, "weightByRelationshipType": true}, "groupDetection": {"enabled": true, "minGroupSize": 3, "strengthThreshold": 70}}, "dialogue": {"updateFrequency": 45000, "triggers": {"needBased": true, "moodBased": true, "relationshipBased": true, "environmentBased": true, "eventBased": true}, "conversationLimits": {"maxMessages": 10, "maxDuration": 300000, "responseDelay": {"min": 1000, "max": 5000}}, "personalityInfluence": {"extroversion": {"conversationChance": 0.3, "responseLength": 1.2}, "agreeableness": {"friendlyResponse": 0.7, "helpfulness": 0.8}}}, "events": {"updateFrequency": 120000, "randomEventChance": 0.1, "eventDurations": {"short": 60000, "medium": 300000, "long": 1800000, "extended": 86400000}, "categories": {"social": {"enabled": true, "frequency": 0.4}, "environmental": {"enabled": true, "frequency": 0.3}, "personal": {"enabled": true, "frequency": 0.2}, "system": {"enabled": true, "frequency": 0.1}}}, "integration": {"memorySystem": {"enabled": true, "syncInterval": 60000, "maxMemories": 1000}, "psychologySystem": {"enabled": true, "emotionSync": true, "stressSync": true, "affinitySync": true}, "oneBot11": {"enabled": true, "reportEvents": true, "allowUserInteraction": true, "notificationChannel": null}}, "ui": {"webInterface": {"enabled": true, "port": 8080, "updateInterval": 5000, "theme": "dark"}, "realTimeUpdates": {"enabled": true, "websocket": true, "compression": true}, "statistics": {"enabled": true, "detailLevel": "full", "historyLength": 1000}}, "logging": {"level": "info", "enableFileLogging": true, "logRotation": true, "maxLogSize": "10MB", "categories": {"world": true, "agents": true, "social": true, "dialogue": true, "events": true, "environment": true}}, "performance": {"maxConcurrentOperations": 10, "batchProcessing": true, "caching": {"enabled": true, "ttl": 300000}, "optimization": {"autoGarbageCollection": true, "memoryThreshold": "500MB"}}, "experimental": {"aiDrivenEvents": {"enabled": false, "aiModel": "gpt-3.5-turbo", "creativity": 0.7}, "emergentBehavior": {"enabled": true, "complexityLevel": "medium"}, "crossWorldInteraction": {"enabled": false, "allowVisitors": false}}}