# 聊天上下文系统迁移指南

## 概述

本次更新完全重构了聊天上下文参数系统，删除了旧的 `group` 参数兼容性，改为使用统一的 `chat_context` 参数，并根据微信适配器监听的对象自动处理群聊和私聊。

## 主要变更

### 1. 删除旧格式支持

**已删除的参数格式：**
```json
{
  "group": "群名",
  "group_history_count": 10
}
```

**新的统一格式：**
```json
{
  "chat_context": {
    "type": "private" | "group",
    "name": "聊天名称",
    "history_count": 10,
    "user_names": {
      "user_id": "显示名称"
    }
  }
}
```

### 2. 微信适配器自动处理

微信适配器现在会根据消息类型自动构建聊天上下文：

#### 私聊处理
- **自动检测**：根据 `message_type == 'private'` 判断
- **上下文构建**：自动生成 `"{assistantName}和{user_name}的私聊"` 格式
- **历史记录**：使用 `private_history_count` 配置（默认5条）
- **处理方式**：转换为 role-based 消息对象

#### 群聊处理
- **自动检测**：根据 `message_type == 'group'` 判断
- **上下文构建**：使用群名作为聊天名称
- **历史记录**：使用 `group_history_count` 配置（默认10条）
- **处理方式**：继续添加到 system 消息

### 3. 配置文件更新

#### 新增配置字段
```python
'private_chat': {
    # ... 其他配置
    'private_history_count': 5  # 私聊历史记录数量
},
'group_chat': {
    # ... 其他配置
    'group_history_count': 10  # 群聊历史记录数量
}
```

#### 配置迁移
- 现有配置会自动添加新字段
- 默认值：私聊5条，群聊10条
- 无需手动修改配置文件

### 4. 代码层面变更

#### messageProcessor.js
- **删除**：`processGroupParameter` 函数的向后兼容逻辑
- **新增**：`processChatContextParameter` 统一处理函数
- **删除**：旧参数的自动转换逻辑
- **优化**：私聊使用 role-based 消息格式

#### wechat_api.py
- **删除**：旧的 `group` 参数构建逻辑
- **新增**：根据消息类型自动构建 `chat_context`
- **优化**：统一的参数处理流程

#### server.js
- **更新**：导入新的处理函数
- **删除**：旧函数的调用

## 迁移步骤

### 对于开发者

1. **更新代码调用**
   ```javascript
   // 旧方式（已不支持）
   {
     "group": "群名",
     "group_history_count": 10
   }
   
   // 新方式
   {
     "chat_context": {
       "type": "group",
       "name": "群名",
       "history_count": 10
     }
   }
   ```

2. **检查配置文件**
   - 确保包含 `private_history_count` 和 `group_history_count` 字段
   - 系统会自动添加缺失字段

3. **测试功能**
   - 运行 `node test_chat_context.js` 测试核心功能
   - 运行 `python test_wechat_context.py` 测试微信适配器

### 对于用户

- **无需操作**：微信适配器会自动处理所有聊天上下文
- **配置调整**：可在管理面板调整历史记录数量
- **功能增强**：私聊历史记录效果更好

## 功能对比

| 功能 | 旧系统 | 新系统 |
|------|--------|--------|
| 私聊历史 | system 消息 | role-based 消息 |
| 群聊历史 | system 消息 | system 消息（保持） |
| 参数格式 | group + group_history_count | 统一 chat_context |
| 自动检测 | 手动指定 | 根据消息类型自动 |
| 向后兼容 | 支持旧格式 | 仅支持新格式 |
| 时间格式 | 无统一格式 | MM-DD HH:mm:ss |

## 优势

1. **更好的私聊效果**：role-based 消息格式提供更自然的对话体验
2. **统一的参数系统**：简化了参数处理逻辑
3. **自动化处理**：微信适配器根据消息类型自动处理
4. **清晰的代码结构**：删除了复杂的兼容性代码
5. **更好的可维护性**：统一的处理流程

## 测试验证

### 核心功能测试
```bash
node test_chat_context.js
```

### 微信适配器测试
```bash
python test_wechat_context.py
```

### 测试覆盖
- ✅ 私聊上下文：role-based 消息格式
- ✅ 群聊上下文：system 消息格式
- ✅ 无效类型：错误处理
- ✅ MCP模式：正常工作
- ✅ 微信适配器：自动构建参数
- ✅ 配置迁移：包含新字段

## 注意事项

1. **不兼容旧格式**：必须使用新的 `chat_context` 格式
2. **配置要求**：需要设置历史记录数量字段
3. **测试建议**：更新后建议运行测试脚本验证功能
4. **监控建议**：观察私聊对话效果是否改善

## 技术细节

### 私聊历史记录格式
```json
{
  "role": "user",
  "content": "[雨安和静的私聊][07-13 18:30:00] 你好，我是静"
}
```

### 群聊历史记录格式
```
=== 群聊【Sentra-Wechat 测试】最近10条历史记录 ===
[07-13 02:12:50] 高中物理王老师说: 晚安晚安！
[07-13 02:13:04] 高中物理王老师说: 梦里见！
```

### 时间格式
- **格式**：`MM-DD HH:mm:ss`
- **示例**：`[07-13 18:30:00]`
- **时区**：北京时间

这次迁移大大改善了私聊历史记录的处理效果，同时简化了系统架构，提高了代码的可维护性。
