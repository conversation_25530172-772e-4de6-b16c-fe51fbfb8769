/**
 * Permission Manager
 * 权限管理器 - 提供便捷的权限检查和管理工具
 */

/**
 * 权限管理器类
 * 简化权限检查逻辑
 */
class PermissionManager {
    constructor(config = {}) {
        this.config = config;
        this.permissions = config.PERMISSIONS || {};
    }

    /**
     * 检查用户是否为超级管理员
     * @param {string|number} userId 用户ID
     * @returns {boolean} 是否为超级管理员
     */
    isSuperAdmin(userId) {
        const superAdmins = this.permissions.SUPER_ADMINS || [];
        return superAdmins.includes(String(userId)) || superAdmins.includes(Number(userId));
    }

    /**
     * 检查用户是否为机器人管理员
     * @param {string|number} userId 用户ID
     * @returns {boolean} 是否为机器人管理员
     */
    isBotAdmin(userId) {
        const botAdmins = this.permissions.BOT_ADMINS || [];
        return botAdmins.includes(String(userId)) || botAdmins.includes(Number(userId));
    }

    /**
     * 检查用户是否为群管理员
     * @param {string|number} userId 用户ID
     * @param {string|number} groupId 群ID
     * @returns {boolean} 是否为群管理员
     */
    isGroupAdmin(userId, groupId) {
        const groupAdmins = this.permissions.GROUP_ADMINS || {};
        const admins = groupAdmins[String(groupId)] || [];
        return admins.includes(String(userId)) || admins.includes(Number(userId));
    }

    /**
     * 检查用户是否为群主或管理员（通过sender信息）
     * @param {Object} sender 发送者信息
     * @returns {boolean} 是否为群主或管理员
     */
    isGroupOwnerOrAdmin(sender) {
        if (!sender || !sender.role) return false;
        return sender.role === 'owner' || sender.role === 'admin';
    }

    /**
     * 检查用户是否有管理员权限
     * @param {string|number} userId 用户ID
     * @param {string|number} groupId 群ID（可选）
     * @param {Object} sender 发送者信息（可选）
     * @returns {boolean} 是否有管理员权限
     */
    hasAdminPermission(userId, groupId = null, sender = null) {
        // 超级管理员拥有所有权限
        if (this.isSuperAdmin(userId)) return true;
        
        // 机器人管理员拥有管理权限
        if (this.isBotAdmin(userId)) return true;
        
        // 群管理员在对应群组中拥有权限
        if (groupId && this.isGroupAdmin(userId, groupId)) return true;
        
        // 群主和管理员拥有群内权限
        if (sender && this.isGroupOwnerOrAdmin(sender)) return true;
        
        return false;
    }

    /**
     * 检查用户是否有超级管理员权限
     * @param {string|number} userId 用户ID
     * @returns {boolean} 是否有超级管理员权限
     */
    hasSuperAdminPermission(userId) {
        return this.isSuperAdmin(userId);
    }

    /**
     * 检查用户是否有主人权限
     * @param {string|number} userId 用户ID
     * @returns {boolean} 是否有主人权限
     */
    hasMasterPermission(userId) {
        // 主人权限通常是超级管理员列表中的第一个
        const superAdmins = this.permissions.SUPER_ADMINS || [];
        if (superAdmins.length === 0) return false;
        
        const masterId = superAdmins[0];
        return String(userId) === String(masterId) || Number(userId) === Number(masterId);
    }

    /**
     * 根据权限级别检查用户权限
     * @param {string|number} userId 用户ID
     * @param {string} permissionLevel 权限级别 ('all', 'admin', 'super_admin', 'master')
     * @param {Object} context 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermission(userId, permissionLevel, context = {}) {
        const { groupId, sender } = context;

        switch (permissionLevel) {
            case 'all':
                return true;
            
            case 'admin':
                return this.hasAdminPermission(userId, groupId, sender);
            
            case 'super_admin':
                return this.hasSuperAdminPermission(userId);
            
            case 'master':
                return this.hasMasterPermission(userId);
            
            default:
                return false;
        }
    }

    /**
     * 检查用户是否在黑名单中
     * @param {string|number} userId 用户ID
     * @param {string|number} groupId 群ID（可选）
     * @returns {boolean} 是否在黑名单中
     */
    isBlacklisted(userId, groupId = null) {
        const blacklistUsers = this.permissions.BLACKLIST_USERS || [];
        const blacklistGroups = this.permissions.BLACKLIST_GROUPS || [];
        
        // 检查用户黑名单
        if (blacklistUsers.includes(String(userId)) || blacklistUsers.includes(Number(userId))) {
            return true;
        }
        
        // 检查群组黑名单
        if (groupId && (blacklistGroups.includes(String(groupId)) || blacklistGroups.includes(Number(groupId)))) {
            return true;
        }
        
        return false;
    }

    /**
     * 检查用户是否在白名单中
     * @param {string|number} userId 用户ID
     * @param {string|number} groupId 群ID（可选）
     * @returns {boolean} 是否在白名单中
     */
    isWhitelisted(userId, groupId = null) {
        const whitelistUsers = this.permissions.WHITELIST_USERS || [];
        const whitelistGroups = this.permissions.WHITELIST_GROUPS || [];
        
        // 如果没有设置白名单，则认为所有用户都在白名单中
        if (whitelistUsers.length === 0 && whitelistGroups.length === 0) {
            return true;
        }
        
        // 检查用户白名单
        if (whitelistUsers.includes(String(userId)) || whitelistUsers.includes(Number(userId))) {
            return true;
        }
        
        // 检查群组白名单
        if (groupId && (whitelistGroups.includes(String(groupId)) || whitelistGroups.includes(Number(groupId)))) {
            return true;
        }
        
        return false;
    }

    /**
     * 综合权限检查（包括黑白名单）
     * @param {string|number} userId 用户ID
     * @param {string} permissionLevel 权限级别
     * @param {Object} context 上下文信息
     * @returns {Object} 检查结果
     */
    fullPermissionCheck(userId, permissionLevel, context = {}) {
        const { groupId } = context;
        
        // 检查是否启用权限检查
        if (!this.permissions.ENABLE_PERMISSION_CHECK) {
            return { allowed: true, reason: 'permission_check_disabled' };
        }
        
        // 检查黑名单
        if (this.isBlacklisted(userId, groupId)) {
            return { allowed: false, reason: 'blacklisted' };
        }
        
        // 检查白名单
        if (!this.isWhitelisted(userId, groupId)) {
            return { allowed: false, reason: 'not_whitelisted' };
        }
        
        // 检查权限级别
        if (!this.checkPermission(userId, permissionLevel, context)) {
            return { allowed: false, reason: 'insufficient_permission' };
        }
        
        return { allowed: true, reason: 'permission_granted' };
    }

    /**
     * 获取权限拒绝消息
     * @param {string} reason 拒绝原因
     * @returns {string} 拒绝消息
     */
    getPermissionDeniedMessage(reason) {
        const messages = {
            blacklisted: '❌ 您已被加入黑名单，无法使用此功能',
            not_whitelisted: '❌ 您不在白名单中，无法使用此功能',
            insufficient_permission: this.permissions.PERMISSION_DENIED_MESSAGE || '❌ 权限不足，该功能需要管理员权限',
            permission_check_disabled: '✅ 权限检查已禁用'
        };
        
        return messages[reason] || '❌ 权限检查失败';
    }

    /**
     * 添加超级管理员
     * @param {string|number} userId 用户ID
     * @returns {boolean} 是否成功
     */
    addSuperAdmin(userId) {
        if (!this.permissions.SUPER_ADMINS) {
            this.permissions.SUPER_ADMINS = [];
        }
        
        if (!this.isSuperAdmin(userId)) {
            this.permissions.SUPER_ADMINS.push(String(userId));
            return true;
        }
        
        return false;
    }

    /**
     * 移除超级管理员
     * @param {string|number} userId 用户ID
     * @returns {boolean} 是否成功
     */
    removeSuperAdmin(userId) {
        if (!this.permissions.SUPER_ADMINS) return false;
        
        const index = this.permissions.SUPER_ADMINS.findIndex(id => 
            String(id) === String(userId) || Number(id) === Number(userId)
        );
        
        if (index !== -1) {
            this.permissions.SUPER_ADMINS.splice(index, 1);
            return true;
        }
        
        return false;
    }

    /**
     * 添加群管理员
     * @param {string|number} userId 用户ID
     * @param {string|number} groupId 群ID
     * @returns {boolean} 是否成功
     */
    addGroupAdmin(userId, groupId) {
        if (!this.permissions.GROUP_ADMINS) {
            this.permissions.GROUP_ADMINS = {};
        }
        
        const groupKey = String(groupId);
        if (!this.permissions.GROUP_ADMINS[groupKey]) {
            this.permissions.GROUP_ADMINS[groupKey] = [];
        }
        
        if (!this.isGroupAdmin(userId, groupId)) {
            this.permissions.GROUP_ADMINS[groupKey].push(String(userId));
            return true;
        }
        
        return false;
    }

    /**
     * 移除群管理员
     * @param {string|number} userId 用户ID
     * @param {string|number} groupId 群ID
     * @returns {boolean} 是否成功
     */
    removeGroupAdmin(userId, groupId) {
        const groupKey = String(groupId);
        if (!this.permissions.GROUP_ADMINS || !this.permissions.GROUP_ADMINS[groupKey]) {
            return false;
        }
        
        const admins = this.permissions.GROUP_ADMINS[groupKey];
        const index = admins.findIndex(id => 
            String(id) === String(userId) || Number(id) === Number(userId)
        );
        
        if (index !== -1) {
            admins.splice(index, 1);
            return true;
        }
        
        return false;
    }

    /**
     * 获取权限统计信息
     * @returns {Object} 权限统计
     */
    getPermissionStats() {
        const superAdmins = this.permissions.SUPER_ADMINS || [];
        const botAdmins = this.permissions.BOT_ADMINS || [];
        const groupAdmins = this.permissions.GROUP_ADMINS || {};
        
        let totalGroupAdmins = 0;
        Object.values(groupAdmins).forEach(admins => {
            totalGroupAdmins += admins.length;
        });
        
        return {
            superAdmins: superAdmins.length,
            botAdmins: botAdmins.length,
            groupAdmins: totalGroupAdmins,
            groups: Object.keys(groupAdmins).length,
            permissionCheckEnabled: this.permissions.ENABLE_PERMISSION_CHECK || false
        };
    }
}

module.exports = PermissionManager;
