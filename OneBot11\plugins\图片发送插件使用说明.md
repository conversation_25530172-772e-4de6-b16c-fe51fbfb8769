# 图片发送插件使用说明

## 📸 功能介绍

图片发送插件（ImageSenderPlugin）是一个演示如何在OneBot11系统中发送各种类型图片的示例插件。

## 🚀 支持的图片类型

### 1. 网络图片
- **随机图片**: 发送 `发送网络图片` - 发送一张随机网络图片
- **猫咪图片**: 发送 `发送猫咪图片` - 发送一张随机猫咪图片

### 2. 本地图片
- **本地图片**: 发送 `发送本地图片` - 发送本地示例图片
  - 需要在 `OneBot11/assets/` 目录下放置 `sample.jpg` 文件

### 3. Base64图片
- **表情包**: 发送 `发送表情包` - 发送一个简单的Base64编码图片

## 📝 使用方法

### 基本命令
在QQ中发送以下任意命令即可触发相应的图片发送：

```
发送网络图片
发送猫咪图片
发送本地图片
发送表情包
```

### 消息格式
插件会发送包含描述文字和图片的组合消息：
- 文字描述 + 换行
- 图片内容

## 🔧 技术实现

### OneBot11消息格式
```javascript
// 文字 + 图片组合消息
[
    { type: 'text', data: { text: '描述文字\n' } },
    { type: 'image', data: { file: '图片URL或路径' } }
]
```

### 图片文件格式支持
- **网络图片**: `https://example.com/image.jpg`
- **本地图片**: `file:///path/to/image.jpg`
- **Base64图片**: `base64://iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...`

## 📁 文件结构

```
OneBot11/
├── plugins/
│   ├── image_sender_plugin.js     # 主插件文件
│   └── plugin_order.txt           # 插件加载顺序
├── assets/
│   ├── README.md                  # 资源目录说明
│   └── sample.jpg                 # 示例图片（需要自行添加）
└── 图片发送插件使用说明.md         # 本说明文档
```

## ⚙️ 配置说明

### 添加本地图片
1. 在 `OneBot11/assets/` 目录下放置图片文件
2. 修改插件中的路径配置：
```javascript
'发送本地图片': {
    type: 'local',
    path: './assets/your-image.jpg',  // 修改为你的图片文件名
    description: '发送本地示例图片'
}
```

### 自定义图片命令
可以通过修改 `imageCommands` 对象来添加新的图片命令：

```javascript
this.imageCommands = {
    '你的命令': {
        type: 'url',  // 或 'local', 'base64'
        url: 'https://your-image-url.com/image.jpg',
        description: '你的描述'
    }
};
```

## 🐛 常见问题

### 1. 本地图片发送失败
- 检查图片文件是否存在于指定路径
- 确保文件权限正确，机器人进程能够读取
- 支持的图片格式：JPG, PNG, GIF, WEBP, BMP

### 2. 网络图片加载失败
- 检查网络连接
- 确保图片URL可访问
- 某些图片服务可能有防盗链保护

### 3. Base64图片显示异常
- 检查Base64编码是否正确
- 确保图片数据完整
- 注意Base64数据大小限制

## 📊 插件统计

插件提供以下统计信息：
- 可用命令数量
- 命令列表
- 处理成功/失败次数

## 🔄 扩展开发

### 添加新的图片类型
1. 在 `imageCommands` 中添加新配置
2. 在 `sendImageByType` 方法中添加处理逻辑
3. 实现对应的创建方法（如 `createNewTypeImage`）

### 集成图片处理
可以集成图片处理库来实现：
- 图片压缩
- 格式转换
- 添加水印
- 图片滤镜

## 📞 技术支持

如有问题或建议，请查看：
- OneBot11官方文档
- NapCat文档
- VCPToolBox项目文档
