/**
 * 概念学习服务 - 基于OpenAI Tools进行概念提取和学习
 */

class ConceptLearningService {
    constructor(config, openaiService, db, logger) {
        this.config = config;
        this.openaiService = openaiService;
        this.db = db;
        this.logger = logger;
        this.isInitialized = false;
        
        // 概念缓存
        this.conceptCache = new Map();
        this.cacheTimeout = 15 * 60 * 1000; // 15分钟缓存
        
        // 概念类型定义
        this.conceptTypes = ['entity', 'emotion', 'action', 'attribute', 'event', 'abstract', 'relationship'];
        this.associationTypes = ['causal', 'similarity', 'contrast', 'temporal', 'spatial', 'categorical', 'functional'];
    }

    /**
     * 初始化服务
     */
    async initialize() {
        try {
            if (!this.openaiService || !this.openaiService.isInitialized) {
                throw new Error('OpenAI服务未初始化');
            }
            
            this.isInitialized = true;
            this.logger.success('概念学习服务', '✅ 概念学习服务初始化成功');
            return { success: true };
            
        } catch (error) {
            this.logger.error('概念学习服务', '初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 从文本中提取和学习概念
     */
    async extractAndLearnConcepts(text, context = {}) {
        try {
            const { userId, personaName, emotionState } = context;
            
            this.logger.info('概念学习服务', '开始提取和学习概念');
            
            // 使用OpenAI Tools提取概念
            const extractionResult = await this.openaiService.extractConcepts(text, {
                userId,
                personaName,
                emotionContext: emotionState
            });
            
            // 处理提取的概念
            const processedConcepts = await this.processConcepts(extractionResult.concepts);
            const processedAssociations = await this.processAssociations(extractionResult.associations);
            
            // 更新概念神经元
            await this.updateConceptNeurons(processedConcepts, emotionState);
            await this.updateConceptAssociations(processedAssociations);
            
            // 激活相关概念
            const activatedConcepts = await this.activateRelatedConcepts(processedConcepts);
            
            this.logger.info('概念学习服务', 
                `概念学习完成: 提取${processedConcepts.length}个概念, 激活${activatedConcepts.length}个相关概念`
            );
            
            return {
                success: true,
                concepts: processedConcepts,
                associations: processedAssociations,
                activated_concepts: activatedConcepts,
                keywords: extractionResult.keywords || []
            };
            
        } catch (error) {
            this.logger.error('概念学习服务', '概念提取和学习失败:', error.message);
            return {
                success: false,
                error: error.message,
                concepts: []
            };
        }
    }

    /**
     * 获取活跃概念
     */
    async getActiveConcepts(userId, personaName, limit = 10) {
        try {
            const cacheKey = `active_concepts_${userId}_${personaName}`;

            // 进一步缩短缓存时间，确保概念实时更新（从30秒改为5秒）
            const shortCacheTimeout = 5 * 1000; // 5秒
            if (this.conceptCache.has(cacheKey)) {
                const cached = this.conceptCache.get(cacheKey);
                if (Date.now() - cached.timestamp < shortCacheTimeout) {
                    this.logger.debug('概念学习服务', `使用缓存的活跃概念 [${userId}]`);
                    return cached.data;
                }
            }

            // 优化查询：获取最近活跃的概念，按激活强度和时间排序
            const activeConcepts = await this.dbAll(`
                SELECT DISTINCT cn.concept_name, cn.concept_type, cn.activation_strength,
                       cn.activation_count, cn.last_activation,
                       datetime(cn.last_activation, 'localtime') as last_activation_beijing
                FROM concept_neurons cn
                WHERE cn.activation_strength > ?
                AND cn.last_activation > datetime('now', '-7 days')
                ORDER BY cn.activation_strength DESC, cn.last_activation DESC
                LIMIT ?
            `, [this.config.concept_activation_threshold || 0.3, limit]);

            this.logger.debug('概念学习服务', `查询到 ${activeConcepts.length} 个活跃概念 [${userId}]`);

            // 缓存结果（短时间缓存）
            this.conceptCache.set(cacheKey, {
                data: activeConcepts,
                timestamp: Date.now()
            });

            return activeConcepts;

        } catch (error) {
            this.logger.error('概念学习服务', '获取活跃概念失败:', error.message);
            return [];
        }
    }

    /**
     * 获取概念关联
     */
    async getConceptAssociations(conceptName, limit = 5) {
        try {
            const associations = await this.dbAll(`
                SELECT concept_a, concept_b, association_strength, association_type
                FROM concept_associations 
                WHERE (concept_a = ? OR concept_b = ?)
                AND association_strength > 0.3
                ORDER BY association_strength DESC
                LIMIT ?
            `, [conceptName, conceptName, limit]);
            
            return associations.map(assoc => ({
                ...assoc,
                related_concept: assoc.concept_a === conceptName ? assoc.concept_b : assoc.concept_a
            }));
            
        } catch (error) {
            this.logger.error('概念学习服务', '获取概念关联失败:', error.message);
            return [];
        }
    }

    /**
     * 处理提取的概念
     */
    async processConcepts(concepts) {
        try {
            const processedConcepts = [];
            
            for (const concept of concepts) {
                const normalizedName = this.normalizeConcept(concept.name);
                const validType = this.conceptTypes.includes(concept.type) ? concept.type : 'abstract';
                
                processedConcepts.push({
                    name: normalizedName,
                    type: validType,
                    importance: this.clampValue(concept.importance || 0.5, 0, 1),
                    emotional_charge: this.clampValue(concept.emotional_charge || 0, -1, 1),
                    activation_strength: this.clampValue(concept.importance || 0.5, 0, 1)
                });
            }
            
            return processedConcepts;
            
        } catch (error) {
            this.logger.warning('概念学习服务', '处理概念失败:', error.message);
            return [];
        }
    }

    /**
     * 处理概念关联
     */
    async processAssociations(associations) {
        try {
            const processedAssociations = [];
            
            for (const assoc of associations) {
                const conceptA = this.normalizeConcept(assoc.concept_a);
                const conceptB = this.normalizeConcept(assoc.concept_b);
                const validType = this.associationTypes.includes(assoc.association_type) ? 
                    assoc.association_type : 'similarity';
                
                processedAssociations.push({
                    concept_a: conceptA,
                    concept_b: conceptB,
                    association_type: validType,
                    strength: this.clampValue(assoc.strength, 0, 1)
                });
            }
            
            return processedAssociations;
            
        } catch (error) {
            this.logger.warning('概念学习服务', '处理关联失败:', error.message);
            return [];
        }
    }

    /**
     * 更新概念神经元（优化版：支持向量嵌入）
     */
    async updateConceptNeurons(concepts, emotionState = null) {
        try {
            for (const concept of concepts) {
                // 确保概念有必要的字段
                if (!concept.activation_strength) {
                    concept.activation_strength = concept.importance || 0.5;
                }

                const existingConcept = await this.dbGet(`
                    SELECT * FROM concept_neurons WHERE concept_name = ?
                `, [concept.name]);

                // 生成概念的向量嵌入
                let embeddingVector = null;
                try {
                    const embedding = await this.generateEmbedding(concept.name);
                    if (embedding) {
                        embeddingVector = JSON.stringify(embedding);
                    }
                } catch (embeddingError) {
                    this.logger.debug('概念学习服务', `生成概念向量失败: ${embeddingError.message}`);
                }

                if (existingConcept) {
                    // 更新现有概念 - 覆盖而不是跳过
                    const newActivationStrength = this.calculateNewActivationStrength(
                        existingConcept.activation_strength,
                        concept.activation_strength
                    );

                    // 更新字段和向量
                    const updateFields = [];
                    const updateValues = [];

                    updateFields.push('activation_strength = ?', 'activation_count = activation_count + 1', 'last_activation = ?', 'concept_type = ?');
                    updateValues.push(newActivationStrength, new Date().toISOString(), concept.type);

                    // 更新概念含义（如果有）
                    if (concept.meaning) {
                        updateFields.push('detailed_meaning = ?');
                        updateValues.push(concept.meaning);
                    }

                    // 总是更新向量嵌入（如果有新的）
                    if (embeddingVector) {
                        updateFields.push('embedding_vector = ?');
                        updateValues.push(embeddingVector);
                    }

                    updateValues.push(concept.name); // WHERE 条件

                    await this.dbRun(`
                        UPDATE concept_neurons
                        SET ${updateFields.join(', ')}
                        WHERE concept_name = ?
                    `, updateValues);

                    const meaningText = concept.meaning || `${concept.type}类型的概念`;
                    this.logger.success('概念保存', `概念已更新: ${concept.name} - ${meaningText}`);
                } else {
                    // 创建新概念
                    const currentTime = new Date().toISOString();
                    await this.dbRun(`
                        INSERT INTO concept_neurons (
                            concept_name, concept_type, activation_strength,
                            activation_count, last_activation, embedding_vector, detailed_meaning, creation_time
                        ) VALUES (?, ?, ?, 1, ?, ?, ?, ?)
                    `, [
                        concept.name,
                        concept.type,
                        concept.activation_strength,
                        currentTime,
                        embeddingVector,
                        concept.meaning || null,
                        currentTime
                    ]);

                    const meaningText = concept.meaning || `${concept.type}类型的概念`;
                    this.logger.success('概念保存', `新概念已保存: ${concept.name} - ${meaningText}`);
                }
            }

            this.conceptCache.clear();
            
        } catch (error) {
            this.logger.error('概念学习服务', '更新概念神经元失败:', error.message);
        }
    }

    /**
     * 更新概念关联
     */
    async updateConceptAssociations(associations) {
        try {
            for (const assoc of associations) {
                const existingAssoc = await this.dbGet(`
                    SELECT * FROM concept_associations 
                    WHERE (concept_a = ? AND concept_b = ?) OR (concept_a = ? AND concept_b = ?)
                `, [assoc.concept_a, assoc.concept_b, assoc.concept_b, assoc.concept_a]);
                
                if (existingAssoc) {
                    // 更新现有关联
                    const newStrength = Math.min(existingAssoc.association_strength + assoc.strength * 0.1, 1.0);
                    
                    await this.dbRun(`
                        UPDATE concept_associations 
                        SET association_strength = ?, co_occurrence_count = co_occurrence_count + 1,
                            last_occurrence = ?
                        WHERE id = ?
                    `, [newStrength, new Date().toISOString(), existingAssoc.id]);
                } else {
                    // 创建新关联
                    await this.dbRun(`
                        INSERT INTO concept_associations (
                            concept_a, concept_b, association_strength,
                            association_type, co_occurrence_count, last_occurrence
                        ) VALUES (?, ?, ?, ?, 1, ?)
                    `, [
                        assoc.concept_a, assoc.concept_b, assoc.strength,
                        assoc.association_type, new Date().toISOString()
                    ]);
                }
            }
            
        } catch (error) {
            this.logger.error('概念学习服务', '更新概念关联失败:', error.message);
        }
    }

    /**
     * 激活相关概念
     */
    async activateRelatedConcepts(newConcepts) {
        try {
            const activatedConcepts = [];
            
            for (const concept of newConcepts) {
                const relatedConcepts = await this.getConceptAssociations(concept.name);
                
                for (const related of relatedConcepts) {
                    const activationBoost = concept.activation_strength * related.association_strength * 0.5;
                    
                    if (activationBoost > 0.1) {
                        await this.dbRun(`
                            UPDATE concept_neurons 
                            SET activation_strength = activation_strength + ?,
                                last_activation = ?
                            WHERE concept_name = ?
                        `, [activationBoost, new Date().toISOString(), related.related_concept]);
                        
                        activatedConcepts.push({
                            concept_name: related.related_concept,
                            activation_boost: activationBoost,
                            source_concept: concept.name
                        });
                    }
                }
            }
            
            return activatedConcepts;
            
        } catch (error) {
            this.logger.error('概念学习服务', '激活相关概念失败:', error.message);
            return [];
        }
    }

    /**
     * 获取高关联概念对
     */
    async getHighAssociationPairs(limit = 20) {
        try {
            const pairs = await this.dbAll(`
                SELECT concept_a, concept_b, association_strength, association_type
                FROM concept_associations 
                WHERE association_strength > 0.7
                ORDER BY association_strength DESC
                LIMIT ?
            `, [limit]);
            
            return pairs;
            
        } catch (error) {
            this.logger.error('概念学习服务', '获取高关联概念对失败:', error.message);
            return [];
        }
    }

    /**
     * 更新概念网络
     */
    async updateConceptNetwork(consciousnessResult) {
        try {
            // 处理来自OpenAI的概念意识分析结果
            this.logger.info('概念学习服务', '概念网络更新完成');
        } catch (error) {
            this.logger.error('概念学习服务', '更新概念网络失败:', error.message);
        }
    }

    // 辅助方法
    normalizeConcept(conceptName) {
        return conceptName.toLowerCase().trim().replace(/\s+/g, '_');
    }

    calculateNewActivationStrength(oldStrength, newActivation) {
        const alpha = 0.3;
        return alpha * newActivation + (1 - alpha) * oldStrength;
    }

    clampValue(value, min, max) {
        return Math.max(min, Math.min(max, value));
    }

    // 数据库方法包装
    async dbRun(query, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(query, params, function(err) {
                if (err) reject(err);
                else resolve(this);
            });
        });
    }

    async dbGet(query, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(query, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }

    async dbAll(query, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(query, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    /**
     * 获取相关概念（基于向量嵌入，优化性能）
     */
    async getRelevantConcepts(message, userId, personaName, limit = 5, skipToolsExtraction = true) {
        try {
            this.logger.debug('概念学习服务', `开始获取相关概念: ${message.substring(0, 50)}...`);

            let allConcepts = [];

            // 优化：上下文生成时跳过耗时的OpenAI Tools调用
            if (!skipToolsExtraction) {
                // 1. 使用OpenAI Tools提取当前消息中的概念（仅在对话后处理时使用）
                const extractedConcepts = await this.extractConceptsWithTools(message, userId, personaName);

                // 2. 为每个概念生成含义描述
                const conceptsWithMeaning = await this.generateConceptMeanings(extractedConcepts, message);
                allConcepts.push(...conceptsWithMeaning);
            }

            // 3. 基于向量嵌入查找相关概念（快速查询）
            const vectorRelevantConcepts = await this.findConceptsByEmbedding(message, userId, personaName, limit);
            allConcepts.push(...vectorRelevantConcepts);

            // 4. 去重和排序
            const uniqueConcepts = this.deduplicateConcepts(allConcepts);

            // 5. 按相关性排序并限制数量
            const sortedConcepts = uniqueConcepts
                .sort((a, b) => (b.relevance_score || 0) - (a.relevance_score || 0))
                .slice(0, limit);

            this.logger.info('概念学习服务', `获取到 ${sortedConcepts.length} 个相关概念`);

            return sortedConcepts;

        } catch (error) {
            this.logger.error('概念学习服务', `获取相关概念失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 使用OpenAI Tools提取概念
     */
    async extractConceptsWithTools(message, userId, personaName) {
        try {
            const tools = [{
                type: "function",
                function: {
                    name: "extract_concepts",
                    description: "从文本中提取重要概念",
                    parameters: {
                        type: "object",
                        properties: {
                            concepts: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        name: { type: "string", description: "概念名称" },
                                        type: { type: "string", description: "概念类型" },
                                        importance: { type: "number", description: "重要性评分(0-1)" }
                                    },
                                    required: ["name", "type", "importance"]
                                }
                            }
                        },
                        required: ["concepts"]
                    }
                }
            }];

            const prompt = `你是一个概念提取专家。从用户消息中提取重要的概念，包括实体、情感、行为、属性等。

请从以下消息中提取重要概念：${message}`;

            const result = await this.openaiService.makeToolCall(prompt, tools, "extract_concepts");

            if (result && result.concepts) {
                return result.concepts;
            }

            return [];

        } catch (error) {
            this.logger.error('概念学习服务', `OpenAI Tools概念提取失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 为概念生成含义描述
     */
    async generateConceptMeanings(concepts, context) {
        try {
            if (!concepts || concepts.length === 0) return [];

            const tools = [{
                type: "function",
                function: {
                    name: "explain_concepts",
                    description: "为概念提供含义解释",
                    parameters: {
                        type: "object",
                        properties: {
                            explanations: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        concept: { type: "string", description: "概念名称" },
                                        meaning: { type: "string", description: "在当前上下文中的含义" },
                                        relevance: { type: "number", description: "相关性评分(0-1)" }
                                    },
                                    required: ["concept", "meaning", "relevance"]
                                }
                            }
                        },
                        required: ["explanations"]
                    }
                }
            }];

            const conceptNames = concepts.map(c => c.name).join(', ');

            const prompt = `你是一个概念解释专家。为给定的概念提供在特定上下文中的含义解释。

在以下上下文中："${context}"，请解释这些概念的含义：${conceptNames}`;

            const result = await this.openaiService.makeToolCall(prompt, tools, "explain_concepts");

            if (result && result.explanations) {
                return result.explanations.map(exp => ({
                    concept_name: exp.concept,
                    meaning: exp.meaning,
                    description: exp.meaning,
                    relevance_score: exp.relevance,
                    similarity: exp.relevance,
                    source: 'openai_tools'
                }));
            }

            return [];

        } catch (error) {
            this.logger.error('概念学习服务', `概念含义生成失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 基于向量嵌入查找相关概念（优化版：真正的语义匹配）
     */
    async findConceptsByEmbedding(message, userId, personaName, limit) {
        try {
            this.logger.debug('概念学习服务', `开始基于向量嵌入查找相关概念: ${message.substring(0, 50)}...`);

            // 1. 生成查询消息的向量嵌入
            const queryEmbedding = await this.generateEmbedding(message);
            if (!queryEmbedding || !Array.isArray(queryEmbedding)) {
                this.logger.warning('概念学习服务', '无法生成查询向量，使用传统方法');
                return await this.findConceptsByTraditionalMethod(message, userId, personaName, limit);
            }

            this.logger.debug('概念学习服务', `查询向量生成成功，维度: ${queryEmbedding.length}`);

            // 2. 查询数据库中有向量嵌入的概念
            // 优化：查询所有有向量的概念进行比较，而不是限制数量
            const concepts = await this.dbAll(`
                SELECT concept_name, concept_type, activation_strength, last_activation,
                       associated_emotions, embedding_vector, detailed_meaning
                FROM concept_neurons
                WHERE embedding_vector IS NOT NULL AND embedding_vector != ''
                ORDER BY activation_strength DESC, last_activation DESC
            `); // 查询所有有向量的概念进行语义匹配

            this.logger.debug('概念学习服务', `数据库查询到 ${concepts.length} 个概念`);

            if (concepts.length === 0) {
                this.logger.info('概念学习服务', '数据库中没有向量嵌入概念，使用传统方法');
                return await this.findConceptsByTraditionalMethod(message, userId, personaName, limit);
            }

            // 3. 计算余弦相似度
            const conceptsWithSimilarity = [];
            let validConceptCount = 0;

            for (const concept of concepts) {
                try {
                    let conceptEmbedding;
                    try {
                        conceptEmbedding = JSON.parse(concept.embedding_vector);
                    } catch (parseError) {
                        this.logger.debug('概念学习服务', `概念 ${concept.concept_name} 向量解析失败: ${parseError.message}`);
                        continue;
                    }

                    if (!Array.isArray(conceptEmbedding) || conceptEmbedding.length === 0) {
                        this.logger.debug('概念学习服务', `概念 ${concept.concept_name} 向量格式无效`);
                        continue;
                    }

                    if (conceptEmbedding.length !== queryEmbedding.length) {
                        this.logger.debug('概念学习服务', `概念 ${concept.concept_name} 向量维度不匹配: ${conceptEmbedding.length} vs ${queryEmbedding.length}`);
                        continue;
                    }

                    validConceptCount++;
                    const similarity = this.calculateCosineSimilarity(queryEmbedding, conceptEmbedding);

                    this.logger.debug('概念学习服务', `概念 ${concept.concept_name} 相似度: ${similarity.toFixed(3)}`);

                    if (similarity > 0.2) { // 降低阈值，更容易找到相关概念
                        conceptsWithSimilarity.push({
                            ...concept,
                            similarity: similarity,
                            relevance_score: similarity * (concept.activation_strength || 0.5), // 结合激活强度
                            source: 'vector_embedding'
                        });
                    }
                } catch (e) {
                    this.logger.debug('概念学习服务', `处理概念 ${concept.concept_name} 时出错: ${e.message}`);
                }
            }

            this.logger.info('概念学习服务', `处理了 ${concepts.length} 个概念，有效概念 ${validConceptCount} 个，找到相似概念 ${conceptsWithSimilarity.length} 个`);

            // 4. 按相似度排序并限制数量
            const sortedConcepts = conceptsWithSimilarity
                .sort((a, b) => b.similarity - a.similarity)
                .slice(0, limit);

            // 5. 优化：上下文生成时直接使用数据库中的含义，避免调用OpenAI
            const conceptsWithMeaning = sortedConcepts.map(concept => ({
                ...concept,
                meaning: concept.detailed_meaning || `${concept.concept_type}类型的概念`,
                description: concept.detailed_meaning || `${concept.concept_type}类型的概念`
            }));

            this.logger.success('概念学习服务', `基于向量嵌入找到 ${conceptsWithMeaning.length} 个相关概念`);
            return conceptsWithMeaning;

        } catch (error) {
            this.logger.error('概念学习服务', `向量嵌入查找失败: ${error.message}`, error.stack);
            return await this.findConceptsByTraditionalMethod(message, userId, personaName, limit);
        }
    }

    /**
     * 传统方法查找概念（备用方案）
     */
    async findConceptsByTraditionalMethod(message, userId, personaName, limit) {
        try {
            // 查询数据库中的概念，按激活强度排序
            const concepts = await this.dbAll(`
                SELECT concept_name, concept_type, activation_strength, last_activation,
                       associated_emotions as description, detailed_meaning
                FROM concept_neurons
                ORDER BY activation_strength DESC, last_activation DESC
                LIMIT ?
            `, [limit * 2]); // 获取更多候选

            // 为概念添加相关性评分（基于激活强度和时间）
            const now = Date.now();
            return concepts.map(concept => {
                const timeDiff = now - new Date(concept.last_activation).getTime();
                const timeDecay = Math.exp(-timeDiff / (24 * 60 * 60 * 1000)); // 24小时衰减
                const relevanceScore = concept.activation_strength * timeDecay;

                // 解析关联情绪作为描述
                let description = concept.detailed_meaning || `${concept.concept_type || '未知'}类型的概念`;
                try {
                    if (concept.description && !concept.detailed_meaning) {
                        const emotions = JSON.parse(concept.description);
                        if (emotions && typeof emotions === 'object') {
                            description = `与${Object.keys(emotions).join('、')}等情绪相关的概念`;
                        }
                    }
                } catch (e) {
                    // 使用默认描述
                }

                return {
                    concept_name: concept.concept_name,
                    meaning: description,
                    description: description,
                    relevance_score: relevanceScore,
                    similarity: relevanceScore,
                    source: 'database_embedding'
                };
            }).slice(0, limit);

        } catch (error) {
            this.logger.error('概念学习服务', `向量嵌入概念查找失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 去重概念
     */
    deduplicateConcepts(concepts) {
        const seen = new Set();
        return concepts.filter(concept => {
            const key = concept.concept_name.toLowerCase();
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    /**
     * 生成文本的向量嵌入
     */
    async generateEmbedding(text) {
        try {
            if (!this.openaiService || !this.openaiService.isInitialized) {
                this.logger.warning('概念学习服务', 'OpenAI服务未初始化');
                return null;
            }

            // 使用OpenAI的嵌入模型
            const response = await this.openaiService.generateEmbedding(text);

            // 提取嵌入向量数组
            let embeddingVector;
            if (response && response.embedding && Array.isArray(response.embedding)) {
                embeddingVector = response.embedding;
            } else if (Array.isArray(response)) {
                embeddingVector = response;
            } else {
                this.logger.error('概念学习服务', `OpenAI返回格式错误: ${JSON.stringify(response).substring(0, 100)}...`);
                return null;
            }

            // 验证嵌入向量
            if (!embeddingVector || embeddingVector.length === 0) {
                this.logger.error('概念学习服务', '嵌入向量为空或无效');
                return null;
            }

            this.logger.debug('概念学习服务', `嵌入向量生成成功，维度: ${embeddingVector.length}`);
            return embeddingVector;

        } catch (error) {
            this.logger.error('概念学习服务', `生成向量嵌入失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 计算两个向量的余弦相似度
     */
    calculateCosineSimilarity(vectorA, vectorB) {
        try {
            if (!vectorA || !vectorB || vectorA.length !== vectorB.length) {
                return 0;
            }

            let dotProduct = 0;
            let normA = 0;
            let normB = 0;

            for (let i = 0; i < vectorA.length; i++) {
                dotProduct += vectorA[i] * vectorB[i];
                normA += vectorA[i] * vectorA[i];
                normB += vectorB[i] * vectorB[i];
            }

            if (normA === 0 || normB === 0) {
                return 0;
            }

            return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));

        } catch (error) {
            this.logger.error('概念学习服务', `计算余弦相似度失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 为概念增强语义描述
     */
    async enhanceConceptsWithMeaning(concepts, queryMessage) {
        try {
            const enhancedConcepts = [];

            for (const concept of concepts) {
                let meaningDescription = concept.detailed_meaning;

                // 如果没有详细含义，使用OpenAI Tools生成
                if (!meaningDescription) {
                    try {
                        const meaningResult = await this.openaiService.generateConceptMeaning(
                            concept.concept_name,
                            queryMessage,
                            {
                                concept_type: concept.concept_type,
                                activation_strength: concept.activation_strength,
                                context: '用户查询相关概念'
                            }
                        );

                        meaningDescription = meaningResult.meaning || `${concept.concept_type}类型的概念 [其他概念]`;

                        // 保存生成的含义到数据库
                        await this.saveConceptMeaning(concept.concept_name, meaningDescription);

                    } catch (meaningError) {
                        this.logger.debug('概念学习服务', `生成概念含义失败: ${meaningError.message}`);
                        meaningDescription = `${concept.concept_type}类型的概念 [其他概念]`;
                    }
                }

                enhancedConcepts.push({
                    ...concept,
                    meaning: meaningDescription,
                    description: meaningDescription
                });
            }

            return enhancedConcepts;

        } catch (error) {
            this.logger.error('概念学习服务', `增强概念语义失败: ${error.message}`);
            return concepts.map(concept => ({
                ...concept,
                meaning: `${concept.concept_type}类型的概念 [其他概念]`,
                description: `${concept.concept_type}类型的概念 [其他概念]`
            }));
        }
    }

    /**
     * 保存概念含义到数据库
     */
    async saveConceptMeaning(conceptName, meaning) {
        try {
            await this.dbRun(`
                UPDATE concept_neurons
                SET detailed_meaning = ?
                WHERE concept_name = ?
            `, [meaning, conceptName]);

        } catch (error) {
            this.logger.debug('概念学习服务', `保存概念含义失败: ${error.message}`);
        }
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            cachedConcepts: this.conceptCache.size,
            supportedConceptTypes: this.conceptTypes.length,
            supportedAssociationTypes: this.associationTypes.length
        };
    }
}

module.exports = ConceptLearningService;