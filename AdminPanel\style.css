:root {
    /* --- 默认暗色主题 (墨色系) --- */
    --primary-bg-dark: #1c1c1e;      /* 深墨灰 - 近黑 */
    --secondary-bg-dark: #28282c;    /* 略浅的墨灰 */
    --tertiary-bg-dark: #121212;     /* 聊天区背景 - 纯黑に近い */
    --accent-bg-dark: #3a3a3e;       /* 悬停/选中背景 - 中墨灰 */
    --primary-text-dark: #e0e0e0;    /* 主要文字 - 浅灰 */
    --secondary-text-dark: #a0a0a0;  /* 次要/标题文字 - 中灰 */
    --highlight-text-dark: #6fa8dc;  /* 高亮文字 - 柔和蓝 (点缀色) */
    --border-color-dark: #3a3a3e;    /* 边框颜色 */
    --user-bubble-bg-dark: rgba(56, 120, 173, 0.75);  /* 用户气泡 - 柔和蓝 (点缀色) - 带透明度 */
    --assistant-bubble-bg-dark: rgba(47, 47, 51, 0.75);/* AI气泡 - 深墨灰 - 带透明度 */
    --button-bg-dark: #48484c;       /* 普通按钮背景 - 中墨灰 */
    --button-hover-bg-dark: #58585e; /* 按钮悬停 - 略亮的墨灰 */
    --danger-color-dark: #e57373;    /* 危险操作 - 柔和红 */
    --danger-hover-bg-dark: #ef5350; /* 危险操作悬停 - 略深的柔和红 */
    --input-bg-dark: #222225;        /* 输入框背景 */
    --tool-bubble-bg-dark: #3a3a3e;   /* VCP工具调用气泡背景 */
    --tool-bubble-border-dark: #58585e;
    --notification-bg-dark: #2f2f33;
    --notification-header-bg-dark: #3a3a3e;
    --notification-border-dark: #48484c;
    --scrollbar-track-dark: rgba(40, 40, 40, 0.5);
    --scrollbar-thumb-dark: rgba(100, 100, 100, 0.6);
    --scrollbar-thumb-hover-dark: rgba(120, 120, 120, 0.8);
    --quoted-text-dark: #FFB74D; /* 深色模式引用文本 - 柔和橙色 */

    /* --- 亮色主题 --- */
    --primary-bg-light: #f4f6f8;      /* 非常浅的灰蓝色 */
    --secondary-bg-light: #ffffff;    /* 白色 */
    --tertiary-bg-light: #e9edf0;     /* 聊天区背景，浅灰 */
    --accent-bg-light: #e0e6eb;       /* 悬停/选中背景 */
    --primary-text-light: #2c3e50;    /* 主要文字 - 深灰蓝 */
    --secondary-text-light: #5a6f80;  /* 次要/标题文字 - 中灰蓝 */
    --highlight-text-light: #3498db;  /* 高亮文字 - 清爽蓝 */
    --border-color-light: #e0e6eb;    /* 边框颜色 - 调整为更浅的颜色，与 accent-bg-light 相似 */
    --user-bubble-bg-light: rgba(52, 152, 219, 0.7);  /* 用户气泡 - 清爽蓝 - 带透明度 */
    --assistant-bubble-bg-light: rgba(232, 244, 248, 0.7);/* AI气泡 - 非常浅的蓝 - 带透明度 */
    --button-bg-light: #3498db;       /* 普通按钮背景 - 清爽蓝 */
    --button-hover-bg-light: #2980b9; /* 按钮悬停 - 深一点的蓝 */
    --danger-color-light: #e74c3c;    /* 危险操作 - 红色 */
    --danger-hover-bg-light: #c0392b; /* 危险操作悬停 - 深红 */
    --input-bg-light: #ffffff;        /* 输入框背景 - 白色 */
    --tool-bubble-bg-light: #e0e6eb;   /* VCP工具调用气泡背景 - 浅灰 */
    --tool-bubble-border-light: #b8c0c8;
    --notification-bg-light: #e8f4f8;
    --notification-header-bg-light: #d0d8e0;
    --notification-border-light: #3498db;
    --scrollbar-track-light: rgba(200, 200, 200, 0.5);
    --scrollbar-thumb-light: rgba(150, 150, 150, 0.6);
    --scrollbar-thumb-hover-light: rgba(120, 120, 120, 0.8);
    --quoted-text-light: #007bff; /* 浅色模式引用文本 - 蓝色 */
}

html[data-theme='dark'] {
    --primary-bg: var(--primary-bg-dark);
    --secondary-bg: var(--secondary-bg-dark);
    --tertiary-bg: var(--tertiary-bg-dark);
    --accent-bg: var(--accent-bg-dark);
    --primary-text: var(--primary-text-dark);
    --secondary-text: var(--secondary-text-dark);
    --highlight-text: var(--highlight-text-dark);
    --border-color: var(--border-color-dark);
    --user-bubble-bg: var(--user-bubble-bg-dark);
    --assistant-bubble-bg: var(--assistant-bubble-bg-dark);
    --button-bg: var(--button-bg-dark);
    --button-hover-bg: var(--button-hover-bg-dark);
    --danger-color: var(--danger-color-dark);
    --danger-hover-bg: var(--danger-hover-bg-dark);
    --input-bg: var(--input-bg-dark);
    --tool-bubble-bg: var(--tool-bubble-bg-dark);
    --tool-bubble-border: var(--tool-bubble-border-dark);
    --notification-bg: var(--notification-bg-dark);
    --notification-header-bg: var(--notification-header-bg-dark);
    --notification-border: var(--notification-border-dark);
    --scrollbar-track: var(--scrollbar-track-dark);
    --scrollbar-thumb: var(--scrollbar-thumb-dark);
    --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-dark);
    --quoted-text: var(--quoted-text-dark);
}

html[data-theme='light'] {
    --primary-bg: var(--primary-bg-light);
    --secondary-bg: var(--secondary-bg-light);
    --tertiary-bg: var(--tertiary-bg-light);
    --accent-bg: var(--accent-bg-light);
    --primary-text: var(--primary-text-light);
    --secondary-text: var(--secondary-text-light);
    --highlight-text: var(--highlight-text-light);
    --border-color: var(--border-color-light);
    --user-bubble-bg: var(--user-bubble-bg-light);
    --assistant-bubble-bg: var(--assistant-bubble-bg-light);
    --button-bg: var(--button-bg-light);
    --button-hover-bg: var(--button-hover-bg-light);
    --danger-color: var(--danger-color-light);
    --danger-hover-bg: var(--danger-hover-bg-light);
    --input-bg: var(--input-bg-light);
    --tool-bubble-bg: var(--tool-bubble-bg-light);
    --tool-bubble-border: var(--tool-bubble-border-light);
    --notification-bg: var(--notification-bg-light);
    --notification-header-bg: var(--notification-header-bg-light);
    --notification-border: var(--notification-border-light);
    --scrollbar-track: var(--scrollbar-track-light);
    --scrollbar-thumb: var(--scrollbar-thumb-light);
    --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-light);
    --quoted-text: var(--quoted-text-light);
}

/* 默认主题设置为暗色 */
html:not([data-theme]), html[data-theme='dark'] {
    color-scheme: dark;
}

/* 亮色主题设置 */
html[data-theme='light'] {
    color-scheme: light;
}
/* style.css */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding-top: 52px; /* 为固定的 top-bar 留出空间 (预估 52px) */
    background-color: var(--primary-bg);
    color: var(--primary-text);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    overflow-y: auto;
    font-size: 16px;
}

.container {
    display: flex;
    width: 95%;
    max-width: 1600px; /* 稍微加宽以容纳更多内容 */
    min-height: calc(100vh - 52px - 40px); /* 减去 top-bar 高度(52px)和上下 margin(20+20) */
    margin-top: 20px;
    margin-bottom: 20px;
    background-color: var(--secondary-bg);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.35);
    overflow: hidden;
}

.sidebar {
    width: 280px;
    flex-shrink: 0; /* 防止侧边栏被压缩 */
    background-color: var(--tertiary-bg);
    padding: 25px;
    border-right: 1px solid var(--border-color);
    box-sizing: border-box;
    overflow-y: auto;
    max-height: calc(100vh - 52px - 40px); /* 与容器同步，并减去 top-bar 高度(52px) */
}

.sidebar h1 {
    font-size: 1.8em;
    color: var(--highlight-text);
    margin-top: 0;
    margin-bottom: 30px;
    text-align: center;
    letter-spacing: 1px;
    font-weight: 600;
}

.sidebar nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.sidebar nav li a {
    display: block;
    color: var(--secondary-text);
    padding: 13px 20px; /* 增加内边距 */
    text-decoration: none;
    border-radius: 8px; /* 更圆润的边角 */
    margin-bottom: 10px;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, transform 0.1s ease;
    font-size: 0.95em;
    font-weight: 500;
}

.sidebar nav li a:hover {
    background-color: var(--accent-bg);
    color: var(--primary-text);
    transform: translateX(3px); /* 轻微悬浮效果 */
}
.sidebar nav li a.active {
    background-color: var(--accent-bg-dark); /* 暗色主题下的激活状态可以更深一些 */
    color: var(--primary-text-dark); /* 确保在深色激活背景下文字清晰 */
    font-weight: 600;
    box-shadow: inset 3px 0 0 var(--highlight-text); /* 左侧指示条 */
}

html[data-theme='light'] .sidebar nav li a.active {
    background-color: var(--accent-bg-light); /* 亮色主题下的激活状态 */
    color: var(--primary-text-light); /* 确保在浅色激活背景下文字清晰 */
}

.sidebar nav li a .plugin-type-icon {
    margin-left: 8px;
    font-size: 0.8em;
    color: var(--highlight-text);
    background-color: var(--accent-bg);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.content {
    flex-grow: 1;
    padding: 30px 40px; /* 增加左右内边距 */
    box-sizing: border-box;
    overflow-y: auto;
    max-height: calc(100vh - 52px - 40px); /* 与容器同步，并减去 top-bar 高度(52px) */
}

.config-section {
    display: none;
    animation: fadeIn 0.5s ease-out;
    overflow-y: auto;
    max-height: 100%;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.config-section.active-section {
    display: block;
}

.config-section h2 {
    font-size: 1.7em; /* 标题稍大 */
    color: var(--highlight-text);
    margin-top: 0;
    margin-bottom: 25px;
    border-bottom: 2px solid var(--highlight-text);
    padding-bottom: 12px;
    font-weight: 600;
}
.config-section h3 { /* 用于插件内部的子标题，例如 "Schema 配置" vs "自定义 .env 配置" */
    font-size: 1.3em;
    color: var(--highlight-text); /* 可以考虑用 secondary-text 或调整后的高亮 */
    margin-top: 30px;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px dashed var(--border-color);
}


form {
    display: flex;
    flex-direction: column;
    gap: 22px; /* 表单组间距 */
}

.form-group {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #dee2e6;
}

.form-group.has-value {
    border-left-color: #28a745;
}

.form-group.required {
    border-left-color: #dc3545;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: 0;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group input[type="password"]:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--highlight-text);
    box-shadow: 0 0 0 3px var(--highlight-text-dark); /* 使用高亮色的阴影，根据主题调整透明度 */
}
html[data-theme='light'] .form-group input[type="text"]:focus,
html[data-theme='light'] .form-group input[type="number"]:focus,
html[data-theme='light'] .form-group input[type="password"]:focus,
html[data-theme='light'] .form-group textarea:focus,
html[data-theme='light'] .form-group select:focus {
    box-shadow: 0 0 0 3px var(--highlight-text-light);
}


.form-group textarea {
    min-height: 100px; /* 文本域更高 */
    resize: vertical;
}

/* 美化 checkbox 为开关样式 */
.switch-container { /* 包裹开关和旁边的文字标签 */
    display: flex;
    align-items: center;
    gap: 10px;
}
.switch {
  position: relative;
  display: inline-block;
  width: 48px; 
  height: 24px; 
}
.switch input { 
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--accent-bg); /* 开关背景 */
  transition: .3s;
  border-radius: 24px;
}
.slider:before {
  position: absolute;
  content: "";
  height: 18px; 
  width: 18px; 
  left: 3px; 
  bottom: 3px; 
  background-color: white; /* 滑块颜色，通常为白色 */
  transition: .3s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}
input:checked + .slider {
  background-color: var(--highlight-text); /* 选中时的背景色 */
}
input:focus + .slider {
  box-shadow: 0 0 2px var(--highlight-text), 0 0 5px var(--highlight-text); /* 焦点时更明显 */
}
input:checked + .slider:before {
  transform: translateX(24px); /* 滑块移动距离调整 */
}


.form-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.form-actions button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.form-actions button[type="submit"] {
    background-color: #007bff;
    color: white;
}

.form-actions button[type="submit"]:hover {
    background-color: #0056b3;
}

.form-actions .add-config-btn {
    background-color: #28a745;
    color: white;
}

.form-actions .add-config-btn:hover {
    background-color: #1e7e34;
}

.delete-config-btn {
    padding: 6px 10px;
    background-color: var(--danger-color);
    color: white; /* 删除按钮文字通常为白色 */
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.85em;
    transition: background-color 0.2s ease;
}
.delete-config-btn:hover {
    background-color: var(--danger-hover-bg);
}
html[data-theme='light'] .delete-config-btn {
    background-color: var(--danger-color-light);
}
html[data-theme='light'] .delete-config-btn:hover {
    background-color: var(--danger-hover-bg-light);
}


/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85); /* 暗色遮罩 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease, visibility 0s linear 0.3s;
}
html[data-theme='light'] .loading-overlay {
    background-color: rgba(244, 246, 248, 0.85); /* 亮色遮罩 */
}

.loading-overlay.visible {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.3s ease, visibility 0s linear 0s;
}

.spinner {
    border: 5px solid var(--accent-bg); 
    border-top: 5px solid var(--highlight-text); 
    border-radius: 50%;
    width: 45px;
    height: 45px;
    animation: spin 0.8s linear infinite; /* 加快旋转 */
    margin-bottom: 18px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: var(--primary-text);
    font-size: 1.1em;
    font-weight: 500;
}

/* 消息提示框 */
.message-popup {
    position: fixed;
    bottom: -100px; /* 初始位置在屏幕外 */
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--notification-bg);
    color: var(--primary-text);
    padding: 16px 28px; /* 增加内边距 */
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s ease, bottom 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55), visibility 0s linear 0.4s; /* 添加弹性动画 */
    font-size: 1em;
    min-width: 250px;
    text-align: center;
    border: 1px solid var(--notification-border);
}

.message-popup.show {
    opacity: 1;
    bottom: 40px; /* 最终位置 */
    visibility: visible;
    transition: opacity 0.4s ease, bottom 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55), visibility 0s linear 0s;
}
.message-popup.success {
    background-color: var(--button-bg); /* 使用按钮背景色或特定成功色 */
    color: white; /* 确保文字清晰 */
    border-color: var(--button-bg);
}
.message-popup.error {
    background-color: var(--danger-color);
    color: white; /* 确保文字清晰 */
    border-color: var(--danger-color);
}
html[data-theme='light'] .message-popup.success {
    background-color: var(--button-bg-light);
    color: var(--primary-text-light);
    border-color: var(--button-bg-light);
}
html[data-theme='light'] .message-popup.error {
    background-color: var(--danger-color-light);
    color: var(--primary-text-light);
    border-color: var(--danger-color-light);
}


/* Top Bar Styles */
.top-bar {
    background-color: var(--secondary-bg); /* 使用半透明效果可能更好 */
    backdrop-filter: blur(8px); 
    -webkit-backdrop-filter: blur(8px); 
    color: var(--primary-text);
    padding: 10px 0; /* 上下内边距 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); /* 调整阴影 */
    width: 100%;
    position: fixed; /* 改为 fixed 使其完全悬浮 */
    top: 0;
    left: 0; 
    right: 0; 
    z-index: 1050;
    box-sizing: border-box;
    height: 52px; /* 明确高度，与 body padding-top 一致 */
    border-bottom: 1px solid var(--border-color);
}
html[data-theme='dark'] .top-bar {
     background-color: rgba(28, 28, 30, 0.85); /* 深色主题下的半透明背景 */
}
html[data-theme='light'] .top-bar {
     background-color: rgba(244, 246, 248, 0.85); /* 浅色主题下的半透明背景 */
}


.top-bar-content {
    width: 95%;
    max-width: 1600px; /* 与主容器一致 */
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px; /* 内边距，避免内容贴边 */
    box-sizing: border-box;
    gap: 10px; /* 为按钮之间添加一些间距 */
}

.server-title {
    font-size: 1.5em; /* 稍大一些的标题 */
    font-weight: 600;
    color: var(--highlight-text);
    letter-spacing: 0.5px;
}

.top-bar-content > .server-title {
    margin-right: auto; /* 将标题推向左边，让按钮组靠右 */
}

.restart-button,
.theme-button { /* 为主题按钮添加统一样式 */
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 500;
    transition: background-color 0.2s ease, transform 0.1s ease;
}

.restart-button {
    background-color: var(--danger-color);
    color: white;
}

.restart-button:hover {
    background-color: var(--danger-hover-bg);
    transform: translateY(-1px);
}

.theme-button {
    background-color: var(--button-bg); /* 使用通用按钮颜色 */
    color: white;
}

.theme-button:hover {
    background-color: var(--button-hover-bg);
    transform: translateY(-1px);
}

html[data-theme='light'] .restart-button {
    background-color: var(--danger-color-light);
    color: var(--primary-text-light);
}
html[data-theme='light'] .restart-button:hover {
    background-color: var(--danger-hover-bg-light);
}

html[data-theme='light'] .theme-button {
    background-color: var(--button-bg-light);
    color: var(--primary-text-light);
}
html[data-theme='light'] .theme-button:hover {
    background-color: var(--button-hover-bg-light);
}


.restart-button:active,
.theme-button:active {
    transform: translateY(0px);
}

/* Invocation Commands Editor Styles */
.invocation-commands-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color); /* Separator from plugin .env config */
}

.invocation-commands-section h3 {
    font-size: 1.4em; /* Slightly smaller than main section h2 */
    color: var(--highlight-text); /* Consistent with other h3 */
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 1px dashed var(--border-color); /* Consistent with other h3 */
}

.command-item {
    background-color: var(--tertiary-bg); /* Slightly different background for each command block */
    padding: 20px;
    border: 1px solid var(--border-color); /* Consistent with form-group border */
    border-radius: 8px; /* Consistent with form-group border-radius */
    margin-bottom: 20px;
}

.command-item h4 {
    font-size: 1.2em;
    color: var(--primary-text); /* Consistent with form-group label color */
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 600;
}

textarea.command-description-edit {
    width: 100%;
    min-height: 100px; 
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 0.95em;
    box-sizing: border-box;
    resize: vertical;
    margin-bottom: 15px; /* Space before save button */
}

textarea.command-description-edit:focus {
    outline: none;
    border-color: var(--highlight-text);
    box-shadow: 0 0 0 3px var(--highlight-text-dark); /* 使用高亮色的阴影，根据主题调整透明度 */
}
html[data-theme='light'] textarea.command-description-edit:focus {
    box-shadow: 0 0 0 3px var(--highlight-text-light);
}


.command-item .form-actions { /* Reusing form-actions for button container */
    border-top: none; /* No top border needed here */
    padding-top: 0;
    margin-top: 0; /* Adjust if needed */
    justify-content: flex-start; /* Align button to the left */
}

.save-command-description-btn {
    padding: 10px 18px; /* Slightly adjusted padding, can match others if preferred */
    color: white; /* 按钮文字颜色 */
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.95em; /* Slightly adjusted font size */
    font-weight: 500;
    transition: background-color 0.2s ease, transform 0.1s ease;
    background-color: var(--button-bg); /* Specific green for this save button */
}
.save-command-description-btn:hover {
    background-color: var(--button-hover-bg); 
    transform: translateY(-1px);
}
html[data-theme='light'] .save-command-description-btn {
    background-color: var(--button-bg-light);
    color: var(--primary-text-light);
}
html[data-theme='light'] .save-command-description-btn:hover {
    background-color: var(--button-hover-bg-light);
}

.save-command-description-btn:active {
    transform: translateY(0px);
}

p.command-status {
    margin-top: 10px;
    font-size: 0.9em;
}
p.command-status.success {
    color: var(--button-bg); /* 使用按钮相关颜色或特定成功色 */
}
p.command-status.error {
    color: var(--danger-color);
}
p.command-status.info {
    color: var(--highlight-text);
}
html[data-theme='light'] p.command-status.success {
    color: var(--button-bg-light);
}
html[data-theme='light'] p.command-status.error {
    color: var(--danger-color-light);
}
html[data-theme='light'] p.command-status.info {
    color: var(--highlight-text-light);
}


/* Plugin Controls Styles */
.plugin-controls {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 15px;
    align-items: center;
}

.toggle-plugin-button {
    padding: 10px 18px;
    color: white; /* 文字颜色 */
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.95em;
    font-weight: 500;
    transition: background-color 0.2s ease, transform 0.1s ease;
    background-color: var(--danger-color); /* Red for Disable */
}

.toggle-plugin-button:hover:not(:disabled) {
    background-color: var(--danger-hover-bg); /* Darker red on hover */
    transform: translateY(-1px);
}

.toggle-plugin-button.disabled-state {
    background-color: var(--button-bg); /* Green for Enable */
}

.toggle-plugin-button.disabled-state:hover:not(:disabled) {
    background-color: var(--button-hover-bg); /* Darker green on hover */
    transform: translateY(-1px);
}

html[data-theme='light'] .toggle-plugin-button {
    background-color: var(--danger-color-light);
    color: var(--primary-text-light);
}
html[data-theme='light'] .toggle-plugin-button:hover:not(:disabled) {
    background-color: var(--danger-hover-bg-light);
}
html[data-theme='light'] .toggle-plugin-button.disabled-state {
    background-color: var(--button-bg-light);
}
html[data-theme='light'] .toggle-plugin-button.disabled-state:hover:not(:disabled) {
    background-color: var(--button-hover-bg-light);
}


.toggle-plugin-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}


/* Daily Notes Manager Styles */
.daily-notes-manager {
    display: flex;
    gap: 20px;
    height: calc(100% - 60px); /* Adjust based on h2 margin */
    justify-content: flex-start; /* 使子项靠左对齐 */
}

.notes-sidebar {
    width: 200px;
    flex-shrink: 0;
    background-color: var(--tertiary-bg); /* Consistent with main sidebar */
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow-y: auto;
    max-height: 100%;
}

.notes-sidebar h3 {
    font-size: 1.2em;
    color: var(--highlight-text);
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px dashed var(--border-color);
}

#notes-folder-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

#notes-folder-list li {
    padding: 10px 12px;
    color: var(--secondary-text);
    cursor: pointer;
    border-radius: 6px;
    margin-bottom: 5px;
    transition: background-color 0.2s ease, color 0.2s ease;
    font-size: 0.9em;
}

#notes-folder-list li:hover {
    background-color: var(--accent-bg);
    color: var(--primary-text);
}

#notes-folder-list li.active {
    background-color: var(--accent-bg-dark); /* 深色主题激活 */
    color: var(--primary-text-dark);
    font-weight: 600;
}
html[data-theme='light'] #notes-folder-list li.active {
    background-color: var(--accent-bg-light); /* 浅色主题激活 */
    color: var(--primary-text-light);
}


.notes-content-area {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto; /* Allow scrolling for notes list */
    max-height: 100%;
}

.notes-toolbar {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 10px;
    background-color: var(--secondary-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.notes-toolbar button,
.notes-toolbar select,
.notes-toolbar input[type="search"] {
    padding: 8px 12px;
    font-size: 0.9em;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    cursor: pointer;
}
.notes-toolbar input[type="search"] {
    flex-grow: 1; /* 让搜索框占据更多空间 */
    max-width: 300px; /* 但不要无限宽 */
}
.notes-toolbar button:disabled,
.notes-toolbar select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.notes-toolbar button:hover:not(:disabled) {
    background-color: var(--accent-bg);
}


#notes-list-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); /* Responsive grid */
    gap: 15px;
    padding: 5px; /* Small padding around the grid */
    overflow-y: auto; /* If content overflows the content area */
}

.note-card {
    background-color: var(--tertiary-bg);
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: box-shadow 0.2s ease, transform 0.2s ease;
    overflow: hidden; /* To contain content */
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* Push actions to bottom */
    min-height: 120px; /* Minimum height for cards */
}

.note-card:hover {
    box-shadow: 0 0 12px var(--highlight-text-dark); /* 使用高亮色的阴影，根据主题调整透明度 */
    transform: translateY(-2px);
}
html[data-theme='light'] .note-card:hover {
    box-shadow: 0 0 12px var(--highlight-text-light);
}

.note-card.selected {
    border-left: 4px solid var(--highlight-text);
    box-shadow: 0 0 15px var(--highlight-text-dark); /* 使用高亮色的阴影，根据主题调整透明度 */
}
html[data-theme='light'] .note-card.selected {
    box-shadow: 0 0 15px var(--highlight-text-light);
}



.note-card-filename {
    font-weight: 600;
    color: var(--primary-text);
    font-size: 0.95em;
    margin-bottom: 8px;
    word-break: break-all; /* Prevent long filenames from breaking layout */
}

.note-card-preview {
    font-size: 0.85em;
    color: var(--secondary-text);
    margin-bottom: 10px;
    flex-grow: 1; /* Allow preview to take available space */
    max-height: 60px; /* Limit preview height */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* Limit to 3 lines */
    -webkit-box-orient: vertical;
}
.note-card-actions {
    margin-top: auto; /* Push to bottom */
    display: flex;
    justify-content: flex-end; /* Align actions to the right */
    gap: 8px;
}
.note-card-actions button {
    font-size: 0.8em;
    padding: 5px 8px;
    background-color: var(--button-bg);
    border-radius: 6px; 
    border: none; 
    color: white; 
    cursor: pointer; 
    transition: background-color 0.2s ease; 
}
.note-card-actions button:hover {
    background-color: var(--button-hover-bg);
}
html[data-theme='light'] .note-card-actions button {
    background-color: var(--button-bg-light);
    color: var(--primary-text-light);
}
html[data-theme='light'] .note-card-actions button:hover {
    background-color: var(--button-hover-bg-light);
}



.note-editor-area {
    padding: 20px;
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-top: 15px; 
    max-width: 1175px; 
    margin-left: 0; 
    margin-right: auto; 
}
.note-editor-area h3 {
    font-size: 1.3em;
    color: var(--highlight-text);
    margin-top: 0;
    margin-bottom: 15px;
}
#note-content-editor {
    width: 100%; 
    min-height: 300px; 
    resize: both; 
    background-color: var(--input-bg);
    color: var(--primary-text);
    border: 1px solid var(--border-color);
}

.status-message {
    font-size: 0.85em;
    margin-left: 10px;
}
.status-message.success {
    color: var(--button-bg); /* 或特定成功色 */
}
.status-message.error {
    color: var(--danger-color);
}
.status-message.info {
    color: var(--highlight-text);
}
html[data-theme='light'] .status-message.success {
    color: var(--button-bg-light);
}
html[data-theme='light'] .status-message.error {
    color: var(--danger-color-light);
}
html[data-theme='light'] .status-message.info {
    color: var(--highlight-text-light);
}


/* Agent Files Editor Styles */
.agent-editor-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--secondary-bg); 
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Agent卡片样式增强 */
.agent-card {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible; /* 修改为visible以显示删除按钮 */
}

.agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 删除按钮样式 */
.agent-card .delete-button {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
}

.agent-card:hover .delete-button {
    opacity: 1;
    transform: scale(1);
}

.agent-card .delete-button:hover {
    background-color: #FEE2E2;
    color: #EF4444;
    transform: scale(1.1);
}

.agent-card .delete-button:active {
    transform: scale(0.95);
}

/* 新增：创建Agent对话框样式 */
#create-agent-dialog {
    backdrop-filter: blur(8px);
    transition: opacity 0.3s ease;
}

#create-agent-dialog .bg-white {
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#create-agent-dialog:not(.hidden) .bg-white {
    transform: scale(1);
    opacity: 1;
}

#create-agent-dialog input,
#create-agent-dialog textarea {
    transition: all 0.2s ease;
    background-color: var(--input-bg);
    color: var(--primary-text);
}

#create-agent-dialog input:focus,
#create-agent-dialog textarea:focus {
    border-color: var(--highlight-text);
    box-shadow: 0 0 0 3px var(--highlight-text-dark);
}

html[data-theme='light'] #create-agent-dialog input:focus,
html[data-theme='light'] #create-agent-dialog textarea:focus {
    box-shadow: 0 0 0 3px var(--highlight-text-light);
}

#create-agent-dialog button {
    transition: all 0.2s ease;
}

#create-agent-dialog button:hover {
    transform: translateY(-1px);
}

#create-agent-dialog button:active {
    transform: translateY(0);
}

.agent-editor-controls label {
    font-weight: 600;
    color: var(--primary-text);
}

#agent-file-select {
    min-width: 200px; 
    padding: 8px 12px;
    font-size: 0.9em;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    cursor: pointer;
}
#agent-file-select:focus {
    outline: none;
    border-color: var(--highlight-text);
    box-shadow: 0 0 0 3px var(--highlight-text-dark); /* 使用高亮色的阴影，根据主题调整透明度 */
}
html[data-theme='light'] #agent-file-select:focus {
    box-shadow: 0 0 0 3px var(--highlight-text-light);
}



#save-agent-file-button {
    padding: 8px 16px; 
    color: white; 
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 500;
    background-color: var(--button-bg); 
    transition: background-color 0.2s ease, transform 0.1s ease;
}
#save-agent-file-button:hover:not(:disabled) {
    background-color: var(--button-hover-bg);
    transform: translateY(-1px);
}
html[data-theme='light'] #save-agent-file-button {
    background-color: var(--button-bg-light);
    color: var(--primary-text-light);
}
html[data-theme='light'] #save-agent-file-button:hover:not(:disabled) {
    background-color: var(--button-hover-bg-light);
}

#save-agent-file-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#agent-file-content-editor {
    width: 100%;
    min-height: 400px; 
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace; 
    font-size: 0.95em;
    box-sizing: border-box;
    resize: vertical; 
}

#agent-file-content-editor:focus {
    outline: none;
    border-color: var(--highlight-text);
    box-shadow: 0 0 0 3px var(--highlight-text-dark); /* 使用高亮色的阴影，根据主题调整透明度 */
}
html[data-theme='light'] #agent-file-content-editor:focus {
    box-shadow: 0 0 0 3px var(--highlight-text-light);
}


#agent-file-status {
    margin-left: auto; 
}

/* Server Log Viewer Styles */
.server-log-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: var(--secondary-bg); 
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

#copy-server-log-button {
    padding: 8px 16px;
    color: white; 
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 500;
    background-color: var(--button-bg); 
    transition: background-color 0.2s ease, transform 0.1s ease;
}
#copy-server-log-button:hover {
    background-color: var(--button-hover-bg);
    transform: translateY(-1px);
}
html[data-theme='light'] #copy-server-log-button {
    background-color: var(--button-bg-light);
    color: var(--primary-text-light);
}
html[data-theme='light'] #copy-server-log-button:hover {
    background-color: var(--button-hover-bg-light);
}


#server-log-path-display {
    font-size: 0.85em;
    color: var(--secondary-text);
    flex-grow: 1; 
}

#server-log-status {
    margin-left: auto; 
}

.log-content-area {
    background-color: var(--input-bg); 
    color: var(--primary-text); 
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    height: 60vh; 
    overflow-y: auto; 
    white-space: pre-wrap; 
    word-break: break-all; 
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace; 
    font-size: 0.9em;
    line-height: 1.5;
}

/* 密钥字段的特殊样式 */
.input-with-toggle {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-toggle input {
    flex: 1;
    padding-right: 40px; /* 为切换按钮留出空间 */
}

.password-toggle {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: var(--secondary-text);
    font-size: 16px;
    border-radius: 4px;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.password-toggle:hover {
    background-color: var(--accent-bg);
    color: var(--primary-text);
}

.password-toggle:focus {
    outline: 2px solid var(--highlight-text);
    outline-offset: 2px;
}

/* 配置状态指示器样式 */
.defined-in {
    font-size: 0.85em;
    color: #888;
    font-style: italic;
}

.form-group .description .defined-in {
    display: inline-block;
    margin-left: 8px;
    padding: 2px 8px;
    background-color: var(--accent-bg);
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 500;
}

/* 插件状态徽章 */
.plugin-disabled-badge,
.plugin-disabled-badge-title {
    background-color: var(--danger-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 500;
    margin-left: 8px;
}

.plugin-type-icon {
    font-size: 0.9em;
    margin-left: 6px;
}

/* 表单字段的password类型样式 */
.form-group input[type="password"] {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 0.95em;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    font-family: 'Courier New', Courier, monospace; /* 等宽字体显示密钥 */
}

.form-group input[type="password"]:focus {
    outline: none;
    border-color: var(--highlight-text);
    box-shadow: 0 0 0 3px rgba(111, 168, 220, 0.2);
}

html[data-theme='light'] .form-group input[type="password"]:focus {
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* 配置状态指示器样式优化 */
.required-field {
    color: #e74c3c;
    font-weight: bold;
    font-size: 0.9em;
}

/* 配置项状态徽章 */
.config-status-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75em;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 8px;
}

.config-status-badge.configured {
    background-color: #27ae60;
    color: white;
}

.config-status-badge.empty {
    background-color: #f39c12;
    color: white;
}

.config-status-badge.inherited {
    background-color: #3498db;
    color: white;
}

.config-status-badge.required {
    background-color: #e74c3c;
    color: white;
}

/* 敏感值输入框样式 */
.sensitive-input-group {
    position: relative;
}

.sensitive-toggle {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    z-index: 10;
}

.sensitive-toggle:hover {
    color: #333;
}

/* 表单组改进 */
.form-group {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #dee2e6;
}

.form-group.has-value {
    border-left-color: #28a745;
}

.form-group.required {
    border-left-color: #dc3545;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: 0;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 插件状态徽章 */
.plugin-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 8px;
}

.plugin-status.enabled {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.plugin-status.disabled {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 配置项分组标题 */
.config-section-title {
    color: #495057;
    font-size: 1.1em;
    font-weight: 600;
    margin: 25px 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #dee2e6;
}

/* 注释和空行样式 */
.form-group-comment {
    margin-bottom: 10px;
}

.form-group-comment pre {
    background-color: #f1f3f4;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 0;
    font-size: 13px;
    color: #6c757d;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-group {
        padding: 10px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions button {
        width: 100%;
    }
}
