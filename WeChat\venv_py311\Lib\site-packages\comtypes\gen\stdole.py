from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    FON<PERSON><PERSON>LIC, FONTSIZ<PERSON>, <PERSON>LE_HANDLE, DISPPROPERTY, OLE_XSIZE_PIXELS,
    StdPicture, GUID, <PERSON><PERSON><PERSON>ARAMS, <PERSON><PERSON>ult, OLE_YSIZE_CONTAINER,
    IPictureDisp, DISPMETHOD, VARIANT_BOOL, Library, OLE_OPTEXCLUSIVE,
    IUnknown, Color, Gray, Picture, OLE_YSIZE_HIMETRIC, F<PERSON>TNA<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>THROUGH, IDispatch, IPicture, OLE_XPOS_HIMETRIC,
    Checked, OLE_YPOS_HIMETRIC, COMMETHOD, OLE_ENABLEDEFAULTBOOL,
    OLE_XPOS_CONTAINER, BSTR, IFontDisp, _check_version,
    IFontEventsDisp, EXCEPINFO, OLE_YSIZE_PIXELS, typelib_path, _lcid,
    HRESULT, CoClass, IFont, IEnumVARIANT, FontEvents,
    OLE_XSIZE_HIMETRIC, OLE_YPOS_CONTAINER, dispid, OLE_XPOS_PIXELS,
    Monochrome, VgaColor, OLE_YPOS_PIXELS, OLE_XSIZE_CONTAINER,
    OLE_COLOR, FONTUNDERSCORE, Font, FONTBOLD, StdFont, Unchecked,
    OLE_CANCELBOOL
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'FONTITALIC', 'FONTSIZE', 'OLE_HANDLE', 'IFontEventsDisp',
    'OLE_XSIZE_PIXELS', 'typelib_path', 'OLE_YSIZE_PIXELS',
    'StdPicture', 'IFont', 'Default', 'OLE_YSIZE_CONTAINER',
    'IPictureDisp', 'FontEvents', 'Library', 'OLE_XSIZE_HIMETRIC',
    'OLE_YPOS_CONTAINER', 'OLE_OPTEXCLUSIVE', 'OLE_XPOS_PIXELS',
    'Monochrome', 'Gray', 'VgaColor', 'Color', 'Picture',
    'OLE_YPOS_PIXELS', 'LoadPictureConstants', 'OLE_YSIZE_HIMETRIC',
    'FONTNAME', 'FONTSTRIKETHROUGH', 'OLE_XSIZE_CONTAINER',
    'IPicture', 'OLE_COLOR', 'OLE_XPOS_HIMETRIC', 'Checked',
    'OLE_YPOS_HIMETRIC', 'FONTUNDERSCORE', 'OLE_ENABLEDEFAULTBOOL',
    'Font', 'OLE_XPOS_CONTAINER', 'OLE_TRISTATE', 'FONTBOLD',
    'StdFont', 'IFontDisp', 'Unchecked', 'OLE_CANCELBOOL'
]

