/**
 * 高级情绪稳态调节系统
 * 避免二极管现象，实现更平滑的状态变化和详细的调节机制
 * 基于神经科学和心理学原理
 */

class AdvancedEmotionalStabilizationSystem {
    constructor(config, logger) {
        this.config = config;
        this.logger = logger;
        
        // 情绪稳态参数
        this.emotionalParameters = {
            // 基线回归参数
            baselineRegression: {
                emotionTarget: 0,        // 情绪基线目标
                stressTarget: 0,         // 压力基线目标
                affinityTarget: 50,      // 好感度基线目标
                regressionRate: 0.02,    // 回归速率
                regressionThreshold: 5   // 开始回归的阈值
            },
            
            // 稳定性控制参数
            stabilityControl: {
                maxChangePerStep: {
                    emotion: 3.0,        // 情绪最大单步变化
                    stress: 2.0,         // 压力最大单步变化
                    affinity: 1.5,       // 好感度最大单步变化
                    meme: 0.05           // 模因最大单步变化
                },
                dampingFactors: {
                    emotion: 0.8,        // 情绪阻尼系数
                    stress: 0.9,         // 压力阻尼系数
                    affinity: 0.85,      // 好感度阻尼系数
                    meme: 0.95           // 模因阻尼系数
                },
                inertiaFactors: {
                    emotion: 0.1,        // 情绪惯性系数
                    stress: 0.15,        // 压力惯性系数
                    affinity: 0.05,      // 好感度惯性系数
                    meme: 0.02           // 模因惯性系数
                }
            },
            
            // 非线性调节参数
            nonlinearRegulation: {
                sigmoidSteepness: 2.0,   // Sigmoid函数陡峭度
                exponentialDecay: 0.1,   // 指数衰减系数
                logarithmicGrowth: 0.3,  // 对数增长系数
                thresholdSensitivity: 0.8 // 阈值敏感性
            },
            
            // 适应性参数
            adaptiveParameters: {
                learningRate: 0.01,      // 学习速率
                memoryDecay: 0.99,       // 记忆衰减
                adaptationThreshold: 0.7, // 适应阈值
                plasticityWindow: 10     // 可塑性窗口
            }
        };
        
        // 历史状态记录（用于计算趋势和惯性）
        this.stateHistory = {
            emotion: [],
            stress: [],
            affinity: [],
            meme: []
        };
        
        // 自适应调节器状态
        this.adaptiveState = {
            emotionRegulator: { gain: 1.0, bias: 0.0, lastError: 0.0 },
            stressRegulator: { gain: 1.0, bias: 0.0, lastError: 0.0 },
            affinityRegulator: { gain: 1.0, bias: 0.0, lastError: 0.0 },
            memeRegulator: { gain: 1.0, bias: 0.0, lastError: 0.0 }
        };
    }

    /**
     * 应用高级情绪稳态调节
     */
    async applyAdvancedStabilization(changes, currentStates, analysisContext) {
        try {
            this.logger.info('情绪稳态', '开始高级情绪稳态调节');

            // 1. 更新状态历史
            this.updateStateHistory(currentStates);
            
            // 2. 计算趋势和惯性
            const trends = this.calculateStateTrends();
            const inertia = this.calculateStateInertia();
            
            // 3. 应用多层次稳态调节
            const stabilizedChanges = {};
            
            // 情绪稳态调节
            stabilizedChanges.emotion_change = await this.stabilizeEmotionChange(
                changes.emotion_change || 0,
                currentStates.emotion,
                trends.emotion,
                inertia.emotion,
                analysisContext
            );
            
            // 压力稳态调节
            stabilizedChanges.stress_change = await this.stabilizeStressChange(
                changes.stress_change || 0,
                currentStates.stress,
                trends.stress,
                inertia.stress,
                analysisContext
            );
            
            // 好感度稳态调节
            stabilizedChanges.affinity_change = await this.stabilizeAffinityChange(
                changes.affinity_change || 0,
                currentStates.affinity,
                trends.affinity,
                inertia.affinity,
                analysisContext
            );
            
            // 模因稳态调节
            stabilizedChanges.meme_change = await this.stabilizeMemeChange(
                changes.meme_change || 0,
                currentStates.meme,
                trends.meme,
                inertia.meme,
                analysisContext
            );
            
            // 4. 系统间协调调节
            const coordinatedChanges = this.applySystemCoordination(stabilizedChanges, currentStates);
            
            // 5. 更新自适应调节器
            this.updateAdaptiveRegulators(coordinatedChanges, currentStates);
            
            // 6. 生成稳态报告
            const stabilityReport = this.generateStabilityReport(coordinatedChanges, currentStates, trends);

            this.logger.success('情绪稳态', '高级情绪稳态调节完成');
            
            return {
                stabilized_changes: coordinatedChanges,
                stability_report: stabilityReport,
                trends: trends,
                inertia: inertia,
                adaptive_state: this.adaptiveState
            };

        } catch (error) {
            this.logger.error('情绪稳态', '稳态调节失败:', error.message);
            throw error;
        }
    }

    /**
     * 稳定情绪变化
     */
    async stabilizeEmotionChange(rawChange, currentEmotion, trend, inertia, context) {
        try {
            const currentValue = currentEmotion?.emotion_value || 0;
            const regulator = this.adaptiveState.emotionRegulator;
            
            // 1. 基础稳态调节
            let stabilizedChange = this.applyBaselineRegression(
                rawChange,
                currentValue,
                this.emotionalParameters.baselineRegression.emotionTarget,
                'emotion'
            );
            
            // 2. 非线性调节（避免极端变化）
            stabilizedChange = this.applyNonlinearRegulation(
                stabilizedChange,
                currentValue,
                'emotion'
            );
            
            // 3. 趋势和惯性调节
            stabilizedChange = this.applyTrendInertiaRegulation(
                stabilizedChange,
                trend,
                inertia,
                'emotion'
            );
            
            // 4. 自适应PID调节
            stabilizedChange = this.applyAdaptivePIDControl(
                stabilizedChange,
                currentValue,
                regulator,
                'emotion'
            );
            
            // 5. 上下文敏感调节
            stabilizedChange = this.applyContextSensitiveRegulation(
                stabilizedChange,
                context,
                'emotion'
            );
            
            // 6. 最终安全约束
            stabilizedChange = this.applySafetyConstraints(
                stabilizedChange,
                currentValue,
                'emotion'
            );
            
            return stabilizedChange;

        } catch (error) {
            this.logger.error('情绪稳态', '情绪稳定化失败:', error.message);
            return rawChange * 0.5; // 降级处理
        }
    }

    /**
     * 稳定压力变化
     */
    async stabilizeStressChange(rawChange, currentStress, trend, inertia, context) {
        try {
            const currentValue = currentStress?.stress_value || 0;
            const regulator = this.adaptiveState.stressRegulator;
            
            // 压力有特殊的生理学特性
            let stabilizedChange = rawChange;
            
            // 1. 压力特异性调节（压力更容易上升，更难下降）
            if (stabilizedChange > 0) {
                // 压力上升时的调节
                stabilizedChange = this.applyStressEscalationRegulation(stabilizedChange, currentValue);
            } else {
                // 压力下降时的调节（更慢的恢复）
                stabilizedChange = this.applyStressRecoveryRegulation(stabilizedChange, currentValue);
            }
            
            // 2. 基础稳态调节
            stabilizedChange = this.applyBaselineRegression(
                stabilizedChange,
                currentValue,
                this.emotionalParameters.baselineRegression.stressTarget,
                'stress'
            );
            
            // 3. 非线性调节
            stabilizedChange = this.applyNonlinearRegulation(
                stabilizedChange,
                currentValue,
                'stress'
            );
            
            // 4. 趋势和惯性调节
            stabilizedChange = this.applyTrendInertiaRegulation(
                stabilizedChange,
                trend,
                inertia,
                'stress'
            );
            
            // 5. 自适应调节
            stabilizedChange = this.applyAdaptivePIDControl(
                stabilizedChange,
                currentValue,
                regulator,
                'stress'
            );
            
            // 6. 上下文调节
            stabilizedChange = this.applyContextSensitiveRegulation(
                stabilizedChange,
                context,
                'stress'
            );
            
            // 7. 安全约束
            stabilizedChange = this.applySafetyConstraints(
                stabilizedChange,
                currentValue,
                'stress'
            );
            
            return stabilizedChange;

        } catch (error) {
            this.logger.error('情绪稳态', '压力稳定化失败:', error.message);
            return rawChange * 0.6; // 降级处理
        }
    }

    /**
     * 应用基线回归
     */
    applyBaselineRegression(change, currentValue, target, dimension) {
        const deviation = currentValue - target;
        const regressionForce = -deviation * this.emotionalParameters.baselineRegression.regressionRate;
        
        // 只有当偏离基线超过阈值时才应用回归力
        if (Math.abs(deviation) > this.emotionalParameters.baselineRegression.regressionThreshold) {
            return change + regressionForce;
        }
        
        return change;
    }

    /**
     * 应用非线性调节
     */
    applyNonlinearRegulation(change, currentValue, dimension) {
        const params = this.emotionalParameters.nonlinearRegulation;
        
        // 使用Sigmoid函数进行软约束
        const normalizedValue = currentValue / 100; // 归一化到[-1, 1]
        const sigmoidFactor = 1 / (1 + Math.exp(params.sigmoidSteepness * Math.abs(normalizedValue)));
        
        // 极端值时减少变化幅度
        const regulatedChange = change * sigmoidFactor;
        
        // 应用指数衰减（距离零点越远，变化越困难）
        const decayFactor = Math.exp(-params.exponentialDecay * Math.abs(normalizedValue));
        
        return regulatedChange * decayFactor;
    }

    /**
     * 应用趋势和惯性调节
     */
    applyTrendInertiaRegulation(change, trend, inertia, dimension) {
        const inertiaFactor = this.emotionalParameters.stabilityControl.inertiaFactors[dimension];
        const dampingFactor = this.emotionalParameters.stabilityControl.dampingFactors[dimension];
        
        // 惯性调节：如果变化方向与趋势一致，稍微增强；如果相反，稍微减弱
        let inertiaAdjustment = 0;
        if (Math.sign(change) === Math.sign(trend)) {
            inertiaAdjustment = inertia * inertiaFactor;
        } else {
            inertiaAdjustment = -inertia * inertiaFactor * 0.5;
        }
        
        // 阻尼调节：减少过度振荡
        const dampedChange = change * dampingFactor + inertiaAdjustment;
        
        return dampedChange;
    }

    /**
     * 应用自适应PID控制
     */
    applyAdaptivePIDControl(change, currentValue, regulator, dimension) {
        const target = this.getTargetValue(dimension);
        const error = currentValue - target;
        
        // PID控制参数
        const Kp = regulator.gain;
        const Ki = 0.1;
        const Kd = 0.05;
        
        // 比例项
        const proportional = Kp * error;
        
        // 积分项（累积误差）
        regulator.bias += error * Ki;
        regulator.bias = Math.max(-10, Math.min(10, regulator.bias)); // 限制积分饱和
        
        // 微分项（误差变化率）
        const derivative = Kd * (error - regulator.lastError);
        regulator.lastError = error;
        
        // PID输出（作为对原始变化的修正）
        const pidCorrection = -(proportional + regulator.bias + derivative) * 0.1;
        
        return change + pidCorrection;
    }

    /**
     * 更新状态历史
     */
    updateStateHistory(currentStates) {
        const maxHistoryLength = 20;
        
        // 更新情绪历史
        if (currentStates.emotion) {
            this.stateHistory.emotion.push(currentStates.emotion.emotion_value || 0);
            if (this.stateHistory.emotion.length > maxHistoryLength) {
                this.stateHistory.emotion.shift();
            }
        }
        
        // 更新压力历史
        if (currentStates.stress) {
            this.stateHistory.stress.push(currentStates.stress.stress_value || 0);
            if (this.stateHistory.stress.length > maxHistoryLength) {
                this.stateHistory.stress.shift();
            }
        }
        
        // 更新好感度历史
        if (currentStates.affinity) {
            this.stateHistory.affinity.push(currentStates.affinity.affinity_value || 50);
            if (this.stateHistory.affinity.length > maxHistoryLength) {
                this.stateHistory.affinity.shift();
            }
        }
        
        // 更新模因历史
        if (currentStates.meme) {
            this.stateHistory.meme.push(currentStates.meme.memetic_influence || 0.5);
            if (this.stateHistory.meme.length > maxHistoryLength) {
                this.stateHistory.meme.shift();
            }
        }
    }

    /**
     * 计算状态趋势
     */
    calculateStateTrends() {
        const trends = {};
        
        for (const [dimension, history] of Object.entries(this.stateHistory)) {
            if (history.length < 3) {
                trends[dimension] = 0;
                continue;
            }
            
            // 使用线性回归计算趋势
            const n = history.length;
            const x = Array.from({length: n}, (_, i) => i);
            const y = history;
            
            const sumX = x.reduce((a, b) => a + b, 0);
            const sumY = y.reduce((a, b) => a + b, 0);
            const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
            const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
            
            const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
            trends[dimension] = slope;
        }
        
        return trends;
    }

    /**
     * 获取目标值
     */
    getTargetValue(dimension) {
        switch (dimension) {
            case 'emotion':
                return this.emotionalParameters.baselineRegression.emotionTarget;
            case 'stress':
                return this.emotionalParameters.baselineRegression.stressTarget;
            case 'affinity':
                return this.emotionalParameters.baselineRegression.affinityTarget;
            case 'meme':
                return 0.5;
            default:
                return 0;
        }
    }

    /**
     * 计算状态惯性
     */
    calculateStateInertia() {
        const inertia = {};

        for (const [dimension, history] of Object.entries(this.stateHistory)) {
            if (history.length < 2) {
                inertia[dimension] = 0;
                continue;
            }

            // 计算最近几次变化的平均值作为惯性
            const recentChanges = [];
            for (let i = 1; i < history.length; i++) {
                recentChanges.push(history[i] - history[i-1]);
            }

            const avgChange = recentChanges.reduce((a, b) => a + b, 0) / recentChanges.length;
            inertia[dimension] = avgChange;
        }

        return inertia;
    }

    /**
     * 稳定好感度变化
     */
    async stabilizeAffinityChange(rawChange, currentAffinity, trend, inertia, context) {
        try {
            const currentValue = currentAffinity?.affinity_value || 50;
            const regulator = this.adaptiveState.affinityRegulator;

            let stabilizedChange = rawChange;

            // 1. 好感度特异性调节（好感度变化相对缓慢）
            stabilizedChange = this.applyAffinitySpecificRegulation(stabilizedChange, currentValue);

            // 2. 基础稳态调节
            stabilizedChange = this.applyBaselineRegression(
                stabilizedChange,
                currentValue,
                this.emotionalParameters.baselineRegression.affinityTarget,
                'affinity'
            );

            // 3. 非线性调节
            stabilizedChange = this.applyNonlinearRegulation(
                stabilizedChange,
                currentValue,
                'affinity'
            );

            // 4. 趋势和惯性调节
            stabilizedChange = this.applyTrendInertiaRegulation(
                stabilizedChange,
                trend,
                inertia,
                'affinity'
            );

            // 5. 自适应调节
            stabilizedChange = this.applyAdaptivePIDControl(
                stabilizedChange,
                currentValue,
                regulator,
                'affinity'
            );

            // 6. 上下文调节
            stabilizedChange = this.applyContextSensitiveRegulation(
                stabilizedChange,
                context,
                'affinity'
            );

            // 7. 安全约束
            stabilizedChange = this.applySafetyConstraints(
                stabilizedChange,
                currentValue,
                'affinity'
            );

            return stabilizedChange;

        } catch (error) {
            this.logger.error('情绪稳态', '好感度稳定化失败:', error.message);
            return rawChange * 0.7;
        }
    }

    /**
     * 稳定模因变化
     */
    async stabilizeMemeChange(rawChange, currentMeme, trend, inertia, context) {
        try {
            const currentValue = currentMeme?.memetic_influence || 0.5;
            const regulator = this.adaptiveState.memeRegulator;

            let stabilizedChange = rawChange;

            // 1. 模因特异性调节（模因变化非常缓慢）
            stabilizedChange = this.applyMemeSpecificRegulation(stabilizedChange, currentValue);

            // 2. 非线性调节（模因在0-1范围内）
            stabilizedChange = this.applyMemeNonlinearRegulation(stabilizedChange, currentValue);

            // 3. 趋势和惯性调节
            stabilizedChange = this.applyTrendInertiaRegulation(
                stabilizedChange,
                trend,
                inertia,
                'meme'
            );

            // 4. 自适应调节
            stabilizedChange = this.applyAdaptivePIDControl(
                stabilizedChange,
                currentValue,
                regulator,
                'meme'
            );

            // 5. 安全约束
            stabilizedChange = this.applySafetyConstraints(
                stabilizedChange,
                currentValue,
                'meme'
            );

            return stabilizedChange;

        } catch (error) {
            this.logger.error('情绪稳态', '模因稳定化失败:', error.message);
            return rawChange * 0.5;
        }
    }

    /**
     * 应用压力上升调节
     */
    applyStressEscalationRegulation(change, currentValue) {
        // 压力上升时的非线性特性：低压力时容易上升，高压力时上升困难
        const normalizedStress = Math.abs(currentValue) / 25; // 归一化到[0, 2]
        const escalationFactor = 1 / (1 + normalizedStress * 0.5);

        return change * escalationFactor;
    }

    /**
     * 应用压力恢复调节
     */
    applyStressRecoveryRegulation(change, currentValue) {
        // 压力下降时的特性：恢复相对缓慢
        const recoveryRate = 0.7; // 恢复速率比上升慢

        // 高压力时恢复稍快
        const normalizedStress = Math.abs(currentValue) / 25;
        const recoveryBonus = normalizedStress * 0.2;

        return change * (recoveryRate + recoveryBonus);
    }

    /**
     * 应用好感度特异性调节
     */
    applyAffinitySpecificRegulation(change, currentValue) {
        // 好感度变化的特性：中等范围变化较容易，极端值变化困难
        const normalizedAffinity = Math.abs(currentValue - 50) / 50; // 距离中性点的距离
        const changeResistance = 1 / (1 + normalizedAffinity * 0.8);

        return change * changeResistance;
    }

    /**
     * 应用模因特异性调节
     */
    applyMemeSpecificRegulation(change, currentValue) {
        // 模因变化非常缓慢，需要大量积累
        const memeInertia = 0.3; // 模因惯性很大

        // 在0.3-0.7范围内变化相对容易
        const optimalRange = [0.3, 0.7];
        let facilitation = 1.0;

        if (currentValue >= optimalRange[0] && currentValue <= optimalRange[1]) {
            facilitation = 1.2; // 在最优范围内稍微容易变化
        } else {
            facilitation = 0.8; // 在极端值时变化困难
        }

        return change * memeInertia * facilitation;
    }

    /**
     * 应用模因非线性调节
     */
    applyMemeNonlinearRegulation(change, currentValue) {
        // 确保模因值保持在[0, 1]范围内
        const distanceFromBoundary = Math.min(currentValue, 1 - currentValue);
        const boundaryResistance = distanceFromBoundary < 0.1 ? 0.5 : 1.0;

        return change * boundaryResistance;
    }

    /**
     * 应用上下文敏感调节
     */
    applyContextSensitiveRegulation(change, context, dimension) {
        if (!context) return change;

        let contextMultiplier = 1.0;

        // 基于批量分析结果调整
        if (context.batchResults) {
            const batchResults = context.batchResults;

            switch (dimension) {
                case 'emotion':
                    // 基于情感强度调整
                    if (batchResults.emotion_analysis?.intensity) {
                        contextMultiplier *= (0.5 + batchResults.emotion_analysis.intensity * 0.5);
                    }
                    break;

                case 'stress':
                    // 基于压力因素数量调整
                    if (batchResults.stress_analysis?.stress_factors) {
                        const factorCount = batchResults.stress_analysis.stress_factors.length;
                        contextMultiplier *= (0.8 + Math.min(factorCount * 0.1, 0.4));
                    }
                    break;

                case 'affinity':
                    // 基于互动质量调整
                    if (batchResults.affinity_analysis?.interaction_quality) {
                        const qualityMap = {
                            'excellent': 1.3,
                            'good': 1.1,
                            'neutral': 1.0,
                            'poor': 0.8,
                            'terrible': 0.6
                        };
                        contextMultiplier *= qualityMap[batchResults.affinity_analysis.interaction_quality] || 1.0;
                    }
                    break;
            }
        }

        return change * contextMultiplier;
    }

    /**
     * 应用安全约束
     */
    applySafetyConstraints(change, currentValue, dimension) {
        const maxChange = this.emotionalParameters.stabilityControl.maxChangePerStep[dimension];

        // 限制最大变化幅度
        let constrainedChange = Math.max(-maxChange, Math.min(maxChange, change));

        // 确保不会超出合理范围
        const bounds = this.getDimensionBounds(dimension);
        const newValue = currentValue + constrainedChange;

        if (newValue < bounds.min) {
            constrainedChange = bounds.min - currentValue;
        } else if (newValue > bounds.max) {
            constrainedChange = bounds.max - currentValue;
        }

        return constrainedChange;
    }

    /**
     * 获取维度边界
     */
    getDimensionBounds(dimension) {
        switch (dimension) {
            case 'emotion':
                return { min: -100, max: 100 };
            case 'stress':
                return { min: -25, max: 25 };
            case 'affinity':
                return { min: -100, max: 100 };
            case 'meme':
                return { min: 0, max: 1 };
            default:
                return { min: -100, max: 100 };
        }
    }

    /**
     * 应用系统间协调调节
     */
    applySystemCoordination(changes, currentStates) {
        const coordinated = { ...changes };

        // 情绪和压力的相互影响
        if (Math.abs(coordinated.emotion_change) > 2 && Math.abs(coordinated.stress_change) > 1) {
            // 如果情绪和压力都有大幅变化，稍微减弱以避免过度反应
            coordinated.emotion_change *= 0.9;
            coordinated.stress_change *= 0.9;
        }

        // 好感度和情绪的协调
        if (coordinated.emotion_change > 0 && coordinated.affinity_change < 0) {
            // 正面情绪和负面好感度变化冲突时，减弱冲突
            const conflictReduction = 0.8;
            coordinated.affinity_change *= conflictReduction;
        }

        // 模因和其他状态的协调
        if (Math.abs(coordinated.meme_change) > 0.03) {
            // 模因大幅变化时，其他状态也会受到影响
            const memeInfluence = coordinated.meme_change * 0.5;
            coordinated.emotion_change += memeInfluence;
            coordinated.stress_change += Math.abs(memeInfluence) * 0.3;
        }

        return coordinated;
    }

    /**
     * 更新自适应调节器
     */
    updateAdaptiveRegulators(changes, currentStates) {
        const learningRate = this.emotionalParameters.adaptiveParameters.learningRate;

        for (const [dimension, change] of Object.entries(changes)) {
            const dimensionKey = dimension.replace('_change', '');
            const regulator = this.adaptiveState[dimensionKey + 'Regulator'];

            if (regulator) {
                // 基于变化幅度调整增益
                const changeIntensity = Math.abs(change);
                if (changeIntensity > this.emotionalParameters.stabilityControl.maxChangePerStep[dimensionKey] * 0.8) {
                    // 变化过大时降低增益
                    regulator.gain = Math.max(0.5, regulator.gain - learningRate);
                } else if (changeIntensity < this.emotionalParameters.stabilityControl.maxChangePerStep[dimensionKey] * 0.2) {
                    // 变化过小时提高增益
                    regulator.gain = Math.min(1.5, regulator.gain + learningRate);
                }
            }
        }
    }

    /**
     * 生成稳定性报告
     */
    generateStabilityReport(changes, currentStates, trends) {
        const report = {
            stability_score: this.calculateOverallStability(changes),
            change_analysis: {},
            trend_analysis: trends,
            recommendations: []
        };

        // 分析各维度变化
        for (const [dimension, change] of Object.entries(changes)) {
            const dimensionKey = dimension.replace('_change', '');
            const maxChange = this.emotionalParameters.stabilityControl.maxChangePerStep[dimensionKey];

            report.change_analysis[dimension] = {
                change_value: change,
                change_ratio: Math.abs(change) / maxChange,
                stability_level: this.getStabilityLevel(Math.abs(change), maxChange)
            };
        }

        // 生成建议
        if (report.stability_score < 0.7) {
            report.recommendations.push('系统稳定性较低，建议减少刺激强度');
        }

        if (Math.abs(changes.emotion_change) > 2) {
            report.recommendations.push('情绪变化较大，建议关注情绪调节');
        }

        if (changes.stress_change > 1.5) {
            report.recommendations.push('压力水平上升，建议采取减压措施');
        }

        return report;
    }

    /**
     * 计算整体稳定性
     */
    calculateOverallStability(changes) {
        const stabilities = [];

        for (const [dimension, change] of Object.entries(changes)) {
            const dimensionKey = dimension.replace('_change', '');
            const maxChange = this.emotionalParameters.stabilityControl.maxChangePerStep[dimensionKey];
            const stability = 1 - Math.min(1, Math.abs(change) / maxChange);
            stabilities.push(stability);
        }

        return stabilities.reduce((sum, val) => sum + val, 0) / stabilities.length;
    }

    /**
     * 获取稳定性等级
     */
    getStabilityLevel(change, maxChange) {
        const ratio = change / maxChange;

        if (ratio < 0.3) return '高度稳定';
        if (ratio < 0.6) return '稳定';
        if (ratio < 0.8) return '轻微波动';
        if (ratio < 1.0) return '中度波动';
        return '高度波动';
    }
}

module.exports = AdvancedEmotionalStabilizationSystem;
