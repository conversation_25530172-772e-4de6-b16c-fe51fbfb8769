# OneBot11 权限和优先级系统

## 📋 系统概述

OneBot11插件系统现已全面支持权限管理和优先级控制，提供了完整的插件开发框架和模板系统。

## 🔧 权限系统

### 权限级别

| 权限级别 | 说明 | 适用场景 |
|---------|------|----------|
| `all` | 所有用户可用 | 普通功能插件 |
| `admin` | 管理员可用 | 群管理、文件管理等 |
| `super_admin` | 超级管理员可用 | 系统配置、高级功能 |
| `master` | 主人可用 | 系统控制、敏感操作 |

### 权限配置

在 `config.json` 中配置权限：

```json
{
    "PERMISSIONS": {
        "SUPER_ADMINS": [123456789],
        "BOT_ADMINS": [987654321],
        "GROUP_ADMINS": {
            "群号1": [用户ID1, 用户ID2],
            "群号2": [用户ID3, 用户ID4]
        },
        "PERMISSION_DENIED_MESSAGE": "❌ 权限不足，该功能需要管理员权限",
        "ENABLE_PERMISSION_CHECK": true
    }
}
```

### 权限检查逻辑

1. **超级管理员** - 拥有所有权限
2. **机器人管理员** - 拥有admin级别权限
3. **群管理员** - 在对应群组中拥有admin权限
4. **群内角色** - 群主和管理员自动获得admin权限

## ⚡ 优先级系统

### 优先级规则

- **数值越小优先级越高**
- **相同优先级按加载顺序执行**
- **建议优先级范围**：

| 优先级范围 | 插件类型 | 示例 |
|-----------|----------|------|
| 1-10 | 系统级插件 | 消息过滤、安全检查 |
| 11-30 | 管理类插件 | 群管理、权限控制 |
| 31-70 | 功能类插件 | 文件处理、图片发送 |
| 71-100 | 娱乐类插件 | 聊天机器人、游戏 |
| 101+ | 低优先级插件 | 日志记录、统计 |

### 优先级示例

```javascript
class MyPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('MyPlugin', adapter, logger, config);
        
        this.priority = 50; // 设置优先级
        this.permission = 'admin'; // 设置权限级别
    }
}
```

## 🏗️ 插件开发

### 使用插件模板

1. **复制模板文件**：
```bash
cp plugin_template.js my_new_plugin.js
```

2. **修改插件信息**：
```javascript
class MyNewPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('MyNewPlugin', adapter, logger, config);
        
        // 设置优先级和权限
        this.priority = 60;
        this.permission = 'all';
        
        // 设置触发条件
        this.triggers = {
            keywords: ['我的关键词'],
            commands: ['mycmd'],
            patterns: [],
            always: false
        };
        
        // 设置元信息
        this.meta = {
            version: '1.0.0',
            author: '我的名字',
            description: '我的插件描述',
            usage: '使用方法说明',
            example: '示例命令'
        };
    }
}
```

### 使用插件生成器

```javascript
const PluginGenerator = require('./plugin_generator');
const generator = new PluginGenerator();

// 生成新插件
const result = await generator.generatePlugin({
    name: '天气查询',
    className: 'WeatherPlugin',
    priority: 50,
    permission: 'all',
    keywords: ['天气', '气温'],
    commands: ['weather', 'tq'],
    description: '查询天气信息',
    author: 'VCPToolBox',
    version: '1.0.0'
});

console.log(result);
```

## 📊 插件管理

### 管理员插件功能

管理员可以使用以下命令：

- `插件管理` - 查看管理功能帮助
- `插件列表` - 查看所有插件
- `插件状态` - 查看插件运行状态
- `启用插件 [名称]` - 启用指定插件
- `禁用插件 [名称]` - 禁用指定插件
- `系统状态` - 查看系统运行状态
- `清理缓存` - 清理系统缓存

### 插件加载顺序

系统会按照以下顺序加载插件：

1. **按优先级排序** (数值小的先加载)
2. **相同优先级按plugin_order.txt顺序**
3. **显示加载信息**：
```
插件加载顺序 (按优先级排序):
  1. AdminPlugin (优先级: 20, 权限: admin)
  2. FileManagerPlugin (优先级: 50, 权限: all)
  3. ImageSenderPlugin (优先级: 60, 权限: all)
  4. FileReceiverPlugin (优先级: 70, 权限: all)
```

## 🔒 安全特性

### 权限检查

- **自动权限验证** - 每个消息都会检查权限
- **权限拒绝统计** - 记录权限拒绝次数
- **友好错误提示** - 权限不足时显示提示信息

### 黑白名单

```javascript
this.permissions = {
    allowedUsers: [123456], // 白名单用户
    allowedGroups: [789012], // 白名单群组
    blacklistUsers: [654321], // 黑名单用户
    blacklistGroups: [210987] // 黑名单群组
};
```

## 📈 统计功能

### 插件统计信息

每个插件都会记录：

- **触发次数** - 插件被触发的总次数
- **错误次数** - 插件处理失败的次数
- **权限拒绝次数** - 因权限不足被拒绝的次数
- **最后触发时间** - 最近一次触发的时间

### 查看统计

```javascript
// 获取插件统计
const stats = plugin.stats;
console.log(`触发次数: ${stats.triggered}`);
console.log(`错误次数: ${stats.errors}`);
console.log(`权限拒绝: ${stats.permissionDenied}`);
```

## 🎯 最佳实践

### 1. 权限设计

- **最小权限原则** - 只给插件必要的权限
- **分层权限管理** - 不同功能使用不同权限级别
- **权限文档化** - 在插件说明中明确权限要求

### 2. 优先级设计

- **功能相关性** - 相关功能使用相近优先级
- **性能考虑** - 高频插件使用较高优先级
- **依赖关系** - 被依赖的插件优先级更高

### 3. 错误处理

```javascript
async handle(context) {
    try {
        // 插件逻辑
        return { handled: true, message: '处理成功' };
    } catch (error) {
        this.logger.error('Plugin', `处理失败: ${error.message}`);
        await this.reply(context, `❌ 处理失败: ${error.message}`);
        return { handled: true, error: error.message };
    }
}
```

### 4. 插件元信息

```javascript
this.meta = {
    version: '1.0.0',
    author: '作者名称',
    description: '详细描述插件功能',
    usage: '使用方法和命令说明',
    example: '具体的使用示例'
};
```

## 🚀 快速开始

### 1. 创建新插件

```bash
# 复制模板
cp plugins/plugin_template.js plugins/my_plugin.js

# 编辑插件
vim plugins/my_plugin.js

# 添加到加载列表
echo "my_plugin" >> plugins/plugin_order.txt
```

### 2. 配置权限

```json
{
    "PERMISSIONS": {
        "SUPER_ADMINS": [你的QQ号],
        "ENABLE_PERMISSION_CHECK": true
    }
}
```

### 3. 重启服务

```bash
node bot.js
```

## 📞 技术支持

如果遇到问题：

1. **检查权限配置** - 确保config.json中权限配置正确
2. **查看日志输出** - 检查插件加载和权限检查日志
3. **验证插件语法** - 确保插件代码语法正确
4. **测试权限功能** - 使用不同权限用户测试功能

## 🎉 功能特色

- ✅ **完整权限系统** - 支持多级权限管理
- ✅ **智能优先级** - 自动按优先级排序执行
- ✅ **插件模板** - 快速创建标准化插件
- ✅ **自动生成器** - 工具化插件创建流程
- ✅ **管理界面** - 管理员可视化管理插件
- ✅ **统计监控** - 完整的插件运行统计
- ✅ **安全保护** - 黑白名单和权限验证
- ✅ **错误处理** - 完善的错误处理机制
