// Plugin/Mcp/SunoGen.js - Suno音乐生成MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}


class SunoGenMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'SunoGen';
        this.description = '使用Suno生成AI音乐';
        this.vcpName = 'SunoGen';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                prompt: {
                    type: 'string',
                    description: '音乐生成提示词，描述想要的音乐风格和内容'
                },
                make_instrumental: {
                    type: 'boolean',
                    description: '是否生成纯音乐（无人声）',
                    default: false
                },
                tags: {
                    type: 'string',
                    description: '音乐标签，描述音乐风格、流派等',
                    default: ''
                },
                continue_at: {
                    type: 'number',
                    description: '从指定时间点继续生成（秒）',
                    minimum: 0,
                    default: undefined
                }
            },
            required: ['prompt']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);
        
        this.log('info', `开始生成音乐`, {
            prompt: args.prompt,
            instrumental: args.make_instrumental || false
        });
        
        try {
        // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);
            
            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }
            
            // 构建标准响应格式
            const response = {
                type: 'music_generation',
                status: 'success',
                message: '音乐生成完成',
                data: {
                    prompt: args.prompt,
                    make_instrumental: args.make_instrumental || false,
                    tags: args.tags || '',
                    audio_url: parsedResult?.audio_url,
                    audio_path: parsedResult?.audio_path,
                    duration: parsedResult?.duration,
                    generation_info: {
                        model: 'Suno',
                        generation_time: parsedResult?.generation_time,
                        track_id: parsedResult?.track_id
                    }
                }
            };
            
            this.log('success', `音乐生成完成`, {
                prompt: args.prompt,
                audio_path: response.data.audio_path,
                duration: response.data.duration
            });
            
            return response;
            
        } catch (error) {
            const errorResponse = {
                type: 'music_generation',
                status: 'error',
                message: error.message,
                data: {
            prompt: args.prompt,
                    error: error.message
                }
            };
            
            this.log('error', `音乐生成失败`, errorResponse);
            throw errorResponse;
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查必要的环境变量
            const requiredEnvVars = ['SUNO_API_KEY'];
            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    throw new Error(`未配置${envVar}环境变量`);
                }
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = SunoGenMcp; 