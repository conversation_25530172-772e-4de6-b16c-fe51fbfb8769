# WeatherReporter Plugin

天气查询插件，支持获取指定城市的天气信息，包括当前天气、7天预报、24小时预报和天气预警。

## 功能特性

- **多种查询类型**：支持当前天气、7天预报、24小时预报、天气预警和全部信息
- **智能截断**：支持token智能截断，防止内容过长
- **缓存机制**：城市ID缓存提高查询效率
- **双模式运行**：支持静态模式（系统提示词占位符）和同步模式（工具调用）
- **MCP支持**：提供完整的MCP接口支持

## 配置说明

### 必需配置
```env
VarCity=北京                                    # 默认查询城市
WeatherKey=your_weather_api_key_here           # 和风天气API密钥
WeatherUrl=devapi.qweather.com                 # 和风天气API域名
```

### Token截断配置
```env
WEATHERREPORTER_TOKEN_TRUNCATE_ENABLED=true     # 是否启用token截断
WEATHERREPORTER_TOKEN_MAX_TOKENS=16000          # 最大token数量
WEATHERREPORTER_TOKEN_TRUNCATE_MARKER=...       # 截断标记文本
```

## 查询类型

| 类型 | 描述 | 返回内容 |
|------|------|----------|
| `current` | 当前天气 | 实时温度、湿度、风力等 |
| `forecast` | 7天预报 | 未来7天的天气预报 |
| `hourly` | 24小时预报 | 未来24小时的详细预报 |
| `warning` | 天气预警 | 当前有效的天气预警信息 |
| `all` | 全部信息 | 包含以上所有信息 |

## 使用方法

### VCP工具调用格式
```text
<<<[TOOL_REQUEST]>>>
tool_name:「始」WeatherReporter「末」,
query_type:「始」current「末」,
city:「始」北京「末」
<<<[END_TOOL_REQUEST]>>>
```

### MCP调用示例
```javascript
{
  "method": "tools/call",
  "params": {
    "name": "WeatherReporter",
    "arguments": {
      "query_type": "current",
      "city": "北京"
    }
  }
}
```

## 返回格式

```json
{
  "status": "success",
  "result": "格式化的天气信息",
  "token_info": {
    "original_tokens": 5000,
    "final_tokens": 4500,
    "truncated": true,
    "max_tokens": 5000,
    "truncate_enabled": true,
    "query_type": "current",
    "city": "北京"
  }
}
```

## 错误处理

插件包含多层错误处理机制：
1. **配置验证**：检查必需的API配置
2. **城市查找**：验证城市名称有效性
3. **API调用**：处理网络和API错误
4. **缓存回退**：API失败时使用缓存数据

## 缓存机制

- **城市ID缓存**：避免重复查询城市ID，永久保存
- **天气数据缓存**：智能缓存不同城市和查询类型的天气数据
- **每日刷新**：缓存数据每天自动过期，确保数据新鲜度
- **多级缓存**：
  - JSON缓存：存储结构化的天气数据，支持不同城市和查询类型
  - 静态缓存：为系统提示词占位符提供默认城市的全量天气信息
- **缓存策略**：优先使用缓存数据，减少API调用，提高响应速度

## API配置

使用和风天气API，需要：
1. 注册和风天气开发者账号
2. 获取API密钥
3. 选择合适的API域名（免费版/付费版）

## 注意事项

- 确保网络连接稳定
- API调用有频率限制
- 城市名称应使用中文
- Token截断可能影响信息完整性
- 缓存文件会自动创建和更新
