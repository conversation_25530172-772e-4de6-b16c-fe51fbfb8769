/**
 * Group Management Plugin
 * Provides group management functionality including member management and permission control
 */

const BasePlugin = require('../base_plugin');

class GroupManagementPlugin extends BasePlugin {
    constructor(adapter, logger, config) {
        super('GroupManagement', adapter, logger, config);

        this.version = '1.0.0';
        this.description = 'Group management with member control, permissions, and auto-moderation';

        // Set plugin properties
        this.priority = 30; // Medium-low priority
        this.triggers.commands = ['/kick', '/ban', '/unban', '/mute', '/unmute']; // Command triggers
        
        // Admin list
        this.admins = new Set();
        
        // Group settings
        this.groupSettings = new Map();
        
        // Statistics
        this.stats = {
            kickedUsers: 0,
            bannedUsers: 0,
            approvedRequests: 0,
            rejectedRequests: 0,
            warningsIssued: 0
        };
        
        // Violation records
        this.violationRecords = new Map();
        
        // Command prefix
        this.commandPrefix = '/';
    }

    /**
     * Plugin initialization
     */
    async initialize() {
        this.logger.info('GroupManagement', 'Initializing group management plugin...');
        
        // Load admin list
        this.loadAdmins();
        
        // Load group settings
        this.loadGroupSettings();
        
        // Register event handlers
        this.adapter.on('message', this.handleMessage.bind(this));
        this.adapter.on('notice', this.handleNotice.bind(this));
        
        this.logger.info('GroupManagement', 'Group management plugin initialized successfully');
    }

    /**
     * Load admin list from config
     */
    loadAdmins() {
        const groupConfig = this.config.GROUP_MANAGEMENT;
        
        if (groupConfig && groupConfig.ADMINS) {
            for (const adminId of groupConfig.ADMINS) {
                this.admins.add(adminId.toString());
            }
        }
        
        this.logger.info('GroupManagement', `Loaded ${this.admins.size} administrators`);
    }

    /**
     * Load group settings from config
     */
    loadGroupSettings() {
        const groupConfig = this.config.GROUP_MANAGEMENT;
        
        if (groupConfig && groupConfig.GROUP_SETTINGS) {
            for (const [groupId, settings] of Object.entries(groupConfig.GROUP_SETTINGS)) {
                this.groupSettings.set(groupId, {
                    autoKick: settings.autoKick || false,
                    autoBan: settings.autoBan || false,
                    welcomeMessage: settings.welcomeMessage || '',
                    maxViolations: settings.maxViolations || 3,
                    banDuration: settings.banDuration || 3600, // seconds
                    allowedCommands: settings.allowedCommands || [],
                    ...settings
                });
            }
        }
        
        this.logger.info('GroupManagement', `Loaded ${this.groupSettings.size} group settings`);
    }

    /**
     * Handle message (BasePlugin interface)
     */
    async handle(context) {
        const handled = await this.handleMessage(context.event);

        return {
            handled: handled || false,
            stopPropagation: false // Allow other plugins to process
        };
    }

    /**
     * Handle message events
     */
    async handleMessage(event) {
        try {
            // Only handle group messages
            if (event.post_type !== 'message' || event.message_type !== 'group') {
                return;
            }
            
            const { group_id, user_id, message } = event;
            
            // Check if it's a command
            if (message.startsWith(this.commandPrefix)) {
                await this.handleCommand(event);
                return;
            }
            
            // Check message content
            await this.checkMessageContent(event);
            
        } catch (error) {
            this.logger.error('GroupManagement', `Message handling failed: ${error.message}`);
        }
    }

    /**
     * Handle notice events
     */
    async handleNotice(event) {
        try {
            if (event.notice_type === 'group_increase') {
                await this.handleNewMember(event);
            }
        } catch (error) {
            this.logger.error('GroupManagement', `Notice handling failed: ${error.message}`);
        }
    }

    /**
     * Handle commands
     */
    async handleCommand(event) {
        const { group_id, user_id, message } = event;
        const args = message.slice(1).split(' ');
        const command = args[0].toLowerCase();
        
        // Check permissions
        if (!this.isAdmin(user_id)) {
            await this.sendGroupMessage(group_id, '❌ You do not have permission to use admin commands');
            return;
        }
        
        switch (command) {
            case 'kick':
                await this.handleKickCommand(event, args);
                break;
            case 'ban':
                await this.handleBanCommand(event, args);
                break;
            case 'unban':
                await this.handleUnbanCommand(event, args);
                break;
            case 'warn':
                await this.handleWarnCommand(event, args);
                break;
            case 'mute':
                await this.handleMuteCommand(event, args);
                break;
            case 'unmute':
                await this.handleUnmuteCommand(event, args);
                break;
            case 'info':
                await this.handleInfoCommand(event, args);
                break;
            case 'help':
                await this.handleHelpCommand(event);
                break;
            default:
                await this.sendGroupMessage(group_id, `❌ Unknown command: ${command}\nUse /help to see available commands`);
        }
    }

    /**
     * Kick member command
     */
    async handleKickCommand(event, args) {
        const { group_id } = event;
        
        if (args.length < 2) {
            await this.sendGroupMessage(group_id, '❌ Usage: /kick <user_id> [reason]');
            return;
        }
        
        const targetUserId = args[1];
        const reason = args.slice(2).join(' ') || 'Violation of group rules';
        
        try {
            // Create bot object for API call
            const bot = { ws: event.ws, selfId: event.self_id };
            await this.adapter.setGroupKick(bot, group_id, targetUserId);
            await this.sendGroupMessage(group_id, `✅ Kicked user ${targetUserId}\nReason: ${reason}`);
            
            this.stats.kickedUsers++;
            this.logger.info('GroupManagement', `Kicked user: ${targetUserId} from ${group_id}, reason: ${reason}`);
            
        } catch (error) {
            await this.sendGroupMessage(group_id, `❌ Kick failed: ${error.message}`);
        }
    }

    /**
     * Ban member command
     */
    async handleBanCommand(event, args) {
        const { group_id } = event;
        
        if (args.length < 2) {
            await this.sendGroupMessage(group_id, '❌ Usage: /ban <user_id> [duration_seconds] [reason]');
            return;
        }
        
        const targetUserId = args[1];
        const duration = parseInt(args[2]) || 3600; // Default 1 hour
        const reason = args.slice(3).join(' ') || 'Violation of group rules';
        
        try {
            // Create bot object for API call
            const bot = { ws: event.ws, selfId: event.self_id };
            await this.adapter.setGroupBan(bot, group_id, targetUserId, duration);
            await this.sendGroupMessage(group_id, `✅ Banned user ${targetUserId}\nDuration: ${duration}s\nReason: ${reason}`);
            
            this.stats.bannedUsers++;
            this.logger.info('GroupManagement', `Banned user: ${targetUserId} in ${group_id}, duration: ${duration}s, reason: ${reason}`);
            
        } catch (error) {
            await this.sendGroupMessage(group_id, `❌ Ban failed: ${error.message}`);
        }
    }

    /**
     * Unban member command
     */
    async handleUnbanCommand(event, args) {
        const { group_id } = event;
        
        if (args.length < 2) {
            await this.sendGroupMessage(group_id, '❌ Usage: /unban <user_id>');
            return;
        }
        
        const targetUserId = args[1];
        
        try {
            // Create bot object for API call
            const bot = { ws: event.ws, selfId: event.self_id };
            await this.adapter.setGroupBan(bot, group_id, targetUserId, 0);
            await this.sendGroupMessage(group_id, `✅ Unbanned user ${targetUserId}`);
            
            this.logger.info('GroupManagement', `Unbanned user: ${targetUserId} in ${group_id}`);
            
        } catch (error) {
            await this.sendGroupMessage(group_id, `❌ Unban failed: ${error.message}`);
        }
    }

    /**
     * Warn member command
     */
    async handleWarnCommand(event, args) {
        const { group_id } = event;
        
        if (args.length < 2) {
            await this.sendGroupMessage(group_id, '❌ Usage: /warn <user_id> [reason]');
            return;
        }
        
        const targetUserId = args[1];
        const reason = args.slice(2).join(' ') || 'Violation of group rules';
        
        // Record violation
        this.addViolation(targetUserId, group_id, reason);
        
        const violations = this.getViolationCount(targetUserId, group_id);
        const maxViolations = this.getGroupSetting(group_id, 'maxViolations', 3);
        
        await this.sendGroupMessage(group_id, `⚠️ Warning for user ${targetUserId}\nReason: ${reason}\nWarnings: ${violations}/${maxViolations}`);
        
        // Check if auto-action is needed
        if (violations >= maxViolations) {
            const autoKick = this.getGroupSetting(group_id, 'autoKick', false);
            const autoBan = this.getGroupSetting(group_id, 'autoBan', false);
            
            if (autoKick) {
                const bot = { ws: event.ws, selfId: event.self_id };
                await this.adapter.setGroupKick(bot, group_id, targetUserId);
                await this.sendGroupMessage(group_id, `🚫 User ${targetUserId} auto-kicked for multiple violations`);
            } else if (autoBan) {
                const banDuration = this.getGroupSetting(group_id, 'banDuration', 3600);
                const bot = { ws: event.ws, selfId: event.self_id };
                await this.adapter.setGroupBan(bot, group_id, targetUserId, banDuration);
                await this.sendGroupMessage(group_id, `🚫 User ${targetUserId} auto-banned for ${banDuration}s`);
            }
        }
        
        this.stats.warningsIssued++;
        this.logger.info('GroupManagement', `Warned user: ${targetUserId} in ${group_id}, reason: ${reason}, count: ${violations}`);
    }

    /**
     * Mute member command
     */
    async handleMuteCommand(event, args) {
        const { group_id } = event;
        
        if (args.length < 2) {
            await this.sendGroupMessage(group_id, '❌ Usage: /mute <user_id> [duration_seconds]');
            return;
        }
        
        const targetUserId = args[1];
        const duration = parseInt(args[2]) || 600; // Default 10 minutes
        
        try {
            const bot = { ws: event.ws, selfId: event.self_id };
            await this.adapter.setGroupBan(bot, group_id, targetUserId, duration);
            await this.sendGroupMessage(group_id, `🔇 Muted user ${targetUserId} for ${duration}s`);
            
            this.logger.info('GroupManagement', `Muted user: ${targetUserId} in ${group_id}, duration: ${duration}s`);
            
        } catch (error) {
            await this.sendGroupMessage(group_id, `❌ Mute failed: ${error.message}`);
        }
    }

    /**
     * Unmute member command
     */
    async handleUnmuteCommand(event, args) {
        const { group_id } = event;
        
        if (args.length < 2) {
            await this.sendGroupMessage(group_id, '❌ Usage: /unmute <user_id>');
            return;
        }
        
        const targetUserId = args[1];
        
        try {
            const bot = { ws: event.ws, selfId: event.self_id };
            await this.adapter.setGroupBan(bot, group_id, targetUserId, 0);
            await this.sendGroupMessage(group_id, `🔊 Unmuted user ${targetUserId}`);
            
            this.logger.info('GroupManagement', `Unmuted user: ${targetUserId} in ${group_id}`);
            
        } catch (error) {
            await this.sendGroupMessage(group_id, `❌ Unmute failed: ${error.message}`);
        }
    }

    /**
     * Info command
     */
    async handleInfoCommand(event, args) {
        const { group_id } = event;

        if (args.length < 2) {
            // Show group info
            const settings = this.groupSettings.get(group_id.toString()) || {};
            const info = [
                `📊 Group ${group_id} Information:`,
                `Auto Kick: ${settings.autoKick ? '✅' : '❌'}`,
                `Auto Ban: ${settings.autoBan ? '✅' : '❌'}`,
                `Max Violations: ${settings.maxViolations || 3}`,
                `Ban Duration: ${settings.banDuration || 3600}s`,
                `Welcome Message: ${settings.welcomeMessage || 'None'}`
            ];

            await this.sendGroupMessage(group_id, info.join('\n'));
        } else {
            // Show user info
            const targetUserId = args[1];
            const violations = this.getViolationCount(targetUserId, group_id);

            await this.sendGroupMessage(group_id, `👤 User ${targetUserId} Information:\nViolations: ${violations}`);
        }
    }

    /**
     * Help command
     */
    async handleHelpCommand(event) {
        const { group_id } = event;

        const helpText = [
            '🔧 Group Management Commands:',
            '/kick <user_id> [reason] - Kick member',
            '/ban <user_id> [duration] [reason] - Ban member',
            '/unban <user_id> - Unban member',
            '/warn <user_id> [reason] - Warn member',
            '/mute <user_id> [duration] - Mute member',
            '/unmute <user_id> - Unmute member',
            '/info [user_id] - Show information',
            '/help - Show this help'
        ];

        await this.sendGroupMessage(group_id, helpText.join('\n'));
    }

    /**
     * Check message content for violations
     */
    async checkMessageContent(event) {
        const { group_id, user_id, message } = event;

        // Skip admins
        if (this.isAdmin(user_id)) {
            return;
        }

        // Check forbidden words
        if (await this.containsForbiddenWords(message)) {
            await this.handleViolation(group_id, user_id, 'Used forbidden words');
        }

        // Check spam
        if (await this.isSpamming(user_id, message)) {
            await this.handleViolation(group_id, user_id, 'Spamming');
        }
    }

    /**
     * Handle violation
     */
    async handleViolation(groupId, userId, reason) {
        this.addViolation(userId, groupId, reason);

        const violations = this.getViolationCount(userId, groupId);
        const maxViolations = this.getGroupSetting(groupId, 'maxViolations', 3);

        if (violations >= maxViolations) {
            const autoKick = this.getGroupSetting(groupId, 'autoKick', false);
            const autoBan = this.getGroupSetting(groupId, 'autoBan', false);

            if (autoKick) {
                // Need to create bot object for API call
                const bot = { ws: null, selfId: null }; // This should be passed from event
                await this.adapter.setGroupKick(bot, groupId, userId);
                await this.sendGroupMessage(groupId, `🚫 User ${userId} auto-kicked for violations`);
            } else if (autoBan) {
                const banDuration = this.getGroupSetting(groupId, 'banDuration', 3600);
                const bot = { ws: null, selfId: null }; // This should be passed from event
                await this.adapter.setGroupBan(bot, groupId, userId, banDuration);
                await this.sendGroupMessage(groupId, `🚫 User ${userId} auto-banned for violations`);
            }
        } else {
            await this.sendGroupMessage(groupId, `⚠️ User ${userId} violation: ${reason}\nWarnings: ${violations}/${maxViolations}`);
        }
    }

    /**
     * Handle new member
     */
    async handleNewMember(event) {
        const { group_id, user_id } = event;
        const welcomeMessage = this.getGroupSetting(group_id, 'welcomeMessage', '');

        if (welcomeMessage) {
            const message = welcomeMessage.replace('{user_id}', user_id).replace('{group_id}', group_id);
            await this.sendGroupMessage(group_id, message);
        }
    }

    /**
     * Check if user is admin
     */
    isAdmin(userId) {
        return this.admins.has(userId.toString());
    }

    /**
     * Get group setting
     */
    getGroupSetting(groupId, key, defaultValue) {
        const settings = this.groupSettings.get(groupId.toString());
        return settings ? (settings[key] !== undefined ? settings[key] : defaultValue) : defaultValue;
    }

    /**
     * Add violation record
     */
    addViolation(userId, groupId, reason) {
        const key = `${userId}_${groupId}`;
        if (!this.violationRecords.has(key)) {
            this.violationRecords.set(key, []);
        }

        this.violationRecords.get(key).push({
            reason: reason,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Get violation count
     */
    getViolationCount(userId, groupId) {
        const key = `${userId}_${groupId}`;
        const violations = this.violationRecords.get(key);
        return violations ? violations.length : 0;
    }

    /**
     * Check forbidden words
     */
    async containsForbiddenWords(message) {
        const forbiddenWords = this.config.GROUP_MANAGEMENT?.FORBIDDEN_WORDS || [];

        for (const word of forbiddenWords) {
            if (message.toLowerCase().includes(word.toLowerCase())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check spam
     */
    async isSpamming(userId, message) {
        // Simple spam detection logic
        // In real applications, more complex detection algorithms should be implemented
        return false;
    }

    /**
     * Send group message
     */
    async sendGroupMessage(groupId, message) {
        try {
            // Create a minimal bot object for API call
            const bot = { ws: null, selfId: null }; // This should be properly passed from events
            await this.adapter.sendGroupMsg(bot, groupId, message);
            this.logger.debug('GroupManagement', `Sent group message to ${groupId}: ${message}`);
        } catch (error) {
            this.logger.error('GroupManagement', `Failed to send group message: ${error.message}`);
        }
    }

    /**
     * Get statistics
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            kickedUsers: 0,
            bannedUsers: 0,
            approvedRequests: 0,
            rejectedRequests: 0,
            warningsIssued: 0
        };
    }

    /**
     * Plugin cleanup
     */
    async destroy() {
        this.logger.info('GroupManagement', 'Unloading group management plugin...');

        // Clean up resources
        this.admins.clear();
        this.groupSettings.clear();
        this.violationRecords.clear();

        this.logger.info('GroupManagement', 'Group management plugin unloaded');
    }

    /**
     * Get plugin info
     */
    getInfo() {
        return {
            name: this.name,
            version: this.version,
            description: this.description,
            stats: this.stats,
            adminsCount: this.admins.size,
            groupsCount: this.groupSettings.size,
            violationRecordsCount: this.violationRecords.size
        };
    }
}

module.exports = GroupManagementPlugin;
