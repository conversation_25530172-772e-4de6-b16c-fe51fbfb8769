/**
 * 智能情感记忆系统 - 主核心系统
 * 基于OpenAI Tools的全新架构，模拟人类情绪和记忆系统
 * 
 * 核心功能：
 * 1. 基于OpenAI Tools的情感分析和记忆处理
 * 2. 用户个性化好感度系统
 * 3. 基于Embedding的智能记忆检索
 * 4. 概念学习和神经网络模拟
 * 5. 记忆演进和自我反思机制
 */

const path = require('path');
const fs = require('fs').promises;
const sqlite3 = require('sqlite3').verbose();
const { promisify } = require('util');

/**
 * 获取北京时间戳 (ISO格式，不带Z后缀)
 */
function getBeijingTimestamp() {
    const now = new Date();
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    // 移除Z后缀，避免前端误解析为UTC时间
    return beijingTime.toISOString().replace('Z', '+08:00');
}

/**
 * 获取北京时间字符串 (YYYY-MM-DD HH:mm:ss)
 */
function getBeijingTimeString() {
    const now = new Date();
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    return beijingTime.toISOString().replace('T', ' ').substr(0, 19);
}

// 引入子系统
const OpenAIToolsService = require('./OpenAIToolsService');
const MemoryEmbeddingService = require('./MemoryEmbeddingService');
const EmotionAnalysisTools = require('./EmotionAnalysisTools');
const ConceptLearningService = require('./ConceptLearningService');
// UserAffinityManager 已移除，使用 OpenAI Tools 替代
const ConversationHistoryManager = require('./ConversationHistoryManager');
const AutoRegulationService = require('./AutoRegulationService');
const EnhancedAffinityThresholdSystem = require('./EnhancedAffinityThresholdSystem');

class EmotionMemorySystem {
    constructor(pluginDir, logger) {
        // 参数验证
        if (!pluginDir || typeof pluginDir !== 'string') {
            throw new Error('EmotionMemorySystem构造函数需要有效的pluginDir字符串参数');
        }
        if (!logger || typeof logger !== 'object') {
            throw new Error('EmotionMemorySystem构造函数需要有效的logger对象参数');
        }
        
        this.pluginDir = pluginDir;
        this.logger = logger;
        this.isInitialized = false;
        this.config = {};
        
        // 子系统实例
        this.openaiService = null;
        this.embeddingService = null;
        this.emotionAnalyzer = null;
        this.conceptLearner = null;
        this.affinityManager = null;
        this.conversationManager = null;
        this.autoRegulationService = null;
        
        // 数据库连接
        this.db = null;
        
        // 后台线程控制
        this.backgroundThreads = new Map();
        this.isShuttingDown = false;
        
        // 内存缓存
        this.memoryCache = new Map();
        this.conceptCache = new Map();
        this.userStateCache = new Map();
    }

    /**
     * 初始化系统
     */
    async initialize() {
        try {
            this.logger.info('情感记忆系统', '开始初始化基于OpenAI Tools的智能系统');
            
            // 1. 加载配置
            await this.loadConfig();
            
            // 2. 初始化数据库
            await this.initializeDatabase();
            
            // 3. 初始化OpenAI服务
            this.openaiService = new OpenAIToolsService(this.config, this.logger);
            await this.openaiService.initialize();
            
            // 4. 初始化嵌入服务
            this.embeddingService = new MemoryEmbeddingService(this.config, this.openaiService, this.logger);
            await this.embeddingService.initialize();
            
            // 5. 初始化情感分析器
            this.emotionAnalyzer = new EmotionAnalysisTools(this.config, this.openaiService, this.logger);
            await this.emotionAnalyzer.initialize();
            
            // 6. 初始化概念学习系统
            this.conceptLearner = new ConceptLearningService(this.config, this.openaiService, this.db, this.logger);
            await this.conceptLearner.initialize();

            // 7. 初始化智能心理分析服务（新增）
            const IntelligentPsychAnalysisService = require('./IntelligentPsychAnalysisService');
            this.intelligentPsychAnalysis = new IntelligentPsychAnalysisService(this.openaiService, this.logger, this.config.intelligent_analysis);
            this.logger.info('情感记忆系统', '智能心理分析服务初始化完成');

            // 8. 用户好感度管理：使用 OpenAI Tools 替代旧的 UserAffinityManager
            this.affinityManager = null; // 已移除旧的好感度管理器，现在使用 OpenAI Tools

            // 9. 初始化对话历史管理器
            this.conversationManager = new ConversationHistoryManager(this.config, this.embeddingService, this.db, this.logger);
            await this.conversationManager.initialize();

            // 9. 初始化自动调节算法服务
            this.autoRegulationService = new AutoRegulationService(this.config, this.openaiService, this.logger);
            await this.autoRegulationService.initialize();
            this.autoRegulationService.setDatabase(this.db);

            // 启动自动调节
            await this.autoRegulationService.startAutoRegulation();
            this.logger.info('情感记忆系统', '自动调节服务已启动');

            // 10. 初始化增强好感度阈值系统
            this.affinityThresholdSystem = new EnhancedAffinityThresholdSystem(this.config, this.logger);
            this.logger.info('情感记忆系统', '增强好感度阈值系统初始化完成');

            // 注意：自动调节监控系统暂时不启动，避免数据库表结构冲突

            // 10. 启动后台线程
            await this.startBackgroundThreads();
            
            this.isInitialized = true;
            this.logger.success('情感记忆系统', '基于OpenAI Tools的智能情感记忆系统初始化完成');
            
            // 输出系统统计信息
            await this.logSystemStats();
            
            return { success: true, message: '系统初始化成功' };
            
        } catch (error) {
            this.logger.error('情感记忆系统', `初始化失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 加载配置
     */
    async loadConfig() {
        try {
            this.logger.info('情感记忆系统', '开始加载配置...');
            
            // 验证pluginDir路径
            if (!this.pluginDir || typeof this.pluginDir !== 'string') {
                throw new Error('无效的插件目录路径');
            }
            
            // 初始化配置对象
            this.config = {};

            // 加载环境配置
            const configPath = path.join(this.pluginDir, 'config.env');
            this.logger.info('情感记忆系统', `尝试加载配置文件: ${configPath}`);

            if (await this.fileExists(configPath)) {
                const configContent = await fs.readFile(configPath, 'utf-8');
                this.parseEnvConfig(configContent);
                this.logger.info('情感记忆系统', '环境配置文件加载成功');
            } else {
                this.logger.warning('情感记忆系统', '环境配置文件不存在，使用默认配置');
            }

            // 构建最终配置 - 优先使用插件自己的配置，不依赖主服务器环境变量
            const finalConfig = {
                // OpenAI配置 - 优先使用插件配置文件中的设置
                openai_api_key: this.config.OPENAI_API_KEY || '',
                openai_api_url: this.config.OPENAI_API_URL || 'https://yuanplus.cloud',
                openai_model: this.config.OPENAI_MODEL || 'gpt-4o-mini',
                openai_embedding_model: this.config.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-small',
                
                // 系统功能开关
                enable_emotion_analysis: this.config.enable_emotion_analysis !== 'false',
                enable_memory_embedding: this.config.enable_memory_embedding !== 'false',
                enable_concept_learning: this.config.enable_concept_learning !== 'false',
                enable_user_affinity: this.config.enable_user_affinity !== 'false',
                enable_background_threads: this.config.enable_background_threads !== 'false',
                
                // 系统参数
                max_context_tokens: parseInt(this.config.max_context_tokens) || 4000,
                emotion_sensitivity: parseFloat(this.config.emotion_sensitivity) || 0.7,
                memory_relevance_threshold: parseFloat(this.config.memory_relevance_threshold) || 0.75,
                concept_activation_threshold: parseFloat(this.config.concept_activation_threshold) || 0.6,
                affinity_update_sensitivity: parseFloat(this.config.affinity_update_sensitivity) || 0.3,
                
                // 后台线程配置
                reflection_interval: parseInt(this.config.reflection_interval) || 300, // 5分钟
                evolution_interval: parseInt(this.config.evolution_interval) || 600,   // 10分钟
                consciousness_interval: parseInt(this.config.consciousness_interval) || 450, // 7.5分钟
                
                // 数据库配置
                database_path: this.config.database_path || 'data/emotion_memory.db',
                
                // 调试配置
                debug_mode: this.config.debug_mode === 'true',
                log_detailed_analysis: this.config.log_detailed_analysis === 'true',
                ...this.config
            };

            // 将最终配置赋值给this.config
            this.config = finalConfig;
            
            this.logger.success('情感记忆系统', '配置加载完成', {
                openai_api_url: this.config.openai_api_url,
                openai_model: this.config.openai_model,
                embedding_model: this.config.openai_embedding_model,
                database_path: this.config.database_path,
                debug_mode: this.config.debug_mode,
                api_key_configured: this.config.openai_api_key ? '是' : '否'
            });
            
        } catch (error) {
            this.logger.error('情感记忆系统', '配置加载失败:', error.message);
            throw error;
        }
    }

    /**
     * 解析环境配置文件
     */
    parseEnvConfig(content) {
        const lines = content.split('\n');
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed && !trimmed.startsWith('#')) {
                const [key, ...valueParts] = trimmed.split('=');
                if (key && valueParts.length > 0) {
                    this.config[key.trim()] = valueParts.join('=').trim();
                }
            }
        }
    }

    /**
     * 初始化数据库
     */
    async initializeDatabase() {
        try {
            this.logger.info('情感记忆系统', '开始初始化数据库...');
            
            // 验证数据库路径配置
            if (!this.config.database_path) {
                throw new Error('数据库路径未配置');
            }
            
            const dbPath = path.join(this.pluginDir, this.config.database_path);
            this.logger.info('情感记忆系统', `数据库路径: ${dbPath}`);

            // 确保数据库目录存在
            const dbDir = path.dirname(dbPath);
            await fs.mkdir(dbDir, { recursive: true });
            this.logger.info('情感记忆系统', `数据库目录创建完成: ${dbDir}`);
            
            // 直接连接数据库
            this.db = new sqlite3.Database(dbPath);
            this.dbRun = promisify(this.db.run.bind(this.db));
            this.dbGet = promisify(this.db.get.bind(this.db));
            this.dbAll = promisify(this.db.all.bind(this.db));
            
            // 创建数据表（如果需要）
            await this.createTables();
            
            this.logger.success('情感记忆系统', `数据库初始化完成: ${dbPath}`);
            
        } catch (error) {
            this.logger.error('情感记忆系统', '数据库初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 创建数据库表结构
     */
    async createTables() {
        const tables = [
            // 智能对话会话表（新增）- 支持对话合并和本地时间
            `CREATE TABLE IF NOT EXISTS conversation_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL, -- 会话ID：{user_id}_{persona_name}_{date}_{session_number}
                user_id TEXT NOT NULL,
                persona_name TEXT NOT NULL,
                chat_type TEXT DEFAULT 'private', -- 'group' or 'private'
                chat_name TEXT, -- 群聊名称或私聊标识
                user_content TEXT NOT NULL, -- 合并的用户消息内容
                assistant_content TEXT NOT NULL, -- 合并的AI回复内容
                message_count INTEGER DEFAULT 1, -- 合并的消息数量
                first_message_time TEXT NOT NULL, -- 本地时间格式：YYYY-MM-DD HH:mm:ss
                last_update_time TEXT NOT NULL, -- 最后更新时间（本地时间）
                importance_score REAL DEFAULT 0.5, -- 会话重要性分数
                emotion_summary TEXT, -- JSON格式的情感状态汇总
                topic_tags TEXT, -- JSON格式的话题标签
                context_hash TEXT, -- 上下文哈希，用于检测相似对话
                UNIQUE(session_id)
            )`,

            // 优化后的对话历史表
            `CREATE TABLE IF NOT EXISTS conversation_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL, -- 关联到conversation_sessions
                user_id TEXT NOT NULL,
                persona_name TEXT NOT NULL,
                conversation_id TEXT,
                speaker TEXT NOT NULL, -- 'user' or 'assistant'
                content TEXT NOT NULL,
                embedding_vector TEXT, -- JSON格式的向量
                emotion_state TEXT, -- JSON格式的情绪状态
                timestamp TEXT NOT NULL, -- 统一使用timestamp字段
                tokens INTEGER DEFAULT 0,
                importance_score REAL DEFAULT 0.0,
                chat_type TEXT DEFAULT 'private', -- 'group' or 'private'
                chat_name TEXT, -- 群聊名称或私聊标识
                is_merged INTEGER DEFAULT 0, -- 是否已合并到会话表
                FOREIGN KEY (session_id) REFERENCES conversation_sessions(session_id)
            )`,
            
            // 用户好感度表
            `CREATE TABLE IF NOT EXISTS user_affinity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                persona_name TEXT NOT NULL,
                current_affinity REAL DEFAULT 0.0,
                relationship_type TEXT DEFAULT 'stranger',
                emotion_valence REAL DEFAULT 0.0, -- 情绪效价 (-1 to 1)
                emotion_arousal REAL DEFAULT 0.0, -- 情绪唤醒 (-1 to 1) 
                emotion_dominance REAL DEFAULT 0.0, -- 情绪支配 (-1 to 1)
                total_interactions INTEGER DEFAULT 0,
                positive_interactions INTEGER DEFAULT 0,
                negative_interactions INTEGER DEFAULT 0,
                last_interaction TEXT NOT NULL, -- 本地时间格式
                affinity_history TEXT, -- JSON格式的好感度历史
                UNIQUE(user_id, persona_name)
            )`,
            
            // 概念神经元表
            `CREATE TABLE IF NOT EXISTS concept_neurons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                concept_name TEXT NOT NULL,
                concept_type TEXT,
                activation_strength REAL DEFAULT 0.0,
                activation_count INTEGER DEFAULT 0,
                last_activation TEXT, -- 允许为空，初次创建时设为NULL
                embedding_vector TEXT, -- JSON格式的概念向量
                associated_emotions TEXT, -- JSON格式的关联情绪
                detailed_meaning TEXT, -- 概念的详细含义描述
                creation_time TEXT, -- 允许为空，自动设置当前时间
                UNIQUE(concept_name)
            )`,
            
            // 概念关联表
            `CREATE TABLE IF NOT EXISTS concept_associations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                concept_a TEXT NOT NULL,
                concept_b TEXT NOT NULL,
                association_strength REAL DEFAULT 0.0,
                co_occurrence_count INTEGER DEFAULT 0,
                last_occurrence TEXT NOT NULL, -- 本地时间格式
                association_type TEXT,
                UNIQUE(concept_a, concept_b)
            )`,
            
            // 记忆片段表（短期和长期记忆）
            `CREATE TABLE IF NOT EXISTS memory_fragments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                persona_name TEXT NOT NULL,
                memory_type TEXT NOT NULL, -- 'short_term', 'long_term', 'reflection', 'summary'
                content TEXT NOT NULL, -- 原始对话内容
                ai_summary TEXT, -- AI生成的智能概括
                key_insights TEXT, -- JSON格式的关键洞察
                conversation_theme TEXT, -- 对话主题
                embedding_vector TEXT, -- JSON格式的向量
                importance_score REAL DEFAULT 0.0,
                access_count INTEGER DEFAULT 0,
                last_accessed TEXT, -- 允许为空，首次创建时设为NULL
                creation_time TEXT NOT NULL, -- 本地时间格式
                expires_at TEXT, -- 本地时间格式
                related_concepts TEXT, -- JSON格式的相关概念
                emotional_context TEXT -- JSON格式的情绪上下文
            )`,

            // 最近对话记录表（替代本地文件存储）
            `CREATE TABLE IF NOT EXISTS recent_conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                persona_name TEXT NOT NULL, -- AI助手名称（主分类）
                user_id TEXT NOT NULL, -- 用户ID（子分类）
                content TEXT NOT NULL, -- 对话摘要内容
                original_content TEXT NOT NULL, -- JSON格式的原始对话内容
                summary_info TEXT, -- JSON格式的摘要信息
                importance_score REAL DEFAULT 0.5,
                creation_time TEXT NOT NULL, -- 本地时间格式：YYYY-MM-DD HH:mm:ss
                memory_type TEXT DEFAULT 'conversation',
                access_count INTEGER DEFAULT 1,
                emotional_context TEXT, -- JSON格式的情绪上下文
                UNIQUE(persona_name, user_id, creation_time) -- 防止重复记录
            )`,
            
            // 系统状态表
            `CREATE TABLE IF NOT EXISTS system_state (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                state_key TEXT UNIQUE NOT NULL,
                state_value TEXT,
                last_updated TEXT NOT NULL -- 本地时间格式
            )`,

            // AI压力值状态表
            `CREATE TABLE IF NOT EXISTS ai_stress_states (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                persona_name TEXT NOT NULL,
                stress_value REAL DEFAULT 0.0, -- 压力值 (-∞ to +∞, 默认0)
                stress_level TEXT DEFAULT 'normal', -- 压力等级描述
                stress_factors TEXT, -- JSON格式的压力因素
                behavior_impact TEXT, -- JSON格式的行为影响
                trend TEXT DEFAULT 'stable',
                timestamp TEXT NOT NULL, -- 统一使用timestamp字段
                UNIQUE(user_id, persona_name)
            )`,

            // 模因认知状态表
            `CREATE TABLE IF NOT EXISTS meme_cognition_states (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                persona_name TEXT NOT NULL,
                active_memes TEXT, -- JSON格式的活跃模因
                meme_network TEXT, -- JSON格式的模因网络
                cognitive_patterns TEXT, -- JSON格式的认知模式
                memetic_influence REAL DEFAULT 0.0, -- 模因影响力
                evolution_stage TEXT DEFAULT 'initial',
                timestamp TEXT NOT NULL, -- 统一使用timestamp字段
                UNIQUE(user_id, persona_name)
            )`,

            // 世界树背景状态表
            `CREATE TABLE IF NOT EXISTS world_tree_states (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                persona_name TEXT NOT NULL,
                current_branch TEXT, -- 当前所在的世界树分支
                narrative_context TEXT, -- JSON格式的叙事背景
                world_state TEXT, -- JSON格式的世界状态
                character_role TEXT, -- 角色定位
                story_progression REAL DEFAULT 0.0, -- 故事进展 (0-1)
                background_influence TEXT, -- JSON格式的背景影响
                timestamp TEXT NOT NULL, -- 统一使用timestamp字段
                UNIQUE(user_id, persona_name)
            )`
        ];
        
        // 创建表
        for (const table of tables) {
            await this.dbRun(table);
        }
        
        // 创建索引
        const indexes = [
            // 会话表索引
            `CREATE INDEX IF NOT EXISTS idx_sessions_user_persona ON conversation_sessions(user_id, persona_name)`,
            `CREATE INDEX IF NOT EXISTS idx_sessions_update_time ON conversation_sessions(last_update_time DESC)`,
            `CREATE INDEX IF NOT EXISTS idx_sessions_chat_type ON conversation_sessions(chat_type, chat_name)`,
            
            // 对话历史表索引
            `CREATE INDEX IF NOT EXISTS idx_conversation_session ON conversation_history(session_id)`,
            `CREATE INDEX IF NOT EXISTS idx_conversation_user_time ON conversation_history(user_id, persona_name, timestamp)`,
            `CREATE INDEX IF NOT EXISTS idx_conversation_merged ON conversation_history(is_merged)`,
            `CREATE INDEX IF NOT EXISTS idx_conversation_chat_type ON conversation_history(chat_type, chat_name)`,
            
            // 其他表索引
            `CREATE INDEX IF NOT EXISTS idx_memory_user_type ON memory_fragments(user_id, persona_name, memory_type)`,
            `CREATE INDEX IF NOT EXISTS idx_memory_importance ON memory_fragments(importance_score DESC)`,
            `CREATE INDEX IF NOT EXISTS idx_memory_accessed ON memory_fragments(last_accessed)`,
            `CREATE INDEX IF NOT EXISTS idx_stress_user_persona ON ai_stress_states(user_id, persona_name)`,
            `CREATE INDEX IF NOT EXISTS idx_meme_user_persona ON meme_cognition_states(user_id, persona_name)`,
            `CREATE INDEX IF NOT EXISTS idx_world_tree_user_persona ON world_tree_states(user_id, persona_name)`
        ];
        
        for (const index of indexes) {
                await this.dbRun(index);
        }
        
        this.logger.success('情感记忆系统', '✅ 优化后的数据库表结构创建完成');
    }

    /**
     * 获取本地时间戳 (YYYY-MM-DD HH:mm:ss 格式)
     */
    getLocalTimestamp() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    /**
     * 获取本地日期 (YYYY-MM-DD 格式)
     */
    getLocalDate() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    }

    /**
     * 检查两个时间戳是否在指定分钟内
     */
    isWithinTimeWindow(timestamp1, timestamp2, windowMinutes = 30) {
        const date1 = new Date(timestamp1);
        const date2 = new Date(timestamp2);
        const diffMs = Math.abs(date2.getTime() - date1.getTime());
        const diffMinutes = diffMs / (1000 * 60);
        
        return diffMinutes <= windowMinutes;
    }

    /**
     * 计算内容的简单哈希值，用于检测相似对话
     */
    calculateContentHash(content) {
        let hash = 0;
        if (!content || content.length === 0) return hash.toString();
        
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return Math.abs(hash).toString();
    }

    /**
     * 执行数据库迁移（已废弃 - 新表结构不需要迁移）
     */
    async performDatabaseMigrations() {
        // 废弃此函数 - 新的表结构已经包含所有必要字段
        this.logger.info('情感记忆系统', '数据库迁移已跳过 - 使用新的优化表结构');
        return;
    }

    /**
     * 启动后台线程
     */
    async startBackgroundThreads() {
        if (!this.config.enable_background_threads) {
            this.logger.info('情感记忆系统', '后台线程已禁用');
            return;
        }
        
        // 自我反思线程已禁用
        this.logger.info('情感记忆系统', '自我反思功能已禁用');

        // 记忆演进线程已禁用
        this.logger.info('情感记忆系统', '记忆演进功能已禁用');
        
        // 概念意识线程已禁用
        this.logger.info('情感记忆系统', '概念意识功能已禁用');
        
        this.logger.info('情感记忆系统', '后台线程启动完成');
    }

    /**
     * 主要处理函数 - 处理用户消息和AI回复
     */
    async processConversation(userId, personaName, userMessage, aiResponse = null, extraInfo = {}) {
        try {
            if (!this.isInitialized) {
                throw new Error('系统未初始化');
            }
            
            const conversationId = extraInfo.conversation_id || `conv_${Date.now()}`;
            const result = {
                success: false,
                user_analysis: null,
                ai_analysis: null,
                memory_context: null,
                emotion_update: null,
                enhanced_context: null
            };
            
            // 1. 分析用户消息
            if (userMessage) {
                this.logger.info('情感记忆系统', `开始分析用户消息: ${userId}`);
                result.user_analysis = await this.analyzeUserMessage(userId, personaName, userMessage, conversationId);
            }
            
            // 2. 分析AI回复（如果提供）
            if (aiResponse) {
                this.logger.info('情感记忆系统', `开始分析AI回复: ${userId}`);
                result.ai_analysis = await this.analyzeAIResponse(userId, personaName, userMessage, aiResponse, conversationId);
            }

            // 3. 使用智能心理分析服务进行综合分析（新增）
            if (userMessage && aiResponse) {
                this.logger.info('情感记忆系统', `开始智能心理状态分析: ${userId}`);
                result.psychological_analysis = await this.performIntelligentPsychologicalAnalysis(
                    userId, personaName, userMessage, aiResponse, conversationId
                );
            }

            // 4. 获取增强上下文
            if (userMessage) {
                this.logger.info('情感记忆系统', `生成增强上下文: ${userId}`);
                result.enhanced_context = await this.generateEnhancedContext(userId, personaName, userMessage);
            }

            result.success = true;
            return result;
            
        } catch (error) {
            this.logger.error('情感记忆系统', '处理对话失败:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 分析用户消息
     */
    async analyzeUserMessage(userId, personaName, userMessage, conversationId) {
        try {
            // 1. 使用OpenAI Tools进行情感分析
            const emotionAnalysis = await this.emotionAnalyzer.analyzeMessage(userMessage, {
                userId,
                personaName,
                messageType: 'user',
                conversationId
            });
            
            // 2. 生成消息嵌入向量
            const messageEmbedding = await this.embeddingService.generateEmbedding(userMessage);
            
            // 3. 概念提取已移除 - 现在由comprehensive_analysis统一处理
            const conceptAnalysis = {
                success: true,
                concepts: [],
                associations: [],
                activated_concepts: [],
                keywords: []
            };
            
            // 4. 存储对话历史
            await this.conversationManager.storeMessage({
                userId,
                personaName,
                conversationId,
                speaker: 'user',
                content: userMessage,
                embeddingVector: messageEmbedding,
                emotionState: emotionAnalysis.emotion_state,
                concepts: conceptAnalysis.concepts
            });
            
            // 5. 用户好感度管理器已移除，保留对话处理逻辑
            this.logger.info('情感记忆系统', '用户消息分析完成，跳过好感度更新');

            return {
                emotion_analysis: emotionAnalysis,
                concept_analysis: conceptAnalysis,
                affinity_update: { success: true, message: '好感度管理器已禁用' },
                embedding_generated: true
            };
            
        } catch (error) {
            this.logger.error('情感记忆系统', '用户消息分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 分析AI回复
     */
    async analyzeAIResponse(userId, personaName, userMessage, aiResponse, conversationId) {
        try {
            // 获取对话历史用于上下文分析
            const conversationHistory = await this.conversationManager.getRecentHistory(userId, personaName, 10);
            
            // 使用OpenAI Tools分析AI回复的情感影响
            const responseAnalysis = await this.emotionAnalyzer.analyzeAIResponse(userMessage, aiResponse, {
                userId,
                personaName,
                conversationId,
                conversationHistory
            });
            
            // 生成AI回复的嵌入向量
            const responseEmbedding = await this.embeddingService.generateEmbedding(aiResponse);
            
            // 存储AI回复
            await this.conversationManager.storeMessage({
                userId,
                personaName,
                conversationId,
                speaker: 'assistant',
                content: aiResponse,
                embeddingVector: responseEmbedding,
                emotionState: responseAnalysis.emotion_impact
            });
            
            // 基于AI回复的好感度更新已移除，保留对话处理逻辑
            this.logger.info('情感记忆系统', 'AI回复分析完成，跳过好感度更新');

            return {
                response_analysis: responseAnalysis,
                affinity_update: { success: true, message: '好感度管理器已禁用' },
                embedding_generated: true
            };
            
        } catch (error) {
            this.logger.error('情感记忆系统', 'AI回复分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 执行智能心理状态分析（新方法）
     */
    async performIntelligentPsychologicalAnalysis(userId, personaName, userMessage, aiResponse, conversationId) {
        try {
            this.logger.info('智能心理分析', `开始为用户 ${userId} 执行智能心理状态分析`);

            // 1. 获取当前心理状态
            const currentStates = await this.getCurrentPsychologicalStates(userId, personaName);

            // 2. 获取对话上下文
            const conversationHistory = await this.conversationManager.getRecentHistory(userId, personaName, 5);

            // 3. 使用智能心理分析服务
            const analysisResult = await this.intelligentPsychAnalysis.analyzePsychologicalState(
                userMessage,
                aiResponse,
                currentStates,
                {
                    userId,
                    personaName,
                    conversationId,
                    conversationHistory,
                    analysis_timestamp: getBeijingTimestamp()
                }
            );

            // 4. 如果分析成功，更新心理状态
            if (analysisResult.success && analysisResult.final_results) {
                await this.updatePsychologicalStatesFromAnalysis(
                    userId,
                    personaName,
                    analysisResult.final_results,
                    analysisResult.confidence_score
                );
            }

            this.logger.info('智能心理分析',
                `分析完成 - 方法: ${analysisResult.analysis_method}, 置信度: ${analysisResult.confidence_score?.toFixed(3) || 'N/A'}`
            );

            return {
                success: analysisResult.success,
                analysis_method: analysisResult.analysis_method,
                psychological_changes: analysisResult.final_results,
                confidence_score: analysisResult.confidence_score,
                tools_analysis: analysisResult.tools_analysis,
                local_analysis: analysisResult.local_analysis,
                analysis_timestamp: analysisResult.analysis_timestamp
            };

        } catch (error) {
            this.logger.error('智能心理分析', `智能心理状态分析失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                analysis_method: 'failed'
            };
        }
    }

    /**
     * 获取当前心理状态
     */
    async getCurrentPsychologicalStates(userId, personaName) {
        try {
            const states = {};

            // 获取情绪状态
            const emotionState = await this.dbGet(`
                SELECT * FROM user_emotions
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_update DESC LIMIT 1
            `, [userId, personaName]);

            // 获取压力状态
            const stressState = await this.dbGet(`
                SELECT * FROM user_stress
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_update DESC LIMIT 1
            `, [userId, personaName]);

            // 获取好感度状态
            const affinityState = await this.dbGet(`
                SELECT * FROM user_affinity
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_interaction DESC LIMIT 1
            `, [userId, personaName]);

            // 获取模因状态
            const memeState = await this.dbGet(`
                SELECT * FROM user_meme_cognition
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_update DESC LIMIT 1
            `, [userId, personaName]);

            states.emotion = emotionState || { emotion_value: 0, current_emotion: '平静' };
            states.stress = stressState || { stress_value: 0, stress_level: '正常' };
            states.affinity = affinityState || { affinity_value: 50, relationship_type: 'neutral' };
            states.meme = memeState || { memetic_influence: 0.5, evolution_stage: '初始' };

            return states;

        } catch (error) {
            this.logger.error('智能心理分析', `获取当前心理状态失败: ${error.message}`);
            return {
                emotion: { emotion_value: 0, current_emotion: '平静' },
                stress: { stress_value: 0, stress_level: '正常' },
                affinity: { affinity_value: 50, relationship_type: 'neutral' },
                meme: { memetic_influence: 0.5, evolution_stage: '初始' }
            };
        }
    }

    /**
     * 根据分析结果更新心理状态
     */
    async updatePsychologicalStatesFromAnalysis(userId, personaName, analysisResults, confidenceScore) {
        try {
            const timestamp = getBeijingTimestamp();

            // 更新情绪状态
            if (Math.abs(analysisResults.emotion_change) > 0) {
                await this.updateEmotionState(userId, personaName, analysisResults.emotion_change, confidenceScore);
            }

            // 更新压力状态
            if (Math.abs(analysisResults.stress_change) > 0) {
                await this.updateStressState(userId, personaName, analysisResults.stress_change, confidenceScore);
            }

            // 更新好感度状态
            if (Math.abs(analysisResults.affinity_change) > 0) {
                await this.updateAffinityState(userId, personaName, analysisResults.affinity_change, confidenceScore);
            }

            // 更新模因状态
            if (Math.abs(analysisResults.meme_change) > 0) {
                await this.updateMemeState(userId, personaName, analysisResults.meme_change, confidenceScore);
            }

            this.logger.info('智能心理分析',
                `心理状态更新完成 - 情绪: ${analysisResults.emotion_change.toFixed(3)}, ` +
                `压力: ${analysisResults.stress_change.toFixed(3)}, ` +
                `好感度: ${analysisResults.affinity_change.toFixed(3)}, ` +
                `模因: ${analysisResults.meme_change.toFixed(3)}`
            );

        } catch (error) {
            this.logger.error('智能心理分析', `心理状态更新失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 生成增强上下文
     */
    async generateEnhancedContext(userId, personaName, currentMessage) {
        try {
            // 1. 基于嵌入向量获取相关记忆
            const relevantMemories = await this.embeddingService.findRelevantMemories(currentMessage, {
                userId,
                personaName,
                limit: 8,
                threshold: this.config.memory_relevance_threshold
            });
            
            // 2. 获取最近对话历史
            const recentHistory = await this.conversationManager.getRecentHistory(userId, personaName, 10);
            
            // 3. 获取当前用户状态
            const userState = await this.affinityManager.getUserState(userId, personaName);
            
            // 4. 获取活跃概念
            const activeConcepts = await this.conceptLearner.getActiveConcepts(userId, personaName);
            
            // 5. 使用OpenAI Tools生成智能上下文摘要
            const contextSummary = await this.openaiService.generateContextSummary({
                currentMessage,
                relevantMemories,
                recentHistory,
                userState,
                activeConcepts,
                userId,
                personaName
            });
            
            return {
                context_summary: contextSummary,
                relevant_memories: relevantMemories,
                recent_history: recentHistory,
                user_state: userState,
                active_concepts: activeConcepts,
                total_context_tokens: this.calculateTokens(contextSummary)
            };
            
        } catch (error) {
            this.logger.error('情感记忆系统', '生成增强上下文失败:', error.message);
            throw error;
        }
    }

    /**
     * 自我反思机制（已禁用）
     */
    async performSelfReflection() {
        this.logger.debug('情感记忆系统', '自我反思功能已禁用，跳过执行');
        return {
            success: true,
            message: '自我反思功能已禁用',
            disabled: true
        };
    }

    /**
     * 记忆演进机制（已禁用）
     */
    async performMemoryEvolution() {
        this.logger.debug('情感记忆系统', '记忆演进功能已禁用，跳过执行');
        return {
            success: true,
            message: '记忆演进功能已禁用',
            disabled: true
        };
    }

    /**
     * 概念意识机制
     */
    async performConceptConsciousness() {
        try {
            this.logger.info('情感记忆系统', '开始执行概念意识建构...');

            // 动态获取高关联性的概念对，数量基于当前概念总数
            const totalConcepts = await this.getTotalConceptsCount();
            const dynamicLimit = Math.min(Math.max(5, Math.floor(totalConcepts * 0.1)), 50); // 10%的概念，最少5个，最多50个

            const conceptPairs = await this.conceptLearner.getHighAssociationPairs(dynamicLimit);

            if (conceptPairs.length > 0) {
                // 使用OpenAI Tools进行概念意识分析
                const consciousness = await this.openaiService.performConceptConsciousness(conceptPairs);

                // 更新概念神经网络
                await this.conceptLearner.updateConceptNetwork(consciousness);

                this.logger.info('情感记忆系统', `概念意识建构完成，处理了 ${conceptPairs.length} 个概念对（总概念数: ${totalConcepts}）`);
            } else {
                this.logger.info('情感记忆系统', '暂无高关联概念对，跳过概念意识建构');
            }

        } catch (error) {
            this.logger.error('情感记忆系统', '概念意识执行失败:', error.message);
        }
    }

    // 辅助方法
    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    calculateTokens(text) {
        // 简单的token估算，实际应该使用更精确的方法
        return Math.ceil(text.length / 4);
    }

    async logSystemStats() {
        try {
            const stats = await this.getSystemStats();
            this.logger.info('情感记忆系统', `系统统计 - 用户数: ${stats.totalUsers}, 对话条数: ${stats.totalConversations}, 概念数: ${stats.totalConcepts}, 记忆片段: ${stats.totalMemories}`);
        } catch (error) {
            this.logger.error('情感记忆系统', '获取系统统计失败:', error.message);
        }
    }

    async getSystemStats() {
        const stats = {};
        try {
            // 优化用户统计 - 从多个表中统计用户
            let userQuery = `
                SELECT COUNT(DISTINCT user_id) as count FROM (
                    SELECT user_id FROM user_affinity WHERE user_id IS NOT NULL AND user_id != ''
                    UNION
                    SELECT user_id FROM conversation_history WHERE user_id IS NOT NULL AND user_id != ''
                    UNION
                    SELECT user_id FROM memory_fragments WHERE user_id IS NOT NULL AND user_id != ''
                ) combined_users
            `;

            stats.totalUsers = (await this.dbGet(userQuery))?.count || 0;
            stats.totalConversations = (await this.dbGet("SELECT COUNT(*) as count FROM conversation_history"))?.count || 0;
            stats.totalConcepts = (await this.dbGet("SELECT COUNT(*) as count FROM concept_neurons"))?.count || 0;
            stats.totalMemories = (await this.dbGet("SELECT COUNT(*) as count FROM memory_fragments"))?.count || 0;

            // 添加调试信息
            this.logger.debug('系统统计', `统计结果: 用户=${stats.totalUsers}, 对话=${stats.totalConversations}, 概念=${stats.totalConcepts}, 记忆=${stats.totalMemories}`);

        } catch (error) {
            this.logger.error('情感记忆系统', '统计查询失败:', error.message);
            // 提供默认值
            stats.totalUsers = 0;
            stats.totalConversations = 0;
            stats.totalConcepts = 0;
            stats.totalMemories = 0;
        }
        return stats;
    }

    async getTotalConceptsCount() {
        try {
            const result = await this.dbGet("SELECT COUNT(*) as count FROM concept_neurons");
            return result?.count || 0;
        } catch (error) {
            this.logger.error('情感记忆系统', '获取概念总数失败:', error.message);
            return 0;
        }
    }

    /**
     * 生成智能上下文（优化版 - 包含AI状态、相关记忆和概念神经元）
     */
    async generateIntelligentContext(userId, currentMessage, options = {}) {
        try {
            const { maxContextSize = 10, personaName = 'Assistant' } = options;

            this.logger.info('情感记忆系统', `开始生成智能上下文 [${userId}] (${personaName})`, {
                currentMessage: currentMessage?.substring(0, 50) + '...',
                maxContextSize: maxContextSize,
                personaName: personaName
            });

            if (!this.isInitialized) {
                this.logger.warning('情感记忆系统', '系统未初始化，返回空上下文');
                return '';
            }

            // === 新增：动态分配策略（整合概念学习系统） ===
            // 分配策略：AI状态(1) + 概念神经元(25%) + 语义记忆(75%)
            const aiStateAllocation = 1; // 固定1个AI状态板块
            const conceptAllocation = Math.max(1, Math.floor(maxContextSize * 0.25)); // 25%给概念神经元
            const semanticMemoryLimit = Math.max(1, maxContextSize - aiStateAllocation - conceptAllocation); // 剩余给语义记忆

            this.logger.info('情感记忆系统', `动态分配策略 [总计${maxContextSize}]: AI状态=${aiStateAllocation}, 概念=${conceptAllocation}, 语义=${semanticMemoryLimit}`);

            // 1. 获取AI当前状态值
            const aiStates = await this.getCurrentAIStates(userId, personaName);

            // 2. 获取相关记忆（根据动态分配）
            let relevantMemories = [];
            if (this.embeddingService && this.embeddingService.isInitialized) {
                relevantMemories = await this.embeddingService.findRelevantMemories(currentMessage, {
                    userId: userId,
                    limit: semanticMemoryLimit,
                    threshold: 0.25  // 降低阈值，使其更容易找到相关记忆
                });
            }

            // === 新增：3. 获取相关概念神经元（基于向量嵌入语义匹配） ===
            let relevantConcepts = [];
            if (this.conceptLearner && userId) {
                try {
                    this.logger.debug('情感记忆系统', `开始获取相关概念神经元: ${conceptAllocation}个`);
                    
                    relevantConcepts = await this.conceptLearner.getRelevantConcepts(
                        currentMessage,
                    userId,
                        personaName,
                        conceptAllocation,
                        true // 跳过耗时的OpenAI Tools调用，使用快速向量嵌入匹配
                    );
                    
                    this.logger.info('情感记忆系统', `获取到 ${relevantConcepts.length} 个相关概念神经元 (请求${conceptAllocation}个)`);
                } catch (conceptError) {
                    this.logger.error('情感记忆系统', `获取相关概念神经元失败: ${conceptError.message}`);
                    relevantConcepts = [];
                }
            }

            // 4. 获取用户状态（保持原有逻辑）
            let userState = {};
            if (this.affinityManager) {
                userState = await this.affinityManager.getUserState(userId, personaName);
            }

            // 5. 构建智能上下文字符串（新格式，包含概念神经元）
            let contextString = '';

            if (relevantMemories.length > 0 || Object.keys(aiStates).length > 0 || relevantConcepts.length > 0) {
                contextString = '\n\n=== 相关记忆和状态信息 ===\n';

                // A. 添加AI当前状态信息（科学理论优化版 - 基于真实心理学算法，类型安全）
                if (Object.keys(aiStates).length > 0) {
                    contextString += '[AI心理状态 - 基于科学算法模拟]\n';
                    
                    // 情绪状态 - 基于Russell环状模型和PAD情感空间理论
                    if (aiStates.emotion !== undefined) {
                        const emotionValue = parseFloat(aiStates.emotion) || 0; // 类型安全转换，默认0
                        
                        // 转换为Valence-Arousal模型 (-1到1范围)
                        const valence = emotionValue / 100; // 情感效价 (Valence)
                        const arousal = Math.abs(emotionValue) / 100; // 情感唤醒度 (Arousal)
                        
                        // PAD模型：Pleasure-Arousal-Dominance
                        const pleasure = valence; // 愉悦度 = 情感效价
                        const dominance = 0.5 + (valence * 0.3); // 支配度基于效价调整
                        
                        // Russell环状模型象限分析
                        let russellQuadrant = '';
                        if (valence > 0 && arousal > 0.3) russellQuadrant = '高唤醒积极象限';
                        else if (valence > 0 && arousal <= 0.3) russellQuadrant = '低唤醒积极象限';
                        else if (valence <= 0 && arousal > 0.3) russellQuadrant = '高唤醒消极象限';
                        else russellQuadrant = '低唤醒消极象限';
                        
                        // Plutchik情感轮盘理论映射
                        let plutchikEmotion = '';
                        if (valence > 0.7) plutchikEmotion = 'Joy/Ecstasy';
                        else if (valence > 0.3) plutchikEmotion = 'Serenity/Joy';
                        else if (valence > 0) plutchikEmotion = 'Acceptance/Trust';
                        else if (valence > -0.3) plutchikEmotion = 'Pensiveness/Sadness';
                        else if (valence > -0.7) plutchikEmotion = 'Sadness/Grief';
                        else plutchikEmotion = 'Grief/Despair';
                        
                        // 行为影响分析（基于Cognitive-Behavioral理论）
                        let behaviorTendency = '';
                        if (valence > 0.5) behaviorTendency = '趋近行为、探索性增强、社交意愿提升';
                        else if (valence > 0) behaviorTendency = '温和积极、合作倾向、开放态度';
                        else if (valence > -0.5) behaviorTendency = '谨慎保守、回避倾向、内向表现';
                        else behaviorTendency = '回避行为、防御机制、社交退缩';
                        
                        contextString += `• 情绪: ${aiStates.emotion_type || '中性'}(${emotionValue}) `;
                        contextString += `[Russell模型:${russellQuadrant}] [PAD:P${pleasure.toFixed(2)}/A${arousal.toFixed(2)}/D${dominance.toFixed(2)}]\n`;
                        contextString += `  ├ Plutchik轮盘: ${plutchikEmotion}\n`;
                        contextString += `  ├ 行为倾向: ${behaviorTendency}\n`;
                        if (aiStates.emotion_stability) {
                            const stabilityScore = parseFloat(aiStates.emotion_stability) || 0.5;
                            const stabilityLevel = stabilityScore > 0.8 ? '高稳定性' : stabilityScore > 0.5 ? '中等稳定性' : '低稳定性';
                            contextString += `  └ 情绪稳定性: ${stabilityLevel}(${stabilityScore.toFixed(2)}) - 基于Eysenck人格理论\n`;
                        }
                    }
                    
                    // 压力状态 - 基于Yerkes-Dodson定律和Holmes-Rahe应激量表
                    if (aiStates.stress !== undefined) {
                        const stressValue = parseFloat(aiStates.stress) || 0; // 类型安全转换，默认0
                        
                        // 转换为Yerkes-Dodson最优表现曲线 (0到1范围)
                        const stressIntensity = Math.abs(stressValue) / 100;
                        
                        // Yerkes-Dodson定律：中等压力最优表现
                        let yerkesPerformance = '';
                        let cognitiveImpact = '';
                        
                        if (stressIntensity < 0.2) {
                            yerkesPerformance = '低压力区间 - 可能表现不足';
                            cognitiveImpact = '注意力分散、动机不足、反应迟缓';
                        } else if (stressIntensity < 0.5) {
                            yerkesPerformance = '最优压力区间 - 表现增强';
                            cognitiveImpact = '专注力提升、反应敏捷、学习效率高';
                        } else if (stressIntensity < 0.8) {
                            yerkesPerformance = '高压力区间 - 表现开始下降';
                            cognitiveImpact = '焦虑增加、决策困难、创造力受限';
                        } else {
                            yerkesPerformance = '过度压力区间 - 严重表现障碍';
                            cognitiveImpact = '认知僵化、逻辑混乱、情绪失控';
                        }
                        
                        // Selye一般适应综合症理论 (GAS)
                        let gasStage = '';
                        if (stressIntensity < 0.3) gasStage = '警觉期';
                        else if (stressIntensity < 0.7) gasStage = '抵抗期';
                        else gasStage = '衰竭期';
                        
                        // Lazarus认知评价理论
                        let appraisalType = stressValue > 0 ? '挑战性评价' : '威胁性评价';
                        
                        contextString += `• 压力: ${aiStates.stress_level || 'normal'}(${stressValue.toFixed(2)}) `;
                        contextString += `[Yerkes-Dodson:${yerkesPerformance}] [GAS阶段:${gasStage}]\n`;
                        contextString += `  ├ 认知评价: ${appraisalType} (Lazarus理论)\n`;
                        contextString += `  ├ 认知影响: ${cognitiveImpact}\n`;
                        if (aiStates.stress_factors) {
                            contextString += `  └ 应激源: ${aiStates.stress_factors} (Holmes-Rahe量表维度)\n`;
                        }
                    }
                    
                    // 好感度状态 - 基于Sternberg三元爱情理论和人际关系梯度模型
                    if (aiStates.affinity !== undefined) {
                        const affinityValue = parseFloat(aiStates.affinity) || 0; // 类型安全转换，默认0
                        
                        // 转换为关系亲密度量表 (0到1范围)
                        const intimacyLevel = (affinityValue + 100) / 200;
                        
                        // Sternberg三元理论分析
                        let intimacy = intimacyLevel * 0.8; // 亲密感
                        let passion = Math.max(0, intimacyLevel - 0.3) * 0.6; // 激情（需要更高基础）
                        let commitment = intimacyLevel * 0.9; // 承诺
                        
                        // 关系类型映射
                        let sternbergType = '';
                        if (intimacy > 0.7 && passion > 0.5 && commitment > 0.7) sternbergType = '完美爱情';
                        else if (intimacy > 0.6 && commitment > 0.6) sternbergType = '伴侣之爱';
                        else if (intimacy > 0.5) sternbergType = '友谊之爱';
                        else if (commitment > 0.4) sternbergType = '空洞之爱';
                        else if (passion > 0.3) sternbergType = '迷恋';
                        else sternbergType = '无爱';
                        
                        // Levinger关系发展阶段
                        let levingerStage = '';
                        if (intimacyLevel < 0.2) levingerStage = '认识阶段';
                        else if (intimacyLevel < 0.4) levingerStage = '建立阶段';
                        else if (intimacyLevel < 0.7) levingerStage = '延续阶段';
                        else if (intimacyLevel < 0.9) levingerStage = '恶化阶段';
                        else levingerStage = '结束阶段';
                        
                        // 社交渗透理论 (Social Penetration Theory)
                        const breadth = intimacyLevel * 0.9; // 交流广度
                        const depth = intimacyLevel * 0.8; // 交流深度
                        
                        // 行为导向分析
                        let socialBehavior = '';
                        if (intimacyLevel > 0.8) socialBehavior = '深度自我披露、情感支持、无条件接纳';
                        else if (intimacyLevel > 0.6) socialBehavior = '信任建立、互惠互利、情感投入';
                        else if (intimacyLevel > 0.4) socialBehavior = '友好合作、表面交流、礼貌距离';
                        else if (intimacyLevel > 0.2) socialBehavior = '谨慎接触、形式化交流、防御姿态';
                        else socialBehavior = '冷漠疏离、最小化互动、避免接触';
                        
                        contextString += `• 关系: ${aiStates.affinity_type || '普通'}(${affinityValue.toFixed(2)}) `;
                        contextString += `[Sternberg:${sternbergType}] [Levinger:${levingerStage}]\n`;
                        contextString += `  ├ 三元分析: 亲密${intimacy.toFixed(2)}/激情${passion.toFixed(2)}/承诺${commitment.toFixed(2)}\n`;
                        contextString += `  ├ 社交渗透: 广度${breadth.toFixed(2)}/深度${depth.toFixed(2)}\n`;
                        contextString += `  └ 社交行为: ${socialBehavior}\n`;
                    }
                    
                    // 模因认知状态 - 基于Dawkins模因理论和认知科学
                    if (aiStates.meme !== undefined) {
                        const memeValue = parseFloat(aiStates.meme) || 0; // 类型安全转换，默认0
                        
                        // 确保meme值在0-1范围内
                        const memeticFitness = Math.max(0, Math.min(1, memeValue));
                        
                        // 认知负荷理论 (Cognitive Load Theory)
                        let cognitiveLoad = '';
                        if (memeticFitness < 0.3) cognitiveLoad = '内在负荷为主 - 基础信息处理';
                        else if (memeticFitness < 0.6) cognitiveLoad = '外在负荷增加 - 环境信息整合';
                        else cognitiveLoad = '相关负荷占主导 - 复杂认知建构';
                        
                        // 信息处理理论模型
                        const encodingEfficiency = memeticFitness * 0.9; // 编码效率
                        const storageCapacity = 0.7 + (memeticFitness * 0.3); // 存储容量
                        const retrievalSpeed = memeticFitness * 0.8; // 检索速度
                        
                        // Bandura社会认知理论
                        let socialCognition = '';
                        if (memeticFitness > 0.7) socialCognition = '观察学习优化、模仿能力增强、自我效能感高';
                        else if (memeticFitness > 0.4) socialCognition = '基础模仿、选择性学习、中等自我效能';
                        else socialCognition = '学习困难、模仿能力弱、自我效能感低';
                        
                        // 模因传播动力学 (Memetic Spreading Dynamics)
                        const replicationRate = memeticFitness * 1.2; // 复制率
                        const mutationRate = (1 - memeticFitness) * 0.8; // 变异率
                        const selectionPressure = memeticFitness * 0.9; // 选择压力
                        
                        contextString += `• 认知: ${aiStates.meme_stage || '初始'}(${memeValue.toFixed(2)}) `;
                        contextString += `[Dawkins适应度:${memeticFitness.toFixed(2)}] [认知负荷:${cognitiveLoad}]\n`;
                        contextString += `  ├ 信息处理: 编码${encodingEfficiency.toFixed(2)}/存储${storageCapacity.toFixed(2)}/检索${retrievalSpeed.toFixed(2)}\n`;
                        contextString += `  ├ 社会认知: ${socialCognition}\n`;
                        contextString += `  └ 模因动力学: 复制率${replicationRate.toFixed(2)}/变异率${mutationRate.toFixed(2)}\n`;
                        if (aiStates.active_memes) {
                            contextString += `  └ 活跃模因群: ${aiStates.active_memes} (共生演化状态)\n`;
                        }
                    }
                    
                    contextString += '\n';
                }

                // === 新增：B. 添加相关概念神经元信息 ===
                if (relevantConcepts.length > 0) {
                    contextString += '[相关概念神经元]\n';
                    
                    relevantConcepts.forEach((concept, index) => {
                        // 概念名称
                        const conceptName = concept.concept_name || concept.name || '未知概念';
                        let conceptText = `• ${conceptName}`;

                        // 概念含义描述（优先使用detailed_meaning）
                        const conceptMeaning = concept.detailed_meaning || concept.meaning;
                        if (conceptMeaning && conceptMeaning !== 'undefined' && conceptMeaning.trim() && conceptMeaning !== '未知') {
                            conceptText += `: ${conceptMeaning}`;
                        }

                        // 概念属性（激活强度、相关度、概念类型）
                        const attributes = [];
                        
                        // 激活强度
                        if (concept.activation_strength) {
                            const activation = (concept.activation_strength * 100).toFixed(0);
                            attributes.push(`激活${activation}%`);
                        }
                        
                        // 语义相关度
                        if (concept.relevance_score || concept.similarity) {
                            const relevance = ((concept.relevance_score || concept.similarity) * 100).toFixed(0);
                            attributes.push(`相关${relevance}%`);
                        }
                        
                        // 概念类型
                        if (concept.concept_type && concept.concept_type !== 'general') {
                            attributes.push(concept.concept_type);
                        }
                        
                        // 使用频率
                        if (concept.activation_count && concept.activation_count > 1) {
                            attributes.push(`${concept.activation_count}次`);
                        }

                        if (attributes.length > 0) {
                            conceptText += ` [${attributes.join(', ')}]`;
                        }

                        contextString += `  ${conceptText}\n`;
                    });
                    
                    contextString += '\n';
                }

                // C. 添加用户状态信息（保持原有逻辑）
                if (userState && Object.keys(userState).length > 0) {
                    const stateDesc = this.generateDetailedUserStateDescription(userState);
                    if (stateDesc) {
                        contextString += `[用户状态] ${stateDesc}\n\n`;
                    }
                }

                // D. 添加格式化的相关记忆（保持原有逻辑）
                if (relevantMemories.length > 0) {
                    contextString += '[相关记忆]\n';
                    contextString += this.formatIntelligentMemories(relevantMemories, userId);
                }

                contextString += '=== 状态信息结束 ===\n';
            }

            this.logger.success('情感记忆系统', `智能上下文生成成功 [${userId}]`, {
                contextLength: contextString.length,
                memoryCount: relevantMemories.length,
                conceptCount: relevantConcepts.length, // 新增：概念数量
                hasAIStates: Object.keys(aiStates).length > 0,
                分配策略: `AI状态=${aiStateAllocation}, 概念=${conceptAllocation}, 语义=${semanticMemoryLimit}`,
                概念详情: relevantConcepts.map(c => `${c.concept_name || c.name}(${((c.relevance_score || c.similarity) * 100).toFixed(0)}%)`).join(', ')
            });

            return contextString;

        } catch (error) {
            this.logger.error('情感记忆系统', `智能上下文生成失败 [${userId}]:`, error.message);
            return '';
        }
    }

    /**
     * 获取AI当前状态值（完整版 - 包含趋势和详细状态）
     */
    async getCurrentAIStates(userId, personaName) {
        const states = {};
        
        try {
            // 获取AI情绪状态（完整字段）
            const emotionState = await this.dbGet(`
                SELECT emotion_value, current_emotion, emotion_type, intensity, 
                       trend, stability, trigger_event, timestamp 
                FROM ai_emotion_states 
                WHERE user_id = ? AND persona_name = ? 
                ORDER BY timestamp DESC LIMIT 1
            `, [userId, personaName]);
            if (emotionState) {
                states.emotion = parseFloat(emotionState.emotion_value).toFixed(2);
                states.emotion_type = emotionState.current_emotion;
                states.emotion_trend = emotionState.trend || 'stable';
                states.emotion_stability = emotionState.stability ? parseFloat(emotionState.stability).toFixed(2) : '0.50';
                states.emotion_intensity = emotionState.intensity ? parseFloat(emotionState.intensity).toFixed(2) : '1.00';
                if (emotionState.trigger_event) {
                    states.emotion_trigger = emotionState.trigger_event;
                }
            }

            // 获取AI压力状态（完整字段）
            const stressState = await this.dbGet(`
                SELECT stress_value, stress_level, stress_factors, behavior_impact, 
                       trend, timestamp 
                FROM ai_stress_states 
                WHERE user_id = ? AND persona_name = ? 
                ORDER BY timestamp DESC LIMIT 1
            `, [userId, personaName]);
            if (stressState) {
                states.stress = parseFloat(stressState.stress_value).toFixed(2);
                states.stress_level = stressState.stress_level;
                states.stress_trend = stressState.trend || 'stable';
                if (stressState.stress_factors) {
                    try {
                        let factors;
                        if (typeof stressState.stress_factors === 'string') {
                            factors = JSON.parse(stressState.stress_factors);
                        } else if (typeof stressState.stress_factors === 'object') {
                            factors = stressState.stress_factors;
                        }
                        
                        if (Array.isArray(factors) && factors.length > 0) {
                            // 提取因素名称或描述
                            const factorNames = factors.slice(0, 2).map(factor => {
                                if (typeof factor === 'object' && factor.name) {
                                    return factor.name;
                                } else if (typeof factor === 'object' && factor.factor) {
                                    return factor.factor;
                                } else if (typeof factor === 'string') {
                                    return factor;
                                } else {
                                    return String(factor);
                                }
                            });
                            states.stress_factors = factorNames.join(', ');
                        } else if (typeof factors === 'object' && Object.keys(factors).length > 0) {
                            // 如果是对象形式，提取值
                            const factorValues = Object.values(factors).slice(0, 2).map(String);
                            states.stress_factors = factorValues.join(', ');
                        }
                    } catch (e) {
                        // 解析失败，尝试从字符串中提取有用信息
                        const rawStr = String(stressState.stress_factors);
                        if (rawStr.includes('repetitive_input') || rawStr.includes('重复输入')) {
                            states.stress_factors = '重复输入';
                        } else if (rawStr.includes('inappropriate_language') || rawStr.includes('不当语言')) {
                            states.stress_factors = '不当语言';
                        } else {
                            states.stress_factors = rawStr.length > 20 ? rawStr.substring(0, 20) + '...' : rawStr;
                        }
                    }
                }
            }

            // 获取用户好感度（完整字段）
            let affinityState = await this.dbGet(`
                SELECT current_affinity, relationship_type, emotion_valence, 
                       emotion_arousal, emotion_dominance, total_interactions,
                       positive_interactions, negative_interactions, last_interaction 
                FROM user_affinity 
                WHERE user_id = ? AND persona_name = ? 
                ORDER BY last_interaction DESC LIMIT 1
            `, [userId, personaName]);
            
            // 如果主表没有数据，尝试状态表
            if (!affinityState) {
                affinityState = await this.dbGet(`
                    SELECT affinity_value, trend, interaction_quality, timestamp 
                    FROM user_affinity_states 
                    WHERE user_id = ? AND persona_name = ? 
                    ORDER BY timestamp DESC LIMIT 1
                `, [userId, personaName]);
            }
            
            if (affinityState) {
                const affinityValue = affinityState.current_affinity || affinityState.affinity_value;
                const numericAffinity = parseFloat(affinityValue);
                states.affinity = numericAffinity.toFixed(2);
                
                // 智能关系级别判断（基于好感度数值）
                states.affinity_type = affinityState.relationship_type || this.getRelationshipType(numericAffinity);
                states.affinity_trend = affinityState.trend || 'stable';
                
                // VAD情感模型数据
                if (affinityState.emotion_valence !== undefined) {
                    states.valence = parseFloat(affinityState.emotion_valence).toFixed(2);
                }
                if (affinityState.emotion_arousal !== undefined) {
                    states.arousal = parseFloat(affinityState.emotion_arousal).toFixed(2);
                }
                if (affinityState.emotion_dominance !== undefined) {
                    states.dominance = parseFloat(affinityState.emotion_dominance).toFixed(2);
                }
                
                // 交互统计
                if (affinityState.total_interactions) {
                    states.total_interactions = affinityState.total_interactions;
                    states.positive_rate = affinityState.positive_interactions && affinityState.total_interactions 
                        ? Math.round((affinityState.positive_interactions / affinityState.total_interactions) * 100) + '%'
                        : '0%';
                }
            }

            // 获取模因认知状态（完整字段）
            const memeState = await this.dbGet(`
                SELECT memetic_influence, evolution_stage, active_memes, 
                       cognitive_patterns, timestamp 
                FROM meme_cognition_states 
                WHERE user_id = ? AND persona_name = ? 
                ORDER BY timestamp DESC LIMIT 1
            `, [userId, personaName]);
            if (memeState) {
                states.meme = parseFloat(memeState.memetic_influence).toFixed(2);
                states.meme_stage = memeState.evolution_stage;
                
                // 活跃模因信息
                if (memeState.active_memes) {
                    try {
                        const activeMemes = JSON.parse(memeState.active_memes);
                        if (Array.isArray(activeMemes) && activeMemes.length > 0) {
                            // 提取模因名称
                            const memeNames = activeMemes.slice(0, 3).map(meme => 
                                typeof meme === 'object' && meme.meme_name ? meme.meme_name : String(meme)
                            );
                            states.active_memes = memeNames.join(', ');
                        }
                    } catch (e) {
                        // JSON解析失败，使用原始字符串的前30个字符
                        const rawStr = String(memeState.active_memes);
                        states.active_memes = rawStr.length > 30 ? rawStr.substring(0, 30) + '...' : rawStr;
                    }
                }
            }

            this.logger.debug('情感记忆系统', `获取完整AI状态成功 [${userId}]`, {
                emotion: `${states.emotion || '0'}(${states.emotion_type || '中性'})`,
                stress: `${states.stress || '0'}(${states.stress_level || 'normal'})`,
                affinity: `${states.affinity || '0'}(${states.affinity_type || '未知'})`,
                meme: `${states.meme || '0'}(${states.meme_stage || '初始'})`
            });

        } catch (error) {
            this.logger.warning('情感记忆系统', `获取AI状态失败: ${error.message}`);
        }

        return states;
    }

    /**
     * 根据好感度数值智能判断关系类型
     */
    getRelationshipType(affinityValue) {
        if (affinityValue < 0) return '陌生人';
        if (affinityValue < 2) return '初识';
        if (affinityValue < 5) return '熟人';
        if (affinityValue < 8) return '朋友';
        if (affinityValue < 12) return '好友';
        if (affinityValue < 15) return '密友';
        return '挚友';
    }

    /**
     * 格式化智能记忆（使用AI概括 + 时间信息）
     */
    formatIntelligentMemories(memories, userId) {
        let result = '';
        
        memories.forEach((memory, index) => {
            let content = '';
            
            // 优先使用AI智能概括
            if (memory.ai_summary && memory.ai_summary.trim()) {
                content = memory.ai_summary;
                
                // 如果有对话主题，添加主题标签
                if (memory.conversation_theme && memory.conversation_theme.trim()) {
                    content = `[${memory.conversation_theme}] ${content}`;
                }
            } else {
                // 回退到原始内容但进行格式化
                content = memory.content;
                if (content && content.includes(':')) {
                    // 提取对话要点而不是完整对话
                    const parts = content.split('\n');
                    const userPart = parts.find(p => p.includes(userId + ':'));
                    const aiPart = parts.find(p => p.includes('雨安安:') || p.includes('Assistant:'));
                    
                    if (userPart && aiPart) {
                        const userMsg = userPart.split(':')[1]?.trim();
                        const aiMsg = aiPart.split(':')[1]?.trim();
                        content = `${userId}询问"${userMsg}"，助手回复要点："${aiMsg?.substring(0, 30)}${aiMsg?.length > 30 ? '...' : ''}"`;
                    }
                }
            }
            
            if (content) {
                // 构建详细信息：相似度 + 时间 + 重要性
                const details = [];
                
                if (memory.similarity) {
                    details.push(`相似度${(memory.similarity * 100).toFixed(0)}%`);
                }
                
                if (memory.creation_time) {
                    const timeStr = this.formatTimeAgo(memory.creation_time);
                    details.push(timeStr);
                }
                
                if (memory.importance_score) {
                    details.push(`重要性${(memory.importance_score * 100).toFixed(0)}%`);
                }
                
                const detailStr = details.length > 0 ? ` (${details.join(', ')})` : '';
                result += `[记忆${index + 1}] ${content}${detailStr}\n`;
            }
        });
        
        return result;
    }

    /**
     * 格式化时间为相对时间描述
     */
    formatTimeAgo(timeStr) {
        try {
            const time = new Date(timeStr);
            const now = new Date();
            const diffMs = now - time;
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
            
            if (diffMinutes < 60) {
                return `${diffMinutes}分钟前`;
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else if (diffDays < 7) {
                return `${diffDays}天前`;
            } else {
                // 格式化为具体日期
                const month = time.getMonth() + 1;
                const day = time.getDate();
                const hour = time.getHours();
                const minute = time.getMinutes();
                return `${month}月${day}日 ${hour}:${minute.toString().padStart(2, '0')}`;
            }
        } catch (error) {
            return timeStr || '未知时间';
        }
    }

    /**
     * 格式化最近会话
     */
    formatRecentSessions(sessions, userId) {
        let result = '';
        
        sessions.forEach((session, index) => {
            let content = '';
            
            // 使用会话的话题标签和概要信息
            if (session.topic_tags && Array.isArray(session.topic_tags) && session.topic_tags.length > 0) {
                const topics = session.topic_tags.join(', ');
                content = `[${topics}主题] `;
            }
            
            // 从合并的内容中提取要点
            const userContents = session.user_content?.split('\n---\n') || [];
            const assistantContents = session.assistant_content?.split('\n---\n') || [];
            
            if (userContents.length > 0 && assistantContents.length > 0) {
                const lastUserMsg = userContents[userContents.length - 1]?.substring(0, 25);
                const lastAiMsg = assistantContents[assistantContents.length - 1]?.substring(0, 30);
                content += `${userId}:"${lastUserMsg}..."，助手:"${lastAiMsg}..."`;
                
                if (session.message_count > 1) {
                    content += ` (共${session.message_count}轮对话)`;
                }
            }
            
            if (content) {
                const timeStr = session.last_update_time || '';
                result += `[会话${index + 1}] ${content} ${timeStr}\n`;
            }
        });
        
        return result;
    }

    /**
     * 生成详细的用户状态描述
     */
    generateDetailedUserStateDescription(userState) {
        const parts = [];

        // 好感度信息
        if (userState.current_affinity !== undefined) {
            const affinity = parseFloat(userState.current_affinity);
            const affinityPercent = (affinity * 100).toFixed(0);
            const level = affinity > 0.7 ? '很高' : affinity > 0.3 ? '中等' : affinity > 0 ? '一般' : '较低';
            parts.push(`好感度${affinityPercent}%(${level})`);
        }

        // 关系类型
        if (userState.relationship_type) {
            parts.push(`关系类型:${userState.relationship_type}`);
        }

        // 情绪状态
        if (userState.emotion_valence !== undefined) {
            const valence = parseFloat(userState.emotion_valence);
            const mood = valence > 0.3 ? '积极' : valence < -0.3 ? '消极' : '中性';
            parts.push(`情绪:${mood}(${valence.toFixed(2)})`);
        }

        // 激活度
        if (userState.emotion_arousal !== undefined) {
            const arousal = parseFloat(userState.emotion_arousal);
            const energy = arousal > 0.3 ? '高能量' : arousal < -0.3 ? '低能量' : '平静';
            parts.push(`能量:${energy}(${arousal.toFixed(2)})`);
        }

        // 主导性
        if (userState.emotion_dominance !== undefined) {
            const dominance = parseFloat(userState.emotion_dominance);
            const control = dominance > 0.3 ? '主导' : dominance < -0.3 ? '被动' : '平衡';
            parts.push(`主导性:${control}(${dominance.toFixed(2)})`);
        }

        return parts.length > 0 ? parts.join(', ') : '';
    }

    /**
     * 生成用户状态描述
     */
    generateUserStateDescription(userState) {
        try {
            const parts = [];

            if (userState.current_affinity !== undefined) {
                const affinityLevel = userState.current_affinity > 0.7 ? '很高' :
                                    userState.current_affinity > 0.3 ? '较高' :
                                    userState.current_affinity > -0.3 ? '中等' :
                                    userState.current_affinity > -0.7 ? '较低' : '很低';
                parts.push(`好感度${affinityLevel}`);
            }

            if (userState.relationship_type) {
                const relationshipMap = {
                    'stranger': '陌生人',
                    'acquaintance': '熟人',
                    'friend': '朋友',
                    'close_friend': '密友'
                };
                parts.push(`关系: ${relationshipMap[userState.relationship_type] || userState.relationship_type}`);
            }

            if (userState.total_interactions) {
                parts.push(`共${userState.total_interactions}次互动`);
            }

            return parts.length > 0 ? parts.join(', ') : null;

        } catch (error) {
            this.logger.error('情感记忆系统', `生成用户状态描述失败: ${error.message}`);
            return null;
        }
    }

    async getActiveUsers() {
        try {
            const users = await this.dbAll(`
                SELECT DISTINCT user_id, persona_name 
                FROM conversation_history 
                WHERE timestamp > datetime('now', '-24 hours')
                ORDER BY timestamp DESC
            `);
            return users || [];
        } catch (error) {
            this.logger.error('情感记忆系统', '获取活跃用户失败:', error.message);
            return [];
        }
    }

    async getHighFrequencyMemories(userId, personaName) {
        try {
            const memories = await this.dbAll(`
                SELECT * FROM memory_fragments 
                WHERE user_id = ? AND persona_name = ? 
                AND access_count > 2
                AND creation_time > datetime('now', '-7 days')
                ORDER BY access_count DESC, importance_score DESC
                LIMIT 10
            `, [userId, personaName]);
            return memories || [];
        } catch (error) {
            this.logger.error('情感记忆系统', '获取高频记忆失败:', error.message);
            return [];
        }
    }

    async getOldMemories() {
        try {
            const memories = await this.dbAll(`
                SELECT * FROM memory_fragments 
                WHERE memory_type = 'short_term'
                AND creation_time < datetime('now', '-7 days')
                AND access_count < 3
                ORDER BY creation_time ASC
                LIMIT 50
            `);
            return memories || [];
        } catch (error) {
            this.logger.error('情感记忆系统', '获取旧记忆失败:', error.message);
            return [];
        }
    }

    async storeReflection(userId, personaName, reflection) {
        try {
            const embeddingVector = await this.embeddingService.generateEmbedding(reflection.content);
            
            await this.dbRun(`
                INSERT INTO memory_fragments (
                    user_id, persona_name, memory_type, content, 
                    embedding_vector, importance_score, related_concepts, emotional_context
                ) VALUES (?, ?, 'reflection', ?, ?, ?, ?, ?)
            `, [
                userId, personaName, reflection.content,
                JSON.stringify(embeddingVector), reflection.importance_score,
                JSON.stringify(reflection.concepts), JSON.stringify(reflection.emotional_context)
            ]);
            
        } catch (error) {
            this.logger.error('情感记忆系统', '存储反思失败:', error.message);
        }
    }

    async applyMemoryEvolution(evolution) {
        // 实现记忆演进逻辑
        // 删除旧记忆，创建新的摘要记忆
        for (const item of evolution.evolved_memories) {
            try {
                // 删除原始记忆
                await this.dbRun(`DELETE FROM memory_fragments WHERE id IN (${item.original_ids.join(',')})`);
                
                // 创建演进后的记忆
                const embeddingVector = await this.embeddingService.generateEmbedding(item.summary);
                await this.dbRun(`
                    INSERT INTO memory_fragments (
                        user_id, persona_name, memory_type, content,
                        embedding_vector, importance_score, related_concepts
                    ) VALUES (?, ?, 'long_term', ?, ?, ?, ?)
                `, [
                    item.userId, item.personaName, item.summary,
                    JSON.stringify(embeddingVector), item.importance_score,
                    JSON.stringify(item.concepts)
                ]);
                
            } catch (error) {
                this.logger.error('情感记忆系统', '应用记忆演进失败:', error.message);
            }
        }
    }

    /**
     * 更新情绪状态
     */
    async updateEmotionState(userId, personaName, emotionChange, confidenceScore) {
        try {
            // 获取当前情绪值
            const currentEmotion = await this.dbGet(`
                SELECT emotion_value FROM user_emotions
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_update DESC LIMIT 1
            `, [userId, personaName]);

            const currentValue = currentEmotion?.emotion_value || 0;
            const newValue = currentValue + emotionChange;

            // 限制情绪值范围
            const constrainedValue = Math.max(-100, Math.min(100, newValue));

            // 确定情绪状态
            let emotionState = '平静';
            if (constrainedValue > 60) emotionState = '非常高兴';
            else if (constrainedValue > 30) emotionState = '高兴';
            else if (constrainedValue > 10) emotionState = '愉快';
            else if (constrainedValue < -60) emotionState = '非常沮丧';
            else if (constrainedValue < -30) emotionState = '沮丧';
            else if (constrainedValue < -10) emotionState = '不悦';

            // 更新数据库
            await this.dbRun(`
                INSERT OR REPLACE INTO user_emotions
                (user_id, persona_name, emotion_value, current_emotion, confidence_score, last_update)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [userId, personaName, constrainedValue, emotionState, confidenceScore, getBeijingTimeString()]);

            this.logger.debug('情绪更新',
                `用户 ${userId} 情绪更新: ${currentValue.toFixed(2)} → ${constrainedValue.toFixed(2)} (${emotionState})`
            );

        } catch (error) {
            this.logger.error('情绪更新', `情绪状态更新失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 更新压力状态
     */
    async updateStressState(userId, personaName, stressChange, confidenceScore) {
        try {
            // 获取当前压力值
            const currentStress = await this.dbGet(`
                SELECT stress_value FROM user_stress
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_update DESC LIMIT 1
            `, [userId, personaName]);

            const currentValue = currentStress?.stress_value || 0;
            const newValue = currentValue + stressChange;

            // 限制压力值范围
            const constrainedValue = Math.max(-100, Math.min(100, newValue));

            // 确定压力等级
            let stressLevel = '正常';
            if (constrainedValue > 50) stressLevel = '极度压力';
            else if (constrainedValue > 30) stressLevel = '高压力';
            else if (constrainedValue > 10) stressLevel = '中等压力';
            else if (constrainedValue > 5) stressLevel = '轻微压力';
            else if (constrainedValue < -30) stressLevel = '极度放松';
            else if (constrainedValue < -10) stressLevel = '放松';

            // 更新数据库
            await this.dbRun(`
                INSERT OR REPLACE INTO user_stress
                (user_id, persona_name, stress_value, stress_level, confidence_score, last_update)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [userId, personaName, constrainedValue, stressLevel, confidenceScore, getBeijingTimeString()]);

            this.logger.debug('压力更新',
                `用户 ${userId} 压力更新: ${currentValue.toFixed(2)} → ${constrainedValue.toFixed(2)} (${stressLevel})`
            );

        } catch (error) {
            this.logger.error('压力更新', `压力状态更新失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 更新好感度状态
     */
    async updateAffinityState(userId, personaName, affinityChange, confidenceScore) {
        try {
            // 获取当前好感度值
            const currentAffinity = await this.dbGet(`
                SELECT current_affinity FROM user_affinity
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_interaction DESC LIMIT 1
            `, [userId, personaName]);

            const currentValue = currentAffinity?.current_affinity || 50;
            const newValue = currentValue + affinityChange;

            // 🚀 使用增强好感度阈值系统进行调节
            const thresholdAdjustment = this.affinityThresholdSystem.applyAffinityThresholdAdjustment(
                affinityChange,
                currentValue,
                {
                    interaction_quality: 'neutral',
                    emotional_resonance: 0.5,
                    trust_level: 0.5
                }
            );

            // 应用调节后的变化
            const adjustedChange = thresholdAdjustment.adjusted_change;
            const finalValue = Math.max(-100, Math.min(100, currentValue + adjustedChange));

            // 获取详细的好感度等级信息
            const affinityLevel = this.affinityThresholdSystem.getAffinityLevel(finalValue);
            const relationshipType = affinityLevel.level;

            // 生成好感度报告
            const affinityReport = this.affinityThresholdSystem.generateAffinityReport(affinityLevel, thresholdAdjustment);

            this.logger.info('好感度更新',
                `用户 ${userId} 好感度阈值调节: ${currentValue.toFixed(2)} → ${finalValue.toFixed(2)} ` +
                `(${affinityLevel.config.name}), 原始变化: ${affinityChange.toFixed(3)}, 调节后: ${adjustedChange.toFixed(3)}`
            );

            // 更新数据库，包含详细的阈值信息
            await this.dbRun(`
                INSERT OR REPLACE INTO user_affinity
                (user_id, persona_name, current_affinity, relationship_type, confidence_score, last_interaction)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [userId, personaName, finalValue, relationshipType, confidenceScore, getBeijingTimeString()]);

            // 记录阈值调节详情（可选，用于调试）
            if (Math.abs(adjustedChange - affinityChange) > 0.01) {
                this.logger.debug('好感度阈值调节',
                    `阈值跨越: ${thresholdAdjustment.threshold_crossed}, ` +
                    `敏感性因子: ${thresholdAdjustment.adjustment_factors.sensitivity.toFixed(3)}, ` +
                    `等级: ${affinityLevel.config.name} (${affinityLevel.config.description})`
                );
            }

        } catch (error) {
            this.logger.error('好感度更新', `好感度状态更新失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 更新模因状态
     */
    async updateMemeState(userId, personaName, memeChange, confidenceScore) {
        try {
            // 获取当前模因状态
            const currentMeme = await this.dbGet(`
                SELECT memetic_influence FROM user_meme_cognition
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_update DESC LIMIT 1
            `, [userId, personaName]);

            const currentValue = currentMeme?.memetic_influence || 0.5;
            const newValue = currentValue + memeChange;

            // 限制模因影响范围
            const constrainedValue = Math.max(0, Math.min(1, newValue));

            // 确定进化阶段
            let evolutionStage = '初始';
            if (constrainedValue >= 0.8) evolutionStage = '转型';
            else if (constrainedValue >= 0.6) evolutionStage = '进化';
            else if (constrainedValue >= 0.4) evolutionStage = '成熟';
            else if (constrainedValue >= 0.2) evolutionStage = '发展';

            // 更新数据库
            await this.dbRun(`
                INSERT OR REPLACE INTO user_meme_cognition
                (user_id, persona_name, memetic_influence, evolution_stage, confidence_score, last_update)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [userId, personaName, constrainedValue, evolutionStage, confidenceScore, getBeijingTimeString()]);

            this.logger.debug('模因更新',
                `用户 ${userId} 模因更新: ${currentValue.toFixed(3)} → ${constrainedValue.toFixed(3)} (${evolutionStage})`
            );

        } catch (error) {
            this.logger.error('模因更新', `模因状态更新失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 清理和关闭系统
     */
    async shutdown() {
        this.logger.info('情感记忆系统', '开始关闭系统...');
        this.isShuttingDown = true;
        
        // 停止后台线程
        for (const [name, interval] of this.backgroundThreads) {
            clearInterval(interval);
            this.logger.info('情感记忆系统', `后台线程 ${name} 已停止`);
        }
        this.backgroundThreads.clear();
        
        // 关闭数据库连接
        if (this.db) {
            await new Promise((resolve) => {
                this.db.close(resolve);
            });
        }
        
        this.logger.info('情感记忆系统', '系统关闭完成');
    }
}

module.exports = EmotionMemorySystem; 