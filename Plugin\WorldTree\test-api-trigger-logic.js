/**
 * 测试API触发逻辑优化
 */

const WorldTreeVCP = require('./WorldTreeVCP');

async function testAPITriggerLogic() {
    console.log('🎯 测试API触发逻辑优化...\n');
    
    const mockLogger = {
        info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
        warning: (tag, ...args) => console.log(`[WARN] [${tag}]`, ...args),
        error: (tag, ...args) => console.log(`[ERROR] [${tag}]`, ...args),
        debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args)
    };
    
    try {
        // 1. 初始化WorldTreeVCP
        console.log('1. 初始化WorldTreeVCP...');
        const worldTreeVCP = new WorldTreeVCP();
        await worldTreeVCP.initialize(mockLogger);
        
        console.log('✅ 插件初始化成功');
        console.log(`配置信息:`);
        console.log(`  useLocalAlgorithm: ${worldTreeVCP.config.useLocalAlgorithm}`);
        console.log(`  forceAPIGeneration: ${worldTreeVCP.config.forceAPIGeneration}`);
        console.log(`  maxCacheAge: ${worldTreeVCP.config.maxCacheAge}ms (${worldTreeVCP.config.maxCacheAge / 60000}分钟)`);
        console.log(`  hasApiKey: ${!!worldTreeVCP.config.apiKey}\n`);
        
        const testUserId = 'test_user_api_trigger';
        const testAgentName = '雨安安';
        
        // 清理旧数据
        await worldTreeVCP.dbRun(`DELETE FROM worldtree_psychology_monologues WHERE user_id = ?`, [testUserId]);
        
        // 2. 测试场景1：无缓存情况
        console.log('2. 测试场景1：无缓存情况...');
        const result1 = await worldTreeVCP.generatePsychologyActivity(
            testUserId,
            testAgentName,
            { isRequestTriggered: true, cognitiveLoad: 0.5 }
        );
        
        console.log(`✅ 无缓存场景完成:`);
        console.log(`  是否缓存: ${result1?.cached}`);
        console.log(`  内容: "${result1?.content?.substring(0, 50)}..."`);
        
        // 等待一下让异步操作有机会完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 3. 测试场景2：有缓存，正常请求
        console.log('\n3. 测试场景2：有缓存，正常请求...');
        const result2 = await worldTreeVCP.generatePsychologyActivity(
            testUserId,
            testAgentName,
            { isRequestTriggered: true, cognitiveLoad: 0.3 }
        );
        
        console.log(`✅ 有缓存场景完成:`);
        console.log(`  是否缓存: ${result2?.cached}`);
        console.log(`  内容: "${result2?.content?.substring(0, 50)}..."`);
        
        // 4. 测试场景3：高认知负荷请求
        console.log('\n4. 测试场景3：高认知负荷请求...');
        const result3 = await worldTreeVCP.generatePsychologyActivity(
            testUserId,
            testAgentName,
            { isRequestTriggered: true, cognitiveLoad: 0.8 } // 高认知负荷
        );
        
        console.log(`✅ 高认知负荷场景完成:`);
        console.log(`  是否缓存: ${result3?.cached}`);
        console.log(`  内容: "${result3?.content?.substring(0, 50)}..."`);
        
        // 5. 测试智能决策函数
        console.log('\n5. 测试智能决策函数...');
        
        // 模拟不同的缓存状态
        const testCases = [
            {
                name: '无缓存',
                cachedActivity: null,
                contextFactors: { isRequestTriggered: true, cognitiveLoad: 0.5 }
            },
            {
                name: '新鲜缓存',
                cachedActivity: {
                    content: '测试内容',
                    timestamp: new Date().toISOString(),
                    generationMethod: 'api'
                },
                contextFactors: { isRequestTriggered: true, cognitiveLoad: 0.3 }
            },
            {
                name: '过期缓存',
                cachedActivity: {
                    content: '测试内容',
                    timestamp: new Date(Date.now() - 2000000).toISOString(), // 33分钟前
                    generationMethod: 'api'
                },
                contextFactors: { isRequestTriggered: true, cognitiveLoad: 0.3 }
            },
            {
                name: '本地生成缓存',
                cachedActivity: {
                    content: '测试内容',
                    timestamp: new Date().toISOString(),
                    generationMethod: 'local'
                },
                contextFactors: { isRequestTriggered: true, cognitiveLoad: 0.3 }
            },
            {
                name: '高认知负荷',
                cachedActivity: {
                    content: '测试内容',
                    timestamp: new Date().toISOString(),
                    generationMethod: 'api'
                },
                contextFactors: { isRequestTriggered: true, cognitiveLoad: 0.8 }
            }
        ];
        
        testCases.forEach(testCase => {
            const decision = worldTreeVCP.shouldTriggerAPIGeneration(
                testCase.cachedActivity,
                testCase.contextFactors
            );
            
            console.log(`  ${testCase.name}: ${decision ? `触发 (${decision.reason})` : '不触发'}`);
        });
        
        // 6. 测试缓存年龄计算
        console.log('\n6. 测试缓存年龄计算...');
        
        const ageTests = [
            { timestamp: new Date().toISOString(), expected: '0分钟' },
            { timestamp: new Date(Date.now() - 300000).toISOString(), expected: '5分钟' },
            { timestamp: new Date(Date.now() - 1800000).toISOString(), expected: '30分钟' },
            { timestamp: null, expected: '无限' },
            { timestamp: 'invalid', expected: '无限' }
        ];
        
        ageTests.forEach(test => {
            const age = worldTreeVCP.getCacheAge(test.timestamp);
            const ageMinutes = age === Infinity ? '无限' : `${Math.round(age / 60000)}分钟`;
            console.log(`  时间戳: ${test.timestamp || 'null'} → 年龄: ${ageMinutes}`);
        });
        
        // 7. 检查实际的API调用记录
        console.log('\n7. 检查实际的API调用记录...');
        
        const logs = await worldTreeVCP.getPsychologyActivityLogs({
            limit: 10,
            agentName: testAgentName,
            userId: testUserId
        });
        
        console.log(`✅ 获取到 ${logs.length} 条记录:`);
        logs.forEach((log, index) => {
            console.log(`  ${index + 1}. [${log.timestamp}] ${log.generationMethod} - "${log.content.substring(0, 30)}..."`);
        });
        
        // 8. 性能测试：多次调用的API触发频率
        console.log('\n8. 性能测试：多次调用的API触发频率...');
        
        let apiTriggerCount = 0;
        const originalLogger = worldTreeVCP.logger.info;
        
        // 临时拦截日志来统计API触发次数
        worldTreeVCP.logger.info = (tag, message, ...args) => {
            if (message && message.includes('触发API异步生成')) {
                apiTriggerCount++;
            }
            originalLogger.call(worldTreeVCP.logger, tag, message, ...args);
        };
        
        // 进行10次快速调用
        for (let i = 0; i < 10; i++) {
            await worldTreeVCP.generatePsychologyActivity(
                testUserId,
                testAgentName,
                { isRequestTriggered: true, cognitiveLoad: 0.2 + i * 0.05 }
            );
        }
        
        // 恢复原始日志函数
        worldTreeVCP.logger.info = originalLogger;
        
        console.log(`✅ 10次调用中API触发次数: ${apiTriggerCount}`);
        console.log(`   触发率: ${(apiTriggerCount / 10 * 100).toFixed(1)}%`);
        
        // 9. 总结
        console.log('\n9. API触发逻辑优化总结:');
        console.log('=' .repeat(60));
        console.log('🎯 触发条件:');
        console.log('  ✅ 无缓存数据 → 必须触发');
        console.log('  ✅ 缓存过期 → 必须触发');
        console.log('  ✅ 本地生成 → 需要API优化');
        console.log('  ✅ 高认知负荷 → 触发更新');
        console.log('  ✅ 随机触发 → 保持新鲜度');
        
        console.log('\n🚀 性能优化:');
        console.log('  ✅ 智能决策减少不必要的API调用');
        console.log('  ✅ 缓存机制提升响应速度');
        console.log('  ✅ 异步生成不阻塞主程序');
        
        console.log('\n📊 配置灵活性:');
        console.log('  ✅ useLocalAlgorithm: 控制算法选择');
        console.log('  ✅ forceAPIGeneration: 强制API生成');
        console.log('  ✅ maxCacheAge: 缓存有效期控制');
        
        console.log('\n🎉 API触发逻辑测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 运行测试
testAPITriggerLogic().catch(console.error);
