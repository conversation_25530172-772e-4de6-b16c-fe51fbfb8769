// Plugin/Mcp/FileProcessor.js - 图文件处理MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}

class FileProcessorMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'FileProcessor';
        this.description = '图文件处理插件，支持markdown链接提取、图片base64转换、文件文本提取和AI分析';
        this.vcpName = 'FileProcessor';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                links: {
                    type: 'array',
                    items: {
                        type: 'string'
                    },
                    minItems: 1,
                    maxItems: 4,
                    description: '文件链接数组，支持网络链接和本地绝对路径，必须包含文件后缀。支持图片(.jpg,.png,.gif,.webp等)、文档(.pdf,.txt,.doc,.docx等)、代码文件(.py,.js,.go,.cpp等)、表格(.xlsx,.csv等)等多种格式'
                },
                prompts: {
                    type: 'array',
                    items: {
                        type: 'string'
                    },
                    description: '对应每个文件的分析提示词数组，数量应与links数组一致。如果只提供一个提示词，将应用到所有文件'
                },
                models: {
                    type: 'array',
                    items: {
                        type: 'string'
                    },
                    description: '指定每个文件使用的模型数组，数量应与links数组一致。如果只提供一个模型，将应用到所有文件。不指定则根据文件类型自动选择'
                }
            },
            required: ['links', 'prompts']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);

        this.log('info', `开始处理文件`, {
            links_count: args.links?.length || 0,
            prompts_count: args.prompts?.length || 0,
            models_count: args.models?.length || 0
        });

        try {
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);

            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }

            // 构建标准响应格式
            const response = {
                type: 'file_processing',
                status: parsedResult.status || 'success',
                message: parsedResult.message || '文件处理完成',
                data: {
                    processed_count: parsedResult.data?.processed_count || 0,
                    results: parsedResult.data?.results || [],
                    original_links: args.links,
                    prompts_used: parsedResult.data?.prompts_used || args.prompts,
                    models_used: parsedResult.data?.models_used || args.models,
                    processing_info: {
                        timestamp: parsedResult.timestamp || new Date().toISOString(),
                        plugin_version: '1.0.0'
                    }
                }
            };

            // 如果有错误，调整响应状态
            if (parsedResult.status === 'error') {
                response.status = 'error';
                response.message = parsedResult.message || '文件处理失败';
                response.data.error = parsedResult.message;
            }

            this.log('success', `文件处理完成`, {
                processed_count: response.data.processed_count,
                status: response.status
            });

            return response;

        } catch (error) {
            const errorResponse = {
                type: 'file_processing',
                status: 'error',
                message: error.message,
                data: {
                    links: args.links,
                    prompts: args.prompts,
                    error: error.message,
                    processing_info: {
                        timestamp: new Date().toISOString(),
                        plugin_version: '1.0.0'
                    }
                }
            };

            this.log('error', `文件处理失败`, errorResponse);
            throw errorResponse;
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查必要的环境变量
            const apiKey = process.env.FILEPROCESSOR_API_KEY || process.env.API_Key;
            if (!apiKey) {
                throw new Error('未配置API密钥 (FILEPROCESSOR_API_KEY 或 API_Key)');
            }
            
            // 检查模型配置
            const imageModels = process.env.FILEPROCESSOR_IMAGE_MODELS;
            const fileModels = process.env.FILEPROCESSOR_FILE_MODELS;
            
            if (!imageModels && !fileModels) {
                this.log('warning', '未配置专用模型列表，将使用默认模型');
            }
            
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = FileProcessorMcp;
