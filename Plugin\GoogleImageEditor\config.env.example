# GoogleImageEditor 插件配置文件示例
# 复制此文件为 config.env 并填入实际配置值

# ===========================================
# 核心配置 (必需)
# ===========================================

# 谷歌API密钥 (必需)
# 从 https://aistudio.google.com/ 获取
GOOGLE_API_KEY=your_google_api_key_here

# 谷歌API代理地址 (可选，如果在中国大陆建议配置)
# 例如: https://your-proxy-domain.com
GOOGLE_PROXY_URL=

# Gemini模型名称 (可选)
# 默认: gemini-2.0-flash-preview-image-generation
GEMINI_MODEL=gemini-2.0-flash-preview-image-generation

# ===========================================
# 调试配置 (可选)
# ===========================================

# 调试模式 (显示详细日志)
# 可选值: true, false
DEBUG_MODE=false
