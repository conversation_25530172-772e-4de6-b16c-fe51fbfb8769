#!/usr/bin/env node

/**
 * 世界树VCP插件快速启动脚本
 * 用于快速配置和测试插件功能
 */

const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function quickStart() {
    console.log('🌳 世界树VCP插件快速启动向导\n');
    
    try {
        // 1. 检查配置文件
        console.log('1. 检查配置文件...');
        const configPath = path.join(__dirname, 'config.env');
        const configExists = await fs.access(configPath).then(() => true).catch(() => false);
        
        if (!configExists) {
            console.log('配置文件不存在，正在创建...');
            const examplePath = path.join(__dirname, 'config.env.example');
            await fs.copyFile(examplePath, configPath);
            console.log('✅ 配置文件已创建\n');
        } else {
            console.log('✅ 配置文件已存在\n');
        }
        
        // 2. 询问是否要创建示例配置
        const createExample = await question('是否要为一个Agent创建示例世界树配置？(y/n): ');
        
        if (createExample.toLowerCase() === 'y' || createExample.toLowerCase() === 'yes') {
            await createExampleConfig();
        }
        
        // 3. 询问是否运行测试
        const runTest = await question('是否要运行插件功能测试？(y/n): ');
        
        if (runTest.toLowerCase() === 'y' || runTest.toLowerCase() === 'yes') {
            console.log('\n正在运行测试...');
            const { testWorldTreeVCP } = require('./test.js');
            await testWorldTreeVCP();
        }
        
        // 4. 显示使用说明
        console.log('\n📖 使用说明:');
        console.log('1. 启动VCPToolBox主服务器');
        console.log('2. 访问管理面板 http://localhost:7700/AdminPanel');
        console.log('3. 点击"世界树VCP"菜单项');
        console.log('4. 在"Agent配置"标签页中配置您的Agent');
        console.log('5. 保存配置后，Agent将自动使用世界树设定\n');
        
        console.log('🎉 快速启动完成！');
        
    } catch (error) {
        console.error('❌ 快速启动失败:', error.message);
    } finally {
        rl.close();
    }
}

async function createExampleConfig() {
    console.log('\n📝 创建示例配置...');
    
    // 读取示例配置
    const examplesPath = path.join(__dirname, 'examples.json');
    const examplesContent = await fs.readFile(examplesPath, 'utf-8');
    const examples = JSON.parse(examplesContent);
    
    // 显示可用示例
    console.log('\n可用的示例配置:');
    const exampleKeys = Object.keys(examples.examples);
    exampleKeys.forEach((key, index) => {
        console.log(`${index + 1}. ${key}`);
    });
    
    const choice = await question('\n请选择一个示例 (输入数字): ');
    const choiceIndex = parseInt(choice) - 1;
    
    if (choiceIndex >= 0 && choiceIndex < exampleKeys.length) {
        const selectedKey = exampleKeys[choiceIndex];
        const selectedConfig = examples.examples[selectedKey];
        
        const agentName = await question(`请输入Agent名称 (默认: ${selectedKey}): `) || selectedKey;
        
        // 创建配置文件
        const configDir = path.join(__dirname, 'configs');
        await fs.mkdir(configDir, { recursive: true });
        
        const configFile = path.join(configDir, `${agentName}.json`);
        await fs.writeFile(configFile, JSON.stringify(selectedConfig, null, 2));
        
        console.log(`✅ 示例配置已保存到: ${configFile}`);
        console.log('您可以在管理面板中导入此配置，或手动复制内容。\n');
        
        // 显示配置预览
        console.log('配置预览:');
        console.log('世界背景:', selectedConfig.worldBackground.substring(0, 100) + '...');
        console.log('时间架构:', Object.keys(selectedConfig.timeArchitecture).join(', '));
        console.log('日程数量:', Object.keys(selectedConfig.characterSchedules).length);
        console.log('叙事规则:', Object.keys(selectedConfig.narrativeRules).join(', '));
        
    } else {
        console.log('❌ 无效的选择');
    }
}

// 运行快速启动
if (require.main === module) {
    quickStart().catch(error => {
        console.error('快速启动过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = { quickStart };
