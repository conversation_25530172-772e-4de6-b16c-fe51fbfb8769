/**
 * 智能心理状态分析服务
 * 完全基于OpenAI Tools和本地智能算法，消除所有硬编码关键词判断
 */

// 尝试加载NLP依赖，如果失败则使用简化版本
let nodejieba = null;
let natural = null;

try {
    nodejieba = require('nodejieba');
} catch (error) {
    console.warn('nodejieba未安装或加载失败，将使用简化分词');
}

try {
    natural = require('natural');
} catch (error) {
    console.warn('natural未安装或加载失败，将使用简化情感分析');
}

class IntelligentPsychAnalysisService {
    constructor(openaiService, logger, config = {}) {
        this.openaiService = openaiService;
        this.logger = logger;
        this.config = {
            // 分析配置
            tools_timeout: config.tools_timeout || 15000,
            fallback_enabled: config.fallback_enabled !== false,
            cache_enabled: config.cache_enabled !== false,
            
            // 算法权重配置
            tools_weight: config.tools_weight || 0.7,
            local_weight: config.local_weight || 0.3,
            
            // 阈值配置
            emotion_threshold: config.emotion_threshold || 0.1,
            stress_threshold: config.stress_threshold || 0.05,
            affinity_threshold: config.affinity_threshold || 0.02,
            
            ...config
        };

        // 初始化分词器
        this.initializeNLP();
        
        // 分析缓存
        this.analysisCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    }

    /**
     * 初始化NLP工具
     */
    initializeNLP() {
        try {
            // 初始化结巴分词（中文分词）
            if (nodejieba) {
                try {
                    nodejieba.load();
                    this.jiebaAvailable = true;
                    this.logger.info('智能心理分析', 'nodejieba中文分词初始化成功');
                } catch (jiebaError) {
                    this.jiebaAvailable = false;
                    this.logger.warning('智能心理分析', `nodejieba初始化失败: ${jiebaError.message}，将使用简化分词`);
                }
            } else {
                this.jiebaAvailable = false;
                this.logger.warning('智能心理分析', 'nodejieba不可用，将使用简化分词');
            }

            // 初始化情感分析器（使用英文模式，因为afinn不支持中文）
            if (natural) {
                try {
                    // 使用英文情感分析器，后续会将中文转换为英文情感词汇
                    this.sentimentAnalyzer = new natural.SentimentAnalyzer('English',
                        natural.PorterStemmer, 'afinn');
                    this.naturalAvailable = true;
                    this.logger.info('智能心理分析', 'natural英文情感分析器初始化成功');
                } catch (naturalError) {
                    this.naturalAvailable = false;
                    this.logger.warning('智能心理分析', `natural初始化失败: ${naturalError.message}，将使用简化情感分析`);
                }
            } else {
                this.naturalAvailable = false;
                this.logger.warning('智能心理分析', 'natural不可用，将使用简化情感分析');
            }

            // 初始化中英文情感词汇映射
            this.initializeEmotionMapping();

            this.logger.info('智能心理分析', `NLP工具初始化完成 - jieba: ${this.jiebaAvailable}, natural: ${this.naturalAvailable}`);
        } catch (error) {
            this.logger.error('智能心理分析', `NLP工具初始化失败: ${error.message}`);
            this.jiebaAvailable = false;
            this.naturalAvailable = false;
        }
    }

    /**
     * 初始化中英文情感词汇映射
     */
    initializeEmotionMapping() {
        // 中文情感词汇到英文的映射，用于natural库分析
        this.emotionMapping = {
            // 积极情感
            '好': 'good',
            '棒': 'great',
            '赞': 'awesome',
            '喜欢': 'like',
            '满意': 'satisfied',
            '开心': 'happy',
            '高兴': 'joyful',
            '谢谢': 'thanks',
            '感谢': 'grateful',
            '很棒': 'excellent',
            '太好了': 'wonderful',
            '完美': 'perfect',
            '优秀': 'outstanding',

            // 消极情感
            '不好': 'bad',
            '差': 'poor',
            '糟糕': 'terrible',
            '失望': 'disappointed',
            '生气': 'angry',
            '烦': 'annoyed',
            '讨厌': 'hate',
            '不满': 'dissatisfied',
            '难过': 'sad',
            '痛苦': 'painful',
            '愤怒': 'furious',
            '烦人': 'annoying',

            // 中性/其他
            '问题': 'problem',
            '困难': 'difficult',
            '复杂': 'complex',
            '简单': 'simple',
            '正常': 'normal',
            '一般': 'average'
        };
    }

    /**
     * 综合心理状态分析（主入口）
     */
    async analyzePsychologicalState(userMessage, aiResponse, currentStates = {}, context = {}) {
        try {
            this.logger.info('智能心理分析', '开始综合心理状态分析');

            // 1. 使用OpenAI Tools进行主要分析
            const toolsAnalysis = await this.analyzeWithTools(userMessage, aiResponse, currentStates, context);
            
            // 2. 使用本地智能算法进行补充分析
            const localAnalysis = await this.analyzeWithLocalAlgorithms(userMessage, aiResponse, currentStates, context);
            
            // 3. 融合分析结果
            const fusedResults = this.fuseAnalysisResults(toolsAnalysis, localAnalysis);
            
            // 4. 应用心理学规律和约束
            const finalResults = this.applyPsychologicalConstraints(fusedResults, currentStates);

            this.logger.info('智能心理分析', `分析完成 - 情绪变化: ${finalResults.emotion_change.toFixed(3)}, 压力变化: ${finalResults.stress_change.toFixed(3)}, 好感度变化: ${finalResults.affinity_change.toFixed(3)}`);

            return {
                success: true,
                analysis_method: 'intelligent_fusion',
                tools_analysis: toolsAnalysis,
                local_analysis: localAnalysis,
                final_results: finalResults,
                analysis_timestamp: new Date().toISOString(),
                confidence_score: this.calculateConfidenceScore(toolsAnalysis, localAnalysis)
            };

        } catch (error) {
            this.logger.error('智能心理分析', `综合分析失败: ${error.message}`);
            return this.getFallbackAnalysis(userMessage, aiResponse, currentStates);
        }
    }

    /**
     * 使用OpenAI Tools进行分析
     */
    async analyzeWithTools(userMessage, aiResponse, currentStates, context) {
        try {
            // 创建超时控制
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Tools分析超时')), this.config.tools_timeout);
            });

            // 🚀 调用优化的心理状态分析工具
            const analysisPromise = this.openaiService.optimizedPsychologicalAnalysis(
                userMessage,
                aiResponse,
                currentStates,
                {
                    ...context,
                    analysis_mode: 'comprehensive',
                    require_detailed_reasoning: true
                }
            );

            const toolsResult = await Promise.race([analysisPromise, timeoutPromise]);

            if (toolsResult && this.validateToolsResult(toolsResult)) {
                this.logger.debug('智能心理分析', 'OpenAI Tools分析成功');
                return {
                    success: true,
                    emotion_analysis: toolsResult.emotion_analysis || {},
                    stress_analysis: toolsResult.stress_analysis || {},
                    affinity_analysis: toolsResult.affinity_analysis || {},
                    meme_analysis: toolsResult.meme_analysis || {},
                    world_tree_analysis: toolsResult.world_tree_analysis || {},
                    reasoning: toolsResult.reasoning || '无详细推理',
                    confidence: toolsResult.confidence || 0.8
                };
            } else {
                throw new Error('Tools返回结果无效');
            }

        } catch (error) {
            this.logger.warning('智能心理分析', `OpenAI Tools分析失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 使用本地智能算法进行分析
     */
    async analyzeWithLocalAlgorithms(userMessage, aiResponse, currentStates, context) {
        try {
            // 1. 语义分析
            const semanticAnalysis = await this.performSemanticAnalysis(userMessage, aiResponse);
            
            // 2. 情感计算
            const emotionAnalysis = await this.calculateEmotionChange(semanticAnalysis, currentStates.emotion);
            
            // 3. 压力计算
            const stressAnalysis = await this.calculateStressChange(semanticAnalysis, currentStates.stress);
            
            // 4. 好感度计算
            const affinityAnalysis = await this.calculateAffinityChange(semanticAnalysis, currentStates.affinity);
            
            // 5. 模因认知分析
            const memeAnalysis = await this.calculateMemeChange(semanticAnalysis, currentStates.meme);

            return {
                success: true,
                semantic_analysis: semanticAnalysis,
                emotion_analysis: emotionAnalysis,
                stress_analysis: stressAnalysis,
                affinity_analysis: affinityAnalysis,
                meme_analysis: memeAnalysis,
                confidence: 0.7
            };

        } catch (error) {
            this.logger.error('智能心理分析', `本地算法分析失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 语义分析（基于NLP，无硬编码）
     */
    async performSemanticAnalysis(userMessage, aiResponse) {
        try {
            // 1. 分词和词性标注
            let userTokens, aiTokens;

            if (this.jiebaAvailable) {
                userTokens = nodejieba.tag(userMessage);
                aiTokens = nodejieba.tag(aiResponse);
            } else {
                // 简化分词（按字符和标点分割）
                userTokens = this.simplifiedTokenize(userMessage);
                aiTokens = this.simplifiedTokenize(aiResponse);
            }

            // 2. 情感极性分析
            const userSentiment = this.analyzeSentimentPolarity(userMessage, userTokens);
            const aiSentiment = this.analyzeSentimentPolarity(aiResponse, aiTokens);

            // 3. 语义复杂度分析
            const userComplexity = this.analyzeSemanticComplexity(userMessage, userTokens);
            const aiComplexity = this.analyzeSemanticComplexity(aiResponse, aiTokens);

            // 4. 交互质量分析
            const interactionQuality = this.analyzeInteractionQuality(userMessage, aiResponse, userTokens, aiTokens);

            // 5. 话题一致性分析
            const topicConsistency = this.analyzeTopicConsistency(userTokens, aiTokens);

            return {
                user_sentiment: userSentiment,
                ai_sentiment: aiSentiment,
                user_complexity: userComplexity,
                ai_complexity: aiComplexity,
                interaction_quality: interactionQuality,
                topic_consistency: topicConsistency,
                semantic_similarity: this.calculateSemanticSimilarity(userMessage, aiResponse)
            };

        } catch (error) {
            this.logger.error('智能心理分析', `语义分析失败: ${error.message}`);
            return this.getDefaultSemanticAnalysis();
        }
    }

    /**
     * 简化分词（当nodejieba不可用时）
     */
    simplifiedTokenize(text) {
        try {
            // 简单的中文分词：按标点和空格分割，然后按字符分组
            const segments = text.split(/[，。！？；：""''（）\s]+/).filter(s => s.length > 0);
            const tokens = [];

            segments.forEach(segment => {
                if (segment.length <= 2) {
                    // 短词直接作为一个token
                    tokens.push({ word: segment, tag: 'n' }); // 默认标记为名词
                } else {
                    // 长词按2-3字符分割
                    for (let i = 0; i < segment.length; i += 2) {
                        const word = segment.substr(i, Math.min(2, segment.length - i));
                        tokens.push({ word: word, tag: 'n' });
                    }
                }
            });

            return tokens;
        } catch (error) {
            this.logger.error('智能心理分析', `简化分词失败: ${error.message}`);
            return [{ word: text, tag: 'n' }];
        }
    }

    /**
     * 情感极性分析（基于词向量和语义，无硬编码）
     */
    analyzeSentimentPolarity(text, tokens) {
        try {
            let sentiment = 0;
            let positiveSignals = 0;
            let negativeSignals = 0;

            // 使用natural库进行情感分析（如果可用）
            if (this.naturalAvailable && this.sentimentAnalyzer) {
                // 将中文词汇映射为英文进行分析
                const englishWords = this.mapChineseToEnglish(tokens.map(token => token.word));
                if (englishWords.length > 0) {
                    sentiment = this.sentimentAnalyzer.getSentiment(englishWords);

                    // 统计积极和消极信号
                    englishWords.forEach(word => {
                        const score = this.getWordSentimentScore(word);
                        if (score > 0) positiveSignals++;
                        else if (score < 0) negativeSignals++;
                    });
                }
            }

            // 如果natural不可用或没有映射词汇，使用智能语义分析
            if (sentiment === 0) {
                const semanticResult = this.intelligentSemanticSentimentAnalysis(text, tokens);
                sentiment = semanticResult.sentiment;
                positiveSignals = semanticResult.positiveSignals;
                negativeSignals = semanticResult.negativeSignals;
            }

            // 结合词性和语义特征计算情感强度
            let emotionalIntensity = this.calculateEmotionalIntensity(tokens, text);

            return {
                polarity: sentiment,
                intensity: Math.min(emotionalIntensity, 1.0),
                positive_signals: positiveSignals,
                negative_signals: negativeSignals,
                confidence: this.calculateSentimentConfidence(tokens, sentiment)
            };

        } catch (error) {
            this.logger.error('智能心理分析', `情感极性分析失败: ${error.message}`);
            return { polarity: 0, intensity: 0.1, confidence: 0.3 };
        }
    }

    /**
     * 将中文词汇映射为英文
     */
    mapChineseToEnglish(chineseWords) {
        const englishWords = [];

        chineseWords.forEach(word => {
            // 直接映射
            if (this.emotionMapping[word]) {
                englishWords.push(this.emotionMapping[word]);
            } else {
                // 部分匹配映射
                for (const [chinese, english] of Object.entries(this.emotionMapping)) {
                    if (word.includes(chinese) || chinese.includes(word)) {
                        englishWords.push(english);
                        break;
                    }
                }
            }
        });

        return englishWords;
    }

    /**
     * 获取单词情感分数（用于natural库）
     */
    getWordSentimentScore(word) {
        try {
            if (this.naturalAvailable && natural.SentimentAnalyzer) {
                // 使用natural的AFINN词典获取分数
                const afinn = natural.SentimentAnalyzer.afinn;
                return afinn[word] || 0;
            }
            return 0;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 智能语义情感分析（无硬编码关键词）
     */
    intelligentSemanticSentimentAnalysis(text, tokens) {
        try {
            let sentiment = 0;
            let positiveSignals = 0;
            let negativeSignals = 0;

            // 1. 基于语言模式的情感分析
            const patterns = this.analyzeLanguagePatterns(text);
            sentiment += patterns.emotionalTone * 0.4;

            // 2. 基于词性组合的情感分析
            const posPatterns = this.analyzePOSEmotionalPatterns(tokens);
            sentiment += posPatterns.emotionalScore * 0.3;

            // 3. 基于语义结构的情感分析
            const structuralEmotion = this.analyzeStructuralEmotion(text, tokens);
            sentiment += structuralEmotion.score * 0.3;

            // 统计信号
            positiveSignals = patterns.positivePatterns + posPatterns.positivePatterns;
            negativeSignals = patterns.negativePatterns + posPatterns.negativePatterns;

            return {
                sentiment: Math.max(-1, Math.min(1, sentiment)),
                positiveSignals,
                negativeSignals
            };

        } catch (error) {
            this.logger.error('智能心理分析', `智能语义情感分析失败: ${error.message}`);
            return { sentiment: 0, positiveSignals: 0, negativeSignals: 0 };
        }
    }

    /**
     * 分析语言模式
     */
    analyzeLanguagePatterns(text) {
        try {
            let emotionalTone = 0;
            let positivePatterns = 0;
            let negativePatterns = 0;

            // 1. 感叹号和问号模式
            const exclamationCount = (text.match(/[!！]/g) || []).length;
            const questionCount = (text.match(/[?？]/g) || []).length;

            if (exclamationCount > 0) {
                emotionalTone += exclamationCount * 0.2; // 感叹号通常表示强烈情感
                positivePatterns += exclamationCount;
            }

            if (questionCount > 2) {
                emotionalTone -= 0.1; // 过多问号可能表示困惑或不满
                negativePatterns += 1;
            }

            // 2. 重复字符模式
            const repeatedChars = text.match(/(.)\1{2,}/g) || [];
            if (repeatedChars.length > 0) {
                emotionalTone += 0.1; // 重复字符通常表示强调
                positivePatterns += repeatedChars.length;
            }

            // 3. 语气词模式
            const moodParticles = ['呀', '啊', '哦', '嗯', '哈', '呵'];
            let moodCount = 0;
            moodParticles.forEach(particle => {
                moodCount += (text.match(new RegExp(particle, 'g')) || []).length;
            });
            if (moodCount > 0) {
                emotionalTone += moodCount * 0.1;
                positivePatterns += moodCount;
            }

            return {
                emotionalTone: Math.max(-1, Math.min(1, emotionalTone)),
                positivePatterns,
                negativePatterns
            };

        } catch (error) {
            return { emotionalTone: 0, positivePatterns: 0, negativePatterns: 0 };
        }
    }

    /**
     * 分析词性情感模式
     */
    analyzePOSEmotionalPatterns(tokens) {
        try {
            let emotionalScore = 0;
            let positivePatterns = 0;
            let negativePatterns = 0;

            // 分析词性组合模式
            for (let i = 0; i < tokens.length - 1; i++) {
                const current = tokens[i];
                const next = tokens[i + 1];

                // 形容词+名词组合（通常表达评价）
                if (current.tag.startsWith('a') && next.tag.startsWith('n')) {
                    emotionalScore += 0.2;
                    positivePatterns++;
                }

                // 副词+动词组合（通常表达程度）
                if (current.tag.startsWith('d') && next.tag.startsWith('v')) {
                    emotionalScore += 0.1;
                    positivePatterns++;
                }

                // 否定词模式检测
                if (this.isNegationWord(current.word)) {
                    emotionalScore -= 0.3;
                    negativePatterns++;
                }
            }

            return {
                emotionalScore: Math.max(-1, Math.min(1, emotionalScore)),
                positivePatterns,
                negativePatterns
            };

        } catch (error) {
            return { emotionalScore: 0, positivePatterns: 0, negativePatterns: 0 };
        }
    }

    /**
     * 分析结构化情感
     */
    analyzeStructuralEmotion(text, tokens) {
        try {
            let score = 0;

            // 1. 句子长度和复杂度
            const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
            const avgSentenceLength = text.length / sentences.length;

            if (avgSentenceLength > 20 && avgSentenceLength < 100) {
                score += 0.1; // 适中的句子长度通常表示清晰表达
            }

            // 2. 词汇多样性
            const uniqueWords = new Set(tokens.map(t => t.word));
            const diversity = uniqueWords.size / tokens.length;
            score += diversity * 0.2;

            // 3. 语言流畅性指标
            const fluencyScore = this.calculateTextFluency(text, tokens);
            score += fluencyScore * 0.3;

            return {
                score: Math.max(-1, Math.min(1, score))
            };

        } catch (error) {
            return { score: 0 };
        }
    }

    /**
     * 计算情感强度
     */
    calculateEmotionalIntensity(tokens, text) {
        try {
            let intensity = 0;

            // 1. 基于词性的情感强度
            tokens.forEach(token => {
                if (token.tag.startsWith('a')) { // 形容词
                    intensity += 0.3;
                }
                if (token.tag.startsWith('v')) { // 动词
                    intensity += 0.2;
                }
                if (token.tag.startsWith('d')) { // 副词
                    intensity += 0.1;
                }
            });

            // 2. 基于文本特征的强度
            const textFeatures = this.analyzeTextFeatures(text);
            intensity += textFeatures.intensityBoost;

            return Math.min(intensity / tokens.length, 1.0);

        } catch (error) {
            return 0.1;
        }
    }

    /**
     * 计算情感置信度
     */
    calculateSentimentConfidence(tokens, sentiment) {
        try {
            let confidence = 0.3; // 基础置信度

            // 基于token数量
            confidence += Math.min(tokens.length / 20, 0.4);

            // 基于情感强度
            confidence += Math.abs(sentiment) * 0.3;

            return Math.min(confidence, 1.0);

        } catch (error) {
            return 0.3;
        }
    }

    /**
     * 判断是否为否定词
     */
    isNegationWord(word) {
        const negationWords = ['不', '没', '无', '非', '未', '别', '勿'];
        return negationWords.some(neg => word.includes(neg));
    }

    /**
     * 分析文本特征
     */
    analyzeTextFeatures(text) {
        try {
            let intensityBoost = 0;

            // 大写字母（在中文环境中较少，但可能存在）
            const upperCaseCount = (text.match(/[A-Z]/g) || []).length;
            intensityBoost += upperCaseCount * 0.05;

            // 特殊符号
            const specialChars = (text.match(/[~@#$%^&*]/g) || []).length;
            intensityBoost += specialChars * 0.02;

            return { intensityBoost };

        } catch (error) {
            return { intensityBoost: 0 };
        }
    }

    /**
     * 计算文本流畅性
     */
    calculateTextFluency(text, tokens) {
        try {
            // 简化的流畅性计算
            const coherenceScore = this.calculateCoherence(tokens);
            const grammarScore = this.assessGrammar(text, tokens);

            return (coherenceScore + grammarScore) / 2;

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 语义复杂度分析
     */
    analyzeSemanticComplexity(text, tokens) {
        try {
            let complexity = 0;

            // 1. 词汇多样性
            const uniqueWords = new Set(tokens.map(t => t.word));
            const lexicalDiversity = uniqueWords.size / tokens.length;
            complexity += lexicalDiversity * 0.3;

            // 2. 句法复杂度
            const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
            const avgSentenceLength = text.length / sentences.length;
            complexity += Math.min(avgSentenceLength / 50, 0.4);

            // 3. 词性分布复杂度
            const posTypes = new Set(tokens.map(t => t.tag));
            complexity += Math.min(posTypes.size / 10, 0.3);

            return Math.min(complexity, 1.0);

        } catch (error) {
            this.logger.error('智能心理分析', `语义复杂度分析失败: ${error.message}`);
            return 0.3;
        }
    }

    /**
     * 交互质量分析
     */
    analyzeInteractionQuality(userMessage, aiResponse, userTokens, aiTokens) {
        try {
            let quality = 0;

            // 1. 回复相关性
            const relevance = this.calculateSemanticSimilarity(userMessage, aiResponse);
            quality += relevance * 0.4;

            // 2. 回复完整性
            const completeness = Math.min(aiResponse.length / userMessage.length, 2.0) / 2.0;
            quality += completeness * 0.3;

            // 3. 语言流畅性
            const fluency = this.analyzeFluency(aiResponse, aiTokens);
            quality += fluency * 0.3;

            return Math.min(quality, 1.0);

        } catch (error) {
            this.logger.error('智能心理分析', `交互质量分析失败: ${error.message}`);
            return 0.5;
        }
    }

    /**
     * 计算语义相似度
     */
    calculateSemanticSimilarity(text1, text2) {
        try {
            let tokens1, tokens2;

            if (this.jiebaAvailable) {
                // 使用Jaccard相似度作为基础
                tokens1 = new Set(nodejieba.cut(text1));
                tokens2 = new Set(nodejieba.cut(text2));
            } else {
                // 简化相似度计算：基于字符和简单词汇
                tokens1 = new Set(this.simplifiedCut(text1));
                tokens2 = new Set(this.simplifiedCut(text2));
            }

            const intersection = new Set([...tokens1].filter(x => tokens2.has(x)));
            const union = new Set([...tokens1, ...tokens2]);

            return union.size > 0 ? intersection.size / union.size : 0.3;

        } catch (error) {
            this.logger.error('智能心理分析', `语义相似度计算失败: ${error.message}`);
            return 0.3;
        }
    }

    /**
     * 简化分词（用于相似度计算）
     */
    simplifiedCut(text) {
        try {
            // 简单分词：按标点分割，然后提取2-3字词汇
            const segments = text.split(/[，。！？；：""''（）\s]+/).filter(s => s.length > 0);
            const words = [];

            segments.forEach(segment => {
                // 提取2-3字的词汇
                for (let len = 2; len <= 3; len++) {
                    for (let i = 0; i <= segment.length - len; i++) {
                        words.push(segment.substr(i, len));
                    }
                }
                // 也包含完整的段落（如果不太长）
                if (segment.length <= 4) {
                    words.push(segment);
                }
            });

            return words;
        } catch (error) {
            return [text];
        }
    }

    /**
     * 验证Tools分析结果
     */
    validateToolsResult(result) {
        if (!result || typeof result !== 'object') return false;
        
        // 检查必要字段
        const requiredFields = ['stress_analysis', 'emotion_analysis'];
        return requiredFields.every(field => result.hasOwnProperty(field));
    }

    /**
     * 获取默认语义分析结果
     */
    getDefaultSemanticAnalysis() {
        return {
            user_sentiment: { polarity: 0, intensity: 0.1, confidence: 0.3 },
            ai_sentiment: { polarity: 0, intensity: 0.1, confidence: 0.3 },
            user_complexity: 0.3,
            ai_complexity: 0.3,
            interaction_quality: 0.5,
            topic_consistency: 0.5,
            semantic_similarity: 0.3
        };
    }

    /**
     * 智能情绪变化计算
     */
    async calculateEmotionChange(semanticAnalysis, currentEmotion = {}) {
        try {
            let emotionChange = 0;
            const factors = {};

            // 1. 基于用户情感极性
            const userSentimentImpact = semanticAnalysis.user_sentiment.polarity *
                                      semanticAnalysis.user_sentiment.intensity * 0.4;
            emotionChange += userSentimentImpact;
            factors.user_sentiment = userSentimentImpact;

            // 2. 基于交互质量
            const qualityImpact = (semanticAnalysis.interaction_quality - 0.5) * 0.3;
            emotionChange += qualityImpact;
            factors.interaction_quality = qualityImpact;

            // 3. 基于话题一致性
            const consistencyImpact = (semanticAnalysis.topic_consistency - 0.5) * 0.2;
            emotionChange += consistencyImpact;
            factors.topic_consistency = consistencyImpact;

            // 4. 应用心理学衰减函数
            const currentValue = currentEmotion.emotion_value || 0;
            emotionChange = this.applyPsychologicalDecay(emotionChange, currentValue, 'emotion');

            return {
                emotion_change: emotionChange,
                factors: factors,
                confidence: semanticAnalysis.user_sentiment.confidence,
                reasoning: `基于语义分析的情绪变化计算: 用户情感${userSentimentImpact.toFixed(3)}, 交互质量${qualityImpact.toFixed(3)}`
            };

        } catch (error) {
            this.logger.error('智能心理分析', `情绪变化计算失败: ${error.message}`);
            return { emotion_change: 0, confidence: 0.1 };
        }
    }

    /**
     * 智能压力变化计算
     */
    async calculateStressChange(semanticAnalysis, currentStress = {}) {
        try {
            let stressChange = 0;
            const factors = {};

            // 1. 基于语义复杂度（复杂度高增加压力）
            const complexityImpact = (semanticAnalysis.user_complexity - 0.5) * 0.3;
            stressChange += complexityImpact;
            factors.complexity = complexityImpact;

            // 2. 基于用户情感（负面情感增加压力）
            const sentimentImpact = -semanticAnalysis.user_sentiment.polarity *
                                   semanticAnalysis.user_sentiment.intensity * 0.4;
            stressChange += sentimentImpact;
            factors.sentiment = sentimentImpact;

            // 3. 基于交互质量（质量差增加压力）
            const qualityImpact = -(semanticAnalysis.interaction_quality - 0.5) * 0.3;
            stressChange += qualityImpact;
            factors.interaction_quality = qualityImpact;

            // 4. 应用心理学衰减函数
            const currentValue = currentStress.stress_value || 0;
            stressChange = this.applyPsychologicalDecay(stressChange, currentValue, 'stress');

            return {
                stress_change: stressChange,
                factors: factors,
                confidence: semanticAnalysis.user_sentiment.confidence,
                reasoning: `基于语义分析的压力变化计算: 复杂度${complexityImpact.toFixed(3)}, 情感${sentimentImpact.toFixed(3)}`
            };

        } catch (error) {
            this.logger.error('智能心理分析', `压力变化计算失败: ${error.message}`);
            return { stress_change: 0, confidence: 0.1 };
        }
    }

    /**
     * 智能好感度变化计算 - 增强版本，确保正确处理负面情绪
     */
    async calculateAffinityChange(semanticAnalysis, currentAffinity = {}) {
        try {
            let affinityChange = 0;
            const factors = {};

            // 1. 用户情感态度分析（主要因素，权重最高）
            const userSentiment = semanticAnalysis.user_sentiment;
            const sentimentImpact = userSentiment.polarity * userSentiment.intensity * 0.6; // 提高权重
            affinityChange += sentimentImpact;
            factors.user_sentiment = sentimentImpact;

            // 2. 强化负面情绪检测
            const negativeBoost = this.detectNegativeEmotions(semanticAnalysis);
            if (negativeBoost < 0) {
                affinityChange += negativeBoost; // 额外的负面影响
                factors.negative_boost = negativeBoost;
            }

            // 3. 基于AI回复质量（次要因素）
            const aiQualityImpact = (semanticAnalysis.interaction_quality - 0.5) * 0.2; // 降低权重
            affinityChange += aiQualityImpact;
            factors.ai_quality = aiQualityImpact;

            // 4. 基于语义相似度（相关性）
            const relevanceImpact = (semanticAnalysis.semantic_similarity - 0.3) * 0.15; // 降低权重
            affinityChange += relevanceImpact;
            factors.relevance = relevanceImpact;

            // 5. 确保负面情绪不被正面因素完全抵消
            if (userSentiment.polarity < -0.3 && affinityChange > -0.1) {
                // 如果用户明显负面但好感度变化不够负面，强制调整
                const forceNegative = Math.min(-0.2, userSentiment.polarity * 0.3);
                affinityChange = Math.min(affinityChange, forceNegative);
                factors.force_negative = forceNegative;
                this.logger.info('好感度计算', `检测到强烈负面情绪，强制负面调整: ${forceNegative.toFixed(3)}`);
            }

            // 6. 应用心理学衰减函数
            const currentValue = currentAffinity.affinity_value || 0;
            affinityChange = this.applyPsychologicalDecay(affinityChange, currentValue, 'affinity');

            return {
                affinity_change: affinityChange,
                factors: factors,
                confidence: semanticAnalysis.user_sentiment.confidence,
                reasoning: `增强好感度计算: 用户情感${sentimentImpact.toFixed(3)}, AI质量${aiQualityImpact.toFixed(3)}, 负面检测${negativeBoost.toFixed(3)}`
            };

        } catch (error) {
            this.logger.error('智能心理分析', `好感度变化计算失败: ${error.message}`);
            return { affinity_change: 0, confidence: 0.1 };
        }
    }

    /**
     * 检测负面情绪并返回额外的负面影响
     */
    detectNegativeEmotions(semanticAnalysis) {
        try {
            let negativeImpact = 0;

            // 检查用户情感的负面程度
            const userSentiment = semanticAnalysis.user_sentiment;

            if (userSentiment.polarity < -0.5) {
                // 强烈负面情绪
                negativeImpact -= 0.3;
            } else if (userSentiment.polarity < -0.3) {
                // 中等负面情绪
                negativeImpact -= 0.2;
            } else if (userSentiment.polarity < -0.1) {
                // 轻微负面情绪
                negativeImpact -= 0.1;
            }

            // 基于强度调整
            negativeImpact *= userSentiment.intensity;

            return negativeImpact;

        } catch (error) {
            this.logger.error('智能心理分析', `负面情绪检测失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 智能模因认知变化计算
     */
    async calculateMemeChange(semanticAnalysis, currentMeme = {}) {
        try {
            let memeChange = 0;
            const factors = {};

            // 1. 基于语义复杂度（复杂对话促进模因进化）
            const complexityImpact = semanticAnalysis.user_complexity * 0.2;
            memeChange += complexityImpact;
            factors.complexity = complexityImpact;

            // 2. 基于话题一致性（一致性促进模因稳定）
            const consistencyImpact = semanticAnalysis.topic_consistency * 0.3;
            memeChange += consistencyImpact;
            factors.consistency = consistencyImpact;

            // 3. 基于创新性（语义相似度低表示新颖性）
            const noveltyImpact = (1 - semanticAnalysis.semantic_similarity) * 0.2;
            memeChange += noveltyImpact;
            factors.novelty = noveltyImpact;

            return {
                meme_change: memeChange,
                factors: factors,
                confidence: 0.6,
                reasoning: `基于语义分析的模因变化计算: 复杂度${complexityImpact.toFixed(3)}, 一致性${consistencyImpact.toFixed(3)}`
            };

        } catch (error) {
            this.logger.error('智能心理分析', `模因变化计算失败: ${error.message}`);
            return { meme_change: 0, confidence: 0.1 };
        }
    }

    /**
     * 应用心理学衰减函数
     */
    applyPsychologicalDecay(change, currentValue, type) {
        try {
            // 根据当前值应用不同的衰减策略
            let decayFactor = 1.0;

            switch (type) {
                case 'emotion':
                    // 情绪值越极端，变化越困难
                    decayFactor = 1 / (1 + Math.abs(currentValue) * 0.01);
                    break;
                case 'stress':
                    // 压力值有自然回归趋势
                    if (Math.abs(currentValue) > 5) {
                        decayFactor = 0.8; // 极端压力时变化减缓
                    }
                    break;
                case 'affinity':
                    // 好感度变化有惯性
                    decayFactor = 1 / (1 + Math.abs(currentValue) * 0.005);
                    break;
            }

            return change * decayFactor;

        } catch (error) {
            this.logger.error('智能心理分析', `心理学衰减函数应用失败: ${error.message}`);
            return change;
        }
    }

    /**
     * 融合分析结果
     */
    fuseAnalysisResults(toolsAnalysis, localAnalysis) {
        try {
            const fusedResults = {};

            // 如果Tools分析成功，以其为主
            if (toolsAnalysis.success) {
                fusedResults.emotion_change = this.extractChangeValue(toolsAnalysis.emotion_analysis, 'emotion_value') * this.config.tools_weight;
                fusedResults.stress_change = this.extractChangeValue(toolsAnalysis.stress_analysis, 'stress_value') * this.config.tools_weight;
                fusedResults.affinity_change = this.extractChangeValue(toolsAnalysis.affinity_analysis, 'affinity_delta') * this.config.tools_weight;
                fusedResults.meme_change = this.extractChangeValue(toolsAnalysis.meme_analysis, 'memetic_influence') * this.config.tools_weight;

                // 如果本地分析也成功，进行加权融合
                if (localAnalysis.success) {
                    fusedResults.emotion_change += localAnalysis.emotion_analysis.emotion_change * this.config.local_weight;
                    fusedResults.stress_change += localAnalysis.stress_analysis.stress_change * this.config.local_weight;
                    fusedResults.affinity_change += localAnalysis.affinity_analysis.affinity_change * this.config.local_weight;
                    fusedResults.meme_change += localAnalysis.meme_analysis.meme_change * this.config.local_weight;
                }

                fusedResults.primary_source = 'tools';
                fusedResults.confidence = toolsAnalysis.confidence || 0.8;

            } else if (localAnalysis.success) {
                // 仅使用本地分析结果
                fusedResults.emotion_change = localAnalysis.emotion_analysis.emotion_change;
                fusedResults.stress_change = localAnalysis.stress_analysis.stress_change;
                fusedResults.affinity_change = localAnalysis.affinity_analysis.affinity_change;
                fusedResults.meme_change = localAnalysis.meme_analysis.meme_change;
                fusedResults.primary_source = 'local';
                fusedResults.confidence = localAnalysis.confidence || 0.6;

            } else {
                // 两者都失败，返回零变化
                fusedResults.emotion_change = 0;
                fusedResults.stress_change = 0;
                fusedResults.affinity_change = 0;
                fusedResults.meme_change = 0;
                fusedResults.primary_source = 'fallback';
                fusedResults.confidence = 0.1;
            }

            // 添加融合元数据
            fusedResults.tools_success = toolsAnalysis.success;
            fusedResults.local_success = localAnalysis.success;
            fusedResults.fusion_method = 'weighted_average';

            return fusedResults;

        } catch (error) {
            this.logger.error('智能心理分析', `结果融合失败: ${error.message}`);
            return {
                emotion_change: 0,
                stress_change: 0,
                affinity_change: 0,
                meme_change: 0,
                primary_source: 'error',
                confidence: 0.1
            };
        }
    }

    /**
     * 应用心理学约束
     */
    applyPsychologicalConstraints(fusedResults, currentStates) {
        try {
            const constrainedResults = { ...fusedResults };

            // 1. 应用变化幅度限制
            constrainedResults.emotion_change = this.constrainChange(
                constrainedResults.emotion_change,
                currentStates.emotion?.emotion_value || 0,
                { min: -5, max: 5, type: 'emotion' }
            );

            constrainedResults.stress_change = this.constrainChange(
                constrainedResults.stress_change,
                currentStates.stress?.stress_value || 0,
                { min: -3, max: 3, type: 'stress' }
            );

            constrainedResults.affinity_change = this.constrainChange(
                constrainedResults.affinity_change,
                currentStates.affinity?.affinity_value || 0,
                { min: -2, max: 2, type: 'affinity' }
            );

            constrainedResults.meme_change = this.constrainChange(
                constrainedResults.meme_change,
                currentStates.meme?.memetic_influence || 0,
                { min: -0.5, max: 0.5, type: 'meme' }
            );

            // 2. 应用心理学相互影响
            constrainedResults = this.applyPsychologicalInteractions(constrainedResults, currentStates);

            // 3. 应用稳定性约束
            constrainedResults = this.applyStabilityConstraints(constrainedResults, currentStates);

            return constrainedResults;

        } catch (error) {
            this.logger.error('智能心理分析', `心理学约束应用失败: ${error.message}`);
            return fusedResults;
        }
    }

    /**
     * 约束变化幅度
     */
    constrainChange(change, currentValue, constraints) {
        try {
            // 基于当前值调整约束
            let maxChange = constraints.max;
            let minChange = constraints.min;

            // 极端值时减少变化幅度
            if (constraints.type === 'emotion') {
                if (Math.abs(currentValue) > 50) {
                    maxChange *= 0.7;
                    minChange *= 0.7;
                }
            } else if (constraints.type === 'stress') {
                if (Math.abs(currentValue) > 10) {
                    maxChange *= 0.8;
                    minChange *= 0.8;
                }
            } else if (constraints.type === 'affinity') {
                if (Math.abs(currentValue) > 80) {
                    maxChange *= 0.6;
                    minChange *= 0.6;
                }
            }

            return Math.max(minChange, Math.min(maxChange, change));

        } catch (error) {
            this.logger.error('智能心理分析', `变化约束失败: ${error.message}`);
            return change;
        }
    }

    /**
     * 应用心理学相互影响
     */
    applyPsychologicalInteractions(results, currentStates) {
        try {
            const interactiveResults = { ...results };

            // 1. 压力对情绪的影响
            if (Math.abs(results.stress_change) > 1) {
                interactiveResults.emotion_change += results.stress_change * -0.3; // 压力增加降低情绪
            }

            // 2. 情绪对好感度的影响
            if (Math.abs(results.emotion_change) > 2) {
                interactiveResults.affinity_change += results.emotion_change * 0.2; // 情绪好转提升好感度
            }

            // 3. 好感度对压力的影响
            if (Math.abs(results.affinity_change) > 1) {
                interactiveResults.stress_change += results.affinity_change * -0.1; // 好感度提升减少压力
            }

            // 4. 模因对其他状态的影响
            if (Math.abs(results.meme_change) > 0.2) {
                interactiveResults.emotion_change += results.meme_change * 0.5; // 模因活跃提升情绪
                interactiveResults.stress_change += results.meme_change * -0.2; // 模因活跃减少压力
            }

            return interactiveResults;

        } catch (error) {
            this.logger.error('智能心理分析', `心理学相互影响应用失败: ${error.message}`);
            return results;
        }
    }

    /**
     * 应用稳定性约束
     */
    applyStabilityConstraints(results, currentStates) {
        try {
            const stableResults = { ...results };

            // 如果变化过小，设为0（避免微小波动）
            if (Math.abs(stableResults.emotion_change) < this.config.emotion_threshold) {
                stableResults.emotion_change = 0;
            }
            if (Math.abs(stableResults.stress_change) < this.config.stress_threshold) {
                stableResults.stress_change = 0;
            }
            if (Math.abs(stableResults.affinity_change) < this.config.affinity_threshold) {
                stableResults.affinity_change = 0;
            }

            return stableResults;

        } catch (error) {
            this.logger.error('智能心理分析', `稳定性约束应用失败: ${error.message}`);
            return results;
        }
    }

    /**
     * 提取变化值
     */
    extractChangeValue(analysisResult, fieldName) {
        try {
            if (!analysisResult || typeof analysisResult !== 'object') return 0;

            const value = analysisResult[fieldName];
            return typeof value === 'number' ? value : 0;

        } catch (error) {
            return 0;
        }
    }

    /**
     * 计算置信度分数
     */
    calculateConfidenceScore(toolsAnalysis, localAnalysis) {
        try {
            let confidence = 0;
            let sources = 0;

            if (toolsAnalysis.success) {
                confidence += (toolsAnalysis.confidence || 0.8) * this.config.tools_weight;
                sources++;
            }

            if (localAnalysis.success) {
                confidence += (localAnalysis.confidence || 0.6) * this.config.local_weight;
                sources++;
            }

            return sources > 0 ? confidence : 0.1;

        } catch (error) {
            this.logger.error('智能心理分析', `置信度计算失败: ${error.message}`);
            return 0.1;
        }
    }

    /**
     * 话题一致性分析
     */
    analyzeTopicConsistency(userTokens, aiTokens) {
        try {
            const userWords = new Set(userTokens.map(t => t.word));
            const aiWords = new Set(aiTokens.map(t => t.word));

            const intersection = new Set([...userWords].filter(x => aiWords.has(x)));
            const union = new Set([...userWords, ...aiWords]);

            return intersection.size / union.size;

        } catch (error) {
            this.logger.error('智能心理分析', `话题一致性分析失败: ${error.message}`);
            return 0.5;
        }
    }

    /**
     * 语言流畅性分析
     */
    analyzeFluency(text, tokens) {
        try {
            let fluency = 0;

            // 1. 句子完整性
            const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
            const avgSentenceLength = text.length / sentences.length;
            fluency += Math.min(avgSentenceLength / 30, 0.4);

            // 2. 词汇连贯性
            const coherenceScore = this.calculateCoherence(tokens);
            fluency += coherenceScore * 0.3;

            // 3. 语法正确性（简化评估）
            const grammarScore = this.assessGrammar(text, tokens);
            fluency += grammarScore * 0.3;

            return Math.min(fluency, 1.0);

        } catch (error) {
            this.logger.error('智能心理分析', `语言流畅性分析失败: ${error.message}`);
            return 0.5;
        }
    }

    /**
     * 计算词汇连贯性
     */
    calculateCoherence(tokens) {
        try {
            if (tokens.length < 2) return 0.5;

            let coherenceScore = 0;
            for (let i = 1; i < tokens.length; i++) {
                const prevTag = tokens[i-1].tag;
                const currTag = tokens[i].tag;

                // 基于词性转换的合理性评分
                if (this.isReasonableTransition(prevTag, currTag)) {
                    coherenceScore += 1;
                }
            }

            return coherenceScore / (tokens.length - 1);

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 判断词性转换是否合理
     */
    isReasonableTransition(prevTag, currTag) {
        // 简化的词性转换合理性判断
        const reasonableTransitions = {
            'n': ['v', 'd', 'p', 'c'], // 名词后可接动词、副词、介词、连词
            'v': ['n', 'd', 'u', 'p'], // 动词后可接名词、副词、助词、介词
            'a': ['n', 'd'], // 形容词后可接名词、副词
            'd': ['v', 'a'], // 副词后可接动词、形容词
        };

        const prevType = prevTag.charAt(0);
        const currType = currTag.charAt(0);

        return reasonableTransitions[prevType]?.includes(currType) || false;
    }

    /**
     * 语法正确性评估（简化）
     */
    assessGrammar(text, tokens) {
        try {
            let grammarScore = 0.5; // 基础分

            // 1. 标点符号使用
            const punctuationCount = (text.match(/[，。！？；：""''（）]/g) || []).length;
            const punctuationRatio = punctuationCount / text.length;
            if (punctuationRatio > 0.02 && punctuationRatio < 0.15) {
                grammarScore += 0.2;
            }

            // 2. 句子结构完整性
            const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
            const avgWordsPerSentence = tokens.length / sentences.length;
            if (avgWordsPerSentence > 3 && avgWordsPerSentence < 20) {
                grammarScore += 0.2;
            }

            // 3. 词性分布合理性
            const posDistribution = this.analyzePOSDistribution(tokens);
            if (posDistribution.isReasonable) {
                grammarScore += 0.1;
            }

            return Math.min(grammarScore, 1.0);

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 分析词性分布
     */
    analyzePOSDistribution(tokens) {
        try {
            const posCount = {};
            tokens.forEach(token => {
                const posType = token.tag.charAt(0);
                posCount[posType] = (posCount[posType] || 0) + 1;
            });

            const totalTokens = tokens.length;
            const nounRatio = (posCount['n'] || 0) / totalTokens;
            const verbRatio = (posCount['v'] || 0) / totalTokens;
            const adjRatio = (posCount['a'] || 0) / totalTokens;

            // 合理的词性分布：名词20-50%，动词15-40%，形容词5-25%
            const isReasonable = nounRatio >= 0.2 && nounRatio <= 0.5 &&
                               verbRatio >= 0.15 && verbRatio <= 0.4 &&
                               adjRatio >= 0.05 && adjRatio <= 0.25;

            return {
                isReasonable,
                nounRatio,
                verbRatio,
                adjRatio,
                distribution: posCount
            };

        } catch (error) {
            return { isReasonable: true };
        }
    }

    /**
     * 获取回退分析结果
     */
    getFallbackAnalysis(userMessage, aiResponse, currentStates) {
        return {
            success: false,
            analysis_method: 'fallback',
            final_results: {
                emotion_change: 0,
                stress_change: 0,
                affinity_change: 0,
                meme_change: 0
            },
            confidence_score: 0.1,
            error: '分析失败，使用默认值'
        };
    }
}

module.exports = IntelligentPsychAnalysisService;
