// components/variableReplacer.js
// 变量替换相关函数

const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger.cjs');

/**
 * 替换通用变量
 */
async function replaceCommonVariables(text, model, AGENT_DIR) {
    if (text == null) return '';
    let processedText = String(text);

    // START: Agent placeholder processing
    const agentConfigs = {};
    for (const envKey in process.env) {
        if (envKey.startsWith('Agent')) { // e.g., AgentNova
            const agentName = envKey.substring(5); // e.g., Nova
            if (agentName) { // Make sure it's not just "Agent"
                agentConfigs[agentName] = process.env[envKey]; // agentConfigs["Nova"] = "Nova.txt"
            }
        }
    }

    for (const agentName in agentConfigs) {
        const placeholder = `{{${agentName}}}`; // e.g., {{Nova}}
        if (processedText.includes(placeholder)) {
            const agentFileName = agentConfigs[agentName]; // e.g., Nova.txt
            const agentFilePath = path.join(AGENT_DIR, agentFileName);
            try {
                let agentFileContent = await fs.readFile(agentFilePath, 'utf-8');
                // Recursively call replaceCommonVariables for the agent's content
                // This ensures placeholders within the agent file are resolved.
                let resolvedAgentContent = await replaceCommonVariables(agentFileContent, model, AGENT_DIR, logger);
                processedText = processedText.replaceAll(placeholder, resolvedAgentContent);
            } catch (error) {
                let errorMsg;
                if (error.code === 'ENOENT') {
                    errorMsg = `[Agent ${agentName} (${agentFileName}) not found]`;
                    if (logger) {
                        logger.warning('Agent系统', `Agent文件未找到: ${agentFilePath}，占位符: ${placeholder}`);
                    }
                } else {
                    errorMsg = `[处理Agent ${agentName} (${agentFileName}) 时出错]`;
                    if (logger) {
                        logger.error('Agent系统', `读取或处理Agent文件 ${agentFilePath} 时出错，占位符: ${placeholder}`, error.message);
                    }
                }
                processedText = processedText.replaceAll(placeholder, errorMsg);
            }
        }
    }
    // END: Agent placeholder processing

    // START: Common variable replacements
    const now = new Date();
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));

    const year = beijingTime.getUTCFullYear();
    const month = (beijingTime.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = beijingTime.getUTCDate().toString().padStart(2, '0');
    const hour = beijingTime.getUTCHours().toString().padStart(2, '0');
    const minute = beijingTime.getUTCMinutes().toString().padStart(2, '0');
    const second = beijingTime.getUTCSeconds().toString().padStart(2, '0');

    const replacements = {
        '{{Year}}': year.toString(),
        '{{Month}}': month,
        '{{Day}}': day,
        '{{Hour}}': hour,
        '{{Minute}}': minute,
        '{{Second}}': second,
        '{{Date}}': `${year}年${month}月${day}日`,
        '{{Time}}': `${hour}时${minute}分${second}秒`,
        '{{DateTime}}': `${year}年${month}月${day}日${hour}时${minute}分${second}秒`,
        '{{Model}}': model || 'Unknown',
        '{{WeekDay}}': ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][beijingTime.getUTCDay()],
        '{{MonthName}}': ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'][beijingTime.getUTCMonth()],
        '{{Season}}': getSeason(beijingTime.getUTCMonth() + 1),
        '{{Timestamp}}': now.getTime().toString(),
        '{{ISO}}': now.toISOString(),
        '{{UnixTime}}': Math.floor(now.getTime() / 1000).toString()
    };

    for (const [placeholder, replacement] of Object.entries(replacements)) {
        processedText = processedText.replaceAll(placeholder, replacement);
    }
    // END: Common variable replacements

    return processedText;
}

/**
 * 获取季节
 */
function getSeason(month) {
    if (month >= 3 && month <= 5) return '春季';
    if (month >= 6 && month <= 8) return '夏季';
    if (month >= 9 && month <= 11) return '秋季';
    return '冬季';
}

/**
 * 处理消息数组中的变量替换
 */
async function processMessagesVariableReplacement(messages, model, AGENT_DIR) {
    return await Promise.all(messages.map(async (msg) => {
        const newMessage = JSON.parse(JSON.stringify(msg));
        if (newMessage.content && typeof newMessage.content === 'string') {
            newMessage.content = await replaceCommonVariables(newMessage.content, model, AGENT_DIR);
        } else if (Array.isArray(newMessage.content)) {
            newMessage.content = await Promise.all(newMessage.content.map(async (part) => {
                if (part.type === 'text' && typeof part.text === 'string') {
                    const newPart = JSON.parse(JSON.stringify(part));
                    newPart.text = await replaceCommonVariables(newPart.text, model, AGENT_DIR);
                    return newPart;
                }
                return part; // 对于image_url类型的part，直接返回不处理
            }));
        }
        return newMessage;
    }));
}

module.exports = {
    replaceCommonVariables,
    processMessagesVariableReplacement
};
