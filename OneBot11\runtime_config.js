/**
 * OneBot11运行时配置
 * 动态配置文件，在运行时生成和更新
 */

const fs = require('fs');
const path = require('path');

class RuntimeConfig {
    constructor(logger) {
        this.logger = logger;
        this.configFile = path.join(__dirname, 'runtime_config.json');
        
        // 默认运行时配置
        this.defaultConfig = {
            // 运行时状态
            runtime: {
                startTime: null,
                lastUpdateTime: null,
                version: '1.0.0',
                processId: process.pid,
                nodeVersion: process.version,
                platform: process.platform
            },
            
            // 连接状态
            connections: {
                onebot11: {
                    connected: false,
                    lastConnectTime: null,
                    lastDisconnectTime: null,
                    reconnectCount: 0,
                    host: null,
                    port: null
                },
                vcp: {
                    connected: false,
                    lastConnectTime: null,
                    lastDisconnectTime: null,
                    reconnectCount: 0,
                    host: null,
                    port: null
                }
            },
            
            // 统计信息
            statistics: {
                messages: {
                    total: 0,
                    private: 0,
                    group: 0,
                    sent: 0,
                    received: 0
                },
                events: {
                    total: 0,
                    notices: 0,
                    requests: 0,
                    metaEvents: 0
                },
                plugins: {
                    loaded: 0,
                    active: 0,
                    errors: 0
                },
                performance: {
                    memoryUsage: 0,
                    cpuUsage: 0,
                    uptime: 0
                }
            },
            
            // 插件状态
            plugins: {},
            
            // 错误日志
            errors: [],
            
            // 性能监控
            performance: {
                lastCheck: null,
                memoryHistory: [],
                cpuHistory: [],
                messageRateHistory: []
            }
        };
        
        this.config = { ...this.defaultConfig };
        this.loadConfig();
    }

    /**
     * 加载运行时配置
     */
    loadConfig() {
        try {
            if (fs.existsSync(this.configFile)) {
                const data = fs.readFileSync(this.configFile, 'utf8');
                const loadedConfig = JSON.parse(data);
                
                // 合并配置，保留默认值
                this.config = this.mergeConfig(this.defaultConfig, loadedConfig);
                
                this.logger.debug('RuntimeConfig', '运行时配置已加载');
            } else {
                this.logger.info('RuntimeConfig', '运行时配置文件不存在，使用默认配置');
                this.saveConfig();
            }
        } catch (error) {
            this.logger.error('RuntimeConfig', `加载运行时配置失败: ${error.message}`);
            this.config = { ...this.defaultConfig };
        }
    }

    /**
     * 保存运行时配置
     */
    saveConfig() {
        try {
            this.config.runtime.lastUpdateTime = new Date().toISOString();
            
            const configData = JSON.stringify(this.config, null, 2);
            fs.writeFileSync(this.configFile, configData, 'utf8');
            
            this.logger.debug('RuntimeConfig', '运行时配置已保存');
        } catch (error) {
            this.logger.error('RuntimeConfig', `保存运行时配置失败: ${error.message}`);
        }
    }

    /**
     * 合并配置
     */
    mergeConfig(defaultConfig, loadedConfig) {
        const merged = { ...defaultConfig };
        
        for (const key in loadedConfig) {
            if (typeof loadedConfig[key] === 'object' && !Array.isArray(loadedConfig[key])) {
                merged[key] = this.mergeConfig(defaultConfig[key] || {}, loadedConfig[key]);
            } else {
                merged[key] = loadedConfig[key];
            }
        }
        
        return merged;
    }

    /**
     * 初始化运行时配置
     */
    initialize() {
        this.config.runtime.startTime = new Date().toISOString();
        this.config.runtime.processId = process.pid;
        this.config.runtime.nodeVersion = process.version;
        this.config.runtime.platform = process.platform;
        
        this.saveConfig();
        this.logger.info('RuntimeConfig', '运行时配置已初始化');
    }

    /**
     * 更新连接状态
     */
    updateConnectionStatus(type, connected, host = null, port = null) {
        if (this.config.connections[type]) {
            this.config.connections[type].connected = connected;
            
            if (connected) {
                this.config.connections[type].lastConnectTime = new Date().toISOString();
                this.config.connections[type].host = host;
                this.config.connections[type].port = port;
            } else {
                this.config.connections[type].lastDisconnectTime = new Date().toISOString();
                this.config.connections[type].reconnectCount++;
            }
            
            this.saveConfig();
        }
    }

    /**
     * 更新统计信息
     */
    updateStatistics(category, subcategory, increment = 1) {
        if (this.config.statistics[category] && this.config.statistics[category][subcategory] !== undefined) {
            this.config.statistics[category][subcategory] += increment;
            this.saveConfig();
        }
    }

    /**
     * 更新插件状态
     */
    updatePluginStatus(pluginName, status) {
        this.config.plugins[pluginName] = {
            ...status,
            lastUpdate: new Date().toISOString()
        };
        
        // 更新插件统计
        const activePlugins = Object.values(this.config.plugins).filter(p => p.active).length;
        this.config.statistics.plugins.active = activePlugins;
        this.config.statistics.plugins.loaded = Object.keys(this.config.plugins).length;
        
        this.saveConfig();
    }

    /**
     * 添加错误日志
     */
    addError(error, source = 'unknown') {
        const errorEntry = {
            timestamp: new Date().toISOString(),
            source: source,
            message: error.message || error,
            stack: error.stack || null
        };
        
        this.config.errors.push(errorEntry);
        
        // 保留最近100条错误
        if (this.config.errors.length > 100) {
            this.config.errors = this.config.errors.slice(-100);
        }
        
        this.saveConfig();
    }

    /**
     * 更新性能监控数据
     */
    updatePerformance() {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        const uptime = process.uptime();
        
        const performanceData = {
            timestamp: new Date().toISOString(),
            memory: {
                rss: memUsage.rss,
                heapTotal: memUsage.heapTotal,
                heapUsed: memUsage.heapUsed,
                external: memUsage.external
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            },
            uptime: uptime
        };
        
        // 更新当前性能数据
        this.config.statistics.performance.memoryUsage = Math.round(memUsage.heapUsed / 1024 / 1024); // MB
        this.config.statistics.performance.uptime = Math.round(uptime);
        
        // 添加到历史记录
        this.config.performance.memoryHistory.push({
            timestamp: performanceData.timestamp,
            value: this.config.statistics.performance.memoryUsage
        });
        
        // 保留最近100条记录
        if (this.config.performance.memoryHistory.length > 100) {
            this.config.performance.memoryHistory = this.config.performance.memoryHistory.slice(-100);
        }
        
        this.config.performance.lastCheck = new Date().toISOString();
        this.saveConfig();
    }

    /**
     * 获取运行时状态
     */
    getStatus() {
        return {
            runtime: this.config.runtime,
            connections: this.config.connections,
            statistics: this.config.statistics,
            plugins: this.config.plugins,
            performance: this.config.performance
        };
    }

    /**
     * 获取统计摘要
     */
    getStatsSummary() {
        const stats = this.config.statistics;
        const runtime = this.config.runtime;
        
        const startTime = new Date(runtime.startTime);
        const uptime = Date.now() - startTime.getTime();
        const uptimeHours = Math.floor(uptime / (1000 * 60 * 60));
        const uptimeMinutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
        
        return {
            uptime: `${uptimeHours}小时${uptimeMinutes}分钟`,
            totalMessages: stats.messages.total,
            totalEvents: stats.events.total,
            activePlugins: stats.plugins.active,
            memoryUsage: `${stats.performance.memoryUsage}MB`,
            connections: {
                onebot11: this.config.connections.onebot11.connected,
                vcp: this.config.connections.vcp.connected
            }
        };
    }

    /**
     * 重置统计信息
     */
    resetStatistics() {
        this.config.statistics = { ...this.defaultConfig.statistics };
        this.config.errors = [];
        this.config.performance.memoryHistory = [];
        this.config.performance.cpuHistory = [];
        this.config.performance.messageRateHistory = [];
        
        this.saveConfig();
        this.logger.info('RuntimeConfig', '统计信息已重置');
    }

    /**
     * 清理旧数据
     */
    cleanup() {
        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        
        // 清理旧的错误日志
        this.config.errors = this.config.errors.filter(error => {
            return new Date(error.timestamp) > oneDayAgo;
        });
        
        // 清理旧的性能数据
        this.config.performance.memoryHistory = this.config.performance.memoryHistory.filter(entry => {
            return new Date(entry.timestamp) > oneDayAgo;
        });
        
        this.saveConfig();
        this.logger.debug('RuntimeConfig', '已清理旧数据');
    }

    /**
     * 获取配置值
     */
    get(path) {
        const keys = path.split('.');
        let value = this.config;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return undefined;
            }
        }
        
        return value;
    }

    /**
     * 设置配置值
     */
    set(path, value) {
        const keys = path.split('.');
        let current = this.config;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        
        current[keys[keys.length - 1]] = value;
        this.saveConfig();
    }

    /**
     * 销毁配置
     */
    destroy() {
        try {
            // 最后一次保存
            this.saveConfig();
            this.logger.info('RuntimeConfig', '运行时配置已销毁');
        } catch (error) {
            this.logger.error('RuntimeConfig', `销毁运行时配置失败: ${error.message}`);
        }
    }
}

module.exports = RuntimeConfig;
