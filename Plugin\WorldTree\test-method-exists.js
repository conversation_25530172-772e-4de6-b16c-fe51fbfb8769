/**
 * 测试方法是否存在
 */

const axios = require('axios');

async function testMethodExists() {
    console.log('🔍 测试getPsychologyActivityLogs方法是否存在...\n');
    
    try {
        // 测试一个简单的API调用来检查方法
        const response = await axios.get('http://localhost:6005/admin_api/worldtree/status');
        const data = response.data;
        
        console.log('世界树状态:', JSON.stringify(data, null, 2));
        
        // 现在测试心理活动日志API，看看是否有错误
        try {
            const logsResponse = await axios.get('http://localhost:6005/admin_api/worldtree/psychology/logs?limit=1');
            const logsData = logsResponse.data;
            
            console.log('\n心理活动日志API响应:', JSON.stringify(logsData, null, 2));
            
            // 检查是否是模拟数据
            if (logsData.success && logsData.logs.length > 0) {
                const firstLog = logsData.logs[0];
                if (firstLog.content === "今天感觉精神状态不错，对新的技术挑战很有兴趣，准备深入研究一些前沿算法。") {
                    console.log('\n❌ 检测到模拟数据，说明getPsychologyActivityLogs方法可能不存在或有错误');
                } else {
                    console.log('\n✅ 检测到真实数据');
                }
            }
            
        } catch (logsError) {
            console.error('\n❌ 心理活动日志API调用失败:', logsError.message);
            if (logsError.response) {
                console.log('错误响应:', logsError.response.data);
            }
        }
        
    } catch (error) {
        console.error('❌ 状态API调用失败:', error.message);
    }
}

testMethodExists();
