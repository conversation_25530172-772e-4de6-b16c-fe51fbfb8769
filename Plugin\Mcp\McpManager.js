// Plugin/Mcp/McpManager.js - MCP插件管理器
const fs = require('fs').promises;
const path = require('path');
const logger = require('../../utils/logger.cjs');

class McpManager {
    constructor() {
        this.mcpPlugins = new Map();
        this.mcpDir = path.join(__dirname);
        this.pluginManager = null; // 将在初始化时设置
    }

    // 设置VCP插件管理器引用
    setPluginManager(pluginManager) {
        this.pluginManager = pluginManager;
    }

    // 初始化MCP插件系统
    async initialize() {
        try {
            logger.info('MCP管理器', '开始初始化MCP插件系统');
            await this.loadMcpPlugins();
            logger.success('MCP管理器', `MCP插件系统初始化完成，共加载${this.mcpPlugins.size}个插件`);
        } catch (error) {
            logger.error('MCP管理器', `MCP插件系统初始化失败: ${error.message}`);
            throw error;
        }
    }

    // 加载所有MCP插件
    async loadMcpPlugins() {
        try {
            const files = await fs.readdir(this.mcpDir);
            
            for (const file of files) {
                if (file.endsWith('.js') && file !== 'McpManager.js' && file !== 'BaseMcpPlugin.js' && file !== 'README.md') {
                    const pluginPath = path.join(this.mcpDir, file);
                    const pluginName = path.basename(file, '.js');
                    
                    try {
                        // 清除require缓存以支持热重载
                        delete require.cache[require.resolve(pluginPath)];
                        
                        const McpPlugin = require(pluginPath);
                        const pluginInstance = new McpPlugin();
                        
                        // 验证插件接口
                        if (!this.validateMcpPlugin(pluginInstance)) {
                            logger.warning('MCP管理器', `插件 ${pluginName} 接口验证失败，跳过加载`);
                            continue;
                        }
                        
                        this.mcpPlugins.set(pluginName, pluginInstance);
                        logger.info('MCP管理器', `已加载MCP插件: ${pluginName}`);
                        
                    } catch (error) {
                        logger.error('MCP管理器', `加载MCP插件 ${pluginName} 失败: ${error.message}`);
                    }
                }
            }
        } catch (error) {
            logger.error('MCP管理器', `读取MCP插件目录失败: ${error.message}`);
        }
    }

    // 验证MCP插件接口
    validateMcpPlugin(plugin) {
        const requiredMethods = ['getName', 'getDescription', 'getParameters', 'execute'];
        const requiredProperties = ['name', 'description'];
        
        for (const method of requiredMethods) {
            if (typeof plugin[method] !== 'function') {
                logger.error('MCP管理器', `插件缺少必需方法: ${method}`);
                return false;
            }
        }
        
        for (const prop of requiredProperties) {
            if (!plugin[prop]) {
                logger.error('MCP管理器', `插件缺少必需属性: ${prop}`);
                return false;
            }
        }
        
        return true;
    }

    // 获取所有可用的MCP插件
    getAvailableMcpPlugins() {
        const plugins = [];
        
        for (const [name, plugin] of this.mcpPlugins) {
            try {
                plugins.push({
                    name: plugin.getName(),
                    description: plugin.getDescription(),
                    parameters: plugin.getParameters(),
                    enabled: plugin.enabled !== false
                });
            } catch (error) {
                logger.error('MCP管理器', `获取插件 ${name} 信息失败: ${error.message}`);
            }
        }
        
        return plugins;
    }

    // 获取OpenAI Tools格式的工具列表
    getOpenAITools() {
        const tools = [];
        
        for (const [name, plugin] of this.mcpPlugins) {
            if (plugin.enabled === false) continue;
            
            try {
                tools.push({
                    type: 'function',
                    function: {
                        name: plugin.getName(),
                        description: plugin.getDescription(),
                        parameters: plugin.getParameters()
                    }
                });
            } catch (error) {
                logger.error('MCP管理器', `生成OpenAI工具定义失败 ${name}: ${error.message}`);
            }
        }
        
        return tools;
    }

    // 执行MCP插件
    async executeMcpPlugin(pluginName, args) {
        const plugin = this.mcpPlugins.get(pluginName);
        
        if (!plugin) {
            throw new Error(`MCP插件 ${pluginName} 不存在`);
        }
        
        if (plugin.enabled === false) {
            throw new Error(`MCP插件 ${pluginName} 已禁用`);
        }
        
        try {
            logger.info('MCP管理器', `执行MCP插件: ${pluginName}`);
            const result = await plugin.execute(args);
            logger.success('MCP管理器', `MCP插件 ${pluginName} 执行成功`);
            return result;
        } catch (error) {
            logger.error('MCP管理器', `MCP插件 ${pluginName} 执行失败: ${error.message}`);
            throw error;
        }
    }

    // 通过VCP名称查找对应的MCP插件
    findMcpByVcpName(vcpName) {
        for (const [mcpName, plugin] of this.mcpPlugins) {
            if (plugin.vcpName === vcpName || mcpName === vcpName) {
                return mcpName;
            }
        }
        return null;
    }

    // 重新加载MCP插件
    async reloadMcpPlugins() {
        logger.info('MCP管理器', '重新加载MCP插件');
        this.mcpPlugins.clear();
        await this.loadMcpPlugins();
        logger.success('MCP管理器', `MCP插件重新加载完成，共${this.mcpPlugins.size}个插件`);
    }

    // 启用/禁用MCP插件
    setMcpPluginEnabled(pluginName, enabled) {
        const plugin = this.mcpPlugins.get(pluginName);
        if (plugin) {
            plugin.enabled = enabled;
            logger.info('MCP管理器', `MCP插件 ${pluginName} 已${enabled ? '启用' : '禁用'}`);
            return true;
        }
        return false;
    }

    // 获取MCP插件配置
    getMcpPluginConfig(pluginName) {
        const plugin = this.mcpPlugins.get(pluginName);
        if (plugin && typeof plugin.getConfig === 'function') {
            return plugin.getConfig();
        }
        return null;
    }

    // 设置MCP插件配置
    setMcpPluginConfig(pluginName, config) {
        const plugin = this.mcpPlugins.get(pluginName);
        if (plugin && typeof plugin.setConfig === 'function') {
            plugin.setConfig(config);
            logger.info('MCP管理器', `MCP插件 ${pluginName} 配置已更新`);
            return true;
        }
        return false;
    }
}

module.exports = McpManager; 