# Generated by h2py from COMMCTRL.H
WM_USER = 1024
ICC_LISTVIEW_CLASSES = 1  # listview, header
ICC_TREEVIEW_CLASSES = 2  # treeview, tooltips
ICC_BAR_CLASSES = 4  # toolbar, statusbar, trackbar, tooltips
ICC_TAB_CLASSES = 8  # tab, tooltips
ICC_UPDOWN_CLASS = 16  # updown
ICC_PROGRESS_CLASS = 32  # progress
ICC_HOTKEY_CLASS = 64  # hotkey
ICC_ANIMATE_CLASS = 128  # animate
ICC_WIN95_CLASSES = 255
ICC_DATE_CLASSES = 256  # month picker, date picker, time picker, updown
ICC_USEREX_CLASSES = 512  # comboex
ICC_COOL_CLASSES = 1024  # rebar (coolbar) control
ICC_INTERNET_CLASSES = 2048
ICC_PAGESCROLLER_CLASS = 4096  # page scroller
ICC_NATIVEFNTCTL_CLASS = 8192  # native font control
ODT_HEADER = 100
ODT_TAB = 101
ODT_LISTVIEW = 102
PY_0U = 0
NM_FIRST = PY_0U  # generic to all controls
NM_LAST = PY_0U - 99
LVN_FIRST = PY_0U - 100  # listview
LVN_LAST = PY_0U - 199
HDN_FIRST = PY_0U - 300  # header
HDN_LAST = PY_0U - 399
TVN_FIRST = PY_0U - 400  # treeview
TVN_LAST = PY_0U - 499
TTN_FIRST = PY_0U - 520  # tooltips
TTN_LAST = PY_0U - 549
TCN_FIRST = PY_0U - 550  # tab control
TCN_LAST = PY_0U - 580
CDN_FIRST = PY_0U - 601  # common dialog (new)
CDN_LAST = PY_0U - 699
TBN_FIRST = PY_0U - 700  # toolbar
TBN_LAST = PY_0U - 720
UDN_FIRST = PY_0U - 721  # updown
UDN_LAST = PY_0U - 740
MCN_FIRST = PY_0U - 750  # monthcal
MCN_LAST = PY_0U - 759
DTN_FIRST = PY_0U - 760  # datetimepick
DTN_LAST = PY_0U - 799
CBEN_FIRST = PY_0U - 800  # combo box ex
CBEN_LAST = PY_0U - 830
RBN_FIRST = PY_0U - 831  # rebar
RBN_LAST = PY_0U - 859
IPN_FIRST = PY_0U - 860  # internet address
IPN_LAST = PY_0U - 879  # internet address
SBN_FIRST = PY_0U - 880  # status bar
SBN_LAST = PY_0U - 899
PGN_FIRST = PY_0U - 900  # Pager Control
PGN_LAST = PY_0U - 950
LVM_FIRST = 4096  # ListView messages
TV_FIRST = 4352  # TreeView messages
HDM_FIRST = 4608  # Header messages
TCM_FIRST = 4864  # Tab control messages
PGM_FIRST = 5120  # Pager control messages
CCM_FIRST = 8192  # Common control shared messages
CCM_SETBKCOLOR = CCM_FIRST + 1  # lParam is bkColor
CCM_SETCOLORSCHEME = CCM_FIRST + 2  # lParam is color scheme
CCM_GETCOLORSCHEME = CCM_FIRST + 3  # fills in COLORSCHEME pointed to by lParam
CCM_GETDROPTARGET = CCM_FIRST + 4
CCM_SETUNICODEFORMAT = CCM_FIRST + 5
CCM_GETUNICODEFORMAT = CCM_FIRST + 6
INFOTIPSIZE = 1024
NM_OUTOFMEMORY = NM_FIRST - 1
NM_CLICK = NM_FIRST - 2  # uses NMCLICK struct
NM_DBLCLK = NM_FIRST - 3
NM_RETURN = NM_FIRST - 4
NM_RCLICK = NM_FIRST - 5  # uses NMCLICK struct
NM_RDBLCLK = NM_FIRST - 6
NM_SETFOCUS = NM_FIRST - 7
NM_KILLFOCUS = NM_FIRST - 8
NM_CUSTOMDRAW = NM_FIRST - 12
NM_HOVER = NM_FIRST - 13
NM_NCHITTEST = NM_FIRST - 14  # uses NMMOUSE struct
NM_KEYDOWN = NM_FIRST - 15  # uses NMKEY struct
NM_RELEASEDCAPTURE = NM_FIRST - 16
NM_SETCURSOR = NM_FIRST - 17  # uses NMMOUSE struct
NM_CHAR = NM_FIRST - 18  # uses NMCHAR struct
MSGF_COMMCTRL_BEGINDRAG = 16896
MSGF_COMMCTRL_SIZEHEADER = 16897
MSGF_COMMCTRL_DRAGSELECT = 16898
MSGF_COMMCTRL_TOOLBARCUST = 16899
CDRF_DODEFAULT = 0
CDRF_NEWFONT = 2
CDRF_SKIPDEFAULT = 4
CDRF_NOTIFYPOSTPAINT = 16
CDRF_NOTIFYITEMDRAW = 32
CDRF_NOTIFYSUBITEMDRAW = 32  # flags are the same, we can distinguish by context
CDRF_NOTIFYPOSTERASE = 64
CDDS_PREPAINT = 1
CDDS_POSTPAINT = 2
CDDS_PREERASE = 3
CDDS_POSTERASE = 4
CDDS_ITEM = 65536
CDDS_ITEMPREPAINT = CDDS_ITEM | CDDS_PREPAINT
CDDS_ITEMPOSTPAINT = CDDS_ITEM | CDDS_POSTPAINT
CDDS_ITEMPREERASE = CDDS_ITEM | CDDS_PREERASE
CDDS_ITEMPOSTERASE = CDDS_ITEM | CDDS_POSTERASE
CDDS_SUBITEM = 131072
CDIS_SELECTED = 1
CDIS_GRAYED = 2
CDIS_DISABLED = 4
CDIS_CHECKED = 8
CDIS_FOCUS = 16
CDIS_DEFAULT = 32
CDIS_HOT = 64
CDIS_MARKED = 128
CDIS_INDETERMINATE = 256
CLR_NONE = -1  # 0xFFFFFFFFL
CLR_DEFAULT = -16777216  # 0xFF000000L
ILC_MASK = 1
ILC_COLOR = 0
ILC_COLORDDB = 254
ILC_COLOR4 = 4
ILC_COLOR8 = 8
ILC_COLOR16 = 16
ILC_COLOR24 = 24
ILC_COLOR32 = 32
ILC_PALETTE = 2048  # (not implemented)
ILD_NORMAL = 0
ILD_TRANSPARENT = 1
ILD_MASK = 16
ILD_IMAGE = 32
ILD_ROP = 64
ILD_BLEND25 = 2
ILD_BLEND50 = 4
ILD_OVERLAYMASK = 3840
ILD_SELECTED = ILD_BLEND50
ILD_FOCUS = ILD_BLEND25
ILD_BLEND = ILD_BLEND50
CLR_HILIGHT = CLR_DEFAULT
ILCF_MOVE = 0
ILCF_SWAP = 1
WC_HEADERA = "SysHeader32"
WC_HEADER = WC_HEADERA
HDS_HORZ = 0
HDS_BUTTONS = 2
HDS_HOTTRACK = 4
HDS_HIDDEN = 8
HDS_DRAGDROP = 64
HDS_FULLDRAG = 128
HDI_WIDTH = 1
HDI_HEIGHT = HDI_WIDTH
HDI_TEXT = 2
HDI_FORMAT = 4
HDI_LPARAM = 8
HDI_BITMAP = 16
HDI_IMAGE = 32
HDI_DI_SETITEM = 64
HDI_ORDER = 128
HDF_LEFT = 0
HDF_RIGHT = 1
HDF_CENTER = 2
HDF_JUSTIFYMASK = 3
HDF_RTLREADING = 4
HDF_OWNERDRAW = 32768
HDF_STRING = 16384
HDF_BITMAP = 8192
HDF_BITMAP_ON_RIGHT = 4096
HDF_IMAGE = 2048
HDM_GETITEMCOUNT = HDM_FIRST + 0
HDM_INSERTITEMA = HDM_FIRST + 1
HDM_INSERTITEMW = HDM_FIRST + 10
HDM_INSERTITEM = HDM_INSERTITEMA
HDM_DELETEITEM = HDM_FIRST + 2
HDM_GETITEMA = HDM_FIRST + 3
HDM_GETITEMW = HDM_FIRST + 11
HDM_GETITEM = HDM_GETITEMA
HDM_SETITEMA = HDM_FIRST + 4
HDM_SETITEMW = HDM_FIRST + 12
HDM_SETITEM = HDM_SETITEMA
HDM_LAYOUT = HDM_FIRST + 5
HHT_NOWHERE = 1
HHT_ONHEADER = 2
HHT_ONDIVIDER = 4
HHT_ONDIVOPEN = 8
HHT_ABOVE = 256
HHT_BELOW = 512
HHT_TORIGHT = 1024
HHT_TOLEFT = 2048
HDM_HITTEST = HDM_FIRST + 6
HDM_GETITEMRECT = HDM_FIRST + 7
HDM_SETIMAGELIST = HDM_FIRST + 8
HDM_GETIMAGELIST = HDM_FIRST + 9
HDM_ORDERTOINDEX = HDM_FIRST + 15
HDM_CREATEDRAGIMAGE = HDM_FIRST + 16  # wparam = which item (by index)
HDM_GETORDERARRAY = HDM_FIRST + 17
HDM_SETORDERARRAY = HDM_FIRST + 18
HDM_SETHOTDIVIDER = HDM_FIRST + 19
HDM_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
HDM_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
HDN_ITEMCHANGINGA = HDN_FIRST - 0
HDN_ITEMCHANGINGW = HDN_FIRST - 20
HDN_ITEMCHANGEDA = HDN_FIRST - 1
HDN_ITEMCHANGEDW = HDN_FIRST - 21
HDN_ITEMCLICKA = HDN_FIRST - 2
HDN_ITEMCLICKW = HDN_FIRST - 22
HDN_ITEMDBLCLICKA = HDN_FIRST - 3
HDN_ITEMDBLCLICKW = HDN_FIRST - 23
HDN_DIVIDERDBLCLICKA = HDN_FIRST - 5
HDN_DIVIDERDBLCLICKW = HDN_FIRST - 25
HDN_BEGINTRACKA = HDN_FIRST - 6
HDN_BEGINTRACKW = HDN_FIRST - 26
HDN_ENDTRACKA = HDN_FIRST - 7
HDN_ENDTRACKW = HDN_FIRST - 27
HDN_TRACKA = HDN_FIRST - 8
HDN_TRACKW = HDN_FIRST - 28
HDN_GETDISPINFOA = HDN_FIRST - 9
HDN_GETDISPINFOW = HDN_FIRST - 29
HDN_BEGINDRAG = HDN_FIRST - 10
HDN_ENDDRAG = HDN_FIRST - 11
HDN_ITEMCHANGING = HDN_ITEMCHANGINGA
HDN_ITEMCHANGED = HDN_ITEMCHANGEDA
HDN_ITEMCLICK = HDN_ITEMCLICKA
HDN_ITEMDBLCLICK = HDN_ITEMDBLCLICKA
HDN_DIVIDERDBLCLICK = HDN_DIVIDERDBLCLICKA
HDN_BEGINTRACK = HDN_BEGINTRACKA
HDN_ENDTRACK = HDN_ENDTRACKA
HDN_TRACK = HDN_TRACKA
HDN_GETDISPINFO = HDN_GETDISPINFOA
TOOLBARCLASSNAMEA = "ToolbarWindow32"
TOOLBARCLASSNAME = TOOLBARCLASSNAMEA
CMB_MASKED = 2
TBSTATE_CHECKED = 1
TBSTATE_PRESSED = 2
TBSTATE_ENABLED = 4
TBSTATE_HIDDEN = 8
TBSTATE_INDETERMINATE = 16
TBSTATE_WRAP = 32
TBSTATE_ELLIPSES = 64
TBSTATE_MARKED = 128
TBSTYLE_BUTTON = 0
TBSTYLE_SEP = 1
TBSTYLE_CHECK = 2
TBSTYLE_GROUP = 4
TBSTYLE_CHECKGROUP = TBSTYLE_GROUP | TBSTYLE_CHECK
TBSTYLE_DROPDOWN = 8
TBSTYLE_AUTOSIZE = 16  # automatically calculate the cx of the button
TBSTYLE_NOPREFIX = 32  # if this button should not have accel prefix
TBSTYLE_TOOLTIPS = 256
TBSTYLE_WRAPABLE = 512
TBSTYLE_ALTDRAG = 1024
TBSTYLE_FLAT = 2048
TBSTYLE_LIST = 4096
TBSTYLE_CUSTOMERASE = 8192
TBSTYLE_REGISTERDROP = 16384
TBSTYLE_TRANSPARENT = 32768
TBSTYLE_EX_DRAWDDARROWS = 1
BTNS_BUTTON = TBSTYLE_BUTTON
BTNS_SEP = TBSTYLE_SEP  # 0x0001
BTNS_CHECK = TBSTYLE_CHECK  # 0x0002
BTNS_GROUP = TBSTYLE_GROUP  # 0x0004
BTNS_CHECKGROUP = TBSTYLE_CHECKGROUP  # (TBSTYLE_GROUP | TBSTYLE_CHECK)
BTNS_DROPDOWN = TBSTYLE_DROPDOWN  # 0x0008
BTNS_AUTOSIZE = TBSTYLE_AUTOSIZE  # 0x0010; automatically calculate the cx of the button
BTNS_NOPREFIX = TBSTYLE_NOPREFIX  # 0x0020; this button should not have accel prefix
BTNS_SHOWTEXT = (
    64  # 0x0040              // ignored unless TBSTYLE_EX_MIXEDBUTTONS is set
)
BTNS_WHOLEDROPDOWN = (
    128  # 0x0080          // draw drop-down arrow, but without split arrow section
)
TBCDRF_NOEDGES = 65536  # Don't draw button edges
TBCDRF_HILITEHOTTRACK = 131072  # Use color of the button bk when hottracked
TBCDRF_NOOFFSET = 262144  # Don't offset button if pressed
TBCDRF_NOMARK = 524288  # Don't draw default highlight of image/text for TBSTATE_MARKED
TBCDRF_NOETCHEDEFFECT = 1048576  # Don't draw etched effect for disabled items
TB_ENABLEBUTTON = WM_USER + 1
TB_CHECKBUTTON = WM_USER + 2
TB_PRESSBUTTON = WM_USER + 3
TB_HIDEBUTTON = WM_USER + 4
TB_INDETERMINATE = WM_USER + 5
TB_MARKBUTTON = WM_USER + 6
TB_ISBUTTONENABLED = WM_USER + 9
TB_ISBUTTONCHECKED = WM_USER + 10
TB_ISBUTTONPRESSED = WM_USER + 11
TB_ISBUTTONHIDDEN = WM_USER + 12
TB_ISBUTTONINDETERMINATE = WM_USER + 13
TB_ISBUTTONHIGHLIGHTED = WM_USER + 14
TB_SETSTATE = WM_USER + 17
TB_GETSTATE = WM_USER + 18
TB_ADDBITMAP = WM_USER + 19
HINST_COMMCTRL = -1
IDB_STD_SMALL_COLOR = 0
IDB_STD_LARGE_COLOR = 1
IDB_VIEW_SMALL_COLOR = 4
IDB_VIEW_LARGE_COLOR = 5
IDB_HIST_SMALL_COLOR = 8
IDB_HIST_LARGE_COLOR = 9
STD_CUT = 0
STD_COPY = 1
STD_PASTE = 2
STD_UNDO = 3
STD_REDOW = 4
STD_DELETE = 5
STD_FILENEW = 6
STD_FILEOPEN = 7
STD_FILESAVE = 8
STD_PRINTPRE = 9
STD_PROPERTIES = 10
STD_HELP = 11
STD_FIND = 12
STD_REPLACE = 13
STD_PRINT = 14
VIEW_LARGEICONS = 0
VIEW_SMALLICONS = 1
VIEW_LIST = 2
VIEW_DETAILS = 3
VIEW_SORTNAME = 4
VIEW_SORTSIZE = 5
VIEW_SORTDATE = 6
VIEW_SORTTYPE = 7
VIEW_PARENTFOLDER = 8
VIEW_NETCONNECT = 9
VIEW_NETDISCONNECT = 10
VIEW_NEWFOLDER = 11
VIEW_VIEWMENU = 12
HIST_BACK = 0
HIST_FORWARD = 1
HIST_FAVORITES = 2
HIST_ADDTOFAVORITES = 3
HIST_VIEWTREE = 4
TB_ADDBUTTONSA = WM_USER + 20
TB_INSERTBUTTONA = WM_USER + 21
TB_ADDBUTTONS = WM_USER + 20
TB_INSERTBUTTON = WM_USER + 21
TB_DELETEBUTTON = WM_USER + 22
TB_GETBUTTON = WM_USER + 23
TB_BUTTONCOUNT = WM_USER + 24
TB_COMMANDTOINDEX = WM_USER + 25
TB_SAVERESTOREA = WM_USER + 26
TB_SAVERESTOREW = WM_USER + 76
TB_CUSTOMIZE = WM_USER + 27
TB_ADDSTRINGA = WM_USER + 28
TB_ADDSTRINGW = WM_USER + 77
TB_GETITEMRECT = WM_USER + 29
TB_BUTTONSTRUCTSIZE = WM_USER + 30
TB_SETBUTTONSIZE = WM_USER + 31
TB_SETBITMAPSIZE = WM_USER + 32
TB_AUTOSIZE = WM_USER + 33
TB_GETTOOLTIPS = WM_USER + 35
TB_SETTOOLTIPS = WM_USER + 36
TB_SETPARENT = WM_USER + 37
TB_SETROWS = WM_USER + 39
TB_GETROWS = WM_USER + 40
TB_SETCMDID = WM_USER + 42
TB_CHANGEBITMAP = WM_USER + 43
TB_GETBITMAP = WM_USER + 44
TB_GETBUTTONTEXTA = WM_USER + 45
TB_GETBUTTONTEXTW = WM_USER + 75
TB_REPLACEBITMAP = WM_USER + 46
TB_SETINDENT = WM_USER + 47
TB_SETIMAGELIST = WM_USER + 48
TB_GETIMAGELIST = WM_USER + 49
TB_LOADIMAGES = WM_USER + 50
TB_GETRECT = WM_USER + 51  # wParam is the Cmd instead of index
TB_SETHOTIMAGELIST = WM_USER + 52
TB_GETHOTIMAGELIST = WM_USER + 53
TB_SETDISABLEDIMAGELIST = WM_USER + 54
TB_GETDISABLEDIMAGELIST = WM_USER + 55
TB_SETSTYLE = WM_USER + 56
TB_GETSTYLE = WM_USER + 57
TB_GETBUTTONSIZE = WM_USER + 58
TB_SETBUTTONWIDTH = WM_USER + 59
TB_SETMAXTEXTROWS = WM_USER + 60
TB_GETTEXTROWS = WM_USER + 61
TB_GETBUTTONTEXT = TB_GETBUTTONTEXTA
TB_SAVERESTORE = TB_SAVERESTOREA
TB_ADDSTRING = TB_ADDSTRINGA
TB_GETOBJECT = WM_USER + 62  # wParam == IID, lParam void **ppv
TB_GETHOTITEM = WM_USER + 71
TB_SETHOTITEM = WM_USER + 72  # wParam == iHotItem
TB_SETANCHORHIGHLIGHT = WM_USER + 73  # wParam == TRUE/FALSE
TB_GETANCHORHIGHLIGHT = WM_USER + 74
TB_MAPACCELERATORA = WM_USER + 78  # wParam == ch, lParam int * pidBtn
TBIMHT_AFTER = 1  # TRUE = insert After iButton, otherwise before
TBIMHT_BACKGROUND = 2  # TRUE iff missed buttons completely
TB_GETINSERTMARK = WM_USER + 79  # lParam == LPTBINSERTMARK
TB_SETINSERTMARK = WM_USER + 80  # lParam == LPTBINSERTMARK
TB_INSERTMARKHITTEST = WM_USER + 81  # wParam == LPPOINT lParam == LPTBINSERTMARK
TB_MOVEBUTTON = WM_USER + 82
TB_GETMAXSIZE = WM_USER + 83  # lParam == LPSIZE
TB_SETEXTENDEDSTYLE = WM_USER + 84  # For TBSTYLE_EX_*
TB_GETEXTENDEDSTYLE = WM_USER + 85  # For TBSTYLE_EX_*
TB_GETPADDING = WM_USER + 86
TB_SETPADDING = WM_USER + 87
TB_SETINSERTMARKCOLOR = WM_USER + 88
TB_GETINSERTMARKCOLOR = WM_USER + 89
TB_SETCOLORSCHEME = CCM_SETCOLORSCHEME  # lParam is color scheme
TB_GETCOLORSCHEME = CCM_GETCOLORSCHEME  # fills in COLORSCHEME pointed to by lParam
TB_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
TB_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
TB_MAPACCELERATORW = WM_USER + 90  # wParam == ch, lParam int * pidBtn
TB_MAPACCELERATOR = TB_MAPACCELERATORA
TBBF_LARGE = 1
TB_GETBITMAPFLAGS = WM_USER + 41
TBIF_IMAGE = 1
TBIF_TEXT = 2
TBIF_STATE = 4
TBIF_STYLE = 8
TBIF_LPARAM = 16
TBIF_COMMAND = 32
TBIF_SIZE = 64
TB_GETBUTTONINFOW = WM_USER + 63
TB_SETBUTTONINFOW = WM_USER + 64
TB_GETBUTTONINFOA = WM_USER + 65
TB_SETBUTTONINFOA = WM_USER + 66
TB_INSERTBUTTONW = WM_USER + 67
TB_ADDBUTTONSW = WM_USER + 68
TB_HITTEST = WM_USER + 69
TB_SETDRAWTEXTFLAGS = WM_USER + 70  # wParam == mask lParam == bit values
TBN_GETBUTTONINFOA = TBN_FIRST - 0
TBN_GETBUTTONINFOW = TBN_FIRST - 20
TBN_BEGINDRAG = TBN_FIRST - 1
TBN_ENDDRAG = TBN_FIRST - 2
TBN_BEGINADJUST = TBN_FIRST - 3
TBN_ENDADJUST = TBN_FIRST - 4
TBN_RESET = TBN_FIRST - 5
TBN_QUERYINSERT = TBN_FIRST - 6
TBN_QUERYDELETE = TBN_FIRST - 7
TBN_TOOLBARCHANGE = TBN_FIRST - 8
TBN_CUSTHELP = TBN_FIRST - 9
TBN_DROPDOWN = TBN_FIRST - 10
TBN_GETOBJECT = TBN_FIRST - 12
HICF_OTHER = 0
HICF_MOUSE = 1  # Triggered by mouse
HICF_ARROWKEYS = 2  # Triggered by arrow keys
HICF_ACCELERATOR = 4  # Triggered by accelerator
HICF_DUPACCEL = 8  # This accelerator is not unique
HICF_ENTERING = 16  # idOld is invalid
HICF_LEAVING = 32  # idNew is invalid
HICF_RESELECT = 64  # hot item reselected
TBN_HOTITEMCHANGE = TBN_FIRST - 13
TBN_DRAGOUT = (
    TBN_FIRST - 14
)  # this is sent when the user clicks down on a button then drags off the button
TBN_DELETINGBUTTON = TBN_FIRST - 15  # uses TBNOTIFY
TBN_GETDISPINFOA = (
    TBN_FIRST - 16
)  # This is sent when the  toolbar needs  some display information
TBN_GETDISPINFOW = (
    TBN_FIRST - 17
)  # This is sent when the  toolbar needs  some display information
TBN_GETINFOTIPA = TBN_FIRST - 18
TBN_GETINFOTIPW = TBN_FIRST - 19
TBN_GETINFOTIP = TBN_GETINFOTIPA
TBNF_IMAGE = 1
TBNF_TEXT = 2
TBNF_DI_SETITEM = 268435456
TBN_GETDISPINFO = TBN_GETDISPINFOA
TBDDRET_DEFAULT = 0
TBDDRET_NODEFAULT = 1
TBDDRET_TREATPRESSED = 2  # Treat as a standard press button
TBN_GETBUTTONINFO = TBN_GETBUTTONINFOA
REBARCLASSNAMEA = "ReBarWindow32"
REBARCLASSNAME = REBARCLASSNAMEA
RBIM_IMAGELIST = 1
RBS_TOOLTIPS = 256
RBS_VARHEIGHT = 512
RBS_BANDBORDERS = 1024
RBS_FIXEDORDER = 2048
RBS_REGISTERDROP = 4096
RBS_AUTOSIZE = 8192
RBS_VERTICALGRIPPER = (
    16384  # this always has the vertical gripper (default for horizontal mode)
)
RBS_DBLCLKTOGGLE = 32768
RBBS_BREAK = 1  # break to new line
RBBS_FIXEDSIZE = 2  # band can't be sized
RBBS_CHILDEDGE = 4  # edge around top & bottom of child window
RBBS_HIDDEN = 8  # don't show
RBBS_NOVERT = 16  # don't show when vertical
RBBS_FIXEDBMP = 32  # bitmap doesn't move during band resize
RBBS_VARIABLEHEIGHT = 64  # allow autosizing of this child vertically
RBBS_GRIPPERALWAYS = 128  # always show the gripper
RBBS_NOGRIPPER = 256  # never show the gripper
RBBIM_STYLE = 1
RBBIM_COLORS = 2
RBBIM_TEXT = 4
RBBIM_IMAGE = 8
RBBIM_CHILD = 16
RBBIM_CHILDSIZE = 32
RBBIM_SIZE = 64
RBBIM_BACKGROUND = 128
RBBIM_ID = 256
RBBIM_IDEALSIZE = 512
RBBIM_LPARAM = 1024
RB_INSERTBANDA = WM_USER + 1
RB_DELETEBAND = WM_USER + 2
RB_GETBARINFO = WM_USER + 3
RB_SETBARINFO = WM_USER + 4
RB_SETBANDINFOA = WM_USER + 6
RB_SETPARENT = WM_USER + 7
RB_HITTEST = WM_USER + 8
RB_GETRECT = WM_USER + 9
RB_INSERTBANDW = WM_USER + 10
RB_SETBANDINFOW = WM_USER + 11
RB_GETBANDCOUNT = WM_USER + 12
RB_GETROWCOUNT = WM_USER + 13
RB_GETROWHEIGHT = WM_USER + 14
RB_IDTOINDEX = WM_USER + 16  # wParam == id
RB_GETTOOLTIPS = WM_USER + 17
RB_SETTOOLTIPS = WM_USER + 18
RB_SETBKCOLOR = WM_USER + 19  # sets the default BK color
RB_GETBKCOLOR = WM_USER + 20  # defaults to CLR_NONE
RB_SETTEXTCOLOR = WM_USER + 21
RB_GETTEXTCOLOR = WM_USER + 22  # defaults to 0x00000000
RB_SIZETORECT = (
    WM_USER + 23
)  # resize the rebar/break bands and such to this rect (lparam)
RB_SETCOLORSCHEME = CCM_SETCOLORSCHEME  # lParam is color scheme
RB_GETCOLORSCHEME = CCM_GETCOLORSCHEME  # fills in COLORSCHEME pointed to by lParam
RB_INSERTBAND = RB_INSERTBANDA
RB_SETBANDINFO = RB_SETBANDINFOA
RB_BEGINDRAG = WM_USER + 24
RB_ENDDRAG = WM_USER + 25
RB_DRAGMOVE = WM_USER + 26
RB_GETBARHEIGHT = WM_USER + 27
RB_GETBANDINFOW = WM_USER + 28
RB_GETBANDINFOA = WM_USER + 29
RB_GETBANDINFO = RB_GETBANDINFOA
RB_MINIMIZEBAND = WM_USER + 30
RB_MAXIMIZEBAND = WM_USER + 31
RB_GETDROPTARGET = CCM_GETDROPTARGET
RB_GETBANDBORDERS = (
    WM_USER + 34
)  # returns in lparam = lprc the amount of edges added to band wparam
RB_SHOWBAND = WM_USER + 35  # show/hide band
RB_SETPALETTE = WM_USER + 37
RB_GETPALETTE = WM_USER + 38
RB_MOVEBAND = WM_USER + 39
RB_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
RB_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
RBN_HEIGHTCHANGE = RBN_FIRST - 0
RBN_GETOBJECT = RBN_FIRST - 1
RBN_LAYOUTCHANGED = RBN_FIRST - 2
RBN_AUTOSIZE = RBN_FIRST - 3
RBN_BEGINDRAG = RBN_FIRST - 4
RBN_ENDDRAG = RBN_FIRST - 5
RBN_DELETINGBAND = RBN_FIRST - 6  # Uses NMREBAR
RBN_DELETEDBAND = RBN_FIRST - 7  # Uses NMREBAR
RBN_CHILDSIZE = RBN_FIRST - 8
RBNM_ID = 1
RBNM_STYLE = 2
RBNM_LPARAM = 4
RBHT_NOWHERE = 1
RBHT_CAPTION = 2
RBHT_CLIENT = 3
RBHT_GRABBER = 4
TOOLTIPS_CLASSA = "tooltips_class32"
TOOLTIPS_CLASS = TOOLTIPS_CLASSA
TTS_ALWAYSTIP = 1
TTS_NOPREFIX = 2
TTF_IDISHWND = 1
TTF_CENTERTIP = 2
TTF_RTLREADING = 4
TTF_SUBCLASS = 16
TTF_TRACK = 32
TTF_ABSOLUTE = 128
TTF_TRANSPARENT = 256
TTF_DI_SETITEM = 32768  # valid only on the TTN_NEEDTEXT callback
TTDT_AUTOMATIC = 0
TTDT_RESHOW = 1
TTDT_AUTOPOP = 2
TTDT_INITIAL = 3
TTM_ACTIVATE = WM_USER + 1
TTM_SETDELAYTIME = WM_USER + 3
TTM_ADDTOOLA = WM_USER + 4
TTM_ADDTOOLW = WM_USER + 50
TTM_DELTOOLA = WM_USER + 5
TTM_DELTOOLW = WM_USER + 51
TTM_NEWTOOLRECTA = WM_USER + 6
TTM_NEWTOOLRECTW = WM_USER + 52
TTM_RELAYEVENT = WM_USER + 7
TTM_GETTOOLINFOA = WM_USER + 8
TTM_GETTOOLINFOW = WM_USER + 53
TTM_SETTOOLINFOA = WM_USER + 9
TTM_SETTOOLINFOW = WM_USER + 54
TTM_HITTESTA = WM_USER + 10
TTM_HITTESTW = WM_USER + 55
TTM_GETTEXTA = WM_USER + 11
TTM_GETTEXTW = WM_USER + 56
TTM_UPDATETIPTEXTA = WM_USER + 12
TTM_UPDATETIPTEXTW = WM_USER + 57
TTM_GETTOOLCOUNT = WM_USER + 13
TTM_ENUMTOOLSA = WM_USER + 14
TTM_ENUMTOOLSW = WM_USER + 58
TTM_GETCURRENTTOOLA = WM_USER + 15
TTM_GETCURRENTTOOLW = WM_USER + 59
TTM_WINDOWFROMPOINT = WM_USER + 16
TTM_TRACKACTIVATE = WM_USER + 17  # wParam = TRUE/FALSE start end  lparam = LPTOOLINFO
TTM_TRACKPOSITION = WM_USER + 18  # lParam = dwPos
TTM_SETTIPBKCOLOR = WM_USER + 19
TTM_SETTIPTEXTCOLOR = WM_USER + 20
TTM_GETDELAYTIME = WM_USER + 21
TTM_GETTIPBKCOLOR = WM_USER + 22
TTM_GETTIPTEXTCOLOR = WM_USER + 23
TTM_SETMAXTIPWIDTH = WM_USER + 24
TTM_GETMAXTIPWIDTH = WM_USER + 25
TTM_SETMARGIN = WM_USER + 26  # lParam = lprc
TTM_GETMARGIN = WM_USER + 27  # lParam = lprc
TTM_POP = WM_USER + 28
TTM_UPDATE = WM_USER + 29
TTM_ADDTOOL = TTM_ADDTOOLA
TTM_DELTOOL = TTM_DELTOOLA
TTM_NEWTOOLRECT = TTM_NEWTOOLRECTA
TTM_GETTOOLINFO = TTM_GETTOOLINFOA
TTM_SETTOOLINFO = TTM_SETTOOLINFOA
TTM_HITTEST = TTM_HITTESTA
TTM_GETTEXT = TTM_GETTEXTA
TTM_UPDATETIPTEXT = TTM_UPDATETIPTEXTA
TTM_ENUMTOOLS = TTM_ENUMTOOLSA
TTM_GETCURRENTTOOL = TTM_GETCURRENTTOOLA
TTN_GETDISPINFOA = TTN_FIRST - 0
TTN_GETDISPINFOW = TTN_FIRST - 10
TTN_SHOW = TTN_FIRST - 1
TTN_POP = TTN_FIRST - 2
TTN_GETDISPINFO = TTN_GETDISPINFOA
TTN_NEEDTEXT = TTN_GETDISPINFO
TTN_NEEDTEXTA = TTN_GETDISPINFOA
TTN_NEEDTEXTW = TTN_GETDISPINFOW
SBARS_SIZEGRIP = 256
SBARS_TOOLTIPS = 2048
STATUSCLASSNAMEA = "msctls_statusbar32"
STATUSCLASSNAME = STATUSCLASSNAMEA
SB_SETTEXTA = WM_USER + 1
SB_SETTEXTW = WM_USER + 11
SB_GETTEXTA = WM_USER + 2
SB_GETTEXTW = WM_USER + 13
SB_GETTEXTLENGTHA = WM_USER + 3
SB_GETTEXTLENGTHW = WM_USER + 12
SB_GETTEXT = SB_GETTEXTA
SB_SETTEXT = SB_SETTEXTA
SB_GETTEXTLENGTH = SB_GETTEXTLENGTHA
SB_SETPARTS = WM_USER + 4
SB_GETPARTS = WM_USER + 6
SB_GETBORDERS = WM_USER + 7
SB_SETMINHEIGHT = WM_USER + 8
SB_SIMPLE = WM_USER + 9
SB_GETRECT = WM_USER + 10
SB_ISSIMPLE = WM_USER + 14
SB_SETICON = WM_USER + 15
SB_SETTIPTEXTA = WM_USER + 16
SB_SETTIPTEXTW = WM_USER + 17
SB_GETTIPTEXTA = WM_USER + 18
SB_GETTIPTEXTW = WM_USER + 19
SB_GETICON = WM_USER + 20
SB_SETTIPTEXT = SB_SETTIPTEXTA
SB_GETTIPTEXT = SB_GETTIPTEXTA
SB_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
SB_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
SBT_OWNERDRAW = 4096
SBT_NOBORDERS = 256
SBT_POPOUT = 512
SBT_RTLREADING = 1024
SBT_NOTABPARSING = 2048
SBT_TOOLTIPS = 2048
SB_SETBKCOLOR = CCM_SETBKCOLOR  # lParam = bkColor
SBN_SIMPLEMODECHANGE = SBN_FIRST - 0
TRACKBAR_CLASSA = "msctls_trackbar32"
TRACKBAR_CLASS = TRACKBAR_CLASSA
TBS_AUTOTICKS = 1
TBS_VERT = 2
TBS_HORZ = 0
TBS_TOP = 4
TBS_BOTTOM = 0
TBS_LEFT = 4
TBS_RIGHT = 0
TBS_BOTH = 8
TBS_NOTICKS = 16
TBS_ENABLESELRANGE = 32
TBS_FIXEDLENGTH = 64
TBS_NOTHUMB = 128
TBS_TOOLTIPS = 256
TBM_GETPOS = WM_USER
TBM_GETRANGEMIN = WM_USER + 1
TBM_GETRANGEMAX = WM_USER + 2
TBM_GETTIC = WM_USER + 3
TBM_SETTIC = WM_USER + 4
TBM_SETPOS = WM_USER + 5
TBM_SETRANGE = WM_USER + 6
TBM_SETRANGEMIN = WM_USER + 7
TBM_SETRANGEMAX = WM_USER + 8
TBM_CLEARTICS = WM_USER + 9
TBM_SETSEL = WM_USER + 10
TBM_SETSELSTART = WM_USER + 11
TBM_SETSELEND = WM_USER + 12
TBM_GETPTICS = WM_USER + 14
TBM_GETTICPOS = WM_USER + 15
TBM_GETNUMTICS = WM_USER + 16
TBM_GETSELSTART = WM_USER + 17
TBM_GETSELEND = WM_USER + 18
TBM_CLEARSEL = WM_USER + 19
TBM_SETTICFREQ = WM_USER + 20
TBM_SETPAGESIZE = WM_USER + 21
TBM_GETPAGESIZE = WM_USER + 22
TBM_SETLINESIZE = WM_USER + 23
TBM_GETLINESIZE = WM_USER + 24
TBM_GETTHUMBRECT = WM_USER + 25
TBM_GETCHANNELRECT = WM_USER + 26
TBM_SETTHUMBLENGTH = WM_USER + 27
TBM_GETTHUMBLENGTH = WM_USER + 28
TBM_SETTOOLTIPS = WM_USER + 29
TBM_GETTOOLTIPS = WM_USER + 30
TBM_SETTIPSIDE = WM_USER + 31
TBTS_TOP = 0
TBTS_LEFT = 1
TBTS_BOTTOM = 2
TBTS_RIGHT = 3
TBM_SETBUDDY = WM_USER + 32  # wparam = BOOL fLeft; (or right)
TBM_GETBUDDY = WM_USER + 33  # wparam = BOOL fLeft; (or right)
TBM_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
TBM_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
TB_LINEUP = 0
TB_LINEDOWN = 1
TB_PAGEUP = 2
TB_PAGEDOWN = 3
TB_THUMBPOSITION = 4
TB_THUMBTRACK = 5
TB_TOP = 6
TB_BOTTOM = 7
TB_ENDTRACK = 8
TBCD_TICS = 1
TBCD_THUMB = 2
TBCD_CHANNEL = 3
DL_BEGINDRAG = WM_USER + 133
DL_DRAGGING = WM_USER + 134
DL_DROPPED = WM_USER + 135
DL_CANCELDRAG = WM_USER + 136
DL_CURSORSET = 0
DL_STOPCURSOR = 1
DL_COPYCURSOR = 2
DL_MOVECURSOR = 3
DRAGLISTMSGSTRING = "commctrl_DragListMsg"
UPDOWN_CLASSA = "msctls_updown32"
UPDOWN_CLASS = UPDOWN_CLASSA
UD_MAXVAL = 32767
UD_MINVAL = -UD_MAXVAL
UDS_WRAP = 1
UDS_SETBUDDYINT = 2
UDS_ALIGNRIGHT = 4
UDS_ALIGNLEFT = 8
UDS_AUTOBUDDY = 16
UDS_ARROWKEYS = 32
UDS_HORZ = 64
UDS_NOTHOUSANDS = 128
UDS_HOTTRACK = 256
UDM_SETRANGE = WM_USER + 101
UDM_GETRANGE = WM_USER + 102
UDM_SETPOS = WM_USER + 103
UDM_GETPOS = WM_USER + 104
UDM_SETBUDDY = WM_USER + 105
UDM_GETBUDDY = WM_USER + 106
UDM_SETACCEL = WM_USER + 107
UDM_GETACCEL = WM_USER + 108
UDM_SETBASE = WM_USER + 109
UDM_GETBASE = WM_USER + 110
UDM_SETRANGE32 = WM_USER + 111
UDM_GETRANGE32 = WM_USER + 112  # wParam & lParam are LPINT
UDM_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
UDM_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
UDN_DELTAPOS = UDN_FIRST - 1
PROGRESS_CLASSA = "msctls_progress32"
PROGRESS_CLASS = PROGRESS_CLASSA
PBS_SMOOTH = 1
PBS_VERTICAL = 4
PBM_SETRANGE = WM_USER + 1
PBM_SETPOS = WM_USER + 2
PBM_DELTAPOS = WM_USER + 3
PBM_SETSTEP = WM_USER + 4
PBM_STEPIT = WM_USER + 5
PBM_SETRANGE32 = WM_USER + 6  # lParam = high, wParam = low
PBM_GETRANGE = (
    WM_USER + 7
)  # wParam = return (TRUE ? low : high). lParam = PPBRANGE or NULL
PBM_GETPOS = WM_USER + 8
PBM_SETBARCOLOR = WM_USER + 9  # lParam = bar color
PBM_SETBKCOLOR = CCM_SETBKCOLOR  # lParam = bkColor
HOTKEYF_SHIFT = 1
HOTKEYF_CONTROL = 2
HOTKEYF_ALT = 4
HOTKEYF_EXT = 8
HKCOMB_NONE = 1
HKCOMB_S = 2
HKCOMB_C = 4
HKCOMB_A = 8
HKCOMB_SC = 16
HKCOMB_SA = 32
HKCOMB_CA = 64
HKCOMB_SCA = 128
HKM_SETHOTKEY = WM_USER + 1
HKM_GETHOTKEY = WM_USER + 2
HKM_SETRULES = WM_USER + 3
HOTKEY_CLASSA = "msctls_hotkey32"
HOTKEY_CLASS = HOTKEY_CLASSA
CCS_TOP = 0x00000001
CCS_NOMOVEY = 0x00000002
CCS_BOTTOM = 0x00000003
CCS_NORESIZE = 0x00000004
CCS_NOPARENTALIGN = 0x00000008
CCS_ADJUSTABLE = 0x00000020
CCS_NODIVIDER = 0x00000040
CCS_VERT = 0x00000080
CCS_LEFT = CCS_VERT | CCS_TOP
CCS_RIGHT = CCS_VERT | CCS_BOTTOM
CCS_NOMOVEX = CCS_VERT | CCS_NOMOVEY
WC_LISTVIEWA = "SysListView32"
WC_LISTVIEW = WC_LISTVIEWA
LVS_ICON = 0
LVS_REPORT = 1
LVS_SMALLICON = 2
LVS_LIST = 3
LVS_TYPEMASK = 3
LVS_SINGLESEL = 4
LVS_SHOWSELALWAYS = 8
LVS_SORTASCENDING = 16
LVS_SORTDESCENDING = 32
LVS_SHAREIMAGELISTS = 64
LVS_NOLABELWRAP = 128
LVS_AUTOARRANGE = 256
LVS_EDITLABELS = 512
LVS_OWNERDATA = 4096
LVS_NOSCROLL = 8192
LVS_TYPESTYLEMASK = 64512
LVS_ALIGNTOP = 0
LVS_ALIGNLEFT = 2048
LVS_ALIGNMASK = 3072
LVS_OWNERDRAWFIXED = 1024
LVS_NOCOLUMNHEADER = 16384
LVS_NOSORTHEADER = 32768
LVM_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
LVM_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
LVM_GETBKCOLOR = LVM_FIRST + 0
LVM_SETBKCOLOR = LVM_FIRST + 1
LVM_GETIMAGELIST = LVM_FIRST + 2
LVSIL_NORMAL = 0
LVSIL_SMALL = 1
LVSIL_STATE = 2
LVM_SETIMAGELIST = LVM_FIRST + 3
LVM_GETITEMCOUNT = LVM_FIRST + 4
LVIF_TEXT = 1
LVIF_IMAGE = 2
LVIF_PARAM = 4
LVIF_STATE = 8
LVIF_INDENT = 16
LVIF_NORECOMPUTE = 2048
LVIS_FOCUSED = 1
LVIS_SELECTED = 2
LVIS_CUT = 4
LVIS_DROPHILITED = 8
LVIS_ACTIVATING = 32
LVIS_OVERLAYMASK = 3840
LVIS_STATEIMAGEMASK = 61440
I_INDENTCALLBACK = -1
LPSTR_TEXTCALLBACKA = -1
LPSTR_TEXTCALLBACK = LPSTR_TEXTCALLBACKA
I_IMAGECALLBACK = -1
LVM_GETITEMA = LVM_FIRST + 5
LVM_GETITEMW = LVM_FIRST + 75
LVM_GETITEM = LVM_GETITEMA
LVM_SETITEMA = LVM_FIRST + 6
LVM_SETITEMW = LVM_FIRST + 76
LVM_SETITEM = LVM_SETITEMA
LVM_INSERTITEMA = LVM_FIRST + 7
LVM_INSERTITEMW = LVM_FIRST + 77
LVM_INSERTITEM = LVM_INSERTITEMA
LVM_DELETEITEM = LVM_FIRST + 8
LVM_DELETEALLITEMS = LVM_FIRST + 9
LVM_GETCALLBACKMASK = LVM_FIRST + 10
LVM_SETCALLBACKMASK = LVM_FIRST + 11
LVNI_ALL = 0
LVNI_FOCUSED = 1
LVNI_SELECTED = 2
LVNI_CUT = 4
LVNI_DROPHILITED = 8
LVNI_ABOVE = 256
LVNI_BELOW = 512
LVNI_TOLEFT = 1024
LVNI_TORIGHT = 2048
LVM_GETNEXTITEM = LVM_FIRST + 12
LVFI_PARAM = 1
LVFI_STRING = 2
LVFI_PARTIAL = 8
LVFI_WRAP = 32
LVFI_NEARESTXY = 64
LVM_FINDITEMA = LVM_FIRST + 13
LVM_FINDITEMW = LVM_FIRST + 83
LVM_FINDITEM = LVM_FINDITEMA
LVIR_BOUNDS = 0
LVIR_ICON = 1
LVIR_LABEL = 2
LVIR_SELECTBOUNDS = 3
LVM_GETITEMRECT = LVM_FIRST + 14
LVM_SETITEMPOSITION = LVM_FIRST + 15
LVM_GETITEMPOSITION = LVM_FIRST + 16
LVM_GETSTRINGWIDTHA = LVM_FIRST + 17
LVM_GETSTRINGWIDTHW = LVM_FIRST + 87
LVM_GETSTRINGWIDTH = LVM_GETSTRINGWIDTHA
LVHT_NOWHERE = 1
LVHT_ONITEMICON = 2
LVHT_ONITEMLABEL = 4
LVHT_ONITEMSTATEICON = 8
LVHT_ONITEM = LVHT_ONITEMICON | LVHT_ONITEMLABEL | LVHT_ONITEMSTATEICON
LVHT_ABOVE = 8
LVHT_BELOW = 16
LVHT_TORIGHT = 32
LVHT_TOLEFT = 64
LVM_HITTEST = LVM_FIRST + 18
LVM_ENSUREVISIBLE = LVM_FIRST + 19
LVM_SCROLL = LVM_FIRST + 20
LVM_REDRAWITEMS = LVM_FIRST + 21
LVA_DEFAULT = 0
LVA_ALIGNLEFT = 1
LVA_ALIGNTOP = 2
LVA_SNAPTOGRID = 5
LVM_ARRANGE = LVM_FIRST + 22
LVM_EDITLABELA = LVM_FIRST + 23
LVM_EDITLABELW = LVM_FIRST + 118
LVM_EDITLABEL = LVM_EDITLABELA
LVM_GETEDITCONTROL = LVM_FIRST + 24
LVCF_FMT = 1
LVCF_WIDTH = 2
LVCF_TEXT = 4
LVCF_SUBITEM = 8
LVCF_IMAGE = 16
LVCF_ORDER = 32
LVCFMT_LEFT = 0
LVCFMT_RIGHT = 1
LVCFMT_CENTER = 2
LVCFMT_JUSTIFYMASK = 3
LVCFMT_IMAGE = 2048
LVCFMT_BITMAP_ON_RIGHT = 4096
LVCFMT_COL_HAS_IMAGES = 32768
LVM_GETCOLUMNA = LVM_FIRST + 25
LVM_GETCOLUMNW = LVM_FIRST + 95
LVM_GETCOLUMN = LVM_GETCOLUMNA
LVM_SETCOLUMNA = LVM_FIRST + 26
LVM_SETCOLUMNW = LVM_FIRST + 96
LVM_SETCOLUMN = LVM_SETCOLUMNA
LVM_INSERTCOLUMNA = LVM_FIRST + 27
LVM_INSERTCOLUMNW = LVM_FIRST + 97
LVM_INSERTCOLUMN = LVM_INSERTCOLUMNA
LVM_DELETECOLUMN = LVM_FIRST + 28
LVM_GETCOLUMNWIDTH = LVM_FIRST + 29
LVSCW_AUTOSIZE = -1
LVSCW_AUTOSIZE_USEHEADER = -2
LVM_SETCOLUMNWIDTH = LVM_FIRST + 30
LVM_GETHEADER = LVM_FIRST + 31
LVM_CREATEDRAGIMAGE = LVM_FIRST + 33
LVM_GETVIEWRECT = LVM_FIRST + 34
LVM_GETTEXTCOLOR = LVM_FIRST + 35
LVM_SETTEXTCOLOR = LVM_FIRST + 36
LVM_GETTEXTBKCOLOR = LVM_FIRST + 37
LVM_SETTEXTBKCOLOR = LVM_FIRST + 38
LVM_GETTOPINDEX = LVM_FIRST + 39
LVM_GETCOUNTPERPAGE = LVM_FIRST + 40
LVM_GETORIGIN = LVM_FIRST + 41
LVM_UPDATE = LVM_FIRST + 42
LVM_SETITEMSTATE = LVM_FIRST + 43
LVM_GETITEMSTATE = LVM_FIRST + 44
LVM_GETITEMTEXTA = LVM_FIRST + 45
LVM_GETITEMTEXTW = LVM_FIRST + 115
LVM_GETITEMTEXT = LVM_GETITEMTEXTA
LVM_SETITEMTEXTA = LVM_FIRST + 46
LVM_SETITEMTEXTW = LVM_FIRST + 116
LVM_SETITEMTEXT = LVM_SETITEMTEXTA
LVSICF_NOINVALIDATEALL = 1
LVSICF_NOSCROLL = 2
LVM_SETITEMCOUNT = LVM_FIRST + 47
LVM_SORTITEMS = LVM_FIRST + 48
LVM_SETITEMPOSITION32 = LVM_FIRST + 49
LVM_GETSELECTEDCOUNT = LVM_FIRST + 50
LVM_GETITEMSPACING = LVM_FIRST + 51
LVM_GETISEARCHSTRINGA = LVM_FIRST + 52
LVM_GETISEARCHSTRINGW = LVM_FIRST + 117
LVM_GETISEARCHSTRING = LVM_GETISEARCHSTRINGA
LVM_SETICONSPACING = LVM_FIRST + 53
LVM_SETEXTENDEDLISTVIEWSTYLE = LVM_FIRST + 54  # optional wParam == mask
LVM_GETEXTENDEDLISTVIEWSTYLE = LVM_FIRST + 55
LVS_EX_GRIDLINES = 1
LVS_EX_SUBITEMIMAGES = 2
LVS_EX_CHECKBOXES = 4
LVS_EX_TRACKSELECT = 8
LVS_EX_HEADERDRAGDROP = 16
LVS_EX_FULLROWSELECT = 32  # applies to report mode only
LVS_EX_ONECLICKACTIVATE = 64
LVS_EX_TWOCLICKACTIVATE = 128
LVS_EX_FLATSB = 256
LVS_EX_REGIONAL = 512
LVS_EX_INFOTIP = 1024  # listview does InfoTips for you
LVS_EX_UNDERLINEHOT = 2048
LVS_EX_UNDERLINECOLD = 4096
LVS_EX_MULTIWORKAREAS = 8192
LVM_GETSUBITEMRECT = LVM_FIRST + 56
LVM_SUBITEMHITTEST = LVM_FIRST + 57
LVM_SETCOLUMNORDERARRAY = LVM_FIRST + 58
LVM_GETCOLUMNORDERARRAY = LVM_FIRST + 59
LVM_SETHOTITEM = LVM_FIRST + 60
LVM_GETHOTITEM = LVM_FIRST + 61
LVM_SETHOTCURSOR = LVM_FIRST + 62
LVM_GETHOTCURSOR = LVM_FIRST + 63
LVM_APPROXIMATEVIEWRECT = LVM_FIRST + 64
LV_MAX_WORKAREAS = 16
LVM_SETWORKAREAS = LVM_FIRST + 65
LVM_GETWORKAREAS = LVM_FIRST + 70
LVM_GETNUMBEROFWORKAREAS = LVM_FIRST + 73
LVM_GETSELECTIONMARK = LVM_FIRST + 66
LVM_SETSELECTIONMARK = LVM_FIRST + 67
LVM_SETHOVERTIME = LVM_FIRST + 71
LVM_GETHOVERTIME = LVM_FIRST + 72
LVM_SETTOOLTIPS = LVM_FIRST + 74
LVM_GETTOOLTIPS = LVM_FIRST + 78
LVBKIF_SOURCE_NONE = 0
LVBKIF_SOURCE_HBITMAP = 1
LVBKIF_SOURCE_URL = 2
LVBKIF_SOURCE_MASK = 3
LVBKIF_STYLE_NORMAL = 0
LVBKIF_STYLE_TILE = 16
LVBKIF_STYLE_MASK = 16
LVM_SETBKIMAGEA = LVM_FIRST + 68
LVM_SETBKIMAGEW = LVM_FIRST + 138
LVM_GETBKIMAGEA = LVM_FIRST + 69
LVM_GETBKIMAGEW = LVM_FIRST + 139
LVKF_ALT = 1
LVKF_CONTROL = 2
LVKF_SHIFT = 4
LVN_ITEMCHANGING = LVN_FIRST - 0
LVN_ITEMCHANGED = LVN_FIRST - 1
LVN_INSERTITEM = LVN_FIRST - 2
LVN_DELETEITEM = LVN_FIRST - 3
LVN_DELETEALLITEMS = LVN_FIRST - 4
LVN_BEGINLABELEDITA = LVN_FIRST - 5
LVN_BEGINLABELEDITW = LVN_FIRST - 75
LVN_ENDLABELEDITA = LVN_FIRST - 6
LVN_ENDLABELEDITW = LVN_FIRST - 76
LVN_COLUMNCLICK = LVN_FIRST - 8
LVN_BEGINDRAG = LVN_FIRST - 9
LVN_BEGINRDRAG = LVN_FIRST - 11
LVN_ODCACHEHINT = LVN_FIRST - 13
LVN_ODFINDITEMA = LVN_FIRST - 52
LVN_ODFINDITEMW = LVN_FIRST - 79
LVN_ITEMACTIVATE = LVN_FIRST - 14
LVN_ODSTATECHANGED = LVN_FIRST - 15
LVN_ODFINDITEM = LVN_ODFINDITEMA
LVN_HOTTRACK = LVN_FIRST - 21
LVN_GETDISPINFOA = LVN_FIRST - 50
LVN_GETDISPINFOW = LVN_FIRST - 77
LVN_SETDISPINFOA = LVN_FIRST - 51
LVN_SETDISPINFOW = LVN_FIRST - 78
LVN_BEGINLABELEDIT = LVN_BEGINLABELEDITA
LVN_ENDLABELEDIT = LVN_ENDLABELEDITA
LVN_GETDISPINFO = LVN_GETDISPINFOA
LVN_SETDISPINFO = LVN_SETDISPINFOA
LVIF_DI_SETITEM = 4096
LVN_KEYDOWN = LVN_FIRST - 55
LVN_MARQUEEBEGIN = LVN_FIRST - 56
LVGIT_UNFOLDED = 1
LVN_GETINFOTIPA = LVN_FIRST - 57
LVN_GETINFOTIPW = LVN_FIRST - 58
LVN_GETINFOTIP = LVN_GETINFOTIPA
WC_TREEVIEWA = "SysTreeView32"
WC_TREEVIEW = WC_TREEVIEWA
TVS_HASBUTTONS = 1
TVS_HASLINES = 2
TVS_LINESATROOT = 4
TVS_EDITLABELS = 8
TVS_DISABLEDRAGDROP = 16
TVS_SHOWSELALWAYS = 32
TVS_RTLREADING = 64
TVS_NOTOOLTIPS = 128
TVS_CHECKBOXES = 256
TVS_TRACKSELECT = 512
TVS_SINGLEEXPAND = 1024
TVS_INFOTIP = 2048
TVS_FULLROWSELECT = 4096
TVS_NOSCROLL = 8192
TVS_NONEVENHEIGHT = 16384
TVIF_TEXT = 1
TVIF_IMAGE = 2
TVIF_PARAM = 4
TVIF_STATE = 8
TVIF_HANDLE = 16
TVIF_SELECTEDIMAGE = 32
TVIF_CHILDREN = 64
TVIF_INTEGRAL = 128
TVIS_SELECTED = 2
TVIS_CUT = 4
TVIS_DROPHILITED = 8
TVIS_BOLD = 16
TVIS_EXPANDED = 32
TVIS_EXPANDEDONCE = 64
TVIS_EXPANDPARTIAL = 128
TVIS_OVERLAYMASK = 3840
TVIS_STATEIMAGEMASK = 61440
TVIS_USERMASK = 61440
I_CHILDRENCALLBACK = -1
TVI_ROOT = -65536
TVI_FIRST = -65535
TVI_LAST = -65534
TVI_SORT = -65533
TVM_INSERTITEMA = TV_FIRST + 0
TVM_INSERTITEMW = TV_FIRST + 50
TVM_INSERTITEM = TVM_INSERTITEMA
TVM_DELETEITEM = TV_FIRST + 1
TVM_EXPAND = TV_FIRST + 2
TVE_COLLAPSE = 1
TVE_EXPAND = 2
TVE_TOGGLE = 3
TVE_EXPANDPARTIAL = 16384
TVE_COLLAPSERESET = 32768
TVM_GETITEMRECT = TV_FIRST + 4
TVM_GETCOUNT = TV_FIRST + 5
TVM_GETINDENT = TV_FIRST + 6
TVM_SETINDENT = TV_FIRST + 7
TVM_GETIMAGELIST = TV_FIRST + 8
TVSIL_NORMAL = 0
TVSIL_STATE = 2
TVM_SETIMAGELIST = TV_FIRST + 9
TVM_GETNEXTITEM = TV_FIRST + 10
TVGN_ROOT = 0
TVGN_NEXT = 1
TVGN_PREVIOUS = 2
TVGN_PARENT = 3
TVGN_CHILD = 4
TVGN_FIRSTVISIBLE = 5
TVGN_NEXTVISIBLE = 6
TVGN_PREVIOUSVISIBLE = 7
TVGN_DROPHILITE = 8
TVGN_CARET = 9
TVGN_LASTVISIBLE = 10
TVM_SELECTITEM = TV_FIRST + 11
TVM_GETITEMA = TV_FIRST + 12
TVM_GETITEMW = TV_FIRST + 62
TVM_GETITEM = TVM_GETITEMA
TVM_SETITEMA = TV_FIRST + 13
TVM_SETITEMW = TV_FIRST + 63
TVM_SETITEM = TVM_SETITEMA
TVM_EDITLABELA = TV_FIRST + 14
TVM_EDITLABELW = TV_FIRST + 65
TVM_EDITLABEL = TVM_EDITLABELA
TVM_GETEDITCONTROL = TV_FIRST + 15
TVM_GETVISIBLECOUNT = TV_FIRST + 16
TVM_HITTEST = TV_FIRST + 17
TVHT_NOWHERE = 1
TVHT_ONITEMICON = 2
TVHT_ONITEMLABEL = 4
TVHT_ONITEMINDENT = 8
TVHT_ONITEMBUTTON = 16
TVHT_ONITEMRIGHT = 32
TVHT_ONITEMSTATEICON = 64
TVHT_ABOVE = 256
TVHT_BELOW = 512
TVHT_TORIGHT = 1024
TVHT_TOLEFT = 2048
TVHT_ONITEM = TVHT_ONITEMICON | TVHT_ONITEMLABEL | TVHT_ONITEMSTATEICON
TVM_CREATEDRAGIMAGE = TV_FIRST + 18
TVM_SORTCHILDREN = TV_FIRST + 19
TVM_ENSUREVISIBLE = TV_FIRST + 20
TVM_SORTCHILDRENCB = TV_FIRST + 21
TVM_ENDEDITLABELNOW = TV_FIRST + 22
TVM_GETISEARCHSTRINGA = TV_FIRST + 23
TVM_GETISEARCHSTRINGW = TV_FIRST + 64
TVM_GETISEARCHSTRING = TVM_GETISEARCHSTRINGA
TVM_SETTOOLTIPS = TV_FIRST + 24
TVM_GETTOOLTIPS = TV_FIRST + 25
TVM_SETINSERTMARK = TV_FIRST + 26
TVM_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
TVM_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
TVM_SETITEMHEIGHT = TV_FIRST + 27
TVM_GETITEMHEIGHT = TV_FIRST + 28
TVM_SETBKCOLOR = TV_FIRST + 29
TVM_SETTEXTCOLOR = TV_FIRST + 30
TVM_GETBKCOLOR = TV_FIRST + 31
TVM_GETTEXTCOLOR = TV_FIRST + 32
TVM_SETSCROLLTIME = TV_FIRST + 33
TVM_GETSCROLLTIME = TV_FIRST + 34
TVM_SETINSERTMARKCOLOR = TV_FIRST + 37
TVM_GETINSERTMARKCOLOR = TV_FIRST + 38
TVN_SELCHANGINGA = TVN_FIRST - 1
TVN_SELCHANGINGW = TVN_FIRST - 50
TVN_SELCHANGEDA = TVN_FIRST - 2
TVN_SELCHANGEDW = TVN_FIRST - 51
TVC_UNKNOWN = 0
TVC_BYMOUSE = 1
TVC_BYKEYBOARD = 2
TVN_GETDISPINFOA = TVN_FIRST - 3
TVN_GETDISPINFOW = TVN_FIRST - 52
TVN_SETDISPINFOA = TVN_FIRST - 4
TVN_SETDISPINFOW = TVN_FIRST - 53
TVIF_DI_SETITEM = 4096
TVN_ITEMEXPANDINGA = TVN_FIRST - 5
TVN_ITEMEXPANDINGW = TVN_FIRST - 54
TVN_ITEMEXPANDEDA = TVN_FIRST - 6
TVN_ITEMEXPANDEDW = TVN_FIRST - 55
TVN_BEGINDRAGA = TVN_FIRST - 7
TVN_BEGINDRAGW = TVN_FIRST - 56
TVN_BEGINRDRAGA = TVN_FIRST - 8
TVN_BEGINRDRAGW = TVN_FIRST - 57
TVN_DELETEITEMA = TVN_FIRST - 9
TVN_DELETEITEMW = TVN_FIRST - 58
TVN_BEGINLABELEDITA = TVN_FIRST - 10
TVN_BEGINLABELEDITW = TVN_FIRST - 59
TVN_ENDLABELEDITA = TVN_FIRST - 11
TVN_ENDLABELEDITW = TVN_FIRST - 60
TVN_KEYDOWN = TVN_FIRST - 12
TVN_GETINFOTIPA = TVN_FIRST - 13
TVN_GETINFOTIPW = TVN_FIRST - 14
TVN_SINGLEEXPAND = TVN_FIRST - 15
TVN_SELCHANGING = TVN_SELCHANGINGA
TVN_SELCHANGED = TVN_SELCHANGEDA
TVN_GETDISPINFO = TVN_GETDISPINFOA
TVN_SETDISPINFO = TVN_SETDISPINFOA
TVN_ITEMEXPANDING = TVN_ITEMEXPANDINGA
TVN_ITEMEXPANDED = TVN_ITEMEXPANDEDA
TVN_BEGINDRAG = TVN_BEGINDRAGA
TVN_BEGINRDRAG = TVN_BEGINRDRAGA
TVN_DELETEITEM = TVN_DELETEITEMA
TVN_BEGINLABELEDIT = TVN_BEGINLABELEDITA
TVN_ENDLABELEDIT = TVN_ENDLABELEDITA
TVN_GETINFOTIP = TVN_GETINFOTIPA
TVCDRF_NOIMAGES = 65536
WC_COMBOBOXEXA = "ComboBoxEx32"
WC_COMBOBOXEX = WC_COMBOBOXEXA
CBEIF_TEXT = 1
CBEIF_IMAGE = 2
CBEIF_SELECTEDIMAGE = 4
CBEIF_OVERLAY = 8
CBEIF_INDENT = 16
CBEIF_LPARAM = 32
CBEIF_DI_SETITEM = 268435456
CBEM_INSERTITEMA = WM_USER + 1
CBEM_SETIMAGELIST = WM_USER + 2
CBEM_GETIMAGELIST = WM_USER + 3
CBEM_GETITEMA = WM_USER + 4
CBEM_SETITEMA = WM_USER + 5
# CBEM_DELETEITEM = CB_DELETESTRING
CBEM_GETCOMBOCONTROL = WM_USER + 6
CBEM_GETEDITCONTROL = WM_USER + 7
CBEM_SETEXSTYLE = WM_USER + 8  # use  SETEXTENDEDSTYLE instead
CBEM_SETEXTENDEDSTYLE = WM_USER + 14  # lparam == new style, wParam (optional) == mask
CBEM_GETEXSTYLE = WM_USER + 9  # use GETEXTENDEDSTYLE instead
CBEM_GETEXTENDEDSTYLE = WM_USER + 9
CBEM_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
CBEM_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
CBEM_HASEDITCHANGED = WM_USER + 10
CBEM_INSERTITEMW = WM_USER + 11
CBEM_SETITEMW = WM_USER + 12
CBEM_GETITEMW = WM_USER + 13
CBEM_INSERTITEM = CBEM_INSERTITEMA
CBEM_SETITEM = CBEM_SETITEMA
CBEM_GETITEM = CBEM_GETITEMA
CBES_EX_NOEDITIMAGE = 1
CBES_EX_NOEDITIMAGEINDENT = 2
CBES_EX_PATHWORDBREAKPROC = 4
CBES_EX_NOSIZELIMIT = 8
CBES_EX_CASESENSITIVE = 16
CBEN_GETDISPINFO = CBEN_FIRST - 0
CBEN_GETDISPINFOA = CBEN_FIRST - 0
CBEN_INSERTITEM = CBEN_FIRST - 1
CBEN_DELETEITEM = CBEN_FIRST - 2
CBEN_BEGINEDIT = CBEN_FIRST - 4
CBEN_ENDEDITA = CBEN_FIRST - 5
CBEN_ENDEDITW = CBEN_FIRST - 6
CBEN_GETDISPINFOW = CBEN_FIRST - 7
CBEN_DRAGBEGINA = CBEN_FIRST - 8
CBEN_DRAGBEGINW = CBEN_FIRST - 9
CBEN_DRAGBEGIN = CBEN_DRAGBEGINA
CBEN_ENDEDIT = CBEN_ENDEDITA
CBENF_KILLFOCUS = 1
CBENF_RETURN = 2
CBENF_ESCAPE = 3
CBENF_DROPDOWN = 4
CBEMAXSTRLEN = 260
WC_TABCONTROLA = "SysTabControl32"
WC_TABCONTROL = WC_TABCONTROLA
TCS_SCROLLOPPOSITE = 1  # assumes multiline tab
TCS_BOTTOM = 2
TCS_RIGHT = 2
TCS_MULTISELECT = 4  # allow multi-select in button mode
TCS_FLATBUTTONS = 8
TCS_FORCEICONLEFT = 16
TCS_FORCELABELLEFT = 32
TCS_HOTTRACK = 64
TCS_VERTICAL = 128
TCS_TABS = 0
TCS_BUTTONS = 256
TCS_SINGLELINE = 0
TCS_MULTILINE = 512
TCS_RIGHTJUSTIFY = 0
TCS_FIXEDWIDTH = 1024
TCS_RAGGEDRIGHT = 2048
TCS_FOCUSONBUTTONDOWN = 4096
TCS_OWNERDRAWFIXED = 8192
TCS_TOOLTIPS = 16384
TCS_FOCUSNEVER = 32768
TCS_EX_FLATSEPARATORS = 1
TCS_EX_REGISTERDROP = 2
TCM_GETIMAGELIST = TCM_FIRST + 2
TCM_SETIMAGELIST = TCM_FIRST + 3
TCM_GETITEMCOUNT = TCM_FIRST + 4
TCIF_TEXT = 1
TCIF_IMAGE = 2
TCIF_RTLREADING = 4
TCIF_PARAM = 8
TCIF_STATE = 16
TCIS_BUTTONPRESSED = 1
TCIS_HIGHLIGHTED = 2
TCM_GETITEMA = TCM_FIRST + 5
TCM_GETITEMW = TCM_FIRST + 60
TCM_GETITEM = TCM_GETITEMA
TCM_SETITEMA = TCM_FIRST + 6
TCM_SETITEMW = TCM_FIRST + 61
TCM_SETITEM = TCM_SETITEMA
TCM_INSERTITEMA = TCM_FIRST + 7
TCM_INSERTITEMW = TCM_FIRST + 62
TCM_INSERTITEM = TCM_INSERTITEMA
TCM_DELETEITEM = TCM_FIRST + 8
TCM_DELETEALLITEMS = TCM_FIRST + 9
TCM_GETITEMRECT = TCM_FIRST + 10
TCM_GETCURSEL = TCM_FIRST + 11
TCM_SETCURSEL = TCM_FIRST + 12
TCHT_NOWHERE = 1
TCHT_ONITEMICON = 2
TCHT_ONITEMLABEL = 4
TCHT_ONITEM = TCHT_ONITEMICON | TCHT_ONITEMLABEL
TCM_HITTEST = TCM_FIRST + 13
TCM_SETITEMEXTRA = TCM_FIRST + 14
TCM_ADJUSTRECT = TCM_FIRST + 40
TCM_SETITEMSIZE = TCM_FIRST + 41
TCM_REMOVEIMAGE = TCM_FIRST + 42
TCM_SETPADDING = TCM_FIRST + 43
TCM_GETROWCOUNT = TCM_FIRST + 44
TCM_GETTOOLTIPS = TCM_FIRST + 45
TCM_SETTOOLTIPS = TCM_FIRST + 46
TCM_GETCURFOCUS = TCM_FIRST + 47
TCM_SETCURFOCUS = TCM_FIRST + 48
TCM_SETMINTABWIDTH = TCM_FIRST + 49
TCM_DESELECTALL = TCM_FIRST + 50
TCM_HIGHLIGHTITEM = TCM_FIRST + 51
TCM_SETEXTENDEDSTYLE = TCM_FIRST + 52  # optional wParam == mask
TCM_GETEXTENDEDSTYLE = TCM_FIRST + 53
TCM_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
TCM_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
TCN_KEYDOWN = TCN_FIRST - 0
ANIMATE_CLASSA = "SysAnimate32"
ANIMATE_CLASS = ANIMATE_CLASSA
ACS_CENTER = 1
ACS_TRANSPARENT = 2
ACS_AUTOPLAY = 4
ACS_TIMER = 8  # don't use threads... use timers
ACM_OPENA = WM_USER + 100
ACM_OPENW = WM_USER + 103
ACM_OPEN = ACM_OPENA
ACM_PLAY = WM_USER + 101
ACM_STOP = WM_USER + 102
ACN_START = 1
ACN_STOP = 2
MONTHCAL_CLASSA = "SysMonthCal32"
MONTHCAL_CLASS = MONTHCAL_CLASSA
MCM_FIRST = 4096
MCM_GETCURSEL = MCM_FIRST + 1
MCM_SETCURSEL = MCM_FIRST + 2
MCM_GETMAXSELCOUNT = MCM_FIRST + 3
MCM_SETMAXSELCOUNT = MCM_FIRST + 4
MCM_GETSELRANGE = MCM_FIRST + 5
MCM_SETSELRANGE = MCM_FIRST + 6
MCM_GETMONTHRANGE = MCM_FIRST + 7
MCM_SETDAYSTATE = MCM_FIRST + 8
MCM_GETMINREQRECT = MCM_FIRST + 9
MCM_SETCOLOR = MCM_FIRST + 10
MCM_GETCOLOR = MCM_FIRST + 11
MCSC_BACKGROUND = 0  # the background color (between months)
MCSC_TEXT = 1  # the dates
MCSC_TITLEBK = 2  # background of the title
MCSC_TITLETEXT = 3
MCSC_MONTHBK = 4  # background within the month cal
MCSC_TRAILINGTEXT = 5  # the text color of header & trailing days
MCM_SETTODAY = MCM_FIRST + 12
MCM_GETTODAY = MCM_FIRST + 13
MCM_HITTEST = MCM_FIRST + 14
MCHT_TITLE = 65536
MCHT_CALENDAR = 131072
MCHT_TODAYLINK = 196608
MCHT_NEXT = 16777216  # these indicate that hitting
MCHT_PREV = 33554432  # here will go to the next/prev month
MCHT_NOWHERE = 0
MCHT_TITLEBK = MCHT_TITLE
MCHT_TITLEMONTH = MCHT_TITLE | 1
MCHT_TITLEYEAR = MCHT_TITLE | 2
MCHT_TITLEBTNNEXT = MCHT_TITLE | MCHT_NEXT | 3
MCHT_TITLEBTNPREV = MCHT_TITLE | MCHT_PREV | 3
MCHT_CALENDARBK = MCHT_CALENDAR
MCHT_CALENDARDATE = MCHT_CALENDAR | 1
MCHT_CALENDARDATENEXT = MCHT_CALENDARDATE | MCHT_NEXT
MCHT_CALENDARDATEPREV = MCHT_CALENDARDATE | MCHT_PREV
MCHT_CALENDARDAY = MCHT_CALENDAR | 2
MCHT_CALENDARWEEKNUM = MCHT_CALENDAR | 3
MCM_SETFIRSTDAYOFWEEK = MCM_FIRST + 15
MCM_GETFIRSTDAYOFWEEK = MCM_FIRST + 16
MCM_GETRANGE = MCM_FIRST + 17
MCM_SETRANGE = MCM_FIRST + 18
MCM_GETMONTHDELTA = MCM_FIRST + 19
MCM_SETMONTHDELTA = MCM_FIRST + 20
MCM_GETMAXTODAYWIDTH = MCM_FIRST + 21
MCM_SETUNICODEFORMAT = CCM_SETUNICODEFORMAT
MCM_GETUNICODEFORMAT = CCM_GETUNICODEFORMAT
MCN_SELCHANGE = MCN_FIRST + 1
MCN_GETDAYSTATE = MCN_FIRST + 3
MCN_SELECT = MCN_FIRST + 4
MCS_DAYSTATE = 1
MCS_MULTISELECT = 2
MCS_WEEKNUMBERS = 4
MCS_NOTODAYCIRCLE = 8
MCS_NOTODAY = 8
GMR_VISIBLE = 0  # visible portion of display
GMR_DAYSTATE = 1  # above plus the grayed out parts of
DATETIMEPICK_CLASSA = "SysDateTimePick32"
DATETIMEPICK_CLASS = DATETIMEPICK_CLASSA
DTM_FIRST = 4096
DTM_GETSYSTEMTIME = DTM_FIRST + 1
DTM_SETSYSTEMTIME = DTM_FIRST + 2
DTM_GETRANGE = DTM_FIRST + 3
DTM_SETRANGE = DTM_FIRST + 4
DTM_SETFORMATA = DTM_FIRST + 5
DTM_SETFORMATW = DTM_FIRST + 50
DTM_SETFORMAT = DTM_SETFORMATA
DTM_SETMCCOLOR = DTM_FIRST + 6
DTM_GETMCCOLOR = DTM_FIRST + 7
DTM_GETMONTHCAL = DTM_FIRST + 8
DTM_SETMCFONT = DTM_FIRST + 9
DTM_GETMCFONT = DTM_FIRST + 10
DTS_UPDOWN = 1  # use UPDOWN instead of MONTHCAL
DTS_SHOWNONE = 2  # allow a NONE selection
DTS_SHORTDATEFORMAT = (
    0  # use the short date format (app must forward WM_WININICHANGE messages)
)
DTS_LONGDATEFORMAT = (
    4  # use the long date format (app must forward WM_WININICHANGE messages)
)
DTS_TIMEFORMAT = 9  # use the time format (app must forward WM_WININICHANGE messages)
DTS_APPCANPARSE = 16  # allow user entered strings (app MUST respond to DTN_USERSTRING)
DTS_RIGHTALIGN = 32  # right-align popup instead of left-align it
DTN_DATETIMECHANGE = DTN_FIRST + 1  # the systemtime has changed
DTN_USERSTRINGA = DTN_FIRST + 2  # the user has entered a string
DTN_USERSTRINGW = DTN_FIRST + 15
DTN_USERSTRING = DTN_USERSTRINGW
DTN_WMKEYDOWNA = DTN_FIRST + 3  # modify keydown on app format field (X)
DTN_WMKEYDOWNW = DTN_FIRST + 16
DTN_WMKEYDOWN = DTN_WMKEYDOWNA
DTN_FORMATA = DTN_FIRST + 4  # query display for app format field (X)
DTN_FORMATW = DTN_FIRST + 17
DTN_FORMAT = DTN_FORMATA
DTN_FORMATQUERYA = DTN_FIRST + 5  # query formatting info for app format field (X)
DTN_FORMATQUERYW = DTN_FIRST + 18
DTN_FORMATQUERY = DTN_FORMATQUERYA
DTN_DROPDOWN = DTN_FIRST + 6  # MonthCal has dropped down
DTN_CLOSEUP = DTN_FIRST + 7  # MonthCal is popping up
GDTR_MIN = 1
GDTR_MAX = 2
GDT_ERROR = -1
GDT_VALID = 0
GDT_NONE = 1
IPM_CLEARADDRESS = WM_USER + 100  # no parameters
IPM_SETADDRESS = WM_USER + 101  # lparam = TCP/IP address
IPM_GETADDRESS = (
    WM_USER + 102
)  # lresult = # of non black fields.  lparam = LPDWORD for TCP/IP address
IPM_SETRANGE = WM_USER + 103  # wparam = field, lparam = range
IPM_SETFOCUS = WM_USER + 104  # wparam = field
IPM_ISBLANK = WM_USER + 105  # no parameters
WC_IPADDRESSA = "SysIPAddress32"
WC_IPADDRESS = WC_IPADDRESSA
IPN_FIELDCHANGED = IPN_FIRST - 0
WC_PAGESCROLLERA = "SysPager"
WC_PAGESCROLLER = WC_PAGESCROLLERA
PGS_VERT = 0
PGS_HORZ = 1
PGS_AUTOSCROLL = 2
PGS_DRAGNDROP = 4
PGF_INVISIBLE = 0  # Scroll button is not visible
PGF_NORMAL = 1  # Scroll button is in normal state
PGF_GRAYED = 2  # Scroll button is in grayed state
PGF_DEPRESSED = 4  # Scroll button is in depressed state
PGF_HOT = 8  # Scroll button is in hot state
PGB_TOPORLEFT = 0
PGB_BOTTOMORRIGHT = 1
PGM_SETCHILD = PGM_FIRST + 1  # lParam == hwnd
PGM_RECALCSIZE = PGM_FIRST + 2
PGM_FORWARDMOUSE = PGM_FIRST + 3
PGM_SETBKCOLOR = PGM_FIRST + 4
PGM_GETBKCOLOR = PGM_FIRST + 5
PGM_SETBORDER = PGM_FIRST + 6
PGM_GETBORDER = PGM_FIRST + 7
PGM_SETPOS = PGM_FIRST + 8
PGM_GETPOS = PGM_FIRST + 9
PGM_SETBUTTONSIZE = PGM_FIRST + 10
PGM_GETBUTTONSIZE = PGM_FIRST + 11
PGM_GETBUTTONSTATE = PGM_FIRST + 12
PGM_GETDROPTARGET = CCM_GETDROPTARGET
PGN_SCROLL = PGN_FIRST - 1
PGF_SCROLLUP = 1
PGF_SCROLLDOWN = 2
PGF_SCROLLLEFT = 4
PGF_SCROLLRIGHT = 8
PGK_SHIFT = 1
PGK_CONTROL = 2
PGK_MENU = 4
PGN_CALCSIZE = PGN_FIRST - 2
PGF_CALCWIDTH = 1
PGF_CALCHEIGHT = 2
WC_NATIVEFONTCTLA = "NativeFontCtl"
WC_NATIVEFONTCTL = WC_NATIVEFONTCTLA
NFS_EDIT = 1
NFS_STATIC = 2
NFS_LISTCOMBO = 4
NFS_BUTTON = 8
NFS_ALL = 16
WM_MOUSEHOVER = 673
WM_MOUSELEAVE = 675
TME_HOVER = 1
TME_LEAVE = 2
TME_QUERY = 1073741824
TME_CANCEL = -2147483648
HOVER_DEFAULT = -1
WSB_PROP_CYVSCROLL = 0x00000001
WSB_PROP_CXHSCROLL = 0x00000002
WSB_PROP_CYHSCROLL = 0x00000004
WSB_PROP_CXVSCROLL = 0x00000008
WSB_PROP_CXHTHUMB = 0x00000010
WSB_PROP_CYVTHUMB = 0x00000020
WSB_PROP_VBKGCOLOR = 0x00000040
WSB_PROP_HBKGCOLOR = 0x00000080
WSB_PROP_VSTYLE = 0x00000100
WSB_PROP_HSTYLE = 0x00000200
WSB_PROP_WINSTYLE = 0x00000400
WSB_PROP_PALETTE = 0x00000800
WSB_PROP_MASK = 0x00000FFF
FSB_FLAT_MODE = 2
FSB_ENCARTA_MODE = 1
FSB_REGULAR_MODE = 0


def INDEXTOOVERLAYMASK(i):
    return i << 8


def INDEXTOSTATEIMAGEMASK(i):
    return i << 12
