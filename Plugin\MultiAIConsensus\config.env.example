# 多AI协商插件配置文件
# Multi-AI Consensus Plugin Configuration

# 配置继承说明：
# 1. 如果以下配置项留空，将自动继承主配置文件(config.env)中的对应设置
# 2. 插件配置优先级高于主配置
# 3. 如果两个配置文件都未设置，将使用默认值

# API配置 - 留空则继承主配置
# API地址，继承 API_URL
MULTI_AI_API_URL=

# API密钥，继承 YUANPLUS_API_KEY
MULTI_AI_API_KEY=

# 并发配置
# 同时请求的最大模型数量 (1-10)
MULTI_AI_MAX_CONCURRENT=3

# 请求超时时间，单位毫秒 (5000-120000)
MULTI_AI_TIMEOUT=30000

# 可用模型列表，用逗号分隔
MULTI_AI_AVAILABLE_MODELS=gpt-4o,gpt-4o-mini,claude-3-5-sonnet-20241022,gemini-2.5-flash-preview-05-20,deepseek-chat

# 默认使用的模型列表，用逗号分隔
MULTI_AI_DEFAULT_MODELS=gpt-4o-mini,claude-3-5-sonnet-20241022

# 是否启用AI总结功能
# Enable AI Summary Feature
MULTI_AI_ENABLE_SUMMARY=true

# 用于生成总结的模型
# Model for Generating Summary
MULTI_AI_SUMMARY_MODEL=gpt-4o-mini

# 高级配置
# Advanced Configuration

# 自定义API端点配置（支持多个不同的API提供商）
# Custom API Endpoints Configuration (Support multiple API providers)

# OpenAI兼容API
MULTI_AI_OPENAI_URL=https://api.openai.com
MULTI_AI_OPENAI_KEY=sk-your-openai-key

# Claude API
MULTI_AI_CLAUDE_URL=https://api.anthropic.com
MULTI_AI_CLAUDE_KEY=sk-ant-your-claude-key

# Google Gemini API
MULTI_AI_GEMINI_URL=https://generativelanguage.googleapis.com
MULTI_AI_GEMINI_KEY=your-gemini-key

# 自定义模型映射 - 将模型名映射到特定的API端点
# Custom Model Mapping - Map model names to specific API endpoints
# 格式: MODEL_NAME=API_TYPE:ENDPOINT_NAME
# Format: MODEL_NAME=API_TYPE:ENDPOINT_NAME
MULTI_AI_MODEL_MAPPING_gpt-4o=openai:main
MULTI_AI_MODEL_MAPPING_claude-3-5-sonnet-20241022=claude:main
MULTI_AI_MODEL_MAPPING_gemini-2.5-flash-preview-05-20=gemini:main

# 响应过滤配置
# Response Filtering Configuration
MULTI_AI_FILTER_EMPTY_RESPONSES=true
MULTI_AI_MIN_RESPONSE_LENGTH=10
MULTI_AI_MAX_RESPONSE_LENGTH=10000

# 缓存配置
# Cache Configuration
MULTI_AI_ENABLE_CACHE=false
MULTI_AI_CACHE_TTL=3600

# 日志配置
# Logging Configuration
MULTI_AI_LOG_LEVEL=info
MULTI_AI_LOG_RESPONSES=false
MULTI_AI_LOG_ERRORS=true

# 费用控制
# Cost Control
MULTI_AI_MAX_TOKENS_PER_MODEL=4000
MULTI_AI_MAX_TOTAL_TOKENS=20000
MULTI_AI_ENABLE_COST_TRACKING=true 
# Multi-AI Consensus Plugin Configuration

# 配置继承说明：
# 1. 如果以下配置项留空，将自动继承主配置文件(config.env)中的对应设置
# 2. 插件配置优先级高于主配置
# 3. 如果两个配置文件都未设置，将使用默认值

# API配置 - 留空则继承主配置
# API地址，继承 API_URL
MULTI_AI_API_URL=

# API密钥，继承 YUANPLUS_API_KEY
MULTI_AI_API_KEY=

# 并发配置
# 同时请求的最大模型数量 (1-10)
MULTI_AI_MAX_CONCURRENT=3

# 请求超时时间，单位毫秒 (5000-120000)
MULTI_AI_TIMEOUT=30000

# 可用模型列表，用逗号分隔
MULTI_AI_AVAILABLE_MODELS=gpt-4o,gpt-4o-mini,claude-3-5-sonnet-20241022,gemini-2.5-flash-preview-05-20,deepseek-chat

# 默认使用的模型列表，用逗号分隔
MULTI_AI_DEFAULT_MODELS=gpt-4o-mini,claude-3-5-sonnet-20241022

# 是否启用AI总结功能
# Enable AI Summary Feature
MULTI_AI_ENABLE_SUMMARY=true

# 用于生成总结的模型
# Model for Generating Summary
MULTI_AI_SUMMARY_MODEL=gpt-4o-mini

# 高级配置
# Advanced Configuration

# 自定义API端点配置（支持多个不同的API提供商）
# Custom API Endpoints Configuration (Support multiple API providers)

# OpenAI兼容API
MULTI_AI_OPENAI_URL=https://api.openai.com
MULTI_AI_OPENAI_KEY=sk-your-openai-key

# Claude API
MULTI_AI_CLAUDE_URL=https://api.anthropic.com
MULTI_AI_CLAUDE_KEY=sk-ant-your-claude-key

# Google Gemini API
MULTI_AI_GEMINI_URL=https://generativelanguage.googleapis.com
MULTI_AI_GEMINI_KEY=your-gemini-key

# 自定义模型映射 - 将模型名映射到特定的API端点
# Custom Model Mapping - Map model names to specific API endpoints
# 格式: MODEL_NAME=API_TYPE:ENDPOINT_NAME
# Format: MODEL_NAME=API_TYPE:ENDPOINT_NAME
MULTI_AI_MODEL_MAPPING_gpt-4o=openai:main
MULTI_AI_MODEL_MAPPING_claude-3-5-sonnet-20241022=claude:main
MULTI_AI_MODEL_MAPPING_gemini-2.5-flash-preview-05-20=gemini:main

# 响应过滤配置
# Response Filtering Configuration
MULTI_AI_FILTER_EMPTY_RESPONSES=true
MULTI_AI_MIN_RESPONSE_LENGTH=10
MULTI_AI_MAX_RESPONSE_LENGTH=10000

# 缓存配置
# Cache Configuration
MULTI_AI_ENABLE_CACHE=false
MULTI_AI_CACHE_TTL=3600

# 日志配置
# Logging Configuration
MULTI_AI_LOG_LEVEL=info
MULTI_AI_LOG_RESPONSES=false
MULTI_AI_LOG_ERRORS=true

# 费用控制
# Cost Control
MULTI_AI_MAX_TOKENS_PER_MODEL=4000
MULTI_AI_MAX_TOTAL_TOKENS=20000
MULTI_AI_ENABLE_COST_TRACKING=true 
 
 