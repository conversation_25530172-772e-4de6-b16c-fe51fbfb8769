/**
 * 世界环境系统
 * 管理时间、天气、地点、资源等环境要素
 */

const EventEmitter = require('events');

class WorldEnvironment extends EventEmitter {
    constructor(worldCore) {
        super();
        this.worldCore = worldCore;
        this.logger = worldCore.logger;
        
        // 时间系统
        this.timeSystem = {
            currentHour: 8, // 0-23
            currentDay: 1, // 1-30
            currentMonth: 1, // 1-12
            currentYear: 2024,
            season: 'spring', // spring, summer, autumn, winter
            timeOfDay: 'morning' // dawn, morning, noon, afternoon, evening, night
        };

        // 天气系统
        this.weatherSystem = {
            current: 'sunny', // sunny, cloudy, rainy, stormy, snowy
            temperature: 20, // 摄氏度
            humidity: 60, // 百分比
            windSpeed: 5, // km/h
            forecast: []
        };

        // 地点系统
        this.locations = new Map();
        this.initializeLocations();

        // 资源系统
        this.resources = {
            globalResources: {
                energy: 1000,
                food: 1000,
                materials: 1000,
                knowledge: 1000
            },
            resourceFlow: {
                energyGeneration: 10,
                foodProduction: 8,
                materialGathering: 5,
                knowledgeCreation: 3
            }
        };

        // 环境事件
        this.environmentEvents = [];
        
        // 更新间隔
        this.updateInterval = null;
        this.updateFrequency = 60000; // 1分钟更新一次
    }

    /**
     * 初始化环境系统
     */
    async init() {
        this.logger.info('🌍 初始化世界环境系统...');
        
        // 初始化天气预报
        this.generateWeatherForecast();
        
        // 设置季节
        this.updateSeason();
        
        this.logger.info('✅ 世界环境系统初始化完成');
    }

    /**
     * 初始化地点
     */
    initializeLocations() {
        const defaultLocations = [
            {
                id: 'home',
                name: '家',
                type: 'residential',
                capacity: 10,
                comfort: 90,
                privacy: 95,
                activities: ['sleep', 'eat', 'relax', 'study'],
                moodEffects: { happiness: 5, stress: -10 }
            },
            {
                id: 'school',
                name: '学校',
                type: 'educational',
                capacity: 100,
                comfort: 60,
                privacy: 30,
                activities: ['study', 'socialize', 'learn'],
                moodEffects: { knowledge: 10, stress: 5 }
            },
            {
                id: 'park',
                name: '公园',
                type: 'recreational',
                capacity: 50,
                comfort: 80,
                privacy: 40,
                activities: ['exercise', 'relax', 'socialize', 'play'],
                moodEffects: { happiness: 15, stress: -15, health: 10 }
            },
            {
                id: 'library',
                name: '图书馆',
                type: 'educational',
                capacity: 30,
                comfort: 70,
                privacy: 80,
                activities: ['study', 'read', 'research'],
                moodEffects: { knowledge: 20, stress: -5 }
            },
            {
                id: 'cafe',
                name: '咖啡厅',
                type: 'social',
                capacity: 25,
                comfort: 75,
                privacy: 50,
                activities: ['socialize', 'eat', 'work', 'relax'],
                moodEffects: { happiness: 8, social: 12 }
            },
            {
                id: 'gym',
                name: '健身房',
                type: 'recreational',
                capacity: 40,
                comfort: 60,
                privacy: 60,
                activities: ['exercise', 'train'],
                moodEffects: { health: 20, stress: -10, confidence: 5 }
            },
            {
                id: 'mall',
                name: '商场',
                type: 'commercial',
                capacity: 200,
                comfort: 70,
                privacy: 20,
                activities: ['shop', 'socialize', 'eat', 'entertainment'],
                moodEffects: { happiness: 10, social: 8 }
            },
            {
                id: 'workplace',
                name: '工作场所',
                type: 'professional',
                capacity: 50,
                comfort: 65,
                privacy: 70,
                activities: ['work', 'collaborate', 'meet'],
                moodEffects: { achievement: 10, stress: 15 }
            }
        ];

        defaultLocations.forEach(location => {
            this.locations.set(location.id, {
                ...location,
                currentOccupants: [],
                events: [],
                lastUpdated: new Date()
            });
        });
    }

    /**
     * 启动环境系统
     */
    async start() {
        if (this.updateInterval) {
            return;
        }

        this.updateInterval = setInterval(() => {
            this.updateEnvironment();
        }, this.updateFrequency);

        this.logger.info('🌍 世界环境系统已启动');
    }

    /**
     * 暂停环境系统
     */
    async pause() {
        // 环境系统可以继续运行，但不触发事件
        this.logger.info('⏸️ 世界环境系统已暂停');
    }

    /**
     * 恢复环境系统
     */
    async resume() {
        this.logger.info('▶️ 世界环境系统已恢复');
    }

    /**
     * 停止环境系统
     */
    async stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        this.logger.info('🛑 世界环境系统已停止');
    }

    /**
     * 更新环境
     */
    updateEnvironment() {
        const worldTime = this.worldCore.getCurrentWorldTime();
        
        // 更新时间
        this.updateTime(worldTime);
        
        // 更新天气
        this.updateWeather();
        
        // 更新资源
        this.updateResources();
        
        // 检查环境事件
        this.checkEnvironmentEvents();
        
        this.emit('environmentUpdated', {
            time: this.timeSystem,
            weather: this.weatherSystem,
            resources: this.resources
        });
    }

    /**
     * 更新时间系统
     */
    updateTime(worldTime) {
        const hour = worldTime.getHours();
        const day = worldTime.getDate();
        const month = worldTime.getMonth() + 1;
        const year = worldTime.getFullYear();

        // 检查是否有时间变化
        const timeChanged = 
            hour !== this.timeSystem.currentHour ||
            day !== this.timeSystem.currentDay ||
            month !== this.timeSystem.currentMonth ||
            year !== this.timeSystem.currentYear;

        if (timeChanged) {
            const oldTime = { ...this.timeSystem };
            
            this.timeSystem.currentHour = hour;
            this.timeSystem.currentDay = day;
            this.timeSystem.currentMonth = month;
            this.timeSystem.currentYear = year;
            
            // 更新时段
            this.timeSystem.timeOfDay = this.getTimeOfDay(hour);
            
            // 更新季节
            this.timeSystem.season = this.getSeason(month);
            
            this.emit('timeChanged', {
                old: oldTime,
                new: this.timeSystem
            });
        }
    }

    /**
     * 获取时段
     */
    getTimeOfDay(hour) {
        if (hour >= 5 && hour < 8) return 'dawn';
        if (hour >= 8 && hour < 12) return 'morning';
        if (hour >= 12 && hour < 14) return 'noon';
        if (hour >= 14 && hour < 18) return 'afternoon';
        if (hour >= 18 && hour < 22) return 'evening';
        return 'night';
    }

    /**
     * 获取季节
     */
    getSeason(month) {
        if (month >= 3 && month <= 5) return 'spring';
        if (month >= 6 && month <= 8) return 'summer';
        if (month >= 9 && month <= 11) return 'autumn';
        return 'winter';
    }

    /**
     * 更新天气
     */
    updateWeather() {
        // 简单的天气变化逻辑
        const weatherTypes = ['sunny', 'cloudy', 'rainy', 'stormy'];
        const changeChance = 0.1; // 10%的概率改变天气

        if (Math.random() < changeChance) {
            const oldWeather = this.weatherSystem.current;
            this.weatherSystem.current = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
            
            // 根据天气调整温度
            this.adjustTemperatureByWeather();
            
            if (oldWeather !== this.weatherSystem.current) {
                this.emit('weatherChanged', {
                    old: oldWeather,
                    new: this.weatherSystem.current,
                    temperature: this.weatherSystem.temperature
                });
            }
        }
    }

    /**
     * 根据天气调整温度
     */
    adjustTemperatureByWeather() {
        const baseTemp = this.getSeasonalBaseTemperature();
        
        switch (this.weatherSystem.current) {
            case 'sunny':
                this.weatherSystem.temperature = baseTemp + Math.random() * 5;
                break;
            case 'cloudy':
                this.weatherSystem.temperature = baseTemp - Math.random() * 3;
                break;
            case 'rainy':
                this.weatherSystem.temperature = baseTemp - Math.random() * 8;
                break;
            case 'stormy':
                this.weatherSystem.temperature = baseTemp - Math.random() * 10;
                break;
        }
        
        this.weatherSystem.temperature = Math.round(this.weatherSystem.temperature);
    }

    /**
     * 获取季节基础温度
     */
    getSeasonalBaseTemperature() {
        switch (this.timeSystem.season) {
            case 'spring': return 18;
            case 'summer': return 28;
            case 'autumn': return 15;
            case 'winter': return 5;
            default: return 20;
        }
    }

    /**
     * 更新资源
     */
    updateResources() {
        const flow = this.resources.resourceFlow;
        const global = this.resources.globalResources;
        
        // 资源生成
        global.energy += flow.energyGeneration;
        global.food += flow.foodProduction;
        global.materials += flow.materialGathering;
        global.knowledge += flow.knowledgeCreation;
        
        // 资源上限
        const maxResource = 10000;
        Object.keys(global).forEach(key => {
            global[key] = Math.min(global[key], maxResource);
        });
    }

    /**
     * 检查环境事件
     */
    checkEnvironmentEvents() {
        // 这里可以添加各种环境事件的检查逻辑
        // 比如节日、自然灾害、特殊天气等
    }

    /**
     * 生成天气预报
     */
    generateWeatherForecast() {
        this.weatherSystem.forecast = [];
        const weatherTypes = ['sunny', 'cloudy', 'rainy'];
        
        for (let i = 0; i < 7; i++) {
            this.weatherSystem.forecast.push({
                day: i + 1,
                weather: weatherTypes[Math.floor(Math.random() * weatherTypes.length)],
                temperature: this.getSeasonalBaseTemperature() + (Math.random() - 0.5) * 10
            });
        }
    }

    /**
     * 更新季节
     */
    updateSeason() {
        this.timeSystem.season = this.getSeason(this.timeSystem.currentMonth);
    }

    /**
     * 获取地点信息
     */
    getLocation(locationId) {
        return this.locations.get(locationId);
    }

    /**
     * 获取所有地点
     */
    getAllLocations() {
        return Array.from(this.locations.values());
    }

    /**
     * Agent进入地点
     */
    enterLocation(agentId, locationId) {
        const location = this.locations.get(locationId);
        if (!location) {
            throw new Error(`地点不存在: ${locationId}`);
        }

        if (location.currentOccupants.length >= location.capacity) {
            throw new Error(`地点已满: ${locationId}`);
        }

        if (!location.currentOccupants.includes(agentId)) {
            location.currentOccupants.push(agentId);
            location.lastUpdated = new Date();
            
            this.emit('agentEnteredLocation', {
                agentId,
                locationId,
                location
            });
        }
    }

    /**
     * Agent离开地点
     */
    leaveLocation(agentId, locationId) {
        const location = this.locations.get(locationId);
        if (!location) {
            return;
        }

        const index = location.currentOccupants.indexOf(agentId);
        if (index !== -1) {
            location.currentOccupants.splice(index, 1);
            location.lastUpdated = new Date();
            
            this.emit('agentLeftLocation', {
                agentId,
                locationId,
                location
            });
        }
    }

    /**
     * 获取统计信息
     */
    async getStatistics() {
        return {
            time: this.timeSystem,
            weather: this.weatherSystem,
            resources: this.resources,
            locations: {
                total: this.locations.size,
                occupied: Array.from(this.locations.values()).filter(loc => loc.currentOccupants.length > 0).length,
                totalOccupants: Array.from(this.locations.values()).reduce((sum, loc) => sum + loc.currentOccupants.length, 0)
            }
        };
    }

    /**
     * 销毁环境系统
     */
    async destroy() {
        await this.stop();
        this.removeAllListeners();
        this.logger.info('🗑️ 世界环境系统已销毁');
    }
}

module.exports = { WorldEnvironment };
