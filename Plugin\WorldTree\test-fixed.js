/**
 * 世界树VCP插件修复后测试脚本
 * 测试修复后的数据库查询和system消息格式化功能
 */

const WorldTreeVCP = require('./WorldTreeVCP.js');

// 模拟日志记录器
const mockLogger = {
    info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
    error: (tag, ...args) => console.error(`[ERROR] [${tag}]`, ...args),
    warning: (tag, ...args) => console.warn(`[WARNING] [${tag}]`, ...args),
    debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args),
    success: (tag, ...args) => console.log(`[SUCCESS] [${tag}]`, ...args),
    system: (...args) => console.log(`[SYSTEM]`, ...args)
};

async function testFixedFeatures() {
    console.log('🔧 测试修复后的世界树VCP插件功能...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 初始化插件
        console.log('1. 初始化插件...');
        worldTreeVCP = new WorldTreeVCP();
        const initResult = await worldTreeVCP.initialize(mockLogger);
        
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        console.log('✅ 插件初始化成功\n');
        
        // 2. 测试创建示例配置
        console.log('2. 创建示例世界树配置...');
        const testConfig = {
            worldBackground: '在一个充满魔法的奇幻世界中，你是一位博学的图书管理员，掌管着古老的魔法图书馆。这里收藏着无数珍贵的魔法典籍和古老的智慧，每天都有学者、法师和冒险者前来寻求知识。',
            timeArchitecture: {
                morning: '图书馆刚刚开放，晨光透过彩色玻璃窗洒在古老的书架上，魔法粒子在空气中轻柔地舞动。这是整理新到典籍的最佳时间。',
                afternoon: '最繁忙的时段，许多学者和冒险者前来查阅资料。你需要运用丰富的知识帮助他们找到所需的信息。',
                evening: '准备闭馆的时间，你会仔细检查借阅记录，确保珍贵的典籍都得到妥善保管。夕阳西下，图书馆显得格外宁静。',
                night: '独自研究的时光，你可以专心阅读那些最古老、最神秘的魔法文献，探索深奥的魔法理论。'
            },
            characterSchedules: {
                '09:00-12:00': '整理新到的魔法典籍，分类归档',
                '14:00-17:00': '协助访客查找资料，解答学术问题',
                '19:00-22:00': '研究古代魔法理论，翻译古老文献'
            },
            narrativeRules: {
                '知识渊博': '对各种魔法知识、历史传说都有深入了解，能够提供准确的信息',
                '谨慎细致': '处理古老典籍时格外小心，重视知识的保护和传承',
                '乐于助人': '愿意帮助真诚的求知者，但会警惕那些心怀不轨的人',
                '学者风范': '说话温和有礼，逻辑清晰，喜欢引用典籍中的智慧'
            }
        };
        
        const configResult = await worldTreeVCP.createOrUpdateWorldTreeConfig('魔法图书管理员', testConfig);
        if (configResult) {
            console.log('✅ 示例配置创建成功\n');
        } else {
            throw new Error('配置创建失败');
        }
        
        // 3. 测试心理状态计算（修复后的版本）
        console.log('3. 测试修复后的心理状态计算...');
        const psychologyState = await worldTreeVCP.calculatePsychologyState('test_user', '魔法图书管理员', {
            taskType: 'creative',
            taskLoad: 0.3,
            restQuality: 0.8
        });
        console.log('心理状态计算结果:');
        console.log(`  压力: ${psychologyState.stress.toFixed(1)}`);
        console.log(`  情绪: ${psychologyState.emotion.toFixed(1)}`);
        console.log(`  能量: ${psychologyState.energy.toFixed(1)}`);
        console.log(`  专注: ${psychologyState.focus.toFixed(1)}`);
        console.log(`  综合评分: ${psychologyState.overallScore.toFixed(1)}`);
        console.log('✅ 心理状态计算成功\n');
        
        // 4. 测试心理活动生成
        console.log('4. 测试心理活动生成...');
        const psychologyActivity = await worldTreeVCP.generatePsychologyActivity('test_user', '魔法图书管理员', {
            hasRecentConversation: true,
            conversationLength: 150
        });
        
        if (psychologyActivity) {
            console.log('生成的心理活动内容:');
            console.log(`"${psychologyActivity.content}"`);
            console.log('✅ 心理活动生成成功\n');
        } else {
            throw new Error('心理活动生成失败');
        }
        
        // 5. 测试格式化的system消息生成
        console.log('5. 测试格式化的system消息生成...');
        const systemMessage = await worldTreeVCP.generateSystemMessage('test_user', '魔法图书管理员', '你好，我想了解一些关于古代魔法的知识。');
        
        console.log('生成的格式化system消息:');
        console.log('─'.repeat(80));
        console.log(systemMessage);
        console.log('─'.repeat(80));
        console.log('✅ 格式化system消息生成成功\n');
        
        // 6. 测试世界树专用心理状态数据存储
        console.log('6. 测试世界树专用心理状态数据存储...');
        const savedState = await worldTreeVCP.getWorldTreePsychologyState('test_user', '魔法图书管理员');
        
        if (savedState) {
            console.log('保存的心理状态数据:');
            console.log(`  最后更新: ${savedState.lastUpdated}`);
            console.log(`  内容摘要: ${savedState.contextSummary}`);
            console.log('✅ 心理状态数据存储成功\n');
        } else {
            console.log('⚠️ 暂无保存的心理状态数据\n');
        }
        
        // 7. 测试批量更新（修复后的版本）
        console.log('7. 测试修复后的批量更新...');
        await worldTreeVCP.updateAllPsychologyStates();
        console.log('✅ 批量更新执行成功\n');
        
        console.log('🎉 所有修复功能测试通过！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error.stack);
    } finally {
        // 清理
        if (worldTreeVCP) {
            try {
                await worldTreeVCP.cleanup();
                console.log('\n🧹 插件清理完成');
            } catch (cleanupError) {
                console.error('清理失败:', cleanupError.message);
            }
        }
    }
}

// 运行测试
if (require.main === module) {
    testFixedFeatures().then(() => {
        console.log('\n测试完成，退出程序。');
        process.exit(0);
    }).catch(error => {
        console.error('\n测试过程中发生未捕获的错误:', error);
        process.exit(1);
    });
}

module.exports = { testFixedFeatures };
