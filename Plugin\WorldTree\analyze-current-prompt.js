/**
 * 分析当前buildEnhancedPsychologyPrompt生成的内容
 */

const WorldTreeVCP = require('./WorldTreeVCP');

async function analyzeCurrentPrompt() {
    console.log('🔍 分析当前心理分析提示词生成...\n');
    
    const mockLogger = {
        info: (tag, ...args) => console.log(`[INFO] [${tag}]`, ...args),
        warning: (tag, ...args) => console.log(`[WARN] [${tag}]`, ...args),
        error: (tag, ...args) => console.log(`[ERROR] [${tag}]`, ...args),
        debug: (tag, ...args) => console.log(`[DEBUG] [${tag}]`, ...args)
    };
    
    try {
        // 1. 初始化WorldTreeVCP
        const worldTreeVCP = new WorldTreeVCP();
        await worldTreeVCP.initialize(mockLogger);
        
        // 2. 模拟真实的心理状态数据
        const mockPsychologyState = {
            focus: 73.6,
            energy: 5.0,
            fatigue: 46.8,
            alertness: 57.5,
            hunger: 8.5,
            stress: 80.0,  // 高压力状态
            mood: 35.0,    // 心情不佳
            timePeriod: 'afternoon'
        };
        
        // 3. 模拟世界树配置
        const mockWorldTreeConfig = {
            worldBackground: '你是雨安安，一位专注于AI研究的技术专家。你对大语言模型和智能体技术有深入的理解，喜欢探索人工智能的前沿技术。',
            timeArchitecture: {
                afternoon: '下午时分，思维活跃，适合进行复杂的技术讨论和问题解决'
            },
            characterSchedules: {
                enabled: true,
                schedules: [
                    { time: '14:00-17:00', activity: '代码重构和系统优化' }
                ]
            }
        };
        
        // 4. 模拟完整上下文
        const mockFullContext = {
            timestamp: '2025/7/20 14:30:45',
            timePeriod: 'afternoon',
            recentConversations: [
                {
                    speaker: '用户',
                    content: '为什么报错啊[2025/7/20 14:08:34] [!] [世界树VCP] 获取缓存心理活动失败: SQLITE_ERROR: no such column: psychology_state',
                    timestamp: '2025/7/20 14:08:34'
                },
                {
                    speaker: 'AI助手',
                    content: '我看到问题了！有两个错误：1. **数据库错误**：`no such column: psychology_state` - 数据库表结构缺少心理状态列 2. **代码错误**：`Cannot read properties of undefined (reading \'analyzeIntegratedPsychology\')` - 模块引用问题',
                    timestamp: '2025/7/20 14:09:15'
                }
            ],
            systemPrompt: '你是雨安安，一位AI研究专家，专注于大语言模型和智能体技术。你有深厚的技术功底，善于分析和解决复杂问题，对AI技术的发展有独特的见解。你的回答总是专业、准确，同时保持人性化的交流方式。',
            emotionalState: {
                emotion_valence: -0.2,  // 轻微负面
                emotion_arousal: 0.6,   // 中等激活
                emotion_dominance: 0.7, // 较强控制感
                current_affinity: 75,
                relationship_type: 'professional_friend'
            },
            memoryFragments: [
                {
                    content: '用户遇到了数据库错误，需要技术支持',
                    ai_summary: '技术问题解决场景，用户信任AI的专业能力',
                    memory_type: 'technical_support',
                    importance_score: 0.8,
                    emotional_context: '{"stress": 0.6, "focus": 0.9}'
                }
            ]
        };
        
        // 5. 生成当前的提示词
        console.log('📝 当前buildEnhancedPsychologyPrompt生成的内容：');
        console.log('=' .repeat(80));
        
        const currentPrompt = worldTreeVCP.buildEnhancedPsychologyPrompt(
            '雨安安',
            mockPsychologyState,
            mockWorldTreeConfig,
            mockFullContext
        );
        
        console.log(currentPrompt);
        console.log('=' .repeat(80));
        
        // 6. 分析当前API调用结构
        console.log('\n🌐 当前API调用的messages数组结构：');
        console.log('=' .repeat(50));
        
        const currentApiCall = {
            model: "gemini-2.5-flash-free",
            messages: [
                {
                    role: 'user',
                    content: currentPrompt
                }
            ]
        };
        
        console.log('```javascript');
        console.log(JSON.stringify(currentApiCall, null, 2));
        console.log('```');
        
        // 7. 分析问题
        console.log('\n❌ 当前结构的问题：');
        console.log('1. 所有信息都塞在一个巨大的字符串中');
        console.log('2. 只有一个user角色，没有利用system和assistant角色');
        console.log('3. 信息混杂，AI难以准确理解各部分的重要性');
        console.log('4. 缺少结构化的对话上下文');
        console.log('5. 提示词过长，可能超出token限制');
        
        console.log('\n✅ 理想的messages数组结构应该是：');
        console.log('```javascript');
        const idealStructure = {
            model: "gemini-2.5-flash-free",
            messages: [
                {
                    role: 'system',
                    content: '你是雨安安，AI研究专家...'
                },
                {
                    role: 'user', 
                    content: '当前状态：专注度73.6%，压力80%...'
                },
                {
                    role: 'assistant',
                    content: '之前的心理状态示例...'
                },
                {
                    role: 'user',
                    content: '请生成当前的内心独白'
                }
            ]
        };
        console.log(JSON.stringify(idealStructure, null, 2));
        console.log('```');
        
        console.log('\n📊 字符串长度分析：');
        console.log(`当前prompt长度: ${currentPrompt.length} 字符`);
        console.log(`预估token数: ${Math.ceil(currentPrompt.length / 4)} tokens`);
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
    }
}

// 运行分析
analyzeCurrentPrompt().catch(console.error);
