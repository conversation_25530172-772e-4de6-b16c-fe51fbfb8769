/**
 * VCP WebSocket客户端
 * 连接到现有的VCPToolBox WebSocket服务器
 * 实现与VCP系统的双向通信
 */

const WebSocket = require('ws');
const EventEmitter = require('events');

/**
 * 获取北京时间戳 (不带Z后缀)
 */
function getBeijingTimestamp() {
    const now = new Date();
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    // 移除Z后缀，避免前端误解析为UTC时间
    return beijingTime.toISOString().replace('Z', '+08:00');
}
const fs = require('fs');
const path = require('path');

class WebSocketClient extends EventEmitter {
    constructor(logger, config) {
        super();

        this.logger = logger;
        this.config = config;

        // 连接状态
        this.ws = null;
        this.isConnected = false;
        this.isConnecting = false;
        this.reconnectTimer = null;
        this.heartbeatTimer = null;

        // 读取主目录的环境配置
        const mainEnvConfig = this.loadMainEnvConfig();

        // 连接配置 - 优先使用主目录环境配置
        this.host = config.VCP_WEBSOCKET_HOST || 'localhost';
        this.port = mainEnvConfig.PORT || config.VCP_WEBSOCKET_PORT || 6005; // 默认使用主目录的PORT
        this.path = config.VCP_WEBSOCKET_PATH || '/onebot11';
        this.vcpKey = mainEnvConfig.VCP_Key || '123456'; // 读取VCP_Key用于认证
        this.reconnectInterval = config.RECONNECT_INTERVAL || 3000;
        this.heartbeatInterval = config.HEARTBEAT_INTERVAL || 30000;
        
        // 消息队列
        this.messageQueue = [];
        this.isProcessingQueue = false;
        
        // 统计信息
        this.stats = {
            connectTime: null,
            disconnectTime: null,
            messagesSent: 0,
            messagesReceived: 0,
            reconnectCount: 0,
            errors: 0
        };
    }

    /**
     * 读取主目录的环境配置文件
     */
    loadMainEnvConfig() {
        try {
            // 主目录的config.env文件路径
            const mainConfigPath = path.join(__dirname, '..', 'config.env');

            if (!fs.existsSync(mainConfigPath)) {
                this.logger.warning('WebSocketClient', '主目录config.env文件不存在，使用默认配置');
                return {};
            }

            const envContent = fs.readFileSync(mainConfigPath, 'utf-8');
            const envConfig = {};

            // 解析环境配置文件
            envContent.split('\n').forEach(line => {
                line = line.trim();
                if (line && !line.startsWith('#') && line.includes('=')) {
                    const [key, ...valueParts] = line.split('=');
                    let value = valueParts.join('=').trim();

                    // 移除引号
                    if ((value.startsWith('"') && value.endsWith('"')) ||
                        (value.startsWith("'") && value.endsWith("'"))) {
                        value = value.slice(1, -1);
                    }

                    envConfig[key.trim()] = value;
                }
            });

            this.logger.info('WebSocketClient', `已加载主目录环境配置: PORT=${envConfig.PORT}, VCP_Key=${envConfig.VCP_Key ? '***' : 'undefined'}`);
            return envConfig;

        } catch (error) {
            this.logger.error('WebSocketClient', `读取主目录环境配置失败: ${error.message}`);
            return {};
        }
    }

    /**
     * 连接到VCP WebSocket服务器
     */
    async connect() {
        if (this.isConnected || this.isConnecting) {
            this.logger.warning('WebSocketClient', '已经连接或正在连接中');
            return;
        }
        
        this.isConnecting = true;
        
        try {
            // 使用VCPLog路径进行认证（VCP服务器支持的正确路径）
            const wsUrl = `ws://${this.host}:${this.port}/VCPlog/VCP_Key=${this.vcpKey}`;
            this.logger.info('WebSocketClient', `正在连接到VCP服务器: ws://${this.host}:${this.port}/VCPlog/VCP_Key=***`);

            this.ws = new WebSocket(wsUrl, {
                headers: {
                    'User-Agent': 'OneBot11-Adapter/1.0.0',
                    'X-Client-Type': 'OneBot11'
                }
            });
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 等待连接建立
            await this.waitForConnection();
            
        } catch (error) {
            this.isConnecting = false;
            this.logger.error('WebSocketClient', `连接失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 等待连接建立
     */
    waitForConnection() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('连接超时'));
            }, 10000);
            
            this.ws.once('open', () => {
                clearTimeout(timeout);
                resolve();
            });
            
            this.ws.once('error', (error) => {
                clearTimeout(timeout);
                reject(error);
            });
        });
    }

    /**
     * 设置WebSocket事件监听器
     */
    setupEventListeners() {
        this.ws.on('open', () => {
            this.isConnected = true;
            this.isConnecting = false;
            this.stats.connectTime = new Date();
            
            this.logger.success('WebSocketClient', '✅ 已连接到VCP服务器');
            
            // 发送连接确认消息，标识为OneBot11客户端
            this.sendMessage({
                type: 'onebot11_client_connect',
                data: {
                    clientType: 'OneBot11',
                    adapterVersion: '1.0.0',
                    timestamp: new Date().toISOString(),
                    config: {
                        host: this.host,
                        port: this.port,
                        enableVCPIntegration: true
                    }
                }
            });
            
            // 启动心跳
            this.startHeartbeat();
            
            // 处理待发送消息队列
            this.processMessageQueue();
            
            this.emit('connected');
        });
        
        this.ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.stats.messagesReceived++;
                
                this.logger.debug('WebSocketClient', `收到VCP消息: ${JSON.stringify(message)}`);
                
                // 处理特殊消息类型
                this.handleSpecialMessages(message);
                
                this.emit('message', message);
                
            } catch (error) {
                this.logger.error('WebSocketClient', `消息解析失败: ${error.message}`);
                this.stats.errors++;
            }
        });
        
        this.ws.on('close', (code, reason) => {
            this.isConnected = false;
            this.isConnecting = false;
            this.stats.disconnectTime = new Date();
            
            this.logger.warning('WebSocketClient', `VCP连接已关闭: ${code} - ${reason}`);
            
            // 停止心跳
            this.stopHeartbeat();
            
            this.emit('disconnected', code, reason);
            
            // 自动重连
            this.scheduleReconnect();
        });
        
        this.ws.on('error', (error) => {
            this.logger.error('WebSocketClient', `VCP连接错误: ${error.message}`);
            this.stats.errors++;
            // 不要emit error事件，避免未处理的错误
            this.isConnected = false;
            this.isConnecting = false;
        });
        
        this.ws.on('ping', (data) => {
            this.logger.debug('WebSocketClient', '收到ping，发送pong');
            this.ws.pong(data);
        });
        
        this.ws.on('pong', (data) => {
            this.logger.debug('WebSocketClient', '收到pong响应');
        });
    }

    /**
     * 处理特殊消息类型
     */
    handleSpecialMessages(message) {
        switch (message.type) {
            case 'heartbeat_response':
                this.logger.debug('WebSocketClient', '收到心跳响应');
                break;

            case 'vcp_tool_result':
                this.logger.info('WebSocketClient', `收到VCP工具结果: ${message.data.tool_name}`);
                break;

            case 'system_notification':
                this.logger.info('WebSocketClient', `系统通知: ${message.data.message}`);
                break;

            case 'client_list_update':
                this.logger.debug('WebSocketClient', `客户端列表更新: ${message.data.clients.length}个客户端在线`);
                break;

            case 'broadcast':
                // 处理广播消息，提取OneBot11事件
                if (message.data && message.data.type === 'onebot11_event' && message.data.data) {
                    this.logger.debug('WebSocketClient', '收到OneBot11事件广播');
                    // 发射OneBot11事件，让bot.js处理
                    this.emit('onebot11_event', message.data.data);
                }
                break;

            default:
                // 其他消息类型不需要特殊处理
                break;
        }
    }

    /**
     * 发送消息
     */
    sendMessage(message) {
        if (!this.isConnected) {
            // 如果未连接，将消息加入队列
            this.messageQueue.push(message);
            this.logger.debug('WebSocketClient', '消息已加入队列，等待连接建立');
            return;
        }
        
        try {
            const messageStr = JSON.stringify(message);
            this.ws.send(messageStr);
            this.stats.messagesSent++;

            // 简化日志输出，只显示消息类型
            const msgType = message.type || 'unknown';
            if (msgType !== 'heartbeat') { // 心跳消息不记录
                this.logger.debug('WebSocketClient', `发送消息: ${msgType}`);
            }

        } catch (error) {
            this.logger.error('WebSocketClient', `发送消息失败: ${error.message}`);
            this.stats.errors++;

            // 如果发送失败，重新加入队列
            this.messageQueue.push(message);
        }
    }

    /**
     * 广播消息到VCP系统
     */
    broadcast(data, targetClientType = null) {
        const message = {
            type: 'broadcast',
            data: data,
            targetClientType: targetClientType,
            timestamp: getBeijingTimestamp()
        };
        
        this.sendMessage(message);
    }

    /**
     * 发送VCP日志
     */
    sendVCPLog(toolName, status, content, source = 'onebot11') {
        const logMessage = {
            type: 'vcp_log',
            data: {
                tool_name: toolName,
                status: status,
                content: content,
                source: source,
                timestamp: getBeijingTimestamp()
            }
        };
        
        this.sendMessage(logMessage);
    }

    /**
     * 处理消息队列
     */
    async processMessageQueue() {
        if (this.isProcessingQueue || this.messageQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        try {
            while (this.messageQueue.length > 0 && this.isConnected) {
                const message = this.messageQueue.shift();
                this.sendMessage(message);
                
                // 控制发送速度，避免过快发送
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        } catch (error) {
            this.logger.error('WebSocketClient', `消息队列处理失败: ${error.message}`);
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.stopHeartbeat(); // 确保没有重复的心跳定时器
        
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                this.sendMessage({
                    type: 'heartbeat',
                    data: {
                        timestamp: new Date().toISOString(),
                        clientType: 'OneBot11'
                    }
                });
                // 只在debug模式下显示心跳日志
                this.logger.debug('WebSocketClient', '发送心跳');
            }
        }, this.heartbeatInterval);

        this.logger.debug('WebSocketClient', '心跳已启动');
    }

    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
            this.logger.debug('WebSocketClient', '心跳已停止');
        }
    }

    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        
        this.reconnectTimer = setTimeout(async () => {
            this.stats.reconnectCount++;
            this.logger.info('WebSocketClient', `尝试重新连接VCP服务器 (第${this.stats.reconnectCount}次)`);
            
            try {
                await this.connect();
            } catch (error) {
                this.logger.error('WebSocketClient', `重连失败: ${error.message}`);
            }
        }, this.reconnectInterval);
    }

    /**
     * 断开连接
     */
    async disconnect() {
        this.logger.info('WebSocketClient', '正在断开VCP连接...');
        
        // 停止心跳和重连定时器
        this.stopHeartbeat();
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        // 发送断开连接消息
        if (this.isConnected) {
            this.sendMessage({
                type: 'client_disconnect',
                data: {
                    clientType: 'OneBot11',
                    timestamp: new Date().toISOString()
                }
            });
            
            // 等待消息发送完成
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // 关闭WebSocket连接
        if (this.ws) {
            this.ws.close(1000, 'Normal closure');
            this.ws = null;
        }
        
        this.isConnected = false;
        this.isConnecting = false;
        
        this.logger.success('WebSocketClient', 'VCP连接已断开');
    }

    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            isConnecting: this.isConnecting,
            host: this.host,
            port: this.port,
            path: this.path,
            stats: this.stats,
            queueLength: this.messageQueue.length
        };
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            connectTime: null,
            disconnectTime: null,
            messagesSent: 0,
            messagesReceived: 0,
            reconnectCount: 0,
            errors: 0
        };
    }
}

module.exports = WebSocketClient;
