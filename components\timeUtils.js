// components/timeUtils.js
// 时间相关工具函数

/**
 * 北京时间格式化函数
 */
function formatBeijingTime(date = new Date()) {
    // 北京时间 (UTC+8)
    const beijingDate = new Date(date.getTime() + (8 * 60 * 60 * 1000));
    
    const year = beijingDate.getUTCFullYear();
    const month = (beijingDate.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = beijingDate.getUTCDate().toString().padStart(2, '0');
    const hour = beijingDate.getUTCHours().toString().padStart(2, '0');
    const minute = beijingDate.getUTCMinutes().toString().padStart(2, '0');
    const second = beijingDate.getUTCSeconds().toString().padStart(2, '0');
    
    return `${year}年${month}月${day}日${hour}时${minute}分${second}秒`;
}

/**
 * 辅助函数：计算时间差
 */
function getTimeAgo(timestamp) {
    try {
        const now = new Date();
        const time = new Date(timestamp);
        const diffMs = now - time;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;
        return time.toLocaleDateString();
    } catch (error) {
        return '未知时间';
    }
}

/**
 * 辅助函数：格式化字节数
 */
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 辅助函数：格式化运行时间
 */
function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    let result = '';
    if (days > 0) result += `${days}天 `;
    if (hours > 0) result += `${hours}小时 `;
    if (minutes > 0) result += `${minutes}分钟 `;
    result += `${secs}秒`;

    return result;
}

module.exports = {
    formatBeijingTime,
    getTimeAgo,
    formatBytes,
    formatUptime
};
