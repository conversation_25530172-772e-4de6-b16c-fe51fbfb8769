@font-face {
    font-family: "jty";
    src: url("jty.OTF");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "NZBZ";
    src: url("NZBZ.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "Chinese";
    src: url("ZCOOLQingKeHuangYou-Regular.ttf");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "AaGuXiLaZhangGuanKeAiDeShen-2";
    src: url("AaGuXiLaZhangGuanKeAiDeShen-2.ttf");
    font-weight: normal;
    font-style: normal;
}

@import url('https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap');
@import url('fontawesome-free-6.6.0-web/css/all.min.css');

body {
    font-family: "Chinese";
    background: #ffffff;
    color: #cfb9b9;
    margin: 0;
    background-image: url('绘梨衣.jpg');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    padding: 20px;
}


.main-title {
    font-family: "NZBZ";
    font-size: 55px;
    background: linear-gradient(90deg, #414241, #1b1f24);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 30px;
    text-align: center;
    text-shadow: 0 0 10px rgba(226, 228, 230, 0.7);
}

.panel {
    background: rgba(30, 30, 30, 0.85);
    border-radius: 15px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
    margin: 20px auto;
    padding: 20px;
    width: 90%;
    max-width: 800px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.panel:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.7);
}

.title {
    font-family: "AaGuXiLaZhangGuanKeAiDeShen-2";
    display: inline-block;
    font-size: 30px;
    background: linear-gradient(90deg, #ffffff, #00ddfb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 20px;
    text-align: left;
    width: 100%;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    clip-path: polygon(0% 5%, 5% 0%, 15% 3%, 25% 0%, 35% 2%, 45% 0%, 55% 3%, 65% 0%, 75% 2%, 85% 0%, 95% 3%, 100% 5%,
            95% 95%, 100% 100%, 90% 97%, 80% 100%, 70% 98%, 60% 100%, 50% 97%, 40% 100%, 30% 98%, 20% 100%, 10% 97%, 0% 100%);
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    border: 2px solid #3a3a3a;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.feature-item::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(45deg,
            transparent,
            transparent 10px,
            rgba(0, 255, 255, 0.05) 10px,
            rgba(0, 255, 255, 0.05) 20px);
    animation: shine 10s linear infinite;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
}

.feature-item h3 {
    color: #00ffff;
    font-family: 'Orbitron', sans-serif;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
    margin-bottom: 10px;
}

.feature-item p {
    color: #b3b3b3;
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
}

@keyframes shine {
    0% {
        transform: translateX(-50%) translateY(-50%) rotate(0deg);
    }

    100% {
        transform: translateX(-50%) translateY(-50%) rotate(360deg);
    }
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.feature-item:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-item:hover:before {
    opacity: 1;
}

.feature-item:nth-child(odd) {
    background: linear-gradient(135deg, #55efc4, #00cec9);
}

.feature-item:nth-child(even) {
    background: linear-gradient(135deg, #74b9ff, #a29bfe);
}

.feature-number {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    margin-top: 5px;
    background: #333;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    flex-shrink: 0;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.feature-item .title {
    font-weight: bold;
    margin: 0;
    color: #fff;
}

.feature-item p {
    font-size: 18px;
    margin-top: 5px;
}

.feature-item:nth-child(odd) p {
    color: #000;
}

.feature-item:nth-child(even) p {
    color: #fff;
}

.created-by {
    font-size: 24px;
    background: linear-gradient(90deg, #ff6a00, #ee0979);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    margin-top: 40px;
    padding: 10px 0;
}

.fa-icon {
    margin-right: 15px;
    font-size: 34px;
}