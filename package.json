{"name": "cherry-var", "version": "1.0.0", "main": "server.js", "scripts": {"start": "pm2 start ecosystem.config.js", "stop": "pm2 stop ecosystem.config.js", "restart": "pm2 restart ecosystem.config.js", "logs": "pm2 logs", "monit": "pm2 monit", "status": "pm2 status", "delete": "pm2 delete all", "dev": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@tavily/core": "^0.5.2", "adm-zip": "^0.5.16", "axios": "^1.6.0", "basic-auth": "^2.0.1", "chalk": "^4.1.2", "chinese-lunar-calendar": "^1.0.1", "chokidar": "^4.0.3", "compromise": "^14.14.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-ws": "^5.0.2", "form-data": "^4.0.3", "fs-extra": "^11.3.0", "handlebars": "^4.7.8", "js-tiktoken": "^1.0.20", "jsdom": "^24.0.0", "json-rules-engine": "^7.3.1", "mime-types": "^2.1.35", "minimist": "^1.2.8", "ml-matrix": "^6.12.1", "moment": "^2.30.1", "natural": "^6.12.0", "node-fetch": "^3.3.2", "node-schedule": "^2.1.1", "nodejieba": "^3.4.4", "open": "^10.1.2", "pdf-parse": "^1.1.1", "puppeteer": "*", "segment": "^0.1.3", "similarity": "^1.2.1", "sqlite3": "^5.1.7", "stemmer": "^2.0.1", "textract": "^2.5.0", "uuid": "^9.0.0", "ws": "^8.17.0", "xlsx": "^0.18.5"}}