/**
 * OneBot11 Plugin Base Class
 * All plugins should inherit from this base class
 */

class BasePlugin {
    constructor(name, adapter, logger, config) {
        this.name = name;
        this.adapter = adapter;
        this.logger = logger;
        this.config = config || {};
        this.enabled = true;

        // 优先级系统 - 数值越小优先级越高
        this.priority = 100; // 默认优先级

        // 权限系统
        this.permission = 'all'; // 权限级别: 'all', 'admin', 'super_admin', 'master'

        // 支持的消息类型
        this.supportedTypes = ['message']; // message, notice, request, meta_event

        // 触发条件
        this.triggers = {
            keywords: [], // 关键词触发
            commands: [], // 命令触发 (以/开头)
            patterns: [], // 正则表达式触发
            always: false // 总是触发
        };

        // 详细权限设置
        this.permissions = {
            requireAdmin: false, // 需要管理员权限
            allowedUsers: [], // 允许的用户ID列表
            allowedGroups: [], // 允许的群组ID列表
            blacklistUsers: [], // 黑名单用户
            blacklistGroups: [], // 黑名单群组
            permissionLevel: 'all' // 权限级别
        };

        // 统计信息
        this.stats = {
            triggered: 0,
            lastTriggered: null,
            errors: 0,
            permissionDenied: 0
        };

        // 插件元信息
        this.meta = {
            version: '1.0.0',
            author: 'Unknown',
            description: '',
            usage: '',
            example: ''
        };
    }

    /**
     * Plugin initialization
     * Subclasses should override this method
     */
    async initialize() {
        this.logger.info('Plugin', `Plugin ${this.name} initialized successfully`);
    }

    /**
     * Check if this message should be handled
     */
    async shouldHandle(context) {
        try {
            // Check if plugin is enabled
            if (!this.enabled) {
                return false;
            }

            // Check message type
            if (!this.supportedTypes.includes(context.event.post_type)) {
                return false;
            }

            // Check permissions
            if (!await this.checkPermissions(context)) {
                return false;
            }

            // Check trigger conditions
            return await this.checkTriggers(context);

        } catch (error) {
            this.logger.error('Plugin', `Plugin ${this.name} trigger check failed: ${error.message}`);
            return false;
        }
    }

    /**
     * 检查权限
     */
    async checkPermissions(context) {
        const { userId, groupId, sender } = context;

        // 检查黑名单
        if (this.permissions.blacklistUsers.includes(userId)) {
            this.stats.permissionDenied++;
            return false;
        }

        if (groupId && this.permissions.blacklistGroups.includes(groupId)) {
            this.stats.permissionDenied++;
            return false;
        }

        // 检查白名单
        if (this.permissions.allowedUsers.length > 0 &&
            !this.permissions.allowedUsers.includes(userId)) {
            this.stats.permissionDenied++;
            return false;
        }

        if (groupId && this.permissions.allowedGroups.length > 0 &&
            !this.permissions.allowedGroups.includes(groupId)) {
            this.stats.permissionDenied++;
            return false;
        }

        // 检查权限级别
        const hasPermission = await this.checkPermissionLevel(context);
        if (!hasPermission) {
            this.stats.permissionDenied++;
            // 发送权限不足提示
            await this.sendPermissionDeniedMessage(context);
            return false;
        }

        return true;
    }

    /**
     * 检查权限级别
     */
    async checkPermissionLevel(context) {
        const { userId, groupId, sender } = context;
        const permissionConfig = this.config.PERMISSIONS || {};

        // 如果权限检查被禁用，直接通过
        if (!permissionConfig.ENABLE_PERMISSION_CHECK) {
            return true;
        }

        // 如果插件不需要特殊权限，直接通过
        if (this.permission === 'all') {
            return true;
        }

        // 检查超级管理员
        if (permissionConfig.SUPER_ADMINS && permissionConfig.SUPER_ADMINS.includes(userId)) {
            return true;
        }

        // 检查master权限
        if (this.permission === 'master') {
            return permissionConfig.SUPER_ADMINS && permissionConfig.SUPER_ADMINS.includes(userId);
        }

        // 检查super_admin权限
        if (this.permission === 'super_admin') {
            return permissionConfig.SUPER_ADMINS && permissionConfig.SUPER_ADMINS.includes(userId);
        }

        // 检查admin权限
        if (this.permission === 'admin') {
            // 检查机器人管理员
            if (permissionConfig.BOT_ADMINS && permissionConfig.BOT_ADMINS.includes(userId)) {
                return true;
            }

            // 检查群管理员
            if (groupId) {
                const groupAdmins = permissionConfig.GROUP_ADMINS && permissionConfig.GROUP_ADMINS[groupId];
                if (groupAdmins && groupAdmins.includes(userId)) {
                    return true;
                }

                // 检查群内角色
                if (sender && (sender.role === 'admin' || sender.role === 'owner')) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 发送权限不足消息
     */
    async sendPermissionDeniedMessage(context) {
        try {
            const permissionConfig = this.config.PERMISSIONS || {};
            const message = permissionConfig.PERMISSION_DENIED_MESSAGE || '❌ 权限不足，该功能需要管理员权限';
            await this.reply(context, message);
        } catch (error) {
            this.logger.error('Plugin', `发送权限不足消息失败: ${error.message}`);
        }
    }

    /**
     * Check trigger conditions
     */
    async checkTriggers(context) {
        const { message } = context;

        // Always trigger
        if (this.triggers.always) {
            return true;
        }

        if (!message) {
            return false;
        }

        const messageText = message.toLowerCase();

        // Check keyword triggers
        for (const keyword of this.triggers.keywords) {
            if (messageText.includes(keyword.toLowerCase())) {
                return true;
            }
        }

        // Check command triggers
        for (const command of this.triggers.commands) {
            if (messageText.startsWith(`/${command.toLowerCase()}`)) {
                return true;
            }
        }

        // Check regex pattern triggers
        for (const pattern of this.triggers.patterns) {
            const regex = new RegExp(pattern, 'i');
            if (regex.test(message)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Handle message
     * Subclasses must override this method
     */
    async handle(context) {
        throw new Error(`Plugin ${this.name} must implement handle method`);
    }

    /**
     * Execute plugin logic
     */
    async execute(context) {
        try {
            this.stats.triggered++;
            this.stats.lastTriggered = new Date();

            const result = await this.handle(context);

            this.logger.debug('Plugin', `Plugin ${this.name} processing completed`);
            return result;

        } catch (error) {
            this.stats.errors++;
            this.logger.error('Plugin', `Plugin ${this.name} processing failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Send reply message
     */
    async reply(context, message) {
        try {
            const bot = { ws: context.ws, selfId: context.selfId };
            if (context.type === 'private') {
                await this.adapter.sendPrivateMsg(bot, context.userId, message);
            } else if (context.type === 'group') {
                await this.adapter.sendGroupMsg(bot, context.groupId, message);
            }
        } catch (error) {
            this.logger.error('Plugin', `Plugin ${this.name} failed to send reply: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get plugin information
     */
    getInfo() {
        return {
            name: this.name,
            enabled: this.enabled,
            priority: this.priority,
            supportedTypes: this.supportedTypes,
            triggers: this.triggers,
            permissions: this.permissions,
            stats: this.stats
        };
    }

    /**
     * Enable plugin
     */
    enable() {
        this.enabled = true;
        this.logger.info('Plugin', `Plugin ${this.name} enabled`);
    }

    /**
     * Disable plugin
     */
    disable() {
        this.enabled = false;
        this.logger.info('Plugin', `Plugin ${this.name} disabled`);
    }

    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            triggered: 0,
            lastTriggered: null,
            errors: 0
        };
    }

    /**
     * Plugin cleanup
     * Subclasses can override this method for cleanup work
     */
    async destroy() {
        this.logger.info('Plugin', `Plugin ${this.name} destroyed`);
    }
}

module.exports = BasePlugin;
