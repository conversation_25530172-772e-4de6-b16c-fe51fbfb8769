{"manifestVersion": "1.0.0", "name": "DailyNoteGet", "version": "1.0.0", "displayName": "日记内容获取器 (静态)", "description": "定期读取所有角色的日记内容，并通过系统占位符提供给服务器。", "author": "System", "pluginType": "static", "entryPoint": {"type": "nodejs", "command": "node daily-note-get.js"}, "communication": {"protocol": "stdio", "timeout": 10000}, "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{AllCharacterDiariesData}}", "description": "一个JSON字符串。解析该字符串后会得到一个对象，该对象的键是角色名 (例如 '小明')，值是对应角色所有日记文件内容的合并字符串 (以 '\\n\\n---\\n\\n' 分隔)。服务器端会使用此数据来支持传统的 `{{角色名日记本}}` 占位符的解析。"}]}, "refreshIntervalCron": "*/5 * * * *", "configSchema": {"DebugMode": {"type": "boolean", "description": "DebugMode 配置项", "required": false}}}