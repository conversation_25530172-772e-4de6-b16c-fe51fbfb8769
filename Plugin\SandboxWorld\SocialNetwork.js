/**
 * 社交网络系统
 * 管理Agent之间的关系、影响力、社交动态
 */

const fs = require('fs').promises;
const EventEmitter = require('events');

class SocialNetwork extends EventEmitter {
    constructor(worldCore) {
        super();
        this.worldCore = worldCore;
        this.logger = worldCore.logger;
        
        // 关系网络存储
        this.relationships = new Map(); // agentId -> Map(targetId -> relationship)
        
        // 关系类型定义
        this.relationshipTypes = {
            stranger: { weight: 0, label: '陌生人' },
            acquaintance: { weight: 1, label: '认识' },
            friend: { weight: 2, label: '朋友' },
            close_friend: { weight: 3, label: '好友' },
            best_friend: { weight: 4, label: '挚友' },
            romantic: { weight: 5, label: '恋人' },
            family: { weight: 6, label: '家人' },
            enemy: { weight: -1, label: '敌人' },
            rival: { weight: -2, label: '对手' }
        };

        // 社交指标
        this.socialMetrics = {
            influence: new Map(), // 影响力
            popularity: new Map(), // 受欢迎度
            trustworthiness: new Map(), // 可信度
            charisma: new Map() // 魅力值
        };

        // 社交群体
        this.socialGroups = new Map();
        
        // 社交事件历史
        this.socialEvents = [];
        
        // 更新间隔
        this.updateInterval = null;
        this.updateFrequency = 60000; // 1分钟更新一次
    }

    /**
     * 初始化社交网络系统
     */
    async init() {
        this.logger.info('🤝 初始化社交网络系统...');
        
        // 加载关系数据
        await this.loadRelationships();
        
        // 初始化社交指标
        this.initializeSocialMetrics();
        
        this.logger.info('✅ 社交网络系统初始化完成');
    }

    /**
     * 加载关系数据
     */
    async loadRelationships() {
        try {
            const data = await fs.readFile(this.worldCore.dataPath.relationships, 'utf-8');
            const relationshipData = JSON.parse(data);
            
            // 重建关系网络
            for (const [agentId, relationships] of Object.entries(relationshipData)) {
                const agentRelationships = new Map();
                for (const [targetId, relationship] of Object.entries(relationships)) {
                    agentRelationships.set(targetId, relationship);
                }
                this.relationships.set(agentId, agentRelationships);
            }
            
            this.logger.info('📖 加载关系数据成功');
            
        } catch (error) {
            this.logger.info('🆕 创建新的关系数据');
            await this.saveRelationships();
        }
    }

    /**
     * 保存关系数据
     */
    async saveRelationships() {
        try {
            const relationshipData = {};
            
            for (const [agentId, relationships] of this.relationships.entries()) {
                relationshipData[agentId] = {};
                for (const [targetId, relationship] of relationships.entries()) {
                    relationshipData[agentId][targetId] = relationship;
                }
            }
            
            await fs.writeFile(
                this.worldCore.dataPath.relationships, 
                JSON.stringify(relationshipData, null, 2)
            );
            
        } catch (error) {
            this.logger.error('❌ 保存关系数据失败:', error);
        }
    }

    /**
     * 初始化社交指标
     */
    initializeSocialMetrics() {
        const agents = this.worldCore.agentEcosystem.getAllAgents();
        
        for (const agent of agents) {
            this.socialMetrics.influence.set(agent.id, 50);
            this.socialMetrics.popularity.set(agent.id, 50);
            this.socialMetrics.trustworthiness.set(agent.id, 50);
            this.socialMetrics.charisma.set(agent.id, 50);
        }
    }

    /**
     * 启动社交网络系统
     */
    async start() {
        if (this.updateInterval) {
            return;
        }

        this.updateInterval = setInterval(() => {
            this.updateSocialNetwork();
        }, this.updateFrequency);

        this.logger.info('🤝 社交网络系统已启动');
    }

    /**
     * 暂停社交网络系统
     */
    async pause() {
        this.logger.info('⏸️ 社交网络系统已暂停');
    }

    /**
     * 恢复社交网络系统
     */
    async resume() {
        this.logger.info('▶️ 社交网络系统已恢复');
    }

    /**
     * 停止社交网络系统
     */
    async stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        await this.saveRelationships();
        this.logger.info('🛑 社交网络系统已停止');
    }

    /**
     * 更新社交网络
     */
    updateSocialNetwork() {
        if (this.worldCore.worldState.isPaused) {
            return;
        }

        // 更新社交指标
        this.updateSocialMetrics();
        
        // 检测社交群体
        this.detectSocialGroups();
        
        // 自然关系衰减
        this.decayRelationships();
        
        this.emit('socialNetworkUpdated');
    }

    /**
     * 更新社交指标
     */
    updateSocialMetrics() {
        const agents = this.worldCore.agentEcosystem.getAllAgents();
        
        for (const agent of agents) {
            // 基于关系网络计算影响力
            const influence = this.calculateInfluence(agent.id);
            this.socialMetrics.influence.set(agent.id, influence);
            
            // 基于关系质量计算受欢迎度
            const popularity = this.calculatePopularity(agent.id);
            this.socialMetrics.popularity.set(agent.id, popularity);
            
            // 基于性格和行为计算可信度
            const trustworthiness = this.calculateTrustworthiness(agent);
            this.socialMetrics.trustworthiness.set(agent.id, trustworthiness);
            
            // 基于性格计算魅力值
            const charisma = this.calculateCharisma(agent);
            this.socialMetrics.charisma.set(agent.id, charisma);
        }
    }

    /**
     * 计算影响力
     */
    calculateInfluence(agentId) {
        const relationships = this.relationships.get(agentId) || new Map();
        let influence = 0;
        
        for (const [targetId, relationship] of relationships.entries()) {
            const relationshipType = this.relationshipTypes[relationship.type];
            if (relationshipType && relationshipType.weight > 0) {
                influence += relationshipType.weight * relationship.strength;
            }
        }
        
        return Math.min(100, Math.max(0, influence));
    }

    /**
     * 计算受欢迎度
     */
    calculatePopularity(agentId) {
        let popularity = 0;
        let relationshipCount = 0;
        
        // 统计其他Agent对该Agent的关系
        for (const [otherId, relationships] of this.relationships.entries()) {
            if (otherId !== agentId && relationships.has(agentId)) {
                const relationship = relationships.get(agentId);
                const relationshipType = this.relationshipTypes[relationship.type];
                if (relationshipType && relationshipType.weight > 0) {
                    popularity += relationshipType.weight * relationship.strength;
                    relationshipCount++;
                }
            }
        }
        
        return relationshipCount > 0 ? popularity / relationshipCount : 50;
    }

    /**
     * 计算可信度
     */
    calculateTrustworthiness(agent) {
        const conscientiousness = agent.personality.conscientiousness || 50;
        const agreeableness = agent.personality.agreeableness || 50;
        const neuroticism = agent.personality.neuroticism || 50;
        
        return (conscientiousness + agreeableness + (100 - neuroticism)) / 3;
    }

    /**
     * 计算魅力值
     */
    calculateCharisma(agent) {
        const extroversion = agent.personality.extroversion || 50;
        const agreeableness = agent.personality.agreeableness || 50;
        const openness = agent.personality.openness || 50;
        
        return (extroversion + agreeableness + openness) / 3;
    }

    /**
     * 检测社交群体
     */
    detectSocialGroups() {
        // 简单的群体检测算法
        const agents = this.worldCore.agentEcosystem.getAllAgents();
        const groups = new Map();
        
        for (const agent of agents) {
            const agentRelationships = this.relationships.get(agent.id) || new Map();
            const closeConnections = [];
            
            for (const [targetId, relationship] of agentRelationships.entries()) {
                if (relationship.strength > 70 && 
                    ['friend', 'close_friend', 'best_friend'].includes(relationship.type)) {
                    closeConnections.push(targetId);
                }
            }
            
            if (closeConnections.length >= 2) {
                const groupId = `group_${agent.id}`;
                groups.set(groupId, {
                    id: groupId,
                    members: [agent.id, ...closeConnections],
                    type: 'friend_group',
                    strength: closeConnections.length,
                    createdAt: new Date()
                });
            }
        }
        
        this.socialGroups = groups;
    }

    /**
     * 关系自然衰减
     */
    decayRelationships() {
        const decayRate = 0.1; // 每次更新减少0.1
        
        for (const [agentId, relationships] of this.relationships.entries()) {
            for (const [targetId, relationship] of relationships.entries()) {
                // 只有非家人关系会衰减
                if (relationship.type !== 'family') {
                    relationship.strength = Math.max(0, relationship.strength - decayRate);
                    
                    // 如果关系强度过低，降级关系类型
                    if (relationship.strength < 20 && relationship.type !== 'stranger') {
                        this.downgradeRelationship(agentId, targetId);
                    }
                }
            }
        }
    }

    /**
     * 建立或更新关系
     */
    updateRelationship(agentId, targetId, interactionType, strength = 5) {
        if (agentId === targetId) return;
        
        // 确保两个Agent都存在关系映射
        if (!this.relationships.has(agentId)) {
            this.relationships.set(agentId, new Map());
        }
        if (!this.relationships.has(targetId)) {
            this.relationships.set(targetId, new Map());
        }
        
        const agentRelationships = this.relationships.get(agentId);
        const targetRelationships = this.relationships.get(targetId);
        
        // 获取或创建关系
        let relationship1 = agentRelationships.get(targetId) || {
            type: 'stranger',
            strength: 0,
            interactions: 0,
            lastInteraction: new Date(),
            history: []
        };
        
        let relationship2 = targetRelationships.get(agentId) || {
            type: 'stranger',
            strength: 0,
            interactions: 0,
            lastInteraction: new Date(),
            history: []
        };
        
        // 更新关系强度
        relationship1.strength = Math.min(100, relationship1.strength + strength);
        relationship2.strength = Math.min(100, relationship2.strength + strength);
        
        // 更新交互次数和时间
        relationship1.interactions++;
        relationship2.interactions++;
        relationship1.lastInteraction = new Date();
        relationship2.lastInteraction = new Date();
        
        // 记录交互历史
        const interactionRecord = {
            type: interactionType,
            strength: strength,
            timestamp: new Date()
        };
        relationship1.history.push(interactionRecord);
        relationship2.history.push(interactionRecord);
        
        // 限制历史记录长度
        if (relationship1.history.length > 50) {
            relationship1.history = relationship1.history.slice(-50);
        }
        if (relationship2.history.length > 50) {
            relationship2.history = relationship2.history.slice(-50);
        }
        
        // 根据强度更新关系类型
        this.updateRelationshipType(relationship1);
        this.updateRelationshipType(relationship2);
        
        // 保存关系
        agentRelationships.set(targetId, relationship1);
        targetRelationships.set(agentId, relationship2);
        
        this.emit('relationshipUpdated', {
            agentId,
            targetId,
            relationship: relationship1,
            interactionType,
            strength
        });
    }

    /**
     * 更新关系类型
     */
    updateRelationshipType(relationship) {
        const strength = relationship.strength;
        
        if (strength >= 90) {
            relationship.type = 'best_friend';
        } else if (strength >= 70) {
            relationship.type = 'close_friend';
        } else if (strength >= 50) {
            relationship.type = 'friend';
        } else if (strength >= 20) {
            relationship.type = 'acquaintance';
        } else {
            relationship.type = 'stranger';
        }
    }

    /**
     * 降级关系
     */
    downgradeRelationship(agentId, targetId) {
        const agentRelationships = this.relationships.get(agentId);
        const targetRelationships = this.relationships.get(targetId);
        
        if (agentRelationships && agentRelationships.has(targetId)) {
            const relationship = agentRelationships.get(targetId);
            this.updateRelationshipType(relationship);
        }
        
        if (targetRelationships && targetRelationships.has(agentId)) {
            const relationship = targetRelationships.get(agentId);
            this.updateRelationshipType(relationship);
        }
    }

    /**
     * 获取Agent的关系
     */
    getAgentRelationships(agentId) {
        return this.relationships.get(agentId) || new Map();
    }

    /**
     * 获取两个Agent之间的关系
     */
    getRelationship(agentId, targetId) {
        const relationships = this.relationships.get(agentId);
        return relationships ? relationships.get(targetId) : null;
    }

    /**
     * 获取Agent的社交指标
     */
    getSocialMetrics(agentId) {
        return {
            influence: this.socialMetrics.influence.get(agentId) || 0,
            popularity: this.socialMetrics.popularity.get(agentId) || 0,
            trustworthiness: this.socialMetrics.trustworthiness.get(agentId) || 0,
            charisma: this.socialMetrics.charisma.get(agentId) || 0
        };
    }

    /**
     * 获取社交群体
     */
    getSocialGroups() {
        return Array.from(this.socialGroups.values());
    }

    /**
     * 移除Agent的所有关系
     */
    removeAgentRelationships(agentId) {
        // 移除该Agent的关系
        this.relationships.delete(agentId);
        
        // 移除其他Agent对该Agent的关系
        for (const [otherId, relationships] of this.relationships.entries()) {
            relationships.delete(agentId);
        }
        
        // 移除社交指标
        this.socialMetrics.influence.delete(agentId);
        this.socialMetrics.popularity.delete(agentId);
        this.socialMetrics.trustworthiness.delete(agentId);
        this.socialMetrics.charisma.delete(agentId);
    }

    /**
     * 获取统计信息
     */
    async getStatistics() {
        const totalRelationships = Array.from(this.relationships.values())
            .reduce((sum, relationships) => sum + relationships.size, 0);
        
        const averageConnections = this.relationships.size > 0 ? 
            totalRelationships / this.relationships.size : 0;
        
        const relationshipTypeDistribution = {};
        for (const relationships of this.relationships.values()) {
            for (const relationship of relationships.values()) {
                const type = relationship.type;
                relationshipTypeDistribution[type] = (relationshipTypeDistribution[type] || 0) + 1;
            }
        }
        
        return {
            totalAgents: this.relationships.size,
            totalRelationships,
            averageConnections,
            socialGroups: this.socialGroups.size,
            relationshipTypeDistribution,
            averageInfluence: this.getAverageMetric('influence'),
            averagePopularity: this.getAverageMetric('popularity'),
            averageTrustworthiness: this.getAverageMetric('trustworthiness'),
            averageCharisma: this.getAverageMetric('charisma')
        };
    }

    /**
     * 获取平均指标
     */
    getAverageMetric(metricName) {
        const values = Array.from(this.socialMetrics[metricName].values());
        return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
    }

    /**
     * 销毁社交网络系统
     */
    async destroy() {
        await this.stop();
        this.removeAllListeners();
        this.logger.info('🗑️ 社交网络系统已销毁');
    }
}

module.exports = { SocialNetwork };
