// Plugin/Mcp/DailyNoteWrite.js - 日记写入MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}


class DailyNoteWriteMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'DailyNoteWrite';
        this.description = '写入和管理日记条目';
        this.vcpName = 'DailyNoteWrite';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                maidName: {
                    type: 'string',
                    description: '女仆名称'
                },
                dateString: {
                    type: 'string',
                    description: '日期字符串，格式：YYYY.M.D'
                },
                contentText: {
                    type: 'string',
                    description: '日记内容'
                }
            },
            required: ['maidName', 'dateString', 'contentText']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);
        
        this.log('info', `开始写入日记`, {
            maid: args.maidName,
            date: args.dateString
        });
        
        try {
        // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);
            
            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }
            
            // 构建标准响应格式
            const response = {
                type: 'diary_write',
                status: parsedResult?.status || 'success',
                message: parsedResult?.message || '日记写入成功',
                data: {
                    maidName: args.maidName,
                    dateString: args.dateString,
                    filePath: parsedResult?.filePath || null
                }
            };
        
            this.log('success', `日记写入完成`, response);
            return response;
            
        } catch (error) {
            const errorResponse = {
            type: 'diary_write',
                status: 'error',
                message: error.message,
                data: {
                    maidName: args.maidName,
                    dateString: args.dateString,
                    error: error.message
                }
            };
            
            this.log('error', `日记写入失败`, errorResponse);
            throw errorResponse;
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = DailyNoteWriteMcp; 