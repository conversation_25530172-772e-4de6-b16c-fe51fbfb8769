// components/messageProcessor.js
// 消息处理相关函数

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');

const logger = require('../utils/logger.cjs');

/**
 * 处理用户消息，添加用户ID前缀
 */
function processUserMessage(originalUserMessageForRender, cleanedBody, DEBUG_MODE = false) {
    // 获取真正的原始用户消息（不包含任何处理）
    const lastUserMessage = cleanedBody.messages.find(msg => msg.role === 'user');
    let originalUserContent = lastUserMessage ? lastUserMessage.content : '';

    // 如果content是数组格式，提取文本内容
    if (Array.isArray(originalUserContent)) {
        const textContent = originalUserContent.find(item => item.type === 'text');
        originalUserContent = textContent ? textContent.text : '';
    }

    let processedUserMessage = originalUserMessageForRender;

    if (DEBUG_MODE && logger) {
        logger.info('消息处理', `原始用户消息: ${originalUserContent.substring(0, 100)}...`);
        logger.info('消息处理', `渲染用消息: ${processedUserMessage.substring(0, 100)}...`);
    }



    // 用户ID前缀添加功能已废弃，不再添加"用户【xxx】:"格式

    if (DEBUG_MODE && logger) {
        logger.info('消息处理', `最终处理后消息: ${processedUserMessage.substring(0, 100)}...`);
    }

    return processedUserMessage;
}

/**
 * 处理Agent参数
 */
async function processAgentParameter(originalBody, cleanedBody, AGENT_DIR) {
    if (originalBody.agent && typeof originalBody.agent === 'string') {
        try {
            const agentName = originalBody.agent;
            const agentFilePath = path.join(AGENT_DIR, `${agentName}.txt`);
            
            logger.info('Agent系统', `尝试加载Agent: ${agentName}`);
            
            // 检查agent文件是否存在
            if (fsSync.existsSync(agentFilePath)) {
                const agentContent = await fs.readFile(agentFilePath, 'utf-8');
                
                // 过滤掉用户提交的所有system消息
                cleanedBody.messages = cleanedBody.messages.filter(msg => msg.role !== 'system');
                
                // 将agent内容作为第一个system消息添加
                cleanedBody.messages.unshift({
                    role: 'system',
                    content: agentContent
                });
                
                logger.success('Agent系统', `成功加载Agent ${agentName}，已替换system消息`);
            } else {
                logger.warning('Agent系统', `Agent文件不存在: ${agentFilePath}，忽略agent参数`);
            }
        } catch (error) {
            logger.error('Agent系统', `加载Agent失败: ${error.message}`);
            // 继续处理，不因agent加载失败而中断
        }
    }
}

/**
 * 处理UserAgent参数
 */
async function processUserAgentParameter(originalBody, cleanedBody, AGENT_DIR) {
    if (originalBody.useragent && typeof originalBody.useragent === 'string') {
        try {
            const useragentName = originalBody.useragent;
            const useragentFilePath = path.join(AGENT_DIR, `${useragentName}.txt`);

            logger.info('UserAgent系统', `尝试加载UserAgent: ${useragentName}`);

            // 检查useragent文件是否存在
            if (fsSync.existsSync(useragentFilePath)) {
                const useragentContent = await fs.readFile(useragentFilePath, 'utf-8');

                // 找到第一个system消息
                const systemMessageIndex = cleanedBody.messages.findIndex(msg => msg.role === 'system');

                if (systemMessageIndex !== -1) {
                    // 在原有system消息后追加useragent内容
                    cleanedBody.messages[systemMessageIndex].content += `\n\n=== 用户 ${cleanedBody.userId || 'Unknown'} 的个人资料 ===\n${useragentContent}`;
                    logger.success('UserAgent系统', `成功加载UserAgent ${useragentName}，已追加到system消息`);
                } else {
                    // 如果没有system消息，创建一个新的
                    cleanedBody.messages.unshift({
                        role: 'system',
                        content: `=== 用户 ${cleanedBody.userId || 'Unknown'} 的个人资料 ===\n${useragentContent}`
                    });
                    logger.success('UserAgent系统', `成功加载UserAgent ${useragentName}，已创建新的system消息`);
                }
            } else {
                logger.warning('UserAgent系统', `UserAgent文件不存在: ${useragentFilePath}，忽略useragent参数`);
            }
        } catch (error) {
            logger.error('UserAgent系统', `加载UserAgent失败: ${error.message}`);
            // 继续处理，不因useragent加载失败而中断
        }
    }
}

/**
 * 处理聊天上下文参数 - 统一处理群聊和私聊
 */
async function processChatContextParameter(originalBody, cleanedBody) {
    if (originalBody.chat_context && typeof originalBody.chat_context === 'object') {
        try {
            const { type, name, history_count = 10, user_names = {} } = originalBody.chat_context;

            logger.info('聊天上下文', `处理${type}聊天上下文: ${name}, 历史记录数量: ${history_count}`);

            // 统一使用私聊处理方式，只通过type参数区分聊天类型
            await processPrivateChatContext(cleanedBody, name, history_count, user_names, originalBody.userId, originalBody.assistantName, type);

        } catch (error) {
            logger.error('聊天上下文', `处理聊天上下文失败: ${error.message}`);
            // 继续处理，不因聊天上下文加载失败而中断
        }
    }
}

// 群聊特殊处理函数已删除，统一使用processPrivateChatContext处理

/**
 * 统一处理聊天上下文 - 支持群聊和私聊
 * @param {Object} cleanedBody - 清理后的请求体
 * @param {string} chatName - 聊天名称
 * @param {number} historyCount - 历史记录数量
 * @param {Object} userNames - 用户名映射
 * @param {string} userId - 用户ID
 * @param {string} assistantName - 助手名称
 * @param {string} chatType - 聊天类型，'group'或'private'
 */
async function processPrivateChatContext(cleanedBody, chatName, historyCount, userNames, userId, assistantName, chatType = 'private') {
    try {
        logger.info('聊天上下文', `加载${chatType}聊天历史: ${chatName}, 数量: ${historyCount}`);

        // 统一获取聊天历史记录
        const chatHistory = await loadChatHistory(userId, assistantName, historyCount, chatType, chatName);

        if (chatHistory && chatHistory.length > 0) {
            // 统一使用私聊的role-based消息处理方式
            // 找到最后一个user消息的位置
            const lastUserMessageIndex = cleanedBody.messages.map((msg, index) => ({ msg, index }))
                .filter(item => item.msg.role === 'user')
                .pop()?.index;

            if (lastUserMessageIndex !== undefined) {
                // 构建历史记录消息并合并连续的相同角色消息
                const rawHistoryMessages = chatHistory.map(record => {
                    const timeStr = formatTimestamp(record.timestamp);

                    // 根据聊天类型生成不同的前缀
                    let chatPrefix;
                    if (chatType === 'group') {
                        chatPrefix = `[在${chatName}群聊中][${timeStr}]`;
                    } else {
                        chatPrefix = `[${chatName}][${timeStr}]`;
                    }

                    // 统一使用speaker字段处理
                    if (record.speaker === 'user') {
                        return {
                            role: 'user',
                            content: `${chatPrefix} ${record.content}`
                        };
                    } else if (record.speaker === 'assistant') {
                        return {
                            role: 'assistant',
                            content: record.content
                        };
                    } else {
                        // 如果遇到其他格式，跳过
                        logger.warning('聊天上下文', `跳过未知speaker类型: ${record.speaker}`);
                        return null;
                    }
                }).filter(msg => msg !== null); // 过滤掉null值

                // 合并连续的相同角色消息
                const historyMessages = mergeConsecutiveMessages(rawHistoryMessages);

                // 替换最后一个user消息前的位置，插入历史记录
                // 这样历史记录会在当前用户消息之前，形成完整的对话历史
                cleanedBody.messages.splice(lastUserMessageIndex, 0, ...historyMessages);

                logger.success('聊天上下文', `成功加载${chatType}聊天历史 ${chatName}，已添加${historyMessages.length}条role-based消息`);
            } else {
                logger.warning('聊天上下文', '未找到user消息，无法插入聊天历史');
            }
        } else {
            logger.warning('聊天上下文', `${chatType}聊天 ${chatName} 暂无历史记录`);
        }
    } catch (error) {
        logger.error('聊天上下文', `处理${chatType}聊天上下文失败: ${error.message}`);
    }
}

/**
 * 统一加载聊天历史记录 - 支持群聊和私聊
 * @param {string} userId - 用户ID
 * @param {string} assistantName - 助手名称
 * @param {number} count - 记录数量
 * @param {string} chatType - 聊天类型，'group'或'private'
 * @param {string} chatName - 聊天名称
 */
async function loadChatHistory(userId, assistantName, count = 10, chatType = 'private', chatName = '') {
    try {
        logger.info('聊天上下文', `从数据库加载${chatType}聊天历史: ${chatName || userId}, 数量: ${count}`);

        // 统一使用数据库查询，不再区分群聊和私聊的加载方式
        if (!global.advancedMemoryPlugin || !global.advancedMemoryPlugin.conversationManager) {
            logger.debug('聊天上下文', '智能情感记忆系统未初始化，无法从数据库获取聊天历史');
            return [];
        }

        const conversationManager = global.advancedMemoryPlugin.conversationManager;

        // 修复查询逻辑 - 直接查询用户和助手的记录
        logger.debug('聊天上下文', `查询参数: userId=${userId}, assistantName=${assistantName}, chatType=${chatType}, chatName=${chatName}`);

        // 统一的数据库查询 - 根据用户ID和助手名称查询
        const history = await conversationManager.dbAll(`
            SELECT speaker, content, timestamp, importance_score
            FROM conversation_history
            WHERE user_id = ?
            AND persona_name = ?
            AND speaker IN ('user', 'assistant')
            AND chat_type = ?
            ORDER BY timestamp DESC
            LIMIT ?
        `, [userId, assistantName, chatType, count * 2]); // 获取更多记录以确保有足够的对话

        if (!history || history.length === 0) {
            logger.debug('聊天上下文', `数据库中没有找到${chatType}聊天 ${chatName || userId} 的历史记录`);
            return [];
        }

        // 统一的格式化处理
        const formattedMessages = history.reverse().map(msg => {
            return {
                speaker: msg.speaker,
                content: msg.content,
                timestamp: msg.timestamp,
                importance_score: msg.importance_score
            };
        });

        return formattedMessages.slice(-count); // 返回最近的count条记录

    } catch (error) {
        logger.error('聊天上下文', `从数据库加载${chatType}聊天历史失败: ${error.message}`);
        return [];
    }
}

// 私聊历史加载函数已删除，统一使用loadChatHistory函数

/**
 * 合并连续的相同角色消息
 * @param {Array} messages - 消息数组
 * @returns {Array} 合并后的消息数组
 */
function mergeConsecutiveMessages(messages) {
    if (!messages || messages.length === 0) {
        return [];
    }

    const mergedMessages = [];
    let currentMessage = null;

    for (const message of messages) {
        if (!currentMessage) {
            // 第一条消息
            currentMessage = { ...message };
        } else if (currentMessage.role === message.role) {
            // 相同角色，合并内容
            if (currentMessage.role === 'user') {
                // 用户消息：保持时间戳格式，用换行分隔
                currentMessage.content += '\n' + message.content;
            } else {
                // 助手消息：直接合并，用换行分隔
                currentMessage.content += '\n' + message.content;
            }
            // 更新时间戳为最新的
            currentMessage.timestamp = message.timestamp;
        } else {
            // 不同角色，保存当前消息并开始新消息
            mergedMessages.push(currentMessage);
            currentMessage = { ...message };
        }
    }

    // 添加最后一条消息
    if (currentMessage) {
        mergedMessages.push(currentMessage);
    }

    return mergedMessages;
}

/**
 * 格式化时间戳为简短格式
 */
function formatTimestamp(timestamp) {
    try {
        const date = new Date(timestamp);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        logger.warning('时间格式化', `格式化时间戳失败: ${error.message}`);
        return '未知时间';
    }
}

// 群聊历史加载函数已删除，统一使用loadChatHistory函数

/**
 * 获取AI助手的真实名称
 * @param {string} chatType - 聊天类型，'group'或'private'
 */
function getAIAssistantName(chatType = 'private') {
    try {
        // 优先从微信适配器配置获取
        const wechatConfig = global.wechatConfig;
        if (wechatConfig && wechatConfig.AI_AUTO_REPLY) {
            const aiConfig = wechatConfig.AI_AUTO_REPLY;

            // 群聊模式下获取assistantName
            if (aiConfig.group_chat && aiConfig.group_chat.assistantName) {
                return aiConfig.group_chat.assistantName;
            }

            // 私聊模式下获取assistantName
            if (aiConfig.private_chat && aiConfig.private_chat.assistantName) {
                return aiConfig.private_chat.assistantName;
            }
        }

        // 从全局配置获取
        const config = global.config;
        if (config && config.AI_AUTO_REPLY) {
            const aiConfig = config.AI_AUTO_REPLY;

            // 群聊模式下获取assistantName
            if (aiConfig.group_chat && aiConfig.group_chat.assistantName) {
                return aiConfig.group_chat.assistantName;
            }

            // 私聊模式下获取assistantName
            if (aiConfig.private_chat && aiConfig.private_chat.assistantName) {
                return aiConfig.private_chat.assistantName;
            }
        }

        // 最后尝试从环境变量或默认值获取
        return process.env.ASSISTANT_NAME || '雨安安';

    } catch (error) {
        logger.debug('群聊上下文', `获取AI助手名称失败: ${error.message}，使用默认名称`);
        return '雨安安';
    }
}

// 群聊数据库加载函数已删除，统一使用loadChatHistory函数


// 群聊文件加载函数已删除，统一使用loadChatHistory函数

/**
 * 处理MCP模式的Agent参数（保留上下文）
 */
async function processMcpAgentParameter(originalBody, cleanedBody, AGENT_DIR) {
    if (originalBody.agent && typeof originalBody.agent === 'string') {
        try {
            const agentName = originalBody.agent;
            const agentFilePath = path.join(AGENT_DIR, `${agentName}.txt`);

            logger.info('Agent系统', `[MCP模式] 尝试加载Agent: ${agentName}`);

            // 检查agent文件是否存在
            if (fsSync.existsSync(agentFilePath)) {
                const agentContent = await fs.readFile(agentFilePath, 'utf-8');

                // 保存已经注入上下文的system消息
                let contextInjectSystemMessage = null;
                const systemMsgWithContext = cleanedBody.messages.find(msg => msg.role === 'system');
                
                // 详细调试当前system消息内容
                if (systemMsgWithContext) {
                    logger.debug('Agent系统', `[MCP模式] 当前system消息长度: ${systemMsgWithContext.content.length}`);
                    logger.debug('Agent系统', `[MCP模式] system消息前200字符: ${systemMsgWithContext.content.substring(0, 200)}...`);
                    
                    const hasContextMarker = systemMsgWithContext.content.includes('=== 相关记忆和状态信息 ===') || 
                                            systemMsgWithContext.content.includes('=== 相关记忆和历史对话 ==='); // 兼容旧格式
                    const hasPsychologyMarker = systemMsgWithContext.content.includes('=== 心理状态强制行为准则 ===');
                    const hasToolAnalysisMarker = systemMsgWithContext.content.includes('=== 🔧') && systemMsgWithContext.content.includes('工具调用分析 ===');
                    
                    logger.debug('Agent系统', `[MCP模式] 包含上下文标记: ${hasContextMarker}`);
                    logger.debug('Agent系统', `[MCP模式] 包含心理状态标记: ${hasPsychologyMarker}`);
                    logger.debug('Agent系统', `[MCP模式] 包含工具分析标记: ${hasToolAnalysisMarker}`);
                    
                    if (hasContextMarker || hasPsychologyMarker || hasToolAnalysisMarker) {
                        contextInjectSystemMessage = systemMsgWithContext;
                        logger.success('Agent系统', '[MCP模式] 检测到已注入智能信息的system消息，将保留并合并');
                    } else {
                        logger.warning('Agent系统', '[MCP模式] system消息中未检测到智能信息标记，可能生成失败');
                    }
                } else {
                    logger.warning('Agent系统', '[MCP模式] 没有找到system消息');
                }

                // 过滤掉用户提交的所有system消息
                cleanedBody.messages = cleanedBody.messages.filter(msg => msg.role !== 'system');

                // 合并agent内容和智能信息（上下文、心理状态、工具分析）
                let finalSystemContent = agentContent;
                if (contextInjectSystemMessage) {
                    // 提取各种智能信息部分
                    const newContextMatch = contextInjectSystemMessage.content.match(/(=== 相关记忆和状态信息 ===.*?=== 状态信息结束 ===)/s);
                    const oldContextMatch = contextInjectSystemMessage.content.match(/(=== 相关记忆和历史对话 ===.*?=== 结束相关记忆 ===)/s);
                    const psychologyMatch = contextInjectSystemMessage.content.match(/(=== 心理状态强制行为准则 ===[\s\S]*)/);
                    const toolAnalysisMatch = contextInjectSystemMessage.content.match(/(=== 🔧.*?工具调用分析 ===.*?=== 🔧 工具分析结束 ===)/s);

                    let intelligentParts = [];
                    
                    // 添加上下文信息（优先新格式）
                    if (newContextMatch) {
                        intelligentParts.push(newContextMatch[1]);
                        logger.debug('Agent系统', '[MCP模式] 提取到新格式智能上下文');
                    } else if (oldContextMatch) {
                        intelligentParts.push(oldContextMatch[1]);
                        logger.debug('Agent系统', '[MCP模式] 提取到旧格式智能上下文');
                    }
                    
                    // 添加心理状态指令
                    if (psychologyMatch) {
                        intelligentParts.push(psychologyMatch[1]);
                        logger.debug('Agent系统', '[MCP模式] 提取到心理状态指令');
                    }
                    
                    // 添加工具分析结果
                    if (toolAnalysisMatch) {
                        intelligentParts.push(toolAnalysisMatch[1]);
                        logger.debug('Agent系统', '[MCP模式] 提取到工具分析结果');
                    }

                    if (intelligentParts.length > 0) {
                        finalSystemContent = agentContent + '\n\n' + intelligentParts.join('\n\n');
                        logger.success('Agent系统', `[MCP模式] 成功合并Agent和${intelligentParts.length}个智能信息部分`);
                    }
                }

                // 将合并后的内容作为第一个system消息添加
                cleanedBody.messages.unshift({
                    role: 'system',
                    content: finalSystemContent
                });

                logger.success('Agent系统', `[MCP模式] 成功加载Agent ${agentName}，已替换system消息`);
            } else {
                logger.warning('Agent系统', `[MCP模式] Agent文件不存在: ${agentFilePath}，忽略agent参数`);
            }
        } catch (error) {
            logger.error('Agent系统', `[MCP模式] 加载Agent失败: ${error.message}`);
            // 继续处理，不因agent加载失败而中断
        }
    }
}

/**
 * 处理MCP模式的UserAgent参数
 */
async function processMcpUserAgentParameter(originalBody, cleanedBody, AGENT_DIR) {
    if (originalBody.useragent && typeof originalBody.useragent === 'string') {
        try {
            const useragentName = originalBody.useragent;
            const useragentFilePath = path.join(AGENT_DIR, `${useragentName}.txt`);

            logger.info('UserAgent系统', `[MCP模式] 尝试加载UserAgent: ${useragentName}`);

            // 检查useragent文件是否存在
            if (fsSync.existsSync(useragentFilePath)) {
                const useragentContent = await fs.readFile(useragentFilePath, 'utf-8');

                // 找到第一个system消息
                const systemMessageIndex = cleanedBody.messages.findIndex(msg => msg.role === 'system');

                if (systemMessageIndex !== -1) {
                    // 在原有system消息后追加useragent内容
                    cleanedBody.messages[systemMessageIndex].content += `\n\n=== 用户 ${cleanedBody.userId || 'Unknown'} 的个人资料 ===\n${useragentContent}`;
                    logger.success('UserAgent系统', `[MCP模式] 成功加载UserAgent ${useragentName}，已追加到system消息`);
                } else {
                    // 如果没有system消息，创建一个新的
                    cleanedBody.messages.unshift({
                        role: 'system',
                        content: `=== 用户 ${cleanedBody.userId || 'Unknown'} 的个人资料 ===\n${useragentContent}`
                    });
                    logger.success('UserAgent系统', `[MCP模式] 成功加载UserAgent ${useragentName}，已创建新的system消息`);
                }
            } else {
                logger.warning('UserAgent系统', `[MCP模式] UserAgent文件不存在: ${useragentFilePath}，忽略useragent参数`);
            }
        } catch (error) {
            logger.error('UserAgent系统', `[MCP模式] 加载UserAgent失败: ${error.message}`);
            // 继续处理，不因useragent加载失败而中断
        }
    }
}

/**
 * 处理MCP模式的聊天上下文参数
 */
async function processMcpChatContextParameter(originalBody, cleanedBody) {
    if (originalBody.chat_context && typeof originalBody.chat_context === 'object') {
        try {
            const { type, name, history_count = 10, user_names = {} } = originalBody.chat_context;

            logger.info('聊天上下文', `[MCP模式] 处理${type}聊天上下文: ${name}, 历史记录数量: ${history_count}`);

            // 统一使用processPrivateChatContext处理，只通过type参数区分聊天类型
            await processPrivateChatContext(cleanedBody, name, history_count, user_names, originalBody.userId, originalBody.assistantName, type);
        } catch (error) {
            logger.error('聊天上下文', `[MCP模式] 处理聊天上下文失败: ${error.message}`);
            // 继续处理，不因聊天上下文加载失败而中断
        }
    }
}

// MCP模式的群聊处理函数已删除，统一使用processPrivateChatContext处理



/**
 * 清理请求体中的自定义参数
 */
function cleanRequestBody(cleanedBody) {
    // 清理请求体，移除自定义参数避免传递给AI API
    delete cleanedBody.memory_tracking;
    delete cleanedBody.enable_context;
    delete cleanedBody.maxContextSize;
    delete cleanedBody.userId;
    delete cleanedBody.user_id;
    delete cleanedBody.type;
    delete cleanedBody.assistantName;
    delete cleanedBody.assistant_id;
    delete cleanedBody.agent;
    delete cleanedBody.useragent;

    delete cleanedBody.chat_context;
    delete cleanedBody.censor_vcp_output;
    delete cleanedBody.vcp_content_max_length;
}

module.exports = {
    processUserMessage,
    processAgentParameter,
    processUserAgentParameter,
    processChatContextParameter,
    processMcpAgentParameter,
    processMcpUserAgentParameter,
    processMcpChatContextParameter,
    cleanRequestBody,
    loadChatHistory
};
