// Plugin/Mcp/VideoGenerator.js - 视频生成MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class VideoGeneratorMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'VideoGenerator';
        this.description = '使用Wan2.1 API进行文本到视频或图像到视频的生成';
        this.vcpName = 'Wan2.1VideoGen';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                command: {
                    type: 'string',
                    enum: ['submit', 'query'],
                    description: '操作类型：submit提交生成任务，query查询任务状态'
                },
                mode: {
                    type: 'string',
                    enum: ['t2v', 'i2v'],
                    description: '生成模式：t2v为文本生成视频，i2v为图像生成视频（仅在command为submit时需要）'
                },
                prompt: {
                    type: 'string',
                    description: '视频描述文本或指导性提示词'
                },
                resolution: {
                    type: 'string',
                    enum: ['1280x720', '720x1280', '960x960'],
                    description: '视频分辨率（仅在t2v模式下需要）'
                },
                image_url: {
                    type: 'string',
                    description: '图片链接（仅在i2v模式下需要）'
                },
                negative_prompt: {
                    type: 'string',
                    description: '负面提示词（可选）'
                },
                request_id: {
                    type: 'string',
                    description: '任务ID（仅在query命令时需要）'
                }
            },
            required: ['command']
        };
    }

    async execute(args) {
        try {
            this.validateArgs(args);
            
            const { command, mode, prompt, resolution, image_url, negative_prompt, request_id } = args;
            
            this.log('info', `开始执行视频生成操作: ${command}`);
            
            // 验证参数
            if (command === 'submit') {
                if (!mode) {
                    throw new Error('submit命令需要指定mode参数');
                }
                
                if (mode === 't2v') {
                    if (!prompt || !resolution) {
                        throw new Error('t2v模式需要prompt和resolution参数');
                    }
                } else if (mode === 'i2v') {
                    if (!image_url) {
                        throw new Error('i2v模式需要image_url参数');
                    }
                }
            } else if (command === 'query') {
                if (!request_id) {
                    throw new Error('query命令需要request_id参数');
                }
            }
            
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);
            
            // 解析VCP插件返回的结果
            let parsedResult;
            if (typeof result === 'string') {
                try {
                    parsedResult = JSON.parse(result);
                } catch (e) {
                    parsedResult = { content: result };
                }
            } else {
                parsedResult = result;
            }
            
            // 格式化返回结果
            const response = {
                type: 'video_generation',
                status: parsedResult.status || 'success',
                message: command === 'submit' ? '视频生成任务已提交' : '任务状态查询完成',
                data: {
                    command: command,
                    mode: mode,
                    result: parsedResult,
                    timestamp: new Date().toISOString()
                }
            };
            
            // 如果是submit命令且成功，添加提示信息
            if (command === 'submit' && parsedResult.status === 'success') {
                response.message = '视频生成任务已提交，请使用返回的request_id查询生成状态';
                response.data.request_id = parsedResult.result?.requestId;
            }
            
            // 如果是query命令，添加状态信息
            if (command === 'query') {
                const taskStatus = parsedResult.result?.status;
                if (taskStatus === 'InProgress') {
                    response.message = '视频正在生成中，请稍后再次查询';
                } else if (taskStatus === 'Succeed') {
                    response.message = '视频生成完成';
                    response.data.video_url = parsedResult.result?.results?.videos?.[0]?.url;
                } else if (taskStatus === 'Failed') {
                    response.message = '视频生成失败';
                    response.data.failure_reason = parsedResult.result?.reason;
                }
            }
            
            this.log('success', `视频生成操作完成: ${command}`);
            return response;
            
        } catch (error) {
            this.log('error', `视频生成操作失败: ${error.message}`);
            return {
                type: 'video_generation',
                status: 'error',
                message: `视频生成操作失败: ${error.message}`,
                data: null
            };
        }
    }
}

module.exports = VideoGeneratorMcp; 