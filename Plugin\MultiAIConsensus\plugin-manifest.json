{"manifestVersion": "1.0.0", "name": "MultiAIConsensus", "displayName": "多AI协商插件", "version": "0.2.0", "description": "同时询问多个AI模型并收集各自的回复。支持配置继承、并发控制和超时设置。", "author": "<PERSON><PERSON>", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node MultiAIConsensus.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "configSchema": {"MULTI_AI_API_URL": {"type": "string", "description": "API地址，留空则继承主配置的API_URL", "required": false}, "MULTI_AI_API_KEY": {"type": "string", "description": "API密钥，留空则继承主配置的YUANPLUS_API_KEY", "required": false}, "MULTI_AI_MAX_CONCURRENT": {"type": "integer", "description": "同时请求的最大模型数量", "required": false, "default": 3, "minimum": 1, "maximum": 10}, "MULTI_AI_TIMEOUT": {"type": "integer", "description": "单个模型请求的超时时间(毫秒)", "required": false, "default": 30000, "minimum": 5000, "maximum": 120000}, "MULTI_AI_AVAILABLE_MODELS": {"type": "string", "description": "可用模型列表，用逗号分隔", "required": false, "default": "gpt-4o,gpt-4o-mini,claude-3-5-sonnet-20241022,gemini-2.5-flash-preview-05-20,deepseek-chat"}, "MULTI_AI_DEFAULT_MODELS": {"type": "string", "description": "默认使用的模型列表，用逗号分隔", "required": false, "default": "gpt-4o-mini,claude-3-5-sonnet-20241022"}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "MultiAIConsensus", "description": "调用此工具同时询问多个AI模型并收集各自的回复。请在您的回复中，使用以下精确格式来请求多AI协商：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MultiAIConsensus「末」,\nquery:「始」(必需) 要询问所有AI模型的问题。「末」,\nmodels:「始」(可选) 指定要使用的模型列表，用逗号分隔。「末」,\nmaxModels:「始」(可选) 最大使用模型数量，范围1-10。「末」,\nincludeDetails:「始」(可选) 是否包含详细响应信息，true或false。「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含所有AI模型回复的JSON对象。请基于这些结果回答用户的问题或完成相关任务。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MultiAIConsensus「末」,\nquery:「始」请分析当前的市场趋势。「末」,\nmodels:「始」gpt-4o-mini,claude-3-5-sonnet-20241022「末」,\nmaxModels:「始」2「末」,\nincludeDetails:「始」true「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}