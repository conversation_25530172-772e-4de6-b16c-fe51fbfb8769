/**
 * File Receiver Plugin
 * 文件和图片接收插件
 * 基于NapCat API文档实现文件接收、下载和处理功能
 * 参考: https://napneko.github.io/develop/api/doc
 */

const BasePlugin = require('./base_plugin');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

class FileReceiverPlugin extends BasePlugin {
        constructor(adapter, logger, config) {
        super('FileReceiver', adapter, logger, config);

        this.priority = 70; // 高优先级，优先处理文件消息
        this.permission = 'all'; // 所有用户可用
        this.supportedTypes = ['message'];

        // 插件元信息
        this.meta = {
            version: '1.0.0',
            author: 'VCPToolBox',
            description: '文件接收和下载插件',
            usage: '发送任意文件、图片、视频或语音，系统会自动接收和下载',
            example: '发送图片文件'
        };

        // 文件下载配置
        this.downloadConfig = {
            downloadDir: './downloads', // 下载目录
            maxFileSize: 100 * 1024 * 1024, // 最大文件大小 100MB
            allowedImageTypes: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'],
            allowedFileTypes: ['.txt', '.pdf', '.doc', '.docx', '.zip', '.rar', '.mp3', '.mp4'],
            autoDownload: true, // 是否自动下载文件
            saveFileInfo: true // 是否保存文件信息到JSON
        };

        // 确保下载目录存在
        this.ensureDownloadDir();

        this.logger.info('Plugin', 'File receiver plugin initialized');
    }

    /**
     * 确保下载目录存在
     */
    ensureDownloadDir() {
        const downloadDir = path.resolve(this.downloadConfig.downloadDir);
        if (!fs.existsSync(downloadDir)) {
            fs.mkdirSync(downloadDir, { recursive: true });
            this.logger.info('Plugin', `创建下载目录: ${downloadDir}`);
        }
    }

    /**
     * 检查是否应该处理此消息
     */
    shouldHandle(context) {
        const message = context.message;
        
        // 检查消息是否包含图片或文件
        if (Array.isArray(message)) {
            return message.some(segment => 
                segment.type === 'image' || 
                segment.type === 'file' || 
                segment.type === 'video' ||
                segment.type === 'record' // 语音消息
            );
        }

        return false;
    }

    /**
     * 处理消息
     */
    async handle(context) {
        if (!this.shouldHandle(context)) {
            return { handled: false };
        }

        try {
            const message = context.message;
            const fileInfos = [];

            // 处理消息中的每个段落
            for (const segment of message) {
                if (segment.type === 'image') {
                    const imageInfo = await this.handleImage(segment, context);
                    if (imageInfo) fileInfos.push(imageInfo);
                } else if (segment.type === 'file') {
                    const fileInfo = await this.handleFile(segment, context);
                    if (fileInfo) fileInfos.push(fileInfo);
                } else if (segment.type === 'video') {
                    const videoInfo = await this.handleVideo(segment, context);
                    if (videoInfo) fileInfos.push(videoInfo);
                } else if (segment.type === 'record') {
                    const recordInfo = await this.handleRecord(segment, context);
                    if (recordInfo) fileInfos.push(recordInfo);
                }
            }

            // 如果处理了文件，发送反馈消息
            if (fileInfos.length > 0) {
                await this.sendFileReport(context, fileInfos);
            }

            return {
                handled: true,
                filesProcessed: fileInfos.length,
                fileInfos: fileInfos
            };

        } catch (error) {
            this.logger.error('Plugin', `文件处理失败: ${error.message}`);
            await this.reply(context, `文件处理失败: ${error.message}`);
            return {
                handled: true,
                error: error.message
            };
        }
    }

    /**
     * 处理图片消息
     */
    async handleImage(segment, context) {
        try {
            const imageData = segment.data;
            const fileInfo = {
                type: 'image',
                file: imageData.file,
                url: imageData.url,
                subType: imageData.sub_type,
                fileSize: imageData.file_size,
                summary: imageData.summary,
                timestamp: new Date().toISOString(),
                sender: context.sender,
                messageId: context.message_id
            };

            this.logger.info('Plugin', `收到图片: ${imageData.file || 'unknown'}`);

            // 如果启用自动下载且有URL
            if (this.downloadConfig.autoDownload && imageData.url) {
                const downloadResult = await this.downloadFile(imageData.url, imageData.file, 'image');
                fileInfo.downloadPath = downloadResult.path;
                fileInfo.downloadSuccess = downloadResult.success;
            }

            // 保存文件信息
            if (this.downloadConfig.saveFileInfo) {
                await this.saveFileInfo(fileInfo);
            }

            return fileInfo;

        } catch (error) {
            this.logger.error('Plugin', `处理图片失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 处理文件消息
     */
    async handleFile(segment, context) {
        try {
            const fileData = segment.data;
            const fileInfo = {
                type: 'file',
                file: fileData.file,
                url: fileData.url,
                name: fileData.name,
                fileSize: fileData.file_size,
                timestamp: new Date().toISOString(),
                sender: context.sender,
                messageId: context.message_id
            };

            this.logger.info('Plugin', `收到文件: ${fileData.name || fileData.file || 'unknown'}`);

            // 检查文件大小
            if (fileData.file_size && parseInt(fileData.file_size) > this.downloadConfig.maxFileSize) {
                this.logger.warning('Plugin', `文件过大，跳过下载: ${fileData.name}`);
                fileInfo.skipReason = 'File too large';
                return fileInfo;
            }

            // 如果启用自动下载且有URL
            if (this.downloadConfig.autoDownload && fileData.url) {
                const downloadResult = await this.downloadFile(fileData.url, fileData.name || fileData.file, 'file');
                fileInfo.downloadPath = downloadResult.path;
                fileInfo.downloadSuccess = downloadResult.success;
            }

            // 保存文件信息
            if (this.downloadConfig.saveFileInfo) {
                await this.saveFileInfo(fileInfo);
            }

            return fileInfo;

        } catch (error) {
            this.logger.error('Plugin', `处理文件失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 处理视频消息
     */
    async handleVideo(segment, context) {
        try {
            const videoData = segment.data;
            const fileInfo = {
                type: 'video',
                file: videoData.file,
                url: videoData.url,
                fileSize: videoData.file_size,
                timestamp: new Date().toISOString(),
                sender: context.sender,
                messageId: context.message_id
            };

            this.logger.info('Plugin', `收到视频: ${videoData.file || 'unknown'}`);

            // 视频文件通常较大，检查大小限制
            if (videoData.file_size && parseInt(videoData.file_size) > this.downloadConfig.maxFileSize) {
                this.logger.warning('Plugin', `视频文件过大，跳过下载: ${videoData.file}`);
                fileInfo.skipReason = 'Video file too large';
                return fileInfo;
            }

            // 如果启用自动下载且有URL
            if (this.downloadConfig.autoDownload && videoData.url) {
                const downloadResult = await this.downloadFile(videoData.url, videoData.file, 'video');
                fileInfo.downloadPath = downloadResult.path;
                fileInfo.downloadSuccess = downloadResult.success;
            }

            // 保存文件信息
            if (this.downloadConfig.saveFileInfo) {
                await this.saveFileInfo(fileInfo);
            }

            return fileInfo;

        } catch (error) {
            this.logger.error('Plugin', `处理视频失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 处理语音消息
     */
    async handleRecord(segment, context) {
        try {
            const recordData = segment.data;
            const fileInfo = {
                type: 'record',
                file: recordData.file,
                url: recordData.url,
                fileSize: recordData.file_size,
                timestamp: new Date().toISOString(),
                sender: context.sender,
                messageId: context.message_id
            };

            this.logger.info('Plugin', `收到语音: ${recordData.file || 'unknown'}`);

            // 如果启用自动下载且有URL
            if (this.downloadConfig.autoDownload && recordData.url) {
                const downloadResult = await this.downloadFile(recordData.url, recordData.file, 'record');
                fileInfo.downloadPath = downloadResult.path;
                fileInfo.downloadSuccess = downloadResult.success;
            }

            // 保存文件信息
            if (this.downloadConfig.saveFileInfo) {
                await this.saveFileInfo(fileInfo);
            }

            return fileInfo;

        } catch (error) {
            this.logger.error('Plugin', `处理语音失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 下载文件
     */
    async downloadFile(url, filename, type) {
        try {
            // 创建类型子目录
            const typeDir = path.join(this.downloadConfig.downloadDir, type);
            if (!fs.existsSync(typeDir)) {
                fs.mkdirSync(typeDir, { recursive: true });
            }

            // 生成安全的文件名
            const safeFilename = this.generateSafeFilename(filename, type);
            const filePath = path.join(typeDir, safeFilename);

            this.logger.info('Plugin', `开始下载文件: ${url} -> ${filePath}`);

            // 下载文件
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'stream',
                timeout: 30000, // 30秒超时
                headers: {
                    'User-Agent': 'OneBot11-FileReceiver/1.0'
                }
            });

            // 写入文件
            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    this.logger.success('Plugin', `文件下载成功: ${filePath}`);
                    resolve({
                        success: true,
                        path: filePath,
                        size: fs.statSync(filePath).size
                    });
                });

                writer.on('error', (error) => {
                    this.logger.error('Plugin', `文件写入失败: ${error.message}`);
                    reject(error);
                });
            });

        } catch (error) {
            this.logger.error('Plugin', `文件下载失败: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 生成安全的文件名
     */
    generateSafeFilename(filename, type) {
        if (!filename) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            return `${type}_${timestamp}`;
        }

        // 移除不安全的字符
        const safeName = filename.replace(/[<>:"/\\|?*]/g, '_');
        
        // 如果文件名太长，截断它
        if (safeName.length > 100) {
            const ext = path.extname(safeName);
            const name = path.basename(safeName, ext);
            return name.substring(0, 100 - ext.length) + ext;
        }

        return safeName;
    }

    /**
     * 保存文件信息到JSON
     */
    async saveFileInfo(fileInfo) {
        try {
            const infoFile = path.join(this.downloadConfig.downloadDir, 'file_info.json');
            let allFileInfos = [];

            // 读取现有的文件信息
            if (fs.existsSync(infoFile)) {
                const content = fs.readFileSync(infoFile, 'utf8');
                allFileInfos = JSON.parse(content);
            }

            // 添加新的文件信息
            allFileInfos.push(fileInfo);

            // 保持最近1000条记录
            if (allFileInfos.length > 1000) {
                allFileInfos = allFileInfos.slice(-1000);
            }

            // 写入文件
            fs.writeFileSync(infoFile, JSON.stringify(allFileInfos, null, 2));

        } catch (error) {
            this.logger.error('Plugin', `保存文件信息失败: ${error.message}`);
        }
    }

    /**
     * 发送文件处理报告
     */
    async sendFileReport(context, fileInfos) {
        try {
            let report = `📁 文件处理报告\n\n`;
            
            for (const fileInfo of fileInfos) {
                const typeEmoji = this.getTypeEmoji(fileInfo.type);
                const sizeText = fileInfo.fileSize ? this.formatFileSize(parseInt(fileInfo.fileSize)) : '未知大小';
                const statusText = fileInfo.downloadSuccess ? '✅ 下载成功' : 
                                 fileInfo.skipReason ? `⏭️ ${fileInfo.skipReason}` : '❌ 下载失败';

                report += `${typeEmoji} ${fileInfo.type.toUpperCase()}\n`;
                report += `📄 文件名: ${fileInfo.name || fileInfo.file || '未知'}\n`;
                report += `📏 大小: ${sizeText}\n`;
                report += `📊 状态: ${statusText}\n`;
                
                if (fileInfo.downloadPath) {
                    report += `💾 路径: ${fileInfo.downloadPath}\n`;
                }
                
                report += `\n`;
            }

            await this.reply(context, report);

        } catch (error) {
            this.logger.error('Plugin', `发送文件报告失败: ${error.message}`);
        }
    }

    /**
     * 获取文件类型对应的emoji
     */
    getTypeEmoji(type) {
        const emojiMap = {
            'image': '🖼️',
            'file': '📄',
            'video': '🎥',
            'record': '🎵'
        };
        return emojiMap[type] || '📎';
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

module.exports = FileReceiverPlugin;
