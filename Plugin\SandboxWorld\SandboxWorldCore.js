/**
 * 沙盒世界核心系统
 * 管理虚拟世界的所有核心功能
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class SandboxWorldCore extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            worldName: config.worldName || 'DefaultWorld',
            timeSpeed: config.timeSpeed || 1, // 时间流速倍数
            maxAgents: config.maxAgents || 50,
            autoSave: config.autoSave !== false,
            saveInterval: config.saveInterval || 300000, // 5分钟自动保存
            dataDir: config.dataDir || path.join(__dirname, 'data'),
            ...config
        };

        // 核心系统组件
        this.worldEnvironment = null;
        this.agentEcosystem = null;
        this.socialNetwork = null;
        this.autonomousDialogue = null;
        this.eventSystem = null;
        
        // 世界状态
        this.worldState = {
            isRunning: false,
            isPaused: false,
            worldTime: new Date(),
            realStartTime: null,
            totalRunTime: 0,
            generation: 0, // 世界代数
            population: 0,
            events: [],
            statistics: {
                totalInteractions: 0,
                totalEvents: 0,
                averageHappiness: 0,
                socialCohesion: 0
            }
        };

        // 数据存储
        this.dataPath = {
            world: path.join(this.config.dataDir, 'world.json'),
            agents: path.join(this.config.dataDir, 'agents'),
            relationships: path.join(this.config.dataDir, 'relationships.json'),
            events: path.join(this.config.dataDir, 'events'),
            statistics: path.join(this.config.dataDir, 'statistics.json')
        };

        this.logger = console; // 可以替换为更高级的日志系统
        
        this.init();
    }

    /**
     * 初始化沙盒世界
     */
    async init() {
        try {
            this.logger.info('🌍 初始化沙盒世界核心系统...');
            
            // 确保数据目录存在
            await this.ensureDirectories();
            
            // 加载或创建世界数据
            await this.loadWorldData();
            
            // 初始化子系统
            await this.initializeSubsystems();
            
            // 设置自动保存
            if (this.config.autoSave) {
                this.setupAutoSave();
            }
            
            this.logger.info('✅ 沙盒世界核心系统初始化完成');
            this.emit('initialized');
            
        } catch (error) {
            this.logger.error('❌ 沙盒世界初始化失败:', error);
            throw error;
        }
    }

    /**
     * 确保必要的目录存在
     */
    async ensureDirectories() {
        const dirs = [
            this.config.dataDir,
            this.dataPath.agents,
            this.dataPath.events
        ];

        for (const dir of dirs) {
            try {
                await fs.access(dir);
            } catch {
                await fs.mkdir(dir, { recursive: true });
                this.logger.info(`📁 创建目录: ${dir}`);
            }
        }
    }

    /**
     * 加载世界数据
     */
    async loadWorldData() {
        try {
            const worldData = await fs.readFile(this.dataPath.world, 'utf-8');
            const savedState = JSON.parse(worldData);
            
            // 合并保存的状态
            this.worldState = {
                ...this.worldState,
                ...savedState,
                isRunning: false, // 重启时总是停止状态
                isPaused: false,
                realStartTime: null
            };
            
            this.logger.info('📖 加载世界数据成功');
            
        } catch (error) {
            this.logger.info('🆕 创建新的世界数据');
            await this.saveWorldData();
        }
    }

    /**
     * 保存世界数据
     */
    async saveWorldData() {
        try {
            const dataToSave = {
                ...this.worldState,
                lastSaved: new Date().toISOString()
            };
            
            await fs.writeFile(this.dataPath.world, JSON.stringify(dataToSave, null, 2));
            this.emit('worldSaved');
            
        } catch (error) {
            this.logger.error('❌ 保存世界数据失败:', error);
            throw error;
        }
    }

    /**
     * 初始化子系统
     */
    async initializeSubsystems() {
        try {
            const { WorldEnvironment } = require('./WorldEnvironment');
            const { AgentEcosystem } = require('./AgentEcosystem');
            const { SocialNetwork } = require('./SocialNetwork');
            const { AutonomousDialogue } = require('./AutonomousDialogue');
            const { EventSystem } = require('./EventSystem');

            // 初始化环境系统
            this.worldEnvironment = new WorldEnvironment(this);
            await this.worldEnvironment.init();

            // 初始化Agent生态系统
            this.agentEcosystem = new AgentEcosystem(this);
            await this.agentEcosystem.init();

            // 初始化社交网络
            this.socialNetwork = new SocialNetwork(this);
            await this.socialNetwork.init();

            // 初始化自主对话系统
            this.autonomousDialogue = new AutonomousDialogue(this);
            await this.autonomousDialogue.init();

            // 初始化事件系统
            this.eventSystem = new EventSystem(this);
            await this.eventSystem.init();

            this.logger.info('🔧 所有子系统初始化完成');
        } catch (error) {
            this.logger.error('❌ 子系统初始化失败:', error);
            throw error;
        }
    }

    /**
     * 设置自动保存
     */
    setupAutoSave() {
        setInterval(async () => {
            if (this.worldState.isRunning) {
                await this.saveWorldData();
                this.logger.info('💾 自动保存完成');
            }
        }, this.config.saveInterval);
    }

    /**
     * 启动世界
     */
    async startWorld() {
        if (this.worldState.isRunning) {
            this.logger.warn('⚠️ 世界已经在运行中');
            return;
        }

        this.worldState.isRunning = true;
        this.worldState.isPaused = false;
        this.worldState.realStartTime = new Date();
        
        // 启动所有子系统
        await this.worldEnvironment.start();
        await this.agentEcosystem.start();
        await this.socialNetwork.start();
        await this.autonomousDialogue.start();
        await this.eventSystem.start();

        this.logger.info('🚀 沙盒世界已启动');
        this.emit('worldStarted');
    }

    /**
     * 暂停世界
     */
    async pauseWorld() {
        if (!this.worldState.isRunning || this.worldState.isPaused) {
            return;
        }

        this.worldState.isPaused = true;
        
        // 暂停所有子系统
        await this.worldEnvironment.pause();
        await this.agentEcosystem.pause();
        await this.socialNetwork.pause();
        await this.autonomousDialogue.pause();
        await this.eventSystem.pause();

        this.logger.info('⏸️ 沙盒世界已暂停');
        this.emit('worldPaused');
    }

    /**
     * 恢复世界
     */
    async resumeWorld() {
        if (!this.worldState.isRunning || !this.worldState.isPaused) {
            return;
        }

        this.worldState.isPaused = false;
        
        // 恢复所有子系统
        await this.worldEnvironment.resume();
        await this.agentEcosystem.resume();
        await this.socialNetwork.resume();
        await this.autonomousDialogue.resume();
        await this.eventSystem.resume();

        this.logger.info('▶️ 沙盒世界已恢复');
        this.emit('worldResumed');
    }

    /**
     * 停止世界
     */
    async stopWorld() {
        if (!this.worldState.isRunning) {
            return;
        }

        this.worldState.isRunning = false;
        this.worldState.isPaused = false;
        
        // 计算总运行时间
        if (this.worldState.realStartTime) {
            this.worldState.totalRunTime += Date.now() - this.worldState.realStartTime.getTime();
            this.worldState.realStartTime = null;
        }

        // 停止所有子系统
        await this.worldEnvironment.stop();
        await this.agentEcosystem.stop();
        await this.socialNetwork.stop();
        await this.autonomousDialogue.stop();
        await this.eventSystem.stop();

        // 保存数据
        await this.saveWorldData();

        this.logger.info('🛑 沙盒世界已停止');
        this.emit('worldStopped');
    }

    /**
     * 获取世界状态
     */
    getWorldState() {
        return {
            ...this.worldState,
            currentTime: this.getCurrentWorldTime(),
            uptime: this.getUptime()
        };
    }

    /**
     * 获取当前世界时间
     */
    getCurrentWorldTime() {
        if (!this.worldState.isRunning || this.worldState.isPaused) {
            return this.worldState.worldTime;
        }

        const realElapsed = Date.now() - this.worldState.realStartTime.getTime();
        const worldElapsed = realElapsed * this.config.timeSpeed;
        
        return new Date(this.worldState.worldTime.getTime() + worldElapsed);
    }

    /**
     * 获取运行时间
     */
    getUptime() {
        let uptime = this.worldState.totalRunTime;

        if (this.worldState.isRunning && this.worldState.realStartTime) {
            uptime += Date.now() - this.worldState.realStartTime.getTime();
        }

        return uptime;
    }

    /**
     * 设置时间流速
     */
    setTimeSpeed(speed) {
        if (speed <= 0) {
            throw new Error('时间流速必须大于0');
        }

        // 更新世界时间
        if (this.worldState.isRunning && !this.worldState.isPaused) {
            this.worldState.worldTime = this.getCurrentWorldTime();
            this.worldState.realStartTime = new Date();
        }

        this.config.timeSpeed = speed;
        this.emit('timeSpeedChanged', speed);
        this.logger.info(`⏰ 时间流速设置为: ${speed}x`);
    }

    /**
     * 添加Agent到世界
     */
    async addAgent(agentConfig) {
        if (this.worldState.population >= this.config.maxAgents) {
            throw new Error('世界人口已达上限');
        }

        const agent = await this.agentEcosystem.createAgent(agentConfig);
        this.worldState.population++;

        this.emit('agentAdded', agent);
        this.logger.info(`👤 新Agent加入世界: ${agent.name}`);

        return agent;
    }

    /**
     * 从世界移除Agent
     */
    async removeAgent(agentId) {
        await this.agentEcosystem.removeAgent(agentId);
        this.worldState.population--;

        this.emit('agentRemoved', agentId);
        this.logger.info(`👤 Agent离开世界: ${agentId}`);
    }

    /**
     * 触发事件
     */
    async triggerEvent(eventType, eventData = {}) {
        return await this.eventSystem.triggerEvent(eventType, eventData);
    }

    /**
     * 获取统计信息
     */
    async getStatistics() {
        return {
            ...this.worldState.statistics,
            population: this.worldState.population,
            uptime: this.getUptime(),
            generation: this.worldState.generation,
            agentStats: await this.agentEcosystem.getStatistics(),
            socialStats: await this.socialNetwork.getStatistics(),
            environmentStats: await this.worldEnvironment.getStatistics()
        };
    }

    /**
     * 销毁世界
     */
    async destroy() {
        await this.stopWorld();

        // 清理所有子系统
        if (this.worldEnvironment) await this.worldEnvironment.destroy();
        if (this.agentEcosystem) await this.agentEcosystem.destroy();
        if (this.socialNetwork) await this.socialNetwork.destroy();
        if (this.autonomousDialogue) await this.autonomousDialogue.destroy();
        if (this.eventSystem) await this.eventSystem.destroy();

        this.removeAllListeners();
        this.logger.info('🗑️ 沙盒世界已销毁');
    }
}

module.exports = { SandboxWorldCore };
