/**
 * 配置热更新管理器
 * 支持全局配置和插件配置的实时热更新，无需重启服务器
 */

const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const EventEmitter = require('events');

/**
 * 获取北京时间戳 (不带Z后缀，避免前端解析为UTC时间)
 */
function getBeijingTimestamp() {
    const now = new Date();
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    // 移除Z后缀，避免前端误解析为UTC时间
    return beijingTime.toISOString().replace('Z', '+08:00');
}

/**
 * 获取北京时间字符串 (YYYY-MM-DD HH:mm:ss)
 */
function getBeijingTimeString() {
    const now = new Date();
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    return beijingTime.toISOString().replace('T', ' ').substr(0, 19);
}

class ConfigHotReloadManager extends EventEmitter {
    constructor(logger) {
        super();
        this.logger = logger;
        this.watchers = new Map(); // 文件监听器
        this.configs = new Map(); // 配置缓存
        this.callbacks = new Map(); // 配置更新回调
        this.updateLogs = []; // 更新日志
        this.maxLogEntries = 100; // 最大日志条数
        this.isInitialized = false;

        // 配置文件路径
        this.globalConfigPath = path.join(process.cwd(), 'config.env');
        this.pluginDir = path.join(process.cwd(), 'Plugin');
    }

    /**
     * 初始化热更新系统
     */
    async initialize() {
        try {
            this.logger.info('配置热更新', '开始初始化配置热更新系统...');
            
            // 监听全局配置文件
            await this.watchGlobalConfig();
            
            // 监听所有插件配置文件
            await this.watchPluginConfigs();
            
            this.isInitialized = true;
            this.logger.success('配置热更新', '配置热更新系统初始化完成');
            
            return true;
        } catch (error) {
            this.logger.error('配置热更新', '初始化失败:', error.message);
            return false;
        }
    }

    /**
     * 监听全局配置文件
     */
    async watchGlobalConfig() {
        if (fs.existsSync(this.globalConfigPath)) {
            const watcher = chokidar.watch(this.globalConfigPath, {
                persistent: true,
                ignoreInitial: false
            });

            watcher.on('change', () => {
                this.handleConfigChange('global', this.globalConfigPath);
            });

            watcher.on('add', () => {
                this.handleConfigChange('global', this.globalConfigPath);
            });

            this.watchers.set('global', watcher);
            this.logger.info('配置热更新', '已开始监听全局配置文件: config.env');
        } else {
            this.logger.warning('配置热更新', '全局配置文件不存在: config.env');
        }
    }

    /**
     * 监听所有插件配置文件
     */
    async watchPluginConfigs() {
        if (!fs.existsSync(this.pluginDir)) {
            this.logger.warning('配置热更新', '插件目录不存在');
            return;
        }

        const pluginDirs = fs.readdirSync(this.pluginDir, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

        for (const pluginName of pluginDirs) {
            const pluginPath = path.join(this.pluginDir, pluginName);
            const configFiles = ['config.env', 'config.json'];
            
            for (const configFile of configFiles) {
                const configPath = path.join(pluginPath, configFile);
                
                if (fs.existsSync(configPath)) {
                    const watcher = chokidar.watch(configPath, {
                        persistent: true,
                        ignoreInitial: false
                    });

                    watcher.on('change', () => {
                        this.handleConfigChange(pluginName, configPath);
                    });

                    watcher.on('add', () => {
                        this.handleConfigChange(pluginName, configPath);
                    });

                    const watcherKey = `${pluginName}_${configFile}`;
                    this.watchers.set(watcherKey, watcher);
                    
                    this.logger.info('配置热更新', `已开始监听插件配置: ${pluginName}/${configFile}`);
                }
            }
        }
    }

    /**
     * 处理配置文件变更
     */
    async handleConfigChange(configKey, configPath) {
        try {
            // 防抖处理，避免频繁触发
            if (this.debounceTimers && this.debounceTimers[configKey]) {
                clearTimeout(this.debounceTimers[configKey]);
            }
            
            if (!this.debounceTimers) this.debounceTimers = {};
            
            this.debounceTimers[configKey] = setTimeout(async () => {
                await this.reloadConfig(configKey, configPath);
            }, 500); // 500ms防抖
            
        } catch (error) {
            this.logger.error('配置热更新', `处理配置变更失败 ${configKey}:`, error.message);
        }
    }

    /**
     * 重新加载配置
     */
    async reloadConfig(configKey, configPath) {
        try {
            this.logger.info('配置热更新', `检测到配置文件变更: ${configKey}`);
            
            // 读取新配置
            const newConfig = await this.loadConfigFile(configPath);
            
            // 比较配置是否真的有变化
            const oldConfig = this.configs.get(configKey) || {};
            const changeResult = this.hasConfigChanges(oldConfig, newConfig);

            if (!changeResult.hasChanges) {
                this.logger.debug('配置热更新', `配置内容无变化: ${configKey}`);
                // 记录无变化的日志
                this.addUpdateLog(configKey, 'file_change', {
                    success: true,
                    message: '配置文件已检查，内容无变化',
                    configPath,
                    hasChanges: false,
                    changeCount: 0
                });
                return;
            }

            // 更新配置缓存
            this.configs.set(configKey, newConfig);

            // 触发配置更新事件
            this.emit('configChanged', {
                configKey,
                configPath,
                oldConfig,
                newConfig,
                timestamp: getBeijingTimestamp(),
                beijingTime: getBeijingTimeString()
            });

            // 执行注册的回调函数
            let callbackResult = null;
            const callback = this.callbacks.get(configKey);
            if (callback && typeof callback === 'function') {
                try {
                    callbackResult = await callback(newConfig, oldConfig);
                } catch (callbackError) {
                    this.logger.error('配置热更新', `回调执行失败 ${configKey}:`, callbackError.message);
                    callbackResult = { error: callbackError.message };
                }
            }

            this.logger.success('配置热更新', `配置已更新: ${configKey}`);

            // 记录成功的更新日志
            this.addUpdateLog(configKey, 'file_change', {
                success: true,
                message: `配置文件已更新 (${changeResult.changeCount}项变更)`,
                configPath,
                hasChanges: true,
                changeCount: changeResult.changeCount,
                configChanges: changeResult.changes,
                hasCallback: !!callback,
                callbackResult,
                oldConfig,
                newConfig
            });
            
        } catch (error) {
            this.logger.error('配置热更新', `重新加载配置失败 ${configKey}:`, error.message);

            // 记录失败的更新日志
            this.addUpdateLog(configKey, 'file_change', {
                success: false,
                message: `配置重新加载失败: ${error.message}`,
                configPath,
                error: error.message
            });
        }
    }

    /**
     * 加载配置文件
     */
    async loadConfigFile(configPath) {
        const ext = path.extname(configPath).toLowerCase();
        
        if (ext === '.env') {
            return this.parseEnvFile(configPath);
        } else if (ext === '.json') {
            return this.parseJsonFile(configPath);
        } else {
            throw new Error(`不支持的配置文件格式: ${ext}`);
        }
    }

    /**
     * 解析.env文件
     */
    parseEnvFile(configPath) {
        const content = fs.readFileSync(configPath, 'utf8');
        const config = {};
        
        content.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const equalIndex = line.indexOf('=');
                if (equalIndex > 0) {
                    const key = line.substring(0, equalIndex).trim();
                    const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                    
                    // 处理布尔值和数字
                    if (value.toLowerCase() === 'true') {
                        config[key] = true;
                    } else if (value.toLowerCase() === 'false') {
                        config[key] = false;
                    } else if (!isNaN(value) && value !== '') {
                        config[key] = parseFloat(value);
                    } else {
                        config[key] = value;
                    }
                }
            }
        });
        
        return config;
    }

    /**
     * 解析JSON文件
     */
    parseJsonFile(configPath) {
        const content = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(content);
    }

    /**
     * 检查配置是否有变化并返回详细的变更信息
     */
    hasConfigChanges(oldConfig, newConfig) {
        const changes = [];
        const allKeys = new Set([...Object.keys(oldConfig), ...Object.keys(newConfig)]);

        for (const key of allKeys) {
            const oldValue = oldConfig[key];
            const newValue = newConfig[key];

            if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
                changes.push({
                    key,
                    oldValue: oldValue !== undefined ? String(oldValue) : undefined,
                    newValue: newValue !== undefined ? String(newValue) : undefined,
                    type: oldValue === undefined ? 'added' :
                          newValue === undefined ? 'removed' : 'modified'
                });
            }
        }

        return {
            hasChanges: changes.length > 0,
            changes: changes,
            changeCount: changes.length
        };
    }

    /**
     * 注册配置更新回调
     */
    registerCallback(configKey, callback) {
        if (typeof callback !== 'function') {
            throw new Error('回调必须是一个函数');
        }
        
        this.callbacks.set(configKey, callback);
        this.logger.info('配置热更新', `已注册配置更新回调: ${configKey}`);
    }

    /**
     * 取消注册回调
     */
    unregisterCallback(configKey) {
        this.callbacks.delete(configKey);
        this.logger.info('配置热更新', `已取消注册配置更新回调: ${configKey}`);
    }

    /**
     * 手动触发配置重新加载
     */
    async manualReload(configKey) {
        let watcher = this.watchers.get(configKey);
        let actualConfigKey = configKey;

        // 如果直接找不到，尝试匹配插件名
        if (!watcher && configKey !== 'global') {
            // 查找以插件名开头的watcher键
            for (const [watcherKey, watcherInstance] of this.watchers) {
                if (watcherKey.startsWith(configKey + '_')) {
                    watcher = watcherInstance;
                    actualConfigKey = watcherKey;
                    break;
                }
            }
        }

        if (!watcher) {
            throw new Error(`未找到配置监听器: ${configKey}`);
        }

        // 获取配置文件路径
        let configPath;
        if (configKey === 'global') {
            configPath = this.globalConfigPath;
        } else {
            // 从监听器中获取路径
            const watchedPaths = watcher.getWatched();

            // watchedPaths格式: { '/path/to/dir': ['file1.env', 'file2.json'] }
            // 我们需要找到正确的文件路径
            for (const [dirPath, files] of Object.entries(watchedPaths)) {
                for (const file of files) {
                    const fullPath = path.join(dirPath, file);
                    // 检查文件名是否匹配actualConfigKey
                    if (actualConfigKey.includes(file.replace(/\.[^.]+$/, ''))) {
                        configPath = fullPath;
                        break;
                    }
                }
                if (configPath) break;
            }

            if (!configPath) {
                throw new Error(`无法找到配置文件路径: ${actualConfigKey}`);
            }
        }

        // 记录手动重载开始的日志
        this.addUpdateLog(configKey, 'manual_reload', {
            success: true,
            message: '开始手动重新加载配置',
            configPath,
            actualConfigKey
        });

        // 使用原始的configKey进行重载，保持configs键的一致性
        try {
            await this.reloadConfig(configKey, configPath);

            // 记录手动重载成功的日志
            this.addUpdateLog(configKey, 'manual_reload', {
                success: true,
                message: '手动重新加载配置成功',
                configPath,
                actualConfigKey
            });
        } catch (error) {
            // 记录手动重载失败的日志
            this.addUpdateLog(configKey, 'manual_reload', {
                success: false,
                message: `手动重新加载配置失败: ${error.message}`,
                configPath,
                actualConfigKey,
                error: error.message
            });
            throw error; // 重新抛出错误
        }
    }

    /**
     * 获取当前配置
     */
    getConfig(configKey) {
        return this.configs.get(configKey) || {};
    }

    /**
     * 获取所有配置
     */
    getAllConfigs() {
        const allConfigs = {};
        for (const [key, config] of this.configs) {
            // 查找对应的watcher信息
            let watcherKey = key;
            let hasWatcher = this.watchers.has(key);

            // 如果直接找不到watcher，尝试匹配插件名
            if (!hasWatcher && key !== 'global') {
                for (const [wKey] of this.watchers) {
                    if (wKey.startsWith(key + '_')) {
                        watcherKey = wKey;
                        hasWatcher = true;
                        break;
                    }
                }
            }

            allConfigs[key] = {
                config: config,
                watcherKey: watcherKey,
                hasWatcher: hasWatcher,
                hasCallback: this.callbacks.has(key)
            };
        }
        return allConfigs;
    }

    /**
     * 停止所有监听器
     */
    async stop() {
        this.logger.info('配置热更新', '正在停止配置热更新系统...');
        
        for (const [key, watcher] of this.watchers) {
            await watcher.close();
            this.logger.debug('配置热更新', `已停止监听: ${key}`);
        }
        
        this.watchers.clear();
        this.callbacks.clear();
        this.configs.clear();
        this.isInitialized = false;
        
        this.logger.info('配置热更新', '配置热更新系统已停止');
    }

    /**
     * 获取监听状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            watchersCount: this.watchers.size,
            callbacksCount: this.callbacks.size,
            configsCount: this.configs.size,
            watchedConfigs: Array.from(this.watchers.keys()),
            updateLogsCount: this.updateLogs.length
        };
    }

    /**
     * 添加更新日志
     */
    addUpdateLog(configKey, action, details = {}) {
        const logEntry = {
            id: Date.now() + Math.random().toString(36).substr(2, 9),
            configKey,
            action, // 'reload', 'manual_reload', 'file_change'
            timestamp: getBeijingTimestamp(),
            beijingTime: getBeijingTimeString(),
            details,
            success: details.success !== false
        };

        this.updateLogs.unshift(logEntry); // 添加到开头，最新的在前面

        // 限制日志条数
        if (this.updateLogs.length > this.maxLogEntries) {
            this.updateLogs = this.updateLogs.slice(0, this.maxLogEntries);
        }

        this.logger.info('配置热更新', `记录更新日志: ${configKey} - ${action}`);
    }

    /**
     * 获取更新日志
     */
    getUpdateLogs(limit = 50) {
        return this.updateLogs.slice(0, limit);
    }

    /**
     * 清空更新日志
     */
    clearUpdateLogs() {
        this.updateLogs = [];
        this.logger.info('配置热更新', '更新日志已清空');
    }
}

module.exports = ConfigHotReloadManager;
