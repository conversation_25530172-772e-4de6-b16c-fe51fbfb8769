/**
 * 对话历史管理器 - 负责存储和检索完整的用户-AI对话历史
 * 优化版：支持智能对话合并、本地时间戳、群聊私聊统一处理
 */

class ConversationHistoryManager {
    constructor(config, embeddingService, db, logger) {
        this.config = config;
        this.embeddingService = embeddingService;
        this.db = db;
        this.logger = logger;
        this.isInitialized = false;
        
        // 对话合并配置
        this.mergeConfig = {
            timeWindowMinutes: 30,        // 30分钟内的对话可以合并
            maxMessageCount: 10,          // 单个会话最多合并10轮对话
            similarityThreshold: 0.7,     // 相似度阈值
            maxContentLength: 8000,       // 合并后内容最大长度
        };
    }

    /**
     * 初始化服务
     */
    async initialize() {
        try {
            if (!this.embeddingService || !this.embeddingService.isInitialized) {
                throw new Error('嵌入服务未初始化');
            }
            
            this.embeddingService.setDatabase(this.db);
            
            this.isInitialized = true;
            this.logger.success('对话历史管理器', '✅ 智能对话历史管理器初始化成功');
            return { success: true };
            
        } catch (error) {
            this.logger.error('对话历史管理器', '初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取本地时间戳 (YYYY-MM-DD HH:mm:ss 格式)
     */
    getLocalTimestamp() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    /**
     * 获取本地日期 (YYYY-MM-DD 格式)
     */
    getLocalDate() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    }

    /**
     * 计算内容的简单哈希值，用于检测相似对话
     */
    calculateContentHash(content) {
        let hash = 0;
        if (!content || content.length === 0) return hash.toString();
        
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return Math.abs(hash).toString();
    }

    /**
     * 检查两个时间戳是否在指定分钟内
     */
    isWithinTimeWindow(timestamp1, timestamp2, windowMinutes = 30) {
        const date1 = new Date(timestamp1);
        const date2 = new Date(timestamp2);
        const diffMs = Math.abs(date2.getTime() - date1.getTime());
        const diffMinutes = diffMs / (1000 * 60);
        
        return diffMinutes <= windowMinutes;
    }

    /**
     * 生成会话ID
     */
    generateSessionId(userId, personaName, chatType = 'private', chatName = '') {
        const date = this.getLocalDate().replace(/-/g, '');
        const baseId = `${userId}_${personaName}_${date}`;
        
        if (chatType === 'group' && chatName) {
            return `${baseId}_group_${this.calculateContentHash(chatName).substring(0, 6)}`;
        }
        
        return `${baseId}_private`;
    }

    /**
     * 查找或创建对话会话
     */
    async findOrCreateSession(userId, personaName, userMessage, assistantMessage, options = {}) {
        try {
            const { chatType = 'private', chatName = '', emotionState = null } = options;
            const currentTime = this.getLocalTimestamp();
            const contentHash = this.calculateContentHash(userMessage + assistantMessage);
            
            // 查找最近的会话
            const recentSessions = await this.dbAll(`
                SELECT * FROM conversation_sessions 
                WHERE user_id = ? AND persona_name = ? AND chat_type = ?
                    AND (chat_name = ? OR chat_name IS NULL)
                ORDER BY last_update_time DESC 
                LIMIT 3
            `, [userId, personaName, chatType, chatName || null]);

            // 检查是否可以合并到现有会话
            for (const session of recentSessions) {
                const canMerge = this.canMergeToSession(session, {
                    userMessage,
                    assistantMessage,
                    currentTime,
                    contentHash,
                    emotionState
                });

                if (canMerge) {
                    return await this.mergeToExistingSession(session, {
                        userMessage,
                        assistantMessage,
                        currentTime,
                        emotionState
                    });
                }
            }

            // 创建新会话
            return await this.createNewSession(userId, personaName, userMessage, assistantMessage, {
                chatType,
                chatName,
                currentTime,
                contentHash,
                emotionState
            });

        } catch (error) {
            this.logger.error('对话历史管理器', '查找或创建会话失败:', error.message);
            throw error;
        }
    }

    /**
     * 检查是否可以合并到现有会话
     */
    canMergeToSession(session, { userMessage, assistantMessage, currentTime, contentHash, emotionState }) {
        // 1. 检查时间窗口
        const timeOk = this.isWithinTimeWindow(session.last_update_time, currentTime, this.mergeConfig.timeWindowMinutes);
        if (!timeOk) return false;

        // 2. 检查消息数量限制
        const countOk = session.message_count < this.mergeConfig.maxMessageCount;
        if (!countOk) return false;

        // 3. 检查合并后内容长度
        const currentLength = (session.user_content || '').length + (session.assistant_content || '').length;
        const newLength = userMessage.length + assistantMessage.length;
        const lengthOk = (currentLength + newLength) < this.mergeConfig.maxContentLength;
        if (!lengthOk) return false;

        // 4. 检查内容相似度（避免完全重复的对话）
        const contextSimilar = session.context_hash !== contentHash;

        return timeOk && countOk && lengthOk && contextSimilar;
    }

    /**
     * 合并到现有会话
     */
    async mergeToExistingSession(session, { userMessage, assistantMessage, currentTime, emotionState }) {
        try {
            const separator = '\n---\n';
            const newUserContent = session.user_content + separator + userMessage;
            const newAssistantContent = session.assistant_content + separator + assistantMessage;
            const newMessageCount = session.message_count + 1;

            // 更新情感状态汇总
            let emotionSummary = {};
            try {
                emotionSummary = JSON.parse(session.emotion_summary || '{}');
            } catch (e) {
                emotionSummary = {};
            }

            if (emotionState) {
                emotionSummary.latest = emotionState;
                emotionSummary.count = (emotionSummary.count || 0) + 1;
            }

            // 更新会话
            await this.dbRun(`
                UPDATE conversation_sessions 
                SET user_content = ?, assistant_content = ?, message_count = ?,
                    last_update_time = ?, emotion_summary = ?, importance_score = ?
                WHERE session_id = ?
            `, [
                newUserContent,
                newAssistantContent,
                newMessageCount,
                currentTime,
                JSON.stringify(emotionSummary),
                this.calculateImportanceScore(newUserContent + newAssistantContent, emotionState),
                session.session_id
            ]);

            this.logger.info('对话历史管理器', 
                `成功合并对话到会话 ${session.session_id}，消息数: ${newMessageCount}`
            );

            return {
                success: true,
                session_id: session.session_id,
                action: 'merged',
                message_count: newMessageCount
            };

        } catch (error) {
            this.logger.error('对话历史管理器', '合并会话失败:', error.message);
            throw error;
        }
    }

    /**
     * 创建新会话
     */
    async createNewSession(userId, personaName, userMessage, assistantMessage, options) {
        try {
            const { chatType, chatName, currentTime, contentHash, emotionState } = options;
            
            // 生成新的会话ID
            let sessionId = this.generateSessionId(userId, personaName, chatType, chatName);
            
            // 检查会话ID是否已存在，如果存在则添加序号
            let counter = 1;
            let originalSessionId = sessionId;
            while (await this.sessionExists(sessionId)) {
                sessionId = `${originalSessionId}_${counter}`;
                counter++;
            }

            const emotionSummary = emotionState ? { latest: emotionState, count: 1 } : {};
            const importanceScore = this.calculateImportanceScore(userMessage + assistantMessage, emotionState);

            // 创建新会话
            await this.dbRun(`
                INSERT INTO conversation_sessions (
                    session_id, user_id, persona_name, chat_type, chat_name,
                    user_content, assistant_content, message_count,
                    first_message_time, last_update_time, importance_score,
                    emotion_summary, context_hash
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                sessionId, userId, personaName, chatType, chatName,
                userMessage, assistantMessage, 1,
                currentTime, currentTime, importanceScore,
                JSON.stringify(emotionSummary), contentHash
            ]);

            this.logger.success('对话历史管理器', 
                `创建新会话 ${sessionId}，用户: ${userId}，助手: ${personaName}`
            );

            return {
                success: true,
                session_id: sessionId,
                action: 'created',
                message_count: 1
            };

        } catch (error) {
            this.logger.error('对话历史管理器', '创建新会话失败:', error.message);
            throw error;
        }
    }

    /**
     * 检查会话是否存在
     */
    async sessionExists(sessionId) {
        try {
            const result = await this.dbGet(`
                SELECT COUNT(*) as count FROM conversation_sessions WHERE session_id = ?
            `, [sessionId]);
            return result && result.count > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * 存储详细对话消息（保留原有接口，但优化逻辑）
     */
    async storeMessage(messageData) {
        try {
            const {
                userId,
                personaName,
                conversationId,
                speaker,
                content,
                embeddingVector,
                emotionState = null,
                timestamp = null,
                importanceScore = null,
                chatType = 'private',
                chatName = '',
                sessionId = null
            } = messageData;

            const finalImportanceScore = importanceScore !== null ?
                importanceScore : this.calculateImportanceScore(content, emotionState);

            const localTimestamp = timestamp || this.getLocalTimestamp();
            const finalSessionId = sessionId || this.generateSessionId(userId, personaName, chatType, chatName);

            // 存储到详细历史表
            const result = await this.dbRun(`
                INSERT INTO conversation_history (
                    session_id, user_id, persona_name, conversation_id, speaker, content,
                    embedding_vector, emotion_state, timestamp, tokens, 
                    importance_score, chat_type, chat_name, is_merged
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                finalSessionId, userId, personaName, conversationId, speaker, content,
                JSON.stringify(embeddingVector), JSON.stringify(emotionState),
                localTimestamp, this.calculateTokens(content), finalImportanceScore,
                chatType, chatName, 0
            ]);

            this.logger.info('对话历史管理器',
                `已存储${speaker}消息: 用户${userId}, 会话${finalSessionId}, 重要性${finalImportanceScore.toFixed(3)}`
            );

            return {
                success: true,
                message_id: result.lastID,
                importance_score: finalImportanceScore,
                session_id: finalSessionId,
                timestamp: localTimestamp
            };

        } catch (error) {
            this.logger.error('对话历史管理器', '存储消息失败:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取最近的对话会话（优化版：基于会话表）
     */
    async getRecentSessions(userId, personaName, limit = 5, chatType = null) {
        try {
            let query = `
                SELECT * FROM conversation_sessions 
                WHERE user_id = ? AND persona_name = ?
            `;
            let params = [userId, personaName];

            if (chatType) {
                query += ` AND chat_type = ?`;
                params.push(chatType);
            }

            query += ` ORDER BY last_update_time DESC LIMIT ?`;
            params.push(limit);

            const sessions = await this.dbAll(query, params);

            this.logger.debug('对话历史管理器', 
                `获取最近会话: 用户${userId}, 助手${personaName}, 会话数${sessions.length}`
            );

            return sessions.map(session => ({
                ...session,
                emotion_summary: this.safeJsonParse(session.emotion_summary, {}),
                topic_tags: this.safeJsonParse(session.topic_tags, [])
            }));

        } catch (error) {
            this.logger.error('对话历史管理器', '获取最近会话失败:', error.message);
            return [];
        }
    }

    /**
     * 获取最近的对话历史（兼容旧接口，基于新的会话表）
     */
    async getRecentHistory(userId, personaName, limit = 5) {
        try {
            const sessions = await this.getRecentSessions(userId, personaName, limit);

            // 转换为旧格式以保持兼容性
            const history = [];
            for (const session of sessions) {
                // 将合并的对话内容拆分回用户和助手消息
                const userMessages = session.user_content.split('\n---\n');
                const assistantMessages = session.assistant_content.split('\n---\n');

                for (let i = 0; i < Math.min(userMessages.length, assistantMessages.length); i++) {
                    history.push({
                        id: `${session.session_id}_user_${i}`,
                        speaker: 'user',
                        content: userMessages[i],
                        emotion_state: session.emotion_summary?.latest || {},
                        timestamp: session.last_update_time,
                        importance_score: session.importance_score,
                        conversation_id: session.session_id,
                        session_id: session.session_id
                    });

                    history.push({
                        id: `${session.session_id}_assistant_${i}`,
                        speaker: 'assistant',
                        content: assistantMessages[i],
                        emotion_state: session.emotion_summary?.latest || {},
                        timestamp: session.last_update_time,
                        importance_score: session.importance_score,
                        conversation_id: session.session_id,
                        session_id: session.session_id
                    });
                }
            }

            this.logger.debug('对话历史管理器',
                `转换历史格式完成: ${sessions.length}个会话 -> ${history.length}条消息`
            );

            return history.slice(0, limit * 2); // 限制返回数量

        } catch (error) {
            this.logger.error('对话历史管理器', '获取历史记录失败:', error.message);
            return [];
        }
    }

    /**
     * 获取上下文相关的对话历史（优化版）
     */
    async getContextualHistory(userId, personaName, currentMessage, options = {}) {
        try {
            const { maxResults = 8, similarityThreshold = 0.75, chatType = null } = options;

            let allContext = [];

            // 1. 基于会话的相关历史
            const sessions = await this.getRecentSessions(userId, personaName, Math.ceil(maxResults / 2), chatType);
            
            for (const session of sessions) {
                // 计算与当前消息的相关性
                const relevanceScore = this.calculateRelevanceScore(currentMessage, session);
                
                if (relevanceScore >= similarityThreshold) {
                    allContext.push({
                        session_id: session.session_id,
                        user_content: session.user_content,
                        assistant_content: session.assistant_content,
                        context_type: 'session_similarity',
                        relevance_score: relevanceScore,
                        message_count: session.message_count,
                        last_update_time: session.last_update_time,
                        emotion_summary: session.emotion_summary,
                        importance_score: session.importance_score
                    });
                }
            }

            // 2. 基于嵌入向量的相似度搜索（如果嵌入服务可用）
            if (this.embeddingService) {
                try {
                const relevantHistory = await this.embeddingService.findRelevantMemories(currentMessage, {
                    userId,
                    personaName,
                        limit: Math.ceil(maxResults / 2),
                    threshold: similarityThreshold
                });

                    allContext = allContext.concat(relevantHistory.map(h => ({
                    ...h,
                        context_type: 'embedding_similarity',
                    relevance_score: h.similarity
                    })));
                } catch (embeddingError) {
                    this.logger.warning('对话历史管理器', '嵌入向量搜索失败:', embeddingError.message);
                }
            }

            // 去重并排序
            const uniqueContext = this.deduplicateContext(allContext);
            uniqueContext.sort((a, b) => (b.relevance_score || 0) - (a.relevance_score || 0));

            return uniqueContext.slice(0, maxResults);

        } catch (error) {
            this.logger.error('对话历史管理器', '获取上下文历史失败:', error.message);
            return await this.getRecentHistory(userId, personaName, 5);
        }
    }

    /**
     * 计算消息与会话的相关性分数
     */
    calculateRelevanceScore(currentMessage, session) {
        // 简单的关键词匹配算法
        const currentWords = this.extractKeywords(currentMessage.toLowerCase());
        const sessionContent = (session.user_content + ' ' + session.assistant_content).toLowerCase();
        const sessionWords = this.extractKeywords(sessionContent);

        if (currentWords.length === 0 || sessionWords.length === 0) {
            return 0;
        }

        const commonWords = currentWords.filter(word => sessionWords.includes(word));
        const relevanceScore = commonWords.length / Math.max(currentWords.length, sessionWords.length);

        return relevanceScore;
    }

    /**
     * 提取关键词
     */
    extractKeywords(text) {
        // 移除标点符号并分词
        const words = text.replace(/[^\w\s\u4e00-\u9fff]/g, ' ').split(/\s+/).filter(word => word.length > 1);
        
        // 简单的停用词过滤
        const stopWords = ['的', '是', '在', '了', '和', '有', '我', '你', '他', '她', '它', '我们', '你们', '他们', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        
        return words.filter(word => !stopWords.includes(word));
    }

    /**
     * 去重上下文数据
     */
    deduplicateContext(contexts) {
        const seen = new Set();
        return contexts.filter(context => {
            const key = context.session_id || context.id || context.content?.substring(0, 50);
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    /**
     * 清理旧的对话数据
     */
    async cleanupOldConversations(retentionDays = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
            const cutoffTimestamp = this.formatTimestamp(cutoffDate);

            this.logger.info('对话历史管理器', `开始清理${retentionDays}天前的对话数据，截止时间: ${cutoffTimestamp}`);

            // 清理会话表中的旧数据
            const sessionResult = await this.dbRun(`
                DELETE FROM conversation_sessions 
                WHERE last_update_time < ?
            `, [cutoffTimestamp]);

            // 清理详细历史表中的旧数据
            const historyResult = await this.dbRun(`
                DELETE FROM conversation_history 
                WHERE timestamp < ?
            `, [cutoffTimestamp]);

            this.logger.success('对话历史管理器', 
                `数据清理完成 - 会话: ${sessionResult.changes || 0}条, 历史: ${historyResult.changes || 0}条`
            );

            return {
                success: true,
                cleaned_sessions: sessionResult.changes || 0,
                cleaned_history: historyResult.changes || 0
            };

        } catch (error) {
            this.logger.error('对话历史管理器', '清理旧数据失败:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取对话统计信息
     */
    async getConversationStats(userId = null, personaName = null) {
        try {
            let sessionQuery = `SELECT COUNT(*) as session_count, SUM(message_count) as total_messages FROM conversation_sessions`;
            let historyQuery = `SELECT COUNT(*) as history_count FROM conversation_history`;
            let params = [];

            if (userId && personaName) {
                sessionQuery += ` WHERE user_id = ? AND persona_name = ?`;
                historyQuery += ` WHERE user_id = ? AND persona_name = ?`;
                params = [userId, personaName];
            } else if (userId) {
                sessionQuery += ` WHERE user_id = ?`;
                historyQuery += ` WHERE user_id = ?`;
                params = [userId];
            }

            const [sessionStats, historyStats] = await Promise.all([
                this.dbGet(sessionQuery, params),
                this.dbGet(historyQuery, params)
            ]);

            return {
                sessions: sessionStats?.session_count || 0,
                total_messages: sessionStats?.total_messages || 0,
                detailed_history: historyStats?.history_count || 0,
                last_updated: this.getLocalTimestamp()
            };

        } catch (error) {
            this.logger.error('对话历史管理器', '获取统计信息失败:', error.message);
            return { sessions: 0, total_messages: 0, detailed_history: 0 };
        }
    }

    /**
     * 计算消息重要性分数
     */
    calculateImportanceScore(content, emotionState = null) {
        // 基础分数：根据内容长度
        let score = Math.min(content.length / 500, 1.0) * 0.3;
        
        // 情感状态加权
            if (emotionState) {
            const emotionIntensity = emotionState.intensity || emotionState.arousal || 0.5;
            score += emotionIntensity * 0.4;
        }
        
        // 关键词加权
        const importantKeywords = ['问题', '错误', '重要', '紧急', '帮助', 'error', 'important', 'urgent', 'help'];
        const hasImportantKeywords = importantKeywords.some(keyword => 
            content.toLowerCase().includes(keyword)
        );
        
        if (hasImportantKeywords) {
            score += 0.3;
            }

            return Math.min(score, 1.0);
    }

    // 辅助方法
    calculateTokens(text) {
        // 简单的token计算：中文字符算1个token，英文单词算1个token
        const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
        const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
        return chineseChars + englishWords;
    }

    generateContentPreview(content, maxLength = 100) {
        return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
    }

    /**
     * 工具方法
     */
    safeJsonParse(jsonString, defaultValue = null) {
        try {
            return JSON.parse(jsonString || '{}');
        } catch (e) {
            return defaultValue;
        }
    }

    formatTimestamp(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    // 数据库方法包装
    async dbRun(query, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(query, params, function(err) {
                if (err) reject(err);
                else resolve({ lastID: this.lastID, changes: this.changes });
            });
        });
    }

    async dbAll(query, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(query, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
    }

    async dbGet(query, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(query, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            realTimeQuery: true,
            cacheDisabled: true
        };
    }

    /**
     * 智能添加完整对话记录（重构版：支持合并逻辑）
     */
    async addConversation(userId, userMessage, aiResponse, timestamp, extraData = {}) {
        try {
            const localTimestamp = timestamp || this.getLocalTimestamp();
            const conversationId = extraData.conversationId || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const personaName = extraData.personaName || 'Assistant';
            const chatType = extraData.chatType || 'private';
            const chatName = extraData.chatName || '';

            this.logger.info('对话历史管理器', '开始智能对话记录处理', {
                userId,
                personaName,
                chatType,
                chatName,
                userMessageLength: userMessage.length,
                aiResponseLength: aiResponse.length,
                localTimestamp
            });

            // 1. 使用智能会话管理
            const sessionResult = await this.findOrCreateSession(userId, personaName, userMessage, aiResponse, {
                chatType,
                chatName,
                emotionState: extraData.emotionData
            });

            if (!sessionResult.success) {
                throw new Error('会话管理失败');
            }

            // 2. 存储详细历史记录（如果需要）
            const storeDetailedHistory = this.config?.storeDetailedHistory !== false;
            let userMessageId = null;
            let assistantMessageId = null;

            if (storeDetailedHistory) {
            const userResult = await this.storeMessage({
                userId,
                personaName,
                conversationId,
                speaker: 'user',
                content: userMessage,
                embeddingVector: null,
                emotionState: extraData.userEmotion || null,
                    timestamp: localTimestamp,
                    chatType,
                    chatName,
                    sessionId: sessionResult.session_id,
                importanceScore: this.calculateImportanceScore(userMessage, extraData.userEmotion)
            });

            const assistantResult = await this.storeMessage({
                userId,
                personaName,
                conversationId,
                speaker: 'assistant',
                content: aiResponse,
                embeddingVector: null,
                emotionState: extraData.aiEmotion || null,
                    timestamp: localTimestamp,
                    chatType,
                    chatName,
                    sessionId: sessionResult.session_id,
                importanceScore: this.calculateImportanceScore(aiResponse, extraData.aiEmotion)
            });

                userMessageId = userResult.message_id;
                assistantMessageId = assistantResult.message_id;
            }

            this.logger.success('对话历史管理器', '智能对话记录处理完成', {
                sessionId: sessionResult.session_id,
                action: sessionResult.action,
                messageCount: sessionResult.message_count,
                userMessageId,
                assistantMessageId,
                localTimestamp
            });

            return {
                success: true,
                id: conversationId,
                session_id: sessionResult.session_id,
                action: sessionResult.action,
                message_count: sessionResult.message_count,
                userMessageId,
                assistantMessageId,
                timestamp: localTimestamp,
                userImportanceScore: this.calculateImportanceScore(userMessage, extraData.userEmotion),
                assistantImportanceScore: this.calculateImportanceScore(aiResponse, extraData.aiEmotion)
            };

        } catch (error) {
            this.logger.error('对话历史管理器', `智能对话记录失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 计算对话重要性分数
     */
    calculateConversationImportance(userMessage, aiResponse, extraData = {}) {
        let score = 0.5; // 基础分数

        // 基于消息长度
        const totalLength = userMessage.length + aiResponse.length;
        if (totalLength > 200) score += 0.1;
        if (totalLength > 500) score += 0.1;

        // 基于情感强度
        if (extraData.emotionData && extraData.emotionData.intensity) {
            score += extraData.emotionData.intensity * 0.2;
        }

        // 基于概念数量
        if (extraData.conceptData && extraData.conceptData.concepts) {
            score += Math.min(extraData.conceptData.concepts.length * 0.05, 0.2);
        }

        // 基于好感度变化
        if (extraData.affinityData && extraData.affinityData.affinityChange) {
            score += Math.abs(extraData.affinityData.affinityChange) * 0.1;
        }

        return Math.min(Math.max(score, 0.1), 1.0); // 限制在0.1-1.0之间
    }
}

module.exports = ConversationHistoryManager; 