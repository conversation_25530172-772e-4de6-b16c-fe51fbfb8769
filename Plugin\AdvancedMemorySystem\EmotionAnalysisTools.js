/**
 * 情感分析工具
 * 基于OpenAI Tools进行高质量情感分析和心理状态评估
 */

class EmotionAnalysisTools {
    constructor(config, openaiService, logger) {
        this.config = config;
        this.openaiService = openaiService;
        this.logger = logger;
        this.isInitialized = false;
        
        // 情感标签映射
        this.emotionLabels = [
            'joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust', 'contempt',
            'happiness', 'excitement', 'anxiety', 'frustration', 'confusion',
            'pride', 'shame', 'guilt', 'love', 'hate', 'trust', 'neutral',
            'anticipation', 'disappointment', 'relief', 'nostalgia', 'curiosity'
        ];
        
        // 关系类型映射
        this.relationshipTypes = [
            'stranger', 'acquaintance', 'friend', 'close_friend', 'romantic',
            'family', 'mentor', 'colleague', 'adversary'
        ];
    }

    /**
     * 初始化服务
     */
    async initialize() {
        try {
            if (!this.openaiService || !this.openaiService.isInitialized) {
                throw new Error('OpenAI服务未初始化');
            }
            
            this.isInitialized = true;
            this.logger.success('情感分析工具', '✅ 情感分析工具初始化成功');
            return { success: true };
            
        } catch (error) {
            this.logger.error('情感分析工具', '初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 分析消息的情感状态
     */
    async analyzeMessage(message, context = {}) {
        try {
            const { userId, personaName, messageType = 'user', conversationId, userMessage } = context;

            this.logger.info('情感分析工具', `开始分析${messageType}消息的情感状态`);

            let emotionResult;

            try {
                // 使用OpenAI Tools进行情感分析
                emotionResult = await this.openaiService.analyzeEmotion(message, {
                    userId,
                    personaName,
                    messageType,
                    conversationId,
                    userMessage,
                    context: this.buildAnalysisContext(context)
                });
            } catch (apiError) {
                this.logger.warning('情感分析工具', `OpenAI API调用失败，使用简化分析: ${apiError.message}`);
                // 回退到简化的情感分析
                emotionResult = this.performSimpleEmotionAnalysis(message, messageType);
            }

            // 标准化结果
            const standardizedResult = this.standardizeEmotionResult(emotionResult);

            // 生成情感状态报告
            const emotionReport = this.generateEmotionReport(standardizedResult, message);

            this.logger.info('情感分析工具', `情感分析完成 - 主要情感: ${standardizedResult.primary_emotion}, 效价: ${standardizedResult.valence.toFixed(2)}`);

            return {
                success: true,
                emotion_state: standardizedResult,
                emotion_report: emotionReport,
                analysis_timestamp: new Date().toISOString(),
                message_type: messageType,
                ...standardizedResult // 直接展开情感状态属性
            };

        } catch (error) {
            this.logger.error('情感分析工具', '消息情感分析失败:', error.message);
            const defaultState = this.getDefaultEmotionState();
            return {
                success: false,
                error: error.message,
                emotion_state: defaultState,
                ...defaultState // 直接展开默认情感状态属性
            };
        }
    }

    /**
     * 分析AI回复对用户情感的影响
     */
    async analyzeAIResponse(userMessage, aiResponse, context = {}) {
        try {
            const { userId, personaName, conversationHistory = [] } = context;
            
            this.logger.info('情感分析工具', '开始分析AI回复的情感影响');
            
            // 🚀 使用优化的综合对话分析工具，一次性获取所有分析结果
            const comprehensiveAnalysis = await this.openaiService.optimizedConversationAnalysis(
                userMessage,
                aiResponse,
                {
                    userId,
                    personaName,
                    conversationHistory: this.formatConversationHistory(conversationHistory),
                    previousInteractions: await this.getPreviousInteractionsSummary(userId, personaName)
                }
            );

            this.logger.info('情感分析工具', `AI回复综合分析完成 - 好感度变化: ${comprehensiveAnalysis.affinity_delta.toFixed(3)}, 关系: ${comprehensiveAnalysis.relationship_type}`);

            // 转换为原有格式以保持兼容性
            const affinityAnalysis = {
                affinity_delta: comprehensiveAnalysis.affinity_delta,
                relationship_type: comprehensiveAnalysis.relationship_type,
                trust_level: comprehensiveAnalysis.trust_change,
                emotional_resonance: comprehensiveAnalysis.emotional_resonance,
                interaction_quality: comprehensiveAnalysis.conversation_outcome
            };

            const responseEmotion = {
                emotion_valence: comprehensiveAnalysis.user_positivity * 50, // 转换为-50到50范围
                arousal_level: comprehensiveAnalysis.user_intensity,
                dominance_level: 0.5
            };

            const impactAnalysis = this.analyzeEmotionalImpact(affinityAnalysis, responseEmotion, context);

            return {
                success: true,
                affinity_analysis: affinityAnalysis,
                emotion_impact: impactAnalysis,
                response_emotion: responseEmotion,
                comprehensive_analysis: comprehensiveAnalysis, // 新增完整分析数据
                analysis_timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            this.logger.error('情感分析工具', 'AI回复分析失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 基于对话历史进行情感趋势分析
     */
    async analyzeLongTermEmotionalTrend(userId, personaName, timeRange = 30) {
        try {
            this.logger.info('情感分析工具', `分析用户 ${userId} 的长期情感趋势`);
            
            // 获取历史对话数据
            const historicalData = await this.getHistoricalEmotionData(userId, personaName, timeRange);
            
            if (historicalData.length === 0) {
                return {
                    success: false,
                    message: '没有足够的历史数据进行趋势分析'
                };
            }
            
            // 使用OpenAI分析长期趋势
            const trendAnalysis = await this.performTrendAnalysis(historicalData);
            
            // 生成趋势报告
            const trendReport = this.generateTrendReport(trendAnalysis, historicalData);
            
            return {
                success: true,
                trend_analysis: trendAnalysis,
                trend_report: trendReport,
                data_points: historicalData.length,
                time_range_days: timeRange
            };
            
        } catch (error) {
            this.logger.error('情感分析工具', '长期情感趋势分析失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 实时情感状态监控
     */
    async monitorEmotionalState(userId, personaName) {
        try {
            // 获取最近的情感状态
            const recentEmotions = await this.getRecentEmotionalStates(userId, personaName, 10);
            
            // 检测情感波动
            const volatilityAnalysis = this.analyzeEmotionalVolatility(recentEmotions);
            
            // 检测异常情感状态
            const anomalies = this.detectEmotionalAnomalies(recentEmotions);
            
            // 生成监控报告
            const monitoringReport = {
                current_state: recentEmotions[0] || this.getDefaultEmotionState(),
                volatility: volatilityAnalysis,
                anomalies: anomalies,
                stability_score: this.calculateStabilityScore(recentEmotions),
                recommendations: this.generateEmotionalRecommendations(volatilityAnalysis, anomalies)
            };
            
            return {
                success: true,
                monitoring_report: monitoringReport,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            this.logger.error('情感分析工具', '情感状态监控失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 标准化情感分析结果
     */
    standardizeEmotionResult(emotionResult) {
        try {
            return {
                emotion_labels: emotionResult.emotion_labels || ['neutral'],
                primary_emotion: emotionResult.emotion_labels ? emotionResult.emotion_labels[0] : 'neutral',
                valence: this.clampValue(emotionResult.valence || 0, -1, 1),
                arousal: this.clampValue(emotionResult.arousal || 0, -1, 1),
                dominance: this.clampValue(emotionResult.dominance || 0, -1, 1),
                intensity: this.clampValue(emotionResult.intensity || 0, 0, 1),
                emotion_description: emotionResult.emotion_description || '中性平静状态',
                psychological_analysis: emotionResult.psychological_analysis || '',
                stability: this.clampValue(emotionResult.stability || 0.5, 0, 1),
                confidence: this.calculateConfidence(emotionResult)
            };
        } catch (error) {
            this.logger.warning('情感分析工具', '标准化情感结果失败:', error.message);
            return this.getDefaultEmotionState();
        }
    }

    /**
     * 分析情感影响
     */
    analyzeEmotionalImpact(affinityAnalysis, responseEmotion, context) {
        try {
            return {
                affinity_delta: affinityAnalysis.affinity_delta || 0,
                relationship_change: this.calculateRelationshipChange(affinityAnalysis),
                emotional_resonance: affinityAnalysis.emotional_resonance || 0,
                trust_impact: this.calculateTrustImpact(affinityAnalysis),
                satisfaction_delta: this.calculateSatisfactionDelta(affinityAnalysis),
                interaction_quality: affinityAnalysis.interaction_quality || 'neutral',
                response_appropriateness: this.evaluateResponseAppropriateness(responseEmotion, context),
                predicted_user_reaction: this.predictUserReaction(affinityAnalysis, responseEmotion)
            };
        } catch (error) {
            this.logger.warning('情感分析工具', '分析情感影响失败:', error.message);
            return {
                affinity_delta: 0,
                relationship_change: 'stable',
                emotional_resonance: 0,
                trust_impact: 0,
                satisfaction_delta: 0,
                interaction_quality: 'neutral'
            };
        }
    }

    /**
     * 生成情感状态报告
     */
    generateEmotionReport(emotionState, message) {
        try {
            const report = {
                summary: `检测到${emotionState.primary_emotion}情感，强度${(emotionState.intensity * 100).toFixed(0)}%`,
                details: {
                    dominant_emotion: emotionState.primary_emotion,
                    emotion_mix: emotionState.emotion_labels.slice(0, 3),
                    emotional_intensity: this.getIntensityDescription(emotionState.intensity),
                    emotional_stability: this.getStabilityDescription(emotionState.stability),
                    psychological_insights: emotionState.psychological_analysis
                },
                vad_analysis: {
                    valence_description: this.getValenceDescription(emotionState.valence),
                    arousal_description: this.getArousalDescription(emotionState.arousal),
                    dominance_description: this.getDominanceDescription(emotionState.dominance)
                },
                recommendations: this.generateResponseRecommendations(emotionState)
            };
            
            return report;
        } catch (error) {
            this.logger.warning('情感分析工具', '生成情感报告失败:', error.message);
            return { summary: '情感分析报告生成失败' };
        }
    }

    /**
     * 简化的情感分析（当OpenAI API不可用时）
     */
    performSimpleEmotionAnalysis(message, messageType = 'user') {
        try {
            const text = message.toLowerCase();

            // 基础情感词典
            const emotionKeywords = {
                happy: ['开心', '高兴', '快乐', '愉快', '兴奋', '满意', '喜欢', '爱', '棒', '好', '赞', '哈哈', '😊', '😄', '😃'],
                sad: ['难过', '伤心', '悲伤', '失望', '沮丧', '痛苦', '哭', '😢', '😭', '😞'],
                angry: ['生气', '愤怒', '恼火', '烦躁', '讨厌', '气', '怒', '😠', '😡'],
                fear: ['害怕', '恐惧', '担心', '紧张', '焦虑', '不安', '怕'],
                surprise: ['惊讶', '震惊', '意外', '没想到', '哇', '😮', '😲'],
                disgust: ['恶心', '厌恶', '讨厌', '反感']
            };

            let maxScore = 0;
            let detectedEmotion = 'neutral';
            let valence = 0;
            let arousal = 0;

            // 检测情感关键词
            for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
                let score = 0;
                for (const keyword of keywords) {
                    if (text.includes(keyword)) {
                        score += 1;
                    }
                }

                if (score > maxScore) {
                    maxScore = score;
                    detectedEmotion = emotion;
                }
            }

            // 设置VAD值
            switch (detectedEmotion) {
                case 'happy':
                    valence = 0.8;
                    arousal = 0.6;
                    break;
                case 'sad':
                    valence = -0.7;
                    arousal = -0.4;
                    break;
                case 'angry':
                    valence = -0.6;
                    arousal = 0.8;
                    break;
                case 'fear':
                    valence = -0.5;
                    arousal = 0.7;
                    break;
                case 'surprise':
                    valence = 0.2;
                    arousal = 0.8;
                    break;
                case 'disgust':
                    valence = -0.8;
                    arousal = 0.3;
                    break;
                default:
                    valence = 0.0;
                    arousal = 0.0;
            }

            const intensity = maxScore > 0 ? Math.min(maxScore * 0.3, 1.0) : 0.1;

            return {
                emotion_labels: [detectedEmotion],
                primary_emotion: detectedEmotion,
                valence: valence,
                arousal: arousal,
                dominance: valence * 0.5, // 简化的支配度计算
                intensity: intensity,
                emotion_description: this.getEmotionDescription(detectedEmotion, intensity),
                psychological_analysis: `基于关键词检测的${detectedEmotion}情感`,
                stability: 0.5,
                confidence: maxScore > 0 ? 0.6 : 0.3,
                analysis_method: 'simplified'
            };

        } catch (error) {
            this.logger.error('情感分析工具', '简化情感分析失败:', error.message);
            return this.getDefaultEmotionState();
        }
    }

    /**
     * 获取情感描述
     */
    getEmotionDescription(emotion, intensity) {
        const descriptions = {
            happy: intensity > 0.7 ? '非常开心' : intensity > 0.4 ? '比较开心' : '略微开心',
            sad: intensity > 0.7 ? '非常难过' : intensity > 0.4 ? '比较难过' : '略微难过',
            angry: intensity > 0.7 ? '非常愤怒' : intensity > 0.4 ? '比较愤怒' : '略微愤怒',
            fear: intensity > 0.7 ? '非常害怕' : intensity > 0.4 ? '比较害怕' : '略微害怕',
            surprise: intensity > 0.7 ? '非常惊讶' : intensity > 0.4 ? '比较惊讶' : '略微惊讶',
            disgust: intensity > 0.7 ? '非常厌恶' : intensity > 0.4 ? '比较厌恶' : '略微厌恶',
            neutral: '中性平静状态'
        };
        return descriptions[emotion] || '中性平静状态';
    }

    /**
     * 简化的好感度分析（当OpenAI API不可用时）
     */
    performSimpleAffinityAnalysis(userMessage, aiResponse) {
        try {
            const userText = userMessage.toLowerCase();
            const aiText = aiResponse.toLowerCase();

            // 分析用户消息的情感倾向
            const userEmotion = this.performSimpleEmotionAnalysis(userMessage);

            // 分析AI回复的情感倾向
            const aiEmotion = this.performSimpleEmotionAnalysis(aiResponse);

            // 计算好感度变化
            let affinityDelta = 0;

            // 如果AI回复是积极的，增加好感度
            if (aiEmotion.valence > 0.3) {
                affinityDelta += 0.1;
            } else if (aiEmotion.valence < -0.3) {
                affinityDelta -= 0.1;
            }

            // 如果AI回复与用户情感产生共鸣，增加好感度
            if (Math.abs(userEmotion.valence - aiEmotion.valence) < 0.3) {
                affinityDelta += 0.05;
            }

            // 检查回复质量指标
            const responseLength = aiResponse.length;
            if (responseLength > 50 && responseLength < 500) {
                affinityDelta += 0.02; // 适中长度的回复
            }

            // 检查礼貌用词
            const politeWords = ['请', '谢谢', '不好意思', '抱歉', '您', '麻烦'];
            const politeCount = politeWords.filter(word => aiText.includes(word)).length;
            affinityDelta += politeCount * 0.01;

            return {
                affinity_delta: Math.max(-0.3, Math.min(0.3, affinityDelta)),
                satisfaction_level: Math.max(0, Math.min(1, 0.5 + aiEmotion.valence * 0.3)),
                relationship_type: affinityDelta > 0.1 ? 'friend' : (affinityDelta < -0.1 ? 'stranger' : 'acquaintance'),
                interaction_quality: affinityDelta > 0.05 ? 'good' : (affinityDelta < -0.05 ? 'poor' : 'neutral'),
                trust_level: 0.5 + (aiEmotion.valence * 0.2),
                emotional_resonance: 1 - Math.abs(userEmotion.valence - aiEmotion.valence),
                analysis_method: 'simplified'
            };

        } catch (error) {
            this.logger.error('情感分析工具', '简化好感度分析失败:', error.message);
            return this.getDefaultAffinityState();
        }
    }

    /**
     * 获取默认情感状态
     */
    getDefaultEmotionState() {
        return {
            emotion_labels: ['neutral'],
            primary_emotion: 'neutral',
            valence: 0,
            arousal: 0,
            dominance: 0,
            intensity: 0.1,
            emotion_description: '中性平静状态',
            psychological_analysis: '',
            stability: 0.5,
            confidence: 0.3,
            analysis_method: 'default'
        };
    }

    /**
     * 构建分析上下文
     */
    buildAnalysisContext(context) {
        return {
            user_id: context.userId,
            persona_name: context.personaName,
            conversation_id: context.conversationId,
            message_type: context.messageType,
            timestamp: new Date().toISOString(),
            previous_emotions: context.previousEmotions || [],
            user_preferences: context.userPreferences || {}
        };
    }

    /**
     * 格式化对话历史
     */
    formatConversationHistory(history) {
        return history.slice(-10).map(h => ({
            speaker: h.speaker,
            content: h.content.length > 200 ? h.content.substring(0, 200) + '...' : h.content,
            timestamp: h.timestamp,
            emotion_state: h.emotion_state
        }));
    }

    /**
     * 值范围限制
     */
    clampValue(value, min, max) {
        return Math.max(min, Math.min(max, value));
    }

    /**
     * 计算置信度
     */
    calculateConfidence(emotionResult) {
        try {
            let confidence = 0.5; // 基础置信度
            
            // 基于情感标签数量调整
            if (emotionResult.emotion_labels && emotionResult.emotion_labels.length > 0) {
                confidence += 0.2;
            }
            
            // 基于描述详细程度调整
            if (emotionResult.emotion_description && emotionResult.emotion_description.length > 20) {
                confidence += 0.1;
            }
            
            // 基于心理分析质量调整
            if (emotionResult.psychological_analysis && emotionResult.psychological_analysis.length > 50) {
                confidence += 0.1;
            }
            
            // 基于VAD值的一致性调整
            const vadConsistency = this.checkVADConsistency(emotionResult);
            confidence += vadConsistency * 0.1;
            
            return this.clampValue(confidence, 0, 1);
        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 检查VAD值一致性
     */
    checkVADConsistency(emotionResult) {
        try {
            const { valence, arousal, dominance, emotion_labels } = emotionResult;
            
            if (!emotion_labels || emotion_labels.length === 0) return 0;
            
            const primaryEmotion = emotion_labels[0];
            
            // 预定义的情感VAD期望值
            const emotionVADExpectations = {
                'joy': { valence: 0.8, arousal: 0.6, dominance: 0.7 },
                'sadness': { valence: -0.7, arousal: -0.3, dominance: -0.4 },
                'anger': { valence: -0.6, arousal: 0.8, dominance: 0.7 },
                'fear': { valence: -0.8, arousal: 0.5, dominance: -0.6 },
                'surprise': { valence: 0.2, arousal: 0.8, dominance: 0.1 },
                'neutral': { valence: 0, arousal: 0, dominance: 0 }
            };
            
            const expected = emotionVADExpectations[primaryEmotion];
            if (!expected) return 0.5;
            
            // 计算与期望值的相似度
            const valenceDiff = Math.abs(valence - expected.valence);
            const arousalDiff = Math.abs(arousal - expected.arousal);
            const dominanceDiff = Math.abs(dominance - expected.dominance);
            
            const avgDiff = (valenceDiff + arousalDiff + dominanceDiff) / 3;
            return Math.max(0, 1 - avgDiff);
            
        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 生成回复建议
     */
    generateResponseRecommendations(emotionState) {
        const recommendations = [];
        
        try {
            // 基于主要情感的建议
            switch (emotionState.primary_emotion) {
                case 'joy':
                case 'happiness':
                    recommendations.push('分享用户的快乐，保持积极的互动氛围');
                    break;
                case 'sadness':
                    recommendations.push('表达同理心和支持，避免过度乐观的回复');
                    break;
                case 'anger':
                case 'frustration':
                    recommendations.push('保持冷静，尝试理解用户的观点，避免争论');
                    break;
                case 'fear':
                case 'anxiety':
                    recommendations.push('提供安慰和支持，给出实用的建议');
                    break;
                case 'surprise':
                    recommendations.push('顺应用户的惊讶，提供更多信息');
                    break;
                default:
                    recommendations.push('保持友好和自然的对话风格');
            }
            
            // 基于情感强度的建议
            if (emotionState.intensity > 0.7) {
                recommendations.push('用户情感强烈，需要特别关注和谨慎回应');
            }
            
            // 基于情感稳定性的建议
            if (emotionState.stability < 0.3) {
                recommendations.push('用户情感不稳定，建议使用平和稳定的语调');
            }
            
            return recommendations;
            
        } catch (error) {
            return ['保持自然友好的对话风格'];
        }
    }

    /**
     * 获取强度描述
     */
    getIntensityDescription(intensity) {
        if (intensity >= 0.8) return '非常强烈';
        if (intensity >= 0.6) return '强烈';
        if (intensity >= 0.4) return '中等';
        if (intensity >= 0.2) return '轻微';
        return '微弱';
    }

    /**
     * 获取稳定性描述
     */
    getStabilityDescription(stability) {
        if (stability >= 0.8) return '非常稳定';
        if (stability >= 0.6) return '稳定';
        if (stability >= 0.4) return '一般';
        if (stability >= 0.2) return '不稳定';
        return '极不稳定';
    }

    /**
     * 获取效价描述
     */
    getValenceDescription(valence) {
        if (valence >= 0.6) return '非常积极';
        if (valence >= 0.2) return '积极';
        if (valence >= -0.2) return '中性';
        if (valence >= -0.6) return '消极';
        return '非常消极';
    }

    /**
     * 获取唤醒描述
     */
    getArousalDescription(arousal) {
        if (arousal >= 0.6) return '非常兴奋';
        if (arousal >= 0.2) return '兴奋';
        if (arousal >= -0.2) return '平静';
        if (arousal >= -0.6) return '低沉';
        return '非常低沉';
    }

    /**
     * 获取支配描述
     */
    getDominanceDescription(dominance) {
        if (dominance >= 0.6) return '主导性强';
        if (dominance >= 0.2) return '有一定主导性';
        if (dominance >= -0.2) return '中性';
        if (dominance >= -0.6) return '被动';
        return '非常被动';
    }

    // 其他辅助方法...
    async getPreviousInteractionsSummary(userId, personaName) {
        // 实现获取用户历史互动摘要的逻辑
        return {};
    }

    calculateRelationshipChange(affinityAnalysis) {
        const delta = affinityAnalysis.affinity_delta || 0;
        if (delta > 0.3) return 'improving_significantly';
        if (delta > 0.1) return 'improving';
        if (delta < -0.3) return 'deteriorating_significantly';
        if (delta < -0.1) return 'deteriorating';
        return 'stable';
    }

    calculateTrustImpact(affinityAnalysis) {
        return (affinityAnalysis.trust_level || 0.5) - 0.5;
    }

    calculateSatisfactionDelta(affinityAnalysis) {
        return (affinityAnalysis.satisfaction_level || 0.5) - 0.5;
    }

    evaluateResponseAppropriateness(responseEmotion, context) {
        // 基于回复的情感色彩评估适当性
        return 'appropriate'; // 简化实现
    }

    predictUserReaction(affinityAnalysis, responseEmotion) {
        // 预测用户可能的反应
        return 'neutral'; // 简化实现
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            supportedEmotions: this.emotionLabels.length,
            supportedRelationships: this.relationshipTypes.length
        };
    }
}

module.exports = EmotionAnalysisTools; 