const fs = require('fs').promises;
const path = require('path');

// 引入VCP日志系统，但强制输出到stderr以避免污染stdout的JSON
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}


async function processDailyNotes(inputContent) {
    // Use PROJECT_BASE_PATH environment variable set by Plugin.js
    const projectBasePath = process.env.PROJECT_BASE_PATH;
    if (!projectBasePath) {
        logger.error('日记管理', 'PROJECT_BASE_PATH环境变量未设置');
        return { status: 'error', error: '无法确定项目主目录。' };
    }
    const outputDir = path.join(projectBasePath, 'dailynote', '已整理日记');
    const results = []; // Define results here

    try {
        // Ensure the output directory exists
        await fs.mkdir(outputDir, { recursive: true });

        // 添加调试输出，显示接收到的内容
        logger.debug('日记管理', `收到的日记内容前100个字符: \${inputContent.substring(0, 100)}...`);

        const lines = inputContent.split('\n');
        let currentFilename = null;
        let currentContentLines = [];

        // Helper function to save the current note
        const saveCurrentNote = async () => {
            if (currentFilename && currentContentLines.length > 0) {
                const filename = currentFilename.trim();
                // Join lines and trim leading/trailing whitespace, but keep internal line breaks
                const content = currentContentLines.join('\n').trim();

                // 添加调试输出，显示即将保存的日记信息
                logger.debug('日记管理', `准备保存日记: 文件名=\${filename}, 内容长度=\${content.length}`);

                if (!filename.toLowerCase().endsWith('.txt') || content.length === 0) {
                     results.push({ status: 'warning', filename: filename || '未知', message: `无效的日记条目格式或内容为空。跳过保存。` });
                     logger.error('日记管理', `无效的日记条目格式或内容为空。文件名: \${filename}, 内容长度: \${content.length}`);
                     return; // Skip saving if invalid
                }

                const filePath = path.join(outputDir, filename);

                try {
                    await fs.writeFile(filePath, content, 'utf-8');
                    results.push({ status: 'success', filename: filename, message: `成功保存日记: ${filename}` });
                    logger.debug('日记管理', `成功保存日记: \${filename}`);
                } catch (writeError) {
                    results.push({ status: 'error', filename: filename, message: `保存日记失败: ${filename} - ${writeError.message}` });
                    logger.error('日记管理', `保存日记失败: \${filename}`, writeError);
                }
            }
        };

        // Iterate through lines to find diary entries
        for (const line of lines) {
            const trimmedLine = line.trim();
            // Check if the line matches the filename pattern (YYYY.MM.DD.txt or YYYY.MM.DD.N.txt)
            const filenameMatch = trimmedLine.match(/^(\d{4}\.\d{2}\.\d{2}(?:\.\d+)?)\.txt$/);

            if (filenameMatch) {
                // Found a new filename line, save the previous note if any
                await saveCurrentNote();

                // Start a new note
                currentFilename = trimmedLine;
                currentContentLines = []; // Reset content lines for the new note
                logger.debug('日记管理', `检测到新的日记文件标记: \${currentFilename}`);
            } else if (currentFilename !== null) {
                // If we are currently collecting content for a note, add the line
                currentContentLines.push(line);
            }
            // If currentFilename is null and the line is not a filename, ignore it (e.g., leading empty lines)
        }

        // Save the last note after the loop finishes
        await saveCurrentNote();

         // Check if any notes were processed
         if (results.length === 0) {
             results.push({ status: 'warning', message: '在命令块中未找到有效的日记条目。请检查AI输出格式。' });
             logger.error('日记管理', '在命令块中未找到有效的日记条目。请检查AI输出格式');
         }

    } catch (dirError) {
        results.push({ status: 'error', message: `创建输出目录失败: ${outputDir} - ${dirError.message}` });
        logger.error('日记管理', `创建输出目录失败: \${outputDir}`, dirError);
        return { status: 'error', error: `创建输出目录失败: ${outputDir} - ${dirError.message}` }; // Return immediately on directory creation failure
    }

    // Determine overall status and format output for Plugin.js
    const errors = results.filter(r => r.status === 'error');
    const warnings = results.filter(r => r.status === 'warning');
    const successes = results.filter(r => r.status === 'success');

    if (errors.length > 0) {
        const errorMessages = errors.map(e => `${e.filename || '未知文件'}: ${e.message}`).join('\n');
        return { status: 'error', error: `保存日记时发生错误:\n${errorMessages}` };
    } else if (results.length === 0) {
         // This case is handled by the check inside the try block, but as a fallback:
         return { status: 'warning', result: '未找到有效的日记条目进行处理。请检查AI输出格式。' };
    }
    else {
        const successMessages = successes.map(s => `成功保存: ${s.filename}`).join('\n');
        const warningMessages = warnings.map(w => `警告: ${w.message}`).join('\n');
        let resultMessage = successMessages;
        if (warningMessages) {
            resultMessage += `\n\n警告:\n${warningMessages}`;
        }
        return { status: 'success', result: `日记处理完成:\n${resultMessage}` };
    }
}

// Read input from stdin
let inputData = '';
process.stdin.on('data', (chunk) => {
    inputData += chunk;
});

process.stdin.on('end', async () => {
    let processingResult;
    let diaryContent = '';

    try {
        // Parse the JSON input from Plugin.js
        const parsedInput = JSON.parse(inputData);
        // Extract the 'command' field which contains the raw diary data
        if (parsedInput && typeof parsedInput.command === 'string') {
            diaryContent = parsedInput.command;
        } else {
            throw new Error('无效的 input format: Expected JSON with a "command" string field.');
        }

        // Process the extracted diary content
        processingResult = await processDailyNotes(diaryContent);

        // 确保返回有效的JSON格式
        if (!processingResult || typeof processingResult !== 'object') {
            processingResult = {
                status: 'error',
                error: '处理结果格式无效'
            };
        }

    } catch (parseError) {
        logger.error('日记管理', '解析输入JSON或提取命令时出错', parseError.message);
        processingResult = { status: 'error', error: `处理输入数据失败: ${parseError.message}` };
    }

    // Output result as JSON to stdout
    logger.debug('日记管理', '处理结果', processingResult); console.log(JSON.stringify(processingResult, null, 2));
    // Exit the process
    process.exit(processingResult.status === 'error' ? 1 : 0);
});

process.stdin.on('error', (err) => {
    logger.error('日记管理', '从stdin读取时出错', err);
    console.log(JSON.stringify({ status: 'error', error: `读取标准输入失败: \${err.message}` }, null, 2));
    process.exit(1);
});