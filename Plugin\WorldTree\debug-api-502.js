/**
 * 调试API 502错误的详细分析脚本
 * 分析请求参数、响应头、错误详情等
 */

const axios = require('axios');

// 从环境变量或配置中获取API信息
const API_CONFIG = {
    url: process.env.API_URL || process.env.OPENAI_TOOLS_URL || 'https://yuanplus.cloud',
    key: process.env.API_Key || process.env.OPENAI_TOOLS_KEY || '',
    model: process.env.OPENAI_TOOLS_MODEL || 'gpt-4o-mini',
    timeout: 120000
};

async function debugAPI502Error() {
    console.log('🔍 调试API 502错误详细分析\n');
    
    // 1. 显示当前配置
    console.log('📋 当前API配置:');
    console.log(`  URL: ${API_CONFIG.url}`);
    console.log(`  Key: ${API_CONFIG.key ? `${API_CONFIG.key.substring(0, 10)}...` : '未配置'}`);
    console.log(`  Model: ${API_CONFIG.model}`);
    console.log(`  Timeout: ${API_CONFIG.timeout}ms\n`);
    
    // 2. 构建测试请求
    const testPrompt = `你是雨安安，请以第一人称的角度，根据当前状态和情况，写出你此刻的内心想法。

【当前时间】2025/07/19 21:10:00 (傍晚时段)

【你的物理状态】
- 专注程度: 75.5/100
- 精力水平: 45.2/100
- 饥饿感: 30.1/100
- 疲劳度: 25.8/100
- 警觉性: 85.3/100

【你的身份背景】
你是雨安安，一位专注于AI研究的技术专家。你对大语言模型和智能体技术有深入的理解，喜欢探索人工智能的前沿技术。

【傍晚时段的你】
傍晚时光，你会整理一天的研究成果，撰写技术文档或思考新的想法。

请以第一人称写出你此刻的内心想法，要求：
1. 体现当前的物理状态（专注度、精力、疲劳等）
2. 符合你的身份背景和性格
3. 考虑当前时间段的特点
4. 自然真实，就像真正的内心独白
5. 控制在50-80字之间
6. 不要使用引号或其他格式符号

请直接输出内心想法：`;

    const requestData = {
        model: API_CONFIG.model,
        messages: [
            {
                role: 'user',
                content: testPrompt
            }
        ],
        max_tokens: 200,
        temperature: 0.8,
        top_p: 0.9
    };

    const requestHeaders = {
        'Authorization': `Bearer ${API_CONFIG.key}`,
        'Content-Type': 'application/json',
        'User-Agent': 'WorldTreeVCP/1.0.0'
    };

    console.log('📤 请求详情:');
    console.log(`  URL: ${API_CONFIG.url}/v1/chat/completions`);
    console.log(`  Method: POST`);
    console.log(`  Headers:`, JSON.stringify(requestHeaders, null, 2));
    console.log(`  Body:`, JSON.stringify(requestData, null, 2));
    console.log('');

    // 3. 发送请求并分析响应
    try {
        console.log('🚀 发送API请求...');
        const startTime = Date.now();
        
        const response = await axios.post(
            `${API_CONFIG.url}/v1/chat/completions`,
            requestData,
            {
                headers: requestHeaders,
                timeout: API_CONFIG.timeout,
                validateStatus: function (status) {
                    // 允许所有状态码，这样我们可以分析错误响应
                    return true;
                }
            }
        );

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        console.log('📥 响应详情:');
        console.log(`  状态码: ${response.status} ${response.statusText}`);
        console.log(`  响应时间: ${responseTime}ms`);
        console.log(`  响应头:`, JSON.stringify(response.headers, null, 2));
        
        if (response.status === 502) {
            console.log('\n❌ 502 Bad Gateway 错误分析:');
            console.log('  这通常表示:');
            console.log('  1. 上游服务器（AI服务）不可用或响应超时');
            console.log('  2. 代理服务器配置问题');
            console.log('  3. 服务器负载过高');
            console.log('  4. 网络连接问题');
            
            console.log('\n📄 错误响应内容:');
            if (response.data) {
                if (typeof response.data === 'string') {
                    console.log(response.data);
                } else {
                    console.log(JSON.stringify(response.data, null, 2));
                }
            } else {
                console.log('  无响应内容');
            }
            
            // 分析可能的原因
            console.log('\n🔍 可能的原因分析:');
            
            // 检查URL格式
            if (!API_CONFIG.url.startsWith('http')) {
                console.log('  ⚠️ URL格式可能有问题，应该以http://或https://开头');
            }
            
            // 检查API Key格式
            if (!API_CONFIG.key.startsWith('sk-')) {
                console.log('  ⚠️ API Key格式可能有问题，通常应该以sk-开头');
            }
            
            // 检查模型名称
            console.log(`  📝 使用的模型: ${API_CONFIG.model}`);
            if (API_CONFIG.model.includes('gemini')) {
                console.log('  💡 检测到Gemini模型，可能需要不同的API格式');
            }
            
            // 检查请求大小
            const requestSize = JSON.stringify(requestData).length;
            console.log(`  📏 请求大小: ${requestSize} 字符`);
            if (requestSize > 4000) {
                console.log('  ⚠️ 请求可能过大');
            }
            
        } else if (response.status === 200) {
            console.log('\n✅ 请求成功!');
            console.log('响应内容:', JSON.stringify(response.data, null, 2));
        } else {
            console.log(`\n❌ 其他错误 (${response.status}):`, response.data);
        }

    } catch (error) {
        console.log('\n💥 请求异常:');
        console.log(`  错误类型: ${error.constructor.name}`);
        console.log(`  错误消息: ${error.message}`);
        
        if (error.response) {
            console.log(`  响应状态: ${error.response.status}`);
            console.log(`  响应头:`, error.response.headers);
            console.log(`  响应数据:`, error.response.data);
        } else if (error.request) {
            console.log('  请求已发送但未收到响应');
            console.log('  可能的网络问题或服务器完全不可达');
        } else {
            console.log('  请求配置错误');
        }
        
        if (error.code) {
            console.log(`  错误代码: ${error.code}`);
            
            switch (error.code) {
                case 'ECONNREFUSED':
                    console.log('  🔍 连接被拒绝 - 服务器可能未运行或端口错误');
                    break;
                case 'ENOTFOUND':
                    console.log('  🔍 域名解析失败 - 检查URL是否正确');
                    break;
                case 'ETIMEDOUT':
                    console.log('  🔍 请求超时 - 服务器响应太慢');
                    break;
                case 'ECONNRESET':
                    console.log('  🔍 连接重置 - 服务器主动断开连接');
                    break;
            }
        }
    }

    // 4. 测试简化请求
    console.log('\n🧪 测试简化请求...');
    try {
        const simpleRequest = {
            model: API_CONFIG.model,
            messages: [
                {
                    role: 'user',
                    content: '你好，请简单回复一下。'
                }
            ],
            max_tokens: 50
        };

        const simpleResponse = await axios.post(
            `${API_CONFIG.url}/v1/chat/completions`,
            simpleRequest,
            {
                headers: requestHeaders,
                timeout: 30000,
                validateStatus: () => true
            }
        );

        console.log(`简化请求状态: ${simpleResponse.status}`);
        if (simpleResponse.status === 200) {
            console.log('✅ 简化请求成功，问题可能在于请求内容过于复杂');
        } else {
            console.log('❌ 简化请求也失败，问题在于基础配置');
        }

    } catch (simpleError) {
        console.log('❌ 简化请求异常:', simpleError.message);
    }

    // 5. 建议解决方案
    console.log('\n💡 建议解决方案:');
    console.log('1. 检查API服务商状态页面，确认服务是否正常');
    console.log('2. 验证API Key是否有效且有足够余额');
    console.log('3. 确认模型名称是否正确');
    console.log('4. 尝试减少请求内容长度');
    console.log('5. 检查网络连接和防火墙设置');
    console.log('6. 联系API服务商技术支持');
    
    if (API_CONFIG.model.includes('gemini')) {
        console.log('\n🔧 Gemini模型特殊说明:');
        console.log('- Gemini可能需要不同的API端点');
        console.log('- 检查是否需要使用Google AI Studio的API格式');
        console.log('- 确认API Key是否为Google AI的有效密钥');
    }
}

// 运行调试
if (require.main === module) {
    debugAPI502Error().then(() => {
        console.log('\n调试完成。');
    }).catch(error => {
        console.error('调试过程中发生错误:', error);
    });
}

module.exports = { debugAPI502Error };
