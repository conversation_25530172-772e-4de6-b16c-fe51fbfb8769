# OneBot11 文件接收功能说明

## 📋 功能概述

OneBot11适配器现在支持完整的文件接收和管理功能，基于NapCat API文档实现，可以自动接收、下载和管理各种类型的文件。

## 🔧 插件组成

### 1. FileReceiverPlugin (文件接收插件)
- **文件**: `file_receiver_plugin.js`
- **功能**: 自动检测和处理消息中的文件、图片、视频、语音
- **优先级**: 70 (高优先级，优先处理文件消息)

### 2. FileManagerPlugin (文件管理插件)
- **文件**: `file_manager_plugin.js`
- **功能**: 提供文件查看、搜索、统计和管理功能
- **优先级**: 50 (中等优先级)

## 📁 支持的文件类型

### 🖼️ 图片文件
- **类型**: `image`
- **格式**: jpg, jpeg, png, gif, webp, bmp
- **功能**: 自动下载、保存文件信息、生成处理报告

### 📄 普通文件
- **类型**: `file`
- **格式**: txt, pdf, doc, docx, zip, rar, mp3, mp4等
- **功能**: 自动下载、大小检查、类型验证

### 🎥 视频文件
- **类型**: `video`
- **格式**: mp4, avi, mov等
- **功能**: 自动下载、大小限制检查

### 🎵 语音文件
- **类型**: `record`
- **格式**: mp3, wav, amr等
- **功能**: 自动下载、音频文件处理

## ⚙️ 配置参数

### 文件下载配置
```javascript
downloadConfig = {
    downloadDir: './downloads',           // 下载目录
    maxFileSize: 100 * 1024 * 1024,     // 最大文件大小 100MB
    allowedImageTypes: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'],
    allowedFileTypes: ['.txt', '.pdf', '.doc', '.docx', '.zip', '.rar', '.mp3', '.mp4'],
    autoDownload: true,                   // 是否自动下载文件
    saveFileInfo: true                    // 是否保存文件信息到JSON
}
```

## 📂 目录结构

```
OneBot11/
├── downloads/                    # 下载根目录
│   ├── image/                   # 图片文件
│   ├── file/                    # 普通文件
│   ├── video/                   # 视频文件
│   ├── record/                  # 语音文件
│   └── file_info.json          # 文件信息记录
└── plugins/
    ├── file_receiver_plugin.js  # 文件接收插件
    └── file_manager_plugin.js   # 文件管理插件
```

## 🎯 使用方法

### 自动文件接收
当用户在群聊或私聊中发送文件时，系统会自动：

1. **检测文件类型** - 识别图片、文件、视频、语音
2. **下载文件** - 自动下载到对应的类型目录
3. **保存信息** - 记录文件元数据到JSON文件
4. **发送报告** - 向用户反馈处理结果

### 文件管理命令

#### 📋 查看文件列表
```
发送: 文件列表
功能: 显示最近下载的10个文件
```

#### 📊 查看文件统计
```
发送: 文件统计
功能: 显示详细的文件统计信息，包括：
- 总文件数和大小
- 各类型文件分布
- 下载成功率
- 磁盘使用情况
```

#### 🔍 搜索文件
```
发送: 搜索文件 [关键词]
示例: 搜索文件 图片
功能: 根据文件名、类型、发送者搜索文件
```

#### 🗑️ 清理文件
```
发送: 清理文件 确认
功能: 删除所有下载的文件和记录
注意: 需要确认参数，防止误操作
```

#### ❓ 查看帮助
```
发送: 文件详情
功能: 显示文件管理功能的详细说明
```

## 📝 文件信息记录

每个处理的文件都会记录以下信息：

```json
{
    "type": "image",                    // 文件类型
    "file": "image_123.jpg",           // 文件标识
    "url": "https://...",              // 下载URL
    "name": "我的图片.jpg",            // 文件名
    "fileSize": "1024000",             // 文件大小(字节)
    "timestamp": "2025-07-05T17:30:00.000Z", // 接收时间
    "sender": {                        // 发送者信息
        "user_id": 123456,
        "nickname": "用户昵称",
        "card": "群名片"
    },
    "messageId": 987654321,            // 消息ID
    "downloadPath": "./downloads/image/image_123.jpg", // 本地路径
    "downloadSuccess": true            // 下载是否成功
}
```

## 🚀 处理流程

### 文件接收流程
1. **消息监听** - 监听包含文件的消息
2. **类型识别** - 根据segment.type识别文件类型
3. **信息提取** - 提取文件URL、大小、名称等信息
4. **大小检查** - 检查文件是否超过大小限制
5. **自动下载** - 使用axios下载文件到本地
6. **信息保存** - 将文件信息保存到JSON记录
7. **反馈报告** - 向用户发送处理结果

### 文件管理流程
1. **命令识别** - 识别文件管理相关命令
2. **数据读取** - 从JSON文件读取文件记录
3. **数据处理** - 根据命令类型处理数据
4. **结果展示** - 格式化并发送结果给用户

## ⚠️ 注意事项

### 安全考虑
- 文件名会进行安全处理，移除危险字符
- 文件大小有限制，默认最大100MB
- 只下载允许的文件类型

### 性能考虑
- 大文件下载有30秒超时限制
- 文件信息记录最多保存1000条
- 自动创建类型子目录避免文件混乱

### 错误处理
- 下载失败会记录错误信息
- 文件过大会跳过下载但保留记录
- 网络错误会重试机制

## 🔧 自定义配置

可以通过修改插件代码来调整配置：

```javascript
// 修改最大文件大小
maxFileSize: 200 * 1024 * 1024,  // 改为200MB

// 修改下载目录
downloadDir: './my_files',

// 添加允许的文件类型
allowedFileTypes: [..., '.xlsx', '.ppt'],

// 关闭自动下载
autoDownload: false,
```

## 📞 技术支持

如果遇到问题，请检查：
1. 下载目录权限是否正确
2. 网络连接是否正常
3. 磁盘空间是否充足
4. NapCat API是否正常工作

## 🎉 功能特色

- ✅ **全自动处理** - 无需手动操作，自动识别和下载
- ✅ **多类型支持** - 支持图片、文件、视频、语音
- ✅ **智能分类** - 按类型自动分目录存储
- ✅ **详细记录** - 完整的文件元数据记录
- ✅ **便捷管理** - 丰富的查看和管理命令
- ✅ **安全可靠** - 文件大小限制和类型检查
- ✅ **性能优化** - 超时控制和错误处理
