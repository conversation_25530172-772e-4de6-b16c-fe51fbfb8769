const fs = require('fs').promises;
const path = require('path');


// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

// 获取 PluginManager 注入的项目基础路径环境变量
const projectBasePath = process.env.PROJECT_BASE_PATH;
const dailyNoteRootPath = projectBasePath ? path.join(projectBasePath, 'dailynote') : path.join(__dirname, '..', '..', 'dailynote'); // Fallback if env var not set

const DEBUG_MODE = (process.env.DebugMode || "false").toLowerCase() === "true";

function debugLog(message, ...args) {
    if (DEBUG_MODE) {
        logger.debug('日记获取', message, ...args);
    }
}

async function getAllCharacterDiaries() {
    const allDiaries = {};
    debugLog(`Starting diary scan in: ${dailyNoteRootPath}`);

    try {
        const characterDirs = await fs.readdir(dailyNoteRootPath, { withFileTypes: true });

        for (const dirEntry of characterDirs) {
            if (dirEntry.isDirectory()) {
                const characterName = dirEntry.name;
                const characterDirPath = path.join(dailyNoteRootPath, characterName);
                let characterDiaryContent = '';
                debugLog(`Scanning directory for character: ${characterName}`);

                try {
                    const files = await fs.readdir(characterDirPath);
                    const txtFiles = files.filter(file => file.toLowerCase().endsWith('.txt')).sort();
                    debugLog(`Found ${txtFiles.length} .txt files for ${characterName}`);

                    if (txtFiles.length > 0) {
                        const fileContents = await Promise.all(
                            txtFiles.map(async (file) => {
                                const filePath = path.join(characterDirPath, file);
                                try {
                                    const content = await fs.readFile(filePath, 'utf-8');
                                    debugLog(`Read content from ${file} (length: ${content.length})`);
                                    return content;
                                } catch (readErr) {
                                    logger.error('日记获取', `读取日记文件 \${filePath} 时出错`, readErr.message);
                                    return `[读取时出错 file: ${file}]`; // Include error marker in content
                                }
                            })
                        );
                        // Combine content with separators, similar to server.js logic
                        characterDiaryContent = fileContents.join('\n\n---\n\n');
                    } else {
                         characterDiaryContent = `[${characterName}日记本内容为空]`; // Explicitly state if empty
                         debugLog(`No .txt files found for ${characterName}, setting content to empty marker.`);
                    }
                } catch (charDirError) {
                     logger.error('日记获取', `读取角色目录 \${characterDirPath} 时出错`, charDirError.message);
                     characterDiaryContent = `[读取时出错 ${characterName}'s diary directory]`;
                }
                allDiaries[characterName] = characterDiaryContent;
            }
        }
        debugLog(`Finished diary scan. Found diaries for ${Object.keys(allDiaries).length} characters.`);

    } catch (error) {
        if (error.code === 'ENOENT') {
            logger.error('日记获取', `在 \${dailyNoteRootPath} 找不到日记根目录`);
        } else {
            logger.error('日记获取', `读取日记根目录 \${dailyNoteRootPath} 时出错`, error.message);
        }
        // Output empty JSON if root directory fails
        return '{}';
    }

    // Output the result as a JSON string to stdout
    return JSON.stringify(allDiaries);
}

(async () => {
    try {
        const resultJsonString = await getAllCharacterDiaries();
        process.stdout.write(resultJsonString); // Write JSON string to stdout
        debugLog('成功写入 diary JSON to stdout.');
    } catch (e) {
        logger.error('日记获取', '执行期间发生致命错误', e);
        // Output empty JSON on fatal error to prevent breaking PluginManager
        process.stdout.write('{}');
        process.exit(1); // Exit with error code
    }
})();