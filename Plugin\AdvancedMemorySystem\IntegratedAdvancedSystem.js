/**
 * 集成高级系统
 * 整合所有优化后的组件：模因认知、约束机制、大脑皮层模拟、世界树背景
 * 提供统一的接口和协调机制
 */

const AdvancedMemeticCognitionSystem = require('./AdvancedMemeticCognitionSystem');
const AdvancedConstraintSystem = require('./AdvancedConstraintSystem');
const CorticalSimulationSystem = require('./CorticalSimulationSystem');
const AdvancedWorldTreeSystem = require('./AdvancedWorldTreeSystem');
const AdvancedEmotionalStabilizationSystem = require('./AdvancedEmotionalStabilizationSystem');

class IntegratedAdvancedSystem {
    constructor(config, logger, openaiService, database) {
        this.config = config;
        this.logger = logger;
        this.openaiService = openaiService;
        this.database = database;
        
        // 初始化各个子系统
        this.memeticSystem = new AdvancedMemeticCognitionSystem(config, logger, openaiService);
        this.constraintSystem = new AdvancedConstraintSystem(config, logger);
        this.corticalSystem = new CorticalSimulationSystem(config, logger, openaiService);
        this.worldTreeSystem = new AdvancedWorldTreeSystem(config, logger, openaiService);
        this.stabilizationSystem = new AdvancedEmotionalStabilizationSystem(config, logger);
        
        // 系统状态
        this.systemState = {
            isInitialized: false,
            lastUpdate: null,
            processingLoad: 0,
            systemHealth: 1.0,
            integrationLevel: 0.8
        };
        
        // 性能监控
        this.performanceMetrics = {
            processingTime: [],
            memoryUsage: [],
            errorRate: 0,
            successRate: 1.0,
            throughput: 0
        };
        
        // 系统配置
        this.systemConfig = {
            maxProcessingTime: 30000, // 30秒
            maxMemoryUsage: 500, // MB
            errorThreshold: 0.1,
            healthCheckInterval: 60000, // 1分钟
            autoOptimization: true
        };
    }

    /**
     * 初始化集成系统
     */
    async initialize() {
        try {
            this.logger.info('集成系统', '开始初始化高级集成系统');

            // 检查依赖
            await this.checkDependencies();
            
            // 初始化各子系统
            await this.initializeSubsystems();
            
            // 建立系统间连接
            await this.establishInterSystemConnections();
            
            // 启动健康监控
            this.startHealthMonitoring();
            
            this.systemState.isInitialized = true;
            this.systemState.lastUpdate = new Date().toISOString();
            
            this.logger.success('集成系统', '✅ 高级集成系统初始化完成');
            return { success: true, message: '系统初始化成功' };

        } catch (error) {
            this.logger.error('集成系统', '初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 主要处理入口 - 优化版异步并行处理
     */
    async processAdvancedPsychologicalAnalysis(params) {
        const startTime = Date.now();

        try {
            this.logger.info('集成系统', '开始高级心理分析处理（优化版）');

            // 验证输入参数
            this.validateInputParameters(params);

            // 获取当前状态
            const currentStates = await this.getCurrentStates(params.userId, params.personaName);

            // 🚀 性能优化：使用批量分析替代多次调用
            const batchAnalysisPromise = this.openaiService.batchComprehensiveAnalysis(
                params.userMessage,
                params.aiResponse,
                currentStates,
                params.context
            );

            // 🚀 异步并行执行：大脑皮层模拟（本地计算）
            const corticalProcessingPromise = this.corticalSystem.simulateCorticalProcessing(
                {
                    userMessage: params.userMessage,
                    aiResponse: params.aiResponse,
                    emotionalContext: currentStates.emotion
                },
                currentStates,
                params.context
            );

            // 等待批量分析和皮层模拟完成
            const [batchResults, corticalProcessing] = await Promise.all([
                batchAnalysisPromise,
                corticalProcessingPromise
            ]);

            this.logger.info('集成系统', '批量分析和皮层模拟完成');

            // 🔄 整合批量分析结果
            const integratedAnalysis = this.integrateBatchResults(batchResults, corticalProcessing);

            // 🚀 异步并行执行：约束处理和优化
            const constraintPromise = this.constraintSystem.applyAdvancedConstraints(
                integratedAnalysis.changes,
                currentStates,
                {
                    userMessage: params.userMessage,
                    aiResponse: params.aiResponse,
                    batchResults: batchResults,
                    corticalState: corticalProcessing
                }
            );

            const optimizationPromise = this.optimizeSystemInteractions(
                integratedAnalysis.changes,
                corticalProcessing,
                integratedAnalysis.memetic,
                integratedAnalysis.worldTree
            );

            // 等待约束和优化完成
            const [constrainedResults, optimizedResults] = await Promise.all([
                constraintPromise,
                optimizationPromise
            ]);

            // 🔧 应用高级情绪稳态调节
            const stabilizedResults = await this.stabilizationSystem.applyAdvancedStabilization(
                constrainedResults.constrained_changes,
                currentStates,
                integratedAnalysis
            );

            // 💾 异步保存状态
            const savePromise = this.saveSystemStates(params.userId, params.personaName, stabilizedResults);

            // 📊 性能监控
            this.updatePerformanceMetrics(startTime);

            const result = {
                success: true,
                processing_time: Date.now() - startTime,
                batch_analysis: batchResults,
                cortical_processing: corticalProcessing,
                integrated_analysis: integratedAnalysis,
                constrained_results: constrainedResults.constrained_changes,
                optimized_results: optimizedResults,
                stabilized_results: stabilizedResults,
                system_health: this.systemState.systemHealth,
                performance_metrics: this.getPerformanceSnapshot(),
                recommendations: this.generateRecommendations(stabilizedResults)
            };

            // 异步完成保存
            savePromise.catch(error => {
                this.logger.error('集成系统', '状态保存失败:', error.message);
            });

            this.logger.success('集成系统', `高级心理分析完成，耗时: ${Date.now() - startTime}ms`);
            return result;

        } catch (error) {
            this.logger.error('集成系统', '高级心理分析失败:', error.message);
            this.updateErrorMetrics();
            throw error;
        }
    }

    /**
     * 整合批量分析结果
     */
    integrateBatchResults(batchResults, corticalProcessing) {
        try {
            const {
                emotion_analysis,
                stress_analysis,
                affinity_analysis,
                memetic_analysis,
                world_tree_analysis
            } = batchResults;

            // 计算综合变化
            const changes = {
                emotion_change: this.calculateEmotionChangeFromBatch(emotion_analysis, corticalProcessing),
                stress_change: this.calculateStressChangeFromBatch(stress_analysis, corticalProcessing),
                affinity_change: this.calculateAffinityChangeFromBatch(affinity_analysis, corticalProcessing),
                meme_change: memetic_analysis.memetic_influence_delta || 0
            };

            return {
                changes,
                emotion: emotion_analysis,
                stress: stress_analysis,
                affinity: affinity_analysis,
                memetic: memetic_analysis,
                worldTree: world_tree_analysis,
                cortical: corticalProcessing
            };

        } catch (error) {
            this.logger.error('集成系统', '批量结果整合失败:', error.message);
            throw error;
        }
    }

    /**
     * 从批量分析计算情绪变化
     */
    calculateEmotionChangeFromBatch(emotionAnalysis, corticalProcessing) {
        try {
            let emotionChange = 0;

            // 基于VAD模型计算情绪变化
            const valence = emotionAnalysis.valence || 0;
            const arousal = emotionAnalysis.arousal || 0;
            const intensity = emotionAnalysis.intensity || 0.5;

            // VAD到情绪值的转换
            const emotionValue = valence * intensity * 50; // 转换到[-50, 50]范围

            // 基于唤醒度调整变化幅度
            const arousalMultiplier = 0.5 + arousal * 0.5; // [0.5, 1.0]
            emotionChange = emotionValue * arousalMultiplier;

            // 结合大脑皮层处理结果
            if (corticalProcessing.neurotransmitters) {
                const serotoninLevel = corticalProcessing.neurotransmitters.serotonin?.level || 0.5;
                const dopamineLevel = corticalProcessing.neurotransmitters.dopamine?.level || 0.5;

                // 神经递质对情绪的影响
                const neurotransmitterEffect = (serotoninLevel + dopamineLevel - 1.0) * 2;
                emotionChange += neurotransmitterEffect;
            }

            return emotionChange;

        } catch (error) {
            this.logger.error('集成系统', '情绪变化计算失败:', error.message);
            return 0;
        }
    }

    /**
     * 从批量分析计算压力变化
     */
    calculateStressChangeFromBatch(stressAnalysis, corticalProcessing) {
        try {
            let stressChange = stressAnalysis.stress_delta || 0;

            // 基于压力因素调整
            if (stressAnalysis.stress_factors) {
                let factorSum = 0;
                for (const factor of stressAnalysis.stress_factors) {
                    const severity = factor.severity || 0.5;
                    const durationMultiplier = this.getDurationMultiplier(factor.duration);
                    factorSum += severity * durationMultiplier;
                }
                stressChange += factorSum * 0.5;
            }

            // 基于神经可塑性响应调整
            if (stressAnalysis.neuroplasticity_response) {
                const adaptationRate = stressAnalysis.neuroplasticity_response.adaptation_rate || 0.5;
                const resilienceFactor = stressAnalysis.neuroplasticity_response.resilience_factor || 1.0;

                // 适应性越强，压力变化越小
                stressChange *= (1 - adaptationRate * 0.3) * (1 / resilienceFactor);
            }

            // 结合大脑皮层认知负荷
            if (corticalProcessing.cognitive_load) {
                const cognitiveLoadEffect = (corticalProcessing.cognitive_load - 0.5) * 1.5;
                stressChange += cognitiveLoadEffect;
            }

            return stressChange;

        } catch (error) {
            this.logger.error('集成系统', '压力变化计算失败:', error.message);
            return 0;
        }
    }

    /**
     * 从批量分析计算好感度变化
     */
    calculateAffinityChangeFromBatch(affinityAnalysis, corticalProcessing) {
        try {
            let affinityChange = affinityAnalysis.affinity_delta || 0;

            // 基于互动质量调整
            const qualityMultipliers = {
                'excellent': 1.5,
                'good': 1.2,
                'neutral': 1.0,
                'poor': 0.7,
                'terrible': 0.4
            };

            const qualityMultiplier = qualityMultipliers[affinityAnalysis.interaction_quality] || 1.0;
            affinityChange *= qualityMultiplier;

            // 基于信任水平调整
            const trustLevel = affinityAnalysis.trust_level || 0.5;
            const trustBonus = (trustLevel - 0.5) * 0.5;
            affinityChange += trustBonus;

            // 基于情感共鸣调整
            const emotionalResonance = affinityAnalysis.emotional_resonance || 0.5;
            const resonanceBonus = (emotionalResonance - 0.5) * 0.3;
            affinityChange += resonanceBonus;

            // 结合大脑皮层处理效率
            if (corticalProcessing.processing_efficiency) {
                const efficiencyBonus = (corticalProcessing.processing_efficiency - 0.5) * 0.4;
                affinityChange += efficiencyBonus;
            }

            return affinityChange;

        } catch (error) {
            this.logger.error('集成系统', '好感度变化计算失败:', error.message);
            return 0;
        }
    }

    /**
     * 获取持续时间乘数
     */
    getDurationMultiplier(duration) {
        const multipliers = {
            '瞬时': 0.5,
            '短期': 0.8,
            '中期': 1.2,
            '长期': 1.5
        };
        return multipliers[duration] || 1.0;
    }

    /**
     * 获取性能快照
     */
    getPerformanceSnapshot() {
        const recentTimes = this.performanceMetrics.processingTime.slice(-10);
        const avgTime = recentTimes.length > 0 ?
            recentTimes.reduce((sum, time) => sum + time, 0) / recentTimes.length : 0;

        return {
            average_processing_time: avgTime,
            success_rate: this.performanceMetrics.successRate,
            error_rate: this.performanceMetrics.errorRate,
            system_health: this.systemState.systemHealth,
            processing_load: this.systemState.processingLoad
        };
    }

    /**
     * 计算综合变化
     */
    async calculateIntegratedChanges(corticalProcessing, memeticAnalysis, worldTreeAnalysis, currentStates) {
        try {
            const changes = {};
            
            // 情绪变化综合计算
            changes.emotion_change = this.calculateEmotionChange(
                corticalProcessing,
                memeticAnalysis,
                worldTreeAnalysis,
                currentStates.emotion
            );
            
            // 压力变化综合计算
            changes.stress_change = this.calculateStressChange(
                corticalProcessing,
                memeticAnalysis,
                worldTreeAnalysis,
                currentStates.stress
            );
            
            // 好感度变化综合计算
            changes.affinity_change = this.calculateAffinityChange(
                corticalProcessing,
                memeticAnalysis,
                worldTreeAnalysis,
                currentStates.affinity
            );
            
            // 模因影响力变化
            changes.meme_change = memeticAnalysis.memetic_influence_delta || 0;
            
            return changes;

        } catch (error) {
            this.logger.error('集成系统', '综合变化计算失败:', error.message);
            return { emotion_change: 0, stress_change: 0, affinity_change: 0, meme_change: 0 };
        }
    }

    /**
     * 计算情绪变化
     */
    calculateEmotionChange(corticalProcessing, memeticAnalysis, worldTreeAnalysis, currentEmotion) {
        let emotionChange = 0;
        
        // 大脑皮层影响 (40%)
        if (corticalProcessing.neurotransmitters) {
            const serotoninEffect = (corticalProcessing.neurotransmitters.serotonin?.level || 0.5) - 0.5;
            const dopamineEffect = (corticalProcessing.neurotransmitters.dopamine?.level || 0.5) - 0.5;
            emotionChange += (serotoninEffect * 3 + dopamineEffect * 2) * 0.4;
        }
        
        // 模因认知影响 (30%)
        if (memeticAnalysis.network_metrics) {
            const memeComplexity = memeticAnalysis.network_metrics.complexity || 0;
            const memeEmergence = memeticAnalysis.network_metrics.emergence || 0;
            emotionChange += (memeComplexity + memeEmergence) * 1.5 * 0.3;
        }
        
        // 世界树背景影响 (30%)
        if (worldTreeAnalysis.background_influence) {
            const emotionalInfluence = worldTreeAnalysis.background_influence.emotional_influence || 0;
            emotionChange += emotionalInfluence * 5 * 0.3;
        }
        
        // 应用当前状态调节
        const currentValue = currentEmotion?.emotion_value || 0;
        const dampingFactor = 1 / (1 + Math.abs(currentValue) * 0.01);
        
        return emotionChange * dampingFactor;
    }

    /**
     * 计算压力变化
     */
    calculateStressChange(corticalProcessing, memeticAnalysis, worldTreeAnalysis, currentStress) {
        let stressChange = 0;
        
        // 大脑皮层影响 - 认知负荷
        if (corticalProcessing.cognitive_load) {
            const cognitiveLoad = corticalProcessing.cognitive_load;
            stressChange += (cognitiveLoad - 0.5) * 2;
        }
        
        // 神经递质影响
        if (corticalProcessing.neurotransmitters) {
            const norepinephrineLevel = corticalProcessing.neurotransmitters.norepinephrine?.level || 0.5;
            const gabaLevel = corticalProcessing.neurotransmitters.gaba?.level || 0.5;
            stressChange += (norepinephrineLevel - gabaLevel) * 1.5;
        }
        
        // 模因网络稳定性影响
        if (memeticAnalysis.network_metrics) {
            const stability = memeticAnalysis.network_metrics.stability || 0.5;
            stressChange += (0.5 - stability) * 2;
        }
        
        // 世界树叙事张力影响
        if (worldTreeAnalysis.narrative_context) {
            const conflictSources = worldTreeAnalysis.narrative_context.conflictSources || [];
            stressChange += conflictSources.length * 0.3;
        }
        
        return stressChange;
    }

    /**
     * 计算好感度变化
     */
    calculateAffinityChange(corticalProcessing, memeticAnalysis, worldTreeAnalysis, currentAffinity) {
        let affinityChange = 0;
        
        // 大脑皮层处理效率影响
        if (corticalProcessing.processing_efficiency) {
            const efficiency = corticalProcessing.processing_efficiency;
            affinityChange += (efficiency - 0.5) * 1.5;
        }
        
        // 全局工作空间整合影响
        if (corticalProcessing.global_workspace) {
            const integration = corticalProcessing.global_workspace.global_availability || 0.5;
            affinityChange += (integration - 0.5) * 1.0;
        }
        
        // 模因认知模式影响
        if (memeticAnalysis.cognitive_patterns) {
            const positivePatterns = memeticAnalysis.cognitive_patterns.filter(p => 
                p.pattern_name.includes('创造') || p.pattern_name.includes('理解')
            ).length;
            affinityChange += positivePatterns * 0.3;
        }
        
        // 世界树角色兼容性影响
        if (worldTreeAnalysis.character_role) {
            const roleConfidence = worldTreeAnalysis.confidence || 0.5;
            affinityChange += (roleConfidence - 0.5) * 0.8;
        }
        
        return affinityChange;
    }

    /**
     * 系统间协调优化
     */
    async optimizeSystemInteractions(constrainedResults, corticalProcessing, memeticAnalysis, worldTreeAnalysis) {
        try {
            const optimized = { ...constrainedResults };
            
            // 检查系统间一致性
            const consistencyScore = this.calculateSystemConsistency(
                corticalProcessing,
                memeticAnalysis,
                worldTreeAnalysis
            );
            
            // 如果一致性较低，应用协调机制
            if (consistencyScore < 0.7) {
                optimized.emotion_change *= 0.8; // 减少变化幅度
                optimized.stress_change *= 0.8;
                optimized.affinity_change *= 0.8;
                optimized.meme_change *= 0.8;
                
                this.logger.warn('集成系统', `系统一致性较低 (${consistencyScore.toFixed(2)})，应用协调机制`);
            }
            
            // 应用系统间反馈循环
            optimized.feedback_loops = this.calculateFeedbackLoops(
                corticalProcessing,
                memeticAnalysis,
                worldTreeAnalysis
            );
            
            // 计算系统稳定性
            optimized.system_stability = this.calculateSystemStability(optimized);
            
            return optimized;

        } catch (error) {
            this.logger.error('集成系统', '系统优化失败:', error.message);
            return constrainedResults;
        }
    }

    /**
     * 计算系统一致性
     */
    calculateSystemConsistency(corticalProcessing, memeticAnalysis, worldTreeAnalysis) {
        let consistencyScore = 1.0;
        
        // 检查皮层激活与模因状态的一致性
        const corticalActivation = Object.values(corticalProcessing.cortical_activation || {});
        const avgCorticalActivation = corticalActivation.reduce((sum, val) => sum + val, 0) / corticalActivation.length;
        const memeInfluence = memeticAnalysis.memetic_influence_delta || 0;
        
        const corticalMemeConsistency = 1 - Math.abs(avgCorticalActivation - (memeInfluence + 0.5));
        consistencyScore *= corticalMemeConsistency;
        
        // 检查世界树与认知状态的一致性
        const worldComplexity = worldTreeAnalysis.thematic_resonance || 0.5;
        const cognitiveComplexity = memeticAnalysis.network_metrics?.complexity || 0.5;
        
        const worldCognitiveConsistency = 1 - Math.abs(worldComplexity - cognitiveComplexity);
        consistencyScore *= worldCognitiveConsistency;
        
        return Math.max(0, Math.min(1, consistencyScore));
    }

    /**
     * 验证输入参数
     */
    validateInputParameters(params) {
        if (!params.userId || !params.userMessage || !params.aiResponse) {
            throw new Error('缺少必要的输入参数');
        }
        
        if (typeof params.userMessage !== 'string' || typeof params.aiResponse !== 'string') {
            throw new Error('消息参数必须是字符串类型');
        }
        
        if (params.userMessage.length > 10000 || params.aiResponse.length > 10000) {
            throw new Error('消息长度超过限制');
        }
    }

    /**
     * 获取当前状态
     */
    async getCurrentStates(userId, personaName) {
        try {
            // 这里应该从数据库获取当前状态
            // 暂时返回默认状态
            return {
                emotion: { emotion_value: 0 },
                stress: { stress_value: 0 },
                affinity: { affinity_value: 50 },
                meme: { memetic_influence: 0.5, evolution_stage: '发展' }
            };
        } catch (error) {
            this.logger.error('集成系统', '获取当前状态失败:', error.message);
            return {
                emotion: { emotion_value: 0 },
                stress: { stress_value: 0 },
                affinity: { affinity_value: 50 },
                meme: { memetic_influence: 0.5 }
            };
        }
    }

    /**
     * 更新性能指标
     */
    updatePerformanceMetrics(startTime) {
        const processingTime = Date.now() - startTime;
        this.performanceMetrics.processingTime.push(processingTime);
        
        // 保持最近100次记录
        if (this.performanceMetrics.processingTime.length > 100) {
            this.performanceMetrics.processingTime.shift();
        }
        
        // 更新成功率
        this.performanceMetrics.successRate = 
            (this.performanceMetrics.successRate * 0.9) + (1 * 0.1);
    }

    /**
     * 生成建议
     */
    generateRecommendations(optimizedResults) {
        const recommendations = [];
        
        if (optimizedResults.system_stability < 0.7) {
            recommendations.push('系统稳定性较低，建议减少处理频率');
        }
        
        if (Math.abs(optimizedResults.emotion_change) > 3) {
            recommendations.push('情绪变化较大，建议关注用户状态');
        }
        
        if (optimizedResults.stress_change > 2) {
            recommendations.push('压力水平上升，建议采取缓解措施');
        }
        
        return recommendations;
    }
}

module.exports = IntegratedAdvancedSystem;
