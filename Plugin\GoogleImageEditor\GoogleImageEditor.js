// Plugin/GoogleImageEditor/GoogleImageEditor.js - 谷歌图像编辑VCP插件
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');
const mime = require('mime-types');

class GoogleImageEditor {
    constructor() {
        this.name = 'GoogleImageEditor';
        this.description = '谷歌图像编辑插件，支持Gemini模型的图像编辑功能，可处理markdown格式的图片链接';
        this.version = '1.0.0';
        this.config = this.loadConfig();
    }

    /**
     * 加载插件配置
     */
    loadConfig() {
        const configPath = path.join(__dirname, 'config.env');
        const config = {
            // 核心配置
            GOOGLE_API_KEY: process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY,
            GOOGLE_PROXY_URL: process.env.GOOGLE_PROXY_URL || '',
            GEMINI_MODEL: process.env.GEMINI_MODEL || 'gemini-2.0-flash-preview-image-generation',

            // 服务器配置 (自动检测)
            PROJECT_BASE_PATH: process.env.PROJECT_BASE_PATH || path.resolve(__dirname, '../..'),

            // 调试模式
            DEBUG_MODE: process.env.DEBUG_MODE === 'true'
        };

        // 如果存在配置文件，加载配置
        if (fs.existsSync(configPath)) {
            require('dotenv').config({ path: configPath });
            // 重新读取环境变量
            Object.keys(config).forEach(key => {
                if (process.env[key] !== undefined) {
                    if (key === 'MAX_IMAGES') {
                        config[key] = parseInt(process.env[key]);
                    } else if (key === 'DEBUG_MODE') {
                        config[key] = process.env[key] === 'true';
                    } else {
                        config[key] = process.env[key];
                    }
                }
            });
        }

        return config;
    }

    /**
     * 创建axios实例
     */
    createAxiosInstance() {
        const axiosConfig = {
            timeout: 180000,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        // 如果配置了代理，使用代理，否则使用默认的谷歌API地址
        if (this.config.GOOGLE_PROXY_URL) {
            axiosConfig.baseURL = this.config.GOOGLE_PROXY_URL;
        } else {
            axiosConfig.baseURL = 'https://generativelanguage.googleapis.com';
        }

        return axios.create(axiosConfig);
    }

    /**
     * 执行插件主要功能
     */
    async execute(inputString) {
        try {
            const input = JSON.parse(inputString);
            
            // 验证必需参数
            if (!input.prompt) {
                throw new Error('缺少必需参数: prompt');
            }

            // 验证API密钥
            if (!this.config.GOOGLE_API_KEY) {
                throw new Error('未配置Google API密钥 (GOOGLE_API_KEY 或 GEMINI_API_KEY)');
            }

            // 只支持编辑模式
            let result = await this.editImage(input);

            return JSON.stringify({
                status: 'success',
                message: '图像处理完成',
                data: result,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            this.logError('执行失败', error);
            return JSON.stringify({
                status: 'error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 生成图像 (文本到图像)
     */
    async generateImage(input) {
        return await this.generateWithGemini(input);
    }

    /**
     * 编辑图像 (图像+文本到图像)
     */
    async editImage(input) {
        // 提取图片信息
        const imageInfo = await this.extractImageFromInput(input);

        // 使用提取的图片信息调用Gemini
        const editInput = {
            ...input,
            image_data: imageInfo.base64Data,
            image_mime_type: imageInfo.mimeType
        };

        return await this.editWithGemini(editInput);
    }

    /**
     * 从输入中提取图片信息
     */
    async extractImageFromInput(input) {
        let imagePath = null;

        // 1. 检查是否直接提供了image_data
        if (input.image_data) {
            return {
                base64Data: input.image_data,
                mimeType: input.image_mime_type || 'image/png'
            };
        }

        // 2. 检查是否提供了image_path
        if (input.image_path) {
            imagePath = input.image_path;
        }

        // 3. 从prompt中提取markdown格式的图片链接
        if (!imagePath && input.prompt) {
            const markdownMatch = input.prompt.match(/!\[.*?\]\((.*?)\)/);
            if (markdownMatch) {
                imagePath = markdownMatch[1];
                this.logDebug('从markdown中提取图片路径', { path: imagePath });
            }
        }

        // 4. 检查是否提供了image_url
        if (!imagePath && input.image_url) {
            imagePath = input.image_url;
        }

        if (!imagePath) {
            throw new Error('未找到图片路径。请在prompt中包含markdown格式的图片链接，或提供image_path/image_url参数');
        }

        // 获取图片的base64数据和MIME类型
        const base64Data = await this.getImageAsBase64(imagePath);
        const mimeType = this.getImageMimeType(imagePath);

        return {
            base64Data,
            mimeType,
            originalPath: imagePath
        };
    }

    /**
     * 获取图片的base64数据（本地文件或网络下载）
     */
    async getImageAsBase64(imagePath) {
        if (this.isLocalPath(imagePath)) {
            // 本地文件直接读取
            return this.readLocalFileAsBase64(imagePath);
        } else {
            // 网络文件下载
            return this.downloadAndConvertToBase64(imagePath);
        }
    }

    /**
     * 检查是否为本地路径
     */
    isLocalPath(url) {
        // 检查是否为绝对路径（Windows: C:\ 或 D:\ 等，Linux/Mac: /）
        return /^[A-Za-z]:\\/.test(url) || url.startsWith('/');
    }

    /**
     * 读取本地文件为base64
     */
    readLocalFileAsBase64(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }

            const buffer = fs.readFileSync(filePath);
            return buffer.toString('base64');
        } catch (error) {
            throw new Error(`读取本地文件失败: ${error.message}`);
        }
    }

    /**
     * 下载文件并转换为base64
     */
    async downloadAndConvertToBase64(url) {
        try {
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'arraybuffer',
                timeout: 60000,
                headers: {
                    'User-Agent': 'GoogleImageEditor/1.0'
                }
            });

            const buffer = Buffer.from(response.data);
            return buffer.toString('base64');

        } catch (error) {
            throw new Error(`下载文件失败: ${error.message}`);
        }
    }

    /**
     * 获取图片的MIME类型
     */
    getImageMimeType(imagePath) {
        try {
            const cleanPath = imagePath.split('?')[0].split('#')[0];
            const mimeType = mime.lookup(cleanPath);

            // 如果mime-types识别成功且是图片类型，直接返回
            if (mimeType && mimeType.startsWith('image/')) {
                return mimeType;
            }

            // 如果无法识别，根据扩展名手动判断
            const ext = path.extname(cleanPath).toLowerCase();
            switch (ext) {
                case '.jpg':
                case '.jpeg':
                    return 'image/jpeg';
                case '.png':
                    return 'image/png';
                case '.gif':
                    return 'image/gif';
                case '.webp':
                    return 'image/webp';
                case '.bmp':
                    return 'image/bmp';
                default:
                    return 'image/png'; // 默认返回PNG
            }
        } catch (error) {
            this.logDebug('获取MIME类型失败，使用默认值', { error: error.message });
            return 'image/png';
        }
    }

    /**
     * 使用Gemini模型生成图像
     */
    async generateWithGemini(input) {
        const axiosInstance = this.createAxiosInstance();
        
        const requestData = {
            contents: [{
                parts: [
                    { text: input.prompt }
                ]
            }],
            generationConfig: {
                responseModalities: ['TEXT', 'IMAGE']
            }
        };

        const url = `/v1beta/models/${this.config.GEMINI_MODEL}:generateContent?key=${this.config.GOOGLE_API_KEY}`;
        
        this.logDebug('发送Gemini请求', { url, prompt: input.prompt });
        
        const response = await axiosInstance.post(url, requestData);
        
        if (!response.data?.candidates?.[0]?.content?.parts) {
            throw new Error('Gemini API返回格式错误');
        }

        // 处理响应，提取图像数据
        const parts = response.data.candidates[0].content.parts;
        let textResponse = '';
        let imageData = null;

        for (const part of parts) {
            if (part.text) {
                textResponse += part.text;
            } else if (part.inlineData) {
                imageData = part.inlineData.data;
                break;
            }
        }

        if (!imageData) {
            throw new Error('未从Gemini响应中找到图像数据');
        }

        // 保存图像并返回结果
        return await this.saveImageAndCreateResponse(imageData, input.prompt, 'gemini', textResponse);
    }

    /**
     * 使用Gemini模型编辑图像
     */
    async editWithGemini(input) {
        const axiosInstance = this.createAxiosInstance();
        
        // 构建包含图像和文本的请求
        const requestData = {
            contents: [{
                parts: [
                    { text: input.prompt },
                    {
                        inlineData: {
                            mimeType: input.image_mime_type || 'image/png',
                            data: input.image_data
                        }
                    }
                ]
            }],
            generationConfig: {
                responseModalities: ['TEXT', 'IMAGE']
            }
        };

        const url = `/v1beta/models/${this.config.GEMINI_MODEL}:generateContent?key=${this.config.GOOGLE_API_KEY}`;
        
        this.logDebug('发送Gemini编辑请求', { url, prompt: input.prompt });
        
        const response = await axiosInstance.post(url, requestData);
        
        if (!response.data?.candidates?.[0]?.content?.parts) {
            throw new Error('Gemini API返回格式错误');
        }

        // 处理响应，提取图像数据
        const parts = response.data.candidates[0].content.parts;
        let textResponse = '';
        let imageData = null;

        for (const part of parts) {
            if (part.text) {
                textResponse += part.text;
            } else if (part.inlineData) {
                imageData = part.inlineData.data;
                break;
            }
        }

        if (!imageData) {
            throw new Error('未从Gemini响应中找到图像数据');
        }

        // 保存图像并返回结果
        return await this.saveImageAndCreateResponse(imageData, input.prompt, 'gemini', textResponse);
    }



    /**
     * 保存图像并创建响应
     */
    async saveImageAndCreateResponse(imageData, prompt, model, textResponse = '') {
        try {
            // 生成文件名
            const timestamp = Date.now();
            const filename = `google_${model}_${timestamp}.png`;
            const relativePath = `image/googleimageeditor/${filename}`;
            const fullPath = path.join(this.config.PROJECT_BASE_PATH, relativePath);

            // 确保目录存在
            const dir = path.dirname(fullPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // 保存图像文件
            const buffer = Buffer.from(imageData, 'base64');
            fs.writeFileSync(fullPath, buffer);

            this.logDebug('图像保存成功', { filename, path: fullPath });

            return {
                prompt: prompt,
                model: model,
                image_url: fullPath, // 使用本地绝对路径
                image_path: fullPath, // 使用本地绝对路径
                filename: filename,
                text_response: textResponse,
                generation_time: new Date().toISOString(),
                markdown_display: `![${prompt.substring(0, 80)}](${fullPath})` // 使用本地绝对路径
            };

        } catch (error) {
            this.logError('保存图像失败', error);
            throw new Error(`保存图像失败: ${error.message}`);
        }
    }

    /**
     * 调试日志
     */
    logDebug(message, data = null) {
        if (this.config.DEBUG_MODE) {
            console.error(`[GoogleImageEditor Debug] ${message}`, data ? JSON.stringify(data, null, 2) : '');
        }
    }

    /**
     * 错误日志
     */
    logError(message, error) {
        console.error(`[GoogleImageEditor Error] ${message}:`, error.message);
        if (this.config.DEBUG_MODE && error.stack) {
            console.error(error.stack);
        }
    }
}

// 如果直接运行此文件，处理标准输入
if (require.main === module) {
    const plugin = new GoogleImageEditor();
    
    let inputData = '';
    process.stdin.on('data', (chunk) => {
        inputData += chunk;
    });
    
    process.stdin.on('end', async () => {
        try {
            const result = await plugin.execute(inputData.trim());
            console.log(result);
        } catch (error) {
            console.log(JSON.stringify({
                status: 'error',
                message: error.message,
                timestamp: new Date().toISOString()
            }));
        }
    });
}

module.exports = GoogleImageEditor;
